import { app, BrowserWindow, ipcMain, screen } from 'electron';
import * as path from 'path';
import { autoUpdater } from 'electron-updater';
import { DatabaseManager } from './database';
import { SyncService } from './sync-service';
import { AuthService } from './auth-service';
import StaffAuthService from './staff-auth-service';
import { SettingsService } from './services/SettingsService';
import { setupPaymentHandlers } from './payment-handlers';
import { HeartbeatService } from './services/HeartbeatService';

// Keep a global reference of the window object
let mainWindow: BrowserWindow | null = null;
let dbManager: DatabaseManager;
let syncService: SyncService;
let authService: AuthService;
let staffAuthService: StaffAuthService;
let settingsService: SettingsService;
let heartbeatService: HeartbeatService;

const isDev = process.env.NODE_ENV === 'development';

function createWindow(): void {
  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;

  // Create the browser window with touch-optimized settings
  mainWindow = new BrowserWindow({
    width: Math.min(1200, width),
    height: Math.min(800, height),
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      // Touch and Windows optimizations
      experimentalFeatures: false, // Disabled experimental features to prevent security warnings
      scrollBounce: false,
      // Add sandbox for better security
      sandbox: true
    },
    // Windows-specific optimizations
    titleBarStyle: process.platform === 'win32' ? 'default' : 'hiddenInset',
    frame: true,
    show: false, // Don't show until ready
    icon: path.join(__dirname, '../../public/icon.png'), // Add app icon
    // Touch-friendly window behavior
    resizable: true,
    maximizable: true,
    fullscreenable: false // Prevent accidental fullscreen on touch
  });

  // Load the app
  if (isDev) {
    // Development mode: Load from webpack-dev-server and open DevTools
    const loadContent = async () => {
      try {
        await mainWindow!.loadURL('http://localhost:3002');
        mainWindow!.webContents.openDevTools();
      } catch (error) {
        console.error('Failed to load React app:', error);
      }
    };
    
    loadContent();
  } else {
    // Production mode: Load built files
    mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show();
      
      // Focus window for better UX
      if (isDev) {
        mainWindow.focus();
      }
    }
  });

  // Handle app cleanup
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
   
  // Handle window focus for activity tracking
  mainWindow.on('focus', () => {
    if (authService) {
      authService.updateActivity();
    }
  });
   
  // Handle window events for touch optimization
  mainWindow.webContents.on('before-input-event', (event, input) => {
    // Track user activity for session management
    if (authService && (input.type === 'keyDown' || input.type === 'mouseDown')) {
      authService.updateActivity();
    }
  });

  // Prevent navigation away from the app
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.origin !== 'http://localhost:8080' && !isDev) {
      event.preventDefault();
    }
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    // Open external links in default browser
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });
}

// App event handlers
app.whenReady().then(async () => {
  try {
    // Initialize services
    dbManager = new DatabaseManager();
    await dbManager.initialize();
    
    // Setup payment handlers
    setupPaymentHandlers(dbManager);
    
    authService = new AuthService(dbManager);
    staffAuthService = new StaffAuthService();
    syncService = new SyncService(dbManager);
    settingsService = new SettingsService(dbManager.db);
    heartbeatService = new HeartbeatService();
    
    // Initialize services
    await authService.initialize();
    await staffAuthService.initialize();
    
    // Pass databaseManager to heartbeatService and initialize
    heartbeatService.setDatabaseManager(dbManager);
    await heartbeatService.initialize();
    
    // Create window first
    createWindow();
    
    // Set main window references after window is created
    if (mainWindow) {
      syncService.setMainWindow(mainWindow);
      authService.setMainWindow(mainWindow);
      settingsService.setMainWindow(mainWindow);
      
      // Settings service is already initialized in constructor
      
      // Start auto-sync and real-time subscriptions
      syncService.startAutoSync();
      syncService.setupRealtimeSubscriptions();
    }
  } catch (error) {
    console.error('Failed to initialize app:', error);
    app.quit();
  }

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', async () => {
  
  try {
    // Stop heartbeat service
    if (heartbeatService) {
      heartbeatService.stop();
    }
    
    // Stop sync service
    if (syncService) {
      syncService.stopAutoSync();
    }
    
    // Close database connection
    if (dbManager) {
      dbManager.close();
    }
    
    // Logout current user
    if (authService) {
      await authService.logout();
    }
  } catch (error) {
    console.error('Error during app cleanup:', error);
  }
});

// IPC handlers for communication with renderer

// Authentication handlers
ipcMain.handle('auth:login', async (event, { pin, staffId }) => {
  try {
    const result = await authService.login(pin, staffId);
    return result;
  } catch (error) {
    console.error('Auth login error:', error);
    return { success: false, error: 'Login failed' };
  }
});

ipcMain.handle('auth:logout', async () => {
  try {
    const success = await authService.logout();
    return { success: true };
  } catch (error) {
    console.error('Auth logout error:', error);
    return { success: false, error: 'Logout failed' };
  }
});

ipcMain.handle('auth:get-current-session', async () => {
  try {
    const session = await authService.getCurrentSession();
    return session;
  } catch (error) {
    console.error('Get session error:', error);
    return null;
  }
});

ipcMain.handle('auth:validate-session', async (event, { sessionId }) => {
  try {
    const isValid = await authService.validateSession(sessionId);
    return { isValid };
  } catch (error) {
    console.error('Validate session error:', error);
    return { isValid: false };
  }
});

ipcMain.handle('auth:has-permission', async (event, { action }) => {
  try {
    const hasPermission = await authService.hasPermission(action);
    return { hasPermission };
  } catch (error) {
    console.error('Check permission error:', error);
    return { hasPermission: false };
  }
});

ipcMain.handle('auth:get-session-stats', async () => {
  try {
    const stats = await authService.getSessionStats();
    return stats;
  } catch (error) {
    console.error('Get session stats error:', error);
    return null;
  }
});

// Order management handlers
ipcMain.handle('order:get-all', async () => {
  try {
    const orders = await dbManager.getOrders();
    // Transform database format (snake_case) to frontend format (camelCase)
    const transformedOrders = orders.map(order => ({
      id: order.id,
      orderNumber: order.order_number || `ORD-${order.id.slice(-6)}`,
      status: order.status,
      items: order.items,
      totalAmount: order.total_amount,
      customerName: order.customer_name,
      customerPhone: order.customer_phone,
      customerEmail: order.customer_email,
      orderType: order.order_type || 'takeaway',
      tableNumber: order.table_number,
      address: order.delivery_address,
      notes: order.special_instructions,
      createdAt: order.created_at,
      updatedAt: order.updated_at,
      estimatedTime: order.estimated_time,
      paymentStatus: order.payment_status,
      paymentMethod: order.payment_method,
      paymentTransactionId: order.payment_transaction_id
    }));
    return transformedOrders;
  } catch (error) {
    console.error('Get orders error:', error);
    return [];
  }
});

ipcMain.handle('order:get-by-id', async (event, { orderId }) => {
  try {
    const order = await dbManager.getOrderById(orderId);
    if (!order) return null;
    
    // Transform database format (snake_case) to frontend format (camelCase)
    const transformedOrder = {
      id: order.id,
      orderNumber: order.order_number || `ORD-${order.id.slice(-6)}`,
      status: order.status,
      items: order.items,
      totalAmount: order.total_amount,
      customerName: order.customer_name,
      customerPhone: order.customer_phone,
      customerEmail: order.customer_email,
      orderType: order.order_type || 'takeaway',
      tableNumber: order.table_number,
      address: order.delivery_address,
      notes: order.special_instructions,
      createdAt: order.created_at,
      updatedAt: order.updated_at,
      estimatedTime: order.estimated_time,
      paymentStatus: order.payment_status,
      paymentMethod: order.payment_method,
      paymentTransactionId: order.payment_transaction_id
    };
    return transformedOrder;
  } catch (error) {
    console.error('Get order by ID error:', error);
    return null;
  }
});

ipcMain.handle('order:update-status', async (event, { orderId, status }) => {
  try {
    // Check permission
    const hasPermission = await authService.hasPermission('update_order_status');
    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }
    
    const success = await dbManager.updateOrderStatus(orderId, status);
    
    if (success) {
      // Update activity for session management
      authService.updateActivity();
      
      // Notify renderer about the update
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('order-status-updated', { orderId, status });
      }
    }
    
    return { success: true };
  } catch (error) {
    console.error('Update order status error:', error);
    return { success: false, error: 'Failed to update order status' };
  }
});

ipcMain.handle('order:create', async (event, { orderData }) => {
  try {
    // Validate order data
    if (!orderData.items || orderData.items.length === 0) {
      return { success: false, error: 'Order must contain at least one item' };
    }
    
    if (!orderData.totalAmount || orderData.totalAmount <= 0) {
      return { success: false, error: 'Order total must be greater than 0' };
    }
    
    // Check permission
    const hasPermission = await authService.hasPermission('create_order');
    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }
    
    // Transform frontend data to match database schema
    const dbOrderData = {
      customer_name: orderData.customerName,
      items: orderData.items,
      total_amount: orderData.totalAmount || 0, // Fallback to 0 if null/undefined
      status: orderData.status,
      order_type: orderData.orderType,
      customer_phone: orderData.customerPhone,
      customer_email: orderData.customerEmail,
      table_number: orderData.tableNumber,
      delivery_address: orderData.deliveryAddress,
      special_instructions: orderData.notes,
      estimated_time: orderData.estimatedTime,
      payment_status: orderData.paymentStatus,
      payment_method: orderData.paymentMethod,
      payment_transaction_id: orderData.paymentTransactionId
    };
    

    
    const orderId = await dbManager.insertOrder(dbOrderData);
    
    // Update activity for session management
    authService.updateActivity();
    
    // Notify renderer about the new order
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('order-created', { orderId });
    }
    
    return { success: true, orderId };
  } catch (error) {
    console.error('Create order error:', error);
    return { success: false, error: 'Failed to create order' };
  }
});

ipcMain.handle('order:delete', async (event, { orderId }) => {
  try {
    // Check permission
    const hasPermission = await authService.hasPermission('delete_order');
    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }
    
    const success = await dbManager.deleteOrder(orderId);
    
    // Update activity for session management
    authService.updateActivity();
    
    // Notify renderer about the update
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('order-deleted', { orderId });
    }
    
    return { success: true };
  } catch (error) {
    console.error('Delete order error:', error);
    return { success: false, error: 'Failed to delete order' };
  }
});

ipcMain.handle('payment:update-payment-status', async (event, { orderId, paymentStatus, paymentMethod, transactionId }) => {
  try {
    // Check permission
    const hasPermission = await authService.hasPermission('update_order_status');
    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }
    
    await dbManager.updateOrderPaymentStatus(orderId, paymentStatus, paymentMethod, transactionId);
    
    // Update activity for session management
    authService.updateActivity();
    
    // Notify renderer about the update
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('order-payment-updated', { orderId, paymentStatus, paymentMethod, transactionId });
    }
    
    return { success: true };
  } catch (error) {
    console.error('Update order payment status error:', error);
    return { success: false, error: 'Failed to update order payment status' };
  }
});

// Sync service handlers
ipcMain.handle('sync:get-status', async () => {
  try {
    const status = await syncService.getSyncStatus();
    return status;
  } catch (error) {
    console.error('Get sync status error:', error);
    return { isOnline: false, lastSync: null, pendingItems: 0, syncInProgress: false, error: (error as Error).message };
  }
});

ipcMain.handle('sync:force', async () => {
  try {
    // Check permission
    const hasPermission = await authService.hasPermission('force_sync');
    if (!hasPermission) {
      return { success: false, error: 'Insufficient permissions' };
    }
    
    await syncService.forceSync();
    
    // Update activity for session management
    authService.updateActivity();
    
    return { success: true };
  } catch (error) {
    console.error('Force sync error:', error);
    return { success: false, error: 'Failed to force sync' };
  }
});

ipcMain.handle('sync:get-network-status', async () => {
  try {
    const isOnline = syncService.getNetworkStatus();
    return { isOnline };
  } catch (error) {
    console.error('Get network status error:', error);
    return { isOnline: false };
  }
});

// System information handlers
ipcMain.handle('system:get-info', async () => {
  return {
    platform: process.platform,
    version: app.getVersion(),
    isPackaged: app.isPackaged,
    userDataPath: app.getPath('userData')
  };
});

// Activity tracking for session management
ipcMain.handle('activity:track', () => {
  authService.updateActivity();
  return true;
});

ipcMain.handle('activity:get-last', () => {
  return authService.getLastActivity();
});

// Staff authentication handlers
ipcMain.handle('staff-auth:authenticate-pin', async (event, pin: string, terminalId?: string) => {
  try {
    return await staffAuthService.authenticateWithPIN(pin, terminalId);
  } catch (error) {
    console.error('Staff auth PIN error:', error);
    return { success: false, error: 'Authentication failed' };
  }
});

ipcMain.handle('staff-auth:get-session', async () => {
  try {
    return await staffAuthService.getCurrentSession();
  } catch (error) {
    console.error('Staff auth get session error:', error);
    return null;
  }
});

ipcMain.handle('staff-auth:get-current', async () => {
  try {
    return await staffAuthService.getCurrentStaff();
  } catch (error) {
    console.error('Staff auth get current error:', error);
    return null;
  }
});

ipcMain.handle('staff-auth:has-permission', async (event, permission: string) => {
  try {
    return await staffAuthService.hasPermission(permission);
  } catch (error) {
    console.error('Staff auth has permission error:', error);
    return false;
  }
});

ipcMain.handle('staff-auth:has-any-permission', async (event, permissions: string[]) => {
  try {
    return await staffAuthService.hasAnyPermission(permissions);
  } catch (error) {
    console.error('Staff auth has any permission error:', error);
    return false;
  }
});

ipcMain.handle('staff-auth:logout', async () => {
  try {
    return await staffAuthService.logout();
  } catch (error) {
    console.error('Staff auth logout error:', error);
    return { success: false, error: 'Logout failed' };
  }
});

ipcMain.handle('staff-auth:validate-session', async () => {
  try {
    return await staffAuthService.validateSession();
  } catch (error) {
    console.error('Staff auth validate session error:', error);
    return { isValid: false };
  }
});

ipcMain.handle('staff-auth:track-activity', async (event, activityType: string, resourceType: string | null, resourceId: string | null, action: string, details?: Record<string, any>, result?: string) => {
  try {
    return await staffAuthService.trackActivity(activityType, resourceType, resourceId, action, details, result);
  } catch (error) {
    console.error('Staff auth track activity error:', error);
    return { success: false };
  }
});

// Window control handlers
ipcMain.handle('window-minimize', async () => {
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.minimize();
  }
});

ipcMain.handle('window-maximize', async () => {
  if (mainWindow && !mainWindow.isDestroyed()) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('window-close', async () => {
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.close();
  }
});

// Settings handlers
ipcMain.handle('get-settings', async () => {
  try {
    return await settingsService.getSettings();
  } catch (error) {
    console.error('Get settings error:', error);
    return {};
  }
});

ipcMain.handle('update-settings', async (event, settings) => {
  try {
    await settingsService.updateSettings(settings);
    return { success: true };
  } catch (error) {
    console.error('Update settings error:', error);
    return { success: false, error: (error as Error).message };
  }
});

// Database handlers
ipcMain.handle('execute-query', async (event, query, params) => {
  try {
    return await dbManager.executeQuery(query, params);
  } catch (error) {
    console.error('Execute query error:', error);
    throw error;
  }
});

// Auth handlers (legacy support)
ipcMain.handle('auth-login', async (event, credentials) => {
  try {
    const result = await authService.login(credentials.pin, credentials.staffId);
    return result;
  } catch (error) {
    console.error('Auth login error:', error);
    return { success: false, error: 'Login failed' };
  }
});

ipcMain.handle('auth-logout', async () => {
  try {
    await authService.logout();
    return { success: true };
  } catch (error) {
    console.error('Auth logout error:', error);
    return { success: false, error: 'Logout failed' };
  }
});

// System info handlers
ipcMain.handle('get-system-info', async () => {
  return {
    platform: process.platform,
    version: process.version,
    arch: process.arch,
    appVersion: app.getVersion()
  };
});

// Development helpers
ipcMain.handle('open-dev-tools', async () => {
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.openDevTools();
  }
});

ipcMain.handle('reload', async () => {
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.webContents.reload();
  }
});

// Sync-related handlers
ipcMain.handle('get-sync-status', async () => {
  try {
    return await syncService.getSyncStatus();
  } catch (error) {
    console.error('Get sync status error:', error);
    return null;
  }
});

ipcMain.handle('request-restart', async () => {
  try {
    // Gracefully restart the application
    app.relaunch();
    app.exit(0);
  } catch (error) {
    console.error('Request restart error:', error);
    throw error;
  }
});

ipcMain.handle('force-sync', async () => {
  try {
    await syncService.forceSyncAll();
    return { success: true };
  } catch (error) {
    console.error('Force sync error:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('open-sync-logs', async () => {
  try {
    // Open sync logs in default text editor or file explorer
    const { shell } = require('electron');
    const logPath = path.join(app.getPath('userData'), 'sync-logs.txt');
    await shell.openPath(logPath);
    return { success: true };
  } catch (error) {
    console.error('Open sync logs error:', error);
    return { success: false, error: (error as Error).message };
  }
});

// Auto-updater events (for production)
if (app.isPackaged) {
  autoUpdater.checkForUpdatesAndNotify();
  
  autoUpdater.on('update-available', (info: any) => {
    // Notify renderer about available update
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('update-available', info);
    }
  });
  
  autoUpdater.on('update-downloaded', (info: any) => {
    // Notify renderer about downloaded update
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('update-downloaded', info);
    }
  });
  
  autoUpdater.on('error', (error: any) => {
    console.error('Auto-updater error:', error);
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('update-error', { error: (error as Error).message });
    }
  });
  
  // Handle update installation request from renderer
  ipcMain.handle('update:install', async () => {
    try {
      autoUpdater.quitAndInstall();
      return { success: true };
    } catch (error) {
      console.error('Failed to install update:', error);
      return { success: false, error: (error as Error).message };
    }
  });
}

// Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.setWindowOpenHandler(({ url }) => {
    // Prevent opening new windows
    return { action: 'deny' };
  });
});