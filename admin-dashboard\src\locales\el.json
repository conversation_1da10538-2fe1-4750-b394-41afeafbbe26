{"auth": {"login": {"title": "Σύνδεση", "subtitle": "<PERSON><PERSON>λ<PERSON>ς ήρθατε στο Creperie <PERSON>min", "email": "Email", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ρόσβασης", "signIn": "Σύνδεση", "forgotPassword": "Ξεχάσατε τον κωδικό σας;", "resetPassword": "Επαναφο<PERSON><PERSON> Κωδικού", "rememberMe": "Να με θυμάσαι", "errors": {"invalidCredentials": "Μη έγκυρο email ή κωδικός πρόσβασης", "emailRequired": "Το email είναι υποχρεωτικό", "passwordRequired": "Ο κωδικός πρόσβασης είναι υποχρεωτικός", "invalidEmail": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε έγκυρη διεύθυνση email"}}, "twoFactor": {"title": "Ταυτοποίηση Δύο Παραγόντων", "subtitle": "Εισάγετε τον κωδικό επαλήθευσης από την εφαρμογή σας", "code": "Κω<PERSON><PERSON><PERSON><PERSON><PERSON> Επαλήθευσης", "verify": "Επαλήθευση", "backToLogin": "Επιστροφή στη Σύνδεση", "setup": {"title": "Ρύθμιση Ταυτοποίησης Δύο Παραγόντων", "subtitle": "Σαρώστε τον κωδικό QR με την εφαρμογή σας", "step1": "Εγκαταστήστε μια εφαρμογή όπως Google Authenticator ή Authy", "step2": "Σαρώστε τον παρακάτω κωδικό QR με την εφαρμογή σας", "step3": "Εισάγετε τον 6ψή<PERSON>ιο κωδικό από την εφαρμογή για επαλήθευση", "qrCode": "Κωδικός QR", "manualEntry": "Κλειδί Χειροκίνητης Εισαγωγής", "enable": "Ενεργοποίηση 2FA", "cancel": "Ακύρωση"}, "errors": {"invalidCode": "Μη έγκυ<PERSON><PERSON> κωδικ<PERSON>ς επαλήθευσης", "codeRequired": "Ο κωδικός επαλήθευσης είναι υποχρεωτικός", "setupFailed": "Αποτυχία ρύθμισης ταυτοποίησης δύο παραγόντων"}}, "resetPassword": {"title": "Επαναφο<PERSON><PERSON> Κωδικού", "subtitle": "Εισάγετε το email σας και θα σας στείλουμε σύνδεσμο επαναφοράς", "email": "Email", "sendReset": "Αποστολή <PERSON>υνδέσμου", "backToLogin": "Επιστροφή στη Σύνδεση", "success": "Ο σύνδεσμος επανα<PERSON><PERSON><PERSON><PERSON><PERSON> στάλθηκε στο email σας", "newPassword": "<PERSON><PERSON><PERSON> Πρόσβασης", "confirmPassword": "Επιβεβαίωση Κωδικού", "updatePassword": "Ενημέρωση Κωδικού", "errors": {"emailRequired": "Το email είναι υποχρεωτικό", "invalidEmail": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε έγκυρη διεύθυνση email", "passwordRequired": "Ο κωδικός πρόσβασης είναι υποχρεωτικός", "passwordMismatch": "Οι κωδικοί πρόσβασης δεν ταιριάζουν", "passwordTooShort": "Ο κωδικός πρέπει να έχει τουλάχιστον 8 χαρακτήρες", "resetFailed": "Αποτυχ<PERSON>α επανα<PERSON><PERSON><PERSON><PERSON><PERSON> κωδικού"}}}, "glassmorphism": {"demo": {"title": "Σύστημα Σχεδιασμού Glassmorphism", "subtitle": "Ένα πλήρες CSS framework για την υλοποίηση εφέ glassmorphism", "sections": {"glassCards": "Κάρ<PERSON><PERSON><PERSON>ού", "statusVariants": "<PERSON>α<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ατάστασης", "glassButtons": "Κουμπιά Γυαλιού", "formElements": "Στοιχεία Φόρμας", "glassContainers": "Περιέκτες <PERSON>λιού"}, "cards": {"primary": {"title": "Κύρια Κάρτα", "description": "Αυτή είναι μια κύρια κάρτα γυαλιού με το προεπιλεγμένο εφέ glassmorphism."}, "secondary": {"title": "Δευτερεύουσα <PERSON>άρτα", "description": "Αυτή είναι μια δευτερεύουσα κάρτα γυαλιού με μειωμένη διαφάνεια."}, "interactive": {"title": "Διαδραστική <PERSON>τα", "description": "Κάντε κλικ! Αυτή η κάρτα έχει εφέ hover και κλικ."}}, "status": {"success": "Η λειτουργία ολοκληρώθηκε επιτυχώς", "warning": "Παρα<PERSON><PERSON><PERSON><PERSON> ελέγξτε αυτή την ενέργεια", "error": "Κάτι πήγε στραβά", "info": "Πρόσθετες πληροφορίες"}, "buttons": {"sizes": "Μεγέθη Κουμπιών", "variants": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ών", "states": "Κατα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Κουμπιών", "small": "Μικρό Κουμπί", "medium": "Μεσα<PERSON><PERSON>", "large": "Μεγ<PERSON><PERSON><PERSON>π<PERSON>", "loading": "Φόρτωση...", "clickForLoading": "Κλικ για Φόρτωση", "disabled": "Απενεργοποιημένο Κουμπί"}, "form": {"glassInput": "Πεδίο <PERSON>γω<PERSON><PERSON>ς <PERSON>λιού", "emailInput": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": {"text": "Εισάγετε κείμενο...", "email": "το@email.σας", "modal": "Πεδί<PERSON> εισαγωγής modal", "password": "Πεδ<PERSON><PERSON> κωδικού"}, "openModal": "Άνοιγμα Modal", "clearForm": "Καθαρισμ<PERSON>ς Φόρμας"}, "containers": {"primary": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "description": "Αυτ<PERSON><PERSON> είν<PERSON><PERSON> ένας κύριος περιέκτης γυαλιού που μπορεί να περιέχει οποιοδήποτε περιεχόμενο."}, "secondary": {"title": "Δευτερ<PERSON><PERSON>ων Περιέκτης", "description": "Αυτ<PERSON><PERSON> είναι ένας δευτερεύων περιέκτης γυαλιού με διαφορετικό στυλ."}}, "modal": {"title": "Παράδειγ<PERSON><PERSON> Γυαλιού", "description": "Αυτό είναι ένα όμορφο glassmorphism modal με εφέ θολώματος φόντου. Υποστηρίζει πλοήγηση με πληκτρολόγιο και χαρακτηριστικά προσβασιμότητας.", "cancel": "Ακύρωση", "confirm": "Επιβεβαίωση", "confirmMessage": "Η ενέργεια modal επιβεβαιώθηκε!"}}}, "common": {"loading": "Φόρτωση...", "error": "Παρου<PERSON><PERSON>ά<PERSON>τηκε σφάλμα", "success": "Επιτυχία", "cancel": "Ακύρωση", "save": "Αποθήκευση", "delete": "Διαγραφή", "edit": "Επεξεργασία", "view": "Προβολή", "close": "Κλείσιμο"}, "deliveryZones": {"map": {"title": "Χάρτης Ζωνών Παράδοσης", "subtitle": "Δημιουργήστε και διαχειριστείτε ζώνες παράδοσης με τον διαδραστικό χάρτη", "drawPolygon": "Σχεδίαση Ζώνης", "editPolygon": "Επεξεργασία Ζώνης", "deletePolygon": "Διαγραφή Ζώνης", "saveZone": "Αποθήκευση Ζώνης", "cancelEdit": "Ακύρωση", "clearMap": "Κα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>η", "zoomIn": "Μεγέθυνση", "zoomOut": "Σμίκρυνση", "resetView": "Επαναφορά Προβολής", "searchAddress": "Αναζήτηση διεύθυνσης...", "loading": "Φόρτωση χάρτη...", "error": "Αποτυχία φόρτωσης χάρτη", "noZones": "Δεν έχουν δημιουργηθεί ζώνες παράδοσης ακόμη", "zoneInfo": {"name": "Όνομα Ζώνης", "deliveryFee": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "minimumOrder": "Ελάχιστη Παραγγελία", "estimatedTime": "Εκτιμώ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "active": "Ενεργή", "inactive": "Ανενεργή"}, "instructions": {"draw": "Κάντε κλικ στον χάρτη για να ξεκινήσετε το σχεδιασμό μιας ζώνης παράδοσης", "edit": "Κάντε κλικ και σύρετε τα σημεία του πολυγώνου για να τροποποιήσετε τη ζώνη", "complete": "Κάντε κλικ στο πρώτο σημείο ξανά για να ολοκληρώσετε το πολύγωνο"}, "validation": {"minimumPoints": "Μια ζώνη παράδοσης πρέπει να έχει τουλάχιστον 3 σημεία", "invalidPolygon": "Μη έγκυρο σχήμα πολυγώνου", "overlappingZone": "Αυτή η ζώνη επικαλύπτεται με υπάρχουσα ζώνη παράδοσης"}}}}