import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { z } from 'zod'

// Validation schemas
const createSubcategorySchema = z.object({
  category_id: z.string().uuid(),
  name_en: z.string().min(1),
  name_el: z.string().min(1),
  description_en: z.string().optional(),
  description_el: z.string().optional(),
  image_url: z.string().url().optional(),
  is_available: z.boolean().default(true),
  display_order: z.number().int().min(0).default(0),
  ingredient_ids: z.array(z.string().uuid()).optional()
})

// GET /api/subcategories - Fetch all subcategories with optional filtering
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    // Extract query parameters
    const category_id = searchParams.get('category_id')
    const is_available = searchParams.get('is_available')
    const search = searchParams.get('search')
    const sort_by = searchParams.get('sort_by') || 'display_order'
    const sort_order = searchParams.get('sort_order') || 'asc'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    
    let query = supabase
      .from('subcategories')
      .select(`
          *,
          menu_categories!inner(
            id,
            name_en,
            name_el
          ),
          subcategory_ingredients(
            ingredient_id,
            ingredients(
              id,
              name_en,
              name_el
            )
          )
        `)
    
    // Apply filters
    if (category_id) {
      query = query.eq('category_id', category_id)
    }
    
    if (is_available !== null) {
      query = query.eq('is_available', is_available === 'true')
    }
    
    if (search) {
      query = query.or(`name_en.ilike.%${search}%,name_el.ilike.%${search}%,description_en.ilike.%${search}%,description_el.ilike.%${search}%`)
    }
    
    // Apply sorting
    query = query.order(sort_by, { ascending: sort_order === 'asc' })
    
    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)
    
    const { data, error, count } = await query
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch subcategories' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total: count || 0,
        pages: Math.ceil((count || 0) / limit)
      }
    })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/subcategories - Create a new subcategory
export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const body = await request.json()
    
    // Validate request body
    const validationResult = createSubcategorySchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }
    
    const subcategoryData = validationResult.data
    
    // Verify category exists
    const { data: category, error: categoryError } = await supabase
      .from('menu_categories')
      .select('id')
      .eq('id', subcategoryData.category_id)
      .single()
    
    if (categoryError || !category) {
      return NextResponse.json(
        { error: 'Invalid category ID' },
        { status: 400 }
      )
    }
    
    // Create subcategory
    const { data, error } = await supabase
      .from('subcategories')
      .insert([subcategoryData])
      .select(`
        *,
        menu_categories(
          id,
          name_en,
          name_el
        )
      `)
      .single()
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to create subcategory' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({ data }, { status: 201 })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}