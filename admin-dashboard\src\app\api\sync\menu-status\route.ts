import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    const terminalId = searchParams.get('terminal_id')
    const timeRange = searchParams.get('time_range') || '24h'

    // Calculate time range
    const now = new Date()
    let startTime: Date
    
    switch (timeRange) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000)
        break
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    }

    // Get menu sync queue status
    let syncQuery = supabase
      .from('menu_sync_queue')
      .select('*')
      .gte('created_at', startTime.toISOString())
      .order('created_at', { ascending: false })

    if (terminalId) {
      syncQuery = syncQuery.contains('metadata', { terminal_id: terminalId })
    }

    const { data: syncOperations, error: syncError } = await syncQuery

    if (syncError) {
      console.error('Error fetching sync operations:', syncError)
      return NextResponse.json(
        { error: 'Failed to fetch sync operations' },
        { status: 500 }
      )
    }

    // Get menu data counts
    const [categoriesResult, subcategoriesResult, ingredientsResult] = await Promise.all([
      supabase.from('menu_categories').select('id, name, is_active, updated_at'),
      supabase.from('subcategories').select('id, name, active, updated_at'),
      supabase.from('ingredients').select('id, name, is_available, stock_quantity, updated_at')
    ])

    const categories = categoriesResult.data || []
    const subcategories = subcategoriesResult.data || []
    const ingredients = ingredientsResult.data || []

    // Calculate sync statistics
    const totalSyncOps = syncOperations?.length || 0
    const pendingSyncOps = syncOperations?.filter(op => op.sync_status === 'pending').length || 0
    const processingSyncOps = syncOperations?.filter(op => op.sync_status === 'processing').length || 0
    const completedSyncOps = syncOperations?.filter(op => op.sync_status === 'completed').length || 0
    const failedSyncOps = syncOperations?.filter(op => op.sync_status === 'failed').length || 0

    // Calculate menu item statistics
    const activeCategories = categories.filter(cat => cat.is_active).length
    const activeSubcategories = subcategories.filter(sub => sub.active).length
    const availableIngredients = ingredients.filter(ing => ing.is_available).length
    const lowStockIngredients = ingredients.filter(ing => 
      ing.stock_quantity !== null && ing.stock_quantity <= 5
    ).length
    const outOfStockIngredients = ingredients.filter(ing => 
      ing.stock_quantity === 0
    ).length

    // Get terminal sync status if specified
    let terminalStatus = null
    if (terminalId) {
      const { data: terminal } = await supabase
        .from('pos_terminals')
        .select('terminal_id, name, sync_status, last_sync_request, last_heartbeat, pending_updates')
        .eq('terminal_id', terminalId)
        .single()
      
      terminalStatus = terminal
    }

    // Calculate sync health score
    const syncSuccessRate = totalSyncOps > 0 ? (completedSyncOps / totalSyncOps) * 100 : 100
    const menuHealthScore = Math.min(100, 
      (activeCategories > 0 ? 25 : 0) +
      (activeSubcategories > 0 ? 25 : 0) +
      (availableIngredients > 0 ? 25 : 0) +
      (outOfStockIngredients === 0 ? 25 : Math.max(0, 25 - (outOfStockIngredients * 5)))
    )

    // Group sync operations by type
    const syncByType = {
      category: syncOperations?.filter(op => op.sync_type === 'category').length || 0,
      subcategory: syncOperations?.filter(op => op.sync_type === 'subcategory').length || 0,
      ingredient: syncOperations?.filter(op => op.sync_type === 'ingredient').length || 0,
      availability: syncOperations?.filter(op => op.sync_type === 'availability').length || 0
    }

    // Recent sync activity (last 10 operations)
    const recentActivity = syncOperations?.slice(0, 10).map(op => ({
      id: op.id,
      sync_type: op.sync_type,
      operation: op.operation,
      sync_status: op.sync_status,
      resource_id: op.resource_id,
      created_at: op.created_at,
      metadata: op.metadata
    })) || []

    return NextResponse.json({
      menu_sync_status: {
        sync_health_score: Math.round(syncSuccessRate),
        menu_health_score: Math.round(menuHealthScore),
        last_sync_at: syncOperations?.[0]?.created_at || null,
        pending_operations: pendingSyncOps,
        processing_operations: processingSyncOps,
        failed_operations: failedSyncOps
      },
      sync_operations: {
        total: totalSyncOps,
        pending: pendingSyncOps,
        processing: processingSyncOps,
        completed: completedSyncOps,
        failed: failedSyncOps,
        success_rate: Math.round(syncSuccessRate * 10) / 10,
        by_type: syncByType
      },
      menu_data: {
        categories: {
          total: categories.length,
          active: activeCategories,
          inactive: categories.length - activeCategories
        },
        subcategories: {
          total: subcategories.length,
          active: activeSubcategories,
          inactive: subcategories.length - activeSubcategories
        },
        ingredients: {
          total: ingredients.length,
          available: availableIngredients,
          unavailable: ingredients.length - availableIngredients,
          low_stock: lowStockIngredients,
          out_of_stock: outOfStockIngredients
        }
      },
      terminal_status: terminalStatus,
      recent_activity: recentActivity,
      filters: {
        terminal_id: terminalId,
        time_range: timeRange
      },
      timestamp: now.toISOString()
    })

  } catch (error) {
    console.error('Menu sync status error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { terminal_id, action, sync_types } = await request.json()

    if (!terminal_id || !action) {
      return NextResponse.json(
        { error: 'terminal_id and action are required' },
        { status: 400 }
      )
    }

    let result = { success: false, message: '', operations: [] }

    switch (action) {
      case 'force_sync':
        // Trigger a force sync for specified types or all menu data
        const typesToSync = sync_types || ['category', 'subcategory', 'ingredient', 'availability']
        
        for (const syncType of typesToSync) {
          const { data: syncOp, error } = await supabase
            .from('menu_sync_queue')
            .insert({
              sync_type: syncType,
              resource_id: crypto.randomUUID(), // Placeholder for full sync
              operation: 'update',
              data_changes: { force_sync: true, sync_all: true },
              target_platforms: ['pos', 'web', 'mobile'],
              priority: 1, // High priority for force sync
              metadata: {
                terminal_id,
                force_sync: true,
                initiated_by: 'admin_dashboard',
                sync_all: true
              }
            })
            .select()
            .single()

          if (!error) {
            result.operations.push(syncOp)
          }
        }

        result.success = result.operations.length > 0
        result.message = `Force sync initiated for ${result.operations.length} sync types`
        break

      case 'clear_failed':
        // Clear failed sync operations
        const { error: clearError } = await supabase
          .from('menu_sync_queue')
          .delete()
          .eq('sync_status', 'failed')
          .contains('metadata', { terminal_id })

        result.success = !clearError
        result.message = clearError ? 'Failed to clear failed operations' : 'Failed operations cleared'
        break

      case 'retry_failed':
        // Retry failed sync operations
        const { data: failedOps, error: fetchError } = await supabase
          .from('menu_sync_queue')
          .select('*')
          .eq('sync_status', 'failed')
          .contains('metadata', { terminal_id })

        if (!fetchError && failedOps) {
          for (const op of failedOps) {
            await supabase
              .from('menu_sync_queue')
              .update({
                sync_status: 'pending',
                sync_attempts: 0,
                error_message: null,
                updated_at: new Date().toISOString()
              })
              .eq('id', op.id)
          }

          result.success = true
          result.message = `${failedOps.length} failed operations queued for retry`
        }
        break

      default:
        return NextResponse.json(
          { error: 'Invalid action. Must be one of: force_sync, clear_failed, retry_failed' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      ...result,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Menu sync status POST error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
