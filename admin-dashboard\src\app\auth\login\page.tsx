'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import Link from 'next/link'
import { RATE_LIMITS } from '@/config/auth-security'
import BiometricAuth from '@/components/auth/BiometricAuth'
import PasswordGenerator from '@/components/auth/PasswordGenerator'
import SecurePasswordRecovery from '@/components/auth/SecurePasswordRecovery'
import { passwordSecurityService } from '@/services/password-security-service'
import { useAuth } from '@/contexts/auth-context'
import { loginWithEmail } from '../../../../../shared/auth/auth-utils'

const loginSchema = z.object({
  email: z.string().email('Invalid email address').min(1, 'Email is required'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().default(false),
})

type LoginFormData = z.infer<typeof loginSchema>

export default function LoginPage() {
  const { t } = useTranslation()
  const router = useRouter()
  const { login } = useAuth()

  // Ensure full viewport coverage without DOM manipulation
  React.useEffect(() => {
    // Add CSS class to body for login page
    document.body.classList.add('login-page-active')
    
    return () => {
      // Remove CSS class when leaving login page
      document.body.classList.remove('login-page-active')
    }
  }, [])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [needsTwoFactor, setNeedsTwoFactor] = useState(false)
  const [tempUserId, setTempUserId] = useState<string | null>(null)
  const [userEmail, setUserEmail] = useState<string | null>(null)
  const [loginAttempts, setLoginAttempts] = useState(0)
  const [isLocked, setIsLocked] = useState(false)
  const [lockTimeRemaining, setLockTimeRemaining] = useState(0)
  const [showRecovery, setShowRecovery] = useState(false)
  const [showPasswordGenerator, setShowPasswordGenerator] = useState(false)
  const [showBiometric, setShowBiometric] = useState(false)
  const [biometricSupported, setBiometricSupported] = useState(false)

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  })

  // Check biometric support on component mount
  useEffect(() => {
    const checkBiometricSupport = async () => {
      if (typeof window !== 'undefined' && window.PublicKeyCredential) {
        const available = await window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()
        setBiometricSupported(available)
      }
    }
    checkBiometricSupport()
  }, [])

  // Security: Handle account lockout timer
  useEffect(() => {
    if (isLocked && lockTimeRemaining > 0) {
      const timer = setInterval(() => {
        setLockTimeRemaining(prev => {
          if (prev <= 1) {
            setIsLocked(false)
            setLoginAttempts(0)
            return 0
          }
          return prev - 1
        })
      }, 1000)
      return () => clearInterval(timer)
    }
    return undefined
  }, [isLocked, lockTimeRemaining])

  // Security: Clear form data on component unmount
  useEffect(() => {
    return () => {
      form.reset()
      setError(null)
      setTempUserId(null)
      setUserEmail(null)
    }
  }, [form])

  const handleFailedLogin = () => {
    const newAttempts = loginAttempts + 1
    setLoginAttempts(newAttempts)
    
    if (newAttempts >= RATE_LIMITS.LOGIN.maxAttempts) {
      setIsLocked(true)
      setLockTimeRemaining(RATE_LIMITS.LOGIN.windowMs / 1000)
      setError(`Too many failed attempts. Account locked for ${RATE_LIMITS.LOGIN.windowMs / 60000} minutes.`)
    } else {
      setError(`Invalid credentials. ${RATE_LIMITS.LOGIN.maxAttempts - newAttempts} attempts remaining.`)
    }
  }

  const onSubmit = async (data: LoginFormData) => {
    if (isLocked) {
      setError(`Account is locked. Please try again in ${Math.ceil(lockTimeRemaining / 60)} minutes.`)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      // Check password security before attempting login
      try {
        const strengthResult = await passwordSecurityService.assessPasswordStrength(data.password)
        if (strengthResult.score < 50) {
          // Log weak password attempt
          await fetch('/api/audit/log', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              eventType: 'auth.login.failure',
              details: { 
                email: data.email, 
                reason: 'weak_password',
                passwordScore: strengthResult.score 
              },
              riskLevel: 'medium'
            })
          }).catch(() => {}) // Ignore audit errors
        }

        if (strengthResult.isBreached) {
          // Log breached password attempt
          await fetch('/api/audit/log', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              eventType: 'auth.login.failure',
              details: { 
                email: data.email, 
                reason: 'breached_password' 
              },
              riskLevel: 'high'
            })
          }).catch(() => {}) // Ignore audit errors
        }
      } catch (securityError) {
        console.warn('Password security check failed:', securityError)
      }

      // Development bypass for Supabase connection issues
      const isDevelopment = process.env.NODE_ENV === 'development'

      let result
      try {
        result = await loginWithEmail({
          email: data.email,
          password: data.password,
          platform: 'admin-dashboard',
        })
      } catch (networkError) {
        console.error('Login network error:', networkError)

        // In development, if network fails and using dev credentials, allow login
        if (isDevelopment &&
            (data.email === '<EMAIL>' || data.email === 'admin@localhost') &&
            data.password === 'dev123') {
          console.warn('Using development bypass login due to network error')
          result = {
            success: true,
            user: {
              id: 'dev-user-123',
              email: data.email,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
            profile: {
              id: 'dev-user-123',
              user_id: 'dev-user-123',
              first_name: 'Development',
              last_name: 'Admin',
              full_name: 'Development Admin',
              phone: '+1234567890',
              is_active: true,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              role_id: 'admin-role'
            }
          }
        } else {
          throw networkError
        }
      }

      if (!result.success) {
        // Log failed login attempt
        await fetch('/api/audit/log', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            eventType: 'auth.login.failure',
            details: {
              email: data.email,
              reason: result.error || 'invalid_credentials',
              attempt: loginAttempts + 1
            },
            riskLevel: 'medium'
          })
        }).catch(() => {}) // Ignore audit errors

        handleFailedLogin()
        setError(result.error || t('auth.login.errors.invalidCredentials'))
        return
      }

      // Reset login attempts on successful authentication
      setLoginAttempts(0)
      setIsLocked(false)

      // Log successful login
      await fetch('/api/audit/log', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          eventType: 'auth.login.success',
          details: { 
            email: data.email,
            authMethod: 'password',
            biometricUsed: false,
            userId: result.user?.id
          },
          userId: result.user?.id,
          riskLevel: 'low'
        })
      }).catch(() => {}) // Ignore audit errors

      // Register GDPR data subject if new user
      try {
        await fetch('/api/gdpr/data-subject', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'register',
            email: data.email,
            personalData: { email: data.email },
            initialConsent: {
              functional: { granted: true, timestamp: new Date(), version: '1.0', method: 'legitimate_interest' },
              analytics: { granted: false, timestamp: new Date(), version: '1.0', method: 'explicit' }
            }
          })
        })
      } catch (gdprError) {
        // User might already exist or GDPR service unavailable, continue login
        console.warn('GDPR registration failed:', gdprError)
      }

      // Check if 2FA is required for admin users
      if (result.profile?.two_fa_enabled && result.role?.name === 'admin') {
        setNeedsTwoFactor(true)
        setTempUserId(result.user?.id || null)
        setUserEmail(data.email)
        return
      }

      // User is successfully authenticated
      if (result.user && result.profile) {
        const userData = {
          id: result.user.id,
          email: result.user.email || data.email,
          created_at: result.user.created_at || new Date().toISOString(),
          updated_at: result.user.updated_at || new Date().toISOString(),
        }

        // Set session token as secure cookie
        if (result.session?.session_token) {
          try {
            await fetch('/api/auth/set-cookie', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                sessionToken: result.session.session_token,
                rememberMe: data.rememberMe
              })
            })
          } catch (cookieError) {
            console.warn('Failed to set auth cookie:', cookieError)
            // Fallback to localStorage for backward compatibility
            const storage = data.rememberMe ? localStorage : sessionStorage
            storage.setItem('auth_session', result.session.session_token)
          }
        }

        login(userData, result.profile)
        router.push('/dashboard')
      } else {
        handleFailedLogin()
        setError('Failed to load user profile')
      }
    } catch (err: any) {
      console.error('Login error:', err)
      handleFailedLogin()
      setError(err.message || 'An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const handleBiometricSuccess = () => {
    setShowBiometric(false)
    router.push('/dashboard')
  }

  const handleBiometricError = (error: string) => {
    setError(error)
  }

  const handlePasswordGenerated = (generatedPassword: string) => {
    form.setValue('password', generatedPassword)
    setShowPasswordGenerator(false)
  }

  const handleRecoveryComplete = () => {
    setShowRecovery(false)
  }

  const handleGoogleSignIn = async () => {
    // Placeholder for Google Sign-In implementation
    setError('Google Sign-In will be implemented soon')
  }

  if (needsTwoFactor && tempUserId) {
    const redirectUrl = `/auth/2fa?userId=${tempUserId}${userEmail ? `&email=${encodeURIComponent(userEmail)}` : ''}`;
    router.push(redirectUrl)
    return null
  }

  return (
    <div className="w-screen h-screen fixed inset-0 overflow-hidden">
      {/* Background with geometric patterns */}
      <div className="absolute inset-0 w-full h-full">
        {/* Base gradient */}
        <div className="absolute inset-0 w-full h-full bg-gradient-to-br from-white via-sky-50 to-sky-100"></div>
        
        {/* Geometric shapes background */}
        <div className="absolute inset-0 w-full h-full">
          {/* Large circle top-left */}
          <div className="absolute -top-24 -left-24 w-96 h-96 bg-gradient-to-br from-sky-200/30 to-blue-300/25 rounded-full blur-3xl"></div>
          
          {/* Medium circle top-right */}
          <div className="absolute -top-12 -right-12 w-72 h-72 bg-gradient-to-br from-blue-100/40 to-sky-200/30 rounded-full blur-2xl"></div>
          
          {/* Small circle bottom-left */}
          <div className="absolute -bottom-16 -left-16 w-64 h-64 bg-gradient-to-br from-cyan-100/35 to-sky-300/25 rounded-full blur-xl"></div>
          
          {/* Floating geometric shapes */}
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-sky-400/50 rounded-full animate-pulse"></div>
          <div className="absolute top-3/4 left-1/3 w-1 h-1 bg-blue-400/50 rounded-full animate-ping"></div>
          <div className="absolute top-1/2 right-1/4 w-3 h-3 bg-cyan-300/40 rounded-full animate-pulse delay-1000"></div>
          
          {/* Grid overlay */}
          <div className="absolute inset-0 opacity-[0.02]">
            <div className="h-full w-full" style={{
              backgroundImage: `radial-gradient(circle at 1px 1px, white 1px, transparent 0)`,
              backgroundSize: '50px 50px'
            }}></div>
          </div>
        </div>
        
        {/* Noise texture overlay */}
        <div className="absolute inset-0 w-full h-full opacity-[0.015] mix-blend-overlay bg-repeat" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
        }}></div>
      </div>

      {/* Main content container */}
      <div className="relative z-10 w-full h-full flex items-center justify-center p-4">
        {/* Login card container */}
        <div className="w-full max-w-md">
          {/* Login form card */}
          <div className="bg-white/60 backdrop-blur-xl border border-white/40 rounded-3xl p-8 shadow-2xl">
            {/* Form Header */}
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">Welcome back</h2>
              <p className="text-gray-600">Please sign in to continue</p>

              {/* Development Notice */}
              {process.env.NODE_ENV === 'development' && (
                <div className="mt-4 p-3 bg-blue-500/10 border border-blue-400/20 rounded-xl backdrop-blur-sm">
                  <p className="text-xs text-blue-600 font-medium">
                    Development Mode: Use <code className="bg-blue-100 px-1 rounded"><EMAIL></code> / <code className="bg-blue-100 px-1 rounded">dev123</code>
                  </p>
                </div>
              )}
            </div>

            {/* Error Display */}
            {error && (
              <div className="mb-6 p-4 bg-red-500/10 border border-red-400/20 rounded-xl backdrop-blur-sm">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"/>
                  </svg>
                  <span className="text-sm font-medium text-red-200">{error}</span>
                </div>
              </div>
            )}

            {/* Login Form */}
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email address
                </label>
                <input
                  type="email"
                  id="email"
                  placeholder="Enter your email"
                  {...form.register('email')}
                  disabled={isLoading || isLocked}
                  className={`w-full px-4 py-3 bg-white/30 border border-gray-200/50 rounded-xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-sky-500/50 focus:border-sky-400/50 transition-all duration-300 backdrop-blur-sm ${
                    form.formState.errors.email ? 'border-red-400/50 bg-red-500/5' : ''
                  }`}
                  autoComplete="email"
                />
                {form.formState.errors.email && (
                  <p className="mt-2 text-sm text-red-600">
                    {form.formState.errors.email.message}
                  </p>
                )}
              </div>

              {/* Password Field */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  placeholder="Enter your password"
                  {...form.register('password')}
                  disabled={isLoading || isLocked}
                  className={`w-full px-4 py-3 bg-white/30 border border-gray-200/50 rounded-xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-sky-500/50 focus:border-sky-400/50 transition-all duration-300 backdrop-blur-sm ${
                    form.formState.errors.password ? 'border-red-400/50 bg-red-500/5' : ''
                  }`}
                  autoComplete="current-password"
                />
                {form.formState.errors.password && (
                  <p className="mt-2 text-sm text-red-600">
                    {form.formState.errors.password.message}
                  </p>
                )}
              </div>

              {/* Remember Me & Forgot Password */}
              <div className="flex items-center justify-between pt-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="remember-me"
                    {...form.register('rememberMe')}
                    disabled={isLoading || isLocked}
                    className="h-4 w-4 text-sky-600 focus:ring-sky-500/50 border-gray-300 rounded bg-white/20 backdrop-blur-sm"
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-600">
                    Remember me
                  </label>
                </div>

                <Link 
                  href="/auth/reset-password" 
                  className="text-sm text-sky-600 hover:text-sky-700 transition-colors"
                >
                  Forgot password?
                </Link>
              </div>

              {/* Sign In Button */}
              <button
                type="submit"
                disabled={isLoading || isLocked}
                className="w-full mt-6 flex justify-center py-3 px-4 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-sky-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-[1.02]"
              >
                {isLoading ? (
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : null}
                {isLoading ? 'Signing in...' : isLocked ? `Locked (${Math.ceil(lockTimeRemaining / 60)}m)` : 'Sign in'}
              </button>

              {/* Biometric Authentication */}
              {biometricSupported && (
                <div className="mt-6 flex justify-center">
                  <button
                    type="button"
                    onClick={() => setShowBiometric(true)}
                    disabled={isLoading || isLocked}
                    className="inline-flex items-center justify-center w-12 h-12 bg-white/40 border border-gray-200/60 rounded-full text-gray-600 hover:bg-white/60 hover:text-sky-600 focus:outline-none focus:ring-2 focus:ring-sky-500/30 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 backdrop-blur-sm group"
                    title="Sign in with biometrics"
                  >
                    <svg className="w-6 h-6 transition-transform group-hover:scale-110" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M17.81 4.47c-.08 0-.16-.02-.23-.06C15.66 3.42 14 3 12.01 3c-1.98 0-3.86.47-5.57 1.41-.24.13-.54.04-.68-.2-.13-.24-.04-.55.2-.68C7.82 2.52 9.86 2 12.01 2c2.13 0 3.99.47 6.03 1.52.25.13.34.43.21.67-.09.18-.26.28-.44.28zM3.5 9.72c-.1 0-.2-.03-.29-.09-.23-.16-.28-.47-.12-.7.99-1.4 2.25-2.5 3.75-3.27C9.98 4.04 14 4.03 17.15 5.65c1.5.77 2.76 1.86 3.75 3.25.16.22.11.54-.12.7-.23.16-.54.11-.7-.12-.9-1.26-2.04-2.25-3.39-2.94-2.87-1.47-6.54-1.47-9.4.01-1.36.7-2.5 1.7-3.4 2.96-.08.14-.23.21-.39.21zm6.25 12.07c-.13 0-.26-.05-.35-.15-.87-.87-1.34-2.04-1.34-3.28 0-1.24.47-2.41 1.34-3.28.87-.87 2.04-1.34 3.28-1.34 1.24 0 2.41.47 3.28 1.34.87.87 1.34 2.04 1.34 3.28 0 1.24-.47 2.41-1.34 3.28-.09.1-.22.15-.35.15s-.26-.05-.35-.15c-.19-.19-.19-.51 0-.71.68-.68 1.05-1.58 1.05-2.57s-.37-1.89-1.05-2.57c-.68-.68-1.58-1.05-2.57-1.05s-1.89.37-2.57 1.05c-.68.68-1.05 1.58-1.05 2.57s.37 1.89 1.05 2.57c.19.2.19.52 0 .71-.1.1-.23.15-.35.15zm2.86-3.9c-.89 0-1.62.73-1.62 1.62s.73 1.62 1.62 1.62 1.62-.73 1.62-1.62-.73-1.62-1.62-1.62zm1.56-4.29c-.1 0-.2-.03-.29-.09-.6-.46-1.32-.71-2.08-.71-.76 0-1.48.25-2.08.71-.19.15-.47.11-.62-.08-.15-.19-.11-.47.08-.62.78-.6 1.71-.92 2.7-.92.99 0 1.92.32 2.7.92.19.15.23.43.08.62-.08.11-.21.17-.35.17z"/>
                    </svg>
                  </button>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}