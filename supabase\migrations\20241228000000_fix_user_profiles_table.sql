-- Fix Missing User Profiles Table
-- Creates the user_profiles table that is referenced by RLS policies but was never created

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- USER PROFILES TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name VARCHAR(255) NOT NULL,
  role_id UUID NOT NULL REFERENCES roles(id) ON DELETE SET NULL,
  avatar_url TEXT,
  phone VARCHAR(20),
  preferences JSONB DEFAULT '{}',
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  last_login_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Create indexes for user_profiles
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles (user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role_id ON user_profiles (role_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_active ON user_profiles (is_active);

-- =============================================
-- TRIGGER FOR UPDATED_AT
-- =============================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for user_profiles
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- RLS POLICIES FOR USER_PROFILES
-- =============================================
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Users can read their own profile
CREATE POLICY "Users can read own profile" ON user_profiles
  FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON user_profiles
  FOR UPDATE USING (auth.uid() = user_id);

-- Admin and managers can read all profiles
CREATE POLICY "Admin read access to user profiles" ON user_profiles
  FOR SELECT USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles up
      JOIN roles r ON up.role_id = r.id
      WHERE up.user_id = auth.uid() 
        AND r.name IN ('admin', 'manager')
    )
  );

-- Admin can manage all profiles
CREATE POLICY "Admin full access to user profiles" ON user_profiles
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles up
      JOIN roles r ON up.role_id = r.id
      WHERE up.user_id = auth.uid() 
        AND r.name = 'admin'
    )
  );

-- =============================================
-- INSERT DEFAULT ROLES IF NOT EXISTS
-- =============================================
INSERT INTO roles (id, name, display_name, description, level, is_system_role, is_active) VALUES 
  ('********-1111-1111-1111-********1111', 'admin', 'Administrator', 'System Administrator with full access', 1, true, true),
  ('********-2222-2222-2222-********2222', 'manager', 'Manager', 'Branch Manager with elevated permissions', 2, true, true),
  ('*************-3333-3333-************', 'staff', 'Staff', 'Staff Member with basic permissions', 3, true, true),
  ('*************-4444-4444-********4444', 'customer', 'Customer', 'Regular customer account', 5, true, true)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  display_name = EXCLUDED.display_name,
  description = EXCLUDED.description,
  level = EXCLUDED.level,
  updated_at = NOW();

-- =============================================
-- CREATE DEVELOPMENT ADMIN USER PROFILE
-- =============================================
-- Note: This creates placeholder entries. The actual auth.users entry 
-- should be created through Supabase Auth dashboard or API

-- Create a placeholder user profile for development
-- The user_id should be updated with the actual auth.users.id once created
INSERT INTO user_profiles (
  id, 
  user_id, 
  full_name, 
  role_id, 
  is_active, 
  created_at, 
  updated_at
) VALUES (
  'dev-admin-profile-id',
  '********-0000-0000-0000-********0000', -- Placeholder - update with real auth.users.id
  'Development Admin',
  '********-1111-1111-1111-********1111', -- admin role
  true,
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  full_name = EXCLUDED.full_name,
  role_id = EXCLUDED.role_id,
  is_active = EXCLUDED.is_active,
  updated_at = NOW();

-- =============================================
-- HELPER FUNCTION TO CREATE USER PROFILE
-- =============================================
CREATE OR REPLACE FUNCTION create_user_profile(
  auth_user_id UUID,
  profile_full_name VARCHAR(255),
  profile_role_name VARCHAR(50) DEFAULT 'customer'
)
RETURNS UUID AS $$
DECLARE
  profile_role_id UUID;
  new_profile_id UUID;
BEGIN
  -- Get role ID
  SELECT id INTO profile_role_id FROM roles WHERE name = profile_role_name;
  
  IF profile_role_id IS NULL THEN
    RAISE EXCEPTION 'Role % not found', profile_role_name;
  END IF;
  
  -- Create user profile
  INSERT INTO user_profiles (
    user_id,
    full_name,
    role_id
  ) VALUES (
    auth_user_id,
    profile_full_name,
    profile_role_id
  ) RETURNING id INTO new_profile_id;
  
  RETURN new_profile_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_user_profile(UUID, VARCHAR, VARCHAR) TO authenticated;

-- =============================================
-- ENABLE REALTIME
-- =============================================
ALTER PUBLICATION supabase_realtime ADD TABLE user_profiles;

-- =============================================
-- COMMENTS
-- =============================================
COMMENT ON TABLE user_profiles IS 
'User profiles table linking auth.users to roles and storing additional user information. To create admin user: 1) Create auth user via Supabase dashboard <NAME_EMAIL> 2) Update user_id in this table with the auth.users.id';

COMMENT ON FUNCTION create_user_profile(UUID, VARCHAR, VARCHAR) IS 
'Helper function to create user profile when new auth user is created. Call this after creating auth user.';