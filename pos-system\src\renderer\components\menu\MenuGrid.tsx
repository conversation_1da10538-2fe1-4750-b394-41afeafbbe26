import React, { useMemo, useCallback } from 'react';

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  preparationTime: number;
  image?: string;
  is_customizable?: boolean;
  customizations?: {
    id: string;
    name: string;
    options: {
      id: string;
      name: string;
      price: number;
    }[];
    required: boolean;
    maxSelections?: number;
  }[];
}

interface MenuCategory {
  id: string;
  name: string;
}

interface MenuGridProps {
  items: MenuItem[];
  selectedCategory: string;
  categories: MenuCategory[];
  onCategoryChange: (category: string) => void;
  onItemClick: (item: MenuItem) => void;
  showMostFrequented?: boolean;
  mostFrequentedTitle?: string;
  hideAllItemsButton?: boolean;
}

export const MenuGrid: React.FC<MenuGridProps> = React.memo(({
  items,
  selectedCategory,
  categories,
  onCategoryChange,
  onItemClick,
  showMostFrequented = false,
  mostFrequentedTitle = "Most Popular Items",
  hideAllItemsButton = false
}) => {
  const handleCategoryChange = useCallback((category: string) => {
    onCategoryChange(category);
  }, [onCategoryChange]);

  const handleItemClick = useCallback((item: MenuItem) => {
    onItemClick(item);
  }, [onItemClick]);

  const displayTitle = useMemo(() => {
    if (showMostFrequented && !selectedCategory) {
      return mostFrequentedTitle;
    }
    if (selectedCategory) {
      const category = categories.find(cat => cat.id === selectedCategory);
      return category ? category.name : 'Menu';
    }
    return 'Menu';
  }, [showMostFrequented, selectedCategory, mostFrequentedTitle, categories]);

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      {/* Category Filter */}
      <div className="mb-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">{displayTitle}</h2>
        <div className="flex flex-wrap gap-2">
          {/* All Items button - only show if not hidden */}
          {!hideAllItemsButton && (
            <button
              onClick={() => handleCategoryChange('')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                !selectedCategory
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {showMostFrequented ? mostFrequentedTitle : 'All Items'}
            </button>
          )}
          
          {/* Category buttons */}
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleCategoryChange(category.id)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Menu Items Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {items.map((item) => (
          <MenuItemCard
            key={item.id}
            item={item}
            onClick={() => handleItemClick(item)}
          />
        ))}
      </div>

      {items.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12 space-y-4">
          <div className="text-6xl opacity-50">🍽️</div>
          <div className="text-gray-500 text-lg font-medium">No items found</div>
          <div className="text-gray-400 text-sm">Try selecting a different category</div>
        </div>
      )}
    </div>
  );
});

interface MenuItemCardProps {
  item: MenuItem;
  onClick: () => void;
}

const MenuItemCard: React.FC<MenuItemCardProps> = React.memo(({ item, onClick }) => {
  return (
    <div
      onClick={onClick}
      className="border border-gray-200 rounded-lg p-4 cursor-pointer transform transition-all duration-200 hover:scale-105 hover:shadow-lg hover:border-blue-300 hover:-translate-y-1"
    >
      {/* Item Image Placeholder */}
      <div className="w-full h-32 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg mb-3 flex items-center justify-center overflow-hidden">
        {item.image ? (
          <img
            src={item.image}
            alt={item.name}
            className="w-full h-full object-cover rounded-lg transition-transform duration-200 hover:scale-110"
          />
        ) : (
          <div className="text-gray-400 text-4xl animate-pulse">🍽️</div>
        )}
      </div>

      {/* Item Details */}
      <div>
        <h3 className="font-semibold text-gray-900 mb-1">{item.name}</h3>
        <p className="text-gray-600 text-sm mb-2 line-clamp-2">{item.description}</p>
        
        <div className="flex justify-between items-center">
          <span className="text-lg font-bold text-green-600 bg-green-50 px-2 py-1 rounded-lg">
            ${item.price.toFixed(2)}
          </span>
          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
            ⏱️ {item.preparationTime} min
          </span>
        </div>

        {/* Customization Indicator */}
        {(item.is_customizable || (item.customizations && item.customizations.length > 0)) && (
          <div className="mt-2">
            <span className="text-xs bg-blue-100 text-blue-800 px-3 py-1 rounded-full border border-blue-200 animate-pulse">
              ⚙️ Customizable
            </span>
          </div>
        )}
      </div>
    </div>
  );
});