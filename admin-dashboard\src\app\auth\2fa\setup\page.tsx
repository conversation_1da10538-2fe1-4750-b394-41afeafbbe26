'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { useAuth } from '@/contexts/auth-context'
import { generateTwoFASecret, verifyTwoFASetup } from '../../../../../../shared/auth/two-fa-utils'

const setupSchema = z.object({
  code: z
    .string()
    .length(6, 'Verification code must be 6 digits')
    .regex(/^\d+$/, 'Code must contain only numbers'),
})

type SetupFormData = z.infer<typeof setupSchema>

export default function TwoFactorSetupPage() {
  const { t } = useTranslation()
  const router = useRouter()
  const { user, profile, updateProfile } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null)
  const [secret, setSecret] = useState<string | null>(null)
  const [manualEntryKey, setManualEntryKey] = useState<string | null>(null)

  const form = useForm<SetupFormData>({
    resolver: zodResolver(setupSchema),
    defaultValues: {
      code: '',
    },
  })

  useEffect(() => {
    if (!user || !profile) {
      router.push('/auth/login')
      return
    }

    // Temporarily disable 2FA redirect for setup/testing
    // TODO: Re-enable this check after testing
    // if (profile.two_fa_enabled) {
    //   router.push('/dashboard');
    //   return;
    // }

    generateQRCode()
  }, [user, router]) // Removed profile dependency to prevent re-running when profile updates

  const generateQRCode = async () => {
    if (!user || !profile) return

    try {
      setIsLoading(true)

      // Generate 2FA secret
      const secretResult = await generateTwoFASecret(user.id, user.email || '')

      if (!secretResult.success) {
        setError('Failed to generate 2FA secret')
        return
      }

      setSecret(secretResult.secret || '')
      setManualEntryKey(secretResult.secret || '')

      // Use the QR code directly
      setQrCodeUrl(secretResult.qr_code || null)
    } catch (err) {
      console.error('QR code generation error:', err)
      setError('Failed to generate QR code')
    } finally {
      setIsLoading(false)
    }
  }

  const onSubmit = async (data: SetupFormData) => {
    if (!user || !secret) {
      setError('Invalid setup state')
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      // Verify 2FA setup
      const verifyResult = await verifyTwoFASetup({
        user_id: user.id,
        secret,
        verification_code: data.code,
      })

      if (!verifyResult.success) {
        setError(verifyResult.error || t('auth.twoFactor.errors.invalidCode'))
        return
      }

      // Update profile to reflect 2FA enabled
      if (profile) {
        const updatedProfile = {
          ...profile,
          two_fa_enabled: true,
        }
        updateProfile(updatedProfile)
      }

      router.push('/dashboard?setup=2fa')
    } catch (err) {
      console.error('2FA setup error:', err)
      setError(t('auth.twoFactor.setup.errors.setupFailed'))
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    router.push('/dashboard')
  }

  if (!user || !profile) {
    return null // Will redirect to login
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center">
              {t('auth.twoFactor.setup.title')}
            </CardTitle>
            <CardDescription className="text-center">
              {t('auth.twoFactor.setup.subtitle')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Setup Instructions */}
            <div className="space-y-4">
              <div className="text-sm space-y-2">
                <p className="font-medium">Setup Instructions:</p>
                <ol className="list-decimal list-inside space-y-1 text-gray-600">
                  <li>{t('auth.twoFactor.setup.step1')}</li>
                  <li>{t('auth.twoFactor.setup.step2')}</li>
                  <li>{t('auth.twoFactor.setup.step3')}</li>
                </ol>
              </div>
            </div>

            {/* QR Code */}
            {qrCodeUrl && (
              <div className="text-center space-y-4">
                <div className="bg-white p-4 rounded-lg border inline-block">
                  <img src={qrCodeUrl} alt="2FA QR Code" className="w-48 h-48" />
                </div>
                <p className="text-sm text-gray-600">{t('auth.twoFactor.setup.qrCode')}</p>
              </div>
            )}

            {/* Manual Entry Key */}
            {manualEntryKey && (
              <div className="space-y-2">
                <p className="text-sm font-medium">{t('auth.twoFactor.setup.manualEntry')}:</p>
                <div className="bg-gray-100 p-3 rounded-md">
                  <code className="text-sm break-all">{manualEntryKey}</code>
                </div>
              </div>
            )}

            {/* Verification Form */}
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('auth.twoFactor.code')}</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="123456"
                          maxLength={6}
                          className="text-center text-lg tracking-widest"
                          {...field}
                          disabled={isLoading}
                          autoComplete="one-time-code"
                          inputMode="numeric"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {error && (
                  <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">{error}</div>
                )}

                <div className="flex space-x-3">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={handleCancel}
                    disabled={isLoading}
                  >
                    {t('auth.twoFactor.setup.cancel')}
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1"
                    disabled={isLoading || !form.watch('code') || form.watch('code').length !== 6}
                  >
                    {isLoading ? t('common.loading') : t('auth.twoFactor.setup.enable')}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
