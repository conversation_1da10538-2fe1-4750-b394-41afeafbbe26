'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { GlassCard, GlassButton } from '@/components/ui/glass-components';
import { Minus, Plus, Trash2, ShoppingCart } from 'lucide-react';

// Types for cart items
interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  category: string;
  customizations?: string[];
}

interface CartSummaryProps {
  className?: string;
}

export function CartSummary({ className = '' }: CartSummaryProps) {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load cart from localStorage on component mount
  useEffect(() => {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      try {
        setCartItems(JSON.parse(savedCart));
      } catch (error) {
        console.error('Error loading cart:', error);
      }
    }
  }, []);

  // Save cart to localStorage whenever cartItems changes
  useEffect(() => {
    localStorage.setItem('cart', JSON.stringify(cartItems));
  }, [cartItems]);

  // Calculate totals
  const subtotal = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const tax = subtotal * 0.1; // 10% tax
  const deliveryFee = subtotal > 25 ? 0 : 3.99; // Free delivery over $25
  const total = subtotal + tax + deliveryFee;

  // Update item quantity
  const updateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(itemId);
      return;
    }

    setCartItems(prev =>
      prev.map(item => (item.id === itemId ? { ...item, quantity: newQuantity } : item))
    );
  };

  // Remove item from cart
  const removeItem = (itemId: string) => {
    setCartItems(prev => prev.filter(item => item.id !== itemId));
  };

  // Clear entire cart
  const clearCart = () => {
    setCartItems([]);
  };

  // Add item to cart (called from menu items)
  const addToCart = (item: Omit<CartItem, 'quantity'>) => {
    setCartItems(prev => {
      const existingItem = prev.find(cartItem => cartItem.id === item.id);

      if (existingItem) {
        return prev.map(cartItem =>
          cartItem.id === item.id ? { ...cartItem, quantity: cartItem.quantity + 1 } : cartItem
        );
      } else {
        return [...prev, { ...item, quantity: 1 }];
      }
    });
  };

  // Expose addToCart function globally for menu items to use
  useEffect(() => {
    (window as any).addToCart = addToCart;
    return () => {
      delete (window as any).addToCart;
    };
  }, []);

  if (cartItems.length === 0) {
    return (
      <GlassCard className={`p-6 ${className}`}>
        <div className="text-center">
          <ShoppingCart className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-semibold mb-2">Your Cart is Empty</h3>
          <p className="text-muted-foreground mb-4">Add items from the menu to get started</p>
        </div>
      </GlassCard>
    );
  }

  return (
    <GlassCard className={`p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold">Your Order</h3>
        <span className="text-sm text-muted-foreground">
          {cartItems.length} {cartItems.length === 1 ? 'item' : 'items'}
        </span>
      </div>

      {/* Cart Items */}
      <div className="space-y-4 mb-6 max-h-96 overflow-y-auto">
        {cartItems.map(item => (
          <div key={item.id} className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg">
            <div className="relative w-16 h-16 flex-shrink-0">
              <Image src={item.image} alt={item.name} fill className="object-cover rounded-md" />
            </div>

            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-sm truncate">{item.name}</h4>
              <p className="text-xs text-muted-foreground">${item.price.toFixed(2)} each</p>
              {item.customizations && item.customizations.length > 0 && (
                <p className="text-xs text-muted-foreground mt-1">
                  {item.customizations.join(', ')}
                </p>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                className="w-6 h-6 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-colors"
                disabled={isLoading}
              >
                <Minus className="w-3 h-3" />
              </button>

              <span className="w-8 text-center text-sm font-medium">{item.quantity}</span>

              <button
                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                className="w-6 h-6 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-colors"
                disabled={isLoading}
              >
                <Plus className="w-3 h-3" />
              </button>
            </div>

            <button
              onClick={() => removeItem(item.id)}
              className="w-6 h-6 rounded-full bg-red-500/20 hover:bg-red-500/30 flex items-center justify-center transition-colors text-red-400"
              disabled={isLoading}
            >
              <Trash2 className="w-3 h-3" />
            </button>
          </div>
        ))}
      </div>

      {/* Order Summary */}
      <div className="border-t border-white/10 pt-4 space-y-2">
        <div className="flex justify-between text-sm">
          <span>Subtotal</span>
          <span>${subtotal.toFixed(2)}</span>
        </div>

        <div className="flex justify-between text-sm">
          <span>Tax</span>
          <span>${tax.toFixed(2)}</span>
        </div>

        <div className="flex justify-between text-sm">
          <span>Delivery Fee</span>
          <span>
            {deliveryFee === 0 ? (
              <span className="text-green-500">FREE</span>
            ) : (
              `$${deliveryFee.toFixed(2)}`
            )}
          </span>
        </div>

        {subtotal < 25 && deliveryFee > 0 && (
          <p className="text-xs text-muted-foreground">
            Add ${(25 - subtotal).toFixed(2)} more for free delivery
          </p>
        )}

        <div className="flex justify-between font-semibold text-lg pt-2 border-t border-white/10">
          <span>Total</span>
          <span>${total.toFixed(2)}</span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="mt-6 space-y-3">
        <GlassButton variant="primary" size="lg" className="w-full" disabled={isLoading}>
          Proceed to Checkout
        </GlassButton>

        <button
          onClick={clearCart}
          className="w-full text-sm text-muted-foreground hover:text-foreground transition-colors"
          disabled={isLoading}
        >
          Clear Cart
        </button>
      </div>
    </GlassCard>
  );
}

// Export the addToCart function for use in menu items
export const useCart = () => {
  const addToCart = (item: Omit<CartItem, 'quantity'>) => {
    if (typeof window !== 'undefined' && (window as any).addToCart) {
      (window as any).addToCart(item);
    }
  };

  return { addToCart };
};
