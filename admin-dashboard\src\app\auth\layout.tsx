'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import '../../lib/i18n' // Import i18n configuration

// Security utilities with safe DOM manipulation
const addSecurityMeta = () => {
  if (typeof document === 'undefined') return null

  const metaElements: HTMLMetaElement[] = []

  try {
    // Remove any existing meta tags that might leak information
    const existingMetas = document.querySelectorAll('meta[name*="dashboard"], meta[name*="admin"]')
    existingMetas.forEach(meta => {
      if (meta.parentNode) {
        meta.parentNode.removeChild(meta)
      }
    })
    
    // Add auth-specific meta tags
    const authMeta = document.createElement('meta')
    authMeta.name = 'robots'
    authMeta.content = 'noindex, nofollow, noarchive, nosnippet, noimageindex'
    document.head.appendChild(authMeta)
    metaElements.push(authMeta)
    
    // Prevent page caching
    const cacheControlMeta = document.createElement('meta')
    cacheControlMeta.httpEquiv = 'Cache-Control'
    cacheControlMeta.content = 'no-store, no-cache, must-revalidate, max-age=0'
    document.head.appendChild(cacheControlMeta)
    metaElements.push(cacheControlMeta)
    
    // Add referrer policy
    const referrerMeta = document.createElement('meta')
    referrerMeta.name = 'referrer'
    referrerMeta.content = 'strict-origin-when-cross-origin'
    document.head.appendChild(referrerMeta)
    metaElements.push(referrerMeta)

    return metaElements
  } catch (error) {
    console.warn('Failed to add security meta tags:', error)
    return null
  }
}

// Enhanced loading component with security features
const SecureLoadingScreen = () => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
    <div className="text-center">
      <div className="relative">
        {/* Animated loading spinner */}
        <div className="w-16 h-16 border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin mx-auto mb-4"></div>
        {/* Pulse effect */}
        <div className="absolute inset-0 w-16 h-16 border-4 border-purple-400 rounded-full animate-ping opacity-20 mx-auto"></div>
      </div>
      <p className="text-purple-200 text-sm font-medium">Securing connection...</p>
    </div>
  </div>
)

// Isolated auth container with enhanced security
const IsolatedAuthContainer = ({ children }: { children: React.ReactNode }) => {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    // Add security meta tags and store references for cleanup
    const metaElements = addSecurityMeta()
    
    // Clear any potential dashboard state from memory
    if (typeof window !== 'undefined') {
      // Clear dashboard-related localStorage/sessionStorage
      const keysToRemove = Object.keys(localStorage).filter(key => 
        key.includes('dashboard') || key.includes('admin') || key.includes('nav')
      )
      keysToRemove.forEach(key => localStorage.removeItem(key))
      
      // Clear session storage
      const sessionKeysToRemove = Object.keys(sessionStorage).filter(key => 
        key.includes('dashboard') || key.includes('admin') || key.includes('nav')
      )
      sessionKeysToRemove.forEach(key => sessionStorage.removeItem(key))
    }
    
    setMounted(true)
    
    return () => {
      // Safe cleanup on unmount using stored references
      if (metaElements && Array.isArray(metaElements)) {
        metaElements.forEach(meta => {
          try {
            if (meta && meta.parentNode) {
              meta.parentNode.removeChild(meta)
            }
          } catch (error) {
            console.warn('Failed to remove meta element:', error)
          }
        })
      }
    }
  }, [])

  if (!mounted) {
    return <SecureLoadingScreen />
  }

  return (
    <div className="min-h-screen bg-gray-50 relative">
      {/* Simple, clean background - no animations */}
      
      {/* Security indicator - simplified */}
      <div className="absolute top-4 right-4 flex items-center space-x-2 text-gray-600 text-xs">
        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
        <span>Secure Connection</span>
      </div>
      
      {/* Main auth content */}
      <div className="flex min-h-screen flex-col items-center justify-center py-12 sm:px-6 lg:px-8">
        {/* Logo section - simplified */}
        <div className="sm:mx-auto sm:w-full sm:max-w-md mb-8">
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-blue-600 rounded-xl flex items-center justify-center">
              <span className="text-2xl font-bold text-white">🔐</span>
            </div>
          </div>
          <h1 className="mt-6 text-center text-2xl font-bold text-gray-900">
            Admin Authentication
          </h1>
          <p className="mt-2 text-center text-sm text-gray-600">
            Secure access to administrative dashboard
          </p>
        </div>
        
        {/* Auth form container */}
        <div className="w-full max-w-md">
          {children}
        </div>
        
        {/* Security footer - simplified */}
        <div className="mt-8 text-center">
          <p className="text-xs text-gray-500">
            Protected by enterprise-grade security
          </p>
        </div>
      </div>
      

    </div>
  )
}

export default function AuthLayout({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [securityCheck, setSecurityCheck] = useState(false)

  // Enhanced security check
  useEffect(() => {
    // Perform security validation
    const performSecurityCheck = () => {
      // Check if we're in a secure context
      if (typeof window !== 'undefined') {
        // Verify HTTPS in production
        if (process.env.NODE_ENV === 'production' && window.location.protocol !== 'https:') {
          console.warn('Insecure connection detected')
        }
        
        // Clear any potential XSS attempts
        const scripts = document.querySelectorAll('script[src*="javascript:"], script[src*="data:"]')
        scripts.forEach(script => script.remove())
        
        // Prevent clickjacking
        if (window.self !== window.top && window.top) {
          window.top.location.href = window.self.location.href
        }
      }
      
      setSecurityCheck(true)
    }
    
    performSecurityCheck()
  }, [])

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated && !isLoading && securityCheck) {
      // Allow access to 2FA setup page even when authenticated
      const currentPath = window.location.pathname
      if (!currentPath.includes('/auth/2fa/setup')) {
        router.push('/dashboard')
      }
    }
  }, [isAuthenticated, isLoading, router, securityCheck])

  // Show secure loading state while checking authentication and security
  if (isLoading || !securityCheck) {
    return <SecureLoadingScreen />
  }

  // If not authenticated, show isolated auth interface
  return (
    <IsolatedAuthContainer>
      {children}
    </IsolatedAuthContainer>
  )
}
