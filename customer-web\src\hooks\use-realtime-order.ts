/**
 * Enhanced Real-time Order Tracking Hook
 * Provides comprehensive order status updates, driver location, and preparation progress
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '../lib/supabase/database.types';
import type { Order, OrderStatus } from '../types/types';
import {
  notificationService,
  type OrderNotificationData,
} from '../lib/notifications/notification-service';

export interface OrderTrackingData {
  order: Order | null;
  status: OrderStatus | null;
  estimatedDeliveryTime: string | null;
  preparationProgress: number;
  driverLocation: {
    lat: number;
    lng: number;
    heading?: number;
    speed?: number;
  } | null;
  statusHistory: OrderStatusHistoryItem[];
  loading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

export interface OrderStatusHistoryItem {
  status: OrderStatus;
  timestamp: string;
  message?: string;
  estimatedTime?: string;
}

export interface DeliveryUpdate {
  driverId: string;
  driverName: string;
  driverPhone: string;
  vehicleInfo: string;
  location: {
    lat: number;
    lng: number;
    heading?: number;
    speed?: number;
  };
  estimatedArrival: string;
}

export interface PreparationUpdate {
  stage: 'received' | 'ingredients' | 'cooking' | 'plating' | 'ready';
  progress: number;
  estimatedCompletion: string;
  message?: string;
}

/**
 * Enhanced hook for real-time order tracking
 */
export function useRealtimeOrder(orderId?: string, userId?: string) {
  const [trackingData, setTrackingData] = useState<OrderTrackingData>({
    order: null,
    status: null,
    estimatedDeliveryTime: null,
    preparationProgress: 0,
    driverLocation: null,
    statusHistory: [],
    loading: true,
    error: null,
    lastUpdated: null,
  });

  const supabase = createClientComponentClient<Database>();
  const subscriptionsRef = useRef<any[]>([]);
  const notificationSentRef = useRef<Set<string>>(new Set());
  const channelIdRef = useRef<string>(crypto.randomUUID());
  const isSubscribedRef = useRef<boolean>(false);

  /**
   * Fetch initial order data
   */
  const fetchOrderData = useCallback(async () => {
    if (!orderId) return;

    try {
      setTrackingData(prev => ({ ...prev, loading: true, error: null }));

      // Fetch order with related data
      const { data: orderData, error: orderError } = await supabase
        .from('orders')
        .select(
          `
          *,
          order_items (
            *,
            subcategories (*)
          ),
          delivery_assignments (
            *,
            drivers (*)
          )
        `
        )
        .eq('id', orderId)
        .single();

      if (orderError) {
        throw new Error(orderError.message);
      }

      // Fetch status history
      const { data: historyData, error: historyError } = await supabase
        .from('order_status_history')
        .select('*')
        .eq('order_id', orderId)
        .order('created_at', { ascending: true });

      if (historyError) {
        console.warn('Failed to fetch status history:', historyError);
      }

      // Fetch preparation progress
      const { data: progressData, error: progressError } = await supabase
        .from('order_preparation_progress')
        .select('*')
        .eq('order_id', orderId)
        .single();

      if (progressError && progressError.code !== 'PGRST116') {
        console.warn('Failed to fetch preparation progress:', progressError);
      }

      setTrackingData(prev => ({
        ...prev,
        order: orderData as Order,
        status: orderData.status as OrderStatus,
        estimatedDeliveryTime: orderData.estimated_delivery_time,
        preparationProgress: progressData?.progress || 0,
        statusHistory:
          historyData?.map(item => ({
            status: item.status as OrderStatus,
            timestamp: item.created_at,
            message: item.message,
            estimatedTime: item.estimated_time,
          })) || [],
        loading: false,
        lastUpdated: new Date().toISOString(),
      }));
    } catch (error) {
      setTrackingData(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to fetch order data',
        loading: false,
      }));
    }
  }, [orderId, supabase]);

  /**
   * Handle order status updates
   */
  const handleOrderUpdate = useCallback(
    async (payload: any) => {
      const updatedOrder = payload.new;

      setTrackingData(prev => ({
        ...prev,
        order: { ...prev.order, ...updatedOrder } as Order,
        status: updatedOrder.status as OrderStatus,
        estimatedDeliveryTime: updatedOrder.estimated_delivery_time,
        lastUpdated: new Date().toISOString(),
      }));

      // Send notification if status changed and user preferences allow
      if (userId && updatedOrder.status !== trackingData.status) {
        const notificationKey = `${orderId}-${updatedOrder.status}`;

        if (!notificationSentRef.current.has(notificationKey)) {
          notificationSentRef.current.add(notificationKey);

          const preferences = await notificationService.getUserNotificationPreferences(userId);

          const notificationData: OrderNotificationData = {
            orderId: orderId!,
            status: updatedOrder.status,
            estimatedTime: updatedOrder.estimated_delivery_time,
            preparationProgress: trackingData.preparationProgress,
          };

          await notificationService.sendOrderStatusNotification(
            userId,
            notificationData,
            preferences
          );
        }
      }
    },
    [orderId, userId, trackingData.status, trackingData.preparationProgress]
  );

  /**
   * Handle delivery location updates
   */
  const handleDeliveryUpdate = useCallback((payload: any) => {
    const deliveryData = payload.new;

    setTrackingData(prev => ({
      ...prev,
      driverLocation: {
        lat: deliveryData.current_lat,
        lng: deliveryData.current_lng,
        heading: deliveryData.heading,
        speed: deliveryData.speed,
      },
      estimatedDeliveryTime: deliveryData.estimated_arrival,
      lastUpdated: new Date().toISOString(),
    }));
  }, []);

  /**
   * Handle preparation progress updates
   */
  const handlePreparationUpdate = useCallback((payload: any) => {
    const progressData = payload.new;

    setTrackingData(prev => ({
      ...prev,
      preparationProgress: progressData.progress,
      estimatedDeliveryTime: progressData.estimated_completion,
      lastUpdated: new Date().toISOString(),
    }));
  }, []);

  /**
   * Handle status history updates
   */
  const handleStatusHistoryUpdate = useCallback((payload: any) => {
    const historyItem = payload.new;

    setTrackingData(prev => ({
      ...prev,
      statusHistory: [
        ...prev.statusHistory,
        {
          status: historyItem.status as OrderStatus,
          timestamp: historyItem.created_at,
          message: historyItem.message,
          estimatedTime: historyItem.estimated_time,
        },
      ].sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()),
      lastUpdated: new Date().toISOString(),
    }));
  }, []);

  /**
   * Setup real-time subscriptions
   */
  const setupSubscriptions = useCallback(() => {
    if (!orderId || isSubscribedRef.current) return;

    // Clean up existing subscriptions
    subscriptionsRef.current.forEach(sub => {
      try {
        sub.unsubscribe();
      } catch (error) {
        console.warn('Error unsubscribing:', error);
      }
    });
    subscriptionsRef.current = [];

    const uniqueId = channelIdRef.current;

    // Subscribe to order updates
    const orderSubscription = supabase
      .channel(`order-${orderId}-${uniqueId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'orders',
          filter: `id=eq.${orderId}`,
        },
        handleOrderUpdate
      )
      .subscribe();

    // Subscribe to delivery location updates
    const deliverySubscription = supabase
      .channel(`delivery-${orderId}-${uniqueId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'delivery_tracking',
          filter: `order_id=eq.${orderId}`,
        },
        handleDeliveryUpdate
      )
      .subscribe();

    // Subscribe to preparation progress updates
    const preparationSubscription = supabase
      .channel(`preparation-${orderId}-${uniqueId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'order_preparation_progress',
          filter: `order_id=eq.${orderId}`,
        },
        handlePreparationUpdate
      )
      .subscribe();

    // Subscribe to status history updates
    const historySubscription = supabase
      .channel(`history-${orderId}-${uniqueId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'order_status_history',
          filter: `order_id=eq.${orderId}`,
        },
        handleStatusHistoryUpdate
      )
      .subscribe();

    subscriptionsRef.current = [
      orderSubscription,
      deliverySubscription,
      preparationSubscription,
      historySubscription,
    ];

    isSubscribedRef.current = true;
  }, [
    orderId,
    supabase,
    handleOrderUpdate,
    handleDeliveryUpdate,
    handlePreparationUpdate,
    handleStatusHistoryUpdate,
  ]);

  /**
   * Refresh order data manually
   */
  const refreshOrder = useCallback(() => {
    fetchOrderData();
  }, [fetchOrderData]);

  /**
   * Get estimated time remaining
   */
  const getTimeRemaining = useCallback(() => {
    if (!trackingData.estimatedDeliveryTime) return null;

    const now = new Date();
    const estimated = new Date(trackingData.estimatedDeliveryTime);
    const diff = estimated.getTime() - now.getTime();

    if (diff <= 0) return 'Any moment now';

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  }, [trackingData.estimatedDeliveryTime]);

  /**
   * Get current status message
   */
  const getStatusMessage = useCallback(() => {
    const { status, preparationProgress } = trackingData;

    switch (status) {
      case 'pending':
        return 'Order received and being reviewed';
      case 'confirmed':
        return 'Order confirmed and queued for preparation';
      case 'preparing':
        return `Being prepared (${preparationProgress}% complete)`;
      case 'ready':
        return 'Ready for pickup';
      case 'out_for_delivery':
        return 'Out for delivery';
      case 'delivered':
        return 'Delivered successfully';
      case 'cancelled':
        return 'Order cancelled';
      default:
        return 'Status unknown';
    }
  }, [trackingData.status, trackingData.preparationProgress]);

  // Initialize on mount
  useEffect(() => {
    if (!orderId) return;

    fetchOrderData();
    setupSubscriptions();

    return () => {
      // Clean up subscriptions on unmount
      isSubscribedRef.current = false;
      subscriptionsRef.current.forEach(sub => {
        try {
          sub.unsubscribe();
        } catch (error) {
          console.warn('Error during cleanup:', error);
        }
      });
      subscriptionsRef.current = [];
    };
  }, [orderId, fetchOrderData, setupSubscriptions]);

  return {
    ...trackingData,
    refreshOrder,
    getTimeRemaining,
    getStatusMessage,
  };
}

export default useRealtimeOrder;
