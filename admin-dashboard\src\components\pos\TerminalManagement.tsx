'use client'

import { useState } from 'react'
import { Monitor, Wifi, Bluetooth, RefreshCw, CheckCircle, AlertCircle, Clock } from 'lucide-react'
import { GlassCard } from '@/components/ui/glass-components'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { toast } from 'react-hot-toast'
import { useTheme } from '@/contexts/theme-context'

interface POSTerminal {
  id: string
  name: string
  location: string
  isActive: boolean
  lastSync: string
  syncStatus: 'synced' | 'pending' | 'failed'
}

interface TerminalSettings {
  display_brightness: number
  screen_timeout: number
  audio_enabled: boolean
  receipt_auto_print: boolean
  cash_drawer_enabled: boolean
  barcode_scanner_enabled: boolean
  customer_display_enabled: boolean
  touch_sensitivity: string
}

interface HardwareSettings {
  cash_drawer_port: string
  barcode_scanner_port: string
  card_reader_enabled: boolean
  scale_enabled: boolean
  scale_port: string
  loyalty_card_reader: boolean
  wifi_ssid: string
  ethernet_enabled: boolean
}

interface TerminalManagementProps {
  terminals: POSTerminal[]
  selectedTerminal: string
  terminalSettings: TerminalSettings
  hardwareSettings: HardwareSettings
  onTerminalSelect: (terminalId: string) => void
  onTerminalSettingsChange: (settings: TerminalSettings) => void
  onHardwareSettingsChange: (settings: HardwareSettings) => void
  onSyncTerminal: (terminalId: string) => void
}

export function TerminalManagement({
  terminals,
  selectedTerminal,
  terminalSettings,
  hardwareSettings,
  onTerminalSelect,
  onTerminalSettingsChange,
  onHardwareSettingsChange,
  onSyncTerminal
}: TerminalManagementProps) {
  const { isDarkTheme } = useTheme()
  const getSyncStatusIcon = (status: string) => {
    switch (status) {
      case 'synced':
        return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-400" />
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-400" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const handleSyncAll = () => {
    terminals.forEach(terminal => {
      if (terminal.isActive) {
        onSyncTerminal(terminal.id)
      }
    })
    toast.success('Syncing all active terminals...')
  }

  return (
    <div className="space-y-6">
      {/* Terminal Status Grid */}
      <GlassCard className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className={`text-xl font-semibold transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>Terminal Status</h3>
          <Button onClick={handleSyncAll} className="bg-blue-600 hover:bg-blue-700">
            <RefreshCw className="h-4 w-4 mr-2" />
            Sync All
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {terminals.map((terminal) => (
            <div
              key={terminal.id}
              className={`p-4 rounded-lg border cursor-pointer transition-all duration-1000 ${
                selectedTerminal === terminal.id
                  ? 'border-blue-400 bg-blue-500/10'
                  : isDarkTheme
                    ? 'border-white/10 bg-white/5 hover:bg-white/10'
                    : 'border-black/10 bg-black/5 hover:bg-black/10'
              }`}
              onClick={() => onTerminalSelect(terminal.id)}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Monitor className="h-5 w-5 text-blue-400" />
                  <span className={`font-medium transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white' : 'text-black'
                  }`}>{terminal.name}</span>
                </div>
                {getSyncStatusIcon(terminal.syncStatus)}
              </div>

              <div className={`text-sm mb-2 transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/60' : 'text-black/60'
              }`}>{terminal.location}</div>
              
              <div className="flex items-center justify-between">
                <span className={`text-xs px-2 py-1 rounded ${
                  terminal.isActive 
                    ? 'bg-green-500/20 text-green-400' 
                    : 'bg-red-500/20 text-red-400'
                }`}>
                  {terminal.isActive ? 'Active' : 'Inactive'}
                </span>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation()
                    onSyncTerminal(terminal.id)
                  }}
                  className="text-blue-400 hover:text-blue-300"
                >
                  <RefreshCw className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </GlassCard>

      {/* Terminal Settings */}
      <GlassCard className="p-6">
        <h3 className={`text-xl font-semibold mb-6 transition-colors duration-1000 ${
          isDarkTheme ? 'text-white' : 'text-black'
        }`}>Terminal Settings</h3>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Display Settings */}
          <div className="space-y-4">
            <h4 className={`text-lg font-medium transition-colors duration-1000 ${
              isDarkTheme ? 'text-white' : 'text-black'
            }`}>Display Settings</h4>

            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Display Brightness: {terminalSettings.display_brightness}%</label>
              <Slider
                value={[terminalSettings.display_brightness]}
                onValueChange={(value: number[]) => 
                  onTerminalSettingsChange({ ...terminalSettings, display_brightness: value[0] })
                }
                max={100}
                step={5}
                className="w-full"
              />
            </div>
            
            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Screen Timeout (seconds)</label>
              <Select
                value={terminalSettings.screen_timeout.toString()}
                onValueChange={(value: string) =>
                  onTerminalSettingsChange({ ...terminalSettings, screen_timeout: parseInt(value) })
                }
              >
                <SelectTrigger className={`transition-all duration-1000 ${
                  isDarkTheme ? 'bg-white/5 border-white/10 text-white' : 'bg-black/5 border-black/10 text-black'
                }`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="60">1 minute</SelectItem>
                  <SelectItem value="300">5 minutes</SelectItem>
                  <SelectItem value="600">10 minutes</SelectItem>
                  <SelectItem value="1800">30 minutes</SelectItem>
                  <SelectItem value="0">Never</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm text-white/80">Touch Sensitivity</label>
              <Select
                value={terminalSettings.touch_sensitivity}
                onValueChange={(value: string) => 
                  onTerminalSettingsChange({ ...terminalSettings, touch_sensitivity: value })
                }
              >
                <SelectTrigger className="bg-white/5 border-white/10 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Hardware Settings */}
          <div className="space-y-4">
            <h4 className={`text-lg font-medium transition-colors duration-1000 ${
              isDarkTheme ? 'text-white' : 'text-black'
            }`}>Hardware Settings</h4>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Audio Enabled</span>
              <Switch
                checked={terminalSettings.audio_enabled}
                onCheckedChange={(checked: boolean) => 
                  onTerminalSettingsChange({ ...terminalSettings, audio_enabled: checked })
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Auto Print Receipts</span>
              <Switch
                checked={terminalSettings.receipt_auto_print}
                onCheckedChange={(checked: boolean) =>
                  onTerminalSettingsChange({ ...terminalSettings, receipt_auto_print: checked })
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Cash Drawer Enabled</span>
              <Switch
                checked={terminalSettings.cash_drawer_enabled}
                onCheckedChange={(checked: boolean) =>
                  onTerminalSettingsChange({ ...terminalSettings, cash_drawer_enabled: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Barcode Scanner</span>
              <Switch
                checked={terminalSettings.barcode_scanner_enabled}
                onCheckedChange={(checked: boolean) =>
                  onTerminalSettingsChange({ ...terminalSettings, barcode_scanner_enabled: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Customer Display</span>
              <Switch
                checked={terminalSettings.customer_display_enabled}
                onCheckedChange={(checked: boolean) =>
                  onTerminalSettingsChange({ ...terminalSettings, customer_display_enabled: checked })
                }
              />
            </div>
          </div>
        </div>
      </GlassCard>

      {/* Hardware Configuration */}
      <GlassCard className="p-6">
        <h3 className={`text-xl font-semibold mb-6 transition-colors duration-1000 ${
          isDarkTheme ? 'text-white' : 'text-black'
        }`}>Hardware Configuration</h3>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Cash Drawer Port</label>
              <Select
                value={hardwareSettings.cash_drawer_port}
                onValueChange={(value: string) =>
                  onHardwareSettingsChange({ ...hardwareSettings, cash_drawer_port: value })
                }
              >
                <SelectTrigger className={`transition-all duration-1000 ${
                  isDarkTheme ? 'bg-white/5 border-white/10 text-white' : 'bg-black/5 border-black/10 text-black'
                }`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="COM1">COM1</SelectItem>
                  <SelectItem value="COM2">COM2</SelectItem>
                  <SelectItem value="USB">USB</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Barcode Scanner Port</label>
              <Select
                value={hardwareSettings.barcode_scanner_port}
                onValueChange={(value: string) =>
                  onHardwareSettingsChange({ ...hardwareSettings, barcode_scanner_port: value })
                }
              >
                <SelectTrigger className={`transition-all duration-1000 ${
                  isDarkTheme ? 'bg-white/5 border-white/10 text-white' : 'bg-black/5 border-black/10 text-black'
                }`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USB">USB</SelectItem>
                  <SelectItem value="COM1">COM1</SelectItem>
                  <SelectItem value="COM2">COM2</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Card Reader Enabled</span>
              <Switch
                checked={hardwareSettings.card_reader_enabled}
                onCheckedChange={(checked: boolean) =>
                  onHardwareSettingsChange({ ...hardwareSettings, card_reader_enabled: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Scale Enabled</span>
              <Switch
                checked={hardwareSettings.scale_enabled}
                onCheckedChange={(checked: boolean) => 
                  onHardwareSettingsChange({ ...hardwareSettings, scale_enabled: checked })
                }
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Loyalty Card Reader</span>
              <Switch
                checked={hardwareSettings.loyalty_card_reader}
                onCheckedChange={(checked: boolean) =>
                  onHardwareSettingsChange({ ...hardwareSettings, loyalty_card_reader: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Ethernet Enabled</span>
              <Switch
                checked={hardwareSettings.ethernet_enabled}
                onCheckedChange={(checked: boolean) =>
                  onHardwareSettingsChange({ ...hardwareSettings, ethernet_enabled: checked })
                }
              />
            </div>
          </div>
        </div>
      </GlassCard>
    </div>
  )
}