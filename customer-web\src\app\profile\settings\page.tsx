import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { GlassCard, GlassButton, GlassInput } from '@/components/ui/glass-components';

export const metadata: Metadata = {
  title: 'Account Settings | Delicious Crepes & Waffles',
  description: 'Manage your account settings and preferences.',
};

export default function SettingsPage() {
  // Mock user data - in a real app, this would come from the AuthProvider
  const user = {
    id: 'user123',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/images/avatar.jpg',
    preferences: {
      language: 'en',
      theme: 'light',
      notifications: {
        email: true,
        push: true,
        sms: false,
        orderUpdates: true,
        promotions: true,
        newsletter: false,
      },
      privacy: {
        shareOrderHistory: false,
        allowDataCollection: true,
      },
    },
  };

  return (
    <main className="min-h-screen py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Account Settings</h1>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <GlassCard>
              <div className="p-6">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="relative h-16 w-16 rounded-full overflow-hidden">
                    <Image src={user.avatar} alt={user.name} fill className="object-cover" />
                  </div>
                  <div>
                    <h2 className="font-semibold">{user.name}</h2>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                  </div>
                </div>

                <nav className="space-y-2">
                  <Link
                    href="/profile"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Personal Information
                  </Link>
                  <Link
                    href="/profile/addresses"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Addresses
                  </Link>
                  <Link
                    href="/profile/payment-methods"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Payment Methods
                  </Link>
                  <Link
                    href="/profile/favorites"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Favorites
                  </Link>
                  <Link
                    href="/profile/settings"
                    className="block p-2 rounded-md bg-primary/10 text-primary font-medium"
                  >
                    Settings
                  </Link>
                  <Link
                    href="/orders"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Order History
                  </Link>
                </nav>
              </div>
            </GlassCard>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Language & Theme Settings */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-6">Display Settings</h2>

                <div className="space-y-6">
                  <div>
                    <h3 className="font-medium mb-3">Language</h3>
                    <div className="flex space-x-4">
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="language"
                          className="h-4 w-4 text-primary"
                          defaultChecked={user.preferences.language === 'en'}
                        />

                        <span>English</span>
                      </label>

                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="language"
                          className="h-4 w-4 text-primary"
                          defaultChecked={user.preferences.language === 'el'}
                        />

                        <span>Greek</span>
                      </label>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-3">Theme</h3>
                    <div className="flex space-x-4">
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="theme"
                          className="h-4 w-4 text-primary"
                          defaultChecked={user.preferences.theme === 'light'}
                        />

                        <span>Light</span>
                      </label>

                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="theme"
                          className="h-4 w-4 text-primary"
                          defaultChecked={user.preferences.theme === 'dark'}
                        />

                        <span>Dark</span>
                      </label>

                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="theme"
                          className="h-4 w-4 text-primary"
                          defaultChecked={user.preferences.theme === 'system'}
                        />

                        <span>System</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end">
                  <GlassButton variant="primary">Save Display Settings</GlassButton>
                </div>
              </div>
            </GlassCard>

            {/* Notification Settings */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-6">Notification Settings</h2>

                <div className="space-y-6">
                  <div>
                    <h3 className="font-medium mb-3">Notification Channels</h3>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-primary rounded"
                          defaultChecked={user.preferences.notifications.email}
                        />

                        <span>Email Notifications</span>
                      </label>

                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-primary rounded"
                          defaultChecked={user.preferences.notifications.push}
                        />

                        <span>Push Notifications</span>
                      </label>

                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-primary rounded"
                          defaultChecked={user.preferences.notifications.sms}
                        />

                        <span>SMS Notifications</span>
                      </label>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-3">Notification Types</h3>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-primary rounded"
                          defaultChecked={user.preferences.notifications.orderUpdates}
                        />

                        <span>Order Updates</span>
                      </label>

                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-primary rounded"
                          defaultChecked={user.preferences.notifications.promotions}
                        />

                        <span>Promotions and Offers</span>
                      </label>

                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-primary rounded"
                          defaultChecked={user.preferences.notifications.newsletter}
                        />

                        <span>Newsletter</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end">
                  <GlassButton variant="primary">Save Notification Settings</GlassButton>
                </div>
              </div>
            </GlassCard>

            {/* Privacy Settings */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-6">Privacy Settings</h2>

                <div className="space-y-4">
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary rounded"
                      defaultChecked={user.preferences.privacy.shareOrderHistory}
                    />

                    <div>
                      <span className="font-medium">Share Order History</span>
                      <p className="text-sm text-muted-foreground">
                        Allow us to use your order history to improve recommendations
                      </p>
                    </div>
                  </label>

                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary rounded"
                      defaultChecked={user.preferences.privacy.allowDataCollection}
                    />

                    <div>
                      <span className="font-medium">Allow Data Collection</span>
                      <p className="text-sm text-muted-foreground">
                        Allow us to collect usage data to improve our services
                      </p>
                    </div>
                  </label>
                </div>

                <div className="mt-6">
                  <p className="text-sm text-muted-foreground mb-4">
                    We value your privacy. Your data is securely stored and never shared with third
                    parties without your consent. You can request a copy of your data or delete your
                    account at any time.
                  </p>

                  <div className="flex space-x-4">
                    <GlassButton variant="secondary" size="sm">
                      Download My Data
                    </GlassButton>
                    <Link href="/profile">
                      <GlassButton variant="danger" size="sm">
                        Delete Account
                      </GlassButton>
                    </Link>
                  </div>
                </div>

                <div className="mt-6 flex justify-end">
                  <GlassButton variant="primary">Save Privacy Settings</GlassButton>
                </div>
              </div>
            </GlassCard>

            {/* Connected Accounts */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-6">Connected Accounts</h2>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border border-input rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="h-10 w-10 bg-[#4285F4] rounded-full flex items-center justify-center text-white">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-6 w-6"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />

                          <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />

                          <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />

                          <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-medium">Google</h3>
                        <p className="text-sm text-muted-foreground">Sign in with Google</p>
                      </div>
                    </div>
                    <GlassButton variant="secondary" size="sm">
                      Connect
                    </GlassButton>
                  </div>

                  <div className="flex items-center justify-between p-4 border border-input rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="h-10 w-10 bg-[#1877F2] rounded-full flex items-center justify-center text-white">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-6 w-6"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-medium">Facebook</h3>
                        <p className="text-sm text-muted-foreground">Sign in with Facebook</p>
                      </div>
                    </div>
                    <GlassButton variant="secondary" size="sm">
                      Connect
                    </GlassButton>
                  </div>

                  <div className="flex items-center justify-between p-4 border border-input rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="h-10 w-10 bg-black rounded-full flex items-center justify-center text-white">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-6 w-6"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path d="M12 0C5.373 0 0 5.373 0 12c0 5.302 3.438 9.8 8.207 11.387.6.113.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-medium">GitHub</h3>
                        <p className="text-sm text-muted-foreground">Sign in with GitHub</p>
                      </div>
                    </div>
                    <GlassButton variant="secondary" size="sm">
                      Connect
                    </GlassButton>
                  </div>
                </div>

                <p className="text-sm text-muted-foreground mt-4">
                  Connecting your accounts allows for easier sign-in and account recovery options.
                </p>
              </div>
            </GlassCard>
          </div>
        </div>
      </div>
    </main>
  );
}
