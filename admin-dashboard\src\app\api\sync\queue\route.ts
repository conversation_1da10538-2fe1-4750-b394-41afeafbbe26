import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    const terminalId = searchParams.get('terminal_id')
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '50')

    // Build query for sync queue (using pos_settings_sync_history as queue)
    let query = supabase
      .from('pos_settings_sync_history')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit)

    if (terminalId) {
      query = query.eq('terminal_id', terminalId)
    }

    if (status) {
      query = query.eq('sync_status', status)
    }

    const { data: queueItems, error } = await query

    if (error) {
      console.error('Error fetching sync queue:', error)
      return NextResponse.json(
        { error: 'Failed to fetch sync queue' },
        { status: 500 }
      )
    }

    // Get summary statistics
    const { data: summaryData } = await supabase
      .from('pos_settings_sync_history')
      .select('sync_status, terminal_id')

    const summary = {
      total: summaryData?.length || 0,
      pending: summaryData?.filter(item => item.sync_status === 'pending').length || 0,
      in_progress: summaryData?.filter(item => item.sync_status === 'in_progress').length || 0,
      completed: summaryData?.filter(item => item.sync_status === 'success').length || 0,
      failed: summaryData?.filter(item => item.sync_status === 'failed').length || 0
    }

    // Group by terminal
    const terminalGroups = queueItems?.reduce((groups, item) => {
      const terminalId = item.terminal_id
      if (!groups[terminalId]) {
        groups[terminalId] = []
      }
      groups[terminalId].push(item)
      return groups
    }, {} as Record<string, any[]>) || {}

    return NextResponse.json({
      queue_items: queueItems || [],
      summary,
      terminal_groups: terminalGroups,
      filters: {
        terminal_id: terminalId,
        status,
        limit
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Sync queue error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { terminal_id, config_id, action, priority } = await request.json()

    if (!terminal_id || !config_id || !action) {
      return NextResponse.json(
        { error: 'terminal_id, config_id, and action are required' },
        { status: 400 }
      )
    }

    // Get the configuration to sync
    const { data: config, error: configError } = await supabase
      .from('pos_configurations')
      .select('*')
      .eq('id', config_id)
      .single()

    if (configError || !config) {
      return NextResponse.json(
        { error: 'Configuration not found' },
        { status: 404 }
      )
    }

    // Create sync queue entry
    const { data: syncEntry, error: syncError } = await supabase
      .from('pos_settings_sync_history')
      .insert({
        terminal_id,
        config_id,
        sync_type: action, // 'push', 'pull', 'delete'
        sync_status: 'pending',
        priority: priority || 'normal',
        created_at: new Date().toISOString(),
        metadata: {
          action,
          config_key: config.config_key,
          config_type: config.config_type,
          setting_category: config.setting_category
        }
      })
      .select()
      .single()

    if (syncError) {
      console.error('Error creating sync entry:', syncError)
      return NextResponse.json(
        { error: 'Failed to create sync entry' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Sync operation queued',
      sync_entry: syncEntry,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Sync queue POST error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    const syncId = searchParams.get('sync_id')
    const terminalId = searchParams.get('terminal_id')
    const status = searchParams.get('status')

    if (syncId) {
      // Delete specific sync entry
      const { error } = await supabase
        .from('pos_settings_sync_history')
        .delete()
        .eq('id', syncId)

      if (error) {
        return NextResponse.json(
          { error: 'Failed to delete sync entry' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'Sync entry deleted',
        sync_id: syncId
      })
    } else if (terminalId && status) {
      // Delete all entries for terminal with specific status
      const { error } = await supabase
        .from('pos_settings_sync_history')
        .delete()
        .eq('terminal_id', terminalId)
        .eq('sync_status', status)

      if (error) {
        return NextResponse.json(
          { error: 'Failed to delete sync entries' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        message: 'Sync entries deleted',
        terminal_id: terminalId,
        status
      })
    } else {
      return NextResponse.json(
        { error: 'sync_id or (terminal_id and status) required' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Sync queue DELETE error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
