'use client'

import React, { useState, useEffect } from 'react'
import { auditLoggingService, AuditEvent, AuditReport, ComplianceFlag } from '@/services/audit-logging-service'
import { gdprComplianceService, ComplianceReport as GDPRReport } from '@/services/gdpr-compliance-service'

interface ComplianceDashboardProps {
  className?: string
}

type TabType = 'overview' | 'audit-logs' | 'gdpr' | 'reports'

export default function ComplianceDashboard({ className = '' }: ComplianceDashboardProps) {
  const [activeTab, setActiveTab] = useState<TabType>('overview')
  const [auditEvents, setAuditEvents] = useState<AuditEvent[]>([])
  const [complianceMetrics, setComplianceMetrics] = useState<any>(null)
  const [gdprReport, setGdprReport] = useState<GDPRReport | null>(null)
  const [auditReport, setAuditReport] = useState<AuditReport | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d' | '90d'>('7d')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCompliance, setSelectedCompliance] = useState<ComplianceFlag[]>(['GDPR', 'SOC2'])

  useEffect(() => {
    loadComplianceData()
  }, [timeRange, selectedCompliance])

  const loadComplianceData = async () => {
    setIsLoading(true)
    try {
      const endDate = new Date()
      const startDate = new Date()
      
      switch (timeRange) {
        case '24h':
          startDate.setDate(endDate.getDate() - 1)
          break
        case '7d':
          startDate.setDate(endDate.getDate() - 7)
          break
        case '30d':
          startDate.setDate(endDate.getDate() - 30)
          break
        case '90d':
          startDate.setDate(endDate.getDate() - 90)
          break
      }

      // Load audit events
      const { events } = await auditLoggingService.queryEvents({
        startDate,
        endDate,
        complianceFlags: selectedCompliance,
        limit: 100
      })
      setAuditEvents(events)

      // Generate audit report
      const auditRpt = await auditLoggingService.generateReport(
        { start: startDate, end: endDate },
        selectedCompliance,
        { includeAnomalies: true, includeRecommendations: true }
      )
      setAuditReport(auditRpt)

      // Generate GDPR report
      const gdprRpt = await gdprComplianceService.generateComplianceReport({
        start: startDate,
        end: endDate
      })
      setGdprReport(gdprRpt)

      // Calculate overall compliance metrics
      const metrics = {
        overallScore: Math.round((auditRpt.complianceMetrics.GDPR?.complianceScore || 0 + 
                                  auditRpt.complianceMetrics.SOC2?.complianceScore || 0) / 2),
        totalEvents: events.length,
        criticalEvents: events.filter(e => e.riskLevel === 'critical').length,
        gdprScore: gdprRpt.complianceScore,
        lastAssessment: new Date()
      }
      setComplianceMetrics(metrics)

    } catch (error) {
      console.error('Failed to load compliance data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getComplianceStatus = (score: number) => {
    if (score >= 90) return { status: 'Excellent', color: 'text-green-600 bg-green-100' }
    if (score >= 80) return { status: 'Good', color: 'text-blue-600 bg-blue-100' }
    if (score >= 70) return { status: 'Needs Attention', color: 'text-yellow-600 bg-yellow-100' }
    return { status: 'Critical', color: 'text-red-600 bg-red-100' }
  }

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'text-red-600 bg-red-100'
      case 'high': return 'text-orange-600 bg-orange-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const filteredEvents = auditEvents.filter(event =>
    searchTerm === '' || 
    event.eventType.toLowerCase().includes(searchTerm.toLowerCase()) ||
    JSON.stringify(event.details).toLowerCase().includes(searchTerm.toLowerCase())
  )

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Compliance Score Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Overall Compliance</p>
              <p className="text-2xl font-bold text-gray-900">
                {complianceMetrics?.overallScore || 0}%
              </p>
            </div>
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
              getComplianceStatus(complianceMetrics?.overallScore || 0).color
            }`}>
              {getComplianceStatus(complianceMetrics?.overallScore || 0).status}
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">GDPR Compliance</p>
              <p className="text-2xl font-bold text-gray-900">
                {gdprReport?.complianceScore || 0}%
              </p>
            </div>
            <div className="text-blue-600">
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Events</p>
              <p className="text-2xl font-bold text-gray-900">
                {complianceMetrics?.totalEvents || 0}
              </p>
            </div>
            <div className="text-purple-600">
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Critical Events</p>
              <p className="text-2xl font-bold text-red-600">
                {complianceMetrics?.criticalEvents || 0}
              </p>
            </div>
            <div className="text-red-600">
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Compliance Framework Status */}
      <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Compliance Framework Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {auditReport?.complianceMetrics && Object.entries(auditReport.complianceMetrics).map(([framework, metric]) => (
            <div key={framework} className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-gray-900">{framework}</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  metric.status === 'compliant' ? 'text-green-600 bg-green-100' :
                  metric.status === 'warning' ? 'text-yellow-600 bg-yellow-100' :
                  'text-red-600 bg-red-100'
                }`}>
                  {metric.status}
                </span>
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {metric.complianceScore}%
              </div>
              <div className="text-sm text-gray-600">
                {metric.totalEvents} events, {metric.criticalEvents} critical
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Recommendations */}
      {auditReport?.recommendations && auditReport.recommendations.length > 0 && (
        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Security Recommendations</h3>
          <div className="space-y-3">
            {auditReport.recommendations.map((recommendation, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-sm text-blue-800">{recommendation}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )

  const renderAuditLogsTab = () => (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex-1 max-w-md">
            <input
              type="text"
              placeholder="Search audit events..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex space-x-4">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
          </div>
        </div>
      </div>

      {/* Audit Events Table */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Audit Events</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Event Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Risk Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  IP Address
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Compliance
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredEvents.map((event) => (
                <tr key={event.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {event.timestamp.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm font-medium text-gray-900">
                      {event.eventType}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRiskLevelColor(event.riskLevel)}`}>
                      {event.riskLevel}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {event.userId || 'Anonymous'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {event.ipAddress}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-wrap gap-1">
                      {event.complianceFlags.map(flag => (
                        <span key={flag} className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                          {flag}
                        </span>
                      ))}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )

  if (isLoading) {
    return (
      <div className={`${className} flex items-center justify-center h-64`}>
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className={`${className} space-y-6`}>
      {/* Header */}
      <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Compliance Dashboard</h1>
            <p className="text-gray-600">Monitor security compliance and audit activities</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              Last updated: {complianceMetrics?.lastAssessment?.toLocaleString()}
            </div>
            <button
              onClick={loadComplianceData}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'audit-logs', label: 'Audit Logs' },
              { id: 'gdpr', label: 'GDPR Compliance' },
              { id: 'reports', label: 'Reports' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as TabType)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && renderOverviewTab()}
          {activeTab === 'audit-logs' && renderAuditLogsTab()}
        </div>
      </div>
    </div>
  )
}