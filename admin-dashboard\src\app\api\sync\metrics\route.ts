import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('time_range') || '24h'
    const terminalId = searchParams.get('terminal_id')

    // Calculate time range
    const now = new Date()
    let startTime: Date
    
    switch (timeRange) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000)
        break
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    }

    // Build query for sync history
    let syncHistoryQuery = supabase
      .from('pos_settings_sync_history')
      .select('*')
      .gte('synced_at', startTime.toISOString())
      .order('synced_at', { ascending: false })

    if (terminalId) {
      syncHistoryQuery = syncHistoryQuery.eq('terminal_id', terminalId)
    }

    const { data: syncHistory, error: syncError } = await syncHistoryQuery

    if (syncError) {
      console.error('Error fetching sync history:', syncError)
    }

    // Build query for heartbeats
    let heartbeatsQuery = supabase
      .from('pos_heartbeats')
      .select('*')
      .gte('timestamp', startTime.toISOString())
      .order('timestamp', { ascending: false })

    if (terminalId) {
      heartbeatsQuery = heartbeatsQuery.eq('terminal_id', terminalId)
    }

    const { data: heartbeats, error: heartbeatError } = await heartbeatsQuery

    if (heartbeatError) {
      console.error('Error fetching heartbeats:', heartbeatError)
    }

    // Calculate metrics
    const totalSyncOperations = syncHistory?.length || 0
    const successfulSyncs = syncHistory?.filter(s => s.sync_status === 'success').length || 0
    const failedSyncs = syncHistory?.filter(s => s.sync_status === 'failed').length || 0
    const pendingSyncs = syncHistory?.filter(s => s.sync_status === 'pending').length || 0

    const successRate = totalSyncOperations > 0 ? (successfulSyncs / totalSyncOperations) * 100 : 0

    // Calculate average sync time
    const completedSyncs = syncHistory?.filter(s => s.sync_status === 'success' && s.sync_duration) || []
    const avgSyncTime = completedSyncs.length > 0 
      ? completedSyncs.reduce((sum, s) => sum + (s.sync_duration || 0), 0) / completedSyncs.length 
      : 0

    // Calculate throughput (operations per hour)
    const hoursInRange = (now.getTime() - startTime.getTime()) / (1000 * 60 * 60)
    const throughputPerHour = hoursInRange > 0 ? totalSyncOperations / hoursInRange : 0

    // Get current pending operations from terminals
    const { data: terminals } = await supabase
      .from('pos_terminals')
      .select('pending_updates')

    const totalPendingOps = terminals?.reduce((sum, t) => sum + (t.pending_updates || 0), 0) || 0

    // Calculate system health score
    const recentHeartbeats = heartbeats?.filter(h => 
      new Date(h.timestamp).getTime() > Date.now() - 5 * 60 * 1000 // Last 5 minutes
    ) || []

    const avgCpuUsage = recentHeartbeats.length > 0
      ? recentHeartbeats.reduce((sum, h) => sum + (h.cpu_usage || 0), 0) / recentHeartbeats.length
      : 0

    const avgMemoryUsage = recentHeartbeats.length > 0
      ? recentHeartbeats.reduce((sum, h) => sum + (h.memory_usage || 0), 0) / recentHeartbeats.length
      : 0

    const systemHealth = Math.max(0, 100 - (avgCpuUsage + avgMemoryUsage) / 2)

    return NextResponse.json({
      time_range: timeRange,
      terminal_id: terminalId,
      metrics: {
        sync_success_rate: Math.round(successRate * 10) / 10,
        avg_sync_time_ms: Math.round(avgSyncTime),
        throughput_per_hour: Math.round(throughputPerHour * 10) / 10,
        pending_operations: totalPendingOps,
        system_health_score: Math.round(systemHealth * 10) / 10
      },
      sync_operations: {
        total: totalSyncOperations,
        successful: successfulSyncs,
        failed: failedSyncs,
        pending: pendingSyncs
      },
      system_metrics: {
        avg_cpu_usage: Math.round(avgCpuUsage * 10) / 10,
        avg_memory_usage: Math.round(avgMemoryUsage * 10) / 10,
        active_terminals: recentHeartbeats.length
      },
      recent_operations: syncHistory?.slice(0, 10) || [],
      timestamp: now.toISOString()
    })

  } catch (error) {
    console.error('Sync metrics error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
