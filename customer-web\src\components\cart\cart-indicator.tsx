'use client';

import React, { useState, useEffect } from 'react';
import { ShoppingCart, Plus } from 'lucide-react';
import { useCartContext } from '@/providers/cart-provider';
import { cn } from '@/lib/utils';

interface CartIndicatorProps {
  className?: string;
  showAnimation?: boolean;
}

export function CartIndicator({ className, showAnimation = true }: CartIndicatorProps) {
  const { cart, itemCount, toggleCart } = useCartContext();
  const [isAnimating, setIsAnimating] = useState(false);
  const [prevItemCount, setPrevItemCount] = useState(itemCount);

  // Animate when item count changes
  useEffect(() => {
    if (showAnimation && itemCount > prevItemCount) {
      setIsAnimating(true);
      const timer = setTimeout(() => setIsAnimating(false), 600);
      return () => clearTimeout(timer);
    }
    setPrevItemCount(itemCount);
  }, [itemCount, prevItemCount, showAnimation]);

  return (
    <button
      onClick={toggleCart}
      className={cn(
        'relative p-2 rounded-full transition-all duration-200 hover:bg-primary/10',
        'focus:outline-none focus:ring-2 focus:ring-primary/50',
        className
      )}
      aria-label={`Shopping cart with ${itemCount} items`}
    >
      <div className="relative">
        <ShoppingCart className="w-6 h-6" />
        
        {/* Item count badge */}
        {itemCount > 0 && (
          <span 
            className={cn(
              'absolute -top-2 -right-2 bg-primary text-primary-foreground',
              'text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center',
              'transition-all duration-300',
              isAnimating && 'animate-bounce scale-125'
            )}
          >
            {itemCount > 99 ? '99+' : itemCount}
          </span>
        )}
        
        {/* Add animation indicator */}
        {isAnimating && (
          <div className="absolute -top-1 -right-1">
            <Plus className="w-3 h-3 text-green-500 animate-ping" />
          </div>
        )}
      </div>
      
      {/* Cart total (optional) */}
      {cart.total > 0 && (
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
          <span className="text-xs font-medium text-muted-foreground whitespace-nowrap">
            €{cart.total.toFixed(2)}
          </span>
        </div>
      )}
    </button>
  );
}

export default CartIndicator;