export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export interface Database {
  public: {
    Tables: {
      categories: {
        Row: {
          created_at: string;
          description: string | null;
          id: string;
          image_url: string | null;
          name: string;
          order: number;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string;
          description?: string | null;
          id?: string;
          image_url?: string | null;
          name: string;
          order?: number;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string;
          description?: string | null;
          id?: string;
          image_url?: string | null;
          name?: string;
          order?: number;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      delivery_zones: {
        Row: {
          created_at: string;
          id: string;
          name: string;
          polygon: Json;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string;
          id?: string;
          name: string;
          polygon: Json;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string;
          id?: string;
          name?: string;
          polygon?: Json;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      subcategories: {
        Row: {
          allergens: string[] | null;
          category_id: string;
          created_at: string;
          description: string | null;
          id: string;
          image_url: string | null;
          ingredients: string[] | null;
          is_available: boolean;
          name: string;
          nutritional_info: Json | null;
          price: number;
          updated_at: string | null;
          variants: Json | null;
        };
        Insert: {
          allergens?: string[] | null;
          category_id: string;
          created_at?: string;
          description?: string | null;
          id?: string;
          image_url?: string | null;
          ingredients?: string[] | null;
          is_available?: boolean;
          name: string;
          nutritional_info?: Json | null;
          price: number;
          updated_at?: string | null;
          variants?: Json | null;
        };
        Update: {
          allergens?: string[] | null;
          category_id?: string;
          created_at?: string;
          description?: string | null;
          id?: string;
          image_url?: string | null;
          ingredients?: string[] | null;
          is_available?: boolean;
          name?: string;
          nutritional_info?: Json | null;
          price?: number;
          updated_at?: string | null;
          variants?: Json | null;
        };
        Relationships: [
          {
            foreignKeyName: 'subcategories_category_id_fkey';
            columns: ['category_id'];
            referencedRelation: 'categories';
            referencedColumns: ['id'];
          },
        ];
      };
      order_items: {
        Row: {
          created_at: string;
          id: string;
          subcategory_id: string;
          order_id: string;
          price: number;
          quantity: number;
          special_instructions: string | null;
          updated_at: string | null;
          variant_id: string | null;
        };
        Insert: {
          created_at?: string;
          id?: string;
          subcategory_id: string;
          order_id: string;
          price: number;
          quantity: number;
          special_instructions?: string | null;
          updated_at?: string | null;
          variant_id?: string | null;
        };
        Update: {
          created_at?: string;
          id?: string;
          subcategory_id?: string;
          order_id?: string;
          price?: number;
          quantity?: number;
          special_instructions?: string | null;
          updated_at?: string | null;
          variant_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'order_items_subcategory_id_fkey';
            columns: ['subcategory_id'];
            referencedRelation: 'subcategories';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'order_items_order_id_fkey';
            columns: ['order_id'];
            referencedRelation: 'orders';
            referencedColumns: ['id'];
          },
        ];
      };
      orders: {
        Row: {
          address: string | null;
          created_at: string;
          delivery_fee: number | null;
          delivery_notes: string | null;
          delivery_time: string | null;
          delivery_zone_id: string | null;
          id: string;
          is_delivery: boolean;
          payment_intent_id: string | null;
          payment_method: string | null;
          payment_status: string;
          phone: string;
          status: string;
          subtotal: number;
          tax: number;
          tip: number | null;
          total: number;
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          address?: string | null;
          created_at?: string;
          delivery_fee?: number | null;
          delivery_notes?: string | null;
          delivery_time?: string | null;
          delivery_zone_id?: string | null;
          id?: string;
          is_delivery?: boolean;
          payment_intent_id?: string | null;
          payment_method?: string | null;
          payment_status?: string;
          phone: string;
          status?: string;
          subtotal: number;
          tax: number;
          tip?: number | null;
          total: number;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          address?: string | null;
          created_at?: string;
          delivery_fee?: number | null;
          delivery_notes?: string | null;
          delivery_time?: string | null;
          delivery_zone_id?: string | null;
          id?: string;
          is_delivery?: boolean;
          payment_intent_id?: string | null;
          payment_method?: string | null;
          payment_status?: string;
          phone?: string;
          status?: string;
          subtotal?: number;
          tax?: number;
          tip?: number | null;
          total?: number;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'orders_delivery_zone_id_fkey';
            columns: ['delivery_zone_id'];
            referencedRelation: 'delivery_zones';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'orders_user_id_fkey';
            columns: ['user_id'];
            referencedRelation: 'profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      profiles: {
        Row: {
          address: string | null;
          created_at: string;
          email: string;
          first_name: string | null;
          id: string;
          last_name: string | null;
          phone: string | null;
          updated_at: string | null;
        };
        Insert: {
          address?: string | null;
          created_at?: string;
          email: string;
          first_name?: string | null;
          id: string;
          last_name?: string | null;
          phone?: string | null;
          updated_at?: string | null;
        };
        Update: {
          address?: string | null;
          created_at?: string;
          email?: string;
          first_name?: string | null;
          id?: string;
          last_name?: string | null;
          phone?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'profiles_id_fkey';
            columns: ['id'];
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}
