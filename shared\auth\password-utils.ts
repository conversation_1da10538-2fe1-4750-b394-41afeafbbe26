import { supabase } from './config';
import type { Platform } from './types';

export interface PasswordResetRequest {
  email: string;
  platform: Platform;
}

export interface PasswordResetData {
  email: string;
  token: string;
  new_password: string;
}

export interface PasswordResetResponse {
  success: boolean;
  error?: string;
  data?: {
    message: string;
  };
}

/**
 * Request Password Reset
 */
export async function requestPasswordReset({
  email,
  platform,
}: PasswordResetRequest): Promise<PasswordResetResponse> {
  try {
    // Validate platform
    if (!['admin-dashboard', 'pos-system', 'customer-web', 'customer-mobile'].includes(platform)) {
      return {
        success: false,
        error: 'Invalid platform',
      };
    }

    // Check if user exists
    const { data: existingUser, error: userError } = await supabase
      .from('user_profiles')
      .select('user_id, email')
      .eq('email', email)
      .eq('is_active', true)
      .single();

    if (userError || !existingUser) {
      // Don't reveal if email exists or not for security
      return {
        success: true,
        data: {
          message: 'If the email exists, a reset link has been sent.',
        },
      };
    }

    // Send password reset email using Supabase Auth
    const { error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password`,
    });

    if (resetError) {
      console.error('Password reset error:', resetError);
      return {
        success: false,
        error: 'Failed to send reset email',
      };
    }

    return {
      success: true,
      data: {
        message: 'Password reset email sent successfully',
      },
    };
  } catch (error) {
    console.error('Request password reset error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred',
    };
  }
}

/**
 * Reset Password
 */
export async function resetPassword({
  new_password,
}: Pick<PasswordResetData, 'new_password'>): Promise<PasswordResetResponse> {
  try {
    // Validate password strength
    if (new_password.length < 8) {
      return {
        success: false,
        error: 'Password must be at least 8 characters long',
      };
    }

    // Update password using Supabase Auth
    const { error: updateError } = await supabase.auth.updateUser({
      password: new_password,
    });

    if (updateError) {
      console.error('Password update error:', updateError);
      return {
        success: false,
        error: 'Failed to update password',
      };
    }

    return {
      success: true,
      data: {
        message: 'Password updated successfully',
      },
    };
  } catch (error) {
    console.error('Reset password error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred',
    };
  }
}