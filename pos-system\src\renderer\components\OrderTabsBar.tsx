import React, { useMemo, useCallback } from "react";
import { useTheme } from '../contexts/theme-context';

interface OrderTabsBarProps {
  activeTab: 'orders' | 'delivered' | 'canceled';
  onTabChange: (tab: 'orders' | 'delivered' | 'canceled') => void;
  orderCounts: {
    orders: number;
    delivered: number;
    canceled: number;
  };
}

const OrderTabsBar: React.FC<OrderTabsBarProps> = React.memo(({ 
  activeTab, 
  onTabChange, 
  orderCounts
}) => {
  const { resolvedTheme } = useTheme();
  
  const tabs = useMemo(() => [
    { id: 'orders', label: 'Orders', count: orderCounts.orders, color: 'green' },
    { id: 'delivered', label: 'Delivered', count: orderCounts.delivered, color: 'orange' },
    { id: 'canceled', label: 'Canceled', count: orderCounts.canceled, color: 'red' }
  ], [orderCounts]);

  const handleTabChange = useCallback((tabId: string) => {
    onTabChange(tabId as 'orders' | 'delivered' | 'canceled');
  }, [onTabChange]);

  return (
    <div className={`flex backdrop-blur-sm rounded-xl p-2 border ${
      resolvedTheme === 'light'
        ? 'bg-gray-100/80 border-gray-200/50 shadow-sm'
        : 'bg-white/10 border-white/20'
    }`}>
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => handleTabChange(tab.id)}
          className={`flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 relative ${
            activeTab === tab.id
              ? resolvedTheme === 'light'
                ? 'bg-white backdrop-blur-sm shadow-sm border border-gray-200/30'
                : 'bg-white/20 shadow-lg'
              : resolvedTheme === 'light'
                ? 'hover:bg-white/60'
                : 'hover:bg-white/10'
          }`}
        >
          <div className="flex items-center justify-center gap-2">
            <span className={`text-lg font-bold transition-all duration-200 ${
              activeTab === tab.id
                ? tab.color === 'green'
                  ? 'text-green-500 drop-shadow-[0_0_8px_rgba(34,197,94,0.8)]'
                  : tab.color === 'orange'
                    ? 'text-orange-500 drop-shadow-[0_0_8px_rgba(249,115,22,0.8)]'
                    : 'text-red-500 drop-shadow-[0_0_8px_rgba(239,68,68,0.8)]'
                : resolvedTheme === 'light'
                  ? 'text-gray-600 hover:text-gray-800'
                  : 'text-white/70 hover:text-white'
            }`}>
              {tab.label}
            </span>
            
            {/* Tab counter with same color as text */}
            <span className={`text-xs font-bold transition-all duration-200 ${
              activeTab === tab.id
                ? tab.color === 'green'
                  ? 'text-green-500'
                  : tab.color === 'orange'
                    ? 'text-orange-500'
                    : 'text-red-500'
                : resolvedTheme === 'light'
                  ? 'text-gray-600'
                  : 'text-white/70'
            }`}>
              {tab.count}
            </span>
          </div>
        </button>
      ))}
    </div>
  );
});

export default OrderTabsBar; 