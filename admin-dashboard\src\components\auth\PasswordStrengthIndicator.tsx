﻿'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { passwordSecurityService, PasswordStrengthResult } from '@/services/password-security-service'

interface PasswordStrengthIndicatorProps {
  password: string
  onStrengthChange?: (result: PasswordStrengthResult) => void
  showDetails?: boolean
  className?: string
}

export default function PasswordStrengthIndicator({
  password,
  onStrengthChange,
  showDetails = true,
  className = ''
}: PasswordStrengthIndicatorProps) {
  const [strengthResult, setStrengthResult] = useState<PasswordStrengthResult | null>(null)
  const [isChecking, setIsChecking] = useState(false)

  // Debounced password assessment to avoid excessive API calls
  const debouncedAssessment = useCallback(
    ((pwd: string) => {
      if (!pwd) {
        setStrengthResult(null)
        return
      }

      setIsChecking(true)
      setTimeout(async () => {
        try {
          const result = await passwordSecurityService.assessPasswordStrength(pwd)
          setStrengthResult(result)
          onStrengthChange?.(result)
        } catch (error) {
          console.error('Password strength assessment failed:', error)
        } finally {
          setIsChecking(false)
        }
      }, 500)
    }),
    [onStrengthChange]
  )

  useEffect(() => {
    debouncedAssessment(password)
  }, [password, debouncedAssessment])

  if (!password) {
    return null
  }

  const getStrengthColor = (strength?: string) => {
    switch (strength) {
      case 'very-strong':
        return 'text-green-600 bg-green-100'
      case 'strong':
        return 'text-green-500 bg-green-50'
      case 'good':
        return 'text-blue-600 bg-blue-100'
      case 'fair':
        return 'text-yellow-600 bg-yellow-100'
      case 'weak':
        return 'text-orange-600 bg-orange-100'
      case 'very-weak':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getProgressBarColor = (strength?: string) => {
    switch (strength) {
      case 'very-strong':
        return 'bg-green-500'
      case 'strong':
        return 'bg-green-400'
      case 'good':
        return 'bg-blue-500'
      case 'fair':
        return 'bg-yellow-500'
      case 'weak':
        return 'bg-orange-500'
      case 'very-weak':
        return 'bg-red-500'
      default:
        return 'bg-gray-300'
    }
  }

  const getRiskLevelIcon = (riskLevel?: string) => {
    switch (riskLevel) {
      case 'low':
        return ''
      case 'medium':
        return ''
      case 'high':
        return ''
      case 'critical':
        return ''
      default:
        return ''
    }
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Main Strength Indicator */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">
            Password Strength
          </span>
          {isChecking ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
              <span className="text-xs text-gray-500">Checking...</span>
            </div>
          ) : strengthResult ? (
            <div className="flex items-center space-x-2">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStrengthColor(strengthResult.strength)}`}>
                {strengthResult.strength.replace('-', ' ').toUpperCase()}
              </span>
              <span className="text-xs text-gray-500">
                {strengthResult.score}/100
              </span>
            </div>
          ) : null}
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor(strengthResult?.strength)}`}
            style={{
              width: `${strengthResult?.score || 0}%`
            }}
          ></div>
        </div>
      </div>

      {/* Detailed Information */}
      {showDetails && strengthResult && (
        <div className="space-y-3">
          {/* Risk Assessment */}
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2">
              <span className="text-lg">{getRiskLevelIcon(strengthResult.riskLevel)}</span>
              <div>
                <p className="text-sm font-medium text-gray-700">Risk Level</p>
                <p className="text-xs text-gray-500 capitalize">{strengthResult.riskLevel}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium text-gray-700">Crack Time</p>
              <p className="text-xs text-gray-500">{strengthResult.estimatedCrackTime}</p>
            </div>
          </div>

          {/* Breach Detection Alert */}
          {strengthResult.isBreached && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <span className="text-red-500"></span>
                <div>
                  <p className="text-sm font-medium text-red-800">Security Alert</p>
                  <p className="text-xs text-red-600">
                    This password has been found in data breaches and should not be used.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Feedback Messages */}
          {strengthResult.feedback.length > 0 && (
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700">Recommendations</p>
              <ul className="space-y-1">
                {strengthResult.feedback.map((feedback, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-blue-500 text-xs mt-0.5"></span>
                    <span className="text-xs text-gray-600">{feedback}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Security Tips */}
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm font-medium text-blue-800 mb-2"> Security Tips</p>
            <ul className="space-y-1 text-xs text-blue-700">
              <li> Use a unique password for each account</li>
              <li> Consider using a passphrase with random words</li>
              <li> Enable two-factor authentication when available</li>
              <li> Use a password manager to generate and store strong passwords</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  )
}
