'use client'

import React, { useState } from 'react'
import { passwordSecurityService } from '@/services/password-security-service'

interface PasswordGeneratorProps {
  onPasswordGenerated?: (password: string) => void
  className?: string
}

export default function PasswordGenerator({
  onPasswordGenerated,
  className = ''
}: PasswordGeneratorProps) {
  const [generatedPassword, setGeneratedPassword] = useState('')
  const [generationType, setGenerationType] = useState<'password' | 'passphrase'>('password')
  const [passwordLength, setPasswordLength] = useState(16)
  const [passphraseWordCount, setPassphraseWordCount] = useState(4)
  const [copied, setCopied] = useState(false)

  const generatePassword = () => {
    let newPassword = ''
    
    if (generationType === 'password') {
      newPassword = passwordSecurityService.generateSecurePassword(passwordLength)
    } else {
      newPassword = passwordSecurityService.generatePassphrase(passphraseWordCount)
    }
    
    setGeneratedPassword(newPassword)
    onPasswordGenerated?.(newPassword)
    setCopied(false)
  }

  const copyToClipboard = async () => {
    if (!generatedPassword) return
    
    try {
      await navigator.clipboard.writeText(generatedPassword)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy password:', error)
    }
  }

  const usePassword = () => {
    if (!generatedPassword) return
    onPasswordGenerated?.(generatedPassword)
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">Password Generator</h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setGenerationType('password')}
            className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
              generationType === 'password'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            Password
          </button>
          <button
            onClick={() => setGenerationType('passphrase')}
            className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
              generationType === 'passphrase'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            Passphrase
          </button>
        </div>
      </div>

      {/* Generation Options */}
      <div className="space-y-3">
        {generationType === 'password' ? (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Password Length: {passwordLength}
            </label>
            <input
              type="range"
              min="8"
              max="64"
              value={passwordLength}
              onChange={(e) => setPasswordLength(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>8</span>
              <span>64</span>
            </div>
          </div>
        ) : (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Number of Words: {passphraseWordCount}
            </label>
            <input
              type="range"
              min="3"
              max="8"
              value={passphraseWordCount}
              onChange={(e) => setPassphraseWordCount(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>3</span>
              <span>8</span>
            </div>
          </div>
        )}

        <button
          onClick={generatePassword}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          Generate {generationType === 'password' ? 'Password' : 'Passphrase'}
        </button>
      </div>

      {/* Generated Password Display */}
      {generatedPassword && (
        <div className="space-y-3">
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Generated {generationType}:</span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={copyToClipboard}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    copied
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {copied ? '✓ Copied' : '📋 Copy'}
                </button>
                <button
                  onClick={usePassword}
                  className="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-medium hover:bg-blue-200 transition-colors"
                >
                  Use This
                </button>
              </div>
            </div>
            <div className="font-mono text-sm bg-white p-3 rounded border break-all">
              {generatedPassword}
            </div>
          </div>

          {/* Security Information */}
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm font-medium text-green-800 mb-1">🔒 Security Features</p>
            <ul className="text-xs text-green-700 space-y-1">
              {generationType === 'password' ? (
                <>
                  <li>• Contains uppercase and lowercase letters</li>
                  <li>• Includes numbers and special characters</li>
                  <li>• Randomly shuffled to avoid patterns</li>
                  <li>• Cryptographically secure generation</li>
                </>
              ) : (
                <>
                  <li>• Uses random dictionary words</li>
                  <li>• Easy to remember, hard to guess</li>
                  <li>• Includes numbers and special characters</li>
                  <li>• Follows modern passphrase best practices</li>
                </>
              )}
            </ul>
          </div>
        </div>
      )}

      {/* Tips and Best Practices */}
      <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <p className="text-sm font-medium text-blue-800 mb-2">💡 Password Tips</p>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• Use a unique password for each account</li>
          <li>• Store passwords in a secure password manager</li>
          <li>• Enable two-factor authentication when available</li>
          <li>• Avoid using personal information in passwords</li>
          <li>• Consider using passphrases for memorable security</li>
        </ul>
      </div>
    </div>
  )
} 