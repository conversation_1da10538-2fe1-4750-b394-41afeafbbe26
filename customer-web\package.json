{"name": "@creperie/customer-web", "version": "0.1.0", "description": "Customer-facing website for Creperie - Online ordering and menu browsing", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start -p 3000", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "clean": "rm -rf .next out dist"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.0", "@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-dropdown-menu": "^2.1.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.0", "@radix-ui/react-select": "^2.1.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.0", "@radix-ui/react-tooltip": "^1.1.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.45.0", "@tanstack/react-query": "^5.51.0", "@types/google.maps": "^3.58.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.6.0", "i18next": "^23.12.0", "i18next-browser-languagedetector": "^8.0.0", "lucide-react": "^0.427.0", "next": "^15.3.5", "next-pwa": "^5.6.0", "next-themes": "^0.3.0", "react": "^18.3.0", "react-dom": "^18.3.0", "react-hook-form": "^7.52.0", "react-hot-toast": "^2.4.0", "react-i18next": "^15.0.0", "tailwind-merge": "^2.5.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.0"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.5", "@testing-library/jest-dom": "^6.4.0", "@testing-library/react": "^16.0.0", "@types/node": "^20.14.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "^15.3.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.41", "prettier": "^3.3.0", "tailwindcss": "^3.4.0", "typescript": "^5.5.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["nextjs", "react", "typescript", "tailwindcss", "shadcn-ui", "supabase", "creperie", "restaurant", "online-ordering", "pwa"]}