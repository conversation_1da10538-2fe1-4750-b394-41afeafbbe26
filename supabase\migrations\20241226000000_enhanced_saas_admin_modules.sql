-- Enhanced SaaS Admin Modules Migration
-- Creates tables for Web Settings, App Settings, Enhanced Users, and Map Analytics

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- WEB CONFIGURATION SETTINGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS web_configurations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  config_section VARCHAR(50) NOT NULL, -- 'branding', 'features', 'content', 'seo', 'appearance'
  config_key VARCHAR(100) NOT NULL,
  config_value JSONB NOT NULL,
  data_type VARCHAR(20) NOT NULL CHECK (data_type IN ('string', 'number', 'boolean', 'object', 'array', 'color', 'url')),
  is_public BOOLEAN NOT NULL DEFAULT TRUE, -- visible to customer webapp
  requires_restart BOOLEAN NOT NULL DEFAULT FALSE,
  validation_rules JSONB DEFAULT '{}',
  description TEXT,
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE, -- NULL = global config
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for web_configurations
CREATE INDEX IF NOT EXISTS idx_web_config_section ON web_configurations (config_section);
CREATE INDEX IF NOT EXISTS idx_web_config_key ON web_configurations (config_key);
CREATE INDEX IF NOT EXISTS idx_web_config_branch ON web_configurations (branch_id);
CREATE INDEX IF NOT EXISTS idx_web_config_public ON web_configurations (is_public);

-- Create unique constraint
ALTER TABLE web_configurations ADD CONSTRAINT web_config_unique UNIQUE(config_section, config_key, branch_id);

-- =============================================
-- MOBILE APP CONFIGURATION SETTINGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS app_configurations_enhanced (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  platform VARCHAR(20) NOT NULL CHECK (platform IN ('ios', 'android', 'both')),
  config_section VARCHAR(50) NOT NULL, -- 'notifications', 'features', 'appearance', 'store_metadata', 'permissions'
  config_key VARCHAR(100) NOT NULL,
  config_value JSONB NOT NULL,
  data_type VARCHAR(20) NOT NULL CHECK (data_type IN ('string', 'number', 'boolean', 'object', 'array')),
  version_constraint VARCHAR(20), -- minimum app version required
  is_feature_flag BOOLEAN NOT NULL DEFAULT FALSE,
  rollout_percentage INTEGER DEFAULT 100 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
  target_user_segments TEXT[], -- specific user segments
  description TEXT,
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE, -- NULL = global config
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for app_configurations_enhanced
CREATE INDEX IF NOT EXISTS idx_app_enhanced_platform ON app_configurations_enhanced (platform);
CREATE INDEX IF NOT EXISTS idx_app_enhanced_section ON app_configurations_enhanced (config_section);
CREATE INDEX IF NOT EXISTS idx_app_enhanced_key ON app_configurations_enhanced (config_key);
CREATE INDEX IF NOT EXISTS idx_app_enhanced_branch ON app_configurations_enhanced (branch_id);
CREATE INDEX IF NOT EXISTS idx_app_enhanced_feature_flag ON app_configurations_enhanced (is_feature_flag);

-- Create unique constraint
ALTER TABLE app_configurations_enhanced ADD CONSTRAINT app_enhanced_config_unique UNIQUE(platform, config_section, config_key, branch_id);

-- =============================================
-- PUSH NOTIFICATION SETTINGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS push_notification_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  notification_type VARCHAR(50) NOT NULL, -- 'order_status', 'promotional', 'reminder', 'news', 'emergency'
  platform VARCHAR(20) NOT NULL CHECK (platform IN ('ios', 'android', 'both')),
  is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
  title_template TEXT NOT NULL,
  body_template TEXT NOT NULL,
  icon_url TEXT,
  sound VARCHAR(50) DEFAULT 'default',
  badge_increment INTEGER DEFAULT 1,
  deep_link_url TEXT,
  custom_data JSONB DEFAULT '{}',
  schedule_type VARCHAR(20) NOT NULL DEFAULT 'immediate' CHECK (schedule_type IN ('immediate', 'scheduled', 'recurring')),
  schedule_config JSONB DEFAULT '{}',
  targeting_rules JSONB DEFAULT '{}', -- user segment targeting
  priority INTEGER NOT NULL DEFAULT 5 CHECK (priority >= 1 AND priority <= 10),
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for push_notification_settings
CREATE INDEX IF NOT EXISTS idx_push_notif_type ON push_notification_settings (notification_type);
CREATE INDEX IF NOT EXISTS idx_push_notif_platform ON push_notification_settings (platform);
CREATE INDEX IF NOT EXISTS idx_push_notif_enabled ON push_notification_settings (is_enabled);
CREATE INDEX IF NOT EXISTS idx_push_notif_branch ON push_notification_settings (branch_id);

-- =============================================
-- ENHANCED USER ANALYTICS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS user_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  platform VARCHAR(20) NOT NULL CHECK (platform IN ('web', 'mobile_ios', 'mobile_android', 'pos')),
  session_id UUID NOT NULL,
  event_type VARCHAR(50) NOT NULL, -- 'login', 'order_placed', 'page_view', 'feature_used', 'logout'
  event_category VARCHAR(50) NOT NULL, -- 'authentication', 'commerce', 'navigation', 'engagement'
  event_data JSONB DEFAULT '{}',
  user_agent TEXT,
  ip_address INET,
  location_data JSONB DEFAULT '{}', -- city, country, coordinates if available
  device_info JSONB DEFAULT '{}', -- device type, OS version, app version
  session_duration INTEGER, -- seconds, filled on logout/session_end events
  referrer TEXT,
  utm_source VARCHAR(50),
  utm_medium VARCHAR(50),
  utm_campaign VARCHAR(50),
  branch_id UUID REFERENCES branches(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for user_analytics
CREATE INDEX IF NOT EXISTS idx_user_analytics_user ON user_analytics (user_id);
CREATE INDEX IF NOT EXISTS idx_user_analytics_platform ON user_analytics (platform);
CREATE INDEX IF NOT EXISTS idx_user_analytics_event_type ON user_analytics (event_type);
CREATE INDEX IF NOT EXISTS idx_user_analytics_category ON user_analytics (event_category);
CREATE INDEX IF NOT EXISTS idx_user_analytics_session ON user_analytics (session_id);
CREATE INDEX IF NOT EXISTS idx_user_analytics_created_at ON user_analytics (created_at);
CREATE INDEX IF NOT EXISTS idx_user_analytics_branch ON user_analytics (branch_id);

-- =============================================
-- USER BEHAVIOR INSIGHTS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS user_behavior_insights (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  insight_type VARCHAR(50) NOT NULL, -- 'favorite_items', 'order_patterns', 'usage_frequency', 'churn_risk'
  insight_data JSONB NOT NULL,
  confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
  data_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
  data_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
  last_calculated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  branch_id UUID REFERENCES branches(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for user_behavior_insights
CREATE INDEX IF NOT EXISTS idx_user_insights_user ON user_behavior_insights (user_id);
CREATE INDEX IF NOT EXISTS idx_user_insights_type ON user_behavior_insights (insight_type);
CREATE INDEX IF NOT EXISTS idx_user_insights_active ON user_behavior_insights (is_active);
CREATE INDEX IF NOT EXISTS idx_user_insights_calculated ON user_behavior_insights (last_calculated);
CREATE INDEX IF NOT EXISTS idx_user_insights_branch ON user_behavior_insights (branch_id);

-- =============================================
-- DELIVERY ZONE ANALYTICS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS delivery_zone_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  zone_id UUID REFERENCES delivery_zones(id) ON DELETE CASCADE,
  date_period DATE NOT NULL,
  period_type VARCHAR(20) NOT NULL DEFAULT 'daily' CHECK (period_type IN ('daily', 'weekly', 'monthly')),
  total_orders INTEGER NOT NULL DEFAULT 0,
  total_revenue DECIMAL(15,2) NOT NULL DEFAULT 0.00,
  average_order_value DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  average_delivery_time INTEGER NOT NULL DEFAULT 0, -- minutes
  customer_satisfaction_score DECIMAL(3,2), -- 1.0 to 5.0 rating
  peak_hours JSONB DEFAULT '{}', -- hour -> order_count mapping
  popular_items JSONB DEFAULT '{}', -- item_id -> order_count mapping
  delivery_success_rate DECIMAL(5,2) NOT NULL DEFAULT 100.00, -- percentage
  weather_conditions VARCHAR(50),
  special_events TEXT[], -- holidays, promotions, etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for delivery_zone_analytics
CREATE INDEX IF NOT EXISTS idx_zone_analytics_zone ON delivery_zone_analytics (zone_id);
CREATE INDEX IF NOT EXISTS idx_zone_analytics_date ON delivery_zone_analytics (date_period);
CREATE INDEX IF NOT EXISTS idx_zone_analytics_period_type ON delivery_zone_analytics (period_type);
CREATE INDEX IF NOT EXISTS idx_zone_analytics_revenue ON delivery_zone_analytics (total_revenue);

-- Create unique constraint
ALTER TABLE delivery_zone_analytics ADD CONSTRAINT zone_analytics_unique UNIQUE(zone_id, date_period, period_type);

-- =============================================
-- MARKETING CAMPAIGNS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS marketing_campaigns (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  campaign_name VARCHAR(100) NOT NULL,
  campaign_type VARCHAR(50) NOT NULL CHECK (campaign_type IN ('email', 'sms', 'push_notification', 'in_app')),
  target_platforms VARCHAR(20)[] DEFAULT ARRAY['web', 'mobile'],
  target_audience JSONB NOT NULL DEFAULT '{}', -- targeting rules
  content_template JSONB NOT NULL, -- title, body, images, etc.
  schedule_type VARCHAR(20) NOT NULL DEFAULT 'immediate' CHECK (schedule_type IN ('immediate', 'scheduled', 'recurring')),
  schedule_config JSONB DEFAULT '{}',
  status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'active', 'paused', 'completed', 'cancelled')),
  budget_limit DECIMAL(10,2),
  spent_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  total_sent INTEGER NOT NULL DEFAULT 0,
  total_delivered INTEGER NOT NULL DEFAULT 0,
  total_opened INTEGER NOT NULL DEFAULT 0,
  total_clicked INTEGER NOT NULL DEFAULT 0,
  total_converted INTEGER NOT NULL DEFAULT 0,
  conversion_revenue DECIMAL(15,2) NOT NULL DEFAULT 0.00,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for marketing_campaigns
CREATE INDEX IF NOT EXISTS idx_campaigns_type ON marketing_campaigns (campaign_type);
CREATE INDEX IF NOT EXISTS idx_campaigns_status ON marketing_campaigns (status);
CREATE INDEX IF NOT EXISTS idx_campaigns_start_date ON marketing_campaigns (start_date);
CREATE INDEX IF NOT EXISTS idx_campaigns_branch ON marketing_campaigns (branch_id);
CREATE INDEX IF NOT EXISTS idx_campaigns_created_by ON marketing_campaigns (created_by);

-- =============================================
-- MENU PRICING STRATEGIES TABLE (Pickup vs Delivery)
-- =============================================
CREATE TABLE IF NOT EXISTS menu_pricing_strategies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  menu_item_id UUID NOT NULL REFERENCES subcategories(id) ON DELETE CASCADE,
  pricing_type VARCHAR(20) NOT NULL CHECK (pricing_type IN ('pickup', 'delivery')),
  base_price DECIMAL(10,2) NOT NULL,
  markup_percentage DECIMAL(5,2) DEFAULT 0.00, -- additional markup for delivery
  markup_fixed DECIMAL(10,2) DEFAULT 0.00, -- fixed additional cost
  minimum_price DECIMAL(10,2), -- price floor
  maximum_price DECIMAL(10,2), -- price ceiling
  time_based_pricing JSONB DEFAULT '{}', -- different prices by time periods
  volume_discounts JSONB DEFAULT '{}', -- quantity-based discounts
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  effective_from TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  effective_until TIMESTAMP WITH TIME ZONE,
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for menu_pricing_strategies
CREATE INDEX IF NOT EXISTS idx_pricing_menu_item ON menu_pricing_strategies (menu_item_id);
CREATE INDEX IF NOT EXISTS idx_pricing_type ON menu_pricing_strategies (pricing_type);
CREATE INDEX IF NOT EXISTS idx_pricing_active ON menu_pricing_strategies (is_active);
CREATE INDEX IF NOT EXISTS idx_pricing_branch ON menu_pricing_strategies (branch_id);
CREATE INDEX IF NOT EXISTS idx_pricing_effective ON menu_pricing_strategies (effective_from, effective_until);

-- Create unique constraint
ALTER TABLE menu_pricing_strategies ADD CONSTRAINT pricing_strategies_unique UNIQUE(menu_item_id, pricing_type, branch_id);

-- =============================================
-- INSERT DEFAULT CONFIGURATIONS
-- =============================================

-- Default web configurations
INSERT INTO web_configurations (config_section, config_key, config_value, data_type, description, is_public) VALUES
-- Branding
('branding', 'logo_url', '"/images/logo.png"', 'url', 'Main logo URL for customer website', true),
('branding', 'favicon_url', '"/images/favicon.ico"', 'url', 'Favicon URL', true),
('branding', 'brand_color_primary', '"#F59E0B"', 'color', 'Primary brand color', true),
('branding', 'brand_color_secondary', '"#6B7280"', 'color', 'Secondary brand color', true),
('branding', 'brand_color_accent', '"#EF4444"', 'color', 'Accent color for CTAs', true),
('branding', 'company_tagline', '"Delicious crepes made with love"', 'string', 'Company tagline', true),

-- Features
('features', 'enable_online_ordering', 'true', 'boolean', 'Enable online ordering on website', true),
('features', 'enable_user_accounts', 'true', 'boolean', 'Allow customers to create accounts', true),
('features', 'enable_loyalty_program', 'true', 'boolean', 'Show loyalty program features', true),
('features', 'enable_reviews', 'true', 'boolean', 'Allow customer reviews', true),
('features', 'enable_newsletter_signup', 'true', 'boolean', 'Show newsletter signup form', true),
('features', 'enable_social_login', 'true', 'boolean', 'Allow social media login', true),

-- Content
('content', 'homepage_hero_title', '"Welcome to Delicious Bites"', 'string', 'Main homepage title', true),
('content', 'homepage_hero_subtitle', '"Fresh crepes and delicious treats"', 'string', 'Homepage subtitle', true),
('content', 'contact_phone', '"+****************"', 'string', 'Contact phone number', true),
('content', 'contact_email', '"<EMAIL>"', 'string', 'Contact email address', true),
('content', 'about_us_text', '"We are passionate about serving the best crepes in town"', 'string', 'About us description', true),

-- SEO
('seo', 'meta_title', '"Delicious Bites - Fresh Crepes & More"', 'string', 'Default page title', true),
('seo', 'meta_description', '"Order fresh crepes, waffles, and delicious treats online"', 'string', 'Default meta description', true),
('seo', 'meta_keywords', '"crepes, waffles, food delivery, online ordering"', 'string', 'Default meta keywords', true),

-- Appearance
('appearance', 'theme_mode', '"light"', 'string', 'Default theme (light/dark/auto)', true),
('appearance', 'font_family', '"Inter"', 'string', 'Primary font family', true),
('appearance', 'enable_animations', 'true', 'boolean', 'Enable UI animations', true)
ON CONFLICT (config_section, config_key, branch_id) DO NOTHING;

-- Default mobile app configurations
INSERT INTO app_configurations_enhanced (platform, config_section, config_key, config_value, data_type, description) VALUES
-- iOS specific
('ios', 'store_metadata', 'app_name', '"Delicious Bites"', 'string', 'App name in App Store'),
('ios', 'store_metadata', 'app_subtitle', '"Fresh Food Delivery"', 'string', 'App subtitle in App Store'),
('ios', 'store_metadata', 'keywords', '"food,delivery,crepes,restaurant"', 'string', 'App Store keywords'),
('ios', 'store_metadata', 'category', '"Food & Drink"', 'string', 'App Store category'),

-- Android specific
('android', 'store_metadata', 'app_name', '"Delicious Bites"', 'string', 'App name in Play Store'),
('android', 'store_metadata', 'short_description', '"Order fresh crepes and food"', 'string', 'Play Store short description'),
('android', 'store_metadata', 'category', '"FOOD_AND_DRINK"', 'string', 'Play Store category'),

-- Both platforms
('both', 'features', 'enable_push_notifications', 'true', 'boolean', 'Enable push notifications'),
('both', 'features', 'enable_location_services', 'true', 'boolean', 'Enable location-based features'),
('both', 'features', 'enable_biometric_auth', 'true', 'boolean', 'Enable fingerprint/face authentication'),
('both', 'features', 'enable_offline_mode', 'false', 'boolean', 'Enable offline functionality'),
('both', 'features', 'enable_voice_ordering', 'false', 'boolean', 'Enable voice-based ordering'),

-- Notifications
('both', 'notifications', 'order_updates', 'true', 'boolean', 'Send order status notifications'),
('both', 'notifications', 'promotional_messages', 'true', 'boolean', 'Send promotional notifications'),
('both', 'notifications', 'reminder_notifications', 'true', 'boolean', 'Send reminder notifications'),

-- Appearance
('both', 'appearance', 'theme_mode', '"system"', 'string', 'App theme preference'),
('both', 'appearance', 'primary_color', '"#F59E0B"', 'color', 'Primary app color'),
('both', 'appearance', 'enable_haptic_feedback', 'true', 'boolean', 'Enable haptic feedback')
ON CONFLICT (platform, config_section, config_key, branch_id) DO NOTHING;

-- Default push notification templates
INSERT INTO push_notification_settings (notification_type, platform, title_template, body_template, description) VALUES
('order_status', 'both', 'Order Update', 'Your order #{{order_number}} is {{status}}!', 'Order status change notifications'),
('promotional', 'both', 'Special Offer!', 'Get {{discount}}% off your next order. Use code {{promo_code}}', 'Promotional campaign notifications'),
('reminder', 'both', 'Don''t forget!', 'You have items in your cart. Complete your order now!', 'Cart abandonment reminders'),
('news', 'both', 'What''s New', 'Check out our new {{item_name}} - now available!', 'New menu item announcements'),
('emergency', 'both', 'Important Notice', 'Store closure: {{reason}}. We apologize for the inconvenience.', 'Emergency notifications')
ON CONFLICT DO NOTHING;

-- =============================================
-- CREATE FUNCTIONS FOR CONFIGURATION MANAGEMENT
-- =============================================

-- Function to get web configuration with fallback
CREATE OR REPLACE FUNCTION get_web_config(
  section_name TEXT,
  key_name TEXT,
  branch_id_param UUID DEFAULT NULL,
  default_value JSONB DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  -- Try branch-specific first, then global
  SELECT config_value INTO result
  FROM web_configurations
  WHERE config_section = section_name 
    AND config_key = key_name 
    AND (applies_to_branch = branch_id_param OR (branch_id_param IS NULL AND applies_to_branch IS NULL))
    AND is_public = true
  ORDER BY applies_to_branch NULLS LAST
  LIMIT 1;
  
  RETURN COALESCE(result, default_value);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get app configuration with platform and version support
CREATE OR REPLACE FUNCTION get_app_config(
  platform_name TEXT,
  section_name TEXT,
  key_name TEXT,
  app_version TEXT DEFAULT '1.0.0',
  branch_id_param UUID DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  SELECT config_value INTO result
  FROM app_configurations_enhanced
  WHERE (platform = platform_name OR platform = 'both')
    AND config_section = section_name 
    AND config_key = key_name
    AND (version_constraint IS NULL OR app_version >= version_constraint)
    AND (branch_id = branch_id_param OR (branch_id_param IS NULL AND branch_id IS NULL))
  ORDER BY 
    CASE WHEN platform = platform_name THEN 1 ELSE 2 END,
    branch_id NULLS LAST
  LIMIT 1;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log user analytics event
CREATE OR REPLACE FUNCTION log_user_event(
  user_id_param UUID,
  platform_param VARCHAR(20),
  session_id_param UUID,
  event_type_param VARCHAR(50),
  event_category_param VARCHAR(50),
  event_data_param JSONB DEFAULT '{}',
  branch_id_param UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  analytics_id UUID;
BEGIN
  INSERT INTO user_analytics (
    user_id, platform, session_id, event_type, event_category, 
    event_data, branch_id
  ) VALUES (
    user_id_param, platform_param, session_id_param, event_type_param, 
    event_category_param, event_data_param, branch_id_param
  ) RETURNING id INTO analytics_id;
  
  RETURN analytics_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate delivery zone performance
CREATE OR REPLACE FUNCTION calculate_zone_analytics(
  zone_id_param UUID,
  date_start DATE,
  date_end DATE
)
RETURNS VOID AS $$
DECLARE
  analytics_record RECORD;
BEGIN
  -- Calculate daily analytics for the date range
  FOR analytics_record IN
    SELECT 
      date_trunc('day', created_at)::date as day_period,
      COUNT(*) as total_orders,
      SUM(total_amount) as total_revenue,
      AVG(total_amount) as avg_order_value,
      AVG(EXTRACT(EPOCH FROM delivery_completed_at - created_at)/60) as avg_delivery_time
    FROM orders o
    JOIN customer_addresses ca ON o.delivery_address_id = ca.id
    WHERE ca.delivery_zone_id = zone_id_param
      AND o.created_at::date BETWEEN date_start AND date_end
      AND o.status = 'delivered'
    GROUP BY date_trunc('day', created_at)::date
  LOOP
    -- Insert or update analytics record
    INSERT INTO delivery_zone_analytics (
      zone_id, date_period, period_type, total_orders, total_revenue,
      average_order_value, average_delivery_time
    ) VALUES (
      zone_id_param, analytics_record.day_period, 'daily',
      analytics_record.total_orders, analytics_record.total_revenue,
      analytics_record.avg_order_value, COALESCE(analytics_record.avg_delivery_time, 0)
    )
    ON CONFLICT (zone_id, date_period, period_type)
    DO UPDATE SET
      total_orders = EXCLUDED.total_orders,
      total_revenue = EXCLUDED.total_revenue,
      average_order_value = EXCLUDED.average_order_value,
      average_delivery_time = EXCLUDED.average_delivery_time,
      updated_at = NOW();
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

ALTER TABLE web_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE app_configurations_enhanced ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_notification_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_behavior_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE delivery_zone_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE marketing_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE menu_pricing_strategies ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Admin full access to web configurations" ON web_configurations
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
        AND user_profiles.role_id IN (
          SELECT id FROM roles WHERE name IN ('admin', 'manager')
        )
    )
  );

CREATE POLICY "Public read access to public web configurations" ON web_configurations
  FOR SELECT USING (is_public = true);

CREATE POLICY "Admin full access to app configurations" ON app_configurations_enhanced
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
        AND user_profiles.role_id IN (
          SELECT id FROM roles WHERE name IN ('admin', 'manager')
        )
    )
  );

CREATE POLICY "Service role full access to user analytics" ON user_analytics
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Users can view own analytics" ON user_analytics
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admin access to marketing campaigns" ON marketing_campaigns
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
        AND user_profiles.role_id IN (
          SELECT id FROM roles WHERE name IN ('admin', 'manager', 'marketing')
        )
    )
  );

-- =============================================
-- ENABLE REALTIME
-- =============================================

ALTER PUBLICATION supabase_realtime ADD TABLE web_configurations;
ALTER PUBLICATION supabase_realtime ADD TABLE app_configurations_enhanced;
ALTER PUBLICATION supabase_realtime ADD TABLE push_notification_settings;
ALTER PUBLICATION supabase_realtime ADD TABLE delivery_zone_analytics;
ALTER PUBLICATION supabase_realtime ADD TABLE marketing_campaigns;
ALTER PUBLICATION supabase_realtime ADD TABLE menu_pricing_strategies;