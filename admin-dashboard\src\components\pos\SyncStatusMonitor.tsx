'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import {
  Activity,
  <PERSON><PERSON>hart<PERSON>,
  <PERSON>,
  AlertT<PERSON>gle,
  CheckCircle,
  XCircle,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Zap,
  Database,
  Wifi,
  WifiOff,
  Monitor,
  Settings,
  Download,
  Filter,
  Calendar,
  Eye,
  Bug,
  Terminal
} from 'lucide-react'

interface SyncMetrics {
  total_operations: number
  successful_operations: number
  failed_operations: number
  pending_operations: number
  avg_sync_time: number
  success_rate: number
  error_rate: number
  throughput_per_hour: number
  peak_usage_time: string
  last_24h_operations: number
}

interface TerminalHealth {
  terminal_id: string
  terminal_name: string
  status: 'online' | 'offline' | 'syncing' | 'error' | 'maintenance'
  health_score: number
  last_heartbeat: string
  uptime: number
  cpu_usage: number
  memory_usage: number
  disk_usage: number
  network_latency: number
  sync_queue_size: number
  error_count: number
  performance_score: number
}

interface SyncOperation {
  id: string
  operation_type: 'staff_permissions' | 'hardware_config' | 'inventory' | 'menu' | 'settings'
  terminal_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  started_at: string
  completed_at: string | null
  duration_ms: number | null
  error_message: string | null
  retry_count: number
  priority: number
  data_size: number
}

interface SyncAlert {
  id: string
  type: 'error' | 'warning' | 'info'
  title: string
  message: string
  terminal_id: string | null
  created_at: string
  acknowledged: boolean
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export default function SyncStatusMonitor() {
  const [syncMetrics, setSyncMetrics] = useState<SyncMetrics>({
    total_operations: 0,
    successful_operations: 0,
    failed_operations: 0,
    pending_operations: 0,
    avg_sync_time: 0,
    success_rate: 0,
    error_rate: 0,
    throughput_per_hour: 0,
    peak_usage_time: '',
    last_24h_operations: 0
  })
  const [terminalHealth, setTerminalHealth] = useState<TerminalHealth[]>([])
  const [syncOperations, setSyncOperations] = useState<SyncOperation[]>([])
  const [syncAlerts, setSyncAlerts] = useState<SyncAlert[]>([])
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('24h')
  const [selectedTerminal, setSelectedTerminal] = useState<string>('all')
  const [showDebugMode, setShowDebugMode] = useState(false)
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadData()
    
    // Set up auto-refresh every 10 seconds
    const interval = setInterval(() => {
      if (autoRefresh) {
        loadData()
      }
    }, 10000)

    return () => clearInterval(interval)
  }, [selectedTimeRange, selectedTerminal, autoRefresh])

  const loadData = async () => {
    try {
      setLoading(true)
      await Promise.all([
        loadSyncMetrics(),
        loadTerminalHealth(),
        loadSyncOperations(),
        loadSyncAlerts()
      ])
    } catch (error) {
      console.error('Failed to load sync monitoring data:', error)
      toast.error('Failed to load sync monitoring data')
    } finally {
      setLoading(false)
    }
  }

  const loadSyncMetrics = async () => {
    try {
      const params = new URLSearchParams()
      params.append('time_range', selectedTimeRange)
      if (selectedTerminal !== 'all') params.append('terminal_id', selectedTerminal)

      const response = await fetch(`/api/sync/metrics?${params}`)
      if (response.ok) {
        const data = await response.json()
        setSyncMetrics(data.metrics || syncMetrics)
      } else {
        // If API fails, use mock data for demonstration
        setSyncMetrics(generateMockSyncMetrics())
      }
    } catch (error) {
      console.error('Failed to load sync metrics:', error)
      // If API fails, use mock data for demonstration
      setSyncMetrics(generateMockSyncMetrics())
    }
  }

  const loadTerminalHealth = async () => {
    try {
      const response = await fetch('/api/pos/terminal-status')
      if (response.ok) {
        const data = await response.json()
        setTerminalHealth(data.terminals || [])
      }
    } catch (error) {
      console.error('Failed to load terminal health:', error)
    }
  }

  const loadSyncOperations = async () => {
    try {
      const params = new URLSearchParams()
      params.append('limit', '50')
      params.append('time_range', selectedTimeRange)
      if (selectedTerminal !== 'all') params.append('terminal_id', selectedTerminal)

      const response = await fetch(`/api/sync/operations?${params}`)
      if (response.ok) {
        const data = await response.json()
        setSyncOperations(data.operations || [])
      } else {
        // If API fails, use mock data for demonstration
        setSyncOperations(generateMockSyncOperations())
      }
    } catch (error) {
      console.error('Failed to load sync operations:', error)
      // If API fails, use mock data for demonstration
      setSyncOperations(generateMockSyncOperations())
    }
  }

  const loadSyncAlerts = async () => {
    try {
      const response = await fetch('/api/sync/alerts?unacknowledged=true')
      if (response.ok) {
        const data = await response.json()
        setSyncAlerts(data.alerts || [])
      }
    } catch (error) {
      console.error('Failed to load sync alerts:', error)
    }
  }

  const acknowledgeAlert = async (alertId: string) => {
    try {
      const response = await fetch(`/api/sync/alerts/${alertId}/acknowledge`, {
        method: 'POST'
      })

      if (response.ok) {
        setSyncAlerts(prev => prev.filter(alert => alert.id !== alertId))
        toast.success('Alert acknowledged')
      } else {
        toast.error('Failed to acknowledge alert')
      }
    } catch (error) {
      console.error('Failed to acknowledge alert:', error)
      toast.error('Failed to acknowledge alert')
    }
  }

  const exportSyncReport = async () => {
    try {
      const response = await fetch('/api/sync/export-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          time_range: selectedTimeRange,
          terminal_id: selectedTerminal === 'all' ? null : selectedTerminal,
          include_debug_info: showDebugMode
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `sync-report-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        toast.success('Sync report exported successfully')
      } else {
        toast.error('Failed to export sync report')
      }
    } catch (error) {
      console.error('Failed to export report:', error)
      toast.error('Failed to export sync report')
    }
  }

  const getHealthColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-yellow-600'
    if (score >= 50) return 'text-orange-600'
    return 'text-red-600'
  }

  const getHealthBgColor = (score: number) => {
    if (score >= 90) return 'bg-green-100 dark:bg-green-900'
    if (score >= 70) return 'bg-yellow-100 dark:bg-yellow-900'
    if (score >= 50) return 'bg-orange-100 dark:bg-orange-900'
    return 'bg-red-100 dark:bg-red-900'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <Wifi className="w-4 h-4 text-green-500" />
      case 'offline':
        return <WifiOff className="w-4 h-4 text-red-500" />
      case 'syncing':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'maintenance':
        return <Settings className="w-4 h-4 text-yellow-500" />
      default:
        return <Monitor className="w-4 h-4 text-gray-500" />
    }
  }

  const getOperationStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'running':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      default:
        return <Activity className="w-4 h-4 text-gray-500" />
    }
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />
      case 'info':
        return <CheckCircle className="w-5 h-5 text-blue-500" />
      default:
        return <Activity className="w-5 h-5 text-gray-500" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Sync Monitoring Dashboard</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Real-time monitoring and analytics for POS synchronization
          </p>
        </div>
        
        <div className="flex gap-2 mt-4 sm:mt-0">
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
              autoRefresh 
                ? 'bg-green-600 text-white hover:bg-green-700' 
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            <RefreshCw className={`w-4 h-4 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh: {autoRefresh ? 'On' : 'Off'}
          </button>
          
          <button
            onClick={() => setShowDebugMode(!showDebugMode)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
              showDebugMode 
                ? 'bg-purple-600 text-white hover:bg-purple-700' 
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            <Bug className="w-4 h-4" />
            Debug Mode
          </button>
          
          <button
            onClick={exportSyncReport}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Download className="w-4 h-4" />
            Export Report
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Time Range
            </label>
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="1h">Last Hour</option>
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Terminal
            </label>
            <select
              value={selectedTerminal}
              onChange={(e) => setSelectedTerminal(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Terminals</option>
              {terminalHealth.map((terminal) => (
                <option key={terminal.terminal_id} value={terminal.terminal_id}>
                  {terminal.terminal_name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={loadData}
              className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              <RefreshCw className="w-4 h-4 mx-auto" />
            </button>
          </div>
        </div>
      </div>

      {/* Sync Alerts */}
      {syncAlerts.length > 0 && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-red-800 dark:text-red-200">
              {syncAlerts.length} Active Alert{syncAlerts.length > 1 ? 's' : ''}
            </h3>
          </div>

          <div className="space-y-3">
            {syncAlerts.slice(0, 3).map((alert) => (
              <div key={alert.id} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg">
                <div className="flex items-center gap-3">
                  {getAlertIcon(alert.type)}
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">{alert.title}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">{alert.message}</div>
                  </div>
                </div>
                <button
                  onClick={() => acknowledgeAlert(alert.id)}
                  className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                >
                  Acknowledge
                </button>
              </div>
            ))}

            {syncAlerts.length > 3 && (
              <div className="text-center">
                <button className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                  View {syncAlerts.length - 3} more alerts
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Sync Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Success Rate</p>
              <p className="text-2xl font-bold text-green-600">{((syncMetrics.success_rate || 0) * 100).toFixed(1)}%</p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Sync Time</p>
              <p className="text-2xl font-bold text-blue-600">{(syncMetrics.avg_sync_time || 0).toFixed(0)}ms</p>
            </div>
            <Clock className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Throughput/Hour</p>
              <p className="text-2xl font-bold text-purple-600">{syncMetrics.throughput_per_hour}</p>
            </div>
            <Zap className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Ops</p>
              <p className="text-2xl font-bold text-yellow-600">{syncMetrics.pending_operations}</p>
            </div>
            <Database className="w-8 h-8 text-yellow-500" />
          </div>
        </div>
      </div>

      {/* Terminal Health Grid */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Terminal Health Status</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Real-time health monitoring for all POS terminals
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
          {terminalHealth.map((terminal) => (
            <div key={terminal.terminal_id} className={`p-4 rounded-lg border-2 ${getHealthBgColor(terminal.health_score)}`}>
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  {getStatusIcon(terminal.status)}
                  <h4 className="font-medium text-gray-900 dark:text-white">{terminal.terminal_name}</h4>
                </div>
                <div className={`text-lg font-bold ${getHealthColor(terminal.health_score || 0)}`}>
                  {(terminal.health_score || 0)}%
                </div>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">CPU:</span>
                  <span className="font-medium">{terminal.cpu_usage?.toFixed(1) || '0'}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Memory:</span>
                  <span className="font-medium">{terminal.memory_usage?.toFixed(1) || '0'}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Queue:</span>
                  <span className="font-medium">{terminal.sync_queue_size || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Latency:</span>
                  <span className="font-medium">{terminal.network_latency || 0}ms</span>
                </div>
              </div>

              <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Last heartbeat: {terminal.last_heartbeat ? new Date(terminal.last_heartbeat).toLocaleTimeString() : 'Never'}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Sync Operations */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Recent Sync Operations</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Latest synchronization operations across all terminals
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Operation
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Terminal
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Started
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {syncOperations.map((operation) => (
                <tr key={operation.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {operation.operation_type.replace('_', ' ').toUpperCase()}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Priority: {operation.priority || 0} • Size: {((operation.data_size || 0) / 1024).toFixed(1)}KB
                    </div>
                  </td>

                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {operation.terminal_id}
                  </td>

                  <td className="px-6 py-4">
                    <div className="flex items-center gap-2">
                      {getOperationStatusIcon(operation.status)}
                      <span className="text-sm font-medium">{operation.status}</span>
                    </div>
                    {operation.error_message && (
                      <div className="text-xs text-red-600 dark:text-red-400 mt-1">
                        {operation.error_message}
                      </div>
                    )}
                  </td>

                  <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                    {operation.duration_ms ? `${operation.duration_ms}ms` : '-'}
                  </td>

                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    {new Date(operation.started_at).toLocaleString()}
                  </td>

                  <td className="px-6 py-4 text-center">
                    <button
                      onClick={() => {/* View operation details */}}
                      className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                      title="View details"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {syncOperations.length === 0 && (
          <div className="text-center py-12">
            <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No sync operations found</h3>
            <p className="text-gray-600 dark:text-gray-400">
              Sync operations will appear here as they occur.
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

// Generate mock sync operations for demonstration
function generateMockSyncOperations(): SyncOperation[] {
  const operations: SyncOperation[] = []
  const operationTypes = ['staff_permissions', 'hardware_config', 'inventory', 'menu', 'settings'] as const
  const statuses = ['completed', 'failed', 'running', 'pending'] as const
  const terminalIds = ['770c7b00', 'terminal-001', 'terminal-002']

  // Generate 12 recent operations
  for (let i = 0; i < 12; i++) {
    const startTime = new Date(Date.now() - Math.random() * 6 * 60 * 60 * 1000) // Last 6 hours
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const duration = status === 'completed' ? Math.floor(Math.random() * 3000) + 500 :
                    status === 'failed' ? Math.floor(Math.random() * 2000) + 200 : null
    const completedAt = status === 'completed' || status === 'failed' ?
                       new Date(startTime.getTime() + (duration || 1000)) : null

    operations.push({
      id: `sync_op_${Date.now()}_${i}`,
      operation_type: operationTypes[Math.floor(Math.random() * operationTypes.length)],
      terminal_id: terminalIds[Math.floor(Math.random() * terminalIds.length)],
      status,
      started_at: startTime.toISOString(),
      completed_at: completedAt?.toISOString() || null,
      duration_ms: duration,
      error_message: status === 'failed' ?
        ['Network timeout', 'Database connection failed', 'Invalid data format', 'Permission denied'][Math.floor(Math.random() * 4)] : null,
      retry_count: status === 'failed' ? Math.floor(Math.random() * 3) : 0,
      priority: Math.floor(Math.random() * 5) + 1,
      data_size: Math.floor(Math.random() * 50000) + 1024
    })
  }

  // Sort by started_at descending (most recent first)
  return operations.sort((a, b) => new Date(b.started_at).getTime() - new Date(a.started_at).getTime())
}

// Generate mock sync metrics for demonstration
function generateMockSyncMetrics(): SyncMetrics {
  const totalOps = Math.floor(Math.random() * 100) + 50
  const successfulOps = Math.floor(totalOps * (0.7 + Math.random() * 0.25)) // 70-95% success rate
  const failedOps = Math.floor((totalOps - successfulOps) * 0.8)
  const pendingOps = totalOps - successfulOps - failedOps

  return {
    total_operations: totalOps,
    successful_operations: successfulOps,
    failed_operations: failedOps,
    pending_operations: pendingOps,
    avg_sync_time: Math.floor(Math.random() * 2000) + 800, // 800-2800ms
    success_rate: successfulOps / totalOps,
    error_rate: failedOps / totalOps,
    throughput_per_hour: Math.floor(Math.random() * 50) + 10, // 10-60 ops/hour
    peak_usage_time: '14:30',
    last_24h_operations: Math.floor(Math.random() * 200) + 100
  }
}
