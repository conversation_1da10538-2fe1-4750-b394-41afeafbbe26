import React, { useCallback, useEffect, useState } from 'react';
import { useTheme } from '../../contexts/theme-context';
import { menuService, MenuCategory } from '../../services/MenuService';

interface MenuCategoryTabsProps {
  selectedCategory: string;
  onCategoryChange: (categoryId: string) => void;
  selectedSubcategory?: string;
  onSubcategoryChange?: (subcategoryId: string) => void;
  hideAllItemsButton?: boolean;
}

const defaultCategories = [
  { id: "all", name: "All Items", icon: "🍽️" },
  { id: "featured", name: "Featured", icon: "⭐" }
];

const getCategoryIcon = (name: string): string => {
  const iconMap: Record<string, string> = {
    'crepes': '🥞',
    'waffles': '🧇',
    'toasts': '🍞',
    'beverages': '🥤',
    'desserts': '🧁',
    'salads': '🥗',
    'my crepe': '🎨',
    'my waffle': '🎨',
    'my toast': '🎨'
  };
  return iconMap[name.toLowerCase()] || '🍽️';
};

export const MenuCategoryTabs: React.FC<MenuCategoryTabsProps> = React.memo(({
  selectedCategory,
  onCategoryChange,
  selectedSubcategory = '',
  onSubcategoryChange,
  hideAllItemsButton = false
}) => {
  const { resolvedTheme } = useTheme();
  const [categories, setCategories] = useState<Array<{ id: string; name: string; icon: string }>>([]);
  const [loading, setLoading] = useState(true);
  
  // Get subcategories for selected category
  const getSubcategories = useCallback((categoryId: string) => {
    if (categoryId === 'all' || categoryId === 'featured') {
      return [];
    }
    
    const category = categories.find(cat => cat.id === categoryId);
    if (!category) return [];
    
    return [
      { id: `${categoryId}-standard`, name: 'Standard', icon: '📋' },
      { id: `${categoryId}-customizable`, name: 'Customizable', icon: '🎨' }
    ];
  }, [categories]);
  
  const handleCategoryChange = useCallback((categoryId: string) => {
    onCategoryChange(categoryId);
    // Reset subcategory when category changes
    if (onSubcategoryChange) {
      onSubcategoryChange('');
    }
  }, [onCategoryChange, onSubcategoryChange]);
  
  const handleSubcategoryChange = useCallback((subcategoryId: string) => {
    if (onSubcategoryChange) {
      onSubcategoryChange(subcategoryId);
    }
  }, [onSubcategoryChange]);

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const menuCategories = await menuService.getMenuCategories();
        
        // Combine default categories with database categories, but exclude defaults if hideAllItemsButton is true
        const allCategories = [
          ...(hideAllItemsButton ? [] : defaultCategories),
          ...menuCategories.map(cat => ({
            id: cat.id,
            name: cat.name || cat.name_en || 'Unknown',
            icon: getCategoryIcon(cat.name || cat.name_en || 'unknown')
          }))
        ];
        
        setCategories(allCategories);
      } catch (error) {
        console.error('Error loading categories:', error);
        // Fallback to default categories only if not hidden
        setCategories(hideAllItemsButton ? [] : defaultCategories);
      } finally {
        setLoading(false);
      }
    };

    loadCategories();

    // Temporarily disable real-time subscription to fix the error
    // TODO: Fix subscription management and re-enable
    /*
    // Subscribe to real-time updates
    const unsubscribe = menuService.subscribeToMenuUpdates(() => {
      loadCategories();
    });

    return unsubscribe;
        */
  }, [hideAllItemsButton]);

  if (loading) {
    return (
      <div className="p-4 border-b border-gray-200/20">
        <div className="flex space-x-2">
          {[1, 2, 3, 4].map(i => (
            <div
              key={i}
              className={`h-10 w-24 rounded-xl animate-pulse ${
                resolvedTheme === 'dark' ? 'bg-gray-700/30' : 'bg-gray-200/50'
              }`}
            />
          ))}
        </div>
      </div>
    );
  }

  const subcategories = getSubcategories(selectedCategory);

  return (
    <div className="border-b border-gray-200/20">
      {/* Main Categories */}
      <div className="p-4">
        <div className="flex space-x-2 overflow-x-auto">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleCategoryChange(category.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-xl font-medium transition-all duration-200 whitespace-nowrap ${
                selectedCategory === category.id
                  ? resolvedTheme === 'dark'
                    ? 'bg-blue-600/30 text-blue-400 border border-blue-500/50'
                    : 'bg-blue-100/50 text-blue-600 border border-blue-300/50'
                  : resolvedTheme === 'dark'
                    ? 'text-gray-300 hover:bg-gray-700/30'
                    : 'text-gray-600 hover:bg-gray-100/30'
              }`}
            >
              <span>{category.icon}</span>
              <span>{category.name}</span>
            </button>
          ))}
        </div>
      </div>
      
      {/* Subcategories - only show if there are subcategories */}
      {subcategories.length > 0 && (
        <div className="px-4 pb-4">
          <div className="flex space-x-2 overflow-x-auto">
            {subcategories.map((subcategory) => (
              <button
                key={subcategory.id}
                onClick={() => handleSubcategoryChange(subcategory.id)}
                className={`flex items-center space-x-2 px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap ${
                  selectedSubcategory === subcategory.id
                    ? resolvedTheme === 'dark'
                      ? 'bg-green-600/30 text-green-400 border border-green-500/50'
                      : 'bg-green-100/50 text-green-600 border border-green-300/50'
                    : resolvedTheme === 'dark'
                      ? 'text-gray-400 hover:bg-gray-700/30'
                      : 'text-gray-500 hover:bg-gray-100/30'
                }`}
              >
                <span>{subcategory.icon}</span>
                <span>{subcategory.name}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
});