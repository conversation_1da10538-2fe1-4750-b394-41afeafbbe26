# The Small Creperie - Admin Dashboard Documentation

## Overview

The Small Creperie Admin Dashboard is a comprehensive centralized management system that controls all aspects of your creperie business operations across multiple platforms including POS systems, customer web application, and mobile app.

## Quick Navigation

### Setup & Installation
- [Installation Guide](./setup/installation.md)
- [Initial Configuration](./setup/initial-configuration.md)
- [Database Setup](./setup/database-setup.md)
- [Environment Configuration](./setup/environment-config.md)

### User Guides
- [Getting Started Guide](./guides/getting-started.md)
- [Settings Management](./guides/settings-management.md)
- [POS Configuration](./guides/pos-configuration.md)
- [Menu Management](./guides/menu-management.md)
- [Staff Management](./guides/staff-management.md)
- [Branch Management](./guides/branch-management.md)
- [Analytics & Reporting](./guides/analytics-reporting.md)
- [System Administration](./guides/system-administration.md)

### Training Materials
- [Manager Training Course](./training/manager-training.md)
- [Staff Onboarding](./training/staff-onboarding.md)
- [POS Training](./training/pos-training.md)
- [Troubleshooting Guide](./training/troubleshooting.md)

### Technical Documentation
- [Architecture Overview](./technical/architecture.md)
- [Database Schema](./technical/database-schema.md)
- [API Reference](./technical/api-reference.md)
- [Real-time Synchronization](./technical/sync-architecture.md)
- [Testing Framework](./technical/testing-framework.md)

### Best Practices
- [Configuration Best Practices](./best-practices/configuration.md)
- [Security Guidelines](./best-practices/security.md)
- [Performance Optimization](./best-practices/performance.md)
- [Backup & Recovery](./best-practices/backup-recovery.md)

### Support
- [FAQ](./support/faq.md)
- [Common Issues](./support/common-issues.md)
- [Contact Support](./support/contact.md)

## System Overview

The admin dashboard provides centralized control over:

- **Multi-Platform Settings**: Configure POS systems, web app, and mobile app from one location
- **Real-time Synchronization**: All changes propagate instantly across all platforms
- **Menu Management**: Centralized menu control with automatic sync to all customer-facing apps
- **Staff & Permissions**: Role-based access control across all systems
- **Branch Management**: Multi-location support with location-specific configurations
- **Analytics & Reporting**: Comprehensive business intelligence and performance monitoring
- **System Administration**: Version management, feature flags, maintenance modes, and security

## Key Features

### 🔄 Real-time Synchronization
All configuration changes are immediately synchronized across all connected systems using Supabase real-time subscriptions.

### 🏪 Multi-Platform Support
- **Admin Dashboard**: Central management interface
- **POS System**: Point-of-sale terminal configuration
- **Customer Web App**: Online ordering system
- **Mobile App**: Customer mobile application

### 📊 Comprehensive Analytics
Real-time business intelligence with sales tracking, customer analytics, staff performance, and system health monitoring.

### 🔒 Security & Access Control
Role-based permissions, audit logging, and secure configuration management across all platforms.

### 🌐 Multi-Branch Support
Independent configuration for multiple locations while maintaining centralized oversight.

## Getting Help

If you need assistance:

1. Check the [FAQ](./support/faq.md) for common questions
2. Review [Common Issues](./support/common-issues.md) for troubleshooting
3. Consult the relevant user guide for your task
4. Contact support using the information in [Contact Support](./support/contact.md)

## Version Information

- **Current Version**: 1.0.0
- **Last Updated**: December 2024
- **Documentation Version**: 1.0.0 