/// <reference types="cypress" />

describe('Customer Checkout Process', () => {
  beforeEach(() => {
    // Clear any existing data
    cy.clearAllCookies();
    cy.clearAllLocalStorage();
    cy.clearAllSessionStorage();
    
    // Set up API interceptors
    cy.intercept('GET', '/api/cart', { fixture: 'orders/orders.json' }).as('getCart');
    cy.intercept('GET', '/api/checkout/delivery-options', { fixture: 'orders/orders.json' }).as('getDeliveryOptions');
    cy.intercept('GET', '/api/checkout/payment-methods', { fixture: 'orders/orders.json' }).as('getPaymentMethods');
    cy.intercept('POST', '/api/checkout/validate-address', { statusCode: 200 }).as('validateAddress');
    cy.intercept('POST', '/api/checkout/calculate-tax', { statusCode: 200 }).as('calculateTax');
    cy.intercept('POST', '/api/orders/create', { statusCode: 200, body: { orderId: 'ORD-12345' } }).as('createOrder');
    cy.intercept('POST', '/api/payments/process', { statusCode: 200 }).as('processPayment');
    
    // Login as customer and add items to cart
    cy.loginAsCustomer();
    cy.addItemToCart(1, 2);
    cy.addItemToCart(2, 1);
  });

  describe('Checkout Page Access', () => {
    it('should redirect to login if not authenticated', () => {
      cy.clearAllCookies();
      cy.visit('/checkout');
      
      cy.url().should('include', '/login');
      cy.get('[data-testid="redirect-message"]')
        .should('contain', 'Please login to continue with checkout');
    });

    it('should redirect to cart if cart is empty', () => {
      cy.intercept('GET', '/api/cart', {
        body: { items: [], total: 0, itemCount: 0 }
      }).as('getEmptyCart');
      
      cy.visit('/checkout');
      cy.wait('@getEmptyCart');
      
      cy.url().should('include', '/cart');
      cy.checkNotification('Your cart is empty', 'warning');
    });

    it('should display checkout page with cart items', () => {
      cy.visitCheckoutPage();
      cy.wait('@getCart');
      
      // Check page title and heading
      cy.title().should('contain', 'Checkout');
      cy.get('h1').should('contain', 'Checkout');
      
      // Check main sections
      cy.get('[data-testid="checkout-steps"]').should('be.visible');
      cy.get('[data-testid="order-summary"]').should('be.visible');
      cy.get('[data-testid="checkout-form"]').should('be.visible');
    });
  });

  describe('Checkout Steps Navigation', () => {
    beforeEach(() => {
      cy.visitCheckoutPage();
      cy.wait('@getCart');
    });

    it('should display checkout steps', () => {
      cy.get('[data-testid="checkout-steps"]').within(() => {
        cy.get('[data-testid="step-delivery"]')
          .should('be.visible')
          .and('contain', 'Delivery')
          .and('have.class', 'active');
        
        cy.get('[data-testid="step-payment"]')
          .should('be.visible')
          .and('contain', 'Payment')
          .and('have.class', 'inactive');
        
        cy.get('[data-testid="step-review"]')
          .should('be.visible')
          .and('contain', 'Review')
          .and('have.class', 'inactive');
      });
    });

    it('should navigate between steps', () => {
      // Complete delivery step
      cy.fillCheckoutForm();
      cy.get('[data-testid="continue-to-payment"]').click();
      
      cy.get('[data-testid="step-delivery"]').should('have.class', 'completed');
      cy.get('[data-testid="step-payment"]').should('have.class', 'active');
      
      // Go back to delivery step
      cy.get('[data-testid="step-delivery"]').click();
      
      cy.get('[data-testid="step-delivery"]').should('have.class', 'active');
      cy.get('[data-testid="delivery-form"]').should('be.visible');
    });

    it('should prevent skipping incomplete steps', () => {
      cy.get('[data-testid="step-payment"]').click();
      
      cy.get('[data-testid="step-delivery"]').should('have.class', 'active');
      cy.checkNotification('Please complete delivery information first', 'warning');
    });
  });

  describe('Delivery Information Step', () => {
    beforeEach(() => {
      cy.visitCheckoutPage();
      cy.wait('@getCart');
    });

    it('should display delivery options', () => {
      cy.get('[data-testid="delivery-options"]').should('be.visible');
      
      cy.get('[data-testid="delivery-option-delivery"]')
        .should('be.visible')
        .and('contain', 'Delivery');
      
      cy.get('[data-testid="delivery-option-pickup"]')
        .should('be.visible')
        .and('contain', 'Pickup');
    });

    it('should select delivery option and show address form', () => {
      cy.selectDeliveryOption('delivery');
      
      cy.get('[data-testid="delivery-address-form"]').should('be.visible');
      
      cy.get('[data-testid="address-street"]').should('be.visible');
      cy.get('[data-testid="address-city"]').should('be.visible');
      cy.get('[data-testid="address-state"]').should('be.visible');
      cy.get('[data-testid="address-zip"]').should('be.visible');
      cy.get('[data-testid="delivery-instructions"]').should('be.visible');
    });

    it('should select pickup option and show pickup details', () => {
      cy.selectDeliveryOption('pickup');
      
      cy.get('[data-testid="pickup-details"]').should('be.visible');
      cy.get('[data-testid="pickup-location"]').should('be.visible');
      cy.get('[data-testid="pickup-time-slots"]').should('be.visible');
    });

    it('should validate delivery address', () => {
      cy.selectDeliveryOption('delivery');
      
      // Test empty fields
      cy.get('[data-testid="continue-to-payment"]').click();
      
      cy.get('[data-testid="address-street-error"]')
        .should('be.visible')
        .and('contain', 'Street address is required');
      
      cy.get('[data-testid="address-city-error"]')
        .should('be.visible')
        .and('contain', 'City is required');
      
      // Fill valid address
      cy.get('[data-testid="address-street"]').type('123 Main St');
      cy.get('[data-testid="address-city"]').type('New York');
      cy.get('[data-testid="address-state"]').select('NY');
      cy.get('[data-testid="address-zip"]').type('10001');
      
      cy.get('[data-testid="address-street-error"]').should('not.exist');
      cy.get('[data-testid="address-city-error"]').should('not.exist');
    });

    it('should validate ZIP code format', () => {
      cy.selectDeliveryOption('delivery');
      
      cy.get('[data-testid="address-zip"]').type('invalid');
      cy.get('[data-testid="address-zip"]').blur();
      
      cy.get('[data-testid="address-zip-error"]')
        .should('be.visible')
        .and('contain', 'Please enter a valid ZIP code');
      
      cy.get('[data-testid="address-zip"]').clear().type('10001');
      cy.get('[data-testid="address-zip-error"]').should('not.exist');
    });

    it('should validate delivery address with API', () => {
      cy.selectDeliveryOption('delivery');
      
      cy.get('[data-testid="address-street"]').type('123 Main St');
      cy.get('[data-testid="address-city"]').type('New York');
      cy.get('[data-testid="address-state"]').select('NY');
      cy.get('[data-testid="address-zip"]').type('10001');
      
      cy.get('[data-testid="validate-address"]').click();
      
      cy.wait('@validateAddress');
      
      cy.get('[data-testid="address-validation-success"]')
        .should('be.visible')
        .and('contain', 'Address validated');
    });

    it('should handle invalid delivery address', () => {
      cy.intercept('POST', '/api/checkout/validate-address', {
        statusCode: 400,
        body: { error: 'Address not found' }
      }).as('invalidAddress');
      
      cy.selectDeliveryOption('delivery');
      
      cy.get('[data-testid="address-street"]').type('Invalid Address');
      cy.get('[data-testid="address-city"]').type('Invalid City');
      cy.get('[data-testid="address-state"]').select('NY');
      cy.get('[data-testid="address-zip"]').type('00000');
      
      cy.get('[data-testid="validate-address"]').click();
      
      cy.wait('@invalidAddress');
      
      cy.get('[data-testid="address-validation-error"]')
        .should('be.visible')
        .and('contain', 'Address not found');
    });

    it('should select pickup time slot', () => {
      cy.selectDeliveryOption('pickup');
      
      cy.get('[data-testid="pickup-time-slots"]').within(() => {
        cy.get('[data-testid="time-slot"]').first().click();
      });
      
      cy.get('[data-testid="time-slot"]').first()
        .should('have.class', 'selected')
        .and('have.attr', 'aria-checked', 'true');
    });

    it('should show estimated delivery time', () => {
      cy.selectDeliveryOption('delivery');
      cy.fillCheckoutForm();
      
      cy.get('[data-testid="estimated-delivery-time"]')
        .should('be.visible')
        .and('contain', 'minutes');
    });

    it('should calculate delivery fee', () => {
      cy.selectDeliveryOption('delivery');
      
      cy.get('[data-testid="delivery-fee"]')
        .should('be.visible')
        .and('contain', '$');
      
      cy.selectDeliveryOption('pickup');
      
      cy.get('[data-testid="delivery-fee"]')
        .should('contain', '$0.00');
    });
  });

  describe('Payment Information Step', () => {
    beforeEach(() => {
      cy.visitCheckoutPage();
      cy.wait('@getCart');
      
      // Complete delivery step
      cy.fillCheckoutForm();
      cy.get('[data-testid="continue-to-payment"]').click();
    });

    it('should display payment methods', () => {
      cy.get('[data-testid="payment-methods"]').should('be.visible');
      
      cy.get('[data-testid="payment-method-card"]')
        .should('be.visible')
        .and('contain', 'Credit/Debit Card');
      
      cy.get('[data-testid="payment-method-paypal"]')
        .should('be.visible')
        .and('contain', 'PayPal');
      
      cy.get('[data-testid="payment-method-apple-pay"]')
        .should('be.visible')
        .and('contain', 'Apple Pay');
    });

    it('should select credit card payment and show form', () => {
      cy.selectPaymentMethod('card');
      
      cy.get('[data-testid="card-payment-form"]').should('be.visible');
      
      cy.get('[data-testid="card-number"]').should('be.visible');
      cy.get('[data-testid="card-expiry"]').should('be.visible');
      cy.get('[data-testid="card-cvc"]').should('be.visible');
      cy.get('[data-testid="card-name"]').should('be.visible');
    });

    it('should validate credit card information', () => {
      cy.selectPaymentMethod('card');
      
      // Test empty fields
      cy.get('[data-testid="continue-to-review"]').click();
      
      cy.get('[data-testid="card-number-error"]')
        .should('be.visible')
        .and('contain', 'Card number is required');
      
      // Test invalid card number
      cy.get('[data-testid="card-number"]').type('1234');
      cy.get('[data-testid="card-number"]').blur();
      
      cy.get('[data-testid="card-number-error"]')
        .should('be.visible')
        .and('contain', 'Please enter a valid card number');
      
      // Test valid card number
      cy.get('[data-testid="card-number"]').clear().type('****************');
      cy.get('[data-testid="card-number-error"]').should('not.exist');
    });

    it('should validate card expiry date', () => {
      cy.selectPaymentMethod('card');
      
      // Test invalid expiry
      cy.get('[data-testid="card-expiry"]').type('01/20');
      cy.get('[data-testid="card-expiry"]').blur();
      
      cy.get('[data-testid="card-expiry-error"]')
        .should('be.visible')
        .and('contain', 'Card has expired');
      
      // Test valid expiry
      const futureYear = new Date().getFullYear() + 2;
      cy.get('[data-testid="card-expiry"]').clear().type(`12/${futureYear.toString().slice(-2)}`);
      cy.get('[data-testid="card-expiry-error"]').should('not.exist');
    });

    it('should validate CVC', () => {
      cy.selectPaymentMethod('card');
      
      // Test invalid CVC
      cy.get('[data-testid="card-cvc"]').type('12');
      cy.get('[data-testid="card-cvc"]').blur();
      
      cy.get('[data-testid="card-cvc-error"]')
        .should('be.visible')
        .and('contain', 'Please enter a valid CVC');
      
      // Test valid CVC
      cy.get('[data-testid="card-cvc"]').clear().type('123');
      cy.get('[data-testid="card-cvc-error"]').should('not.exist');
    });

    it('should format card number input', () => {
      cy.selectPaymentMethod('card');
      
      cy.get('[data-testid="card-number"]').type('****************');
      
      cy.get('[data-testid="card-number"]')
        .should('have.value', '4111 1111 1111 1111');
    });

    it('should detect card type', () => {
      cy.selectPaymentMethod('card');
      
      // Test Visa
      cy.get('[data-testid="card-number"]').type('4111');
      cy.get('[data-testid="card-type-icon"]').should('have.class', 'visa');
      
      // Test Mastercard
      cy.get('[data-testid="card-number"]').clear().type('5555');
      cy.get('[data-testid="card-type-icon"]').should('have.class', 'mastercard');
      
      // Test American Express
      cy.get('[data-testid="card-number"]').clear().type('3782');
      cy.get('[data-testid="card-type-icon"]').should('have.class', 'amex');
    });

    it('should save payment method for future use', () => {
      cy.selectPaymentMethod('card');
      cy.fillPaymentForm();
      
      cy.get('[data-testid="save-payment-method"]').check();
      
      cy.get('[data-testid="save-payment-method"]')
        .should('be.checked');
    });

    it('should use saved payment method', () => {
      // Mock saved payment methods
      cy.intercept('GET', '/api/checkout/payment-methods', {
        body: {
          savedMethods: [
            {
              id: 'pm_123',
              type: 'card',
              last4: '1111',
              brand: 'visa',
              expiryMonth: 12,
              expiryYear: 2025
            }
          ]
        }
      }).as('getSavedMethods');
      
      cy.visit('/checkout');
      cy.wait('@getSavedMethods');
      
      cy.get('[data-testid="saved-payment-method"]').should('be.visible');
      cy.get('[data-testid="saved-payment-method"]').click();
      
      cy.get('[data-testid="saved-method-selected"]')
        .should('be.visible')
        .and('contain', '**** 1111');
    });

    it('should handle PayPal payment', () => {
      cy.selectPaymentMethod('paypal');
      
      cy.get('[data-testid="paypal-button"]').should('be.visible');
      cy.get('[data-testid="paypal-info"]')
        .should('be.visible')
        .and('contain', 'You will be redirected to PayPal');
    });

    it('should handle Apple Pay payment', () => {
      cy.selectPaymentMethod('apple-pay');
      
      cy.get('[data-testid="apple-pay-button"]').should('be.visible');
      cy.get('[data-testid="apple-pay-info"]')
        .should('be.visible')
        .and('contain', 'Use Touch ID or Face ID');
    });
  });

  describe('Order Review Step', () => {
    beforeEach(() => {
      cy.visitCheckoutPage();
      cy.wait('@getCart');
      
      // Complete delivery and payment steps
      cy.fillCheckoutForm();
      cy.get('[data-testid="continue-to-payment"]').click();
      
      cy.selectPaymentMethod('card');
      cy.fillPaymentForm();
      cy.get('[data-testid="continue-to-review"]').click();
    });

    it('should display order summary', () => {
      cy.get('[data-testid="order-review"]').should('be.visible');
      
      cy.get('[data-testid="review-items"]').should('be.visible');
      cy.get('[data-testid="review-delivery"]').should('be.visible');
      cy.get('[data-testid="review-payment"]').should('be.visible');
      cy.get('[data-testid="review-totals"]').should('be.visible');
    });

    it('should display order items correctly', () => {
      cy.fixture('orders/orders').then((orderData) => {
        const items = orderData.cart.items;
        
        cy.get('[data-testid="review-item"]').should('have.length', items.length);
        
        cy.get('[data-testid="review-item"]').first().within(() => {
          cy.get('[data-testid="item-name"]').should('contain', items[0].name);
          cy.get('[data-testid="item-quantity"]').should('contain', `x${items[0].quantity}`);
          cy.get('[data-testid="item-price"]').should('contain', `$${items[0].total}`);
        });
      });
    });

    it('should display delivery information', () => {
      cy.get('[data-testid="review-delivery"]').within(() => {
        cy.get('[data-testid="delivery-method"]').should('be.visible');
        cy.get('[data-testid="delivery-address"]').should('be.visible');
        cy.get('[data-testid="delivery-time"]').should('be.visible');
      });
    });

    it('should display payment information', () => {
      cy.get('[data-testid="review-payment"]').within(() => {
        cy.get('[data-testid="payment-method"]').should('be.visible');
        cy.get('[data-testid="payment-details"]').should('contain', '**** 1111');
      });
    });

    it('should display order totals', () => {
      cy.get('[data-testid="review-totals"]').within(() => {
        cy.get('[data-testid="subtotal"]').should('be.visible');
        cy.get('[data-testid="tax-amount"]').should('be.visible');
        cy.get('[data-testid="delivery-fee"]').should('be.visible');
        cy.get('[data-testid="total-amount"]').should('be.visible');
      });
    });

    it('should allow editing delivery information', () => {
      cy.get('[data-testid="edit-delivery"]').click();
      
      cy.get('[data-testid="step-delivery"]').should('have.class', 'active');
      cy.get('[data-testid="delivery-form"]').should('be.visible');
    });

    it('should allow editing payment information', () => {
      cy.get('[data-testid="edit-payment"]').click();
      
      cy.get('[data-testid="step-payment"]').should('have.class', 'active');
      cy.get('[data-testid="payment-methods"]').should('be.visible');
    });

    it('should show terms and conditions', () => {
      cy.get('[data-testid="terms-checkbox"]').should('be.visible');
      cy.get('[data-testid="terms-link"]')
        .should('be.visible')
        .and('contain', 'Terms and Conditions');
      
      cy.get('[data-testid="privacy-link"]')
        .should('be.visible')
        .and('contain', 'Privacy Policy');
    });

    it('should require terms acceptance', () => {
      cy.get('[data-testid="place-order-button"]').click();
      
      cy.get('[data-testid="terms-error"]')
        .should('be.visible')
        .and('contain', 'Please accept the terms and conditions');
      
      cy.get('[data-testid="terms-checkbox"]').check();
      cy.get('[data-testid="terms-error"]').should('not.exist');
    });
  });

  describe('Order Placement', () => {
    beforeEach(() => {
      cy.visitCheckoutPage();
      cy.wait('@getCart');
      
      // Complete all steps
      cy.fillCheckoutForm();
      cy.get('[data-testid="continue-to-payment"]').click();
      
      cy.selectPaymentMethod('card');
      cy.fillPaymentForm();
      cy.get('[data-testid="continue-to-review"]').click();
      
      cy.get('[data-testid="terms-checkbox"]').check();
    });

    it('should place order successfully', () => {
      cy.get('[data-testid="place-order-button"]').click();
      
      cy.wait('@processPayment');
      cy.wait('@createOrder');
      
      cy.url().should('include', '/order-confirmation');
      
      cy.get('[data-testid="order-success-message"]')
        .should('be.visible')
        .and('contain', 'Order placed successfully');
      
      cy.get('[data-testid="order-number"]')
        .should('be.visible')
        .and('contain', 'ORD-12345');
    });

    it('should show loading state during order placement', () => {
      cy.intercept('POST', '/api/orders/create', (req) => {
        req.reply((res) => {
          res.delay(2000);
          res.send({ statusCode: 200, body: { orderId: 'ORD-12345' } });
        });
      }).as('slowOrder');
      
      cy.get('[data-testid="place-order-button"]').click();
      
      cy.get('[data-testid="place-order-button"]')
        .should('be.disabled')
        .and('contain', 'Placing Order...');
      
      cy.get('[data-testid="order-loading"]').should('be.visible');
      
      cy.wait('@slowOrder');
    });

    it('should handle payment failure', () => {
      cy.intercept('POST', '/api/payments/process', {
        statusCode: 400,
        body: { error: 'Payment declined' }
      }).as('paymentError');
      
      cy.get('[data-testid="place-order-button"]').click();
      
      cy.wait('@paymentError');
      
      cy.checkNotification('Payment declined', 'error');
      
      // Should stay on review step
      cy.get('[data-testid="step-review"]').should('have.class', 'active');
    });

    it('should handle order creation failure', () => {
      cy.intercept('POST', '/api/orders/create', {
        statusCode: 500,
        body: { error: 'Unable to create order' }
      }).as('orderError');
      
      cy.get('[data-testid="place-order-button"]').click();
      
      cy.wait('@processPayment');
      cy.wait('@orderError');
      
      cy.checkNotification('Unable to create order', 'error');
      
      cy.get('[data-testid="retry-order"]').should('be.visible');
    });

    it('should retry failed order', () => {
      // First attempt fails
      cy.intercept('POST', '/api/orders/create', {
        statusCode: 500,
        body: { error: 'Unable to create order' }
      }).as('orderError');
      
      cy.get('[data-testid="place-order-button"]').click();
      cy.wait('@orderError');
      
      // Set up successful retry
      cy.intercept('POST', '/api/orders/create', {
        statusCode: 200,
        body: { orderId: 'ORD-12345' }
      }).as('orderRetry');
      
      cy.get('[data-testid="retry-order"]').click();
      
      cy.wait('@orderRetry');
      
      cy.url().should('include', '/order-confirmation');
    });

    it('should prevent double submission', () => {
      cy.get('[data-testid="place-order-button"]').click();
      cy.get('[data-testid="place-order-button"]').click();
      
      // Should only make one API call
      cy.get('@createOrder.all').should('have.length', 1);
    });
  });

  describe('Order Summary Sidebar', () => {
    beforeEach(() => {
      cy.visitCheckoutPage();
      cy.wait('@getCart');
    });

    it('should display order summary', () => {
      cy.get('[data-testid="order-summary"]').should('be.visible');
      
      cy.get('[data-testid="summary-items"]').should('be.visible');
      cy.get('[data-testid="summary-totals"]').should('be.visible');
    });

    it('should show item details in summary', () => {
      cy.fixture('orders/orders').then((orderData) => {
        const items = orderData.cart.items;
        
        cy.get('[data-testid="summary-item"]').should('have.length', items.length);
        
        cy.get('[data-testid="summary-item"]').first().within(() => {
          cy.get('[data-testid="item-name"]').should('contain', items[0].name);
          cy.get('[data-testid="item-quantity"]').should('contain', items[0].quantity);
          cy.get('[data-testid="item-price"]').should('contain', `$${items[0].total}`);
        });
      });
    });

    it('should update totals when delivery option changes', () => {
      cy.get('[data-testid="summary-total"]').invoke('text').then((initialTotal) => {
        cy.selectDeliveryOption('pickup');
        
        cy.get('[data-testid="summary-total"]').should('not.contain', initialTotal);
      });
    });

    it('should show promo code discount', () => {
      cy.intercept('POST', '/api/cart/promo', {
        statusCode: 200,
        body: { discount: 10.00, code: 'SAVE10' }
      }).as('applyPromo');
      
      cy.get('[data-testid="promo-code-input"]').type('SAVE10');
      cy.get('[data-testid="apply-promo-button"]').click();
      
      cy.wait('@applyPromo');
      
      cy.get('[data-testid="summary-discount"]')
        .should('be.visible')
        .and('contain', '-$10.00');
    });

    it('should be sticky on scroll', () => {
      cy.scrollTo('bottom');
      
      cy.get('[data-testid="order-summary"]')
        .should('have.css', 'position', 'sticky');
    });
  });

  describe('Form Validation', () => {
    beforeEach(() => {
      cy.visitCheckoutPage();
      cy.wait('@getCart');
    });

    it('should validate required fields', () => {
      cy.selectDeliveryOption('delivery');
      cy.get('[data-testid="continue-to-payment"]').click();
      
      cy.get('[data-testid="validation-errors"]').should('be.visible');
      cy.get('[data-testid="error-count"]').should('contain', 'errors');
    });

    it('should clear validation errors when fields are filled', () => {
      cy.selectDeliveryOption('delivery');
      cy.get('[data-testid="continue-to-payment"]').click();
      
      cy.get('[data-testid="address-street-error"]').should('be.visible');
      
      cy.get('[data-testid="address-street"]').type('123 Main St');
      
      cy.get('[data-testid="address-street-error"]').should('not.exist');
    });

    it('should validate email format', () => {
      cy.get('[data-testid="contact-email"]').type('invalid-email');
      cy.get('[data-testid="contact-email"]').blur();
      
      cy.get('[data-testid="contact-email-error"]')
        .should('be.visible')
        .and('contain', 'Please enter a valid email address');
    });

    it('should validate phone number format', () => {
      cy.get('[data-testid="contact-phone"]').type('123');
      cy.get('[data-testid="contact-phone"]').blur();
      
      cy.get('[data-testid="contact-phone-error"]')
        .should('be.visible')
        .and('contain', 'Please enter a valid phone number');
    });
  });

  describe('Auto-save and Recovery', () => {
    beforeEach(() => {
      cy.visitCheckoutPage();
      cy.wait('@getCart');
    });

    it('should auto-save form data', () => {
      cy.selectDeliveryOption('delivery');
      cy.get('[data-testid="address-street"]').type('123 Main St');
      cy.get('[data-testid="address-city"]').type('New York');
      
      // Refresh page
      cy.reload();
      cy.wait('@getCart');
      
      // Check if data is restored
      cy.get('[data-testid="address-street"]').should('have.value', '123 Main St');
      cy.get('[data-testid="address-city"]').should('have.value', 'New York');
    });

    it('should show unsaved changes warning', () => {
      cy.selectDeliveryOption('delivery');
      cy.get('[data-testid="address-street"]').type('123 Main St');
      
      cy.window().then((win) => {
        cy.stub(win, 'beforeunload').as('beforeUnload');
      });
      
      cy.visit('/menu');
      
      cy.get('@beforeUnload').should('have.been.called');
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      cy.visitCheckoutPage();
      cy.wait('@getCart');
    });

    it('should be accessible', () => {
      cy.checkAccessibility();
    });

    it('should support keyboard navigation', () => {
      cy.checkKeyboardNavigation();
      
      // Test tab order through form fields
      cy.get('[data-testid="delivery-option-delivery"]').focus();
      cy.focused().should('have.attr', 'data-testid', 'delivery-option-delivery');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'delivery-option-pickup');
    });

    it('should have proper ARIA labels', () => {
      cy.get('[data-testid="checkout-form"]')
        .should('have.attr', 'role', 'form');
      
      cy.get('[data-testid="delivery-options"]')
        .should('have.attr', 'role', 'radiogroup');
      
      cy.get('[data-testid="address-street"]')
        .should('have.attr', 'aria-label')
        .and('contain', 'Street address');
    });

    it('should announce step changes to screen readers', () => {
      cy.fillCheckoutForm();
      cy.get('[data-testid="continue-to-payment"]').click();
      
      cy.get('[data-testid="step-announcement"]')
        .should('have.attr', 'aria-live', 'polite')
        .and('contain', 'Payment step');
    });
  });

  describe('Responsive Design', () => {
    it('should work on different screen sizes', () => {
      cy.testResponsiveDesign();
      
      // Test mobile layout
      cy.viewport(375, 667);
      cy.visitCheckoutPage();
      cy.wait('@getCart');
      
      cy.get('[data-testid="checkout-layout"]')
        .should('have.css', 'flex-direction', 'column');
      
      cy.get('[data-testid="order-summary"]')
        .should('have.css', 'position', 'static');
      
      // Test tablet layout
      cy.viewport(768, 1024);
      cy.get('[data-testid="checkout-layout"]')
        .should('have.css', 'grid-template-columns')
        .and('match', /1fr/);
      
      // Test desktop layout
      cy.viewport(1280, 720);
      cy.get('[data-testid="checkout-layout"]')
        .should('have.css', 'grid-template-columns')
        .and('match', /2fr 1fr/);
    });

    it('should show mobile-optimized steps', () => {
      cy.viewport(375, 667);
      cy.visitCheckoutPage();
      cy.wait('@getCart');
      
      cy.get('[data-testid="mobile-steps"]').should('be.visible');
      cy.get('[data-testid="step-progress-bar"]').should('be.visible');
    });
  });

  describe('Performance', () => {
    it('should load checkout page quickly', () => {
      cy.visit('/checkout');
      cy.measurePageLoadTime();
    });

    it('should optimize images', () => {
      cy.visitCheckoutPage();
      cy.wait('@getCart');
      cy.checkImageOptimization();
    });

    it('should debounce address validation', () => {
      cy.visitCheckoutPage();
      cy.wait('@getCart');
      
      cy.selectDeliveryOption('delivery');
      
      // Rapidly type in address field
      cy.get('[data-testid="address-street"]')
        .type('123')
        .type(' Main')
        .type(' Street');
      
      // Should only make one validation call after debounce
      cy.wait('@validateAddress');
      cy.get('@validateAddress.all').should('have.length', 1);
    });
  });

  describe('Security', () => {
    beforeEach(() => {
      cy.visitCheckoutPage();
      cy.wait('@getCart');
    });

    it('should not expose sensitive payment data', () => {
      cy.selectDeliveryOption('delivery');
      cy.fillCheckoutForm();
      cy.get('[data-testid="continue-to-payment"]').click();
      
      cy.selectPaymentMethod('card');
      cy.fillPaymentForm();
      
      // Check that card number is not visible in DOM
      cy.get('body').should('not.contain', '****************');
      
      // Check that CVC is not visible in DOM
      cy.get('body').should('not.contain', '123');
    });

    it('should use HTTPS for payment processing', () => {
      cy.selectDeliveryOption('delivery');
      cy.fillCheckoutForm();
      cy.get('[data-testid="continue-to-payment"]').click();
      
      cy.selectPaymentMethod('card');
      cy.fillPaymentForm();
      cy.get('[data-testid="continue-to-review"]').click();
      
      cy.get('[data-testid="terms-checkbox"]').check();
      cy.get('[data-testid="place-order-button"]').click();
      
      cy.wait('@processPayment').then((interception) => {
        expect(interception.request.url).to.include('https://');
      });
    });

    it('should validate CSRF token', () => {
      cy.get('[data-testid="csrf-token"]')
        .should('exist')
        .and('have.attr', 'content');
    });
  });
});