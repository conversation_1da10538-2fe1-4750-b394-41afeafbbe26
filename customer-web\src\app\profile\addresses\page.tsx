import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { GlassCard, GlassButton, GlassInput, GlassModal } from '@/components/ui/glass-components';

export const metadata: Metadata = {
  title: 'Manage Addresses | Delicious Crepes & Waffles',
  description: 'Add, edit, and manage your delivery addresses.',
};

export default function AddressesPage() {
  // Mock user data - in a real app, this would come from the AuthProvider
  const user = {
    id: 'user123',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/images/avatar.jpg',
    addresses: [
      {
        id: 'addr1',
        name: 'Home',
        address: '123 Main St, Apt 4B',
        city: 'Athens',
        postalCode: '12345',
        isDefault: true,
      },
      {
        id: 'addr2',
        name: 'Work',
        address: '456 Office Blvd, Floor 3',
        city: 'Athens',
        postalCode: '12346',
        isDefault: false,
      },
      {
        id: 'addr3',
        name: '<PERSON>',
        address: '789 Family Lane',
        city: 'Thessaloniki',
        postalCode: '54321',
        isDefault: false,
      },
    ],
  };

  return (
    <main className="min-h-screen py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">My Addresses</h1>
          <GlassButton variant="primary">Add New Address</GlassButton>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <GlassCard>
              <div className="p-6">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="relative h-16 w-16 rounded-full overflow-hidden">
                    <Image src={user.avatar} alt={user.name} fill className="object-cover" />
                  </div>
                  <div>
                    <h2 className="font-semibold">{user.name}</h2>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                  </div>
                </div>

                <nav className="space-y-2">
                  <Link
                    href="/profile"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Personal Information
                  </Link>
                  <Link
                    href="/profile/addresses"
                    className="block p-2 rounded-md bg-primary/10 text-primary font-medium"
                  >
                    Addresses
                  </Link>
                  <Link
                    href="/profile/payment-methods"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Payment Methods
                  </Link>
                  <Link
                    href="/profile/favorites"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Favorites
                  </Link>
                  <Link
                    href="/profile/settings"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Settings
                  </Link>
                  <Link
                    href="/orders"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Order History
                  </Link>
                </nav>
              </div>
            </GlassCard>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {/* Address List */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {user.addresses.map(address => (
                <GlassCard key={address.id}>
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="font-semibold text-lg">{address.name}</h3>
                        {address.isDefault && (
                          <span className="inline-block text-xs bg-primary/10 text-primary px-2 py-1 rounded-full mt-1">
                            Default
                          </span>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <button className="text-muted-foreground hover:text-foreground">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                          </svg>
                        </button>
                        <button className="text-muted-foreground hover:text-red-500">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>

                    <div className="text-muted-foreground">
                      <p>{address.address}</p>
                      <p>
                        {address.city}, {address.postalCode}
                      </p>
                    </div>

                    <div className="mt-4 flex justify-between">
                      {!address.isDefault && (
                        <GlassButton variant="secondary" size="sm">
                          Set as Default
                        </GlassButton>
                      )}
                      {address.isDefault && (
                        <div className="text-sm text-muted-foreground">
                          Used for deliveries by default
                        </div>
                      )}
                    </div>
                  </div>
                </GlassCard>
              ))}

              {/* Add New Address Card */}
              <GlassCard className="border border-dashed border-muted-foreground/50">
                <button className="h-full w-full flex flex-col items-center justify-center p-6 text-muted-foreground hover:text-foreground transition-colors">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-12 w-12 mb-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  <span className="font-medium">Add New Address</span>
                </button>
              </GlassCard>
            </div>

            {/* Address Form - This would be shown in a modal in a real app */}
            <GlassCard className="mt-8">
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-6">Add New Address</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <GlassInput label="Address Name" placeholder="e.g., Home, Work, etc." />

                  <GlassInput label="Full Address" placeholder="Street address, apt, suite, etc." />

                  <GlassInput label="City" placeholder="City" />

                  <GlassInput label="Postal Code" placeholder="Postal code" />

                  <div className="md:col-span-2">
                    <label className="flex items-center space-x-2 cursor-pointer">
                      <input type="checkbox" className="h-4 w-4 text-primary rounded" />

                      <span>Set as default address</span>
                    </label>
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-4">
                  <GlassButton variant="secondary">Cancel</GlassButton>
                  <GlassButton variant="primary">Save Address</GlassButton>
                </div>
              </div>
            </GlassCard>
          </div>
        </div>
      </div>
    </main>
  );
}
