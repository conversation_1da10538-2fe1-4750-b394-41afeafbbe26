'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import {
  Power,
  RotateCcw,
  Settings,
  AlertTriangle,
  Shield,
  Monitor,
  Wifi,
  WifiOff,
  Lock,
  Unlock,
  Download,
  Upload,
  Terminal,
  Activity,
  Clock,
  CheckCircle,
  XCircle,
  Play,
  Pause,
  Square,
  RefreshCw,
  Eye,
  EyeOff,
  Zap,
  Database,
  HardDrive
} from 'lucide-react'

interface TerminalControl {
  terminal_id: string
  terminal_name: string
  status: 'online' | 'offline' | 'maintenance' | 'locked' | 'error'
  last_seen: string
  version: string
  location: string
  ip_address: string
  uptime: number
  remote_access_enabled: boolean
  maintenance_mode: boolean
  emergency_lock: boolean
  pending_commands: RemoteCommand[]
}

interface RemoteCommand {
  id: string
  command_type: 'restart' | 'shutdown' | 'lock' | 'unlock' | 'maintenance' | 'deploy_config' | 'update_software' | 'clear_cache' | 'backup_data'
  terminal_id: string
  status: 'pending' | 'executing' | 'completed' | 'failed' | 'cancelled'
  created_at: string
  executed_at: string | null
  completed_at: string | null
  error_message: string | null
  created_by: string
  parameters: any
  priority: 'low' | 'normal' | 'high' | 'emergency'
}

interface EmergencyAction {
  id: string
  name: string
  description: string
  icon: any
  color: string
  confirmation_required: boolean
  admin_only: boolean
}

export default function RemotePOSControl() {
  const [terminals, setTerminals] = useState<TerminalControl[]>([])
  const [selectedTerminals, setSelectedTerminals] = useState<string[]>([])
  const [pendingCommands, setPendingCommands] = useState<RemoteCommand[]>([])
  const [showEmergencyPanel, setShowEmergencyPanel] = useState(false)
  const [showCommandHistory, setShowCommandHistory] = useState(false)
  const [confirmationModal, setConfirmationModal] = useState<{
    show: boolean
    action: string
    terminals: string[]
    callback: () => void
  }>({ show: false, action: '', terminals: [], callback: () => {} })
  const [loading, setLoading] = useState(true)
  const [executing, setExecuting] = useState(false)

  // Emergency actions configuration
  const emergencyActions: EmergencyAction[] = [
    {
      id: 'emergency_lock',
      name: 'Emergency Lock',
      description: 'Immediately lock all terminals and prevent transactions',
      icon: Lock,
      color: 'bg-red-600 hover:bg-red-700',
      confirmation_required: true,
      admin_only: true
    },
    {
      id: 'emergency_unlock',
      name: 'Emergency Unlock',
      description: 'Unlock all terminals and resume operations',
      icon: Unlock,
      color: 'bg-green-600 hover:bg-green-700',
      confirmation_required: true,
      admin_only: true
    },
    {
      id: 'force_restart_all',
      name: 'Force Restart All',
      description: 'Force restart all terminals (use with caution)',
      icon: RotateCcw,
      color: 'bg-orange-600 hover:bg-orange-700',
      confirmation_required: true,
      admin_only: true
    },
    {
      id: 'maintenance_mode_all',
      name: 'Maintenance Mode',
      description: 'Put all terminals in maintenance mode',
      icon: Settings,
      color: 'bg-yellow-600 hover:bg-yellow-700',
      confirmation_required: true,
      admin_only: false
    }
  ]

  useEffect(() => {
    loadData()
    
    // Set up real-time updates every 5 seconds
    const interval = setInterval(loadData, 5000)
    return () => clearInterval(interval)
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      await Promise.all([
        loadTerminals(),
        loadPendingCommands()
      ])
    } catch (error) {
      console.error('Failed to load remote control data:', error)
      toast.error('Failed to load remote control data')
    } finally {
      setLoading(false)
    }
  }

  const loadTerminals = async () => {
    try {
      const response = await fetch('/api/pos/remote-control/terminals')
      if (response.ok) {
        const data = await response.json()
        setTerminals(data.terminals || [])
      }
    } catch (error) {
      console.error('Failed to load terminals:', error)
    }
  }

  const loadPendingCommands = async () => {
    try {
      const response = await fetch('/api/pos/remote-control/commands?status=pending,executing')
      if (response.ok) {
        const data = await response.json()
        setPendingCommands(data.commands || [])
      }
    } catch (error) {
      console.error('Failed to load pending commands:', error)
    }
  }

  const executeCommand = async (
    commandType: string, 
    terminalIds: string[], 
    parameters: any = {},
    priority: string = 'normal'
  ) => {
    try {
      setExecuting(true)
      const response = await fetch('/api/pos/remote-control/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          command_type: commandType,
          terminal_ids: terminalIds,
          parameters,
          priority
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          toast.success(`Command ${commandType} executed successfully`)
          await loadData()
        } else {
          toast.error(`Failed to execute command: ${result.errors?.join(', ')}`)
        }
      } else {
        toast.error('Failed to execute remote command')
      }
    } catch (error) {
      console.error('Failed to execute command:', error)
      toast.error('Failed to execute remote command')
    } finally {
      setExecuting(false)
    }
  }

  const handleBulkAction = (action: string) => {
    if (selectedTerminals.length === 0) {
      toast.error('Please select at least one terminal')
      return
    }

    const terminalNames = terminals
      .filter(t => selectedTerminals.includes(t.terminal_id))
      .map(t => t.terminal_name)

    setConfirmationModal({
      show: true,
      action,
      terminals: terminalNames,
      callback: () => {
        executeCommand(action, selectedTerminals)
        setConfirmationModal({ show: false, action: '', terminals: [], callback: () => {} })
      }
    })
  }

  const handleEmergencyAction = (actionId: string) => {
    const action = emergencyActions.find(a => a.id === actionId)
    if (!action) return

    const allTerminalIds = terminals.map(t => t.terminal_id)
    const allTerminalNames = terminals.map(t => t.terminal_name)

    setConfirmationModal({
      show: true,
      action: action.name,
      terminals: allTerminalNames,
      callback: () => {
        executeCommand(actionId, allTerminalIds, {}, 'emergency')
        setConfirmationModal({ show: false, action: '', terminals: [], callback: () => {} })
        setShowEmergencyPanel(false)
      }
    })
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <Wifi className="w-4 h-4 text-green-500" />
      case 'offline':
        return <WifiOff className="w-4 h-4 text-red-500" />
      case 'maintenance':
        return <Settings className="w-4 h-4 text-yellow-500" />
      case 'locked':
        return <Lock className="w-4 h-4 text-red-500" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <Monitor className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300'
      case 'offline':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300'
      case 'maintenance':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300'
      case 'locked':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300'
      case 'error':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300'
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  const getCommandStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'executing':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'cancelled':
        return <Square className="w-4 h-4 text-gray-500" />
      default:
        return <Activity className="w-4 h-4 text-gray-500" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Remote POS Control</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Remotely control and manage POS terminals from the admin dashboard
          </p>
        </div>
        
        <div className="flex gap-2 mt-4 sm:mt-0">
          <button
            onClick={() => setShowCommandHistory(true)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Activity className="w-4 h-4" />
            Command History
          </button>
          
          <button
            onClick={() => setShowEmergencyPanel(true)}
            className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
          >
            <AlertTriangle className="w-4 h-4" />
            Emergency Controls
          </button>
        </div>
      </div>

      {/* Pending Commands Alert */}
      {pendingCommands.length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />
              <div>
                <h3 className="font-medium text-blue-800 dark:text-blue-200">
                  {pendingCommands.length} Command{pendingCommands.length > 1 ? 's' : ''} Executing
                </h3>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Remote commands are being processed by terminals
                </p>
              </div>
            </div>
            <button
              onClick={() => setShowCommandHistory(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              View Details
            </button>
          </div>
        </div>
      )}

      {/* Bulk Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Bulk Actions</h3>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {selectedTerminals.length} terminal{selectedTerminals.length !== 1 ? 's' : ''} selected
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
          <button
            onClick={() => handleBulkAction('restart')}
            disabled={selectedTerminals.length === 0 || executing}
            className="flex flex-col items-center gap-2 p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RotateCcw className="w-5 h-5 text-blue-600" />
            <span className="text-sm font-medium">Restart</span>
          </button>

          <button
            onClick={() => handleBulkAction('lock')}
            disabled={selectedTerminals.length === 0 || executing}
            className="flex flex-col items-center gap-2 p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Lock className="w-5 h-5 text-red-600" />
            <span className="text-sm font-medium">Lock</span>
          </button>

          <button
            onClick={() => handleBulkAction('unlock')}
            disabled={selectedTerminals.length === 0 || executing}
            className="flex flex-col items-center gap-2 p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Unlock className="w-5 h-5 text-green-600" />
            <span className="text-sm font-medium">Unlock</span>
          </button>

          <button
            onClick={() => handleBulkAction('maintenance')}
            disabled={selectedTerminals.length === 0 || executing}
            className="flex flex-col items-center gap-2 p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Settings className="w-5 h-5 text-yellow-600" />
            <span className="text-sm font-medium">Maintenance</span>
          </button>

          <button
            onClick={() => handleBulkAction('deploy_config')}
            disabled={selectedTerminals.length === 0 || executing}
            className="flex flex-col items-center gap-2 p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Download className="w-5 h-5 text-purple-600" />
            <span className="text-sm font-medium">Deploy Config</span>
          </button>

          <button
            onClick={() => handleBulkAction('clear_cache')}
            disabled={selectedTerminals.length === 0 || executing}
            className="flex flex-col items-center gap-2 p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RefreshCw className="w-5 h-5 text-gray-600" />
            <span className="text-sm font-medium">Clear Cache</span>
          </button>
        </div>
      </div>

      {/* Terminal Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {terminals.map((terminal) => (
          <div key={terminal.terminal_id} className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={selectedTerminals.includes(terminal.terminal_id)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedTerminals(prev => [...prev, terminal.terminal_id])
                    } else {
                      setSelectedTerminals(prev => prev.filter(id => id !== terminal.terminal_id))
                    }
                  }}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">{terminal.terminal_name}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{terminal.terminal_id}</p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {getStatusIcon(terminal.status)}
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(terminal.status)}`}>
                  {terminal.status}
                </span>
              </div>
            </div>

            <div className="space-y-2 mb-4 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Location:</span>
                <span className="font-medium text-gray-900 dark:text-white">{terminal.location}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">IP Address:</span>
                <span className="font-medium text-gray-900 dark:text-white">{terminal.ip_address}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Version:</span>
                <span className="font-medium text-gray-900 dark:text-white">{terminal.version}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Uptime:</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {Math.floor(terminal.uptime / 3600)}h {Math.floor((terminal.uptime % 3600) / 60)}m
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Last Seen:</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {new Date(terminal.last_seen).toLocaleTimeString()}
                </span>
              </div>
            </div>

            {terminal.emergency_lock && (
              <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-center gap-2">
                  <Shield className="w-4 h-4 text-red-600" />
                  <span className="text-sm font-medium text-red-700 dark:text-red-400">Emergency Lock Active</span>
                </div>
              </div>
            )}

            {terminal.maintenance_mode && (
              <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <div className="flex items-center gap-2">
                  <Settings className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-700 dark:text-yellow-400">Maintenance Mode</span>
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={() => executeCommand('restart', [terminal.terminal_id])}
                disabled={terminal.status === 'offline' || executing}
                className="flex items-center justify-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <RotateCcw className="w-4 h-4" />
                Restart
              </button>

              <button
                onClick={() => executeCommand(terminal.emergency_lock ? 'unlock' : 'lock', [terminal.terminal_id])}
                disabled={terminal.status === 'offline' || executing}
                className={`flex items-center justify-center gap-2 px-3 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed ${
                  terminal.emergency_lock
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : 'bg-red-600 text-white hover:bg-red-700'
                }`}
              >
                {terminal.emergency_lock ? <Unlock className="w-4 h-4" /> : <Lock className="w-4 h-4" />}
                {terminal.emergency_lock ? 'Unlock' : 'Lock'}
              </button>
            </div>
          </div>
        ))}
      </div>

      {terminals.length === 0 && (
        <div className="text-center py-12">
          <Terminal className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No terminals found</h3>
          <p className="text-gray-600 dark:text-gray-400">
            No POS terminals are currently registered for remote control.
          </p>
        </div>
      )}

      {/* Emergency Panel Modal */}
      {showEmergencyPanel && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <AlertTriangle className="w-6 h-6 text-red-600" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Emergency Controls</h3>
              </div>
              <button
                onClick={() => setShowEmergencyPanel(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>

            <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-sm text-red-700 dark:text-red-400">
                <strong>Warning:</strong> Emergency controls affect all terminals simultaneously.
                Use these functions only in critical situations that require immediate action.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {emergencyActions.map((action) => {
                const Icon = action.icon
                return (
                  <button
                    key={action.id}
                    onClick={() => handleEmergencyAction(action.id)}
                    disabled={executing}
                    className={`flex items-center gap-4 p-4 rounded-lg text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${action.color}`}
                  >
                    <Icon className="w-6 h-6" />
                    <div className="text-left">
                      <div className="font-medium">{action.name}</div>
                      <div className="text-sm opacity-90">{action.description}</div>
                    </div>
                  </button>
                )
              })}
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {confirmationModal.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <AlertTriangle className="w-6 h-6 text-orange-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Confirm Action</h3>
            </div>

            <div className="mb-6">
              <p className="text-gray-700 dark:text-gray-300 mb-3">
                Are you sure you want to execute <strong>{confirmationModal.action}</strong> on the following terminals?
              </p>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 max-h-32 overflow-y-auto">
                {confirmationModal.terminals.map((terminal, index) => (
                  <div key={index} className="text-sm text-gray-600 dark:text-gray-400">
                    • {terminal}
                  </div>
                ))}
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => setConfirmationModal({ show: false, action: '', terminals: [], callback: () => {} })}
                className="flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500"
              >
                Cancel
              </button>
              <button
                onClick={confirmationModal.callback}
                disabled={executing}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
              >
                {executing ? 'Executing...' : 'Confirm'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
