import React from 'react';
import { useTheme } from '../contexts/theme-context';

export const ThemeSwitcher: React.FC = () => {
  const { resolvedTheme, setTheme } = useTheme();

  const handleToggle = () => {
    setTheme(resolvedTheme === 'light' ? 'dark' : 'light');
  };

  return (
    <button
      onClick={handleToggle}
      className={`w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 ${
        resolvedTheme === 'light'
          ? 'text-gray-400 hover:text-yellow-400 hover:drop-shadow-[0_0_8px_rgba(234,179,8,0.8)]'
          : 'text-gray-500 hover:text-blue-400 hover:drop-shadow-[0_0_8px_rgba(59,130,246,0.8)]'
      }`}
      title={resolvedTheme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}
    >
      <span className="text-lg font-light">
        {resolvedTheme === 'light' ? '☀' : '☽'}
      </span>
    </button>
  );
}; 