import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';

interface PricingBreakdown {
  subtotal: number;
  deliveryFee: number;
  pickupDiscount: number;
  serviceFee: number;
  taxAmount: number;
  totalAmount: number;
  deliveryZone: any;
  estimatedTime: { min: number; max: number; message?: string } | null;
  [key: string]: number | any; // Allow dynamic access for numeric properties
}

// POST /api/pricing/calculate - Calculate order pricing based on type and location
export async function POST(request: NextRequest) {
  const supabase = createServerSupabaseClient();
  
  try {
    const body = await request.json();
    const { 
      orderType, // 'pickup' | 'delivery'
      subtotal,
      address, // { lat: number, lng: number } or string address
      branchId,
      customerTier = 'standard' // for future loyalty pricing
    } = body;

    // Validate required fields
    if (!orderType || !subtotal) {
      return NextResponse.json(
        { error: 'orderType and subtotal are required' },
        { status: 400 }
      );
    }

    // Initialize pricing breakdown
    const pricingBreakdown: PricingBreakdown = {
      subtotal: parseFloat(subtotal),
      deliveryFee: 0,
      pickupDiscount: 0,
      serviceFee: 0,
      taxAmount: 0,
      totalAmount: 0,
      deliveryZone: null,
      estimatedTime: null
    };

    // Calculate tax (10% - configurable later)
    const taxRate = 0.10;
    pricingBreakdown.taxAmount = pricingBreakdown.subtotal * taxRate;

    if (orderType === 'pickup') {
      // Get pickup discount from settings
      const { data: pickupSettings } = await supabase
        .from('restaurant_settings')
        .select('setting_value')
        .eq('setting_key', 'pickup_discount_percentage')
        .single();

      const discountRate = pickupSettings?.setting_value 
        ? parseFloat(pickupSettings.setting_value) 
        : 0.05; // Default 5% pickup discount

      pricingBreakdown.pickupDiscount = pricingBreakdown.subtotal * discountRate;
      pricingBreakdown.estimatedTime = {
        min: 10,
        max: 20,
        message: 'Order will be ready for pickup'
      };

    } else if (orderType === 'delivery') {
      // Delivery pricing requires address
      if (!address) {
        return NextResponse.json(
          { error: 'address is required for delivery orders' },
          { status: 400 }
        );
      }

      let lat: number, lng: number;

      // Handle coordinate object or address string
      if (typeof address === 'object' && address.lat && address.lng) {
        lat = address.lat;
        lng = address.lng;
      } else if (typeof address === 'string') {
        // For address strings, we'd need geocoding service (Google Maps API)
        // For now, return error asking for coordinates
        return NextResponse.json(
          { error: 'Please provide coordinates {lat, lng} for delivery address' },
          { status: 400 }
        );
      } else {
        return NextResponse.json(
          { error: 'Invalid address format' },
          { status: 400 }
        );
      }

      // Find delivery zone using existing database function
      const { data: zoneData, error: zoneError } = await supabase
        .rpc('find_delivery_zone_for_address', {
          address_lat: lat,
          address_lng: lng,
          target_branch_id: branchId || null
        });

      if (zoneError) {
        console.error('Error finding delivery zone:', zoneError);
        return NextResponse.json(
          { error: 'Failed to calculate delivery pricing' },
          { status: 500 }
        );
      }

      if (zoneData && zoneData.length > 0) {
        const zone = zoneData[0];
        pricingBreakdown.deliveryZone = zone;
        pricingBreakdown.deliveryFee = parseFloat(zone.delivery_fee);
        pricingBreakdown.estimatedTime = {
          min: zone.estimated_delivery_time_min,
          max: zone.estimated_delivery_time_max,
          message: `Delivery to ${zone.zone_name}`
        };

        // Check minimum order amount
        if (pricingBreakdown.subtotal < zone.minimum_order_amount) {
          return NextResponse.json({
            error: 'MINIMUM_ORDER_NOT_MET',
            minimumOrderAmount: zone.minimum_order_amount,
            currentAmount: pricingBreakdown.subtotal,
            message: `Minimum order amount for ${zone.zone_name} is €${zone.minimum_order_amount}`
          }, { status: 400 });
        }
      } else {
        // Address not in any delivery zone
        return NextResponse.json({
          error: 'DELIVERY_UNAVAILABLE',
          message: 'Delivery is not available to this address',
          suggestPickup: true
        }, { status: 400 });
      }
    }

    // Calculate final total
    pricingBreakdown.totalAmount = 
      pricingBreakdown.subtotal + 
      pricingBreakdown.deliveryFee + 
      pricingBreakdown.serviceFee + 
      pricingBreakdown.taxAmount - 
      pricingBreakdown.pickupDiscount;

    // Round to 2 decimal places
    (Object.keys(pricingBreakdown) as Array<keyof PricingBreakdown>).forEach(key => {
      const value = pricingBreakdown[key];
      if (typeof value === 'number') {
        (pricingBreakdown as any)[key] = Math.round(value * 100) / 100;
      }
    });

    return NextResponse.json({
      success: true,
      orderType,
      pricing: pricingBreakdown
    });

  } catch (error) {
    console.error('Pricing calculation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/pricing/calculate - Get pricing configuration
export async function GET(request: NextRequest) {
  const supabase = createServerSupabaseClient();
  
  try {
    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');

    // Get pricing configuration
    const [
      { data: pickupSettings },
      { data: deliveryZones },
      { data: taxSettings }
    ] = await Promise.all([
      supabase
        .from('restaurant_settings')
        .select('setting_key, setting_value')
        .in('setting_key', ['pickup_discount_percentage', 'tax_rate', 'service_fee_rate']),
      
      supabase
        .from('delivery_zones')
        .select('id, name, delivery_fee, minimum_order_amount, estimated_delivery_time_min, estimated_delivery_time_max, is_active')
        .eq('is_active', true)
        .eq('branch_id', branchId || null)
        .order('priority', { ascending: false }),
      
      supabase
        .from('restaurant_settings')
        .select('setting_value')
        .eq('setting_key', 'tax_rate')
        .single()
    ]);

    // Parse settings
    const settings: Record<string, number> = (pickupSettings || []).reduce((acc: Record<string, number>, setting: any) => {
      acc[setting.setting_key] = parseFloat(setting.setting_value) || 0;
      return acc;
    }, {});

    const pricingConfig = {
      pickup: {
        discountPercentage: settings.pickup_discount_percentage || 0.05,
        estimatedTimeMin: 10,
        estimatedTimeMax: 20
      },
      delivery: {
        zones: deliveryZones || [],
        defaultFee: 2.50 // Fallback delivery fee
      },
      general: {
        taxRate: settings.tax_rate || 0.10,
        serviceFeeRate: settings.service_fee_rate || 0,
        currency: 'EUR'
      }
    };

    return NextResponse.json({
      success: true,
      configuration: pricingConfig
    });

  } catch (error) {
    console.error('Error fetching pricing configuration:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}