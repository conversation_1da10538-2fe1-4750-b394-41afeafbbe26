'use client'

import React, { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { X, Plus, Save, Shield, Trash2, Edit } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface Role {
  id: string
  name: string
  display_name: string
  description?: string
  level: number
  color: string
  is_active: boolean
  is_system_role: boolean
}

interface Permission {
  id: string
  name: string
  display_name: string
  description?: string
  category: string
  resource?: string
  action?: string
}

interface RolePermission {
  role_id: string
  permission_id: string
}

interface RoleManagementModalProps {
  isOpen: boolean
  onClose: () => void
  onRoleUpdated: () => void
  selectedRole?: Role | null
}

interface RoleFormData {
  name: string
  display_name: string
  description: string
  level: number
  color: string
  permissions: string[]
}

const RoleManagementModal = ({
  isOpen,
  onClose,
  onRoleUpdated,
  selectedRole
}: RoleManagementModalProps) => {
  const [roles, setRoles] = useState<Role[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [rolePermissions, setRolePermissions] = useState<RolePermission[]>([])
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'list' | 'add' | 'edit'>('list')
  const [editingRole, setEditingRole] = useState<Role | null>(null)
  
  const [formData, setFormData] = useState<RoleFormData>({
    name: '',
    display_name: '',
    description: '',
    level: 5,
    color: '#6B7280',
    permissions: []
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const predefinedColors = [
    '#DC2626', '#EA580C', '#D97706', '#CA8A04', '#65A30D',
    '#059669', '#0891B2', '#0284C7', '#2563EB', '#7C3AED',
    '#9333EA', '#C026D3', '#DB2777', '#E11D48', '#6B7280'
  ]

  useEffect(() => {
    if (isOpen) {
      loadRoles()
      loadPermissions()
      loadRolePermissions()
    }
  }, [isOpen])

  useEffect(() => {
    if (selectedRole) {
      setEditingRole(selectedRole)
      setActiveTab('edit')
      populateFormWithRole(selectedRole)
    }
  }, [selectedRole])

  const loadRoles = async () => {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .order('level')

      if (error) {
        if (error.message?.includes('relation') || error.message?.includes('does not exist')) {
          setRoles([])
          return
        }
        throw error
      }
      setRoles(data || [])
    } catch (error) {
      console.error('Error loading roles:', error)
      setRoles([])
    }
  }

  const loadPermissions = async () => {
    try {
      console.log('Loading permissions...')
      const { data, error } = await supabase
        .from('permissions')
        .select('*')
        .order('category', { ascending: true })

      console.log('Permissions query result:', { data, error })

      if (error) {
        if (error.message?.includes('relation') || error.message?.includes('does not exist')) {
          console.log('Permissions table not found')
          setPermissions([])
          return
        }
        throw error
      }
      console.log('Loaded permissions:', data?.length || 0)
      setPermissions(data || [])
    } catch (error) {
      console.error('Error loading permissions:', error)
      setPermissions([])
    }
  }

  const loadRolePermissions = async () => {
    try {
      const { data, error } = await supabase
        .from('role_permissions')
        .select('role_id, permission_id')

      if (error) {
        if (error.message?.includes('relation') || error.message?.includes('does not exist')) {
          setRolePermissions([])
          return
        }
        throw error
      }
      setRolePermissions(data || [])
    } catch (error) {
      console.error('Error loading role permissions:', error)
      setRolePermissions([])
    }
  }

  const populateFormWithRole = async (role: Role) => {
    // Get permissions for this role
    const rolePerms = rolePermissions
      .filter(rp => rp.role_id === role.id)
      .map(rp => rp.permission_id)

    setFormData({
      name: role.name || '',
      display_name: role.display_name || '',
      description: role.description || '',
      level: role.level || 5,
      color: role.color || '#6B7280',
      permissions: rolePerms
    })
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    console.log('Validating form with data:', formData)
    console.log('Existing roles:', roles)

    if (!formData.name.trim()) newErrors.name = 'Role name is required'
    else if (!/^[a-z_]+$/.test(formData.name)) newErrors.name = 'Role name must be lowercase with underscores only'
    
    if (!formData.display_name.trim()) newErrors.display_name = 'Display name is required'
    
    if (isNaN(formData.level) || formData.level < 1 || formData.level > 10) newErrors.level = 'Level must be between 1 and 10'

    // Check for duplicate names (excluding current role when editing)
    const existingRole = roles.find(r => 
      r.name.toLowerCase() === formData.name.toLowerCase() && 
      (!editingRole || r.id !== editingRole.id)
    )
    if (existingRole) newErrors.name = 'Role name already exists'

    console.log('Validation errors:', newErrors)
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Test function to isolate the issue
  const testDatabaseConnection = async () => {
    try {
      console.log('Testing database connection...')
      const { data, error } = await supabase
        .from('roles')
        .select('count(*)')
        .single()
      
      console.log('Database test result:', { data, error })
      
      if (error) {
        toast.error(`Database connection failed: ${error.message}`)
      } else {
        toast.success('Database connection successful')
      }
    } catch (error) {
      console.error('Database test error:', error)
      toast.error('Database test failed')
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    console.log('Form submission started with data:', formData)
    
    if (!validateForm()) {
      toast.error('Please fix the errors before saving')
      return
    }

    setLoading(true)
    
    try {
      console.log('Step 1: Attempting to save role:', { editingRole: !!editingRole, formData })

      // Step 1: Create/Update the role
      let roleId: string
      
      if (editingRole) {
        console.log('Step 2: Updating existing role...')
        // Update existing role logic here
        roleId = editingRole.id
        toast.error('Update functionality not implemented yet')
        return
      } else {
        console.log('Step 2: Creating new role...')
        
        // Prepare insert data
        const insertData = {
          name: formData.name,
          display_name: formData.display_name,
          description: formData.description || null,
          level: Number(formData.level), // Ensure it's a number
          color: formData.color,
          is_system_role: false,
          is_active: true
        }
        
        console.log('Step 3: Insert data prepared:', insertData)
        
        // Validate insert data
        if (!insertData.name || !insertData.display_name) {
          throw new Error('Missing required fields: name or display_name')
        }
        
        if (isNaN(insertData.level) || insertData.level < 1 || insertData.level > 10) {
          throw new Error('Invalid level value')
        }
        
        console.log('Step 4: Calling Supabase insert...')
        
        const result = await supabase
          .from('roles')
          .insert(insertData)
          .select()
          .single()

        console.log('Step 5: Supabase result:', result)

        if (result.error) {
          console.error('Step 5 ERROR:', result.error)
          throw result.error
        }
        
        if (!result.data) {
          throw new Error('No data returned from insert')
        }
        
        roleId = result.data.id
        console.log('Step 6: Role created with ID:', roleId)
      }

      // Step 2: Handle permissions (skip for now to isolate the issue)
      console.log('Step 7: Skipping permissions for now...')
      
      console.log('Step 8: Success!')
      toast.success('Role created successfully')
      
      // Refresh the roles list
      await loadRoles()
      
      onRoleUpdated()
      resetForm()
      setActiveTab('list')
      
    } catch (error) {
      console.error('Error saving role:', error)
      const errorMessage = (error as any)?.message || ''
      
      if (errorMessage.includes('relation') || errorMessage.includes('does not exist')) {
        toast.error('Database tables not found. Please run the staff management migration first.')
      } else if (errorMessage.includes('duplicate key')) {
        toast.error('A role with this name already exists')
      } else if (errorMessage.includes('permission')) {
        toast.error('Error assigning permissions to role')
      } else {
        toast.error(`Failed to save role: ${errorMessage || 'Unknown error'}`)
      }
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteRole = async (role: Role) => {
    if (role.is_system_role) {
      toast.error('Cannot delete system roles')
      return
    }

    if (!confirm(`Are you sure you want to delete the role "${role.display_name}"?`)) {
      return
    }

    try {
      setLoading(true)

      // Delete role permissions first
      await supabase
        .from('role_permissions')
        .delete()
        .eq('role_id', role.id)

      // Delete the role
      const { error } = await supabase
        .from('roles')
        .delete()
        .eq('id', role.id)

      if (error) throw error

      toast.success('Role deleted successfully')
      loadRoles()
      
    } catch (error) {
      console.error('Error deleting role:', error)
      const errorMessage = (error as any)?.message || ''
      
      if (errorMessage.includes('relation') || errorMessage.includes('does not exist')) {
        toast.error('Database tables not found. Please run the staff management migration first.')
      } else {
        toast.error(`Failed to delete role: ${errorMessage || 'Unknown error'}`)
      }
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      display_name: '',
      description: '',
      level: 5,
      color: '#6B7280',
      permissions: []
    })
    setErrors({})
    setEditingRole(null)
  }

  const handleInputChange = (field: keyof RoleFormData, value: any) => {
    // Handle numeric fields properly
    if (field === 'level') {
      const numValue = parseInt(value)
      value = isNaN(numValue) ? 5 : numValue // Default to 5 if NaN
    }
    
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const togglePermission = (permissionId: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permissionId)
        ? prev.permissions.filter(id => id !== permissionId)
        : [...prev.permissions, permissionId]
    }))
  }

  const groupedPermissions = permissions.reduce<Record<string, Permission[]>>((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = []
    }
    acc[permission.category].push(permission)
    return acc
  }, {})

  const getRolePermissions = (roleId: string) => {
    return rolePermissions
      .filter(rp => rp.role_id === roleId)
      .map(rp => rp.permission_id)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="backdrop-blur-md bg-white/10 border border-white/20 rounded-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/20">
          <h2 className="text-2xl font-bold text-white">Role Management</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg bg-white/10 text-white/60 hover:bg-white/20 border border-white/20 transition-all duration-200"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-white/20 bg-white/5">
          <button
            onClick={() => { setActiveTab('list'); resetForm() }}
            className={`px-6 py-3 font-medium transition-all duration-200 ${
              activeTab === 'list'
                ? 'text-white border-b-2 border-blue-400 bg-white/10'
                : 'text-white/70 hover:text-white hover:bg-white/5'
            }`}
          >
            All Roles
          </button>
          <button
            onClick={() => { setActiveTab('add'); resetForm() }}
            className={`px-6 py-3 font-medium transition-all duration-200 ${
              activeTab === 'add'
                ? 'text-white border-b-2 border-blue-400 bg-white/10'
                : 'text-white/70 hover:text-white hover:bg-white/5'
            }`}
          >
            Add New Role
          </button>
          {editingRole && (
            <button
              onClick={() => setActiveTab('edit')}
              className={`px-6 py-3 font-medium transition-all duration-200 ${
                activeTab === 'edit'
                  ? 'text-white border-b-2 border-blue-400 bg-white/10'
                  : 'text-white/70 hover:text-white hover:bg-white/5'
              }`}
            >
              Edit Role
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {activeTab === 'list' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-white">System Roles ({roles.length})</h3>
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      console.log('Current roles state:', roles)
                      loadRoles()
                    }}
                    className="inline-flex items-center px-3 py-2 backdrop-blur-md bg-green-500/20 border border-green-400/30 rounded-xl text-sm font-medium text-white hover:bg-green-500/30 transition-all duration-200"
                  >
                    Refresh
                  </button>
                  <button
                    onClick={() => setActiveTab('add')}
                    className="inline-flex items-center px-4 py-2 backdrop-blur-md bg-blue-500/20 border border-blue-400/30 rounded-xl text-sm font-medium text-white hover:bg-blue-500/30 transition-all duration-200"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Role
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {roles.map((role) => {
                  const rolePerms = getRolePermissions(role.id)
                  return (
                    <div key={role.id} className="backdrop-blur-md bg-white/5 border border-white/10 rounded-xl p-4 hover:bg-white/10 transition-all duration-200">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div 
                            className="w-4 h-4 rounded-full border border-white/30"
                            style={{ backgroundColor: role.color }}
                          />
                          <h4 className="font-medium text-white">{role.display_name}</h4>
                        </div>
                        <span className="text-xs text-white/60 bg-white/10 px-2 py-1 rounded-lg">
                          Level {role.level}
                        </span>
                      </div>
                      
                      {role.description && (
                        <p className="text-sm text-white/70 mb-3">{role.description}</p>
                      )}
                      
                      <div className="text-xs text-white/60 mb-3">
                        {rolePerms.length} permissions assigned
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          role.is_system_role 
                            ? 'bg-purple-500/20 text-purple-300 border border-purple-400/30'
                            : 'bg-green-500/20 text-green-300 border border-green-400/30'
                        }`}>
                          {role.is_system_role ? 'System' : 'Custom'}
                        </span>
                        
                        <div className="flex space-x-1">
                          <button
                            onClick={() => {
                              setEditingRole(role)
                              setActiveTab('edit')
                              populateFormWithRole(role)
                            }}
                            className="p-1 rounded bg-blue-500/20 text-blue-300 hover:bg-blue-500/30 border border-blue-400/30 transition-all duration-200"
                          >
                            <Edit className="w-3 h-3" />
                          </button>
                          {!role.is_system_role && (
                            <button
                              onClick={() => handleDeleteRole(role)}
                              className="p-1 rounded bg-red-500/20 text-red-300 hover:bg-red-500/30 border border-red-400/30 transition-all duration-200"
                            >
                              <Trash2 className="w-3 h-3" />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          {(activeTab === 'add' || activeTab === 'edit') && (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-white">Basic Information</h3>
                  
                  <div>
                    <label className="block text-sm font-medium text-white/70 mb-2">
                      Role Name (Internal)
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value.toLowerCase().replace(/[^a-z_]/g, ''))}
                      placeholder="e.g., senior_manager"
                      className="w-full px-4 py-3 backdrop-blur-md bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-blue-400/50 focus:border-blue-400/50 transition-all duration-200"
                    />
                    {errors.name && <p className="text-red-300 text-sm mt-1">{errors.name}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white/70 mb-2">
                      Display Name
                    </label>
                    <input
                      type="text"
                      value={formData.display_name}
                      onChange={(e) => handleInputChange('display_name', e.target.value)}
                      placeholder="e.g., Senior Manager"
                      className="w-full px-4 py-3 backdrop-blur-md bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-blue-400/50 focus:border-blue-400/50 transition-all duration-200"
                    />
                    {errors.display_name && <p className="text-red-300 text-sm mt-1">{errors.display_name}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white/70 mb-2">
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Describe the role responsibilities..."
                      rows={3}
                      className="w-full px-4 py-3 backdrop-blur-md bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-blue-400/50 focus:border-blue-400/50 transition-all duration-200"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white/70 mb-2">
                      Hierarchy Level (1 = Highest, 10 = Lowest)
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="10"
                      value={formData.level || 5}
                      onChange={(e) => handleInputChange('level', e.target.value)}
                      className="w-full px-4 py-3 backdrop-blur-md bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:ring-2 focus:ring-blue-400/50 focus:border-blue-400/50 transition-all duration-200"
                    />
                    {errors.level && <p className="text-red-300 text-sm mt-1">{errors.level}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white/70 mb-2">
                      Role Color
                    </label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {predefinedColors.map((color) => (
                        <button
                          key={color}
                          type="button"
                          onClick={() => handleInputChange('color', color)}
                          className={`w-8 h-8 rounded-lg border-2 transition-all duration-200 ${
                            formData.color === color 
                              ? 'border-white scale-110' 
                              : 'border-white/30 hover:border-white/60'
                          }`}
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    <input
                      type="color"
                      value={formData.color}
                      onChange={(e) => handleInputChange('color', e.target.value)}
                      className="w-full h-12 backdrop-blur-md bg-white/10 border border-white/20 rounded-xl"
                    />
                  </div>
                </div>

                {/* Permissions */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-white">Permissions</h3>
                  
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {Object.entries(groupedPermissions).map(([category, categoryPermissions]) => (
                      <div key={category} className="backdrop-blur-md bg-white/5 border border-white/10 rounded-xl p-4">
                        <h4 className="font-medium text-white capitalize mb-3">{category}</h4>
                        <div className="space-y-2">
                          {categoryPermissions.map((permission) => (
                            <label key={permission.id} className="flex items-center space-x-3 cursor-pointer">
                              <input
                                type="checkbox"
                                checked={formData.permissions.includes(permission.id)}
                                onChange={() => togglePermission(permission.id)}
                                className="w-4 h-4 text-blue-600 bg-white/10 border-white/30 rounded focus:ring-blue-500 focus:ring-2"
                              />
                              <div className="flex-1">
                                <div className="text-sm font-medium text-white">
                                  {permission.display_name}
                                </div>
                                <div className="text-xs text-white/60">
                                  {permission.name}
                                </div>
                              </div>
                            </label>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-white/20">
                <button
                  type="button"
                  onClick={() => { setActiveTab('list'); resetForm() }}
                  className="px-6 py-3 backdrop-blur-md bg-white/10 border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-200"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={testDatabaseConnection}
                  className="px-6 py-3 backdrop-blur-md bg-yellow-500/20 border border-yellow-400/30 rounded-xl text-white hover:bg-yellow-500/30 transition-all duration-200"
                >
                  Test DB
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="inline-flex items-center px-6 py-3 backdrop-blur-md bg-blue-500/20 border border-blue-400/30 rounded-xl text-white hover:bg-blue-500/30 transition-all duration-200 disabled:opacity-50"
                >
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  ) : (
                    <Save className="w-4 h-4 mr-2" />
                  )}
                  {editingRole ? 'Update Role' : 'Create Role'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  )
}

export default RoleManagementModal 