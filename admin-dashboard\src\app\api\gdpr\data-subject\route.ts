import { NextRequest, NextResponse } from 'next/server'
import { gdprComplianceService, DataSubjectRightType } from '@/services/gdpr-compliance-service'

export async function POST(request: NextRequest) {
  try {
    const { action, ...data } = await request.json()

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'register':
        const { email, personalData, initialConsent } = data
        if (!email) {
          return NextResponse.json(
            { error: 'Email is required for registration' },
            { status: 400 }
          )
        }

        const subjectId = await gdprComplianceService.registerDataSubject(
          email,
          personalData || {},
          initialConsent || {}
        )

        return NextResponse.json({
          success: true,
          subjectId,
          message: 'Data subject registered successfully'
        })

      case 'update-consent':
        const { subjectId: updateSubjectId, consentType, granted, method } = data
        if (!updateSubjectId || !consentType || granted === undefined) {
          return NextResponse.json(
            { error: 'Subject ID, consent type, and granted status are required' },
            { status: 400 }
          )
        }

        await gdprComplianceService.updateConsent(
          updateSubjectId,
          consentType,
          granted,
          method || 'explicit'
        )

        return NextResponse.json({
          success: true,
          message: 'Consent updated successfully'
        })

      case 'rights-request':
        const { email: requestEmail, requestType, requestDetails, verificationMethod } = data
        if (!requestEmail || !requestType || !requestDetails) {
          return NextResponse.json(
            { error: 'Email, request type, and request details are required' },
            { status: 400 }
          )
        }

        const requestId = await gdprComplianceService.processDataSubjectRequest(
          requestEmail,
          requestType as DataSubjectRightType,
          requestDetails,
          verificationMethod || 'email_verification'
        )

        return NextResponse.json({
          success: true,
          requestId,
          message: 'Data subject rights request submitted successfully'
        })

      case 'export-data':
        const { subjectId: exportSubjectId } = data
        if (!exportSubjectId) {
          return NextResponse.json(
            { error: 'Subject ID is required for data export' },
            { status: 400 }
          )
        }

        const exportedData = await gdprComplianceService.exportSubjectData(exportSubjectId)

        return NextResponse.json({
          success: true,
          data: exportedData,
          message: 'Data exported successfully'
        })

      case 'erase-data':
        const { subjectId: eraseSubjectId, reason } = data
        if (!eraseSubjectId) {
          return NextResponse.json(
            { error: 'Subject ID is required for data erasure' },
            { status: 400 }
          )
        }

        await gdprComplianceService.eraseSubjectData(
          eraseSubjectId,
          reason || 'data_subject_request'
        )

        return NextResponse.json({
          success: true,
          message: 'Data erased successfully'
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('GDPR operation failed:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'GDPR operation failed' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'compliance-report') {
      const startDate = searchParams.get('startDate')
      const endDate = searchParams.get('endDate')

      if (!startDate || !endDate) {
        return NextResponse.json(
          { error: 'Start date and end date are required for compliance report' },
          { status: 400 }
        )
      }

      const report = await gdprComplianceService.generateComplianceReport({
        start: new Date(startDate),
        end: new Date(endDate)
      })

      return NextResponse.json(report)
    }

    return NextResponse.json(
      { error: 'Invalid action or missing parameters' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Failed to process GDPR request:', error)
    return NextResponse.json(
      { error: 'Failed to process GDPR request' },
      { status: 500 }
    )
  }
} 