/**
 * Data Protection Module
 * Implements comprehensive data protection measures including:
 * - Encryption at rest and in transit
 * - HTTPS enforcement
 * - PII data handling and anonymization
 * - GDPR compliance utilities
 * - Data retention and deletion
 */

import crypto from 'crypto';
import { createClient } from '@supabase/supabase-js';
import { SECURITY_CONFIG } from './security-config';
import { logSecurityEvent, logAuditEvent } from './security-middleware';

// Types
interface EncryptedData {
  data: string;
  iv: string;
  tag: string;
  algorithm: string;
  keyVersion: number;
}

interface PIIField {
  fieldName: string;
  dataType: 'email' | 'phone' | 'name' | 'address' | 'payment' | 'custom';
  encryptionRequired: boolean;
  anonymizationMethod?: 'hash' | 'mask' | 'remove' | 'pseudonymize';
}

interface GDPRRequest {
  id: string;
  userId: string;
  requestType: 'access' | 'rectification' | 'erasure' | 'portability' | 'restriction' | 'objection';
  status: 'pending' | 'in_progress' | 'completed' | 'rejected';
  requestedBy: string;
  verificationMethod: string;
  requestDetails: any;
  responseData?: any;
  processedBy?: string;
  legalBasis?: string;
  retentionPeriod?: number;
}

interface DataRetentionPolicy {
  tableName: string;
  dataCategory: string;
  retentionPeriodDays: number;
  legalBasis: string;
  autoDelete: boolean;
  anonymizeInstead: boolean;
}

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * Encryption Manager for Data at Rest
 */
export class EncryptionManager {
  private static readonly ALGORITHM = 'aes-256-gcm';
  private static readonly KEY_LENGTH = 32;
  private static readonly IV_LENGTH = 16;
  private static readonly TAG_LENGTH = 16;

  /**
   * Encrypts sensitive data using AES-256-GCM
   */
  static async encryptData(data: string, keyName: string = 'default'): Promise<EncryptedData> {
    try {
      const key = await this.getEncryptionKey(keyName);
      const iv = crypto.randomBytes(this.IV_LENGTH);
      
      const cipher = crypto.createCipher(this.ALGORITHM, key);
      cipher.setAAD(Buffer.from(keyName)); // Additional authenticated data
      
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();
      
      const result: EncryptedData = {
        data: encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex'),
        algorithm: this.ALGORITHM,
        keyVersion: await this.getKeyVersion(keyName)
      };
      
      await logSecurityEvent('data_encrypted', 'low', {
        keyName,
        dataLength: data.length,
        algorithm: this.ALGORITHM
      });
      
      return result;
    } catch (error) {
      await logSecurityEvent('encryption_failed', 'high', {
        keyName,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypts data using AES-256-GCM
   */
  static async decryptData(encryptedData: EncryptedData, keyName: string = 'default'): Promise<string> {
    try {
      const key = await this.getEncryptionKey(keyName, encryptedData.keyVersion);
      
      const decipher = crypto.createDecipheriv(encryptedData.algorithm, key, Buffer.from(encryptedData.iv, 'hex'));
      if (encryptedData.tag) {
        (decipher as any).setAuthTag(Buffer.from(encryptedData.tag, 'hex'));
      }
      
      let decrypted = decipher.update(encryptedData.data, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      await logSecurityEvent('decryption_failed', 'high', {
        keyName,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Decryption failed');
    }
  }

  /**
   * Encrypts PII data with specific handling
   */
  static async encryptPII(data: string, fieldType: string): Promise<string> {
    const keyName = `pii_${fieldType}`;
    const encrypted = await this.encryptData(data, keyName);
    return JSON.stringify(encrypted);
  }

  /**
   * Decrypts PII data
   */
  static async decryptPII(encryptedData: string, fieldType: string): Promise<string> {
    const keyName = `pii_${fieldType}`;
    const parsed = JSON.parse(encryptedData) as EncryptedData;
    return this.decryptData(parsed, keyName);
  }

  /**
   * Rotates encryption keys
   */
  static async rotateKey(keyName: string): Promise<void> {
    try {
      const newKey = crypto.randomBytes(this.KEY_LENGTH);
      const newVersion = await this.getKeyVersion(keyName) + 1;
      
      // Store new key version
      await supabase
        .from('encryption_keys')
        .insert({
          key_name: keyName,
          key_version: newVersion,
          encrypted_key: this.encryptKeyWithMaster(newKey),
          algorithm: this.ALGORITHM,
          purpose: this.getKeyPurpose(keyName),
          is_active: true
        });
      
      // Deactivate old key
      await supabase
        .from('encryption_keys')
        .update({ is_active: false })
        .eq('key_name', keyName)
        .lt('key_version', newVersion);
      
      await logSecurityEvent('key_rotated', 'medium', {
        keyName,
        newVersion,
        algorithm: this.ALGORITHM
      });
    } catch (error) {
      await logSecurityEvent('key_rotation_failed', 'critical', {
        keyName,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private static async getEncryptionKey(keyName: string, version?: number): Promise<Buffer> {
    const query = supabase
      .from('encryption_keys')
      .select('encrypted_key')
      .eq('key_name', keyName)
      .eq('is_active', true);
    
    if (version) {
      query.eq('key_version', version);
    } else {
      query.order('key_version', { ascending: false }).limit(1);
    }
    
    const { data } = await query.single();
    
    if (!data) {
      throw new Error(`Encryption key not found: ${keyName}`);
    }
    
    return this.decryptKeyWithMaster(data.encrypted_key);
  }

  private static async getKeyVersion(keyName: string): Promise<number> {
    const { data } = await supabase
      .from('encryption_keys')
      .select('key_version')
      .eq('key_name', keyName)
      .order('key_version', { ascending: false })
      .limit(1)
      .single();
    
    return data?.key_version || 0;
  }

  private static encryptKeyWithMaster(key: Buffer): string {
    const masterKey = crypto.scryptSync(process.env.MASTER_ENCRYPTION_KEY!, 'salt', 32);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-cbc', masterKey, iv);
    let encrypted = cipher.update(key);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return iv.toString('hex') + ':' + encrypted.toString('hex');
  }

  private static decryptKeyWithMaster(encryptedKey: string): Buffer {
    const masterKey = crypto.scryptSync(process.env.MASTER_ENCRYPTION_KEY!, 'salt', 32);
    const [ivHex, encryptedHex] = encryptedKey.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const encrypted = Buffer.from(encryptedHex, 'hex');
    const decipher = crypto.createDecipheriv('aes-256-cbc', masterKey, iv);
    let decrypted = decipher.update(encrypted);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    return decrypted;
  }

  private static getKeyPurpose(keyName: string): string {
    if (keyName.startsWith('pii_')) return 'pii_data';
    if (keyName.startsWith('payment_')) return 'payment_data';
    return 'user_data';
  }
}

/**
 * HTTPS Enforcement Utilities
 */
export class HTTPSEnforcement {
  /**
   * Middleware to enforce HTTPS
   */
  static enforceHTTPS() {
    return (req: any, res: any, next: any) => {
      if (process.env.NODE_ENV === 'production') {
        if (!req.secure && req.get('x-forwarded-proto') !== 'https') {
          const httpsUrl = `https://${req.get('host')}${req.url}`;
          return res.redirect(301, httpsUrl);
        }
      }
      next();
    };
  }

  /**
   * Sets security headers for HTTPS
   */
  static setSecurityHeaders() {
    return (req: any, res: any, next: any) => {
      // Strict Transport Security
      res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
      
      // Content Security Policy
      res.setHeader('Content-Security-Policy', SECURITY_CONFIG.HEADERS.CSP);
      
      // Other security headers
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('X-XSS-Protection', '1; mode=block');
      res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
      res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
      
      next();
    };
  }

  /**
   * Validates SSL certificate
   */
  static async validateSSLCertificate(domain: string): Promise<boolean> {
    try {
      const https = require('https');
      const options = {
        hostname: domain,
        port: 443,
        path: '/',
        method: 'HEAD',
        timeout: 5000
      };

      return new Promise((resolve) => {
        const req = https.request(options, (res: any) => {
          resolve(res.statusCode === 200 || res.statusCode === 301 || res.statusCode === 302);
        });

        req.on('error', () => resolve(false));
        req.on('timeout', () => resolve(false));
        req.end();
      });
    } catch (error) {
      return false;
    }
  }
}

/**
 * PII Data Handler
 */
export class PIIDataHandler {
  private static readonly PII_FIELDS: PIIField[] = [
    { fieldName: 'email', dataType: 'email', encryptionRequired: true, anonymizationMethod: 'hash' },
    { fieldName: 'phone', dataType: 'phone', encryptionRequired: true, anonymizationMethod: 'mask' },
    { fieldName: 'first_name', dataType: 'name', encryptionRequired: true, anonymizationMethod: 'pseudonymize' },
    { fieldName: 'last_name', dataType: 'name', encryptionRequired: true, anonymizationMethod: 'pseudonymize' },
    { fieldName: 'address', dataType: 'address', encryptionRequired: true, anonymizationMethod: 'remove' },
    { fieldName: 'card_number', dataType: 'payment', encryptionRequired: true, anonymizationMethod: 'remove' },
    { fieldName: 'card_cvv', dataType: 'payment', encryptionRequired: true, anonymizationMethod: 'remove' }
  ];

  /**
   * Identifies PII fields in data
   */
  static identifyPIIFields(data: Record<string, any>): string[] {
    const piiFields: string[] = [];
    
    for (const [key, value] of Object.entries(data)) {
      if (this.isPIIField(key, value)) {
        piiFields.push(key);
      }
    }
    
    return piiFields;
  }

  /**
   * Encrypts PII fields in data object
   */
  static async encryptPIIFields(data: Record<string, any>): Promise<Record<string, any>> {
    const result = { ...data };
    
    for (const field of this.PII_FIELDS) {
      if (result[field.fieldName] && field.encryptionRequired) {
        result[field.fieldName] = await EncryptionManager.encryptPII(
          result[field.fieldName],
          field.dataType
        );
      }
    }
    
    return result;
  }

  /**
   * Decrypts PII fields in data object
   */
  static async decryptPIIFields(data: Record<string, any>): Promise<Record<string, any>> {
    const result = { ...data };
    
    for (const field of this.PII_FIELDS) {
      if (result[field.fieldName] && field.encryptionRequired) {
        try {
          result[field.fieldName] = await EncryptionManager.decryptPII(
            result[field.fieldName],
            field.dataType
          );
        } catch (error) {
          // Log decryption failure but don't throw
          await logSecurityEvent('pii_decryption_failed', 'high', {
            fieldName: field.fieldName,
            dataType: field.dataType,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }
    
    return result;
  }

  /**
   * Anonymizes PII data
   */
  static anonymizePIIFields(data: Record<string, any>): Record<string, any> {
    const result = { ...data };
    
    for (const field of this.PII_FIELDS) {
      if (result[field.fieldName] && field.anonymizationMethod) {
        result[field.fieldName] = this.anonymizeValue(
          result[field.fieldName],
          field.anonymizationMethod
        );
      }
    }
    
    return result;
  }

  /**
   * Masks sensitive data for display
   */
  static maskSensitiveData(data: Record<string, any>): Record<string, any> {
    const result = { ...data };
    
    // Email masking
    if (result.email) {
      const [local, domain] = result.email.split('@');
      result.email = `${local.charAt(0)}***@${domain}`;
    }
    
    // Phone masking
    if (result.phone) {
      result.phone = result.phone.replace(/.(?=.{4})/g, '*');
    }
    
    // Card number masking
    if (result.card_number) {
      result.card_number = `****-****-****-${result.card_number.slice(-4)}`;
    }
    
    return result;
  }

  private static isPIIField(fieldName: string, value: any): boolean {
    // Check if field is in known PII fields
    if (this.PII_FIELDS.some(field => field.fieldName === fieldName)) {
      return true;
    }
    
    // Pattern-based detection
    if (typeof value === 'string') {
      // Email pattern
      if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) return true;
      
      // Phone pattern
      if (/^[+]?[1-9]?[0-9]{7,15}$/.test(value.replace(/\s/g, ''))) return true;
      
      // Credit card pattern
      if (/^[0-9]{13,19}$/.test(value.replace(/\s/g, ''))) return true;
    }
    
    return false;
  }

  private static anonymizeValue(value: string, method: string): string {
    switch (method) {
      case 'hash':
        return crypto.createHash('sha256').update(value).digest('hex');
      case 'mask':
        return value.replace(/.(?=.{4})/g, '*');
      case 'remove':
        return '[REMOVED]';
      case 'pseudonymize':
        return `ANON_${crypto.createHash('md5').update(value).digest('hex').substring(0, 8)}`;
      default:
        return value;
    }
  }
}

/**
 * GDPR Compliance Manager
 */
export class GDPRCompliance {
  /**
   * Processes GDPR data subject request
   */
  static async processGDPRRequest(request: Partial<GDPRRequest>): Promise<string> {
    try {
      const { data } = await supabase
        .from('gdpr_requests')
        .insert({
          user_id: request.userId,
          request_type: request.requestType,
          requested_by: request.requestedBy,
          verification_method: request.verificationMethod,
          request_details: request.requestDetails,
          legal_basis: request.legalBasis,
          retention_period: request.retentionPeriod
        })
        .select('id')
        .single();
      
      if (data) {
        await logAuditEvent('gdpr_request_created', 'gdpr_requests', data.id, {
          requestType: request.requestType,
          requestedBy: request.requestedBy
        });
        
        return data.id;
      } else {
        throw new Error('Failed to create GDPR request');
      }
    } catch (error) {
      await logSecurityEvent('gdpr_request_failed', 'high', {
        requestType: request.requestType,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Exports user data for GDPR access request
   */
  static async exportUserData(userId: string): Promise<Record<string, any>> {
    try {
      const userData: Record<string, any> = {};
      
      // Get user profile
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();
      
      if (profile) {
        userData.profile = await PIIDataHandler.decryptPIIFields(profile);
      }
      
      // Get orders
      const { data: orders } = await supabase
        .from('orders')
        .select('*')
        .eq('user_id', userId);
      
      userData.orders = orders || [];
      
      // Get payments (without sensitive card data)
      const { data: payments } = await supabase
        .from('payments')
        .select('id, amount, currency, status, created_at')
        .eq('user_id', userId);
      
      userData.payments = payments || [];
      
      // Get preferences
      const { data: preferences } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId);
      
      userData.preferences = preferences || [];
      
      await logAuditEvent('gdpr_data_exported', 'user_profiles', userId, {
        dataCategories: Object.keys(userData),
        recordCount: Object.values(userData).flat().length
      });
      
      return userData;
    } catch (error) {
      await logSecurityEvent('gdpr_export_failed', 'high', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Deletes user data for GDPR erasure request
   */
  static async deleteUserData(userId: string, retainLegal: boolean = true): Promise<void> {
    try {
      const deletedData: Record<string, number> = {};
      
      if (!retainLegal) {
        // Delete user profile
        const { count: profileCount } = await supabase
          .from('user_profiles')
          .delete()
          .eq('id', userId);
        deletedData.profiles = profileCount || 0;
        
        // Delete preferences
        const { count: prefCount } = await supabase
          .from('user_preferences')
          .delete()
          .eq('user_id', userId);
        deletedData.preferences = prefCount || 0;
      } else {
        // Anonymize instead of delete for legal retention
        await this.anonymizeUserData(userId);
      }
      
      // Always delete authentication data
      const { count: sessionCount } = await supabase
        .from('user_sessions')
        .delete()
        .eq('user_id', userId);
      deletedData.sessions = sessionCount || 0;
      
      await logAuditEvent('gdpr_data_deleted', 'user_profiles', userId, {
        retainLegal,
        deletedCounts: deletedData
      });
    } catch (error) {
      await logSecurityEvent('gdpr_deletion_failed', 'high', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Anonymizes user data while retaining for legal purposes
   */
  static async anonymizeUserData(userId: string): Promise<void> {
    try {
      // Get current user data
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();
      
      if (profile) {
        const anonymizedData = PIIDataHandler.anonymizePIIFields(profile);
        
        await supabase
          .from('user_profiles')
          .update({
            ...anonymizedData,
            anonymized_at: new Date().toISOString(),
            anonymization_reason: 'gdpr_erasure_request'
          })
          .eq('id', userId);
      }
      
      await logAuditEvent('user_data_anonymized', 'user_profiles', userId, {
        reason: 'gdpr_erasure_request'
      });
    } catch (error) {
      await logSecurityEvent('anonymization_failed', 'high', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Applies data retention policies
   */
  static async applyRetentionPolicies(): Promise<void> {
    try {
      const { data: policies } = await supabase
        .from('data_retention_policies')
        .select('*')
        .eq('is_active', true);
      
      if (!policies) return;
      
      for (const policy of policies) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - policy.retention_period_days);
        
        if (policy.auto_delete) {
          if (policy.anonymize_instead) {
            // Anonymize old data
            await this.anonymizeOldData(policy.table_name, cutoffDate);
          } else {
            // Delete old data
            await this.deleteOldData(policy.table_name, cutoffDate);
          }
        }
      }
      
      await logAuditEvent('retention_policies_applied', 'data_retention_policies', null, {
        policiesProcessed: policies.length,
        cutoffDate: new Date()
      });
    } catch (error) {
      await logSecurityEvent('retention_policy_failed', 'high', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private static async anonymizeOldData(tableName: string, cutoffDate: Date): Promise<void> {
    // Implementation would depend on specific table structure
    // This is a simplified version
    await logAuditEvent('old_data_anonymized', tableName, null, {
      cutoffDate,
      tableName
    });
  }

  private static async deleteOldData(tableName: string, cutoffDate: Date): Promise<void> {
    // Implementation would depend on specific table structure
    // This is a simplified version
    await logAuditEvent('old_data_deleted', tableName, null, {
      cutoffDate,
      tableName
    });
  }
}

/**
 * Data Classification and Labeling
 */
export class DataClassification {
  private static readonly CLASSIFICATION_LEVELS = {
    PUBLIC: 1,
    INTERNAL: 2,
    CONFIDENTIAL: 3,
    RESTRICTED: 4
  };

  /**
   * Classifies data based on content and context
   */
  static classifyData(data: Record<string, any>, context: string): number {
    let maxLevel = this.CLASSIFICATION_LEVELS.PUBLIC;
    
    // Check for PII
    const piiFields = PIIDataHandler.identifyPIIFields(data);
    if (piiFields.length > 0) {
      maxLevel = Math.max(maxLevel, this.CLASSIFICATION_LEVELS.CONFIDENTIAL);
    }
    
    // Check for payment data
    if (this.containsPaymentData(data)) {
      maxLevel = Math.max(maxLevel, this.CLASSIFICATION_LEVELS.RESTRICTED);
    }
    
    // Context-based classification
    if (context.includes('admin') || context.includes('internal')) {
      maxLevel = Math.max(maxLevel, this.CLASSIFICATION_LEVELS.INTERNAL);
    }
    
    return maxLevel;
  }

  /**
   * Gets handling requirements for data classification level
   */
  static getHandlingRequirements(classificationLevel: number): string[] {
    const requirements: string[] = [];
    
    if (classificationLevel >= this.CLASSIFICATION_LEVELS.INTERNAL) {
      requirements.push('access_control_required');
    }
    
    if (classificationLevel >= this.CLASSIFICATION_LEVELS.CONFIDENTIAL) {
      requirements.push('encryption_required', 'audit_logging_required');
    }
    
    if (classificationLevel >= this.CLASSIFICATION_LEVELS.RESTRICTED) {
      requirements.push('multi_factor_auth_required', 'data_loss_prevention');
    }
    
    return requirements;
  }

  private static containsPaymentData(data: Record<string, any>): boolean {
    const paymentFields = ['card_number', 'cvv', 'card_expiry', 'bank_account', 'routing_number'];
    return paymentFields.some(field => data[field]);
  }
}

