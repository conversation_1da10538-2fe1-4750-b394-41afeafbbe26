'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  TrendingDown,
  Activity,
  DollarSign,
  Users,
  ShoppingCart,
  Clock,
  Target,
  Zap,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Eye,
  Settings,
  Brain,
  Lightbulb,
  Star,
  Award,
  MapPin,
  Timer,
  Package,
  CreditCard
} from 'lucide-react'

interface AnalyticsData {
  revenue_analytics: RevenueAnalytics
  performance_analytics: PerformanceAnalytics
  customer_analytics: CustomerAnalytics
  operational_analytics: OperationalAnalytics
  predictive_insights: PredictiveInsights
  recommendations: Recommendation[]
}

interface RevenueAnalytics {
  total_revenue: number
  revenue_growth: number
  average_order_value: number
  aov_growth: number
  revenue_by_hour: HourlyData[]
  revenue_by_terminal: TerminalData[]
  revenue_by_category: CategoryData[]
  payment_method_breakdown: PaymentMethodData[]
}

interface PerformanceAnalytics {
  orders_per_hour: number
  average_prep_time: number
  prep_time_trend: number
  efficiency_score: number
  peak_hours: string[]
  bottlenecks: Bottleneck[]
  terminal_performance: TerminalPerformanceData[]
  staff_performance: StaffPerformanceData[]
}

interface CustomerAnalytics {
  total_customers: number
  new_customers: number
  returning_customers: number
  customer_satisfaction: number
  popular_items: PopularItemData[]
  order_patterns: OrderPatternData[]
  customer_segments: CustomerSegmentData[]
}

interface OperationalAnalytics {
  inventory_turnover: number
  waste_percentage: number
  staff_utilization: number
  equipment_uptime: number
  sync_reliability: number
  error_rates: ErrorRateData[]
  maintenance_alerts: MaintenanceAlert[]
}

interface PredictiveInsights {
  demand_forecast: ForecastData[]
  maintenance_predictions: MaintenancePrediction[]
  inventory_recommendations: InventoryRecommendation[]
  staffing_recommendations: StaffingRecommendation[]
  revenue_projections: RevenueProjection[]
}

interface Recommendation {
  id: string
  type: 'revenue' | 'efficiency' | 'customer' | 'operational'
  priority: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  impact: string
  effort: 'low' | 'medium' | 'high'
  estimated_benefit: number
  implementation_steps: string[]
}

interface HourlyData {
  hour: number
  value: number
  comparison: number
}

interface TerminalData {
  terminal_id: string
  terminal_name: string
  value: number
  percentage: number
}

interface CategoryData {
  category: string
  value: number
  percentage: number
  growth: number
}

interface PaymentMethodData {
  method: string
  value: number
  percentage: number
  transactions: number
}

interface Bottleneck {
  area: string
  impact_score: number
  description: string
  suggested_solution: string
}

interface TerminalPerformanceData {
  terminal_id: string
  terminal_name: string
  efficiency_score: number
  orders_processed: number
  average_time: number
  error_count: number
}

interface StaffPerformanceData {
  staff_id: string
  staff_name: string
  orders_processed: number
  average_time: number
  customer_rating: number
  efficiency_score: number
}

export default function AdvancedAnalytics() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    revenue_analytics: {
      total_revenue: 0,
      revenue_growth: 0,
      average_order_value: 0,
      aov_growth: 0,
      revenue_by_hour: [],
      revenue_by_terminal: [],
      revenue_by_category: [],
      payment_method_breakdown: []
    },
    performance_analytics: {
      orders_per_hour: 0,
      average_prep_time: 0,
      prep_time_trend: 0,
      efficiency_score: 0,
      peak_hours: [],
      bottlenecks: [],
      terminal_performance: [],
      staff_performance: []
    },
    customer_analytics: {
      total_customers: 0,
      new_customers: 0,
      returning_customers: 0,
      customer_satisfaction: 0,
      popular_items: [],
      order_patterns: [],
      customer_segments: []
    },
    operational_analytics: {
      inventory_turnover: 0,
      waste_percentage: 0,
      staff_utilization: 0,
      equipment_uptime: 0,
      sync_reliability: 0,
      error_rates: [],
      maintenance_alerts: []
    },
    predictive_insights: {
      demand_forecast: [],
      maintenance_predictions: [],
      inventory_recommendations: [],
      staffing_recommendations: [],
      revenue_projections: []
    },
    recommendations: []
  })
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('7d')
  const [selectedMetric, setSelectedMetric] = useState<string>('revenue')
  const [showPredictive, setShowPredictive] = useState(false)
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadAnalyticsData()
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(() => {
      if (autoRefresh) {
        loadAnalyticsData()
      }
    }, 30000)

    return () => clearInterval(interval)
  }, [selectedTimeRange, selectedMetric, autoRefresh])

  const loadAnalyticsData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/analytics/advanced?time_range=${selectedTimeRange}&metric=${selectedMetric}`)
      if (response.ok) {
        const data = await response.json()
        setAnalyticsData(data.analytics || analyticsData)
      }
    } catch (error) {
      console.error('Failed to load analytics data:', error)
      toast.error('Failed to load analytics data')
    } finally {
      setLoading(false)
    }
  }

  const exportAnalyticsReport = async () => {
    try {
      const response = await fetch('/api/analytics/export-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          time_range: selectedTimeRange,
          include_predictive: showPredictive
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `analytics-report-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        toast.success('Analytics report exported successfully')
      } else {
        toast.error('Failed to export analytics report')
      }
    } catch (error) {
      console.error('Failed to export report:', error)
      toast.error('Failed to export analytics report')
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-600'
    if (growth < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <TrendingUp className="w-4 h-4 text-green-500" />
    if (growth < 0) return <TrendingDown className="w-4 h-4 text-red-500" />
    return <Activity className="w-4 h-4 text-gray-500" />
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300'
      case 'high':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-300'
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300'
      case 'low':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300'
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  const getEfficiencyColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-yellow-600'
    if (score >= 50) return 'text-orange-600'
    return 'text-red-600'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Advanced Analytics</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Deep insights and predictive analytics for POS performance optimization
          </p>
        </div>
        
        <div className="flex gap-2 mt-4 sm:mt-0">
          <button
            onClick={() => setShowPredictive(!showPredictive)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
              showPredictive 
                ? 'bg-purple-600 text-white hover:bg-purple-700' 
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            <Brain className="w-4 h-4" />
            Predictive Mode
          </button>
          
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
              autoRefresh 
                ? 'bg-green-600 text-white hover:bg-green-700' 
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            <RefreshCw className={`w-4 h-4 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh
          </button>
          
          <button
            onClick={exportAnalyticsReport}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Download className="w-4 h-4" />
            Export Report
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Time Range
            </label>
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
              <option value="1y">Last Year</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Primary Metric
            </label>
            <select
              value={selectedMetric}
              onChange={(e) => setSelectedMetric(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="revenue">Revenue</option>
              <option value="performance">Performance</option>
              <option value="customer">Customer</option>
              <option value="operational">Operational</option>
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={loadAnalyticsData}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <RefreshCw className="w-4 h-4 mx-auto" />
            </button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(analyticsData.revenue_analytics.total_revenue)}
              </p>
              <div className="flex items-center gap-1 mt-1">
                {getGrowthIcon(analyticsData.revenue_analytics.revenue_growth)}
                <span className={`text-sm font-medium ${getGrowthColor(analyticsData.revenue_analytics.revenue_growth)}`}>
                  {formatPercentage(analyticsData.revenue_analytics.revenue_growth)}
                </span>
              </div>
            </div>
            <DollarSign className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Efficiency Score</p>
              <p className={`text-2xl font-bold ${getEfficiencyColor(analyticsData.performance_analytics.efficiency_score)}`}>
                {analyticsData.performance_analytics.efficiency_score}%
              </p>
              <div className="flex items-center gap-1 mt-1">
                {getGrowthIcon(analyticsData.performance_analytics.prep_time_trend)}
                <span className={`text-sm font-medium ${getGrowthColor(analyticsData.performance_analytics.prep_time_trend)}`}>
                  {formatPercentage(analyticsData.performance_analytics.prep_time_trend)}
                </span>
              </div>
            </div>
            <Zap className="w-8 h-8 text-yellow-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Customer Satisfaction</p>
              <p className="text-2xl font-bold text-blue-600">
                {analyticsData.customer_analytics.customer_satisfaction.toFixed(1)}
              </p>
              <div className="flex items-center gap-1 mt-1">
                <Star className="w-4 h-4 text-yellow-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">out of 5.0</span>
              </div>
            </div>
            <Users className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">System Reliability</p>
              <p className={`text-2xl font-bold ${getEfficiencyColor(analyticsData.operational_analytics.sync_reliability)}`}>
                {analyticsData.operational_analytics.sync_reliability.toFixed(1)}%
              </p>
              <div className="flex items-center gap-1 mt-1">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">uptime</span>
              </div>
            </div>
            <Activity className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Recommendations */}
      {analyticsData.recommendations.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <Lightbulb className="w-6 h-6 text-yellow-500" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">AI Recommendations</h3>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Actionable insights to improve your POS performance
            </p>
          </div>

          <div className="p-6 space-y-4">
            {analyticsData.recommendations.slice(0, 3).map((recommendation) => (
              <div key={recommendation.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(recommendation.priority)}`}>
                      {recommendation.priority}
                    </span>
                    <h4 className="font-medium text-gray-900 dark:text-white">{recommendation.title}</h4>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-green-600">
                      {formatCurrency(recommendation.estimated_benefit)}
                    </div>
                    <div className="text-xs text-gray-500">potential benefit</div>
                  </div>
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{recommendation.description}</p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span>Impact: {recommendation.impact}</span>
                    <span>Effort: {recommendation.effort}</span>
                  </div>
                  <button className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Performance Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Terminal Performance</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Efficiency metrics for each terminal
            </p>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              {analyticsData.performance_analytics.terminal_performance.map((terminal) => (
                <div key={terminal.terminal_id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">{terminal.terminal_name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {terminal.orders_processed} orders • {terminal.average_time}min avg
                    </p>
                  </div>
                  <div className="text-right">
                    <div className={`text-lg font-bold ${getEfficiencyColor(terminal.efficiency_score)}`}>
                      {terminal.efficiency_score}%
                    </div>
                    <div className="text-xs text-gray-500">efficiency</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Revenue by Category</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Sales performance by menu category
            </p>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              {analyticsData.revenue_analytics.revenue_by_category.map((category) => (
                <div key={category.category} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="font-medium text-gray-900 dark:text-white">{category.category}</span>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {formatCurrency(category.value)}
                    </div>
                    <div className="flex items-center gap-1">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {formatPercentage(category.percentage)}
                      </span>
                      {getGrowthIcon(category.growth)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Predictive Insights */}
      {showPredictive && (
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-xl border border-purple-200 dark:border-purple-800">
          <div className="p-6 border-b border-purple-200 dark:border-purple-700">
            <div className="flex items-center gap-3">
              <Brain className="w-6 h-6 text-purple-600" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Predictive Insights</h3>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              AI-powered predictions and recommendations for future optimization
            </p>
          </div>

          <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Demand Forecast</h4>
              <div className="space-y-2">
                {analyticsData.predictive_insights.demand_forecast.slice(0, 3).map((forecast, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Next {index + 1} days:</span>
                    <span className="font-medium">{forecast.predicted_orders} orders</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Maintenance Alerts</h4>
              <div className="space-y-2">
                {analyticsData.predictive_insights.maintenance_predictions.slice(0, 3).map((prediction, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <AlertTriangle className="w-4 h-4 text-yellow-500" />
                    <span className="text-gray-600 dark:text-gray-400">{prediction.equipment}</span>
                    <span className="text-xs text-yellow-600">{prediction.days_until} days</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Revenue Projection</h4>
              <div className="space-y-2">
                {analyticsData.predictive_insights.revenue_projections.slice(0, 3).map((projection, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">{projection.period}:</span>
                    <span className="font-medium text-green-600">
                      {formatCurrency(projection.projected_revenue)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Operational Insights */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Operational Insights</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Key operational metrics and bottleneck analysis
          </p>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {analyticsData.operational_analytics.inventory_turnover.toFixed(1)}x
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Inventory Turnover</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {formatPercentage(analyticsData.operational_analytics.staff_utilization)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Staff Utilization</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {formatPercentage(analyticsData.operational_analytics.equipment_uptime)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Equipment Uptime</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {formatPercentage(analyticsData.operational_analytics.waste_percentage)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Waste Percentage</div>
            </div>
          </div>

          {/* Bottlenecks */}
          {analyticsData.performance_analytics.bottlenecks.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Identified Bottlenecks</h4>
              <div className="space-y-3">
                {analyticsData.performance_analytics.bottlenecks.map((bottleneck, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h5 className="font-medium text-gray-900 dark:text-white">{bottleneck.area}</h5>
                        <span className="text-sm font-medium text-yellow-600">
                          Impact: {bottleneck.impact_score}/10
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{bottleneck.description}</p>
                      <p className="text-sm text-blue-600 dark:text-blue-400">
                        <strong>Suggestion:</strong> {bottleneck.suggested_solution}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
