-- Security and Audit System Migration
-- Creates comprehensive security, audit logging, and compliance tables

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- SECURITY LOGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS security_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_type VARCHAR(100) NOT NULL,
  severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  ip_address INET,
  user_agent TEXT,
  endpoint VARCHAR(255),
  http_method VARCHAR(10),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  payload JSONB DEFAULT '{}',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes for performance
  INDEX idx_security_logs_event_type (event_type),
  INDEX idx_security_logs_severity (severity),
  INDEX idx_security_logs_user_id (user_id),
  INDEX idx_security_logs_created_at (created_at),
  INDEX idx_security_logs_ip_address (ip_address)
);

-- =============================================
-- AUDIT LOGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  action VARCHAR(100) NOT NULL,
  resource VARCHAR(100) NOT NULL,
  resource_id UUID,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  changes JSONB DEFAULT '{}',
  old_values JSONB DEFAULT '{}',
  new_values JSONB DEFAULT '{}',
  metadata JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes for performance
  INDEX idx_audit_logs_action (action),
  INDEX idx_audit_logs_resource (resource),
  INDEX idx_audit_logs_resource_id (resource_id),
  INDEX idx_audit_logs_user_id (user_id),
  INDEX idx_audit_logs_created_at (created_at)
);

-- =============================================
-- AUTHENTICATION ATTEMPTS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS auth_attempts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  identifier VARCHAR(255) NOT NULL, -- email, phone, or user ID
  attempt_type VARCHAR(50) NOT NULL, -- 'login', 'pin', 'otp', '2fa'
  platform VARCHAR(50) NOT NULL, -- 'admin-dashboard', 'pos-system', 'customer-web', 'customer-mobile'
  success BOOLEAN NOT NULL DEFAULT FALSE,
  failure_reason VARCHAR(255),
  ip_address INET,
  user_agent TEXT,
  location_data JSONB DEFAULT '{}',
  device_fingerprint VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes for performance and rate limiting
  INDEX idx_auth_attempts_identifier (identifier),
  INDEX idx_auth_attempts_type (attempt_type),
  INDEX idx_auth_attempts_platform (platform),
  INDEX idx_auth_attempts_success (success),
  INDEX idx_auth_attempts_created_at (created_at),
  INDEX idx_auth_attempts_ip_address (ip_address)
);

-- =============================================
-- RATE LIMITING TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS rate_limits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  identifier VARCHAR(255) NOT NULL, -- IP address, user ID, or API key
  endpoint VARCHAR(255) NOT NULL,
  request_count INTEGER NOT NULL DEFAULT 1,
  window_start TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  window_end TIMESTAMP WITH TIME ZONE NOT NULL,
  blocked_until TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Unique constraint for rate limiting windows
  UNIQUE(identifier, endpoint, window_start),
  
  -- Indexes for performance
  INDEX idx_rate_limits_identifier (identifier),
  INDEX idx_rate_limits_endpoint (endpoint),
  INDEX idx_rate_limits_window_end (window_end),
  INDEX idx_rate_limits_blocked_until (blocked_until)
);

-- =============================================
-- DATA ENCRYPTION KEYS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS encryption_keys (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  key_name VARCHAR(100) NOT NULL UNIQUE,
  key_version INTEGER NOT NULL DEFAULT 1,
  encrypted_key TEXT NOT NULL, -- Encrypted with master key
  algorithm VARCHAR(50) NOT NULL DEFAULT 'AES-256-GCM',
  purpose VARCHAR(100) NOT NULL, -- 'user_data', 'payment_data', 'pii_data'
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE,
  rotated_at TIMESTAMP WITH TIME ZONE,
  
  -- Indexes
  INDEX idx_encryption_keys_name (key_name),
  INDEX idx_encryption_keys_active (is_active),
  INDEX idx_encryption_keys_purpose (purpose)
);

-- =============================================
-- GDPR COMPLIANCE TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS gdpr_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  request_type VARCHAR(50) NOT NULL CHECK (request_type IN ('access', 'rectification', 'erasure', 'portability', 'restriction', 'objection')),
  status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'rejected')),
  requested_by VARCHAR(255) NOT NULL, -- email or phone of requester
  verification_method VARCHAR(50), -- how identity was verified
  verification_data JSONB DEFAULT '{}',
  request_details JSONB DEFAULT '{}',
  response_data JSONB DEFAULT '{}',
  processed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  legal_basis VARCHAR(100),
  retention_period INTEGER, -- days
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  due_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (NOW() + INTERVAL '30 days'),
  completed_at TIMESTAMP WITH TIME ZONE,
  
  -- Indexes
  INDEX idx_gdpr_requests_user_id (user_id),
  INDEX idx_gdpr_requests_type (request_type),
  INDEX idx_gdpr_requests_status (status),
  INDEX idx_gdpr_requests_due_date (due_date)
);

-- =============================================
-- DATA RETENTION POLICIES TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS data_retention_policies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  table_name VARCHAR(100) NOT NULL,
  data_category VARCHAR(100) NOT NULL, -- 'personal', 'financial', 'behavioral', etc.
  retention_period_days INTEGER NOT NULL,
  legal_basis VARCHAR(100) NOT NULL,
  auto_delete BOOLEAN NOT NULL DEFAULT FALSE,
  anonymize_instead BOOLEAN NOT NULL DEFAULT FALSE,
  policy_description TEXT,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Unique constraint
  UNIQUE(table_name, data_category),
  
  -- Indexes
  INDEX idx_retention_policies_table (table_name),
  INDEX idx_retention_policies_category (data_category),
  INDEX idx_retention_policies_active (is_active)
);

-- =============================================
-- PCI DSS COMPLIANCE TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS pci_compliance_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_type VARCHAR(100) NOT NULL, -- 'card_data_access', 'key_rotation', 'vulnerability_scan'
  compliance_requirement VARCHAR(100) NOT NULL, -- PCI DSS requirement number
  status VARCHAR(50) NOT NULL CHECK (status IN ('compliant', 'non_compliant', 'remediated')),
  details JSONB DEFAULT '{}',
  evidence_location TEXT,
  responsible_party VARCHAR(255),
  remediation_required BOOLEAN DEFAULT FALSE,
  remediation_deadline TIMESTAMP WITH TIME ZONE,
  remediation_completed TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_pci_logs_event_type (event_type),
  INDEX idx_pci_logs_requirement (compliance_requirement),
  INDEX idx_pci_logs_status (status),
  INDEX idx_pci_logs_created_at (created_at)
);

-- =============================================
-- SECURITY INCIDENTS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS security_incidents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  incident_type VARCHAR(100) NOT NULL,
  severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  status VARCHAR(50) NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'contained', 'resolved', 'closed')),
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  affected_systems TEXT[],
  affected_users UUID[],
  detection_method VARCHAR(100),
  detection_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  response_time TIMESTAMP WITH TIME ZONE,
  containment_time TIMESTAMP WITH TIME ZONE,
  resolution_time TIMESTAMP WITH TIME ZONE,
  assigned_to UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  root_cause TEXT,
  remediation_actions JSONB DEFAULT '{}',
  lessons_learned TEXT,
  cost_impact DECIMAL(10,2),
  regulatory_notification_required BOOLEAN DEFAULT FALSE,
  regulatory_notification_sent BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_security_incidents_type (incident_type),
  INDEX idx_security_incidents_severity (severity),
  INDEX idx_security_incidents_status (status),
  INDEX idx_security_incidents_detection_time (detection_time),
  INDEX idx_security_incidents_assigned_to (assigned_to)
);

-- =============================================
-- VULNERABILITY ASSESSMENTS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS vulnerability_assessments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  assessment_type VARCHAR(50) NOT NULL, -- 'automated', 'manual', 'penetration_test'
  target_system VARCHAR(100) NOT NULL,
  vulnerability_id VARCHAR(100),
  cve_id VARCHAR(20),
  severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  cvss_score DECIMAL(3,1),
  title VARCHAR(255) NOT NULL,
  description TEXT NOT NULL,
  affected_component VARCHAR(255),
  remediation_recommendation TEXT,
  status VARCHAR(50) NOT NULL DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'resolved', 'accepted_risk', 'false_positive')),
  discovered_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  remediation_deadline TIMESTAMP WITH TIME ZONE,
  remediation_completed TIMESTAMP WITH TIME ZONE,
  assigned_to UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  scanner_tool VARCHAR(100),
  evidence JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_vulnerability_assessments_type (assessment_type),
  INDEX idx_vulnerability_assessments_severity (severity),
  INDEX idx_vulnerability_assessments_status (status),
  INDEX idx_vulnerability_assessments_target (target_system),
  INDEX idx_vulnerability_assessments_discovered (discovered_date)
);

-- =============================================
-- SECURITY CONFIGURATION TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS security_configurations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  config_name VARCHAR(100) NOT NULL UNIQUE,
  config_category VARCHAR(50) NOT NULL, -- 'authentication', 'encryption', 'network', 'application'
  config_value JSONB NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  environment VARCHAR(20) NOT NULL DEFAULT 'production', -- 'development', 'staging', 'production'
  last_modified_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  change_reason TEXT,
  previous_value JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_security_configs_name (config_name),
  INDEX idx_security_configs_category (config_category),
  INDEX idx_security_configs_active (is_active),
  INDEX idx_security_configs_environment (environment)
);

-- =============================================
-- FUNCTIONS FOR SECURITY OPERATIONS
-- =============================================

-- Function to log authentication attempts
CREATE OR REPLACE FUNCTION log_auth_attempt(
  p_identifier VARCHAR(255),
  p_attempt_type VARCHAR(50),
  p_platform VARCHAR(50),
  p_success BOOLEAN,
  p_failure_reason VARCHAR(255) DEFAULT NULL,
  p_ip_address INET DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  attempt_id UUID;
BEGIN
  INSERT INTO auth_attempts (
    identifier, attempt_type, platform, success, failure_reason, ip_address, user_agent
  ) VALUES (
    p_identifier, p_attempt_type, p_platform, p_success, p_failure_reason, p_ip_address, p_user_agent
  ) RETURNING id INTO attempt_id;
  
  RETURN attempt_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check rate limits
CREATE OR REPLACE FUNCTION check_rate_limit(
  p_identifier VARCHAR(255),
  p_endpoint VARCHAR(255),
  p_window_minutes INTEGER DEFAULT 15,
  p_max_requests INTEGER DEFAULT 100
) RETURNS BOOLEAN AS $$
DECLARE
  current_count INTEGER;
  window_start TIMESTAMP WITH TIME ZONE;
BEGIN
  window_start := NOW() - (p_window_minutes || ' minutes')::INTERVAL;
  
  -- Count requests in current window
  SELECT COALESCE(SUM(request_count), 0)
  INTO current_count
  FROM rate_limits
  WHERE identifier = p_identifier
    AND endpoint = p_endpoint
    AND window_start >= window_start;
  
  -- Check if limit exceeded
  IF current_count >= p_max_requests THEN
    RETURN FALSE;
  END IF;
  
  -- Update or insert rate limit record
  INSERT INTO rate_limits (identifier, endpoint, window_start, window_end)
  VALUES (p_identifier, p_endpoint, NOW(), NOW() + (p_window_minutes || ' minutes')::INTERVAL)
  ON CONFLICT (identifier, endpoint, window_start)
  DO UPDATE SET
    request_count = rate_limits.request_count + 1,
    updated_at = NOW();
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to encrypt sensitive data
CREATE OR REPLACE FUNCTION encrypt_sensitive_data(
  p_data TEXT,
  p_key_name VARCHAR(100)
) RETURNS TEXT AS $$
DECLARE
  encryption_key TEXT;
BEGIN
  -- Get the active encryption key
  SELECT encrypted_key INTO encryption_key
  FROM encryption_keys
  WHERE key_name = p_key_name
    AND is_active = TRUE
  ORDER BY created_at DESC
  LIMIT 1;
  
  IF encryption_key IS NULL THEN
    RAISE EXCEPTION 'Encryption key not found: %', p_key_name;
  END IF;
  
  -- Use pgcrypto to encrypt the data
  RETURN pgp_sym_encrypt(p_data, encryption_key);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrypt sensitive data
CREATE OR REPLACE FUNCTION decrypt_sensitive_data(
  p_encrypted_data TEXT,
  p_key_name VARCHAR(100)
) RETURNS TEXT AS $$
DECLARE
  encryption_key TEXT;
BEGIN
  -- Get the encryption key
  SELECT encrypted_key INTO encryption_key
  FROM encryption_keys
  WHERE key_name = p_key_name
    AND is_active = TRUE
  ORDER BY created_at DESC
  LIMIT 1;
  
  IF encryption_key IS NULL THEN
    RAISE EXCEPTION 'Encryption key not found: %', p_key_name;
  END IF;
  
  -- Use pgcrypto to decrypt the data
  RETURN pgp_sym_decrypt(p_encrypted_data, encryption_key);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- TRIGGERS FOR AUDIT LOGGING
-- =============================================

-- Generic audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function() RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO audit_logs (action, resource, resource_id, new_values, user_id)
    VALUES ('INSERT', TG_TABLE_NAME, NEW.id, to_jsonb(NEW), current_setting('app.current_user_id', true)::UUID);
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO audit_logs (action, resource, resource_id, old_values, new_values, changes, user_id)
    VALUES (
      'UPDATE',
      TG_TABLE_NAME,
      NEW.id,
      to_jsonb(OLD),
      to_jsonb(NEW),
      jsonb_diff(to_jsonb(OLD), to_jsonb(NEW)),
      current_setting('app.current_user_id', true)::UUID
    );
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO audit_logs (action, resource, resource_id, old_values, user_id)
    VALUES ('DELETE', TG_TABLE_NAME, OLD.id, to_jsonb(OLD), current_setting('app.current_user_id', true)::UUID);
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to calculate JSON differences
CREATE OR REPLACE FUNCTION jsonb_diff(old_data JSONB, new_data JSONB) RETURNS JSONB AS $$
DECLARE
  result JSONB := '{}'::JSONB;
  key TEXT;
  old_value JSONB;
  new_value JSONB;
BEGIN
  -- Check for changed or new keys
  FOR key IN SELECT jsonb_object_keys(new_data) LOOP
    old_value := old_data -> key;
    new_value := new_data -> key;
    
    IF old_value IS DISTINCT FROM new_value THEN
      result := result || jsonb_build_object(key, jsonb_build_object('old', old_value, 'new', new_value));
    END IF;
  END LOOP;
  
  -- Check for deleted keys
  FOR key IN SELECT jsonb_object_keys(old_data) LOOP
    IF NOT new_data ? key THEN
      result := result || jsonb_build_object(key, jsonb_build_object('old', old_data -> key, 'new', null));
    END IF;
  END LOOP;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================

-- Enable RLS on all security tables
ALTER TABLE security_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE rate_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE encryption_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE gdpr_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_retention_policies ENABLE ROW LEVEL SECURITY;
ALTER TABLE pci_compliance_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_incidents ENABLE ROW LEVEL SECURITY;
ALTER TABLE vulnerability_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_configurations ENABLE ROW LEVEL SECURITY;

-- Security logs: Only admins and security team can access
CREATE POLICY "security_logs_admin_access" ON security_logs
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles up
      JOIN roles r ON up.role_id = r.id
      WHERE up.id = auth.uid()
        AND r.name IN ('admin', 'security_admin')
    )
  );

-- Audit logs: Admins and auditors can read, system can write
CREATE POLICY "audit_logs_read_access" ON audit_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_profiles up
      JOIN roles r ON up.role_id = r.id
      WHERE up.id = auth.uid()
        AND r.name IN ('admin', 'auditor', 'security_admin')
    )
  );

CREATE POLICY "audit_logs_system_write" ON audit_logs
  FOR INSERT WITH CHECK (true); -- System can always write audit logs

-- GDPR requests: Users can see their own requests, admins can see all
CREATE POLICY "gdpr_requests_user_access" ON gdpr_requests
  FOR ALL USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM user_profiles up
      JOIN roles r ON up.role_id = r.id
      WHERE up.id = auth.uid()
        AND r.name IN ('admin', 'privacy_officer')
    )
  );

-- =============================================
-- INITIAL SECURITY CONFIGURATIONS
-- =============================================

-- Insert default encryption keys (these should be rotated in production)
INSERT INTO encryption_keys (key_name, encrypted_key, purpose) VALUES
  ('user_data_key', pgp_sym_encrypt('default_user_key_change_in_production', 'master_key'), 'user_data'),
  ('payment_data_key', pgp_sym_encrypt('default_payment_key_change_in_production', 'master_key'), 'payment_data'),
  ('pii_data_key', pgp_sym_encrypt('default_pii_key_change_in_production', 'master_key'), 'pii_data')
ON CONFLICT (key_name) DO NOTHING;

-- Insert default data retention policies
INSERT INTO data_retention_policies (table_name, data_category, retention_period_days, legal_basis) VALUES
  ('user_profiles', 'personal', 1095, 'legitimate_interests'), -- 3 years
  ('orders', 'financial', 2555, 'legal_obligation'), -- 7 years
  ('payments', 'financial', 2555, 'legal_obligation'), -- 7 years
  ('auth_attempts', 'security', 730, 'legitimate_interests'), -- 2 years
  ('security_logs', 'security', 730, 'legitimate_interests'), -- 2 years
  ('audit_logs', 'compliance', 2555, 'legal_obligation') -- 7 years
ON CONFLICT (table_name, data_category) DO NOTHING;

-- Insert default security configurations
INSERT INTO security_configurations (config_name, config_category, config_value) VALUES
  ('password_policy', 'authentication', '{
    "min_length": 8,
    "require_uppercase": true,
    "require_lowercase": true,
    "require_numbers": true,
    "require_special_chars": true,
    "max_age_days": 90,
    "history_count": 5
  }'),
  ('session_policy', 'authentication', '{
    "max_duration_hours": 24,
    "idle_timeout_minutes": 30,
    "require_2fa_for_admin": true,
    "concurrent_sessions_limit": 3
  }'),
  ('rate_limiting', 'network', '{
    "login_attempts": {"window_minutes": 15, "max_attempts": 5},
    "api_requests": {"window_minutes": 15, "max_requests": 1000},
    "password_reset": {"window_minutes": 60, "max_attempts": 3}
  }'),
  ('encryption_settings', 'encryption', '{
    "algorithm": "AES-256-GCM",
    "key_rotation_days": 90,
    "encrypt_pii": true,
    "encrypt_payment_data": true
  }')
ON CONFLICT (config_name) DO NOTHING;

-- Create indexes for performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_security_logs_composite ON security_logs(event_type, severity, created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_composite ON audit_logs(resource, action, created_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_auth_attempts_composite ON auth_attempts(identifier, attempt_type, created_at);

-- Add comments for documentation
COMMENT ON TABLE security_logs IS 'Logs all security-related events for monitoring and incident response';
COMMENT ON TABLE audit_logs IS 'Comprehensive audit trail of all data changes for compliance';
COMMENT ON TABLE auth_attempts IS 'Tracks all authentication attempts for security monitoring';
COMMENT ON TABLE rate_limits IS 'Implements rate limiting to prevent abuse';
COMMENT ON TABLE encryption_keys IS 'Manages encryption keys for data protection';
COMMENT ON TABLE gdpr_requests IS 'Tracks GDPR data subject requests for compliance';
COMMENT ON TABLE data_retention_policies IS 'Defines data retention policies for compliance';
COMMENT ON TABLE pci_compliance_logs IS 'Tracks PCI DSS compliance activities';
COMMENT ON TABLE security_incidents IS 'Manages security incidents and response';
COMMENT ON TABLE vulnerability_assessments IS 'Tracks security vulnerabilities and remediation';
COMMENT ON TABLE security_configurations IS 'Stores security configuration settings';