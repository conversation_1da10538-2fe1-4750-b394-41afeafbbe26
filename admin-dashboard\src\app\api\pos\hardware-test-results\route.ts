import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    const terminalId = searchParams.get('terminal_id')
    const hardwareType = searchParams.get('hardware_type')
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '50')

    // For now, return mock test results since we don't have a hardware_test_results table
    // In a real implementation, this would query the database
    const mockResults = [
      {
        id: 'test-001',
        terminal_id: 'terminal-770c7b00',
        hardware_type: 'printer',
        test_type: 'connectivity',
        status: 'passed',
        test_duration_ms: 1250,
        started_at: new Date(Date.now() - 3600000).toISOString(),
        completed_at: new Date(Date.now() - 3598750).toISOString(),
        details: {
          ip_address: '*************',
          port: 9100,
          response_time_ms: 45,
          print_test_successful: true
        },
        error_message: null
      },
      {
        id: 'test-002',
        terminal_id: 'terminal-770c7b00',
        hardware_type: 'cash_drawer',
        test_type: 'functionality',
        status: 'passed',
        test_duration_ms: 800,
        started_at: new Date(Date.now() - 7200000).toISOString(),
        completed_at: new Date(Date.now() - 7199200).toISOString(),
        details: {
          connection_type: 'printer',
          trigger_successful: true,
          open_duration_ms: 500
        },
        error_message: null
      },
      {
        id: 'test-003',
        terminal_id: 'terminal-770c7b00',
        hardware_type: 'payment_terminal',
        test_type: 'connectivity',
        status: 'failed',
        test_duration_ms: 5000,
        started_at: new Date(Date.now() - 10800000).toISOString(),
        completed_at: new Date(Date.now() - 10795000).toISOString(),
        details: {
          provider: 'stripe',
          terminal_id: 'tmr_test123',
          connection_timeout: true
        },
        error_message: 'Connection timeout after 5 seconds'
      },
      {
        id: 'test-004',
        terminal_id: 'terminal-770c7b00',
        hardware_type: 'barcode_scanner',
        test_type: 'functionality',
        status: 'passed',
        test_duration_ms: 2100,
        started_at: new Date(Date.now() - 14400000).toISOString(),
        completed_at: new Date(Date.now() - 14397900).toISOString(),
        details: {
          port: 'COM4',
          scan_test_successful: true,
          beep_test_successful: true,
          test_barcode: '1234567890123'
        },
        error_message: null
      },
      {
        id: 'test-005',
        terminal_id: 'terminal-770c7b00',
        hardware_type: 'customer_display',
        test_type: 'display',
        status: 'warning',
        test_duration_ms: 1500,
        started_at: new Date(Date.now() - 18000000).toISOString(),
        completed_at: new Date(Date.now() - 17998500).toISOString(),
        details: {
          port: 'COM3',
          display_test_successful: true,
          brightness_low: true,
          character_test_passed: true
        },
        error_message: 'Display brightness is below recommended level'
      }
    ]

    // Filter results based on query parameters
    let filteredResults = mockResults

    if (terminalId) {
      filteredResults = filteredResults.filter(result => result.terminal_id === terminalId)
    }

    if (hardwareType) {
      filteredResults = filteredResults.filter(result => result.hardware_type === hardwareType)
    }

    if (status) {
      filteredResults = filteredResults.filter(result => result.status === status)
    }

    // Apply limit
    filteredResults = filteredResults.slice(0, limit)

    // Calculate summary statistics
    const totalTests = filteredResults.length
    const passedTests = filteredResults.filter(r => r.status === 'passed').length
    const failedTests = filteredResults.filter(r => r.status === 'failed').length
    const warningTests = filteredResults.filter(r => r.status === 'warning').length

    const avgDuration = filteredResults.length > 0 
      ? filteredResults.reduce((sum, r) => sum + r.test_duration_ms, 0) / filteredResults.length 
      : 0

    return NextResponse.json({
      results: filteredResults,
      summary: {
        total_tests: totalTests,
        passed: passedTests,
        failed: failedTests,
        warnings: warningTests,
        success_rate: totalTests > 0 ? (passedTests / totalTests) * 100 : 0,
        avg_duration_ms: Math.round(avgDuration)
      },
      hardware_types: [...new Set(filteredResults.map(r => r.hardware_type))],
      filters: {
        terminal_id: terminalId,
        hardware_type: hardwareType,
        status,
        limit
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Hardware test results error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { terminal_id, hardware_type, test_type, status, test_duration_ms, details, error_message } = await request.json()

    if (!terminal_id || !hardware_type || !test_type || !status) {
      return NextResponse.json(
        { error: 'terminal_id, hardware_type, test_type, and status are required' },
        { status: 400 }
      )
    }

    // In a real implementation, this would save to database
    const testResult = {
      id: `test-${Date.now()}`,
      terminal_id,
      hardware_type,
      test_type,
      status,
      test_duration_ms: test_duration_ms || 0,
      started_at: new Date().toISOString(),
      completed_at: new Date().toISOString(),
      details: details || {},
      error_message: error_message || null,
      created_at: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      message: 'Test result recorded successfully',
      result: testResult,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Hardware test results POST error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const testId = searchParams.get('test_id')
    const terminalId = searchParams.get('terminal_id')
    const olderThan = searchParams.get('older_than') // ISO date string

    if (!testId && !terminalId && !olderThan) {
      return NextResponse.json(
        { error: 'test_id, terminal_id, or older_than parameter is required' },
        { status: 400 }
      )
    }

    // In a real implementation, this would delete from database
    let deletedCount = 0

    if (testId) {
      deletedCount = 1
    } else if (terminalId) {
      deletedCount = 5 // Mock deletion count
    } else if (olderThan) {
      deletedCount = 10 // Mock deletion count
    }

    return NextResponse.json({
      success: true,
      message: `${deletedCount} test result(s) deleted successfully`,
      deleted_count: deletedCount,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Hardware test results DELETE error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
