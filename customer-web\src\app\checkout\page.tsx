import { Metadata } from 'next';
import Link from 'next/link';
import { GlassCard, GlassButton, GlassDivider, GlassInput } from '@/components/ui/glass-components';

export const metadata: Metadata = {
  title: 'Checkout | Delicious Crepes & Waffles',
  description: 'Complete your order with secure checkout.',
};

export default function CheckoutPage() {
  // Mock cart data - in a real app, this would come from the CartProvider
  const cartItems = [
    {
      id: 'nutella-strawberry',
      name: '<PERSON><PERSON><PERSON> & Strawberry',
      price: 7.5,
      quantity: 2,
    },
    {
      id: 'ham-cheese',
      name: 'Ham & Cheese',
      price: 8.5,
      quantity: 1,
    },
    {
      id: 'coffee',
      name: 'Coffee',
      price: 2.5,
      quantity: 2,
    },
  ];

  const subtotal = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const tax = subtotal * 0.13; // 13% tax
  const deliveryFee = 2.5;
  const total = subtotal + tax + deliveryFee;

  return (
    <main className="min-h-screen py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Checkout</h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Checkout Form - Takes up 2/3 of the space on large screens */}
          <div className="lg:col-span-2 space-y-6">
            {/* Delivery Method */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-4">Delivery Method</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border border-input rounded-lg p-4 cursor-pointer hover:border-primary transition-colors">
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        id="delivery"
                        name="deliveryMethod"
                        className="h-4 w-4 text-primary"
                        defaultChecked
                      />

                      <label htmlFor="delivery" className="font-medium cursor-pointer flex-grow">
                        Delivery
                      </label>
                      <span className="text-sm font-semibold">+€2.50</span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-2 ml-7">
                      Estimated delivery time: 30-45 minutes
                    </p>
                  </div>

                  <div className="border border-input rounded-lg p-4 cursor-pointer hover:border-primary transition-colors">
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        id="pickup"
                        name="deliveryMethod"
                        className="h-4 w-4 text-primary"
                      />

                      <label htmlFor="pickup" className="font-medium cursor-pointer flex-grow">
                        Pickup
                      </label>
                      <span className="text-sm font-semibold text-green-600">Free</span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-2 ml-7">
                      Ready for pickup in: 15-20 minutes
                    </p>
                  </div>
                </div>
              </div>
            </GlassCard>

            {/* Delivery Address - Only shown if delivery is selected */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-4">Delivery Address</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <GlassInput
                      label="Full Address"
                      placeholder="Street address, apartment, etc."
                      required
                    />
                  </div>

                  <GlassInput label="Floor/Apartment" placeholder="Floor, apartment number, etc." />

                  <GlassInput label="Doorbell Name" placeholder="Name on the doorbell" />

                  <div className="md:col-span-2">
                    <GlassInput
                      label="Delivery Instructions"
                      placeholder="Any special instructions for delivery"
                      multiline
                      rows={3}
                    />
                  </div>
                </div>
              </div>
            </GlassCard>

            {/* Contact Information */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-4">Contact Information</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <GlassInput label="Full Name" placeholder="Your full name" required />

                  <GlassInput
                    label="Phone Number"
                    placeholder="Your phone number"
                    type="tel"
                    required
                  />

                  <div className="md:col-span-2">
                    <GlassInput
                      label="Email"
                      placeholder="Your email address"
                      type="email"
                      required
                    />
                  </div>
                </div>
              </div>
            </GlassCard>

            {/* Payment Method */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-4">Payment Method</h2>

                <div className="space-y-4">
                  <div className="border border-input rounded-lg p-4 cursor-pointer hover:border-primary transition-colors">
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        id="cash"
                        name="paymentMethod"
                        className="h-4 w-4 text-primary"
                        defaultChecked
                      />

                      <label htmlFor="cash" className="font-medium cursor-pointer">
                        Cash on Delivery
                      </label>
                    </div>
                  </div>

                  <div className="border border-input rounded-lg p-4 cursor-pointer hover:border-primary transition-colors">
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        id="card"
                        name="paymentMethod"
                        className="h-4 w-4 text-primary"
                      />

                      <label htmlFor="card" className="font-medium cursor-pointer">
                        Credit/Debit Card
                      </label>
                    </div>

                    {/* Card details would be shown conditionally when card is selected */}
                    <div className="mt-4 ml-7 space-y-4 hidden">
                      <GlassInput label="Card Number" placeholder="1234 5678 9012 3456" />

                      <div className="grid grid-cols-2 gap-4">
                        <GlassInput label="Expiry Date" placeholder="MM/YY" />

                        <GlassInput label="CVC" placeholder="123" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </GlassCard>
          </div>

          {/* Order Summary - Takes up 1/3 of the space on large screens */}
          <div>
            <GlassCard className="sticky top-24">
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-4">Order Summary</h2>

                <div className="space-y-3 mb-4">
                  {cartItems.map(item => (
                    <div key={item.id} className="flex justify-between">
                      <span className="text-muted-foreground">
                        {item.quantity}x {item.name}
                      </span>
                      <span>${(item.price * item.quantity).toFixed(2)}</span>
                    </div>
                  ))}
                </div>

                <GlassDivider />

                <div className="space-y-3 my-4">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Subtotal</span>
                    <span>${subtotal.toFixed(2)}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Tax (13%)</span>
                    <span>${tax.toFixed(2)}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Delivery Fee</span>
                    <span>${deliveryFee.toFixed(2)}</span>
                  </div>
                </div>

                <GlassDivider />

                <div className="flex justify-between font-bold text-lg my-4">
                  <span>Total</span>
                  <span>${total.toFixed(2)}</span>
                </div>

                <div className="mt-6 space-y-3">
                  <GlassButton variant="primary" size="lg" className="w-full">
                    Place Order
                  </GlassButton>

                  <Link href="/cart">
                    <GlassButton variant="secondary" size="lg" className="w-full">
                      Back to Cart
                    </GlassButton>
                  </Link>
                </div>
              </div>
            </GlassCard>
          </div>
        </div>
      </div>
    </main>
  );
}
