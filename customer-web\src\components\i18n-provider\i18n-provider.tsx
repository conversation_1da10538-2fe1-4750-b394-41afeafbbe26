'use client';

import { useEffect } from 'react';
import { I18nextProvider } from 'react-i18next';
import i18n, { initializeLanguage } from '@/lib/i18n/i18n';

interface I18nProviderProps {
  children: React.ReactNode;
}

/**
 * I18nProvider component that wraps the application with i18next provider
 * and initializes the language based on user preferences
 */
export function I18nProvider({ children }: I18nProviderProps) {
  useEffect(() => {
    // Initialize language from localStorage or browser settings
    initializeLanguage();
  }, []);

  return <I18nextProvider i18n={i18n}>{children}</I18nextProvider>;
}
