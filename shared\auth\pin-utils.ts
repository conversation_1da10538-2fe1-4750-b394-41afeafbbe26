// PIN Utilities for POS System Authentication

import { supabase, AUTH_CONFIG } from './config';
import * as bcrypt from 'bcryptjs';

/**
 * Hash a PIN for secure storage
 */
export async function hashPIN(pin: string): Promise<string> {
  try {
    const saltRounds = 12;
    const hashedPIN = await bcrypt.hash(pin, saltRounds);
    return hashedPIN;
  } catch (error) {
    console.error('PIN hashing error:', error);
    throw new Error('Failed to hash PIN');
  }
}

/**
 * Verify a PIN against its hash
 */
export async function verifyPIN(pin: string, hashedPIN: string): Promise<boolean> {
  try {
    const isValid = await bcrypt.compare(pin, hashedPIN);
    return isValid;
  } catch (error) {
    console.error('PIN verification error:', error);
    return false;
  }
}

/**
 * Validate PIN format
 */
export function isValidPIN(pin: string): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  // Check length
  if (pin.length !== AUTH_CONFIG.pin.length) {
    errors.push(`PIN must be exactly ${AUTH_CONFIG.pin.length} digits`);
  }
  
  // Check if all characters are digits
  if (!/^\d+$/.test(pin)) {
    errors.push('PIN must contain only numbers');
  }
  
  // Check for sequential numbers (1234, 4321)
  const isSequential = isSequentialPIN(pin);
  if (isSequential) {
    errors.push('PIN cannot be sequential numbers');
  }
  
  // Check for repeated numbers (1111, 2222)
  const isRepeated = isRepeatedPIN(pin);
  if (isRepeated) {
    errors.push('PIN cannot be all the same number');
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Check if PIN is sequential (ascending or descending)
 */
function isSequentialPIN(pin: string): boolean {
  const digits = pin.split('').map(Number);
  
  // Check ascending sequence
  let isAscending = true;
  for (let i = 1; i < digits.length; i++) {
    if (digits[i] !== digits[i - 1] + 1) {
      isAscending = false;
      break;
    }
  }
  
  // Check descending sequence
  let isDescending = true;
  for (let i = 1; i < digits.length; i++) {
    if (digits[i] !== digits[i - 1] - 1) {
      isDescending = false;
      break;
    }
  }
  
  return isAscending || isDescending;
}

/**
 * Check if PIN has all repeated digits
 */
function isRepeatedPIN(pin: string): boolean {
  const firstDigit = pin[0];
  return pin.split('').every(digit => digit === firstDigit);
}

/**
 * Verify staff PIN using database function
 */
export async function verifyStaffPIN(pin: string): Promise<{
  success: boolean;
  staff_id?: string;
  staff_name?: string;
  role_name?: string;
  message: string;
}> {
  try {
    // Validate PIN format first
    const validation = isValidPIN(pin);
    if (!validation.valid) {
      return {
        success: false,
        message: validation.errors.join(', '),
      };
    }

    // Use database function to verify PIN
    const { data: result, error } = await (supabase as any)
      .rpc('verify_staff_pin', { staff_pin: pin });

    if (error) {
      console.error('Staff PIN verification error:', error);
      return {
        success: false,
        message: 'PIN verification failed',
      };
    }

    return {
      success: result.success,
      staff_id: result.staff_id,
      staff_name: result.staff_name,
      role_name: result.role_name,
      message: result.message,
    };
  } catch (error) {
    console.error('Staff PIN verification error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'PIN verification failed',
    };
  }
}

/**
 * Update staff PIN
 */
export async function updateStaffPIN(staffId: string, newPIN: string): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    // Validate new PIN
    const validation = isValidPIN(newPIN);
    if (!validation.valid) {
      return {
        success: false,
        message: validation.errors.join(', '),
      };
    }

    // Hash the new PIN
    const hashedPIN = await hashPIN(newPIN);

    // Update in database
    const { error } = await supabase
      .from('user_profiles')
      .update({ 
        pin_hash: hashedPIN,
        updated_at: new Date().toISOString(),
      })
      .eq('id', staffId);

    if (error) {
      console.error('Update staff PIN error:', error);
      return {
        success: false,
        message: 'Failed to update PIN',
      };
    }

    return {
      success: true,
      message: 'PIN updated successfully',
    };
  } catch (error) {
    console.error('Update staff PIN error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to update PIN',
    };
  }
}

/**
 * Generate a random secure PIN
 */
export function generateSecurePIN(): string {
  const length = AUTH_CONFIG.pin.length;
  let pin = '';
  
  // Generate random digits
  for (let i = 0; i < length; i++) {
    pin += Math.floor(Math.random() * 10).toString();
  }
  
  // Check if generated PIN is valid (not sequential or repeated)
  const validation = isValidPIN(pin);
  if (!validation.valid) {
    // Regenerate if invalid
    return generateSecurePIN();
  }
  
  return pin;
}

/**
 * Get PIN attempt history for a staff member
 */
export async function getPINAttemptHistory(staffId: string, hours: number = 24): Promise<{
  attempts: number;
  lastAttempt?: Date;
  isLocked: boolean;
  lockoutEnd?: Date;
}> {
  try {
    const since = new Date();
    since.setHours(since.getHours() - hours);

    // Get staff user profile to find user_id
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('id', staffId)
      .single();

    if (!profile) {
      return {
        attempts: 0,
        isLocked: false,
      };
    }

    // Get recent failed PIN attempts
    const { data: attempts, error } = await supabase
      .from('auth_attempts')
      .select('created_at')
      .eq('user_id', profile.id) // Changed 'profile.user_id' to 'profile.id'
      .eq('attempt_type', 'pin')
      .eq('success', false)
      .gte('created_at', since.toISOString())
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Get PIN attempt history error:', error);
      return {
        attempts: 0,
        isLocked: false,
      };
    }

    const attemptCount = attempts?.length || 0;
    const lastAttempt = attempts?.[0] && attempts[0].created_at ? new Date(attempts[0].created_at) : undefined;
    
    // Check if locked out
    const isLocked = attemptCount >= AUTH_CONFIG.pin.maxAttempts;
    let lockoutEnd: Date | undefined;
    
    if (isLocked && lastAttempt) {
      lockoutEnd = new Date(lastAttempt.getTime() + (AUTH_CONFIG.pin.lockoutMinutes * 60 * 1000));
      
      // Check if lockout has expired
      if (new Date() > lockoutEnd) {
        return {
          attempts: attemptCount,
          lastAttempt,
          isLocked: false,
        };
      }
    }

    return {
      attempts: attemptCount,
      lastAttempt,
      isLocked,
      lockoutEnd,
    };
  } catch (error) {
    console.error('Get PIN attempt history error:', error);
    return {
      attempts: 0,
      isLocked: false,
    };
  }
}

/**
 * Check if staff member is locked out from PIN attempts
 */
export async function isPINLocked(staffId: string): Promise<{
  locked: boolean;
  remainingTime?: number;
  attempts?: number;
}> {
  try {
    const history = await getPINAttemptHistory(staffId, AUTH_CONFIG.pin.lockoutMinutes / 60);
    
    if (!history.isLocked) {
      return { locked: false, attempts: history.attempts };
    }
    
    if (history.lockoutEnd) {
      const now = new Date();
      const remainingTime = Math.max(0, Math.ceil((history.lockoutEnd.getTime() - now.getTime()) / 1000 / 60));
      
      return {
        locked: remainingTime > 0,
        remainingTime,
        attempts: history.attempts,
      };
    }
    
    return { locked: true, attempts: history.attempts };
  } catch (error) {
    console.error('Check PIN lock error:', error);
    return { locked: false };
  }
}

/**
 * Reset PIN lockout for a staff member
 */
export async function resetPINLockout(staffId: string): Promise<{ success: boolean }> {
  try {
    // Get staff user profile
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('id', staffId)
      .single();

    if (!profile) {
      return { success: false };
    }

    // Delete recent failed PIN attempts
    const { error } = await supabase
      .from('auth_attempts')
      .delete()
      .eq('user_id', profile.id) // Changed 'profile.user_id' to 'profile.id'
      .eq('attempt_type', 'pin')
      .eq('success', false);

    if (error) {
      console.error('Reset PIN lockout error:', error);
      return { success: false };
    }

    return { success: true };
  } catch (error) {
    console.error('Reset PIN lockout error:', error);
    return { success: false };
  }
}

/**
 * Mask PIN for display purposes
 */
export function maskPIN(pin: string): string {
  return '*'.repeat(pin.length);
}

/**
 * Get staff member by PIN (for verification)
 */
export async function getStaffByPIN(pin: string): Promise<{
  staff?: { id: string; email: string; phone?: string; pin_hash: string; created_at: string; updated_at: string };
  valid: boolean;
}> {
  try {
    const verification = await verifyStaffPIN(pin);
    
    if (!verification.success || !verification.staff_id) {
      return { valid: false };
    }

    const { data: staff, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', verification.staff_id)
      .eq('is_active', true)
      .single();

    if (error || !staff) {
      return { valid: false };
    }

    return {
      staff: {
        id: staff.id,
        email: staff.email || '',
        phone: staff.phone || undefined,
        pin_hash: staff.pin_hash || '',
        created_at: staff.created_at || new Date().toISOString(),
        updated_at: staff.updated_at || new Date().toISOString(),
      },
      valid: true,
    };
  } catch (error) {
    console.error('Get staff by PIN error:', error);
    return { valid: false };
  }
}