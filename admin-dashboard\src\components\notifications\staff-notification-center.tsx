/**
 * Staff Notification Center
 * Real-time notifications for kitchen staff, delivery personnel, and managers
 */

'use client'

import React, { useState } from 'react'
import { useStaffNotifications } from '../../hooks/use-staff-notifications'
import { useAuth } from '../../hooks/auth-hooks'
import type { StaffNotification, NotificationType } from '../../types/notifications'

interface NotificationCenterProps {
  userRole: 'admin' | 'kitchen' | 'delivery' | 'manager'
  className?: string
}

interface NotificationItemProps {
  notification: StaffNotification
  onMarkAsRead: (id: string) => void
  onDelete: (id: string) => void
  onAction?: (notification: StaffNotification) => void
}

const NotificationItem = ({
  notification,
  onMarkAsRead,
  onDelete,
  onAction,
}: NotificationItemProps) => {
  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'new_order':
        return '🆕'
      case 'order_ready':
        return '✅'
      case 'delivery_assigned':
        return '🚗'
      case 'kitchen_alert':
        return '🔥'
      case 'order_cancelled':
        return '❌'
      case 'urgent':
        return '🚨'
      default:
        return '📢'
    }
  }

  const getNotificationColor = (type: NotificationType) => {
    switch (type) {
      case 'new_order':
        return 'border-l-blue-500 bg-blue-50'
      case 'order_ready':
        return 'border-l-green-500 bg-green-50'
      case 'delivery_assigned':
        return 'border-l-purple-500 bg-purple-50'
      case 'kitchen_alert':
        return 'border-l-orange-500 bg-orange-50'
      case 'order_cancelled':
        return 'border-l-red-500 bg-red-50'
      case 'urgent':
        return 'border-l-red-600 bg-red-100'
      default:
        return 'border-l-gray-500 bg-gray-50'
    }
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) {
      return 'Just now'
    }
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    }
    if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`
    }
    return time.toLocaleDateString()
  }

  return (
    <div
      className={`border-l-4 p-4 mb-3 rounded-r-lg transition-all duration-200 hover:shadow-md ${getNotificationColor(
        notification.type
      )} ${!notification.read ? 'ring-2 ring-blue-200' : ''}`}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          <div className="text-2xl">{getNotificationIcon(notification.type)}</div>
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <h4 className="font-semibold text-gray-900">{notification.title}</h4>
              {!notification.read && <span className="w-2 h-2 bg-blue-500 rounded-full"></span>}
              {notification.priority === 'high' && (
                <span className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded-full font-medium">
                  HIGH
                </span>
              )}
            </div>
            <p className="text-gray-700 mb-2">{notification.message}</p>
            {notification.orderId && (
              <div className="text-sm text-gray-600 mb-2">Order #{notification.orderId}</div>
            )}
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-500">{formatTimeAgo(notification.timestamp)}</span>
              {notification.actionRequired && onAction && (
                <button
                  onClick={() => onAction(notification)}
                  className="text-sm px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Take Action
                </button>
              )}
            </div>
          </div>
        </div>
        <div className="flex space-x-2 ml-4">
          {!notification.read && (
            <button
              onClick={() => onMarkAsRead(notification.id)}
              className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
              title="Mark as read"
            >
              ✓
            </button>
          )}
          <button
            onClick={() => onDelete(notification.id)}
            className="p-1 text-gray-400 hover:text-red-600 transition-colors"
            title="Delete"
          >
            🗑️
          </button>
        </div>
      </div>
    </div>
  )
}

interface FilterTabsProps {
  activeFilter: string
  onFilterChange: (filter: string) => void
  counts: Record<string, number>
}

const FilterTabs = ({ activeFilter, onFilterChange, counts }: FilterTabsProps) => {
  const filters = [
    { key: 'all', label: 'All', count: counts.all },
    { key: 'unread', label: 'Unread', count: counts.unread },
    { key: 'new_order', label: 'New Orders', count: counts.new_order },
    { key: 'kitchen_alert', label: 'Kitchen', count: counts.kitchen_alert },
    { key: 'delivery_assigned', label: 'Delivery', count: counts.delivery_assigned },
    { key: 'urgent', label: 'Urgent', count: counts.urgent },
  ]

  return (
    <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
      {filters.map(filter => (
        <button
          key={filter.key}
          onClick={() => onFilterChange(filter.key)}
          className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            activeFilter === filter.key
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          {filter.label}
          {filter.count > 0 && (
            <span
              className={`ml-2 px-2 py-1 text-xs rounded-full ${
                activeFilter === filter.key
                  ? 'bg-blue-100 text-blue-600'
                  : 'bg-gray-200 text-gray-600'
              }`}
            >
              {filter.count}
            </span>
          )}
        </button>
      ))}
    </div>
  )
}

export const StaffNotificationCenter = ({
  userRole,
  className = '',
}: NotificationCenterProps) => {
  const { user } = useAuth()
  const {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
    playSound,
    toggleSound,
  } = useStaffNotifications(user?.id, userRole)

  const [activeFilter, setActiveFilter] = useState('all')
  const [soundEnabled, setSoundEnabled] = useState(true)

  // Filter notifications based on active filter
  const filteredNotifications = notifications.filter((notification: StaffNotification) => {
    switch (activeFilter) {
      case 'unread':
        return !notification.read
      case 'all':
        return true
      default:
        return notification.type === activeFilter
    }
  })

  // Calculate counts for filter tabs
  const filterCounts = {
    all: notifications.length,
    unread: notifications.filter((n: StaffNotification) => !n.read).length,
    new_order: notifications.filter((n: StaffNotification) => n.type === 'new_order').length,
    kitchen_alert: notifications.filter((n: StaffNotification) => n.type === 'kitchen_alert')
      .length,
    delivery_assigned: notifications.filter(
      (n: StaffNotification) => n.type === 'delivery_assigned'
    ).length,
    urgent: notifications.filter((n: StaffNotification) => n.type === 'urgent').length,
  }

  const handleNotificationAction = (notification: StaffNotification) => {
    // Handle specific actions based on notification type
    switch (notification.type) {
      case 'new_order':
        // Navigate to order details or POS
        window.open(`/orders/${notification.orderId}`, '_blank')
        break
      case 'delivery_assigned':
        // Open delivery management
        window.open(`/delivery/${notification.orderId}`, '_blank')
        break
      case 'kitchen_alert':
        // Open kitchen dashboard
        window.open(`/kitchen/${notification.orderId}`, '_blank')
        break
      default:
        // Mark as read
        markAsRead(notification.id)
    }
  }

  const handleSoundToggle = () => {
    setSoundEnabled(!soundEnabled)
    toggleSound(!soundEnabled)
  }

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="text-center text-red-600">
          <div className="text-4xl mb-2">⚠️</div>
          <div className="font-medium mb-2">Unable to load notifications</div>
          <div className="text-sm text-gray-600">{error}</div>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold mb-1">Staff Notifications</h2>
            <p className="text-blue-100 capitalize">{userRole} Dashboard</p>
          </div>
          <div className="flex items-center space-x-4">
            {unreadCount > 0 && (
              <div className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                {unreadCount} new
              </div>
            )}
            <button
              onClick={handleSoundToggle}
              className={`p-2 rounded-lg transition-colors ${
                soundEnabled ? 'bg-white/20' : 'bg-white/10'
              }`}
              title={soundEnabled ? 'Disable sound' : 'Enable sound'}
            >
              {soundEnabled ? '🔊' : '🔇'}
            </button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Filter Tabs */}
        <FilterTabs
          activeFilter={activeFilter}
          onFilterChange={setActiveFilter}
          counts={filterCounts}
        />

        {/* Action Buttons */}
        {notifications.length > 0 && (
          <div className="flex space-x-3 mb-6">
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Mark All as Read ({unreadCount})
              </button>
            )}
            <button
              onClick={clearAll}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Clear All
            </button>
            <button
              onClick={() => playSound('notification')}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              🔔 Test Sound
            </button>
          </div>
        )}

        {/* Notifications List */}
        <div className="max-h-96 overflow-y-auto">
          {filteredNotifications.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <div className="text-4xl mb-4">📭</div>
              <div className="font-medium mb-2">
                {activeFilter === 'all' ? 'No notifications' : `No ${activeFilter} notifications`}
              </div>
              <div className="text-sm">
                {activeFilter === 'all'
                  ? "You're all caught up!"
                  : 'Try switching to a different filter'}
              </div>
            </div>
          ) : (
            <div>
              {filteredNotifications.map((notification: StaffNotification) => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={markAsRead}
                  onDelete={deleteNotification}
                  onAction={handleNotificationAction}
                />
              ))}
            </div>
          )}
        </div>

        {/* Footer Stats */}
        {notifications.length > 0 && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex justify-between text-sm text-gray-600">
              <span>Total: {notifications.length} notifications</span>
              <span>Unread: {unreadCount}</span>
              <span>Filter: {filteredNotifications.length} shown</span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default StaffNotificationCenter
