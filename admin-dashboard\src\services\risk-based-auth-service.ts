// Risk-Based Authentication Service

export interface DeviceFingerprint {
  id: string
  userAgent: string
  screen: string
  timezone: string
  language: string
  platform: string
  trusted: boolean
  lastSeen: Date
}

export interface RiskAssessment {
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  riskScore: number
  factors: string[]
  allowedMethods: string[]
  requiresAdditionalAuth: boolean
  recommendations: string[]
}

class RiskBasedAuthService {
  async generateDeviceFingerprint(): Promise<DeviceFingerprint> {
    const fingerprint: DeviceFingerprint = {
      id: 'device_' + Math.random().toString(36).substr(2, 9),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
      screen: typeof screen !== 'undefined' ? `${screen.width}x${screen.height}` : 'unknown',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: typeof navigator !== 'undefined' ? navigator.language : 'en-US',
      platform: typeof navigator !== 'undefined' ? navigator.platform : 'unknown',
      trusted: false,
      lastSeen: new Date()
    }
    
    return fingerprint
  }

  async assessLoginRisk(
    deviceFingerprint: DeviceFingerprint,
    isNewDevice: boolean,
    isNewLocation: boolean,
    failedAttempts: number
  ): Promise<RiskAssessment> {
    let riskScore = 0
    const factors: string[] = []
    
    if (isNewDevice) {
      riskScore += 30
      factors.push('New device detected')
    }
    
    if (isNewLocation) {
      riskScore += 20
      factors.push('New location detected')
    }
    
    if (failedAttempts > 0) {
      riskScore += failedAttempts * 10
      factors.push(`${failedAttempts} failed attempts`)
    }

    const riskLevel = riskScore >= 70 ? 'critical' : 
                     riskScore >= 50 ? 'high' : 
                     riskScore >= 30 ? 'medium' : 'low'

    return {
      riskLevel,
      riskScore,
      factors,
      allowedMethods: ['webauthn', 'biometric', 'password'],
      requiresAdditionalAuth: riskLevel === 'high' || riskLevel === 'critical',
      recommendations: factors.length > 0 ? ['Enable 2FA', 'Verify device'] : []
    }
  }
}

export const riskBasedAuthService = new RiskBasedAuthService() 