import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { z } from 'zod'

// Validation schema for updates
const updateIngredientSchema = z.object({
  category_id: z.string().uuid().optional(),
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  price: z.number().min(0).optional(),
  cost: z.number().min(0).optional(),
  image_url: z.string().optional(),
  stock_quantity: z.number().min(0).optional(),
  min_stock_level: z.number().min(0).optional(),
  is_available: z.boolean().optional(),
  allergens: z.array(z.string()).optional(),
  nutritional_info: z.object({}).optional(),
  display_order: z.number().optional()
})

interface RouteParams {
  params: {
    id: string
  }
}

// GET /api/ingredients/[id] - Fetch a specific ingredient
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params
    const { searchParams } = new URL(request.url)
    const include_subcategories = searchParams.get('include_subcategories') === 'true'
    
    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json(
        { error: 'Invalid ingredient ID format' },
        { status: 400 }
      )
    }
    
    // First, get the basic ingredient data
    const { data, error } = await supabase
      .from('ingredients')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Ingredient not found' },
          { status: 404 }
        )
      }
      
      return NextResponse.json(
        { error: 'Failed to fetch ingredient' },
        { status: 500 }
      )
    }
    
    // If subcategories are requested, fetch them separately
    let subcategoryIngredients = null
    if (include_subcategories) {
      const { data: subcategoryData, error: subcategoryError } = await supabase
        .from('menu_item_ingredients')
        .select('id, quantity, subcategories(id, name_en, is_available)')
        .eq('ingredient_id', id)
      
      if (!subcategoryError) {
        subcategoryIngredients = subcategoryData
      }
    }
    

    
    // Add stock status
    const isLowStock = data.stock_quantity > 0 && 
                      data.min_stock_level > 0 && 
                      data.stock_quantity <= data.min_stock_level
    
    const isOutOfStock = data.stock_quantity === 0
    
    const responseData = {
      ...data,
      stock_status: {
        is_low_stock: isLowStock,
        is_out_of_stock: isOutOfStock,
        status: isOutOfStock ? 'out_of_stock' : isLowStock ? 'low_stock' : 'in_stock'
      },
      subcategories_using: subcategoryIngredients?.length || 0,
      ...(subcategoryIngredients && { subcategory_ingredients: subcategoryIngredients })
    }
    
    return NextResponse.json({ data: responseData })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/ingredients/[id] - Update a specific ingredient
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params
    const body = await request.json()
    
    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json(
        { error: 'Invalid ingredient ID format' },
        { status: 400 }
      )
    }
    
    // Validate request body
    const validationResult = updateIngredientSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }
    
    const updateData = validationResult.data
    
    // Check if ingredient exists
    const { data: existingIngredient, error: fetchError } = await supabase
      .from('ingredients')
      .select('id, name, stock_quantity, is_available')
      .eq('id', id)
      .single()
    
    if (fetchError || !existingIngredient) {
      return NextResponse.json(
        { error: 'Ingredient not found' },
        { status: 404 }
      )
    }
    
    // Check for name conflicts if name is being updated
    if (updateData.name) {
      const { data: conflictingIngredient } = await supabase
        .from('ingredients')
        .select('id, name')
        .neq('id', id)
        .ilike('name', updateData.name)
        .single()
      
      if (conflictingIngredient) {
        return NextResponse.json(
          { error: 'An ingredient with this name already exists' },
          { status: 400 }
        )
      }
    }
    
    // Handle stock quantity changes and auto-disable logic
    let shouldAutoDisableSubcategories = false
    if (updateData.stock_quantity !== undefined) {
      // If stock goes to 0 and ingredient was available, disable related subcategories
      shouldAutoDisableSubcategories = updateData.stock_quantity === 0 && existingIngredient.is_available
      
      // If stock goes to 0, also set ingredient as unavailable
      if (updateData.stock_quantity === 0) {
        updateData.is_available = false
      }
    }
    
    // Update ingredient
    const { data, error } = await supabase
      .from('ingredients')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single()
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to update ingredient' },
        { status: 500 }
      )
    }
    
    // Auto-disable subcategories if ingredient ran out of stock
    if (shouldAutoDisableSubcategories) {
      await autoDisableSubcategories(supabase, id)
    }
    
    // Add stock status to response
    const isLowStock = data.stock_quantity > 0 && 
                      data.min_stock_level > 0 && 
                      data.stock_quantity <= data.min_stock_level
    
    const isOutOfStock = data.stock_quantity === 0
    
    const responseData = {
      ...data,
      stock_status: {
        is_low_stock: isLowStock,
        is_out_of_stock: isOutOfStock,
        status: isOutOfStock ? 'out_of_stock' : isLowStock ? 'low_stock' : 'in_stock'
      }
    }
    
    return NextResponse.json({ 
      data: responseData,
      auto_disabled_subcategories: shouldAutoDisableSubcategories
    })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/ingredients/[id] - Delete a specific ingredient
export async function DELETE(
  _request: NextRequest,
  { params }: RouteParams
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params
    
    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json(
        { error: 'Invalid ingredient ID format' },
        { status: 400 }
      )
    }
    
    // Check if ingredient exists and is used in subcategories
    const { data: existingIngredient, error: fetchError } = await supabase
      .from('ingredients')
      .select(`
        id,
        name,
        menu_item_ingredients(
          subcategory_id,
          subcategories(
            id,
            name_en
          )
        )
      `)
      .eq('id', id)
      .single()
    
    if (fetchError || !existingIngredient) {
      return NextResponse.json(
        { error: 'Ingredient not found' },
        { status: 404 }
      )
    }
    
    // Check if ingredient is used in subcategories
    const subcategoriesUsing = existingIngredient.menu_item_ingredients?.length || 0
    if (subcategoriesUsing > 0) {
      const subcategoryNames = existingIngredient.menu_item_ingredients
        ?.map((item: any) => item.subcategories?.name_en)
        .filter(Boolean)
        .slice(0, 3) // Show first 3 items
      
      return NextResponse.json(
        { 
          error: 'Cannot delete ingredient used in subcategories',
          details: `This ingredient is used in ${subcategoriesUsing} subcategory(ies): ${subcategoryNames?.join(', ')}${subcategoriesUsing > 3 ? '...' : ''}. Please remove it from all subcategories first.`
        },
        { status: 400 }
      )
    }
    
    // Delete ingredient
    const { error } = await supabase
      .from('ingredients')
      .delete()
      .eq('id', id)
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to delete ingredient' },
        { status: 500 }
      )
    }
    
    return NextResponse.json(
      { 
        message: 'Ingredient deleted successfully',
        deleted_ingredient: {
          id: existingIngredient.id,
          name: existingIngredient.name
        }
      },
      { status: 200 }
    )
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to auto-disable subcategories when ingredient runs out
async function autoDisableSubcategories(supabase: any, ingredientId: string) {
  try {
    // Find all subcategories that use this ingredient
    const { data: subcategoryIngredients, error: fetchError } = await supabase
      .from('menu_item_ingredients')
      .select(`
        subcategory_id,
        subcategories(
          id,
          name_en,
          is_available
        )
      `)
      .eq('ingredient_id', ingredientId)
    
    if (fetchError || !subcategoryIngredients) {
      return
    }
    
    // Disable subcategories that are currently available
    const subcategoriesToDisable = subcategoryIngredients
      .filter((item: any) => item.subcategories?.is_available)
      .map((item: any) => item.subcategory_id)
    
    if (subcategoriesToDisable.length > 0) {
      const { error: updateError } = await supabase
        .from('subcategories')
        .update({ 
          is_available: false,
          updated_at: new Date().toISOString()
        })
        .in('id', subcategoriesToDisable)
      
      if (updateError) {
      } else {
      }
    }
  } catch (error) {
  }
}