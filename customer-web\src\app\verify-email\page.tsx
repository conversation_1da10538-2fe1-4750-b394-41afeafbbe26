import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { GlassCard, GlassButton } from '@/components/ui/glass-components';

export const metadata: Metadata = {
  title: 'Verify Email | Delicious Crepes & Waffles',
  description: 'Verify your email address to complete your registration.',
};

export default function VerifyEmailPage() {
  return (
    <main className="min-h-screen py-12 px-4 flex items-center justify-center">
      <div className="max-w-md w-full">
        <div className="text-center mb-8">
          <Link href="/" className="inline-block">
            <Image
              src="/images/logo.svg"
              alt="Delicious Crepes & Waffles"
              width={150}
              height={60}
              className="mx-auto"
            />
          </Link>
          <h1 className="text-3xl font-bold mt-6">Email Verification</h1>
        </div>

        <GlassCard>
          <div className="p-6 text-center">
            <div className="flex justify-center mb-6">
              <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8 text-green-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>

            <h2 className="text-xl font-semibold mb-2">Email Verified Successfully!</h2>
            <p className="text-muted-foreground mb-6">
              Your email has been verified successfully. You can now access all features of your
              account.
            </p>

            <div className="space-y-3">
              <GlassButton variant="primary" className="w-full">
                <Link href="/login">Sign In to Your Account</Link>
              </GlassButton>

              <GlassButton variant="outline" className="w-full">
                <Link href="/">Return to Home</Link>
              </GlassButton>
            </div>
          </div>
        </GlassCard>

        {/* Alternative state - could be controlled by a query parameter or state */}
        <div className="hidden">
          <GlassCard>
            <div className="p-6 text-center">
              <div className="flex justify-center mb-6">
                <div className="h-16 w-16 rounded-full bg-amber-100 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-8 w-8 text-amber-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                </div>
              </div>

              <h2 className="text-xl font-semibold mb-2">Verification Link Expired</h2>
              <p className="text-muted-foreground mb-6">
                The verification link has expired or is invalid. Please request a new verification
                email.
              </p>

              <div className="space-y-3">
                <GlassButton variant="primary" className="w-full">
                  Resend Verification Email
                </GlassButton>

                <GlassButton variant="outline" className="w-full">
                  <Link href="/login">Back to Login</Link>
                </GlassButton>
              </div>
            </div>
          </GlassCard>
        </div>

        <div className="mt-8 text-center">
          <p className="text-sm text-muted-foreground">
            Need help?{' '}
            <Link href="/contact" className="text-primary hover:underline">
              Contact Support
            </Link>
          </p>
        </div>
      </div>
    </main>
  );
}
