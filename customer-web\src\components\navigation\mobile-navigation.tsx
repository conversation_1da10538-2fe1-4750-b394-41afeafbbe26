'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Home, 
  Menu, 
  Heart, 
  User, 
  ChevronLeft, 
  ChevronRight,
  MoreHorizontal 
} from 'lucide-react';
import { CartIndicator } from '@/components/cart/cart-indicator';
import { cn } from '@/lib/utils';
import { GlassNavbar } from '@/components/ui/glass-components';

interface NavigationItem {
  href: string;
  label: string;
  icon: React.ReactNode;
  priority: number; // Lower number = higher priority
}

interface MobileNavigationProps {
  className?: string;
}

export function MobileNavigation({ className }: MobileNavigationProps) {
  const pathname = usePathname();
  const [visibleItems, setVisibleItems] = useState<NavigationItem[]>([]);
  const [hiddenItems, setHiddenItems] = useState<NavigationItem[]>([]);
  const [showOverflow, setShowOverflow] = useState(false);
  const navRef = useRef<HTMLDivElement>(null);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  const allNavigationItems: NavigationItem[] = [
    {
      href: '/',
      label: 'Home',
      icon: <Home className="w-5 h-5" />,
      priority: 1,
    },
    {
      href: '/menu',
      label: 'Menu',
      icon: <Menu className="w-5 h-5" />,
      priority: 2,
    },
    {
      href: '/cart',
      label: 'Cart',
      icon: <CartIndicator className="w-5 h-5" showAnimation={true} />,
      priority: 3,
    },
    {
      href: '/profile/favorites',
      label: 'Favorites',
      icon: <Heart className="w-5 h-5" />,
      priority: 4,
    },
    {
      href: '/profile',
      label: 'Profile',
      icon: <User className="w-5 h-5" />,
      priority: 5,
    },
  ];

  // Calculate visible items based on screen width
  useEffect(() => {
    const calculateVisibleItems = () => {
      const screenWidth = window.innerWidth;
      let maxItems = 5; // Default for larger screens
      
      if (screenWidth < 320) {
        maxItems = 3;
      } else if (screenWidth < 375) {
        maxItems = 4;
      } else if (screenWidth < 414) {
        maxItems = 5;
      }
      
      // Sort by priority and split into visible/hidden
      const sortedItems = [...allNavigationItems].sort((a, b) => a.priority - b.priority);
      const visible = sortedItems.slice(0, maxItems - (sortedItems.length > maxItems ? 1 : 0));
      const hidden = sortedItems.slice(visible.length);
      
      setVisibleItems(visible);
      setHiddenItems(hidden);
    };

    calculateVisibleItems();
    window.addEventListener('resize', calculateVisibleItems);
    
    return () => window.removeEventListener('resize', calculateVisibleItems);
  }, []);

  // Handle touch events for swipe gestures
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;
    
    if (isLeftSwipe || isRightSwipe) {
      // Handle swipe navigation
      const currentIndex = allNavigationItems.findIndex(item => 
        pathname === item.href || 
        (item.href !== '/' && pathname && pathname.startsWith(item.href))
      );
      
      if (currentIndex !== -1) {
        let nextIndex;
        if (isLeftSwipe && currentIndex < allNavigationItems.length - 1) {
          nextIndex = currentIndex + 1;
        } else if (isRightSwipe && currentIndex > 0) {
          nextIndex = currentIndex - 1;
        }
        
        if (nextIndex !== undefined) {
          // Programmatic navigation would go here
          // For now, we'll just provide visual feedback
          const nextItem = allNavigationItems[nextIndex];
          if (nextItem) {
            // You could trigger a router.push here
            console.log(`Swipe navigation to: ${nextItem.href}`);
          }
        }
      }
    }
  };

  const isActive = (href: string) => {
    return pathname === href || (href !== '/' && pathname && pathname.startsWith(href));
  };

  const renderNavigationItem = (item: NavigationItem, index: number) => {
    const active = isActive(item.href);
    
    return (
      <Link
        key={item.href}
        href={item.href}
        className={cn(
          'flex flex-col items-center justify-center space-y-1 px-2 py-2 rounded-lg transition-all duration-200',
          'min-w-0 flex-1 relative group',
          'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2',
          active
            ? 'text-primary bg-primary/10 scale-105'
            : 'text-muted-foreground hover:text-primary hover:bg-primary/5 hover:scale-105'
        )}
        aria-label={`Navigate to ${item.label}`}
        role="tab"
        aria-selected={active}
      >
        {/* Active indicator */}
        {active && (
          <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full" />
        )}
        
        {/* Icon */}
        <div className="relative transition-transform duration-200 group-hover:scale-110">
          {item.icon}
        </div>
        
        {/* Label */}
        <span className="text-xs font-medium truncate max-w-full leading-tight">
          {item.label}
        </span>
        
        {/* Ripple effect on tap */}
        <div className="absolute inset-0 rounded-lg overflow-hidden">
          <div className="absolute inset-0 bg-primary/20 scale-0 rounded-full transition-transform duration-300 group-active:scale-100" />
        </div>
      </Link>
    );
  };

  return (
    <GlassNavbar 
      ref={navRef}
      className={cn(
        'fixed bottom-0 left-0 right-0 z-50 border-t backdrop-blur-md',
        'safe-area-inset-bottom', // Handle iPhone notch
        className
      )}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      role="tablist"
      aria-label="Main navigation"
    >
      <div className="max-w-7xl mx-auto px-2">
        <div className="flex items-center justify-between h-16 relative">
          {/* Swipe indicator */}
          <div className="absolute top-1 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-muted-foreground/30 rounded-full" />
          
          {/* Navigation Items */}
          <div className="flex items-center justify-around w-full space-x-1">
            {visibleItems.map((item, index) => renderNavigationItem(item, index))}
            
            {/* Overflow menu */}
            {hiddenItems.length > 0 && (
              <div className="relative">
                <button
                  onClick={() => setShowOverflow(!showOverflow)}
                  className={cn(
                    'flex flex-col items-center justify-center space-y-1 px-2 py-2 rounded-lg transition-all duration-200',
                    'min-w-0 flex-1 relative group',
                    'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2',
                    'text-muted-foreground hover:text-primary hover:bg-primary/5'
                  )}
                  aria-label="More navigation options"
                  aria-expanded={showOverflow}
                >
                  <MoreHorizontal className="w-5 h-5" />
                  <span className="text-xs font-medium">More</span>
                </button>
                
                {/* Overflow dropdown */}
                {showOverflow && (
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 bg-background/95 backdrop-blur-md border border-border rounded-lg shadow-lg p-2 min-w-[120px]">
                    {hiddenItems.map((item) => (
                      <Link
                        key={item.href}
                        href={item.href}
                        onClick={() => setShowOverflow(false)}
                        className={cn(
                          'flex items-center space-x-2 px-3 py-2 rounded-md transition-colors w-full',
                          isActive(item.href)
                            ? 'text-primary bg-primary/10'
                            : 'text-muted-foreground hover:text-primary hover:bg-primary/5'
                        )}
                      >
                        {item.icon}
                        <span className="text-sm font-medium">{item.label}</span>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Background overlay for overflow menu */}
      {showOverflow && (
        <div 
          className="fixed inset-0 z-[-1]" 
          onClick={() => setShowOverflow(false)}
          aria-hidden="true"
        />
      )}
    </GlassNavbar>
  );
}

export default MobileNavigation;