/// <reference types="cypress" />

describe('Order Confirmation', () => {
  beforeEach(() => {
    // Clear any existing data
    cy.clearAllCookies();
    cy.clearAllLocalStorage();
    cy.clearAllSessionStorage();
    
    // Set up API interceptors
    cy.intercept('GET', '/api/orders/ORD-12345', { fixture: 'orders/orders.json' }).as('getOrder');
    cy.intercept('POST', '/api/orders/ORD-12345/cancel', { statusCode: 200 }).as('cancelOrder');
    cy.intercept('GET', '/api/orders/ORD-12345/tracking', { fixture: 'orders/orders.json' }).as('getTracking');
    cy.intercept('POST', '/api/orders/ORD-12345/feedback', { statusCode: 200 }).as('submitFeedback');
    
    // Login as customer
    cy.loginAsCustomer();
  });

  describe('Order Confirmation Page Access', () => {
    it('should redirect to login if not authenticated', () => {
      cy.clearAllCookies();
      cy.visit('/order-confirmation/ORD-12345');
      
      cy.url().should('include', '/login');
      cy.get('[data-testid="redirect-message"]')
        .should('contain', 'Please login to view your order');
    });

    it('should show error for invalid order ID', () => {
      cy.intercept('GET', '/api/orders/INVALID-ORDER', {
        statusCode: 404,
        body: { error: 'Order not found' }
      }).as('getInvalidOrder');
      
      cy.visit('/order-confirmation/INVALID-ORDER');
      cy.wait('@getInvalidOrder');
      
      cy.get('[data-testid="order-not-found"]')
        .should('be.visible')
        .and('contain', 'Order not found');
      
      cy.get('[data-testid="back-to-orders"]')
        .should('be.visible')
        .and('contain', 'View All Orders');
    });

    it('should show error for unauthorized order access', () => {
      cy.intercept('GET', '/api/orders/ORD-12345', {
        statusCode: 403,
        body: { error: 'Access denied' }
      }).as('getUnauthorizedOrder');
      
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getUnauthorizedOrder');
      
      cy.get('[data-testid="access-denied"]')
        .should('be.visible')
        .and('contain', 'You do not have permission to view this order');
    });

    it('should display order confirmation page successfully', () => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
      
      // Check page title and heading
      cy.title().should('contain', 'Order Confirmation');
      cy.get('h1').should('contain', 'Order Confirmed');
      
      // Check main sections
      cy.get('[data-testid="order-success-message"]').should('be.visible');
      cy.get('[data-testid="order-details"]').should('be.visible');
      cy.get('[data-testid="order-items"]').should('be.visible');
      cy.get('[data-testid="order-summary"]').should('be.visible');
    });
  });

  describe('Order Success Message', () => {
    beforeEach(() => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
    });

    it('should display success message and order number', () => {
      cy.get('[data-testid="order-success-message"]').within(() => {
        cy.get('[data-testid="success-icon"]').should('be.visible');
        cy.get('[data-testid="success-title"]')
          .should('be.visible')
          .and('contain', 'Order Confirmed!');
        
        cy.get('[data-testid="order-number"]')
          .should('be.visible')
          .and('contain', 'ORD-12345');
        
        cy.get('[data-testid="success-description"]')
          .should('be.visible')
          .and('contain', 'Thank you for your order');
      });
    });

    it('should display estimated delivery time', () => {
      cy.get('[data-testid="estimated-delivery"]')
        .should('be.visible')
        .and('contain', 'Estimated delivery');
      
      cy.get('[data-testid="delivery-time"]')
        .should('be.visible')
        .and('contain', 'minutes');
    });

    it('should display order status', () => {
      cy.get('[data-testid="order-status"]')
        .should('be.visible')
        .and('contain', 'Confirmed');
      
      cy.get('[data-testid="status-description"]')
        .should('be.visible')
        .and('contain', 'Your order is being prepared');
    });
  });

  describe('Order Details Section', () => {
    beforeEach(() => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
    });

    it('should display order information', () => {
      cy.get('[data-testid="order-details"]').within(() => {
        cy.get('[data-testid="order-id"]')
          .should('be.visible')
          .and('contain', 'ORD-12345');
        
        cy.get('[data-testid="order-date"]')
          .should('be.visible')
          .and('contain', new Date().toLocaleDateString());
        
        cy.get('[data-testid="order-total"]')
          .should('be.visible')
          .and('contain', '$');
      });
    });

    it('should display delivery information', () => {
      cy.get('[data-testid="delivery-info"]').within(() => {
        cy.get('[data-testid="delivery-method"]')
          .should('be.visible')
          .and('contain', 'Delivery');
        
        cy.get('[data-testid="delivery-address"]')
          .should('be.visible')
          .and('contain', '123 Main St');
        
        cy.get('[data-testid="delivery-time"]')
          .should('be.visible');
      });
    });

    it('should display pickup information for pickup orders', () => {
      cy.fixture('orders/orders').then((orderData) => {
        const pickupOrder = {
          ...orderData.currentOrder,
          deliveryMethod: 'pickup',
          pickupLocation: 'Downtown Store',
          pickupTime: '2024-01-15T18:00:00Z'
        };
        
        cy.intercept('GET', '/api/orders/ORD-12345', {
          body: pickupOrder
        }).as('getPickupOrder');
      });
      
      cy.reload();
      cy.wait('@getPickupOrder');
      
      cy.get('[data-testid="pickup-info"]').within(() => {
        cy.get('[data-testid="pickup-method"]')
          .should('be.visible')
          .and('contain', 'Pickup');
        
        cy.get('[data-testid="pickup-location"]')
          .should('be.visible')
          .and('contain', 'Downtown Store');
        
        cy.get('[data-testid="pickup-time"]')
          .should('be.visible');
      });
    });

    it('should display payment information', () => {
      cy.get('[data-testid="payment-info"]').within(() => {
        cy.get('[data-testid="payment-method"]')
          .should('be.visible')
          .and('contain', 'Credit Card');
        
        cy.get('[data-testid="payment-details"]')
          .should('be.visible')
          .and('contain', '**** 1111');
        
        cy.get('[data-testid="payment-status"]')
          .should('be.visible')
          .and('contain', 'Paid');
      });
    });
  });

  describe('Order Items Section', () => {
    beforeEach(() => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
    });

    it('should display all order items', () => {
      cy.fixture('orders/orders').then((orderData) => {
        const items = orderData.currentOrder.items;
        
        cy.get('[data-testid="order-item"]').should('have.length', items.length);
        
        items.forEach((item, index) => {
          cy.get('[data-testid="order-item"]').eq(index).within(() => {
            cy.get('[data-testid="item-image"]').should('be.visible');
            cy.get('[data-testid="item-name"]').should('contain', item.name);
            cy.get('[data-testid="item-quantity"]').should('contain', `x${item.quantity}`);
            cy.get('[data-testid="item-price"]').should('contain', `$${item.price}`);
            cy.get('[data-testid="item-total"]').should('contain', `$${item.total}`);
          });
        });
      });
    });

    it('should display item customizations', () => {
      cy.get('[data-testid="order-item"]').first().within(() => {
        cy.get('[data-testid="item-customizations"]').should('be.visible');
        cy.get('[data-testid="customization"]').should('contain', 'Extra cheese');
      });
    });

    it('should display special instructions', () => {
      cy.get('[data-testid="order-item"]').first().within(() => {
        cy.get('[data-testid="special-instructions"]')
          .should('be.visible')
          .and('contain', 'No onions');
      });
    });

    it('should show dietary tags', () => {
      cy.get('[data-testid="order-item"]').first().within(() => {
        cy.get('[data-testid="dietary-tags"]').should('be.visible');
        cy.get('[data-testid="tag-vegetarian"]').should('be.visible');
      });
    });
  });

  describe('Order Summary Section', () => {
    beforeEach(() => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
    });

    it('should display order totals', () => {
      cy.get('[data-testid="order-summary"]').within(() => {
        cy.get('[data-testid="subtotal"]')
          .should('be.visible')
          .and('contain', 'Subtotal')
          .and('contain', '$');
        
        cy.get('[data-testid="tax-amount"]')
          .should('be.visible')
          .and('contain', 'Tax')
          .and('contain', '$');
        
        cy.get('[data-testid="delivery-fee"]')
          .should('be.visible')
          .and('contain', 'Delivery Fee')
          .and('contain', '$');
        
        cy.get('[data-testid="total-amount"]')
          .should('be.visible')
          .and('contain', 'Total')
          .and('contain', '$');
      });
    });

    it('should display discount if applied', () => {
      cy.fixture('orders/orders').then((orderData) => {
        const orderWithDiscount = {
          ...orderData.currentOrder,
          discount: {
            code: 'SAVE10',
            amount: 10.00
          }
        };
        
        cy.intercept('GET', '/api/orders/ORD-12345', {
          body: orderWithDiscount
        }).as('getOrderWithDiscount');
      });
      
      cy.reload();
      cy.wait('@getOrderWithDiscount');
      
      cy.get('[data-testid="discount-amount"]')
        .should('be.visible')
        .and('contain', 'SAVE10')
        .and('contain', '-$10.00');
    });

    it('should show tip amount if added', () => {
      cy.fixture('orders/orders').then((orderData) => {
        const orderWithTip = {
          ...orderData.currentOrder,
          tip: 5.00
        };
        
        cy.intercept('GET', '/api/orders/ORD-12345', {
          body: orderWithTip
        }).as('getOrderWithTip');
      });
      
      cy.reload();
      cy.wait('@getOrderWithTip');
      
      cy.get('[data-testid="tip-amount"]')
        .should('be.visible')
        .and('contain', 'Tip')
        .and('contain', '$5.00');
    });
  });

  describe('Order Tracking', () => {
    beforeEach(() => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
    });

    it('should display order tracking section', () => {
      cy.get('[data-testid="order-tracking"]').should('be.visible');
      
      cy.get('[data-testid="tracking-title"]')
        .should('be.visible')
        .and('contain', 'Track Your Order');
    });

    it('should show current order status', () => {
      cy.get('[data-testid="tracking-status"]').within(() => {
        cy.get('[data-testid="status-confirmed"]')
          .should('be.visible')
          .and('have.class', 'completed');
        
        cy.get('[data-testid="status-preparing"]')
          .should('be.visible')
          .and('have.class', 'active');
        
        cy.get('[data-testid="status-ready"]')
          .should('be.visible')
          .and('have.class', 'pending');
        
        cy.get('[data-testid="status-delivered"]')
          .should('be.visible')
          .and('have.class', 'pending');
      });
    });

    it('should update tracking status in real-time', () => {
      // Simulate status update
      cy.intercept('GET', '/api/orders/ORD-12345/tracking', {
        body: {
          status: 'ready',
          estimatedDelivery: '2024-01-15T19:30:00Z',
          updates: [
            { status: 'confirmed', timestamp: '2024-01-15T18:00:00Z' },
            { status: 'preparing', timestamp: '2024-01-15T18:15:00Z' },
            { status: 'ready', timestamp: '2024-01-15T19:00:00Z' }
          ]
        }
      }).as('getUpdatedTracking');
      
      cy.get('[data-testid="refresh-tracking"]').click();
      cy.wait('@getUpdatedTracking');
      
      cy.get('[data-testid="status-ready"]')
        .should('have.class', 'active');
      
      cy.get('[data-testid="status-preparing"]')
        .should('have.class', 'completed');
    });

    it('should show estimated delivery time updates', () => {
      cy.get('[data-testid="estimated-delivery-update"]')
        .should('be.visible')
        .and('contain', 'Updated');
    });

    it('should display tracking timeline', () => {
      cy.get('[data-testid="tracking-timeline"]').should('be.visible');
      
      cy.get('[data-testid="timeline-item"]').should('have.length.at.least', 1);
      
      cy.get('[data-testid="timeline-item"]').first().within(() => {
        cy.get('[data-testid="timeline-time"]').should('be.visible');
        cy.get('[data-testid="timeline-status"]').should('be.visible');
        cy.get('[data-testid="timeline-description"]').should('be.visible');
      });
    });
  });

  describe('Order Actions', () => {
    beforeEach(() => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
    });

    it('should allow order cancellation within time limit', () => {
      cy.get('[data-testid="cancel-order-button"]')
        .should('be.visible')
        .and('not.be.disabled');
      
      cy.get('[data-testid="cancel-order-button"]').click();
      
      cy.get('[data-testid="cancel-confirmation-modal"]').should('be.visible');
      
      cy.get('[data-testid="cancel-reason-select"]').select('Changed my mind');
      cy.get('[data-testid="confirm-cancel-button"]').click();
      
      cy.wait('@cancelOrder');
      
      cy.checkNotification('Order cancelled successfully', 'success');
      
      cy.get('[data-testid="order-status"]')
        .should('contain', 'Cancelled');
    });

    it('should disable cancellation after time limit', () => {
      cy.fixture('orders/orders').then((orderData) => {
        const oldOrder = {
          ...orderData.currentOrder,
          createdAt: new Date(Date.now() - 20 * 60 * 1000).toISOString(), // 20 minutes ago
          canCancel: false
        };
        
        cy.intercept('GET', '/api/orders/ORD-12345', {
          body: oldOrder
        }).as('getOldOrder');
      });
      
      cy.reload();
      cy.wait('@getOldOrder');
      
      cy.get('[data-testid="cancel-order-button"]')
        .should('be.disabled');
      
      cy.get('[data-testid="cancel-disabled-message"]')
        .should('be.visible')
        .and('contain', 'Order cannot be cancelled');
    });

    it('should handle cancellation errors', () => {
      cy.intercept('POST', '/api/orders/ORD-12345/cancel', {
        statusCode: 400,
        body: { error: 'Order cannot be cancelled' }
      }).as('cancelError');
      
      cy.get('[data-testid="cancel-order-button"]').click();
      cy.get('[data-testid="cancel-reason-select"]').select('Changed my mind');
      cy.get('[data-testid="confirm-cancel-button"]').click();
      
      cy.wait('@cancelError');
      
      cy.checkNotification('Order cannot be cancelled', 'error');
    });

    it('should allow reordering', () => {
      cy.get('[data-testid="reorder-button"]')
        .should('be.visible')
        .and('contain', 'Reorder');
      
      cy.get('[data-testid="reorder-button"]').click();
      
      cy.url().should('include', '/cart');
      
      cy.checkNotification('Items added to cart', 'success');
    });

    it('should navigate to order history', () => {
      cy.get('[data-testid="view-all-orders"]')
        .should('be.visible')
        .and('contain', 'View All Orders');
      
      cy.get('[data-testid="view-all-orders"]').click();
      
      cy.url().should('include', '/orders');
    });

    it('should allow sharing order details', () => {
      cy.get('[data-testid="share-order-button"]')
        .should('be.visible')
        .and('contain', 'Share');
      
      cy.get('[data-testid="share-order-button"]').click();
      
      cy.get('[data-testid="share-options"]').should('be.visible');
      
      cy.get('[data-testid="copy-link-button"]').click();
      
      cy.checkNotification('Link copied to clipboard', 'success');
    });
  });

  describe('Customer Support', () => {
    beforeEach(() => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
    });

    it('should display customer support section', () => {
      cy.get('[data-testid="customer-support"]').should('be.visible');
      
      cy.get('[data-testid="support-title"]')
        .should('be.visible')
        .and('contain', 'Need Help?');
    });

    it('should provide contact options', () => {
      cy.get('[data-testid="contact-phone"]')
        .should('be.visible')
        .and('contain', 'Call us');
      
      cy.get('[data-testid="contact-email"]')
        .should('be.visible')
        .and('contain', 'Email us');
      
      cy.get('[data-testid="live-chat"]')
        .should('be.visible')
        .and('contain', 'Live Chat');
    });

    it('should open live chat', () => {
      cy.get('[data-testid="live-chat"]').click();
      
      cy.get('[data-testid="chat-widget"]').should('be.visible');
    });

    it('should show FAQ section', () => {
      cy.get('[data-testid="faq-section"]').should('be.visible');
      
      cy.get('[data-testid="faq-item"]').should('have.length.at.least', 3);
      
      cy.get('[data-testid="faq-item"]').first().click();
      
      cy.get('[data-testid="faq-answer"]').first().should('be.visible');
    });
  });

  describe('Order Feedback', () => {
    beforeEach(() => {
      // Set order as delivered
      cy.fixture('orders/orders').then((orderData) => {
        const deliveredOrder = {
          ...orderData.currentOrder,
          status: 'delivered',
          deliveredAt: new Date().toISOString()
        };
        
        cy.intercept('GET', '/api/orders/ORD-12345', {
          body: deliveredOrder
        }).as('getDeliveredOrder');
      });
      
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getDeliveredOrder');
    });

    it('should show feedback section for delivered orders', () => {
      cy.get('[data-testid="order-feedback"]').should('be.visible');
      
      cy.get('[data-testid="feedback-title"]')
        .should('be.visible')
        .and('contain', 'Rate Your Order');
    });

    it('should allow rating the order', () => {
      cy.get('[data-testid="rating-stars"]').should('be.visible');
      
      cy.get('[data-testid="star-5"]').click();
      
      cy.get('[data-testid="star-5"]')
        .should('have.class', 'selected');
      
      cy.get('[data-testid="rating-text"]')
        .should('contain', 'Excellent');
    });

    it('should allow writing feedback comments', () => {
      cy.get('[data-testid="feedback-comment"]')
        .should('be.visible')
        .type('Great food and fast delivery!');
      
      cy.get('[data-testid="feedback-comment"]')
        .should('have.value', 'Great food and fast delivery!');
    });

    it('should submit feedback successfully', () => {
      cy.get('[data-testid="star-5"]').click();
      cy.get('[data-testid="feedback-comment"]')
        .type('Excellent service and delicious food!');
      
      cy.get('[data-testid="submit-feedback-button"]').click();
      
      cy.wait('@submitFeedback');
      
      cy.checkNotification('Thank you for your feedback!', 'success');
      
      cy.get('[data-testid="feedback-submitted"]')
        .should('be.visible')
        .and('contain', 'Feedback submitted');
    });

    it('should validate feedback before submission', () => {
      cy.get('[data-testid="submit-feedback-button"]').click();
      
      cy.get('[data-testid="rating-error"]')
        .should('be.visible')
        .and('contain', 'Please select a rating');
    });
  });

  describe('Email Receipt', () => {
    beforeEach(() => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
    });

    it('should show email receipt section', () => {
      cy.get('[data-testid="email-receipt"]').should('be.visible');
      
      cy.get('[data-testid="receipt-message"]')
        .should('be.visible')
        .and('contain', 'Receipt sent to');
    });

    it('should allow resending receipt', () => {
      cy.intercept('POST', '/api/orders/ORD-12345/resend-receipt', {
        statusCode: 200
      }).as('resendReceipt');
      
      cy.get('[data-testid="resend-receipt-button"]').click();
      
      cy.wait('@resendReceipt');
      
      cy.checkNotification('Receipt sent successfully', 'success');
    });

    it('should handle receipt resend errors', () => {
      cy.intercept('POST', '/api/orders/ORD-12345/resend-receipt', {
        statusCode: 500,
        body: { error: 'Failed to send receipt' }
      }).as('resendError');
      
      cy.get('[data-testid="resend-receipt-button"]').click();
      
      cy.wait('@resendError');
      
      cy.checkNotification('Failed to send receipt', 'error');
    });
  });

  describe('Loading States', () => {
    it('should show loading state while fetching order', () => {
      cy.intercept('GET', '/api/orders/ORD-12345', (req) => {
        req.reply((res) => {
          res.delay(2000);
          res.send({ fixture: 'orders/orders.json' });
        });
      }).as('slowOrder');
      
      cy.visit('/order-confirmation/ORD-12345');
      
      cy.get('[data-testid="order-loading"]').should('be.visible');
      cy.get('[data-testid="loading-spinner"]').should('be.visible');
      
      cy.wait('@slowOrder');
      
      cy.get('[data-testid="order-loading"]').should('not.exist');
    });

    it('should show loading state for tracking updates', () => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
      
      cy.intercept('GET', '/api/orders/ORD-12345/tracking', (req) => {
        req.reply((res) => {
          res.delay(1000);
          res.send({ fixture: 'orders/orders.json' });
        });
      }).as('slowTracking');
      
      cy.get('[data-testid="refresh-tracking"]').click();
      
      cy.get('[data-testid="tracking-loading"]').should('be.visible');
      
      cy.wait('@slowTracking');
      
      cy.get('[data-testid="tracking-loading"]').should('not.exist');
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
    });

    it('should handle network errors gracefully', () => {
      cy.intercept('GET', '/api/orders/ORD-12345/tracking', {
        forceNetworkError: true
      }).as('networkError');
      
      cy.get('[data-testid="refresh-tracking"]').click();
      
      cy.wait('@networkError');
      
      cy.get('[data-testid="tracking-error"]')
        .should('be.visible')
        .and('contain', 'Unable to update tracking');
      
      cy.get('[data-testid="retry-tracking"]').should('be.visible');
    });

    it('should retry failed requests', () => {
      cy.intercept('GET', '/api/orders/ORD-12345/tracking', {
        statusCode: 500
      }).as('trackingError');
      
      cy.get('[data-testid="refresh-tracking"]').click();
      cy.wait('@trackingError');
      
      // Set up successful retry
      cy.intercept('GET', '/api/orders/ORD-12345/tracking', {
        fixture: 'orders/orders.json'
      }).as('trackingRetry');
      
      cy.get('[data-testid="retry-tracking"]').click();
      
      cy.wait('@trackingRetry');
      
      cy.get('[data-testid="tracking-error"]').should('not.exist');
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
    });

    it('should be accessible', () => {
      cy.checkAccessibility();
    });

    it('should support keyboard navigation', () => {
      cy.checkKeyboardNavigation();
      
      // Test tab order through action buttons
      cy.get('[data-testid="cancel-order-button"]').focus();
      cy.focused().should('have.attr', 'data-testid', 'cancel-order-button');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'reorder-button');
    });

    it('should have proper ARIA labels', () => {
      cy.get('[data-testid="order-status"]')
        .should('have.attr', 'aria-label')
        .and('contain', 'Order status');
      
      cy.get('[data-testid="tracking-timeline"]')
        .should('have.attr', 'role', 'list');
      
      cy.get('[data-testid="timeline-item"]')
        .should('have.attr', 'role', 'listitem');
    });

    it('should announce status updates to screen readers', () => {
      cy.get('[data-testid="status-announcement"]')
        .should('have.attr', 'aria-live', 'polite');
      
      cy.get('[data-testid="refresh-tracking"]').click();
      cy.wait('@getTracking');
      
      cy.get('[data-testid="status-announcement"]')
        .should('contain', 'Order status updated');
    });
  });

  describe('Responsive Design', () => {
    it('should work on different screen sizes', () => {
      cy.testResponsiveDesign();
      
      // Test mobile layout
      cy.viewport(375, 667);
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
      
      cy.get('[data-testid="order-confirmation-layout"]')
        .should('have.css', 'flex-direction', 'column');
      
      cy.get('[data-testid="order-actions"]')
        .should('have.css', 'flex-direction', 'column');
      
      // Test tablet layout
      cy.viewport(768, 1024);
      cy.get('[data-testid="order-details"]')
        .should('have.css', 'grid-template-columns')
        .and('match', /1fr/);
      
      // Test desktop layout
      cy.viewport(1280, 720);
      cy.get('[data-testid="order-details"]')
        .should('have.css', 'grid-template-columns')
        .and('match', /2fr 1fr/);
    });

    it('should optimize for mobile touch interactions', () => {
      cy.viewport(375, 667);
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
      
      cy.get('[data-testid="cancel-order-button"]')
        .should('have.css', 'min-height', '44px');
      
      cy.get('[data-testid="reorder-button"]')
        .should('have.css', 'min-height', '44px');
    });
  });

  describe('Performance', () => {
    it('should load order confirmation page quickly', () => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.measurePageLoadTime();
    });

    it('should optimize images', () => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
      cy.checkImageOptimization();
    });

    it('should implement auto-refresh for tracking', () => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
      
      // Should auto-refresh tracking every 30 seconds
      cy.wait('@getTracking', { timeout: 35000 });
    });
  });

  describe('Security', () => {
    beforeEach(() => {
      cy.visit('/order-confirmation/ORD-12345');
      cy.wait('@getOrder');
    });

    it('should not expose sensitive payment information', () => {
      cy.get('body').should('not.contain', '****************');
      cy.get('body').should('not.contain', 'CVV');
      
      // Should only show masked card number
      cy.get('[data-testid="payment-details"]')
        .should('contain', '**** 1111');
    });

    it('should validate order ownership', () => {
      // This is handled by the API interceptor setup
      // In real implementation, the API should verify user owns the order
      cy.get('[data-testid="order-details"]').should('be.visible');
    });

    it('should use secure communication', () => {
      cy.get('[data-testid="refresh-tracking"]').click();
      
      cy.wait('@getTracking').then((interception) => {
        expect(interception.request.url).to.include('https://');
      });
    });
  });
});