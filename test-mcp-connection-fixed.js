// Simplified test script to verify Supabase connection and MCP functionality
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration from the shared config
const SUPABASE_CONFIG = {
  url: 'https://voiwzwyfnkzvcffuxpwl.supabase.co',
  anon<PERSON>ey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0MDU2MzIsImV4cCI6MjA2NDk4MTYzMn0.RvabQ6EGP1gBb0w8J8_VpaqqXgkpEFR7Nbbkmw11wNA',
  serviceRoleKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMTg0NjgzMywiZXhwIjoyMDQ3NDIyODMzfQ.qPWQJLa-C8vPOZBq9FjvC0yKjG6kN8N8jQZ9XGZ8Z0k'
};

async function testSupabaseConnection() {
  console.log('🔍 Testing Supabase connection...');
  
  try {
    // Test basic connection
    const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.anonKey);
    
    console.log('✅ Supabase client created successfully');
    
    // Test connection by trying to access a known table
    console.log('📋 Testing database connection...');
    
    // Try to access some known tables from the conversation history
    const knownTables = ['branches', 'orders', 'subcategories', 'customers', 'order_items'];
    let accessibleTables = [];
    
    for (const tableName of knownTables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);
        
        if (!error) {
          accessibleTables.push(tableName);
          console.log(`✅ Table '${tableName}' is accessible`);
        } else {
          console.log(`⚠️  Table '${tableName}' error: ${error.message}`);
        }
      } catch (err) {
        console.log(`❌ Table '${tableName}' failed: ${err.message}`);
      }
    }
    
    console.log(`📊 Accessible tables: ${accessibleTables.join(', ')}`);
    
    // Test if delivery_zones table exists
    console.log('🔍 Checking for delivery_zones table...');
    
    const { data: deliveryZones, error: dzError } = await supabase
      .from('delivery_zones')
      .select('id, name')
      .limit(1);
    
    if (dzError) {
      console.log('⚠️  delivery_zones table not found:', dzError.message);
      
      // Try to create the table using service role key
      console.log('🔧 Attempting to create delivery_zones table with service role...');
      
      const adminSupabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.serviceRoleKey);
      
      // Use direct SQL execution via REST API
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS public.delivery_zones (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name VARCHAR(255) NOT NULL,
          description TEXT,
          coordinates JSONB NOT NULL,
          delivery_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
          minimum_order_amount DECIMAL(10,2) DEFAULT 0.00,
          estimated_delivery_time_min INTEGER DEFAULT 30,
          estimated_delivery_time_max INTEGER DEFAULT 60,
          is_active BOOLEAN DEFAULT true,
          priority INTEGER DEFAULT 0,
          color VARCHAR(7) DEFAULT '#3B82F6',
          branch_id UUID,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW(),
          created_by UUID,
          updated_by UUID
        );
        
        ALTER TABLE public.delivery_zones ENABLE ROW LEVEL SECURITY;
        
        DROP POLICY IF EXISTS "Enable read access for all users" ON public.delivery_zones;
        CREATE POLICY "Enable read access for all users" ON public.delivery_zones
          FOR SELECT USING (true);
          
        DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.delivery_zones;
        CREATE POLICY "Enable insert for authenticated users" ON public.delivery_zones
          FOR INSERT WITH CHECK (auth.role() = 'authenticated');
      `;
      
      try {
        const response = await fetch(`${SUPABASE_CONFIG.url}/rest/v1/rpc/exec_sql`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': SUPABASE_CONFIG.serviceRoleKey,
            'Authorization': `Bearer ${SUPABASE_CONFIG.serviceRoleKey}`,
          },
          body: JSON.stringify({ sql: createTableSQL })
        });
        
        if (response.ok) {
          console.log('✅ delivery_zones table created successfully');
          
          // Test access again
          const { data: newCheck, error: newError } = await supabase
            .from('delivery_zones')
            .select('id')
            .limit(1);
            
          if (!newError) {
            console.log('✅ delivery_zones table is now accessible');
          } else {
            console.log('⚠️  delivery_zones table still not accessible:', newError.message);
          }
        } else {
          const errorText = await response.text();
          console.error('❌ Failed to create delivery_zones table:', errorText);
        }
      } catch (createError) {
        console.error('❌ Error creating delivery_zones table:', createError.message);
      }
    } else {
      console.log('✅ delivery_zones table exists and is accessible');
      console.log('📊 Current delivery zones count:', deliveryZones?.length || 0);
    }
    
    return accessibleTables.length > 0;
    
  } catch (error) {
    console.error('❌ Connection test failed:', error);
    return false;
  }
}

// Test MCP-like functionality
async function testMCPFunctionality() {
  console.log('\n🔧 Testing MCP-like functionality...');
  
  try {
    // Test basic REST API access (what MCP tools use)
    console.log('📋 Testing REST API access...');
    
    const response = await fetch(`${SUPABASE_CONFIG.url}/rest/v1/`, {
      method: 'HEAD',
      headers: {
        'apikey': SUPABASE_CONFIG.anonKey,
        'Authorization': `Bearer ${SUPABASE_CONFIG.anonKey}`,
      },
    });
    
    if (response.ok) {
      console.log('✅ REST API is accessible');
    } else {
      console.log('❌ REST API access failed:', response.status, response.statusText);
      return false;
    }
    
    // Simulate project info (what MCP would return)
    console.log('📋 Simulating project listing...');
    const projectInfo = {
      id: SUPABASE_CONFIG.url.match(/https:\/\/(.+)\.supabase\.co/)?.[1],
      name: 'The Small',
      region: 'eu-central-1',
      status: 'active'
    };
    console.log('✅ Project info:', projectInfo);
    
    return true;
    
  } catch (error) {
    console.error('❌ MCP functionality test failed:', error);
    return false;
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting Supabase and MCP connection tests...\n');
  
  const connectionTest = await testSupabaseConnection();
  const mcpTest = await testMCPFunctionality();
  
  console.log('\n📊 Test Results:');
  console.log(`Connection Test: ${connectionTest ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`MCP Functionality Test: ${mcpTest ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (connectionTest && mcpTest) {
    console.log('\n🎉 All tests passed! MCP tools should work correctly.');
    console.log('\n💡 If MCP tools are still failing, the issue might be:');
    console.log('   1. MCP server configuration');
    console.log('   2. Network connectivity');
    console.log('   3. Authentication/authorization issues');
    console.log('   4. MCP server restart needed');
  } else {
    console.log('\n⚠️  Some tests failed. MCP tools may have issues.');
    console.log('\n🔧 Troubleshooting steps:');
    console.log('   1. Check Supabase project status');
    console.log('   2. Verify API keys are correct');
    console.log('   3. Check network connectivity');
    console.log('   4. Restart MCP server if needed');
  }
}

// Check if @supabase/supabase-js is available
try {
  require('@supabase/supabase-js');
  runTests();
} catch (error) {
  console.error('❌ @supabase/supabase-js not found. Please install it first:');
  console.log('npm install @supabase/supabase-js');
}