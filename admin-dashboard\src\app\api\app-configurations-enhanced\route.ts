import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    const platform = searchParams.get('platform')
    const section = searchParams.get('section')
    const branchId = searchParams.get('branchId')

    let query = supabase
      .from('app_configurations_enhanced')
      .select('*')
      .order('platform, config_section, config_key')

    if (platform) {
      query = query.in('platform', [platform, 'both'])
    }

    if (section) {
      query = query.eq('config_section', section)
    }

    if (branchId) {
      query = query.eq('branch_id', branchId)
    } else {
      query = query.is('branch_id', null)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching app configurations:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const body = await request.json()
    const {
      platform,
      config_section,
      config_key,
      config_value,
      data_type,
      description,
      branch_id,
      version_constraint,
      is_feature_flag = false,
      rollout_percentage = 100
    } = body

    const { data, error } = await supabase
      .from('app_configurations_enhanced')
      .insert({
        platform,
        config_section,
        config_key,
        config_value,
        data_type,
        description,
        branch_id,
        version_constraint,
        is_feature_flag,
        rollout_percentage
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating app configuration:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const body = await request.json()
    const {
      id,
      config_value,
      description,
      version_constraint,
      is_feature_flag,
      rollout_percentage
    } = body

    if (!id) {
      return NextResponse.json({ error: 'ID is required' }, { status: 400 })
    }

    const { data, error } = await supabase
      .from('app_configurations_enhanced')
      .update({
        config_value,
        description,
        version_constraint,
        is_feature_flag,
        rollout_percentage,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating app configuration:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}