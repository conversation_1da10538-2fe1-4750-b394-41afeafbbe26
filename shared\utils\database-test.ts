// Database connection testing utility for all platforms
import { testSupabaseConnection, getSupabaseConfig, validateSupabaseConfig } from '../config/supabase-config';

export interface DatabaseTestResult {
  platform: string;
  connected: boolean;
  latency?: number;
  error?: string;
  configValid: boolean;
  configErrors?: string[];
  timestamp: string;
}

// Test database connection for a specific platform
export const testDatabaseConnection = async (platform: 'web' | 'mobile' | 'desktop' | 'server'): Promise<DatabaseTestResult> => {
  const config = getSupabaseConfig(platform);
  const validation = validateSupabaseConfig({
    url: config.url,
    anonKey: config.anonKey,
    serviceRoleKey: config.serviceRoleKey,
    projectRef: config.url.split('//')[1]?.split('.')[0] || '',
    region: 'eu-west-1'
  });

  let connectionTest: { success: boolean; error?: string; latency?: number } = { 
    success: false, 
    error: 'Configuration validation failed' 
  };
  
  if (validation.valid) {
    connectionTest = await testSupabaseConnection(config.url, config.anonKey);
  }

  return {
    platform,
    connected: connectionTest.success,
    latency: connectionTest.latency,
    error: connectionTest.error,
    configValid: validation.valid,
    configErrors: validation.errors,
    timestamp: new Date().toISOString()
  };
};

// Test all platform connections
export const testAllPlatformConnections = async (): Promise<DatabaseTestResult[]> => {
  const platforms: Array<'web' | 'mobile' | 'desktop' | 'server'> = ['web', 'mobile', 'desktop', 'server'];
  
  const results = await Promise.all(
    platforms.map(platform => testDatabaseConnection(platform))
  );

  return results;
};

// Generate connection status report
export const generateConnectionReport = (results: DatabaseTestResult[]): string => {
  const successful = results.filter(r => r.connected).length;
  const total = results.length;
  
  let report = `Database Connection Report\n`;
  report += `=========================\n`;
  report += `Generated: ${new Date().toISOString()}\n`;
  report += `Overall Status: ${successful}/${total} platforms connected\n\n`;

  results.forEach(result => {
    report += `${result.platform.toUpperCase()} Platform:\n`;
    report += `  Status: ${result.connected ? '✅ Connected' : '❌ Failed'}\n`;
    
    if (result.latency) {
      report += `  Latency: ${result.latency}ms\n`;
    }
    
    if (!result.configValid && result.configErrors) {
      report += `  Config Issues: ${result.configErrors.join(', ')}\n`;
    }
    
    if (result.error) {
      report += `  Error: ${result.error}\n`;
    }
    
    report += `\n`;
  });

  return report;
};

// Check if any migrations are pending (requires service role key)
export const checkMigrationStatus = async (): Promise<{
  applied: number;
  pending: string[];
  error?: string;
}> => {
  try {
    const config = getSupabaseConfig('server');
    
    if (!config.serviceRoleKey) {
      throw new Error('Service role key required for migration status check');
    }

    const response = await fetch(`${config.url}/rest/v1/supabase_migrations`, {
      headers: {
        'apikey': config.serviceRoleKey,
        'Authorization': `Bearer ${config.serviceRoleKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const migrations = await response.json();
    
    return {
      applied: migrations.length,
      pending: [], // Would need to compare with local migration files
    };
  } catch (error) {
    return {
      applied: 0,
      pending: [],
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// Verify essential tables exist
export const verifyEssentialTables = async (): Promise<{
  tablesFound: string[];
  missingTables: string[];
  error?: string;
}> => {
  const essentialTables = [
    'branches',
    'customers',
    'customer_addresses',
    'orders',
    'order_items',
    'menu_categories',
    'subcategories',
    'pos_terminals',
    'pos_configurations',
    'web_configurations',
    'app_configurations_enhanced'
  ];

  try {
    const config = getSupabaseConfig('server');
    
    if (!config.serviceRoleKey) {
      throw new Error('Service role key required for table verification');
    }

    // Query information_schema to get table list
    const response = await fetch(`${config.url}/rest/v1/rpc/get_table_list`, {
      method: 'POST',
      headers: {
        'apikey': config.serviceRoleKey,
        'Authorization': `Bearer ${config.serviceRoleKey}`,
        'Content-Type': 'application/json'
      } as HeadersInit
    });

    if (!response.ok) {
      // Fallback: try to query each table individually
      const tableChecks = await Promise.allSettled(
        essentialTables.map(async (table) => {
          const tableResponse = await fetch(`${config.url}/rest/v1/${table}?limit=1`, {
            headers: {
              'apikey': config.serviceRoleKey,
              'Authorization': `Bearer ${config.serviceRoleKey}`,
            } as HeadersInit
          });
          return { table, exists: tableResponse.ok };
        })
      );

      const results = tableChecks
        .filter((result): result is PromiseFulfilledResult<{table: string, exists: boolean}> => result.status === 'fulfilled')
        .map(result => result.value);

      const tablesFound = results.filter(r => r.exists).map(r => r.table);
      const missingTables = essentialTables.filter(table => !tablesFound.includes(table));

      return { tablesFound, missingTables };
    }

    const tableList = await response.json();
    const existingTables = tableList.map((t: any) => t.table_name);
    
    const tablesFound = essentialTables.filter(table => existingTables.includes(table));
    const missingTables = essentialTables.filter(table => !existingTables.includes(table));

    return { tablesFound, missingTables };
  } catch (error) {
    return {
      tablesFound: [],
      missingTables: essentialTables,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};