// Export all services from a central location
export { BaseService } from './BaseService';
export { DatabaseService } from './DatabaseService';
export { OrderService } from './OrderService';
export { StaffService } from './StaffService';
export { SyncService } from './SyncService';
export { SettingsService } from './SettingsService';
export { PaymentService } from './PaymentService';

// Export types from shared types for consistency
export type {
  Order,
  OrderItem,
  OrderStatus,
  OrderType,
  PaymentStatus,
  PaymentMethod
} from '../../shared/types/orders';

export type {
  User,
  LoginCredentials,
  LoginResult,
  AuthResult,
  StaffSession,
  StaffInfo
} from '../../shared/types/auth';

export type {
  SyncQueue,
  SyncResult,
  LocalSettings,
  POSLocalConfig,
  PaymentTransaction,
  PaymentReceipt,
  PaymentRefund
} from '../../shared/types/database';