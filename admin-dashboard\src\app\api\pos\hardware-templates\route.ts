import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Hardware configuration templates for common setups
    const templates = [
      {
        id: 'basic-setup',
        name: 'Basic POS Setup',
        description: 'Essential hardware for a small restaurant',
        hardware_types: ['printer', 'cash_drawer'],
        configurations: {
          printer: {
            type: 'network',
            ip: '*************',
            port: 9100,
            paper_size: '80mm',
            cut_type: 'full',
            encoding: 'utf8'
          },
          cash_drawer: {
            connection: 'printer',
            trigger_code: '1B7000',
            open_duration: 500
          }
        },
        estimated_cost: 450,
        setup_time_minutes: 30
      },
      {
        id: 'standard-setup',
        name: 'Standard Restaurant Setup',
        description: 'Complete setup for medium-sized restaurant',
        hardware_types: ['printer', 'cash_drawer', 'kitchen_printer', 'customer_display'],
        configurations: {
          printer: {
            type: 'network',
            ip: '*************',
            port: 9100,
            paper_size: '80mm',
            cut_type: 'full',
            encoding: 'utf8'
          },
          kitchen_printer: {
            type: 'network',
            ip: '*************',
            port: 9100,
            paper_size: '80mm',
            cut_type: 'partial',
            encoding: 'utf8'
          },
          cash_drawer: {
            connection: 'printer',
            trigger_code: '1B7000',
            open_duration: 500
          },
          customer_display: {
            type: 'usb',
            port: 'COM3',
            baud_rate: 9600,
            display_lines: 2,
            character_width: 20
          }
        },
        estimated_cost: 850,
        setup_time_minutes: 60
      },
      {
        id: 'premium-setup',
        name: 'Premium Restaurant Setup',
        description: 'Full-featured setup with all hardware options',
        hardware_types: ['printer', 'cash_drawer', 'kitchen_printer', 'customer_display', 'barcode_scanner', 'payment_terminal'],
        configurations: {
          printer: {
            type: 'network',
            ip: '*************',
            port: 9100,
            paper_size: '80mm',
            cut_type: 'full',
            encoding: 'utf8'
          },
          kitchen_printer: {
            type: 'network',
            ip: '*************',
            port: 9100,
            paper_size: '80mm',
            cut_type: 'partial',
            encoding: 'utf8'
          },
          cash_drawer: {
            connection: 'printer',
            trigger_code: '1B7000',
            open_duration: 500
          },
          customer_display: {
            type: 'usb',
            port: 'COM3',
            baud_rate: 9600,
            display_lines: 2,
            character_width: 20
          },
          barcode_scanner: {
            type: 'usb',
            port: 'COM4',
            scan_mode: 'continuous',
            beep_enabled: true
          },
          payment_terminal: {
            provider: 'stripe',
            terminal_id: 'tmr_1234567890',
            connection: 'ethernet',
            timeout: 30,
            auto_settle: true
          }
        },
        estimated_cost: 1500,
        setup_time_minutes: 120
      },
      {
        id: 'mobile-setup',
        name: 'Mobile/Tablet Setup',
        description: 'Lightweight setup for mobile POS',
        hardware_types: ['printer', 'payment_terminal'],
        configurations: {
          printer: {
            type: 'bluetooth',
            device_name: 'Star_TSP143III',
            paper_size: '58mm',
            cut_type: 'full',
            encoding: 'utf8'
          },
          payment_terminal: {
            provider: 'square',
            terminal_id: 'sq_1234567890',
            connection: 'bluetooth',
            timeout: 30,
            auto_settle: true
          }
        },
        estimated_cost: 350,
        setup_time_minutes: 15
      },
      {
        id: 'delivery-setup',
        name: 'Delivery/Takeout Setup',
        description: 'Optimized for delivery and takeout orders',
        hardware_types: ['printer', 'kitchen_printer', 'barcode_scanner'],
        configurations: {
          printer: {
            type: 'network',
            ip: '*************',
            port: 9100,
            paper_size: '80mm',
            cut_type: 'full',
            encoding: 'utf8'
          },
          kitchen_printer: {
            type: 'network',
            ip: '*************',
            port: 9100,
            paper_size: '80mm',
            cut_type: 'partial',
            encoding: 'utf8'
          },
          barcode_scanner: {
            type: 'usb',
            port: 'COM4',
            scan_mode: 'trigger',
            beep_enabled: true
          }
        },
        estimated_cost: 600,
        setup_time_minutes: 45
      }
    ]

    // Filter templates based on query parameters
    const { searchParams } = new URL(request.url)
    const hardwareType = searchParams.get('hardware_type')
    const maxCost = searchParams.get('max_cost')
    const maxSetupTime = searchParams.get('max_setup_time')

    let filteredTemplates = templates

    if (hardwareType) {
      filteredTemplates = filteredTemplates.filter(template => 
        template.hardware_types.includes(hardwareType)
      )
    }

    if (maxCost) {
      const maxCostNum = parseInt(maxCost)
      filteredTemplates = filteredTemplates.filter(template => 
        template.estimated_cost <= maxCostNum
      )
    }

    if (maxSetupTime) {
      const maxSetupTimeNum = parseInt(maxSetupTime)
      filteredTemplates = filteredTemplates.filter(template => 
        template.setup_time_minutes <= maxSetupTimeNum
      )
    }

    return NextResponse.json({
      templates: filteredTemplates,
      summary: {
        total_templates: filteredTemplates.length,
        hardware_types: [...new Set(filteredTemplates.flatMap(t => t.hardware_types))],
        cost_range: {
          min: Math.min(...filteredTemplates.map(t => t.estimated_cost)),
          max: Math.max(...filteredTemplates.map(t => t.estimated_cost))
        },
        setup_time_range: {
          min: Math.min(...filteredTemplates.map(t => t.setup_time_minutes)),
          max: Math.max(...filteredTemplates.map(t => t.setup_time_minutes))
        }
      },
      filters: {
        hardware_type: hardwareType,
        max_cost: maxCost,
        max_setup_time: maxSetupTime
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Hardware templates error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, description, hardware_types, configurations, estimated_cost, setup_time_minutes } = await request.json()

    if (!name || !hardware_types || !configurations) {
      return NextResponse.json(
        { error: 'name, hardware_types, and configurations are required' },
        { status: 400 }
      )
    }

    // In a real implementation, this would save to database
    const newTemplate = {
      id: `custom-${Date.now()}`,
      name,
      description: description || 'Custom hardware template',
      hardware_types,
      configurations,
      estimated_cost: estimated_cost || 0,
      setup_time_minutes: setup_time_minutes || 30,
      created_at: new Date().toISOString(),
      is_custom: true
    }

    return NextResponse.json({
      success: true,
      message: 'Hardware template created successfully',
      template: newTemplate,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Hardware templates POST error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
