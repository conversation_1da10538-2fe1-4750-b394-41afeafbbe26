import React, { memo } from 'react';
import { useTheme } from '../../contexts/theme-context';

interface OrderCardProps {
  order: any;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onDoubleClick?: (id: string) => void;
  orderIndex: number;
}

export const OrderCard = memo<OrderCardProps>(({ 
  order, 
  isSelected, 
  onSelect,
  onDoubleClick,
  orderIndex
}) => {
  const { resolvedTheme } = useTheme();

  const getTimeColorClass = (createdAt: string) => {
    const now = new Date();
    const orderTime = new Date(createdAt);
    const diffMinutes = Math.floor((now.getTime() - orderTime.getTime()) / (1000 * 60));
    
    if (diffMinutes <= 30) {
      return 'text-green-500 drop-shadow-[0_0_8px_rgba(34,197,94,0.8)]';
    } else if (diffMinutes <= 40) {
      return 'text-orange-500 drop-shadow-[0_0_8px_rgba(249,115,22,0.8)]';
    } else {
      return 'text-red-500 drop-shadow-[0_0_8px_rgba(239,68,68,0.8)]';
    }
  };

  const getElapsedMinutes = (createdAt: string) => {
    const now = new Date();
    const orderTime = new Date(createdAt);
    return Math.floor((now.getTime() - orderTime.getTime()) / (1000 * 60));
  };

  const formatOrderNumber = (index: number) => {
    return `#${String(index + 1).padStart(2, '0')}`;
  };

  // Icon component for order type
  const OrderTypeIcon = ({ orderType }: { orderType: string }) => {
    const iconColor = resolvedTheme === 'light' ? '#374151' : '#D1D5DB'; // gray-700 / gray-300
    const iconSize = 20;
    
    if (orderType === 'delivery') {
      // Delivery scooter icon - neon effect only in dark theme
      return (
        <svg 
          width={iconSize + 4} 
          height={iconSize + 4} 
          viewBox="0 0 100 100" 
          fill="#FFFF00" 
          stroke="#FFFF00" 
          strokeWidth="3" 
          strokeLinecap="round" 
          strokeLinejoin="round"
          style={{
            filter: resolvedTheme === 'dark' 
              ? 'drop-shadow(0 0 8px #FFFF00) drop-shadow(0 0 16px #FFFF00) drop-shadow(0 0 24px #FFFF00) drop-shadow(0 0 32px #FFFF00)'
              : 'none',
          }}
        >
          {/* Delivery box */}
          <rect x="5" y="25" width="25" height="20" rx="3" />
          {/* Box to scooter connector */}
          <path d="M30 35h15" strokeWidth="4"/>
          {/* Scooter body/frame */}
          <path d="M45 35h15l5 10v15h-5" strokeWidth="4"/>
          {/* Seat */}
          <path d="M50 30h12" strokeWidth="6" strokeLinecap="round"/>
          {/* Handlebars */}
          <path d="M65 25v10" strokeWidth="4"/>
          <path d="M62 25h6" strokeWidth="4"/>
          {/* Front wheel */}
          <circle cx="75" cy="65" r="12" strokeWidth="4"/>
          {/* Rear wheel */}
          <circle cx="25" cy="65" r="12" strokeWidth="4"/>
          {/* Footrest */}
          <path d="M35 55h15" strokeWidth="3"/>
          {/* Windshield */}
          <path d="M65 20l5 15" strokeWidth="3"/>
        </svg>
      );
    } else {
      // Store icon for pickup (dine-in, takeaway)
      return (
        <svg 
          width={iconSize} 
          height={iconSize} 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke={iconColor} 
          strokeWidth="2" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        >
          <path d="m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7"/>
          <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
          <path d="M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4"/>
          <path d="M2 7h20"/>
          <path d="M22 7v3a2 2 0 0 1-2 2v0a2.18 2.18 0 0 1-2-2v0a2.18 2.18 0 0 1-2 2v0a2.18 2.18 0 0 1-2-2v0a2.18 2.18 0 0 1-2 2v0a2.18 2.18 0 0 1-2-2v0a2.18 2.18 0 0 1-2 2v0a2 2 0 0 1-2-2V7"/>
        </svg>
      );
    }
  };

  return (
    <div 
      className={`relative rounded-full py-3 px-6 cursor-pointer transform transition-all duration-300 backdrop-blur-sm hover:scale-[1.02] hover:-translate-y-1 ${
        resolvedTheme === 'light'
          ? 'bg-gray-50/90 border border-gray-200/60 hover:bg-gray-100/90 hover:border-gray-300/60 shadow-sm hover:shadow-lg'
          : 'bg-white/10 border border-white/20 hover:bg-white/15 hover:border-white/30 shadow-lg hover:shadow-xl'
      } ${
        (order.orderType === 'dine-in' || order.orderType === 'takeaway') 
          ? 'border-l-4 border-l-blue-400' 
          : 'border-l-4 border-l-yellow-400'
      } ${
        isSelected ? 'ring-2 ring-blue-400/50 scale-[1.02] shadow-lg' : ''
      }`}
      onClick={() => onSelect(order.id)}
      onDoubleClick={() => onDoubleClick?.(order.id)}
    >
      <div className="flex items-center justify-between">
        {/* Left Section - Order Number & Time */}
        <div className="flex items-center gap-4">
          <div className="flex flex-col gap-1">
            <span className={`text-lg font-bold min-w-[80px] ${
              resolvedTheme === 'light' ? 'text-gray-900' : 'text-white'
            }`}>
              {formatOrderNumber(orderIndex)}
            </span>
            <span className={`text-sm font-medium ${getTimeColorClass(order.createdAt)}`}>
              {getElapsedMinutes(order.createdAt)}m
            </span>
          </div>
        </div>
        
        {/* Center Section - Customer Info */}
        <div className="flex items-center gap-6 flex-1 mx-6">
          <div className="flex items-center gap-4">
            <div className={`text-sm font-medium ${
              resolvedTheme === 'light' ? 'text-gray-700' : 'text-white/80'
            }`}>
              {order.customerName}
            </div>
            {order.customerPhone && (
              <div className={`text-xs ${
                resolvedTheme === 'light' ? 'text-gray-500' : 'text-white/60'
              }`}>
                {order.customerPhone}
              </div>
            )}
          </div>
        </div>
        
        {/* Right Section - Price & Order Type Icon */}
        <div className="flex flex-col items-center gap-2">
          <span className={`text-xl font-bold ${
            resolvedTheme === 'light' ? 'text-gray-900' : 'text-white/90'
          }`}>
            ${order.totalAmount.toFixed(2)}
          </span>
          <OrderTypeIcon orderType={order.orderType} />
        </div>
      </div>
    </div>
  );
});

OrderCard.displayName = 'OrderCard';
export default OrderCard; 