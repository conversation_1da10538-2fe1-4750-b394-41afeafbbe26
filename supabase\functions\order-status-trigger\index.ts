/**
 * Supabase Edge Function: Order Status Trigger
 * Automatically sends notifications when order status changes
 * Triggered by database changes via webhooks
 */

/// <reference types="https://esm.sh/@supabase/functions-js/src/edge-runtime.d.ts" />

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

interface OrderStatusChange {
  type: 'INSERT' | 'UPDATE' | 'DELETE';
  table: string;
  schema: string;
  record: any;
  old_record?: any;
}

interface NotificationChannel {
  type: 'push' | 'email' | 'sms';
  enabled: boolean;
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

const getOrderDetails = async (supabase: any, orderId: string) => {
  const { data: order, error: orderError } = await supabase
    .from('orders')
    .select(`
      *,
      user_profiles!orders_user_id_fkey (
        full_name,
        email,
        phone,
        preferences
      ),
      order_items (
        quantity,
        price,
        subcategories (
          name,
          description
        )
      )
    `)
    .eq('id', orderId)
    .single();

  if (orderError) {
    throw new Error(`Failed to fetch order details: ${orderError.message}`);
  }

  return order;
};

const getUserNotificationPreferences = (userProfile: any): NotificationChannel[] => {
  const preferences = userProfile?.preferences || {};
  
  return [
    { type: 'push', enabled: preferences.pushNotifications !== false },
    { type: 'email', enabled: preferences.emailNotifications !== false },
    { type: 'sms', enabled: preferences.smsNotifications === true }, // SMS opt-in
  ];
};

const shouldSendNotification = (oldStatus: string, newStatus: string): boolean => {
  // Define which status changes should trigger notifications
  const notifiableTransitions = [
    'pending -> confirmed',
    'confirmed -> preparing',
    'preparing -> ready',
    'preparing -> out_for_delivery',
    'ready -> completed',
    'out_for_delivery -> delivered',
    'delivered -> completed',
    '* -> cancelled', // Any status to cancelled
  ];

  const transition = `${oldStatus} -> ${newStatus}`;
  const anyToCancelled = newStatus === 'cancelled';
  
  return notifiableTransitions.includes(transition) || anyToCancelled;
};

const getEstimatedDeliveryTime = (order: any): string | null => {
  if (!order.estimated_delivery_time) return null;
  
  const deliveryTime = new Date(order.estimated_delivery_time);
  const now = new Date();
  const diffMinutes = Math.ceil((deliveryTime.getTime() - now.getTime()) / (1000 * 60));
  
  if (diffMinutes <= 0) return 'Any moment now';
  if (diffMinutes < 60) return `${diffMinutes} minutes`;
  
  const hours = Math.floor(diffMinutes / 60);
  const minutes = diffMinutes % 60;
  
  if (minutes === 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
  return `${hours}h ${minutes}m`;
};

const formatOrderData = (order: any) => {
  return {
    orderNumber: order.id,
    customerName: order.user_profiles?.full_name || 'Customer',
    status: order.status,
    items: order.order_items?.map((item: any) => ({
      name: item.subcategories?.name || 'Item',
      quantity: item.quantity,
      price: item.price,
    })) || [],
    total: order.total,
    estimatedDeliveryTime: getEstimatedDeliveryTime(order),
    deliveryAddress: order.delivery_address ? 
      `${order.delivery_address.address_line_1}, ${order.delivery_address.city}` : null,
    trackingUrl: `${Deno.env.get('FRONTEND_URL') || 'https://app.thesmallcreperie.com'}/orders/${order.id}`,
  };
};

const sendNotification = async (channel: string, payload: any) => {
  const functionUrl = `${Deno.env.get('SUPABASE_URL')}/functions/v1/send-${channel}-notification`;
  const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
  
  const response = await fetch(functionUrl, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${serviceRoleKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to send ${channel} notification: ${error}`);
  }

  return await response.json();
};

const createStaffNotification = async (supabase: any, order: any, oldStatus: string) => {
  const staffNotification = {
    type: 'order_status_change',
    title: `Order #${order.id} Status Update`,
    message: `Order status changed from ${oldStatus} to ${order.status}`,
    order_id: order.id,
    target_roles: ['admin', 'manager'],
    priority: order.status === 'cancelled' ? 'high' : 'normal',
    metadata: {
      oldStatus,
      newStatus: order.status,
      customerName: order.user_profiles?.full_name,
      orderTotal: order.total,
    },
  };

  // Add specific staff notifications based on status
  if (order.status === 'confirmed') {
    staffNotification.target_roles = ['kitchen', 'admin'];
    staffNotification.type = 'new_order';
    staffNotification.title = `New Order #${order.id}`;
    staffNotification.message = `New order received from ${order.user_profiles?.full_name || 'Customer'}`;
  } else if (order.status === 'ready' && order.delivery_method === 'delivery') {
    staffNotification.target_roles = ['delivery', 'admin'];
    staffNotification.type = 'delivery_assigned';
    staffNotification.title = `Delivery Assignment #${order.id}`;
    staffNotification.message = `Order ready for delivery to ${order.delivery_address?.city || 'customer'}`;
  } else if (order.status === 'cancelled') {
    staffNotification.target_roles = ['admin', 'manager', 'kitchen'];
    staffNotification.type = 'order_cancelled';
    staffNotification.title = `Order Cancelled #${order.id}`;
    staffNotification.message = `Order cancelled - ${order.cancellation_reason || 'No reason provided'}`;
  }

  try {
    await supabase
      .from('staff_notifications')
      .insert(staffNotification);
  } catch (error) {
    console.error('Failed to create staff notification:', error);
  }
};

const logNotificationAttempt = async (supabase: any, data: {
  userId: string;
  orderId: string;
  type: string;
  channels: string[];
  success: boolean;
  errors?: Record<string, string>;
}) => {
  try {
    await supabase
      .from('notification_attempts')
      .insert({
        user_id: data.userId,
        order_id: data.orderId,
        notification_type: data.type,
        channels_attempted: data.channels,
        success: data.success,
        errors: data.errors,
        attempted_at: new Date().toISOString(),
      });
  } catch (error) {
    console.error('Failed to log notification attempt:', error);
  }
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const payload: OrderStatusChange = await req.json();
    
    // Only process order table updates
    if (payload.table !== 'orders' || payload.type !== 'UPDATE') {
      return new Response(
        JSON.stringify({ message: 'Not an order update, skipping' }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const newOrder = payload.record;
    const oldOrder = payload.old_record;
    
    // Check if status actually changed
    if (!oldOrder || newOrder.status === oldOrder.status) {
      return new Response(
        JSON.stringify({ message: 'No status change detected, skipping' }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check if this status change should trigger notifications
    if (!shouldSendNotification(oldOrder.status, newOrder.status)) {
      return new Response(
        JSON.stringify({ message: 'Status change does not require notification, skipping' }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Get full order details with user information
    const order = await getOrderDetails(supabaseClient, newOrder.id);
    
    if (!order.user_profiles) {
      console.warn(`No user profile found for order ${newOrder.id}`);
      return new Response(
        JSON.stringify({ message: 'No user profile found, skipping customer notifications' }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Get user notification preferences
    const notificationChannels = getUserNotificationPreferences(order.user_profiles);
    const enabledChannels = notificationChannels.filter(channel => channel.enabled);
    
    if (enabledChannels.length === 0) {
      console.log(`No notification channels enabled for user ${order.user_id}`);
    }

    // Prepare order data for notifications
    const orderData = formatOrderData(order);
    const notificationResults: Record<string, any> = {};
    const errors: Record<string, string> = {};

    // Send notifications through enabled channels
    for (const channel of enabledChannels) {
      try {
        let payload: any = {
          userId: order.user_id,
          type: 'order_status_update',
          orderId: order.id,
          orderData,
        };

        switch (channel.type) {
          case 'push':
            payload = {
              ...payload,
              title: `Order #${order.id} Update`,
              body: `Your order is now ${newOrder.status.replace('_', ' ')}`,
            };
            break;
            
          case 'email':
            payload = {
              ...payload,
              to: order.user_profiles.email,
              subject: `Order Update - #${order.id}`,
            };
            break;
            
          case 'sms':
            if (order.user_profiles.phone) {
              payload = {
                ...payload,
                to: order.user_profiles.phone,
                message: `🥞 Order #${order.id} is now ${newOrder.status.replace('_', ' ')}. ${orderData.trackingUrl}`,
              };
            } else {
              console.warn(`No phone number for user ${order.user_id}, skipping SMS`);
              continue;
            }
            break;
        }

        const result = await sendNotification(channel.type, payload);
        notificationResults[channel.type] = result;
        
      } catch (error) {
        console.error(`Failed to send ${channel.type} notification:`, error);
        errors[channel.type] = error.message;
      }
    }

    // Create staff notification
    await createStaffNotification(supabaseClient, order, oldOrder.status);

    // Log the notification attempt
    await logNotificationAttempt(supabaseClient, {
      userId: order.user_id,
      orderId: order.id,
      type: 'order_status_update',
      channels: enabledChannels.map(c => c.type),
      success: Object.keys(errors).length === 0,
      errors: Object.keys(errors).length > 0 ? errors : undefined,
    });

    const successCount = Object.keys(notificationResults).length;
    const errorCount = Object.keys(errors).length;

    return new Response(
      JSON.stringify({
        success: true,
        message: `Order status notification processed: ${successCount} sent, ${errorCount} failed`,
        orderId: order.id,
        statusChange: `${oldOrder.status} -> ${newOrder.status}`,
        notificationResults,
        errors: errorCount > 0 ? errors : undefined,
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Error processing order status trigger:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});