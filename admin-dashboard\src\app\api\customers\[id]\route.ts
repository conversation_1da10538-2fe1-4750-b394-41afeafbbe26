import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params

    // Get customer details
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .select('*')
      .eq('id', id)
      .single()

    if (customerError) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }

    // Get customer addresses
    const { data: addresses, error: addressError } = await supabase
      .from('customer_addresses')
      .select('*')
      .eq('customer_id', id)
      .order('is_default', { ascending: false })

    if (addressError) {
    }

    // Get menu preferences
    const { data: menuPreferences, error: menuError } = await supabase
      .from('customer_menu_preferences')
      .select('*')
      .eq('customer_id', id)
      .order('order_count', { ascending: false })

    if (menuError) {
    }

    // Calculate enhanced fields
    const totalOrders = customer.total_orders || 0
    const totalSpent = totalOrders * 15 // Estimate average order value of €15
    const avgOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0
    const lastOrderDate = customer.updated_at // Use updated_at as proxy for last order
    
    // Determine loyalty tier
    const loyaltyTier = getLoyaltyTier(customer.loyalty_points || 0, totalSpent)

    const enhancedCustomer = {
      ...customer,
      total_spent: totalSpent,
      avg_order_value: avgOrderValue,
      last_order_date: lastOrderDate,
      loyalty_tier: loyaltyTier,
      addresses: addresses || [],
      menu_preferences: menuPreferences || []
    }

    return NextResponse.json({ data: enhancedCustomer })
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function getLoyaltyTier(points: number, totalSpent: number): string {
  if (points >= 10000 || totalSpent >= 1000) return 'Platinum'
  if (points >= 5000 || totalSpent >= 500) return 'Gold'
  if (points >= 1000 || totalSpent >= 100) return 'Silver'
  return 'Bronze'
}



export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServerSupabaseClient()
    const customerId = params.id
    const body = await request.json()
    
    const {
      first_name,
      last_name,
      email,
      phone,
      date_of_birth,
      is_active,
      preferences
    } = body
    
    // Build update object
    const updateData: any = {}
    
    if (first_name !== undefined) updateData.first_name = first_name
    if (last_name !== undefined) updateData.last_name = last_name
    if (email !== undefined) updateData.email = email
    if (phone !== undefined) updateData.phone = phone
    if (date_of_birth !== undefined) updateData.date_of_birth = date_of_birth
    if (is_active !== undefined) updateData.is_active = is_active
    if (preferences !== undefined) updateData.preferences = preferences
    
    // Update full name if first or last name changed
    if (first_name !== undefined || last_name !== undefined) {
      const { data: currentCustomer } = await supabase
        .from('user_profiles')
        .select('first_name, last_name')
        .eq('id', customerId)
        .single()
      
      const newFirstName = first_name !== undefined ? first_name : currentCustomer?.first_name
      const newLastName = last_name !== undefined ? last_name : currentCustomer?.last_name
      updateData.full_name = `${newFirstName || ''} ${newLastName || ''}`.trim()
    }
    
    updateData.updated_at = new Date().toISOString()
    
    // Check if email is being changed and if it already exists
    if (email !== undefined) {
      const { data: existingUser } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('email', email)
        .neq('id', customerId)
        .single()
      
      if (existingUser) {
        return NextResponse.json(
          { error: 'Customer with this email already exists' },
          { status: 409 }
        )
      }
    }
    
    // Update customer
    const { data: updatedCustomer, error: updateError } = await supabase
      .from('user_profiles')
      .update(updateData)
      .eq('id', customerId)
      .select()
      .single()
    
    if (updateError) {
      return NextResponse.json(
        { error: 'Failed to update customer' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      data: updatedCustomer,
      message: 'Customer updated successfully'
    })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params

    // Delete customer
    const { error } = await supabase
      .from('customers')
      .delete()
      .eq('id', id)

    if (error) {
      return NextResponse.json(
        { error: 'Failed to delete customer' },
        { status: 500 }
      )
    }

    return NextResponse.json({ message: 'Customer deleted successfully' })
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params
    const body = await request.json()

    const {
      name,
      ringer_name,
      email,
      phone,
      address,
      postal_code,
      notes
    } = body

    // Validation
    if (!name || !email || !phone) {
      return NextResponse.json(
        { error: 'Name, email, and phone number are required' },
        { status: 400 }
      )
    }

    // Update customer
    const { data: customer, error } = await supabase
      .from('customers')
      .update({
        name,
        ringer_name,
        email,
        phone,
        address,
        postal_code,
        notes,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      return NextResponse.json(
        { error: 'Failed to update customer' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      data: customer,
      message: 'Customer updated successfully'
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 