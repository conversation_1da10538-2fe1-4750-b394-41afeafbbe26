'use client';

import React from 'react';
import { Heart } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useFavorites } from './favorites-provider';
import { GlassButton } from '@/components/ui/glass-components';

interface FavoriteButtonProps {
  item: {
    id: string;
    name: string;
    price: number;
    image_url: string;
    category_id: string;
    description?: string;
  };
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'icon' | 'button';
  showText?: boolean;
}

export function FavoriteButton({ 
  item, 
  className, 
  size = 'md', 
  variant = 'icon',
  showText = false 
}: FavoriteButtonProps) {
  const { addToFavorites, removeFromFavorites, isFavorite } = useFavorites();
  const isItemFavorite = isFavorite(item.id);

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isItemFavorite) {
      removeFromFavorites(item.id);
    } else {
      addToFavorites(item);
    }
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const buttonSizes = {
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-3'
  };

  if (variant === 'button') {
    return (
      <GlassButton
        variant={isItemFavorite ? 'primary' : 'secondary'}
        size={size}
        onClick={handleToggleFavorite}
        className={cn('flex items-center gap-2', className)}
      >
        <Heart 
          className={cn(
            iconSizes[size],
            isItemFavorite ? 'fill-current' : ''
          )} 
        />
        {showText && (
          <span>
            {isItemFavorite ? 'Remove from Favorites' : 'Add to Favorites'}
          </span>
        )}
      </GlassButton>
    );
  }

  return (
    <button
      onClick={handleToggleFavorite}
      className={cn(
        'rounded-full bg-white/80 backdrop-blur-sm border border-white/20 transition-all duration-200 hover:bg-white/90 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary/50',
        buttonSizes[size],
        {
          'text-red-500': isItemFavorite,
          'text-gray-600 hover:text-red-500': !isItemFavorite,
        },
        className
      )}
      title={isItemFavorite ? 'Remove from favorites' : 'Add to favorites'}
    >
      <Heart 
        className={cn(
          iconSizes[size],
          'transition-all duration-200',
          {
            'fill-current': isItemFavorite,
            'hover:fill-current': !isItemFavorite
          }
        )} 
      />
    </button>
  );
}

// Specialized component for menu item cards
export function MenuItemFavoriteButton({ item, className }: { 
  item: FavoriteButtonProps['item']; 
  className?: string; 
}) {
  return (
    <div className={cn('absolute top-2 right-2 z-10', className)}>
      <FavoriteButton item={item} size="md" variant="icon" />
    </div>
  );
}