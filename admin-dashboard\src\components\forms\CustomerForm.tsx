'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Loader2 } from 'lucide-react'

interface CustomerFormData {
  name: string
  ringer_name: string
  phone: string
  email: string
  address: string
  postal_code: string
  notes: string
}

interface CustomerFormProps {
  initialData?: Partial<CustomerFormData>
  onSubmit: (data: CustomerFormData) => Promise<void>
  onCancel: () => void
  submitText?: string
  isLoading?: boolean
}

export function CustomerForm({
  initialData = {},
  onSubmit,
  onCancel,
  submitText = 'Save Customer',
  isLoading = false
}: CustomerFormProps) {
  const [formData, setFormData] = useState<CustomerFormData>({
    name: '',
    ringer_name: '',
    phone: '',
    email: '',
    address: '',
    postal_code: '',
    notes: '',
    ...initialData
  })

  const [errors, setErrors] = useState<Partial<CustomerFormData>>({})

  const validateForm = (): boolean => {
    const newErrors: Partial<CustomerFormData> = {}

    if (!formData.name?.trim()) {
      newErrors.name = 'Name is required'
    }

    if (!formData.phone?.trim()) {
      newErrors.phone = 'Phone number is required'
    } else if (!/^\d{10}$/.test(formData.phone.replace(/\D/g, ''))) {
      newErrors.phone = 'Please enter a valid 10-digit phone number'
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      await onSubmit(formData)
    } catch (error) {
      console.error('Form submission error:', error)
    }
  }

  const updateField = (field: keyof CustomerFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4" noValidate>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label
            htmlFor="customer-name"
            className="block text-sm font-medium text-white/80 mb-1"
          >
            Customer Name *
          </label>
          <Input
            id="customer-name"
            value={formData.name}
            onChange={(e) => updateField('name', e.target.value)}
            placeholder="Enter customer name"
            aria-required="true"
            aria-invalid={!!errors.name}
            aria-describedby={errors.name ? 'name-error' : undefined}
            className={errors.name ? 'border-red-500' : ''}
            disabled={isLoading}
          />
          {errors.name && (
            <p
              id="name-error"
              className="text-red-400 text-sm mt-1 flex items-center gap-1"
              role="alert"
            >
              <span aria-hidden="true">⚠</span>
              {errors.name}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Name on Ringer
          </label>
          <Input
            value={formData.ringer_name}
            onChange={(e) => updateField('ringer_name', e.target.value)}
            placeholder="Name for delivery calls"
            disabled={isLoading}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Phone Number *
          </label>
          <Input
            value={formData.phone}
            onChange={(e) => updateField('phone', e.target.value)}
            placeholder="10-digit phone number"
            className={errors.phone ? 'border-red-500' : ''}
            disabled={isLoading}
          />
          {errors.phone && (
            <p className="text-red-400 text-sm mt-1">{errors.phone}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Email Address
          </label>
          <Input
            type="email"
            value={formData.email}
            onChange={(e) => updateField('email', e.target.value)}
            placeholder="<EMAIL>"
            className={errors.email ? 'border-red-500' : ''}
            disabled={isLoading}
          />
          {errors.email && (
            <p className="text-red-400 text-sm mt-1">{errors.email}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Address
          </label>
          <Input
            value={formData.address}
            onChange={(e) => updateField('address', e.target.value)}
            placeholder="Street address"
            disabled={isLoading}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-white/80 mb-1">
            Postal Code
          </label>
          <Input
            value={formData.postal_code}
            onChange={(e) => updateField('postal_code', e.target.value)}
            placeholder="12345"
            disabled={isLoading}
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-white/80 mb-1">
          Notes
        </label>
        <Textarea
          value={formData.notes}
          onChange={(e) => updateField('notes', e.target.value)}
          placeholder="Additional notes about the customer"
          rows={3}
          disabled={isLoading}
        />
      </div>

      <div className="flex gap-3 justify-end pt-4 border-t border-white/10">
        <Button
          type="button"
          variant="ghost"
          onClick={onCancel}
          disabled={isLoading}
          className="text-white/60 hover:text-white hover:bg-white/10"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
          {submitText}
        </Button>
      </div>
    </form>
  )
}