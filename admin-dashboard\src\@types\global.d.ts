/// <reference types="jest" />
/// <reference types="@testing-library/jest-dom" />

declare global {
  namespace jest {
    interface Matchers<R = void> {
      // Testing Library Jest DOM matchers
      toBeInTheDocument(): R
      toHaveClass(className: string): R
      toBeDisabled(): R
      toHaveFocus(): R
      toHaveAttribute(attr: string, value?: string | RegExp | number): R
      toHaveTextContent(text: string | RegExp): R
      toHaveDisplayValue(value: string | RegExp | Array<string | RegExp>): R
      toBeChecked(): R
      toBeValid(): R
      toBeInvalid(): R
      toHaveDescription(text?: string | RegExp): R
      toHaveErrorMessage(text?: string | RegExp): R
      toHaveValue(value: string | string[] | number): R
      toHaveStyle(css: string | Record<string, any>): R
      toBeVisible(): R
      toBeEmptyDOMElement(): R
      toBeRequired(): R
      toHaveAccessibleDescription(text?: string | RegExp): R
      toHaveAccessibleName(text?: string | RegExp): R

      // Standard Jest matchers
      toBe(value: any): R
      toEqual(value: any): R
      toStrictEqual(value: any): R
      toBeNull(): R
      toBeUndefined(): R
      toBeDefined(): R
      toBeTruthy(): R
      toBeFalsy(): R
      toBeInstanceOf(constructor: any): R
      toHaveProperty(property: string, value?: any): R
      toMatchObject(obj: object): R
      toContain(item: any): R
      toContainEqual(item: any): R
      toHaveLength(length: number): R
      toThrow(error?: string | RegExp | Error | jest.Constructable): R
      toThrowError(error?: string | RegExp | Error | jest.Constructable): R
      toThrowErrorMatchingSnapshot(snapshotName?: string): R
      toThrowErrorMatchingInlineSnapshot(snapshot: string): R

      // Jest spy matchers
      toHaveBeenCalled(): R
      toHaveBeenCalledTimes(times: number): R
      toHaveBeenCalledWith(...args: any[]): R
      toHaveBeenLastCalledWith(...args: any[]): R
      toHaveBeenNthCalledWith(nthCall: number, ...args: any[]): R
      toHaveReturned(): R
      toHaveReturnedTimes(times: number): R
      toHaveReturnedWith(value: any): R
      toHaveLastReturnedWith(value: any): R
      toHaveNthReturnedWith(nthCall: number, value: any): R

      // Snapshot matchers
      toMatchSnapshot(propertyMatchers?: any, hint?: string): R
      toMatchInlineSnapshot(snapshot?: string): R
      toThrowErrorMatchingSnapshot(): R
      toThrowErrorMatchingInlineSnapshot(snapshot: string): R

      // Number matchers
      toBeCloseTo(num: number, numDigits?: number): R
      toBeGreaterThan(num: number): R
      toBeGreaterThanOrEqual(num: number): R
      toBeLessThan(num: number): R
      toBeLessThanOrEqual(num: number): R
      toBeNaN(): R

      // String matchers
      toMatch(regexp: string | RegExp): R
      toMatchObject(obj: object): R

      // Array matchers
      toContain(item: any): R
      toContainEqual(item: any): R
      toHaveLength(length: number): R

      // Promise matchers
      resolves: jest.Matchers<Promise<R>>
      rejects: jest.Matchers<Promise<R>>
    }

    interface Expect {
      objectContaining(obj: Record<string, any>): any
      arrayContaining(arr: readonly any[]): any
      stringContaining(str: string): any
      stringMatching(regexp: string | RegExp): any
      any(constructor: any): any
      anything(): any
      not: {
        objectContaining(obj: Record<string, any>): any
        arrayContaining(arr: readonly any[]): any
        stringContaining(str: string): any
        stringMatching(regexp: string | RegExp): any
        any(constructor: any): any
        anything(): any
      }
    }
  }

  interface ExpectStatic extends jest.Expect {}

  var expect: ExpectStatic
}

export {}