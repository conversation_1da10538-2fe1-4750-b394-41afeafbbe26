/// <reference types="cypress" />

describe('Menu Page - Comprehensive Testing', () => {
  const baseUrl = 'http://localhost:3001'
  
  beforeEach(() => {
    // Wait for server to be ready
    cy.visit(`${baseUrl}/menu`, { 
      timeout: 30000,
      failOnStatusCode: false 
    })
  })

  describe('Page Load and Basic Navigation', () => {
    it('should load the menu page without errors', () => {
      cy.url().should('include', '/menu')
      cy.get('[data-cy="menu-page"]').should('be.visible')
      
      // Check for critical elements
      cy.get('h1').should('contain', 'Enhanced Menu Management')
      cy.get('[data-cy="menu-tabs"]').should('be.visible')
    })

    it('should not have console errors on page load', () => {
      cy.window().then((win) => {
        cy.stub(win.console, 'error').as('consoleError')
      })
      
      cy.reload()
      cy.get('@consoleError').should('not.have.been.called')
    })

    it('should have proper page title and meta elements', () => {
      cy.title().should('not.be.empty')
      cy.get('head meta[name="description"]').should('exist')
    })

    it('should render in both light and dark themes', () => {
      // Test theme toggle if available
      cy.get('[data-cy="theme-toggle"]').then(($toggle) => {
        if ($toggle.length) {
          cy.wrap($toggle).click()
          cy.get('body').should('have.class', 'dark')
          cy.wrap($toggle).click()
          cy.get('body').should('not.have.class', 'dark')
        }
      })
    })
  })

  describe('Tab Navigation', () => {
    it('should navigate between all menu tabs', () => {
      const tabs = ['categories', 'ingredients', 'menu-items']
      
      tabs.forEach(tab => {
        cy.get(`[data-cy="tab-${tab}"]`).click()
        cy.get(`[data-cy="content-${tab}"]`).should('be.visible')
        cy.url().should('include', '/menu')
      })
    })

    it('should highlight active tab correctly', () => {
      cy.get('[data-cy="tab-categories"]').click()
      cy.get('[data-cy="tab-categories"]').should('have.class', 'active')
      
      cy.get('[data-cy="tab-ingredients"]').click()
      cy.get('[data-cy="tab-ingredients"]').should('have.class', 'active')
      cy.get('[data-cy="tab-categories"]').should('not.have.class', 'active')
    })
  })

  describe('Categories Tab', () => {
    beforeEach(() => {
      cy.get('[data-cy="tab-categories"]').click()
      cy.wait(2000) // Wait for data loading
    })

    it('should display categories statistics correctly', () => {
      cy.get('[data-cy="categories-stats"]').should('be.visible')
      cy.get('[data-cy="stat-total-categories"]').should('contain', 'Categories')
      cy.get('[data-cy="stat-featured-categories"]').should('contain', 'Featured')
      cy.get('[data-cy="stat-active-categories"]').should('contain', 'Active')
    })

    it('should handle empty state gracefully', () => {
      cy.get('[data-cy="categories-grid"]').within(() => {
        cy.get('[data-cy="empty-state"]').then(($emptyState) => {
          if ($emptyState.length) {
            cy.wrap($emptyState).should('be.visible')
            cy.wrap($emptyState).should('contain', 'No categories found')
          }
        })
      })
    })

    it('should display category cards with proper information', () => {
      cy.get('[data-cy="category-card"]').then(($cards) => {
        if ($cards.length) {
          cy.wrap($cards).first().within(() => {
            cy.get('[data-cy="category-name"]').should('be.visible')
            cy.get('[data-cy="category-type"]').should('be.visible')
            cy.get('[data-cy="category-actions"]').should('be.visible')
          })
        }
      })
    })

    it('should test search functionality', () => {
      cy.get('[data-cy="search-input"]').type('crepe')
      cy.get('[data-cy="category-card"]').should('have.length.lessThan', 10)
      
      cy.get('[data-cy="search-input"]').clear()
      cy.get('[data-cy="category-card"]').should('have.length.greaterThan', -1)
    })

    it('should test view mode toggle', () => {
      cy.get('[data-cy="view-toggle"]').click()
      cy.get('[data-cy="categories-grid"]').should('have.class', 'list-view')
      
      cy.get('[data-cy="view-toggle"]').click()
      cy.get('[data-cy="categories-grid"]').should('have.class', 'grid-view')
    })

    it('should test refresh functionality', () => {
      cy.get('[data-cy="refresh-button"]').click()
      cy.get('[data-cy="loading-spinner"]').should('be.visible')
      cy.get('[data-cy="loading-spinner"]').should('not.exist')
    })

    it('should test add new category button', () => {
      cy.get('[data-cy="add-new-button"]').click()
      cy.get('[data-cy="category-modal"]').should('be.visible')
      cy.get('[data-cy="modal-close"]').click()
      cy.get('[data-cy="category-modal"]').should('not.exist')
    })

    it('should test category actions', () => {
      cy.get('[data-cy="category-card"]').first().within(() => {
        cy.get('[data-cy="edit-button"]').should('be.visible')
        cy.get('[data-cy="delete-button"]').should('be.visible')
      })
    })
  })

  describe('Ingredients Tab', () => {
    beforeEach(() => {
      cy.get('[data-cy="tab-ingredients"]').click()
      cy.wait(2000) // Wait for data loading
    })

    it('should display ingredients statistics correctly', () => {
      cy.get('[data-cy="ingredients-stats"]').should('be.visible')
      cy.get('[data-cy="stat-total-ingredients"]').should('contain', 'Ingredients')
      cy.get('[data-cy="stat-available-ingredients"]').should('contain', 'Available')
      cy.get('[data-cy="stat-low-stock"]').should('contain', 'Low Stock')
      cy.get('[data-cy="stat-out-of-stock"]').should('contain', 'Out of Stock')
    })

    it('should display ingredient categories', () => {
      cy.get('[data-cy="ingredient-categories"]').should('be.visible')
      cy.get('[data-cy="ingredient-category"]').should('have.length.greaterThan', 0)
    })

    it('should display ingredient cards with proper information', () => {
      cy.get('[data-cy="ingredient-card"]').then(($cards) => {
        if ($cards.length) {
          cy.wrap($cards).first().within(() => {
            cy.get('[data-cy="ingredient-name"]').should('be.visible')
            cy.get('[data-cy="ingredient-price"]').should('be.visible')
            cy.get('[data-cy="ingredient-stock"]').should('be.visible')
            cy.get('[data-cy="stock-status"]').should('be.visible')
          })
        }
      })
    })

    it('should test ingredient search functionality', () => {
      cy.get('[data-cy="search-input"]').type('nutella')
      cy.get('[data-cy="ingredient-card"]').should('have.length.lessThan', 10)
      
      cy.get('[data-cy="search-input"]').clear()
    })

    it('should test availability filter', () => {
      cy.get('[data-cy="availability-filter"]').check()
      cy.get('[data-cy="ingredient-card"]').each(($card) => {
        cy.wrap($card).within(() => {
          cy.get('[data-cy="stock-status"]').should('not.contain', 'Out of Stock')
        })
      })
      
      cy.get('[data-cy="availability-filter"]').uncheck()
    })

    it('should test ingredient actions', () => {
      cy.get('[data-cy="ingredient-card"]').first().within(() => {
        cy.get('[data-cy="edit-button"]').should('be.visible')
        cy.get('[data-cy="availability-toggle"]').should('be.visible')
      })
    })
  })

  describe('Subcategories Tab', () => {
    beforeEach(() => {
      cy.get('[data-cy="tab-subcategories"]').click()
      cy.wait(2000) // Wait for data loading
    })

    it('should display subcategories statistics correctly', () => {
      cy.get('[data-cy="menu-items-stats"]').should('be.visible')
      cy.get('[data-cy="stat-total-items"]').should('contain', 'Subcategories')
      cy.get('[data-cy="stat-available-items"]').should('contain', 'Available')
      cy.get('[data-cy="stat-featured-items"]').should('contain', 'Featured')
      cy.get('[data-cy="stat-customizable-items"]').should('contain', 'Customizable')
    })

    it('should display category filters', () => {
      cy.get('[data-cy="category-filters"]').should('be.visible')
      cy.get('[data-cy="filter-all"]').should('be.visible')
      cy.get('[data-cy="category-filter"]').should('have.length.greaterThan', 0)
    })

    it('should display subcategory cards with proper information', () => {
      cy.get('[data-cy="subcategory-card"]').then(($cards) => {
        if ($cards.length) {
          cy.wrap($cards).first().within(() => {
            cy.get('[data-cy="subcategory-name"]').should('be.visible')
            cy.get('[data-cy="subcategory-price"]').should('be.visible')
            cy.get('[data-cy="subcategory-category"]').should('be.visible')
            cy.get('[data-cy="subcategory-prep-time"]').should('be.visible')
            cy.get('[data-cy="subcategory-calories"]').should('be.visible')
          })
        }
      })
    })

    it('should test category filtering', () => {
      cy.get('[data-cy="category-filter"]').first().click()
      cy.get('[data-cy="subcategory-card"]').should('have.length.greaterThan', -1)
      
      cy.get('[data-cy="filter-all"]').click()
    })

    it('should test subcategory search functionality', () => {
      cy.get('[data-cy="search-input"]').type('crepe')
      cy.get('[data-cy="subcategory-card"]').should('have.length.lessThan', 10)
      
      cy.get('[data-cy="search-input"]').clear()
    })

    it('should test subcategory actions', () => {
      cy.get('[data-cy="subcategory-card"]').first().within(() => {
        cy.get('[data-cy="edit-button"]').should('be.visible')
        cy.get('[data-cy="availability-toggle"]').should('be.visible')
      })
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle network errors gracefully', () => {
      cy.intercept('GET', '/api/menu_categories', { forceNetworkError: true }).as('networkError')
      cy.get('[data-cy="refresh-button"]').click()
      cy.wait('@networkError')
      
      cy.get('[data-cy="error-message"]').should('be.visible')
      cy.get('[data-cy="error-message"]').should('contain', 'connection')
    })

    it('should handle API errors gracefully', () => {
      cy.intercept('GET', '/api/menu_categories', { statusCode: 500 }).as('apiError')
      cy.get('[data-cy="refresh-button"]').click()
      cy.wait('@apiError')
      
      cy.get('[data-cy="error-message"]').should('be.visible')
    })

    it('should show loading states during data fetch', () => {
      cy.intercept('GET', '/api/menu_categories', { delay: 2000 }).as('slowResponse')
      cy.get('[data-cy="refresh-button"]').click()
      
      cy.get('[data-cy="loading-spinner"]').should('be.visible')
      cy.wait('@slowResponse')
      cy.get('[data-cy="loading-spinner"]').should('not.exist')
    })
  })

  describe('Performance and Accessibility', () => {
    it('should have reasonable load times', () => {
      const start = performance.now()
      cy.visit(`${baseUrl}/menu`)
      cy.get('[data-cy="menu-page"]').should('be.visible').then(() => {
        const loadTime = performance.now() - start
        expect(loadTime).to.be.lessThan(5000) // 5 seconds max
      })
    })

    it('should be keyboard navigable', () => {
      cy.get('body').tab()
      cy.focused().should('have.attr', 'tabindex', '0')
      
      cy.get('[data-cy="tab-categories"]').focus().should('be.focused')
      cy.get('[data-cy="tab-categories"]').type('{enter}')
      cy.get('[data-cy="content-categories"]').should('be.visible')
    })

    it('should have proper ARIA labels', () => {
      cy.get('[data-cy="search-input"]').should('have.attr', 'aria-label')
      cy.get('[data-cy="add-new-button"]').should('have.attr', 'aria-label')
      cy.get('[data-cy="menu-tabs"]').should('have.attr', 'role', 'tablist')
    })

    it('should have proper color contrast', () => {
      cy.get('[data-cy="menu-page"]').should('be.visible')
      cy.checkA11y(null, {
        rules: {
          'color-contrast': { enabled: true }
        }
      })
    })
  })

  describe('Responsive Design', () => {
    it('should work on mobile devices', () => {
      cy.viewport('iphone-6')
      cy.get('[data-cy="menu-page"]').should('be.visible')
      cy.get('[data-cy="menu-tabs"]').should('be.visible')
      cy.get('[data-cy="mobile-menu"]').should('be.visible')
    })

    it('should work on tablet devices', () => {
      cy.viewport('ipad-2')
      cy.get('[data-cy="menu-page"]').should('be.visible')
      cy.get('[data-cy="menu-tabs"]').should('be.visible')
    })

    it('should work on desktop', () => {
      cy.viewport(1920, 1080)
      cy.get('[data-cy="menu-page"]').should('be.visible')
      cy.get('[data-cy="menu-tabs"]').should('be.visible')
    })
  })

  describe('Data Validation and Edge Cases', () => {
    it('should handle empty responses gracefully', () => {
      cy.intercept('GET', '/api/menu_categories', []).as('emptyResponse')
      cy.get('[data-cy="refresh-button"]').click()
      cy.wait('@emptyResponse')
      
      cy.get('[data-cy="empty-state"]').should('be.visible')
      cy.get('[data-cy="empty-state"]').should('contain', 'No categories found')
    })

    it('should handle malformed data gracefully', () => {
      cy.intercept('GET', '/api/menu_categories', { malformed: 'data' }).as('malformedResponse')
      cy.get('[data-cy="refresh-button"]').click()
      cy.wait('@malformedResponse')
      
      cy.get('[data-cy="error-message"]').should('be.visible')
    })

    it('should validate required fields in forms', () => {
      cy.get('[data-cy="add-new-button"]').click()
      cy.get('[data-cy="category-modal"]').should('be.visible')
      
      cy.get('[data-cy="save-button"]').click()
      cy.get('[data-cy="validation-error"]').should('be.visible')
      cy.get('[data-cy="validation-error"]').should('contain', 'required')
    })
  })
})