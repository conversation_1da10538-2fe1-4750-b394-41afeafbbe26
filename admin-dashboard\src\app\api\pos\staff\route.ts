import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Loading POS staff via API...')
    
    // Use server-side client with service role key
    let supabase;
    try {
      supabase = createServerSupabaseClient()
      console.log('✅ Supabase server client created successfully')
    } catch (clientError) {
      console.error('❌ Error creating Supabase client:', clientError)
      return NextResponse.json({ error: 'Failed to create database client' }, { status: 500 })
    }
    
    // Fetch all roles first
    console.log('🔄 Fetching roles...')
    const { data: roles, error: rolesError } = await supabase
      .from('roles')
      .select('*')
    
    if (rolesError) {
      console.error('❌ Error fetching roles:', rolesError)
      return NextResponse.json({ 
        error: 'Failed to fetch roles', 
        details: rolesError.message,
        code: rolesError.code 
      }, { status: 500 })
    }
    
    console.log('📊 Roles fetched:', roles?.length || 0)
    
    // Fetch staff data with can_login_pos filter
    console.log('🔄 Fetching staff...')
    const { data: staff, error: staffError } = await supabase
      .from('staff')
      .select(`
        id,
        staff_code,
        first_name,
        last_name,
        email,
        role_id,
        is_active,
        can_login_pos,
        last_login_at,
        created_at
      `)
      .eq('can_login_pos', true)
      .order('first_name')
    
    if (staffError) {
      console.error('❌ Error fetching staff:', staffError)
      return NextResponse.json({ 
        error: 'Failed to fetch staff', 
        details: staffError.message,
        code: staffError.code 
      }, { status: 500 })
    }
    
    console.log('📊 Staff fetched:', staff?.length || 0)
    
    // Transform staff data with role information
    const transformedStaff = staff?.map(member => {
      const role = roles?.find(r => r.id === member.role_id)
      return {
        id: member.id,
        staff_code: member.staff_code,
        first_name: member.first_name,
        last_name: member.last_name,
        email: member.email,
        role: role ? {
          id: role.id,
          name: role.name,
          display_name: role.display_name || role.name
        } : undefined,
        is_active: member.is_active,
        can_login_pos: member.can_login_pos,
        last_login_at: member.last_login_at
      }
    }) || []
    
    console.log('✅ Transformed staff data:', transformedStaff.length)
    
    return NextResponse.json({ 
      success: true, 
      data: transformedStaff 
    })
    
  } catch (error) {
    console.error('❌ API Error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      }, 
      { status: 500 }
    )
  }
}