/**
 * Supabase Edge Function: Send SMS Notification
 * Handles sending SMS notifications for order updates via Twilio
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

interface SMSRequest {
  to: string;
  message: string;
  orderId?: string;
  userId?: string;
  type: string;
}

interface OrderSMSData {
  orderNumber: string;
  customerName: string;
  status: string;
  estimatedDeliveryTime?: string;
  trackingUrl?: string;
  total?: number;
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// SMS message templates
const getOrderStatusMessage = (data: OrderSMSData): string => {
  const statusMessages = {
    confirmed: `🥞 The Small Creperie: Your order #${data.orderNumber} has been confirmed! We're preparing your delicious crepes.`,
    preparing: `👨‍🍳 Your order #${data.orderNumber} is now being prepared by our chefs. ${data.estimatedDeliveryTime ? `ETA: ${data.estimatedDeliveryTime}` : ''}`,
    ready: `✅ Great news! Your order #${data.orderNumber} is ready for pickup at The Small Creperie!`,
    out_for_delivery: `🚗 Your order #${data.orderNumber} is on its way! ${data.estimatedDeliveryTime ? `ETA: ${data.estimatedDeliveryTime}` : ''} ${data.trackingUrl ? `Track: ${data.trackingUrl}` : ''}`,
    delivered: `🎉 Your order #${data.orderNumber} has been delivered! Enjoy your delicious crepes from The Small Creperie!`,
    cancelled: `❌ Your order #${data.orderNumber} has been cancelled. If you have questions, please contact us at +31 20 123 4567.`
  };

  return statusMessages[data.status as keyof typeof statusMessages] || 
    `📱 Order #${data.orderNumber} update: ${data.status}. Thank you for choosing The Small Creperie!`;
};

const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Add country code if missing (assuming Netherlands +31)
  if (cleaned.length === 9 && !cleaned.startsWith('31')) {
    return `+31${cleaned}`;
  }
  
  // Add + if missing
  if (!cleaned.startsWith('+')) {
    return `+${cleaned}`;
  }
  
  return cleaned;
};

const sendSMS = async (smsData: SMSRequest) => {
  const twilioAccountSid = Deno.env.get('TWILIO_ACCOUNT_SID');
  const twilioAuthToken = Deno.env.get('TWILIO_AUTH_TOKEN');
  const twilioFromNumber = Deno.env.get('TWILIO_FROM_NUMBER');
  
  if (!twilioAccountSid || !twilioAuthToken || !twilioFromNumber) {
    throw new Error('Twilio credentials are not properly configured');
  }

  const formattedTo = formatPhoneNumber(smsData.to);
  
  // Validate phone number format
  if (!/^\+\d{10,15}$/.test(formattedTo)) {
    throw new Error(`Invalid phone number format: ${formattedTo}`);
  }

  // Truncate message if too long (SMS limit is 160 characters for single SMS)
  let message = smsData.message;
  if (message.length > 160) {
    message = message.substring(0, 157) + '...';
  }

  const url = `https://api.twilio.com/2010-04-01/Accounts/${twilioAccountSid}/Messages.json`;
  
  const body = new URLSearchParams({
    To: formattedTo,
    From: twilioFromNumber,
    Body: message,
  });

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${btoa(`${twilioAccountSid}:${twilioAuthToken}`)}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: body.toString(),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to send SMS: ${error}`);
  }

  return await response.json();
};

const logNotification = async (supabase: any, data: {
  userId: string;
  type: string;
  channel: string;
  status: string;
  orderId?: string;
  errorMessage?: string;
  twilioSid?: string;
}) => {
  try {
    await supabase
      .from('notification_logs')
      .insert({
        user_id: data.userId,
        type: data.type,
        channel: data.channel,
        status: data.status,
        order_id: data.orderId,
        error_message: data.errorMessage,
        external_id: data.twilioSid,
        sent_at: new Date().toISOString(),
      });
  } catch (error) {
    console.error('Failed to log notification:', error);
  }
};

const checkUserSMSPreferences = async (supabase: any, userId: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('preferences')
      .eq('user_id', userId)
      .single();

    if (error || !data) {
      console.warn(`Could not fetch SMS preferences for user ${userId}:`, error);
      return false; // Default to not sending if we can't verify preferences
    }

    return data.preferences?.smsNotifications === true;
  } catch (error) {
    console.error('Error checking SMS preferences:', error);
    return false;
  }
};

const checkQuietHours = (preferences: any): boolean => {
  if (!preferences?.quietHours?.enabled) {
    return false;
  }

  const now = new Date();
  const currentTime = now.getHours() * 60 + now.getMinutes();
  
  const [startHour, startMin] = preferences.quietHours.startTime.split(':').map(Number);
  const [endHour, endMin] = preferences.quietHours.endTime.split(':').map(Number);
  
  const startTime = startHour * 60 + startMin;
  const endTime = endHour * 60 + endMin;
  
  // Handle overnight quiet hours (e.g., 22:00 to 08:00)
  if (startTime > endTime) {
    return currentTime >= startTime || currentTime <= endTime;
  }
  
  return currentTime >= startTime && currentTime <= endTime;
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { to, message, orderId, userId, type, orderData, skipPreferenceCheck } = await req.json();

    if (!to || !type) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: to, type' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check user SMS preferences unless explicitly skipped (for urgent notifications)
    if (userId && !skipPreferenceCheck) {
      const smsEnabled = await checkUserSMSPreferences(supabaseClient, userId);
      if (!smsEnabled) {
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: 'SMS notifications disabled for user',
            skipped: true
          }),
          { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      // Check quiet hours for non-urgent notifications
      if (type !== 'urgent') {
        const { data: userProfile } = await supabaseClient
          .from('user_profiles')
          .select('preferences')
          .eq('user_id', userId)
          .single();

        if (userProfile?.preferences && checkQuietHours(userProfile.preferences)) {
          return new Response(
            JSON.stringify({ 
              success: false, 
              error: 'Message not sent due to quiet hours',
              skipped: true
            }),
            { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          );
        }
      }
    }

    let smsMessage = message;

    // Generate SMS content based on type and order data
    if (type === 'order_status_update' && orderData) {
      smsMessage = getOrderStatusMessage(orderData);
    }

    // Send the SMS
    const result = await sendSMS({
      to,
      message: smsMessage,
      orderId,
      userId,
      type,
    });

    // Log successful notification
    if (userId) {
      await logNotification(supabaseClient, {
        userId,
        type,
        channel: 'sms',
        status: 'sent',
        orderId,
        twilioSid: result.sid,
      });
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        messageId: result.sid,
        message: 'SMS sent successfully',
        to: result.to,
        status: result.status
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error sending SMS:', error);

    // Log failed notification
    try {
      const { userId, type, orderId } = await req.json();
      if (userId) {
        const supabaseClient = createClient(
          Deno.env.get('SUPABASE_URL') ?? '',
          Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
        );
        
        await logNotification(supabaseClient, {
          userId,
          type,
          channel: 'sms',
          status: 'failed',
          orderId,
          errorMessage: error.message,
        });
      }
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }

    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});