'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface BreadcrumbProps {
  className?: string;
  customItems?: BreadcrumbItem[];
}

export function Breadcrumb({ className, customItems }: BreadcrumbProps) {
  const pathname = usePathname();

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    if (customItems) {
      return [{ label: 'Home', href: '/' }, ...customItems];
    }

    if (!pathname) {
      return [{ label: 'Home', href: '/' }];
    }

    const pathSegments = pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [{ label: 'Home', href: '/' }];

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      // Format segment label
      let label = segment.charAt(0).toUpperCase() + segment.slice(1);
      
      // Handle special cases
      switch (segment) {
        case 'sweet-crepes':
          label = 'Sweet Crepes';
          break;
        case 'savory-crepes':
          label = 'Savory Crepes';
          break;
        case 'waffles':
          label = 'Waffles';
          break;
        case 'beverages':
          label = 'Beverages';
          break;
        case 'sides':
          label = 'Sides';
          break;
        case 'specials':
          label = 'Specials';
          break;
        case 'menu':
          label = 'Menu';
          break;
        case 'cart':
          label = 'Shopping Cart';
          break;
        case 'profile':
          label = 'Profile';
          break;
        case 'orders':
          label = 'Orders';
          break;
        case 'checkout':
          label = 'Checkout';
          break;
        case 'favorites':
          label = 'Favorites';
          break;
        case 'addresses':
          label = 'Addresses';
          break;
        case 'payment-methods':
          label = 'Payment Methods';
          break;
        case 'settings':
          label = 'Settings';
          break;
        default:
          // Handle dynamic segments (like order IDs)
          if (segment.match(/^[a-f0-9-]{36}$/)) {
            label = 'Order Details';
          }
          break;
      }

      breadcrumbs.push({
        label,
        href: index === pathSegments.length - 1 ? undefined : currentPath,
      });
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  if (breadcrumbs.length <= 1) {
    return null; // Don't show breadcrumbs on homepage
  }

  return (
    <nav className={cn('flex items-center space-x-1 text-sm text-muted-foreground', className)}>
      {breadcrumbs.map((item, index) => {
        const isLast = index === breadcrumbs.length - 1;
        
        return (
          <React.Fragment key={index}>
            {index === 0 ? (
              <Link
                href={item.href!}
                className="flex items-center hover:text-primary transition-colors"
              >
                <Home className="w-4 h-4" />
                <span className="sr-only">{item.label}</span>
              </Link>
            ) : (
              <>
                <ChevronRight className="w-4 h-4" />
                {isLast || !item.href ? (
                  <span className="font-medium text-foreground">{item.label}</span>
                ) : (
                  <Link
                    href={item.href}
                    className="hover:text-primary transition-colors"
                  >
                    {item.label}
                  </Link>
                )}
              </>
            )}
          </React.Fragment>
        );
      })}
    </nav>
  );
}

export default Breadcrumb;