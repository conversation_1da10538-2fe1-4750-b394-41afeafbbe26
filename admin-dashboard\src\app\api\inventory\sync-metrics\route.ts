import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    const terminalId = searchParams.get('terminal_id')
    const timeRange = searchParams.get('time_range') || '24h'

    // Calculate time range
    const now = new Date()
    let startTime: Date
    
    switch (timeRange) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000)
        break
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    }

    // Get inventory sync operations (all sync operations for now)
    let syncQuery = supabase
      .from('pos_settings_sync_history')
      .select('*')
      .gte('synced_at', startTime.toISOString())
      .order('synced_at', { ascending: false })

    if (terminalId) {
      syncQuery = syncQuery.eq('terminal_id', terminalId)
    }

    const { data: syncOperations, error: syncError } = await syncQuery

    if (syncError) {
      console.error('Error fetching sync operations:', syncError)
      return NextResponse.json(
        { error: 'Failed to fetch sync operations' },
        { status: 500 }
      )
    }

    // Get current inventory status
    const { data: ingredients, error: ingredientsError } = await supabase
      .from('ingredients')
      .select('id, name, stock_quantity, min_stock_level, is_available, category_id, updated_at')

    if (ingredientsError) {
      console.error('Error fetching ingredients:', ingredientsError)
    }

    // Calculate sync metrics
    const totalSyncOps = syncOperations?.length || 0
    const successfulSyncs = syncOperations?.filter(op => op.sync_status === 'success').length || 0
    const failedSyncs = syncOperations?.filter(op => op.sync_status === 'failed').length || 0
    const pendingSyncs = syncOperations?.filter(op => op.sync_status === 'pending').length || 0
    const inProgressSyncs = syncOperations?.filter(op => op.sync_status === 'in_progress').length || 0

    // Calculate success rate
    const successRate = totalSyncOps > 0 ? (successfulSyncs / totalSyncOps) * 100 : 100

    // Calculate average sync time
    const completedSyncs = syncOperations?.filter(op => 
      op.sync_status === 'success' && op.sync_duration
    ) || []
    const avgSyncTime = completedSyncs.length > 0 
      ? completedSyncs.reduce((sum, op) => sum + (op.sync_duration || 0), 0) / completedSyncs.length 
      : 0

    // Calculate inventory health metrics
    const totalIngredients = ingredients?.length || 0
    const availableIngredients = ingredients?.filter(ing => ing.is_available).length || 0
    const lowStockIngredients = ingredients?.filter(ing => 
      ing.stock_quantity !== null && 
      ing.min_stock_level !== null && 
      ing.stock_quantity <= ing.min_stock_level
    ).length || 0
    const outOfStockIngredients = ingredients?.filter(ing => 
      ing.stock_quantity === 0
    ).length || 0

    // Calculate sync frequency (operations per hour)
    const timeRangeHours = (now.getTime() - startTime.getTime()) / (1000 * 60 * 60)
    const syncFrequency = timeRangeHours > 0 ? totalSyncOps / timeRangeHours : 0

    // Group sync operations by hour for trend analysis
    const syncTrends = []
    const hoursToAnalyze = Math.min(24, Math.ceil(timeRangeHours))
    
    for (let i = 0; i < hoursToAnalyze; i++) {
      const hourStart = new Date(now.getTime() - (i + 1) * 60 * 60 * 1000)
      const hourEnd = new Date(now.getTime() - i * 60 * 60 * 1000)
      
      const hourOps = syncOperations?.filter(op => {
        const opTime = new Date(op.synced_at)
        return opTime >= hourStart && opTime < hourEnd
      }) || []

      syncTrends.unshift({
        hour: hourStart.toISOString(),
        total_operations: hourOps.length,
        successful: hourOps.filter(op => op.sync_status === 'success').length,
        failed: hourOps.filter(op => op.sync_status === 'failed').length,
        avg_duration: hourOps.length > 0 
          ? hourOps.reduce((sum, op) => sum + (op.sync_duration || 0), 0) / hourOps.length 
          : 0
      })
    }

    // Calculate terminal-specific metrics if terminal_id is provided
    let terminalMetrics = null
    if (terminalId) {
      const { data: terminal } = await supabase
        .from('pos_terminals')
        .select('terminal_id, name, location, status, last_heartbeat, pending_updates')
        .eq('terminal_id', terminalId)
        .single()

      if (terminal) {
        const terminalSyncs = syncOperations?.filter(op => op.terminal_id === terminalId) || []
        terminalMetrics = {
          terminal_info: terminal,
          sync_operations: terminalSyncs.length,
          success_rate: terminalSyncs.length > 0 
            ? (terminalSyncs.filter(op => op.sync_status === 'success').length / terminalSyncs.length) * 100 
            : 100,
          last_sync: terminalSyncs[0]?.synced_at || null,
          pending_updates: terminal.pending_updates || 0,
          is_online: terminal.status === 'online'
        }
      }
    }

    // Calculate data freshness metrics
    const dataFreshness = ingredients?.map(ingredient => {
      const lastUpdated = new Date(ingredient.updated_at)
      const ageHours = (now.getTime() - lastUpdated.getTime()) / (1000 * 60 * 60)
      return {
        ingredient_id: ingredient.id,
        ingredient_name: ingredient.name,
        age_hours: ageHours,
        is_stale: ageHours > 24 // Consider data stale if older than 24 hours
      }
    }) || []

    const staleDataCount = dataFreshness.filter(item => item.is_stale).length
    const avgDataAge = dataFreshness.length > 0 
      ? dataFreshness.reduce((sum, item) => sum + item.age_hours, 0) / dataFreshness.length 
      : 0

    return NextResponse.json({
      sync_performance: {
        success_rate: Math.round(successRate * 10) / 10,
        avg_sync_time_ms: Math.round(avgSyncTime),
        sync_frequency_per_hour: Math.round(syncFrequency * 10) / 10,
        total_operations: totalSyncOps,
        successful_operations: successfulSyncs,
        failed_operations: failedSyncs,
        pending_operations: pendingSyncs,
        in_progress_operations: inProgressSyncs
      },
      inventory_health: {
        total_ingredients: totalIngredients,
        available_ingredients: availableIngredients,
        availability_rate: totalIngredients > 0 ? (availableIngredients / totalIngredients) * 100 : 100,
        low_stock_count: lowStockIngredients,
        out_of_stock_count: outOfStockIngredients,
        stock_health_score: totalIngredients > 0 
          ? Math.max(0, 100 - (lowStockIngredients * 10) - (outOfStockIngredients * 20)) 
          : 100
      },
      data_freshness: {
        avg_age_hours: Math.round(avgDataAge * 10) / 10,
        stale_data_count: staleDataCount,
        freshness_score: totalIngredients > 0 
          ? Math.max(0, 100 - (staleDataCount / totalIngredients) * 100) 
          : 100
      },
      sync_trends: syncTrends,
      terminal_metrics: terminalMetrics,
      filters: {
        terminal_id: terminalId,
        time_range: timeRange
      },
      timestamp: now.toISOString()
    })

  } catch (error) {
    console.error('Inventory sync metrics error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { action, terminal_id } = await request.json()

    if (!action) {
      return NextResponse.json(
        { error: 'action is required' },
        { status: 400 }
      )
    }

    let result = { success: false, message: '' }

    switch (action) {
      case 'reset_metrics':
        // Clear old sync history (older than 30 days)
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        
        let deleteQuery = supabase
          .from('pos_settings_sync_history')
          .delete()
          .in('sync_type', ['inventory_item', 'inventory_full', 'inventory'])
          .lt('synced_at', thirtyDaysAgo.toISOString())

        if (terminal_id) {
          deleteQuery = deleteQuery.eq('terminal_id', terminal_id)
        }

        const { error: deleteError } = await deleteQuery

        result.success = !deleteError
        result.message = deleteError 
          ? 'Failed to reset metrics' 
          : 'Metrics reset successfully'
        break

      case 'refresh_data':
        // Trigger a data refresh by updating all ingredient timestamps
        const { error: refreshError } = await supabase
          .from('ingredients')
          .update({ updated_at: new Date().toISOString() })
          .neq('id', '00000000-0000-0000-0000-000000000000') // Update all

        result.success = !refreshError
        result.message = refreshError 
          ? 'Failed to refresh data' 
          : 'Data refresh triggered successfully'
        break

      default:
        return NextResponse.json(
          { error: 'Invalid action. Must be one of: reset_metrics, refresh_data' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      ...result,
      action,
      terminal_id,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Inventory sync metrics POST error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
