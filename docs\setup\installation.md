# Installation Guide

## Prerequisites

Before installing The Small Creperie Admin Dashboard, ensure you have the following:

### System Requirements
- **Node.js**: Version 18.0 or higher
- **npm**: Version 8.0 or higher (or yarn equivalent)
- **Git**: For version control
- **Modern Browser**: Chrome, Firefox, Safari, or Edge (latest versions)
- **Internet Connection**: Required for Supabase and real-time features

### Hardware Requirements (Recommended)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space minimum
- **CPU**: Multi-core processor (2.0GHz+)
- **Network**: Stable internet connection for real-time sync

## Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/your-org/the-small-002.git
cd the-small-002
```

### 2. Install Dependencies

```bash
# Install admin dashboard dependencies
cd admin-dashboard
npm install

# Install POS system dependencies
cd ../pos-system
npm install

# Return to root directory
cd ..
```

### 3. Environment Configuration

Create environment files for each application:

#### Admin Dashboard (.env.local)
```bash
cd admin-dashboard
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_POS_URL=http://localhost:3002
NEXT_PUBLIC_WEB_URL=http://localhost:3003
NEXT_PUBLIC_MOBILE_URL=http://localhost:3004

# Security Configuration
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=http://localhost:3001

# Email Configuration (Optional)
EMAIL_FROM=<EMAIL>
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASSWORD=your-smtp-password
```

#### POS System (.env)
```bash
cd ../pos-system
cp .env.example .env
```

Edit `.env` with your configuration:
```env
# Supabase Configuration
REACT_APP_SUPABASE_URL=your-supabase-project-url
REACT_APP_SUPABASE_ANON_KEY=your-supabase-anon-key

# Application Configuration
REACT_APP_ADMIN_DASHBOARD_URL=http://localhost:3001
REACT_APP_API_URL=http://localhost:3001/api

# POS Configuration
REACT_APP_TERMINAL_ID=terminal-001
REACT_APP_BRANCH_ID=main-branch
```

### 4. Database Setup

The system uses Supabase as the backend database. Follow these steps:

1. **Create a Supabase Project**:
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Note your project URL and API keys

2. **Run Database Migrations**:
   ```bash
   cd admin-dashboard
   npm run db:migrate
   ```

3. **Seed Initial Data** (Optional):
   ```bash
   npm run db:seed
   ```

### 5. Start the Applications

#### Development Mode

Start all applications in development mode:

```bash
# Terminal 1: Admin Dashboard
cd admin-dashboard
npm run dev

# Terminal 2: POS System
cd pos-system
npm run dev
```

The applications will be available at:
- **Admin Dashboard**: http://localhost:3001
- **POS System**: http://localhost:3002

#### Production Mode

For production deployment:

```bash
# Build admin dashboard
cd admin-dashboard
npm run build
npm start

# Build POS system
cd pos-system
npm run build
npm run electron
```

## Post-Installation Setup

### 1. Initial Admin Account

1. Open the admin dashboard at http://localhost:3001
2. Click "Create Admin Account"
3. Fill in your restaurant details:
   - Restaurant name
   - Admin email and password
   - Primary location details
   - Contact information

### 2. Configure Restaurant Settings

1. Navigate to **Settings** → **Restaurant**
2. Complete your restaurant profile:
   - Business hours
   - Contact information
   - Tax rates
   - Payment methods

### 3. Setup POS Terminals

1. Go to **POS** → **Terminal Management**
2. Add your POS terminals:
   - Terminal ID
   - Location assignment
   - Hardware configuration

### 4. Configure Staff Accounts

1. Navigate to **Staff Management**
2. Add staff members:
   - Create user accounts
   - Assign roles and permissions
   - Set up POS access codes

## Verification

To verify your installation is working correctly:

### 1. Check Dashboard Access
- ✅ Admin dashboard loads at http://localhost:3001
- ✅ Login with admin credentials works
- ✅ Dashboard shows restaurant overview

### 2. Verify Database Connection
- ✅ Settings can be viewed and modified
- ✅ Changes are saved successfully
- ✅ Real-time updates work

### 3. Test POS Integration
- ✅ POS system loads at http://localhost:3002
- ✅ Settings sync from admin dashboard
- ✅ Real-time updates are received

### 4. Check System Health
- ✅ Navigate to **System** → **Health Monitoring**
- ✅ All systems show "Online" status
- ✅ No critical errors in logs

## Troubleshooting

### Common Installation Issues

**Node.js Version Issues**:
```bash
# Check Node.js version
node --version

# Update Node.js if needed
# Visit https://nodejs.org for latest version
```

**Permission Errors**:
```bash
# Fix npm permissions (macOS/Linux)
sudo chown -R $(whoami) ~/.npm

# Or use nvm for Node.js management
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install node
```

**Port Conflicts**:
```bash
# Check what's running on port 3001
lsof -i :3001

# Kill process if needed
kill -9 PID
```

**Database Connection Issues**:
1. Verify Supabase URL and keys in environment files
2. Check Supabase project status
3. Ensure database migrations have run successfully

### Getting Help

If you encounter issues during installation:

1. Check the [Common Issues](../support/common-issues.md) guide
2. Review the [FAQ](../support/faq.md)
3. Contact support with your error logs

## Next Steps

After successful installation:

1. Read the [Initial Configuration](./initial-configuration.md) guide
2. Complete the [Getting Started](../guides/getting-started.md) tutorial
3. Review [Security Guidelines](../best-practices/security.md)
4. Set up [Backup & Recovery](../best-practices/backup-recovery.md) 