import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { GlassCard, GlassButton } from '@/components/ui/glass-components';

export const metadata: Metadata = {
  title: 'Home | Delicious Crepes & Waffles',
  description:
    'Welcome to Creperie - Order delicious crepes, waffles, and more. Fast delivery and pickup options available.',
};

export default function HomePage() {
  return (
    <main className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="hero-section relative h-screen flex items-center justify-center text-center p-4">
        <div className="absolute inset-0 z-0">
          <Image
            src="/images/hero-background.svg"
            alt="Delicious crepes with fruits"
            fill
            priority
            className="object-cover"
          />

          <div className="absolute inset-0 bg-black/40" />
        </div>

        <div className="relative z-10 max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4 animate-fade-in">
            Delicious Crepes & Waffles
          </h1>
          <p className="text-xl md:text-2xl text-white mb-8 animate-fade-in animation-delay-200">
            Handcrafted with love, delivered to your door
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in animation-delay-400">
            <Link href="/menu">
              <GlassButton variant="primary" size="lg">
                View Menu
              </GlassButton>
            </Link>
            <Link href="/menu">
              <GlassButton variant="secondary" size="lg">
                Order Now
              </GlassButton>
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <section className="py-16 px-4 bg-gradient-to-b from-background to-background/80">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">Our Specialties</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <GlassCard className="p-0 overflow-hidden transition-transform hover:scale-105">
              <div className="relative h-48">
                <Image
                  src="/images/sweet-crepes.svg"
                  alt="Sweet Crepes"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2">Sweet Crepes</h3>
                <p className="text-muted-foreground mb-4">
                  Indulge in our delicious sweet crepes filled with Nutella, fruits, and more.
                </p>
                <Link href="/menu/sweet-crepes">
                  <GlassButton variant="crepe" size="sm" className="w-full">
                    Explore Sweet Crepes
                  </GlassButton>
                </Link>
              </div>
            </GlassCard>

            <GlassCard className="p-0 overflow-hidden transition-transform hover:scale-105">
              <div className="relative h-48">
                <Image
                  src="/images/savory-crepes.svg"
                  alt="Savory Crepes"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2">Savory Crepes</h3>
                <p className="text-muted-foreground mb-4">
                  Try our savory crepes with cheese, ham, vegetables, and special sauces.
                </p>
                <Link href="/menu/savory-crepes">
                  <GlassButton variant="chocolate" size="sm" className="w-full">
                    Explore Savory Crepes
                  </GlassButton>
                </Link>
              </div>
            </GlassCard>

            <GlassCard className="p-0 overflow-hidden transition-transform hover:scale-105">
              <div className="relative h-48">
                <Image src="/images/waffles.svg" alt="Waffles" fill className="object-cover" />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2">Waffles</h3>
                <p className="text-muted-foreground mb-4">
                  Enjoy our crispy waffles topped with ice cream, fruits, and sweet sauces.
                </p>
                <Link href="/menu/waffles">
                  <GlassButton variant="strawberry" size="sm" className="w-full">
                    Explore Waffles
                  </GlassButton>
                </Link>
              </div>
            </GlassCard>
          </div>
        </div>
      </section>

      {/* About Us Section */}
      <section className="py-16 px-4 bg-muted/30">
        <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-center gap-12">
          <div className="md:w-1/2">
            <h2 className="text-3xl font-bold mb-6">Our Story</h2>
            <p className="text-lg mb-4">
              Founded in 2010, Creperie has been serving delicious crepes and waffles made with the
              finest ingredients and traditional recipes.
            </p>
            <p className="text-lg mb-6">
              Our passion for quality and taste has made us a favorite destination for crepe lovers
              in Athens. We take pride in our handcrafted creations and friendly service.
            </p>
            <Link href="/about">
              <GlassButton variant="secondary">Learn More About Us</GlassButton>
            </Link>
          </div>
          <div className="md:w-1/2 relative h-80 w-full rounded-lg overflow-hidden">
            <Image src="/images/about-us.svg" alt="Our creperie" fill className="object-cover" />
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 px-4 bg-gradient-to-b from-background/80 to-background">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">What Our Customers Say</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <GlassCard>
              <div className="flex flex-col h-full">
                <div className="flex items-center text-amber-500 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <p className="text-lg mb-4 flex-grow">
                  "The best crepes I've ever had! The Nutella and strawberry crepe is simply divine.
                  Fast delivery and always hot!"
                </p>
                <div className="flex items-center mt-auto">
                  <div className="relative h-10 w-10 rounded-full overflow-hidden mr-3">
                    <Image
                      src="/images/testimonial-1.svg"
                      alt="Customer"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <p className="font-semibold">Maria K.</p>
                    <p className="text-sm text-muted-foreground">Loyal customer</p>
                  </div>
                </div>
              </div>
            </GlassCard>

            <GlassCard>
              <div className="flex flex-col h-full">
                <div className="flex items-center text-amber-500 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <p className="text-lg mb-4 flex-grow">
                  "Their savory crepes are amazing! The ham and cheese with special sauce is my
                  go-to lunch. Great value for money!"
                </p>
                <div className="flex items-center mt-auto">
                  <div className="relative h-10 w-10 rounded-full overflow-hidden mr-3">
                    <Image
                      src="/images/testimonial-2.svg"
                      alt="Customer"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <p className="font-semibold">Nikos P.</p>
                    <p className="text-sm text-muted-foreground">Regular customer</p>
                  </div>
                </div>
              </div>
            </GlassCard>

            <GlassCard>
              <div className="flex flex-col h-full">
                <div className="flex items-center text-amber-500 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <p className="text-lg mb-4 flex-grow">
                  "The app makes ordering so easy! I love getting notifications about my delivery.
                  And the waffles with ice cream are to die for!"
                </p>
                <div className="flex items-center mt-auto">
                  <div className="relative h-10 w-10 rounded-full overflow-hidden mr-3">
                    <Image
                      src="/images/testimonial-3.svg"
                      alt="Customer"
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <p className="font-semibold">Elena T.</p>
                    <p className="text-sm text-muted-foreground">New customer</p>
                  </div>
                </div>
              </div>
            </GlassCard>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-primary/10">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Order?</h2>
          <p className="text-xl mb-8">
            Satisfy your cravings with our delicious crepes and waffles. Order now for delivery or
            pickup!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/menu">
              <GlassButton variant="primary" size="lg">
                Browse Menu
              </GlassButton>
            </Link>
            <Link href="/order">
              <GlassButton variant="secondary" size="lg">
                Order Now
              </GlassButton>
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
