{"name": "creperie-pos-system", "version": "1.0.0", "description": "Point of Sale system for Creperie with offline capabilities", "main": "dist/main/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:main": "webpack --config webpack.main.config.js --mode development --watch", "dev:renderer": "webpack serve --config webpack.renderer.config.js --mode development", "build": "npm run build:main && npm run build:renderer", "build:main": "webpack --config webpack.main.config.js --mode production", "build:renderer": "webpack --config webpack.renderer.config.js --mode production", "start": "electron .", "start:debug": "electron . --remote-debugging-port=9222", "pack": "electron-builder --dir", "dist": "electron-builder"}, "keywords": ["electron", "react", "pos", "creperie", "offline", "supabase"], "author": "Creperie Team", "license": "MIT", "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/node": "^20.10.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "buffer": "^6.0.3", "compression-webpack-plugin": "^11.1.0", "concurrently": "^8.2.2", "crypto-browserify": "^3.12.1", "css-loader": "^6.8.1", "electron": "^28.3.3", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.6.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "stream-browserify": "^3.0.0", "style-loader": "^3.3.3", "ts-loader": "^9.5.1", "typescript": "5.8.3", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "autoprefixer": "^10.4.16", "better-sqlite3": "^9.2.2", "date-fns": "^2.30.0", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "lucide-react": "^0.294.0", "postcss": "^8.4.32", "postcss-loader": "^7.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.30.1", "tailwindcss": "^3.3.6", "url": "^0.11.4", "zustand": "^4.4.7"}, "build": {"appId": "com.creperie.pos", "productName": "Creperie POS", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "public/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}, "optionalDependencies": {"bufferutil": "^4.0.9", "utf-8-validate": "^5.0.10"}}