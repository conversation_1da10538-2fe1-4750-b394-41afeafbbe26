// Import the ElectronAPI interface from preload
import type { ElectronAPI } from '../../main/preload';

// Declare global Window interface
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

interface ElectronAPI {
  // Window controls
  minimize: () => void;
  maximize: () => void;
  close: () => void;

  // Settings
  getSettings: () => Promise<Record<string, unknown>>;
  updateSettings: (settings: Record<string, unknown>) => Promise<void>;

  // Database
  executeQuery: (query: string, params?: (string | number | boolean | null)[]) => Promise<unknown>;

  // Auth
  login: (credentials: LoginCredentials) => Promise<AuthResult>;
  logout: () => Promise<void>;

  // System
  getSystemInfo: () => Promise<SystemInfo>;

  // Environment
  getEnv: (key: string) => string | undefined;

  // Development helpers
  openDevTools: () => void;
  reload: () => void;

  // Sync Event Listeners
  onSettingsUpdate: (callback: (data: any) => void) => void;
  onStaffPermissionUpdate: (callback: (data: any) => void) => void;
  onHardwareConfigUpdate: (callback: (data: any) => void) => void;
  onRestartRequired: (callback: (data: any) => void) => void;
  onSyncError: (callback: (error: any) => void) => void;
  onSyncComplete: (callback: (data: any) => void) => void;

  // Sync Status Methods
  getSyncStatus: () => Promise<any>;
  onSyncStatus: (callback: (status: any) => void) => void;
  onNetworkStatus: (callback: (status: any) => void) => void;

  // Sync Action Methods
  requestRestart: () => Promise<void>;
  forceSync: () => Promise<void>;
  openSyncLogs: () => Promise<void>;

  // Event Listener Cleanup Methods
  removeSettingsUpdateListener: () => void;
  removeStaffPermissionUpdateListener: () => void;
  removeHardwareConfigUpdateListener: () => void;
  removeRestartRequiredListener: () => void;
  removeSyncErrorListener: () => void;
  removeSyncCompleteListener: () => void;
  removeSyncStatusListener: () => void;
  removeNetworkStatusListener: () => void;
}

interface LoginCredentials {
  username?: string;
  password?: string;
  pin?: string;
  staffId?: string;
}

interface AuthResult {
  success: boolean;
  user?: {
    id: string;
    name: string;
    role: string;
  };
  token?: string;
  error?: string;
}

interface SystemInfo {
  platform: string;
  version: string;
  arch: string;
  appVersion: string;
}

export {};