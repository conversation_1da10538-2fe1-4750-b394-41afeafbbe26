'use client';

import React from 'react';
import { useOrderType, type OrderType } from '../contexts/OrderTypeContext';

interface OrderTypeSelectorProps {
  className?: string;
}

export function OrderTypeSelector({ className = '' }: OrderTypeSelectorProps) {
  const { orderType, setOrderType } = useOrderType();

  const handleOrderTypeChange = (type: OrderType) => {
    setOrderType(type);
  };

  return (
    <div className={`flex bg-gray-100 rounded-lg p-1 ${className}`}>
      <button
        onClick={() => handleOrderTypeChange('pickup')}
        className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
          orderType === 'pickup'
            ? 'bg-white text-gray-900 shadow-sm'
            : 'text-gray-600 hover:text-gray-900'
        }`}
      >
        🥡 Pickup
      </button>
      <button
        onClick={() => handleOrderTypeChange('delivery')}
        className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
          orderType === 'delivery'
            ? 'bg-white text-gray-900 shadow-sm'
            : 'text-gray-600 hover:text-gray-900'
        }`}
      >
        🚚 Delivery
      </button>
    </div>
  );
}