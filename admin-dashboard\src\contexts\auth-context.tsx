'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, UserProfile } from '../../../shared/auth/types'
import { validateSession } from '../../../shared/auth/session-utils'
import { getUserProfile } from '../../../shared/auth/auth-utils'

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (user: User, profile: UserProfile) => void
  logout: () => void
  updateProfile: (profile: UserProfile) => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user && !!profile

  const login = (userData: User, profileData: UserProfile) => {
    setUser(userData)
    setProfile(profileData)
  }

  const logout = async () => {
    setUser(null)
    setProfile(null)
    
    // Clear cookie via API
    try {
      await fetch('/api/auth/set-cookie', {
        method: 'DELETE'
      })
    } catch (error) {
      console.warn('Failed to clear auth cookie:', error)
    }
    
    // Clear any stored session data (fallback)
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_session')
      sessionStorage.removeItem('auth_session')
    }
  }

  const updateProfile = (profileData: UserProfile) => {
    setProfile(profileData)
  }

  // Check for existing session on mount
  useEffect(() => {
    const checkSession = async () => {
      try {
        setIsLoading(true)

        // Check if we're in development mode
        const isDevelopment = process.env.NODE_ENV === 'development'

        // Check for stored session
        const sessionToken =
          typeof window !== 'undefined'
            ? localStorage.getItem('auth_session') || sessionStorage.getItem('auth_session')
            : null

        if (!sessionToken) {
          // In development, if no session and Supabase might be unreachable, create mock session
          if (isDevelopment) {
            console.warn('No session found in development, creating mock authentication')
            const mockUser: User = {
              id: 'dev-user-123',
              email: '<EMAIL>',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            }
            const mockProfile: UserProfile = {
              id: 'dev-user-123',
              user_id: 'dev-user-123',
              first_name: 'Development',
              last_name: 'Admin',
              full_name: 'Development Admin',
              phone: '+1234567890',
              is_active: true,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              role_id: 'admin-role'
            }
            setUser(mockUser)
            setProfile(mockProfile)
          }
          setIsLoading(false)
          return
        }

        try {
          // Validate session
          const sessionResult = await validateSession(sessionToken)

          if (sessionResult.valid && sessionResult.session?.user_id) {
            // Get user profile
            const profileResult = await getUserProfile(sessionResult.session.user_id)

            if (profileResult.success && profileResult.data) {
              const userData: User = {
                id: sessionResult.session.user_id,
                email: profileResult.data.full_name || '',
                created_at: profileResult.data.created_at,
                updated_at: profileResult.data.updated_at,
              }

              setUser(userData)
              setProfile(profileResult.data)
            }
          } else {
            // Invalid session, clear storage
            logout()
          }
        } catch (networkError) {
          console.error('Network error during session validation:', networkError)

          // In development, if network fails, use mock auth
          if (isDevelopment && (networkError as any)?.message?.includes('Failed to fetch')) {
            console.warn('Network error in development, using mock authentication')
            const mockUser: User = {
              id: 'dev-user-123',
              email: '<EMAIL>',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            }
            const mockProfile: UserProfile = {
              id: 'dev-user-123',
              user_id: 'dev-user-123',
              first_name: 'Development',
              last_name: 'Admin',
              full_name: 'Development Admin',
              phone: '+1234567890',
              is_active: true,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              role_id: 'admin-role'
            }
            setUser(mockUser)
            setProfile(mockProfile)
          } else {
            logout()
          }
        }
      } catch (error) {
        console.error('Session validation error:', error)
        logout()
      } finally {
        setIsLoading(false)
      }
    }

    checkSession()
  }, [])

  const value: AuthContextType = {
    user,
    profile,
    isLoading,
    isAuthenticated,
    login,
    logout,
    updateProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
