import Database from 'better-sqlite3';
import * as path from 'path';
import { app } from 'electron';

// Import the new DatabaseService
import { DatabaseService } from './services/DatabaseService';

// Re-export types from services for backward compatibility
export type { Order, OrderItem } from './services/OrderService';
export type { StaffSession } from './services/StaffService';
export type { SyncQueue } from './services/SyncService';
export type { LocalSettings, POSLocalConfig } from './services/SettingsService';
export type { PaymentTransaction, PaymentReceipt, PaymentRefund } from './services/PaymentService';

// Import types for local use
import type { Order, OrderItem } from './services/OrderService';
import type { StaffSession } from './services/StaffService';
import type { PaymentTransaction, PaymentReceipt, PaymentRefund } from './services/PaymentService';

// Additional types for database operations
interface OrderFilters {
  status?: string;
  fromDate?: string;
  toDate?: string;
  customerId?: string;
  paymentStatus?: string;
}

interface OrderUpdateData {
  status?: 'pending' | 'preparing' | 'ready' | 'completed';
  payment_status?: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
  payment_method?: string;
  payment_transaction_id?: string;
  supabase_id?: string;
  [key: string]: any;
}

interface SyncOperation {
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  data: Record<string, any>;
}

type StaffRole = 'admin' | 'staff';

interface SettingsCategory {
  category: 'terminal' | 'restaurant' | 'payment';
  key: string;
  value: any;
}

interface SyncItemUpdate {
  status: 'success' | 'failed';
  error_message?: string;
}

// Legacy DatabaseManager - deprecated, use DatabaseService instead
export class DatabaseManager {
  private databaseService: DatabaseService;

  constructor() {
    this.databaseService = new DatabaseService();
  }

  get db() {
    return (this.databaseService as any).db;
  }

  get orders() {
    return this.databaseService.orders;
  }

  get staff() {
    return this.databaseService.staff;
  }

  get sync() {
    return this.databaseService.sync;
  }

  get settings() {
    return this.databaseService.settings;
  }

  get payments() {
    return this.databaseService.payments;
  }

  async initialize(): Promise<void> {
    return this.databaseService.initialize();
  }

  async close(): Promise<void> {
    return this.databaseService.close();
  }

  // Legacy methods for backward compatibility
  async addOrder(orderData: Partial<Order>) {
    return this.databaseService.orders.createOrder(orderData);
  }

  async getOrder(id: string) {
    return this.databaseService.orders.getOrder(id);
  }

  async getAllOrders(filters?: OrderFilters) {
    return this.databaseService.orders.getAllOrders(filters);
  }

  async updateOrder(id: string, updates: OrderUpdateData) {
    return this.databaseService.orders.updateOrder(id, updates);
  }

  async deleteOrder(id: string) {
    return this.databaseService.orders.deleteOrder(id);
  }

  async updateOrderStatus(id: string, status: Order['status']) {
    return this.databaseService.orders.updateOrderStatus(id, status);
  }

  async getOrdersByStatus(status: Order['status']) {
    return this.databaseService.orders.getOrdersByStatus(status);
  }

  async getTodaysOrders() {
    return this.databaseService.orders.getTodaysOrders();
  }

  async createStaffSession(staffId: string, pin: string, role: StaffRole) {
    return this.databaseService.staff.createSession(staffId, pin, role);
  }

  async validateStaffPin(staffId: string, pin: string) {
    return this.databaseService.staff.validatePin(staffId, pin);
  }

  async getActiveStaffSession(staffId: string) {
    return this.databaseService.staff.getActiveSession(staffId);
  }

  async endStaffSession(sessionId: string) {
    return this.databaseService.staff.endSession(sessionId);
  }

  async isStaffLoggedIn(staffId: string) {
    return this.databaseService.staff.isStaffLoggedIn(staffId);
  }

  async addToSyncQueue(tableName: string, recordId: string, operation: SyncOperation, data: Record<string, any>) {
    return this.databaseService.sync.addToSyncQueue(tableName, recordId, operation.type.toLowerCase() as 'insert' | 'update' | 'delete', data);
  }

  async getPendingSyncItems(limit?: number) {
    return this.databaseService.sync.getPendingSyncItems(limit);
  }

  async markSyncSuccess(syncId: string) {
    return this.databaseService.sync.markSyncSuccess(syncId);
  }

  async markSyncFailed(syncId: string, errorMessage: string) {
    return this.databaseService.sync.markSyncFailed(syncId, errorMessage);
  }

  async getSetting(category: SettingsCategory['category'], key: string, defaultValue?: any) {
    return this.databaseService.settings.getSetting(category, key, defaultValue);
  }

  async setSetting(category: SettingsCategory['category'], key: string, value: any) {
    return this.databaseService.settings.setSetting(category, key, value);
  }

  async getAllSettings(category?: SettingsCategory['category']) {
    return this.databaseService.settings.getAllSettings(category);
  }

  // Missing auth/session methods
  async getActiveSession(staffId?: string) {
    if (staffId) {
      return this.databaseService.staff.getActiveSession(staffId);
    }
    // If no staffId provided, get the first active session (legacy behavior)
    const stmt = (this.databaseService as any).db.prepare(`
      SELECT * FROM staff_sessions 
      WHERE is_active = 1
      ORDER BY login_time DESC
      LIMIT 1
    `);
    return stmt.get() as StaffSession | undefined;
  }

  async endSession(sessionId: string) {
    return this.databaseService.staff.endSession(sessionId);
  }

  // Missing order methods
  async getOrders(filters?: OrderFilters) {
    return this.databaseService.orders.getAllOrders(filters);
  }

  async getOrderById(id: string) {
    return this.databaseService.orders.getOrder(id);
  }

  async insertOrder(orderData: Partial<Order>) {
    return this.databaseService.orders.createOrder(orderData);
  }

  async updateOrderPaymentStatus(orderId: string, status: 'pending' | 'completed' | 'processing' | 'failed' | 'refunded', paymentMethod?: string, transactionId?: string) {
    const updateData: OrderUpdateData = { payment_status: status };
    if (paymentMethod) updateData.payment_method = paymentMethod;
    if (transactionId) updateData.payment_transaction_id = transactionId;
    return this.databaseService.orders.updateOrder(orderId, updateData);
  }

  // Missing payment methods
  async insertPaymentTransaction(transactionData: Partial<PaymentTransaction>) {
    return this.databaseService.payments.createTransaction(transactionData);
  }

  async insertPaymentReceipt(receiptData: Partial<PaymentReceipt>) {
    return this.databaseService.payments.createReceipt(receiptData);
  }

  async insertPaymentRefund(refundData: Partial<PaymentRefund>) {
    return this.databaseService.payments.createRefund(refundData);
  }

  async updateReceiptStatus(receiptId: string, printed: boolean = false, emailed: boolean = false) {
    if (printed) {
      return this.databaseService.payments.markReceiptPrinted(receiptId);
    }
    if (emailed) {
      return this.databaseService.payments.markReceiptEmailed(receiptId);
    }
  }

  async getPaymentTransaction(transactionId: string) {
    return this.databaseService.payments.getTransaction(transactionId);
  }

  async getPaymentRefundsByTransactionId(transactionId: string) {
    return this.databaseService.payments.getRefundsByTransaction(transactionId);
  }

  async getPaymentTransactionsByOrderId(orderId: string) {
    return this.databaseService.payments.getTransactionsByOrder(orderId);
  }

  async getPaymentReceiptByNumber(receiptNumber: string) {
    // This method doesn't exist in PaymentService, implement a workaround
    const stmt = (this.databaseService as any).db.prepare('SELECT * FROM payment_receipts WHERE receipt_number = ?');
    return stmt.get(receiptNumber) as PaymentReceipt | undefined;
  }

  // Missing settings methods
  async getLocalSettings() {
    return this.databaseService.settings.getAllSettings();
  }

  async updateLocalSettings(settingType: string, settings: Record<string, any>) {
    // Map setting types to proper categories
    const categoryMap: Record<string, SettingsCategory['category']> = {
      'pos': 'terminal',
      'restaurant': 'restaurant',
      'payment': 'payment',
      'general': 'terminal'
    };
    
    const category = categoryMap[settingType] || 'terminal';
    
    for (const [key, value] of Object.entries(settings)) {
      this.databaseService.settings.setSetting(category, key, value);
    }
  }

  async updatePOSLocalConfig(terminalId: string | Record<string, any>, configType?: string, configKey?: string, configValue?: any) {
    if (configKey && configValue !== undefined) {
      // Single config update
      this.databaseService.settings.setSetting('terminal', configKey, configValue);
    } else if (typeof terminalId === 'object') {
      // Legacy: first parameter is config object
      for (const [key, value] of Object.entries(terminalId)) {
        this.databaseService.settings.setSetting('terminal', key, value);
      }
    }
  }

  async updateRestaurantLocalConfig(restaurantId: string | Record<string, any>, configKey?: string, configValue?: any) {
    if (configKey && configValue !== undefined) {
      // Single config update
      this.databaseService.settings.setSetting('restaurant', configKey, configValue);
    } else if (typeof restaurantId === 'object') {
      // Legacy: first parameter is config object
      for (const [key, value] of Object.entries(restaurantId)) {
        this.databaseService.settings.setSetting('restaurant', key, value);
      }
    }
  }

  async updatePaymentLocalConfig(config: Record<string, any> | string, configKey?: string) {
    if (configKey && typeof config === 'string') {
      // Single config update
      this.databaseService.settings.setSetting('payment', configKey, config);
    } else if (typeof config === 'object') {
      // Config object
      for (const [key, value] of Object.entries(config)) {
        this.databaseService.settings.setSetting('payment', key, value);
      }
    }
  }

  // Missing sync methods
  async clearOldSyncQueue() {
    return this.databaseService.sync.cleanupOldSyncItems();
  }

  async getSyncQueue() {
    return this.databaseService.sync.getPendingSyncItems();
  }

  async updateSyncQueueItem(syncId: string, success: boolean | SyncItemUpdate, errorMessage?: string) {
    if (typeof success === 'boolean') {
      if (success) {
        return this.databaseService.sync.markSyncSuccess(syncId);
      } else {
        return this.databaseService.sync.markSyncFailed(syncId, errorMessage || 'Unknown error');
      }
    } else {
      // Legacy object format
      const updates = success;
      if (updates.status === 'success') {
        return this.databaseService.sync.markSyncSuccess(syncId);
      } else if (updates.status === 'failed') {
        return this.databaseService.sync.markSyncFailed(syncId, updates.error_message || 'Unknown error');
      }
    }
  }

  async updateOrderSupabaseId(orderId: string, supabaseId: string) {
    return this.databaseService.orders.updateOrder(orderId, { supabase_id: supabaseId });
  }

  // Legacy compatibility method for main.ts
  async executeQuery(query: string, params?: any[]): Promise<any> {
    try {
      const db = (this.databaseService as any).db;
      if (!db) {
        throw new Error('Database not initialized');
      }

      // Determine if it's a SELECT query or a modification query
      const trimmedQuery = query.trim().toLowerCase();

      if (trimmedQuery.startsWith('select')) {
        // For SELECT queries, return all results
        const stmt = db.prepare(query);
        return params ? stmt.all(...params) : stmt.all();
      } else {
        // For INSERT, UPDATE, DELETE queries, return the result info
        const stmt = db.prepare(query);
        return params ? stmt.run(...params) : stmt.run();
      }
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }
}

// Export the new DatabaseService as the primary interface
export { DatabaseService };

// Create a singleton instance for backward compatibility
let databaseInstance: DatabaseManager | null = null;

export function getDatabaseManager(): DatabaseManager {
  if (!databaseInstance) {
    databaseInstance = new DatabaseManager();
  }
  return databaseInstance;
}

export function getDatabaseService(): DatabaseService {
  return new DatabaseService();
}