/**
 * Comprehensive Audit Logging Service
 * Tracks all authentication events, user actions, and security activities
 * Provides GDPR-compliant logging with data retention policies
 */

export interface AuditEvent {
  id: string
  timestamp: Date
  eventType: AuditEventType
  userId?: string
  sessionId?: string
  ipAddress: string
  userAgent: string
  location?: {
    country?: string
    region?: string
    city?: string
    coordinates?: [number, number]
  }
  details: Record<string, any>
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  complianceFlags: ComplianceFlag[]
  dataClassification: DataClassification
  retentionPeriod: number // days
  encrypted: boolean
}

export type AuditEventType = 
  | 'auth.login.success'
  | 'auth.login.failure'
  | 'auth.logout'
  | 'auth.password.change'
  | 'auth.password.reset'
  | 'auth.mfa.enabled'
  | 'auth.mfa.disabled'
  | 'auth.biometric.registered'
  | 'auth.biometric.removed'
  | 'auth.session.created'
  | 'auth.session.expired'
  | 'auth.session.revoked'
  | 'security.anomaly.detected'
  | 'security.breach.suspected'
  | 'security.device.new'
  | 'security.device.blocked'
  | 'security.ip.blocked'
  | 'security.rate.limit.exceeded'
  | 'data.access'
  | 'data.modification'
  | 'data.export'
  | 'data.deletion'
  | 'privacy.consent.given'
  | 'privacy.consent.withdrawn'
  | 'privacy.data.request'
  | 'admin.user.created'
  | 'admin.user.modified'
  | 'admin.user.deleted'
  | 'admin.permission.granted'
  | 'admin.permission.revoked'
  | 'system.backup.created'
  | 'system.backup.restored'
  | 'system.configuration.changed'

export type ComplianceFlag = 
  | 'GDPR'
  | 'PCI_DSS'
  | 'SOC2'
  | 'HIPAA'
  | 'SOX'
  | 'ISO27001'

export type DataClassification = 
  | 'public'
  | 'internal'
  | 'confidential'
  | 'restricted'
  | 'personal_data'
  | 'sensitive_personal_data'

export interface AuditQuery {
  startDate?: Date
  endDate?: Date
  eventTypes?: AuditEventType[]
  userIds?: string[]
  riskLevels?: string[]
  complianceFlags?: ComplianceFlag[]
  ipAddresses?: string[]
  searchText?: string
  limit?: number
  offset?: number
}

export interface AuditReport {
  id: string
  title: string
  description: string
  generatedAt: Date
  generatedBy: string
  period: {
    start: Date
    end: Date
  }
  eventCount: number
  riskDistribution: Record<string, number>
  complianceMetrics: Record<ComplianceFlag, ComplianceMetric>
  anomalies: AuditAnomaly[]
  recommendations: string[]
  attachments: ReportAttachment[]
}

export interface ComplianceMetric {
  totalEvents: number
  criticalEvents: number
  complianceScore: number // 0-100
  violations: ComplianceViolation[]
  status: 'compliant' | 'warning' | 'violation'
}

export interface ComplianceViolation {
  id: string
  type: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  recommendation: string
  events: string[] // audit event IDs
  detectedAt: Date
  resolvedAt?: Date
}

export interface AuditAnomaly {
  id: string
  type: 'behavioral' | 'temporal' | 'geographical' | 'technical'
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  confidence: number // 0-100
  events: string[]
  detectedAt: Date
  investigatedAt?: Date
  resolvedAt?: Date
  falsePositive?: boolean
}

export interface ReportAttachment {
  id: string
  name: string
  type: 'csv' | 'pdf' | 'json' | 'xlsx'
  size: number
  url: string
  generatedAt: Date
}

class AuditLoggingService {
  private events: AuditEvent[] = []
  private retentionPolicies: Map<DataClassification, number> = new Map([
    ['public', 365],
    ['internal', 1095], // 3 years
    ['confidential', 2555], // 7 years
    ['restricted', 3650], // 10 years
    ['personal_data', 2555], // 7 years (GDPR)
    ['sensitive_personal_data', 2555] // 7 years (GDPR)
  ])

  /**
   * Log an audit event with comprehensive metadata
   */
  async logEvent(
    eventType: AuditEventType,
    details: Record<string, any>,
    options: {
      userId?: string
      sessionId?: string
      riskLevel?: 'low' | 'medium' | 'high' | 'critical'
      dataClassification?: DataClassification
      complianceFlags?: ComplianceFlag[]
    } = {}
  ): Promise<string> {
    const eventId = this.generateEventId()
    const timestamp = new Date()
    
    // Get client information
    const clientInfo = await this.getClientInformation()
    
    // Determine risk level based on event type and content
    const riskLevel = options.riskLevel || this.assessRiskLevel(eventType, details)
    
    // Determine data classification
    const dataClassification = options.dataClassification || this.classifyData(eventType, details)
    
    // Determine compliance flags
    const complianceFlags = options.complianceFlags || this.getComplianceFlags(eventType, dataClassification)
    
    // Create audit event
    const auditEvent: AuditEvent = {
      id: eventId,
      timestamp,
      eventType,
      userId: options.userId,
      sessionId: options.sessionId,
      ipAddress: clientInfo.ipAddress,
      userAgent: clientInfo.userAgent,
      location: clientInfo.location,
      details: this.sanitizeDetails(details, dataClassification),
      riskLevel,
      complianceFlags,
      dataClassification,
      retentionPeriod: this.retentionPolicies.get(dataClassification) || 365,
      encrypted: this.shouldEncrypt(dataClassification, riskLevel)
    }

    // Encrypt sensitive data
    if (auditEvent.encrypted) {
      auditEvent.details = await this.encryptData(auditEvent.details)
    }

    // Store event
    this.events.push(auditEvent)
    
    // Real-time compliance check
    await this.performComplianceCheck(auditEvent)
    
    // Trigger alerts for high-risk events
    if (riskLevel === 'high' || riskLevel === 'critical') {
      await this.triggerSecurityAlert(auditEvent)
    }

    return eventId
  }

  /**
   * Query audit events with filtering and pagination
   */
  async queryEvents(query: AuditQuery): Promise<{
    events: AuditEvent[]
    total: number
    hasMore: boolean
  }> {
    let filteredEvents = [...this.events]

    // Apply filters
    if (query.startDate) {
      filteredEvents = filteredEvents.filter(e => e.timestamp >= query.startDate!)
    }
    if (query.endDate) {
      filteredEvents = filteredEvents.filter(e => e.timestamp <= query.endDate!)
    }
    if (query.eventTypes?.length) {
      filteredEvents = filteredEvents.filter(e => query.eventTypes!.includes(e.eventType))
    }
    if (query.userIds?.length) {
      filteredEvents = filteredEvents.filter(e => e.userId && query.userIds!.includes(e.userId))
    }
    if (query.riskLevels?.length) {
      filteredEvents = filteredEvents.filter(e => query.riskLevels!.includes(e.riskLevel))
    }
    if (query.complianceFlags?.length) {
      filteredEvents = filteredEvents.filter(e => 
        query.complianceFlags!.some(flag => e.complianceFlags.includes(flag))
      )
    }
    if (query.ipAddresses?.length) {
      filteredEvents = filteredEvents.filter(e => query.ipAddresses!.includes(e.ipAddress))
    }
    if (query.searchText) {
      const searchLower = query.searchText.toLowerCase()
      filteredEvents = filteredEvents.filter(e => 
        e.eventType.toLowerCase().includes(searchLower) ||
        JSON.stringify(e.details).toLowerCase().includes(searchLower)
      )
    }

    // Sort by timestamp (newest first)
    filteredEvents.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())

    // Apply pagination
    const offset = query.offset || 0
    const limit = query.limit || 50
    const paginatedEvents = filteredEvents.slice(offset, offset + limit)

    return {
      events: paginatedEvents,
      total: filteredEvents.length,
      hasMore: offset + limit < filteredEvents.length
    }
  }

  /**
   * Generate comprehensive audit report
   */
  async generateReport(
    period: { start: Date; end: Date },
    complianceFlags: ComplianceFlag[] = ['GDPR', 'SOC2'],
    options: {
      includeAnomalies?: boolean
      includeRecommendations?: boolean
      exportFormats?: ('csv' | 'pdf' | 'json' | 'xlsx')[]
    } = {}
  ): Promise<AuditReport> {
    const reportId = this.generateReportId()
    
    // Query events for the period
    const { events } = await this.queryEvents({
      startDate: period.start,
      endDate: period.end
    })

    // Calculate risk distribution
    const riskDistribution = events.reduce((acc, event) => {
      acc[event.riskLevel] = (acc[event.riskLevel] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // Calculate compliance metrics
    const complianceMetrics = {} as Record<ComplianceFlag, ComplianceMetric>
    for (const flag of complianceFlags) {
      complianceMetrics[flag] = await this.calculateComplianceMetric(events, flag)
    }

    // Detect anomalies
    const anomalies = options.includeAnomalies 
      ? await this.detectAnomalies(events)
      : []

    // Generate recommendations
    const recommendations = options.includeRecommendations
      ? await this.generateRecommendations(events, complianceMetrics, anomalies)
      : []

    // Generate attachments
    const attachments = await this.generateReportAttachments(
      events,
      options.exportFormats || ['csv', 'pdf']
    )

    return {
      id: reportId,
      title: `Audit Report - ${period.start.toISOString().split('T')[0]} to ${period.end.toISOString().split('T')[0]}`,
      description: `Comprehensive audit report covering ${events.length} events across ${complianceFlags.join(', ')} compliance frameworks`,
      generatedAt: new Date(),
      generatedBy: 'system', // In real implementation, this would be the current user
      period,
      eventCount: events.length,
      riskDistribution,
      complianceMetrics,
      anomalies,
      recommendations,
      attachments
    }
  }

  // Private helper methods
  private generateEventId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateReportId(): string {
    return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private async getClientInformation(): Promise<{
    ipAddress: string
    userAgent: string
    location?: { country?: string; region?: string; city?: string; coordinates?: [number, number] }
  }> {
    // In a real implementation, this would get actual client info
    return {
      ipAddress: '*************',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown',
      location: {
        country: 'United States',
        region: 'California',
        city: 'San Francisco'
      }
    }
  }

  private assessRiskLevel(eventType: AuditEventType, details: Record<string, any>): 'low' | 'medium' | 'high' | 'critical' {
    const highRiskEvents = [
      'auth.login.failure',
      'security.anomaly.detected',
      'security.breach.suspected',
      'security.rate.limit.exceeded',
      'admin.permission.granted',
      'data.export'
    ]

    const criticalRiskEvents = [
      'security.breach.suspected',
      'admin.user.deleted',
      'system.configuration.changed'
    ]

    if (criticalRiskEvents.includes(eventType)) return 'critical'
    if (highRiskEvents.includes(eventType)) return 'high'
    if (eventType.startsWith('auth.') || eventType.startsWith('security.')) return 'medium'
    return 'low'
  }

  private classifyData(eventType: AuditEventType, details: Record<string, any>): DataClassification {
    if (eventType.includes('privacy') || details.personalData) return 'personal_data'
    if (eventType.startsWith('admin.') || eventType.includes('permission')) return 'confidential'
    if (eventType.startsWith('security.') || eventType.startsWith('auth.')) return 'internal'
    return 'internal'
  }

  private getComplianceFlags(eventType: AuditEventType, dataClassification: DataClassification): ComplianceFlag[] {
    const flags: ComplianceFlag[] = ['SOC2'] // All events are SOC2 relevant

    if (dataClassification === 'personal_data' || dataClassification === 'sensitive_personal_data') {
      flags.push('GDPR')
    }

    if (eventType.includes('payment') || eventType.includes('card')) {
      flags.push('PCI_DSS')
    }

    if (eventType.includes('health') || eventType.includes('medical')) {
      flags.push('HIPAA')
    }

    if (eventType.includes('financial') || eventType.startsWith('admin.')) {
      flags.push('SOX')
    }

    flags.push('ISO27001') // Information security standard

    return flags
  }

  private sanitizeDetails(details: Record<string, any>, classification: DataClassification): Record<string, any> {
    const sanitized = { ...details }

    // Remove or mask sensitive data based on classification
    if (classification === 'personal_data' || classification === 'sensitive_personal_data') {
      if (sanitized.password) sanitized.password = '[REDACTED]'
      if (sanitized.ssn) sanitized.ssn = '[REDACTED]'
      if (sanitized.creditCard) sanitized.creditCard = '[REDACTED]'
      if (sanitized.email) sanitized.email = this.maskEmail(sanitized.email)
    }

    return sanitized
  }

  private maskEmail(email: string): string {
    const [local, domain] = email.split('@')
    const maskedLocal = local.substring(0, 2) + '*'.repeat(local.length - 2)
    return `${maskedLocal}@${domain}`
  }

  private shouldEncrypt(classification: DataClassification, riskLevel: string): boolean {
    return classification === 'personal_data' || 
           classification === 'sensitive_personal_data' || 
           classification === 'restricted' ||
           riskLevel === 'high' || 
           riskLevel === 'critical'
  }

  private async encryptData(data: Record<string, any>): Promise<Record<string, any>> {
    // In a real implementation, this would use proper encryption
    return { ...data, _encrypted: true }
  }

  private async performComplianceCheck(event: AuditEvent): Promise<void> {
    // Implement real-time compliance checking
    // This would check for violations and trigger alerts
  }

  private async triggerSecurityAlert(event: AuditEvent): Promise<void> {
    // Implement security alerting
    console.warn(`Security Alert: ${event.eventType} - Risk Level: ${event.riskLevel}`)
  }

  private async calculateComplianceMetric(events: AuditEvent[], flag: ComplianceFlag): Promise<ComplianceMetric> {
    const relevantEvents = events.filter(e => e.complianceFlags.includes(flag))
    const criticalEvents = relevantEvents.filter(e => e.riskLevel === 'critical')
    
    // Simple compliance scoring
    const complianceScore = Math.max(0, 100 - (criticalEvents.length * 10))
    
    return {
      totalEvents: relevantEvents.length,
      criticalEvents: criticalEvents.length,
      complianceScore,
      violations: [], // Would be populated with actual violations
      status: complianceScore >= 90 ? 'compliant' : complianceScore >= 70 ? 'warning' : 'violation'
    }
  }

  private async detectAnomalies(events: AuditEvent[]): Promise<AuditAnomaly[]> {
    // Implement anomaly detection algorithms
    return []
  }

  private async generateRecommendations(
    events: AuditEvent[], 
    metrics: Record<ComplianceFlag, ComplianceMetric>,
    anomalies: AuditAnomaly[]
  ): Promise<string[]> {
    const recommendations: string[] = []

    // Generate recommendations based on analysis
    if (events.filter(e => e.riskLevel === 'high').length > 10) {
      recommendations.push('Consider implementing additional security controls to reduce high-risk events')
    }

    if (Object.values(metrics).some(m => m.complianceScore < 80)) {
      recommendations.push('Review and strengthen compliance controls for failing frameworks')
    }

    if (anomalies.length > 5) {
      recommendations.push('Investigate recurring anomalies and adjust detection thresholds')
    }

    return recommendations
  }

  private async generateReportAttachments(
    events: AuditEvent[], 
    formats: ('csv' | 'pdf' | 'json' | 'xlsx')[]
  ): Promise<ReportAttachment[]> {
    const attachments: ReportAttachment[] = []

    for (const format of formats) {
      attachments.push({
        id: `attachment_${Date.now()}_${format}`,
        name: `audit_report.${format}`,
        type: format,
        size: events.length * 1024, // Estimated size
        url: `/api/reports/download/${format}`,
        generatedAt: new Date()
      })
    }

    return attachments
  }
}

export const auditLoggingService = new AuditLoggingService()
export default auditLoggingService