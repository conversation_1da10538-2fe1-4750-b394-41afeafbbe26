# Shared Authentication Utilities

This package contains shared authentication utilities for the Small Business POS system. It provides a comprehensive set of functions for user authentication, session management, role-based access control (RBAC), and security features across all platforms.

## Features

- **Multi-platform Authentication**: Support for admin dashboard, customer web/mobile, and POS system
- **Multiple Auth Methods**: Email/password, phone OTP, PIN, and 2FA
- **Role-Based Access Control (RBAC)**: Admin, Manager, Staff, and Customer roles with granular permissions
- **Session Management**: Secure session handling with automatic cleanup
- **Two-Factor Authentication (2FA)**: TOTP and backup codes for admin security
- **Phone OTP**: SMS-based authentication for customers
- **PIN Authentication**: Quick access for POS staff
- **Rate Limiting**: Protection against brute force attacks
- **Security Features**: Attempt logging, account lockouts, and session validation

## Installation

```bash
npm install @small-business-pos/shared-auth
```

## Dependencies

This package requires the following dependencies:

```bash
npm install @supabase/supabase-js bcryptjs speakeasy qrcode
npm install -D @types/bcryptjs @types/speakeasy @types/qrcode
```

## Configuration

Before using the authentication utilities, you need to configure your Supabase connection:

```typescript
// Set environment variables
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## Usage

### Basic Authentication

```typescript
import { 
  loginWithEmail, 
  loginWithPhoneOTP, 
  loginWithPIN, 
  logout,
  getCurrentUser 
} from '@small-business-pos/shared-auth';

// Admin/Staff login with email and password
const adminLogin = await loginWithEmail({
  email: '<EMAIL>',
  password: 'password123',
  platform: 'admin-dashboard'
});

// Customer login with phone OTP
const otpRequest = await requestPhoneOTP({
  phone: '+**********',
  platform: 'customer-web'
});

const customerLogin = await loginWithPhoneOTP({
  phone: '+**********',
  otp: '123456',
  platform: 'customer-web'
});

// Staff POS login with PIN
const posLogin = await loginWithPIN({
  pin: '1234',
  branch_id: 'branch-uuid',
  platform: 'pos-system'
});

// Get current user
const user = await getCurrentUser();

// Logout
await logout();
```

### Role-Based Access Control

```typescript
import { 
  hasPermission, 
  hasRole, 
  isAdmin, 
  getUserRolePermissions 
} from '@small-business-pos/shared-auth';

// Check specific permission
const canManageUsers = await hasPermission(userId, 'users.write');

// Check role
const isAdminUser = await isAdmin(userId);
const isManagerUser = await hasRole(userId, 'manager');

// Get all user roles and permissions
const { roles, permissions } = await getUserRolePermissions(userId);
```

### Two-Factor Authentication

```typescript
import { 
  generateTwoFASecret, 
  verifyTwoFACode, 
  enableTwoFA 
} from '@small-business-pos/shared-auth';

// Setup 2FA
const setup = await generateTwoFASecret(userId, userEmail);
if (setup.success) {
  console.log('QR Code:', setup.qr_code);
  console.log('Backup Codes:', setup.backup_codes);
}

// Verify 2FA code
const verification = await verifyTwoFACode({
  user_id: userId,
  code: '123456'
});

// Enable 2FA
if (verification.success) {
  await enableTwoFA(userId);
}
```

### Session Management

```typescript
import { 
  validateSession, 
  refreshSession, 
  getUserSessions 
} from '@small-business-pos/shared-auth';

// Validate current session
const isValid = await validateSession(sessionToken);

// Refresh session
const newSession = await refreshSession(sessionToken);

// Get all user sessions
const sessions = await getUserSessions(userId);
```

### Phone OTP

```typescript
import { 
  generatePhoneOTP, 
  verifyPhoneOTP, 
  formatPhoneNumber 
} from '@small-business-pos/shared-auth';

// Generate OTP
const otpResult = await generatePhoneOTP('+**********');

// Verify OTP
const verification = await verifyPhoneOTP('+**********', '123456');

// Format phone number
const formatted = formatPhoneNumber('**********'); // Returns: +**********
```

### PIN Authentication

```typescript
import { 
  verifyStaffPIN, 
  updateStaffPIN, 
  validatePINFormat 
} from '@small-business-pos/shared-auth';

// Verify staff PIN
const pinResult = await verifyStaffPIN('1234', 'branch-uuid');

// Update PIN
const updateResult = await updateStaffPIN(staffId, '5678');

// Validate PIN format
const isValidFormat = validatePINFormat('1234'); // true
const isInvalidFormat = validatePINFormat('1111'); // false (repeated digits)
```

## API Reference

### Core Authentication

- `loginWithEmail(request)` - Admin/staff email login
- `requestPhoneOTP(request)` - Request phone OTP
- `loginWithPhoneOTP(request)` - Customer phone OTP login
- `loginWithPIN(request)` - Staff PIN login
- `logout()` - Logout current user
- `getCurrentUser()` - Get current authenticated user
- `validateUserSession(token)` - Validate session token

### Role & Permissions

- `hasPermission(userId, permission)` - Check specific permission
- `hasRole(userId, role)` - Check user role
- `getUserRolePermissions(userId)` - Get user roles and permissions
- `isAdmin(userId)` - Check if user is admin
- `isManager(userId)` - Check if user is manager
- `isStaff(userId)` - Check if user is staff
- `hasStaffAccess(userId)` - Check if user has staff-level access

### Two-Factor Authentication

- `generateTwoFASecret(userId, email)` - Generate 2FA setup
- `verifyTwoFACode(request)` - Verify 2FA code
- `enableTwoFA(userId)` - Enable 2FA for user
- `disableTwoFA(userId)` - Disable 2FA for user
- `getRemainingBackupCodes(userId)` - Get unused backup codes

### Session Management

- `createSession(userId, platform)` - Create new session
- `validateSession(token)` - Validate session
- `refreshSession(token)` - Refresh session
- `revokeSession(token)` - Revoke specific session
- `getUserSessions(userId)` - Get all user sessions

### Phone OTP

- `generatePhoneOTP(phone)` - Generate OTP for phone
- `verifyPhoneOTP(phone, code)` - Verify phone OTP
- `formatPhoneNumber(phone)` - Format phone number
- `validatePhoneNumber(phone)` - Validate phone format

### PIN Authentication

- `verifyStaffPIN(pin, branchId)` - Verify staff PIN
- `updateStaffPIN(staffId, newPin)` - Update staff PIN
- `validatePINFormat(pin)` - Validate PIN format
- `generateRandomPIN()` - Generate secure random PIN

## Types

### User Roles

```typescript
type UserRole = 'admin' | 'manager' | 'staff' | 'customer';
```

### Permissions

```typescript
type Permission = 
  | 'users.read' | 'users.write' | 'users.delete'
  | 'products.read' | 'products.write' | 'products.delete'
  | 'orders.read' | 'orders.write' | 'orders.delete'
  | 'inventory.read' | 'inventory.write'
  | 'reports.read' | 'reports.write'
  | 'settings.read' | 'settings.write'
  | 'pos.access' | 'admin.access';
```

### Platforms

```typescript
type Platform = 
  | 'admin-dashboard' 
  | 'customer-web' 
  | 'customer-mobile' 
  | 'pos-system';
```

### Auth Methods

```typescript
type AuthMethod = 
  | 'email_password' 
  | 'phone_otp' 
  | 'pin' 
  | 'two_fa';
```

## Security Features

### Rate Limiting

- Login attempts: 5 attempts per 15 minutes
- OTP requests: 3 requests per 5 minutes
- PIN attempts: 5 attempts before lockout

### Session Security

- Automatic session expiration
- Session token rotation
- Device-based session tracking
- Concurrent session limits

### Data Protection

- PIN hashing with bcrypt
- Secure OTP generation
- 2FA secret encryption
- Audit logging for all auth events

## Error Handling

All functions return structured responses with success/error states:

```typescript
interface AuthResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}
```

Common error codes:

- `INVALID_CREDENTIALS` - Wrong email/password
- `INVALID_OTP` - Wrong or expired OTP
- `INVALID_PIN` - Wrong PIN
- `RATE_LIMITED` - Too many attempts
- `SESSION_EXPIRED` - Session no longer valid
- `INSUFFICIENT_PERMISSIONS` - User lacks required permissions

## Platform-Specific Usage

### Admin Dashboard

```typescript
// Admin login with 2FA
const login = await loginWithEmail({
  email: '<EMAIL>',
  password: 'password',
  platform: 'admin-dashboard'
});

if (login.requires_2fa) {
  const twoFA = await verifyTwoFACode({
    user_id: login.user.id,
    code: '123456'
  });
}
```

### Customer Web/Mobile

```typescript
// Customer phone OTP login
const otpRequest = await requestPhoneOTP({
  phone: '+**********',
  platform: 'customer-web'
});

const login = await loginWithPhoneOTP({
  phone: '+**********',
  otp: '123456',
  platform: 'customer-web'
});
```

### POS System

```typescript
// Staff PIN login
const login = await loginWithPIN({
  pin: '1234',
  branch_id: 'branch-uuid',
  platform: 'pos-system'
});
```

## Contributing

1. Follow TypeScript best practices
2. Add proper error handling
3. Include JSDoc comments
4. Write unit tests
5. Update documentation

## License

MIT License - see LICENSE file for details.