-- Migration: Real-time Notifications Setup
-- Creates tables and triggers for real-time order notifications

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create notification_logs table to track all notification attempts
CREATE TABLE IF NOT EXISTS notification_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  notification_type VARCHAR(50) NOT NULL,
  channel VARCHAR(20) NOT NULL, -- 'push', 'email', 'sms'
  status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'pending', 'sent', 'failed', 'delivered'
  recipient VARCHAR(255), -- email address or phone number
  subject VARCHAR(255),
  message TEXT,
  error_message TEXT,
  external_id VARCHAR(255), -- ID from external service (<PERSON><PERSON><PERSON>, <PERSON>send, etc.)
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  sent_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for notification_logs
CREATE INDEX idx_notification_logs_user_id ON notification_logs(user_id);
CREATE INDEX idx_notification_logs_order_id ON notification_logs(order_id);
CREATE INDEX idx_notification_logs_type_status ON notification_logs(notification_type, status);
CREATE INDEX idx_notification_logs_created_at ON notification_logs(created_at);

-- Create staff_notifications table for admin dashboard
CREATE TABLE IF NOT EXISTS staff_notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  type VARCHAR(50) NOT NULL, -- 'new_order', 'order_status_change', 'delivery_assigned', 'kitchen_alert'
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  target_roles TEXT[] NOT NULL DEFAULT '{}', -- ['admin', 'kitchen', 'delivery', 'manager']
  priority VARCHAR(20) NOT NULL DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
  is_read BOOLEAN DEFAULT FALSE,
  read_by UUID[] DEFAULT '{}', -- Array of user IDs who have read this notification
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days')
);

-- Create indexes for staff_notifications
CREATE INDEX idx_staff_notifications_type ON staff_notifications(type);
CREATE INDEX idx_staff_notifications_order_id ON staff_notifications(order_id);
CREATE INDEX idx_staff_notifications_target_roles ON staff_notifications USING GIN(target_roles);
CREATE INDEX idx_staff_notifications_priority ON staff_notifications(priority);
CREATE INDEX idx_staff_notifications_created_at ON staff_notifications(created_at);
CREATE INDEX idx_staff_notifications_expires_at ON staff_notifications(expires_at);

-- Create push_subscriptions table for web push notifications
CREATE TABLE IF NOT EXISTS push_subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  endpoint TEXT NOT NULL,
  p256dh_key TEXT NOT NULL,
  auth_key TEXT NOT NULL,
  user_agent TEXT,
  device_type VARCHAR(50), -- 'desktop', 'mobile', 'tablet'
  is_active BOOLEAN DEFAULT TRUE,
  last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, endpoint)
);

-- Create indexes for push_subscriptions
CREATE INDEX idx_push_subscriptions_user_id ON push_subscriptions(user_id);
CREATE INDEX idx_push_subscriptions_active ON push_subscriptions(is_active);
CREATE INDEX idx_push_subscriptions_last_used ON push_subscriptions(last_used_at);

-- Create notification_attempts table to track batch notification attempts
CREATE TABLE IF NOT EXISTS notification_attempts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  notification_type VARCHAR(50) NOT NULL,
  channels_attempted TEXT[] NOT NULL DEFAULT '{}',
  success BOOLEAN NOT NULL DEFAULT FALSE,
  errors JSONB DEFAULT '{}',
  attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for notification_attempts
CREATE INDEX idx_notification_attempts_user_id ON notification_attempts(user_id);
CREATE INDEX idx_notification_attempts_order_id ON notification_attempts(order_id);
CREATE INDEX idx_notification_attempts_attempted_at ON notification_attempts(attempted_at);

-- Add notification preferences to user_profiles if not exists
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'user_profiles' AND column_name = 'preferences') THEN
    ALTER TABLE user_profiles ADD COLUMN preferences JSONB DEFAULT '{}';
  END IF;
END $$;

-- Update user_profiles preferences with default notification settings
UPDATE user_profiles 
SET preferences = COALESCE(preferences, '{}') || '{
  "pushNotifications": true,
  "emailNotifications": true,
  "smsNotifications": false,
  "orderUpdates": true,
  "promotions": true,
  "newsletters": false,
  "quietHours": {
    "enabled": false,
    "start": "22:00",
    "end": "08:00",
    "timezone": "UTC"
  }
}'
WHERE preferences IS NULL OR NOT (preferences ? 'pushNotifications');

-- Create function to handle order status changes
CREATE OR REPLACE FUNCTION handle_order_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Only proceed if status actually changed
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    -- Call the Edge Function via HTTP request
    PERFORM
      net.http_post(
        url := current_setting('app.supabase_url') || '/functions/v1/order-status-trigger',
        headers := jsonb_build_object(
          'Content-Type', 'application/json',
          'Authorization', 'Bearer ' || current_setting('app.service_role_key')
        ),
        body := jsonb_build_object(
          'type', 'UPDATE',
          'table', 'orders',
          'schema', 'public',
          'record', row_to_json(NEW),
          'old_record', row_to_json(OLD)
        )
      );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for order status changes
DROP TRIGGER IF EXISTS order_status_change_trigger ON orders;
CREATE TRIGGER order_status_change_trigger
  AFTER UPDATE ON orders
  FOR EACH ROW
  EXECUTE FUNCTION handle_order_status_change();

-- Create function to clean up old notifications
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS void AS $$
BEGIN
  -- Delete notification logs older than 30 days
  DELETE FROM notification_logs 
  WHERE created_at < NOW() - INTERVAL '30 days';
  
  -- Delete expired staff notifications
  DELETE FROM staff_notifications 
  WHERE expires_at < NOW();
  
  -- Delete inactive push subscriptions older than 90 days
  DELETE FROM push_subscriptions 
  WHERE is_active = FALSE AND last_used_at < NOW() - INTERVAL '90 days';
  
  -- Delete old notification attempts older than 30 days
  DELETE FROM notification_attempts 
  WHERE attempted_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Create function to get user notification stats
CREATE OR REPLACE FUNCTION get_user_notification_stats(user_uuid UUID)
RETURNS TABLE (
  total_notifications BIGINT,
  successful_notifications BIGINT,
  failed_notifications BIGINT,
  push_notifications BIGINT,
  email_notifications BIGINT,
  sms_notifications BIGINT,
  last_notification_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_notifications,
    COUNT(*) FILTER (WHERE status = 'sent' OR status = 'delivered') as successful_notifications,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_notifications,
    COUNT(*) FILTER (WHERE channel = 'push') as push_notifications,
    COUNT(*) FILTER (WHERE channel = 'email') as email_notifications,
    COUNT(*) FILTER (WHERE channel = 'sms') as sms_notifications,
    MAX(created_at) as last_notification_at
  FROM notification_logs
  WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to mark staff notification as read
CREATE OR REPLACE FUNCTION mark_staff_notification_read(notification_uuid UUID, user_uuid UUID)
RETURNS boolean AS $$
DECLARE
  updated_count INTEGER;
BEGIN
  UPDATE staff_notifications 
  SET 
    read_by = array_append(read_by, user_uuid::TEXT),
    is_read = CASE 
      WHEN array_length(target_roles, 1) = 1 THEN TRUE
      ELSE is_read
    END
  WHERE id = notification_uuid 
    AND NOT (user_uuid::TEXT = ANY(read_by));
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RETURN updated_count > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enable Row Level Security
ALTER TABLE notification_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE push_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_attempts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for notification_logs
CREATE POLICY "Users can view their own notification logs" ON notification_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all notification logs" ON notification_logs
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Create RLS policies for staff_notifications
CREATE POLICY "Staff can view notifications for their roles" ON staff_notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
        AND user_profiles.role = ANY(staff_notifications.target_roles)
    )
  );

CREATE POLICY "Staff can update notifications they can view" ON staff_notifications
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
        AND user_profiles.role = ANY(staff_notifications.target_roles)
    )
  );

CREATE POLICY "Service role can manage all staff notifications" ON staff_notifications
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Create RLS policies for push_subscriptions
CREATE POLICY "Users can manage their own push subscriptions" ON push_subscriptions
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all push subscriptions" ON push_subscriptions
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Create RLS policies for notification_attempts
CREATE POLICY "Users can view their own notification attempts" ON notification_attempts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all notification attempts" ON notification_attempts
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Enable realtime for staff notifications
ALTER PUBLICATION supabase_realtime ADD TABLE staff_notifications;
ALTER PUBLICATION supabase_realtime ADD TABLE notification_logs;

-- Create a scheduled job to clean up old notifications (if pg_cron is available)
-- This would typically be set up separately in the Supabase dashboard
-- SELECT cron.schedule('cleanup-notifications', '0 2 * * *', 'SELECT cleanup_old_notifications();');

-- Insert some sample notification templates (optional)
INSERT INTO notification_logs (user_id, notification_type, channel, status, message, created_at)
VALUES 
  (NULL, 'system', 'email', 'sent', 'Notification system initialized', NOW())
ON CONFLICT DO NOTHING;

COMMIT;