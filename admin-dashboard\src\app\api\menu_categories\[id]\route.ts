import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { z } from 'zod'

// Validation schema for updates
const updateCategorySchema = z.object({
  name_en: z.string().min(1).optional(),
  name_el: z.string().min(1).optional(),
  description_en: z.string().optional(),
  description_el: z.string().optional(),
  display_order: z.number().int().min(0).optional(),
  is_active: z.boolean().optional()
})

interface RouteParams {
  params: {
    id: string
  }
}

// GET /api/menu_categories/[id] - Fetch a specific menu category
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params
    const { searchParams } = new URL(request.url)
    const include_subcategories = searchParams.get('include_subcategories') === 'true'
    
    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json(
        { error: 'Invalid category ID format' },
        { status: 400 }
      )
    }
    
    let query = supabase
      .from('menu_categories')
      .select(`
        *
        ${include_subcategories ? `,
        subcategories(
          id,
          name_en,
          name_el,
          price,
          is_available,
          display_order
        )` : ''}
      `)
      .eq('id', id)
      .single()
    
    const { data, error } = await query
    
    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Menu category not found' },
          { status: 404 }
        )
      }
      
      return NextResponse.json(
        { error: 'Failed to fetch menu category' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({ data })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/menu_categories/[id] - Update a specific menu category
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params
    const body = await request.json()
    
    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json(
        { error: 'Invalid category ID format' },
        { status: 400 }
      )
    }
    
    // Validate request body
    const validationResult = updateCategorySchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }
    
    const updateData = validationResult.data
    
    // Update menu category
    const { data, error } = await supabase
      .from('menu_categories')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Menu category not found' },
          { status: 404 }
        )
      }
      
      return NextResponse.json(
        { error: 'Failed to update menu category' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({ data })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/menu_categories/[id] - Delete a specific menu category
export async function DELETE(
  _request: NextRequest,
  { params }: RouteParams
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params
    
    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json(
        { error: 'Invalid category ID format' },
        { status: 400 }
      )
    }
    
    // Check if category exists and has subcategories
    const { data: existingCategory, error: fetchError } = await supabase
      .from('menu_categories')
      .select(`
        id,
        name_en,
        subcategories(
          count
        )
      `)
      .eq('id', id)
      .single()
    
    if (fetchError || !existingCategory) {
      return NextResponse.json(
        { error: 'Menu category not found' },
        { status: 404 }
      )
    }
    
    // Check if category has subcategories
    const subcategoriesCount = existingCategory.subcategories?.length || 0
    if (subcategoriesCount > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete category with existing subcategories',
          details: `This category contains ${subcategoriesCount} subcategory(ies). Please move or delete them first.`
        },
        { status: 400 }
      )
    }
    
    // Delete menu category
    const { error } = await supabase
      .from('menu_categories')
      .delete()
      .eq('id', id)
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to delete menu category' },
        { status: 500 }
      )
    }
    
    return NextResponse.json(
      { 
        message: 'Menu category deleted successfully',
        deleted_category: {
          id: existingCategory.id,
          name_en: existingCategory.name_en
        }
      },
      { status: 200 }
    )
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}