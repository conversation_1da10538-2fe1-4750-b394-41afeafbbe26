// Core types for the customer mobile app

export interface User {
  id: string;
  email: string;
  phone: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface Address {
  id: string;
  user_id: string;
  street_address: string;
  city: string;
  postal_code: string;
  floor_number?: string;
  notes?: string;
  address_type: 'home' | 'work' | 'other';
  is_default: boolean;
  created_at: string;
}

export interface MenuCategory {
  id: string;
  name_en: string;
  name_el: string;
  description_en?: string;
  description_el?: string;
  image_url?: string;
  display_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface MenuItem {
  id: string;
  category_id: string;
  name_en: string;
  name_el: string;
  description_en?: string;
  description_el?: string;
  price: number;
  image_url?: string;
  is_available: boolean;
  allergens?: string[];
  nutritional_info?: any;
  display_order: number;
  created_at: string;
  updated_at: string;
}

export interface CartItem {
  id: string;
  menu_item: MenuItem;
  quantity: number;
  notes?: string;
  customizations?: any[];
  total_price: number;
}

export interface Order {
  id: string;
  user_id: string;
  order_number: string;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'out_for_delivery' | 'delivered' | 'cancelled';
  order_type: 'pickup' | 'delivery';
  items: CartItem[];
  total_amount: number;
  delivery_address?: Address;
  special_instructions?: string;
  estimated_delivery_time?: string;
  created_at: string;
  updated_at: string;
}

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'order_update' | 'promotion' | 'general';
  is_read: boolean;
  data?: any;
  created_at: string;
}

// Navigation types
export type RootStackParamList = {
  Home: undefined;
  Menu: { categoryId?: string };
  MenuItemDetails: { itemId: string };
  Cart: undefined;
  Checkout: undefined;
  Orders: undefined;
  OrderDetails: { orderId: string };
  OrderTracking: { orderId: string };
  Profile: undefined;
  Settings: undefined;
  Auth: undefined;
  OTPVerification: { phone: string };
};

export type TabParamList = {
  Home: undefined;
  Menu: undefined;
  Orders: undefined;
  Profile: undefined;
};

// Context types
export interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  signIn: (phone: string) => Promise<void>;
  verifyOTP: (phone: string, otp: string) => Promise<void>;
  signOut: () => Promise<void>;
}

export interface CartContextType {
  items: CartItem[];
  addItem: (item: CartItem) => void;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  getTotalPrice: () => number;
  getTotalItems: () => number;
}

export interface LocationContextType {
  currentLocation: {
    latitude: number;
    longitude: number;
  } | null;
  isLoading: boolean;
  error: string | null;
  requestLocation: () => Promise<void>;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  error?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  limit: number;
  total_pages: number;
}
