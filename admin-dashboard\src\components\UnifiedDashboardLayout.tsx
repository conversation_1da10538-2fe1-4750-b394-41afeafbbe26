'use client';

import React from 'react';
import FloatingNavbar from './FloatingNavbar';
import NotificationArea from './NotificationArea';
import { ErrorBoundary } from './ErrorBoundary';

interface UnifiedDashboardLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export default function UnifiedDashboardLayout({ 
  children, 
  className = '' 
}: UnifiedDashboardLayoutProps) {
  return (
    <ErrorBoundary>
      <div className={`min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative ${className}`}>
        {/* Background Effects */}
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent"></div>
        
        {/* Floating Navigation */}
        <ErrorBoundary>
          <FloatingNavbar />
        </ErrorBoundary>
        
        {/* Notification Area */}
        <ErrorBoundary>
          <NotificationArea />
        </ErrorBoundary>
        
        {/* Main Content */}
        <main className="relative pt-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <ErrorBoundary>
              {children}
            </ErrorBoundary>
          </div>
        </main>
      </div>
    </ErrorBoundary>
  );
}