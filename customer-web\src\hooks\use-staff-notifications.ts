/**
 * Staff Notification System Hook
 * Handles real-time notifications for staff members including:
 * - New order alerts
 * - Kitchen notifications
 * - Delivery assignments
 * - Order status changes
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import type { Database } from '../lib/supabase/database.types';
import type { Order, OrderStatus } from '../types/types';

export interface StaffNotification {
  id: string;
  type: 'new_order' | 'kitchen_alert' | 'delivery_assignment' | 'order_update' | 'urgent';
  title: string;
  message: string;
  orderId?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  timestamp: string;
  read: boolean;
  actionRequired?: boolean;
  metadata?: Record<string, any>;
}

export interface StaffRole {
  id: string;
  name: string;
  permissions: string[];
  notificationTypes: string[];
}

export interface KitchenNotification {
  orderId: string;
  items: {
    id: string;
    name: string;
    quantity: number;
    specialInstructions?: string;
    preparationTime: number;
  }[];
  priority: 'normal' | 'rush' | 'urgent';
  estimatedCompletionTime: string;
  specialRequests?: string[];
}

export interface DeliveryAssignment {
  orderId: string;
  driverId: string;
  driverName: string;
  customerAddress: string;
  customerPhone: string;
  estimatedDeliveryTime: string;
  deliveryInstructions?: string;
  orderValue: number;
}

/**
 * Hook for managing staff notifications
 */
export function useStaffNotifications(staffId?: string, role?: StaffRole) {
  const [notifications, setNotifications] = useState<StaffNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [soundEnabled, setSoundEnabled] = useState(true);

  const supabase = createClientComponentClient<Database>();
  const subscriptionsRef = useRef<any[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const channelIdRef = useRef<string>(crypto.randomUUID());
  const isSubscribedRef = useRef<boolean>(false);

  /**
   * Initialize notification sounds
   */
  useEffect(() => {
    audioRef.current = new Audio('/sounds/notification.mp3');
    audioRef.current.volume = 0.7;
  }, []);

  /**
   * Fetch existing notifications
   */
  const fetchNotifications = useCallback(async () => {
    if (!staffId) return;

    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('staff_notifications')
        .select('*')
        .eq('staff_id', staffId)
        .order('created_at', { ascending: false })
        .limit(50);

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      const formattedNotifications: StaffNotification[] = data.map(item => ({
        id: item.id,
        type: item.type as StaffNotification['type'],
        title: item.title,
        message: item.message,
        orderId: item.order_id,
        priority: item.priority as StaffNotification['priority'],
        timestamp: item.created_at,
        read: item.read,
        actionRequired: item.action_required,
        metadata: item.metadata,
      }));

      setNotifications(formattedNotifications);
      setUnreadCount(formattedNotifications.filter(n => !n.read).length);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch notifications');
    } finally {
      setLoading(false);
    }
  }, [staffId, supabase]);

  /**
   * Play notification sound
   */
  const playNotificationSound = useCallback(
    (priority: StaffNotification['priority']) => {
      if (!soundEnabled || !audioRef.current) return;

      // Different sounds for different priorities
      switch (priority) {
        case 'urgent':
          audioRef.current.src = '/sounds/urgent.mp3';
          audioRef.current.play().catch(console.error);
          break;
        case 'high':
          audioRef.current.src = '/sounds/high-priority.mp3';
          audioRef.current.play().catch(console.error);
          break;
        default:
          audioRef.current.src = '/sounds/notification.mp3';
          audioRef.current.play().catch(console.error);
      }
    },
    [soundEnabled]
  );

  /**
   * Handle new order notifications
   */
  const handleNewOrder = useCallback(
    (payload: any) => {
      const order = payload.new;

      // Check if this role should receive new order notifications
      if (!role?.notificationTypes.includes('new_order')) return;

      const notification: StaffNotification = {
        id: `new-order-${order.id}-${Date.now()}`,
        type: 'new_order',
        title: 'New Order Received',
        message: `Order #${order.id} - Total: €${order.total.toFixed(2)}`,
        orderId: order.id,
        priority: 'high',
        timestamp: new Date().toISOString(),
        read: false,
        actionRequired: true,
        metadata: {
          orderTotal: order.total,
          deliveryMethod: order.delivery_method,
          customerPhone: order.contact_phone,
        },
      };

      setNotifications(prev => [notification, ...prev]);
      setUnreadCount(prev => prev + 1);
      playNotificationSound('high');

      // Send to kitchen if order contains items
      if (role?.name === 'kitchen' || role?.permissions.includes('kitchen_access')) {
        sendKitchenNotification(order);
      }
    },
    [role, playNotificationSound]
  );

  /**
   * Handle order status updates
   */
  const handleOrderUpdate = useCallback(
    (payload: any) => {
      const updatedOrder = payload.new;
      const oldOrder = payload.old;

      // Only notify if status actually changed
      if (updatedOrder.status === oldOrder.status) return;

      const notification: StaffNotification = {
        id: `order-update-${updatedOrder.id}-${Date.now()}`,
        type: 'order_update',
        title: 'Order Status Updated',
        message: `Order #${updatedOrder.id} is now ${updatedOrder.status}`,
        orderId: updatedOrder.id,
        priority: 'medium',
        timestamp: new Date().toISOString(),
        read: false,
        actionRequired: false,
        metadata: {
          oldStatus: oldOrder.status,
          newStatus: updatedOrder.status,
        },
      };

      setNotifications(prev => [notification, ...prev]);
      setUnreadCount(prev => prev + 1);
      playNotificationSound('medium');
    },
    [playNotificationSound]
  );

  /**
   * Handle delivery assignments
   */
  const handleDeliveryAssignment = useCallback(
    (payload: any) => {
      const assignment = payload.new;

      // Only notify delivery staff
      if (!role?.permissions.includes('delivery_access')) return;

      const notification: StaffNotification = {
        id: `delivery-${assignment.id}-${Date.now()}`,
        type: 'delivery_assignment',
        title: 'New Delivery Assignment',
        message: `Delivery for Order #${assignment.order_id}`,
        orderId: assignment.order_id,
        priority: 'high',
        timestamp: new Date().toISOString(),
        read: false,
        actionRequired: true,
        metadata: {
          assignmentId: assignment.id,
          customerAddress: assignment.delivery_address,
          estimatedTime: assignment.estimated_delivery_time,
        },
      };

      setNotifications(prev => [notification, ...prev]);
      setUnreadCount(prev => prev + 1);
      playNotificationSound('high');
    },
    [role, playNotificationSound]
  );

  /**
   * Send kitchen notification for new order
   */
  const sendKitchenNotification = useCallback(
    async (order: any) => {
      try {
        // Fetch order items with menu details
        const { data: orderItems, error } = await supabase
          .from('order_items')
          .select(
            `
          *,
          subcategories (
            name,
            preparation_time
          )
        `
          )
          .eq('order_id', order.id);

        if (error) {
          console.error('Failed to fetch order items for kitchen notification:', error);
          return;
        }

        const kitchenNotification: KitchenNotification = {
          orderId: order.id,
          items: orderItems.map(item => ({
            id: item.id,
            name: item.subcategories.name,
            quantity: item.quantity,
            specialInstructions: item.special_instructions,
            preparationTime: item.subcategories.preparation_time || 15,
          })),
          priority: order.delivery_method === 'pickup' ? 'normal' : 'rush',
          estimatedCompletionTime: order.estimated_delivery_time,
          specialRequests: order.special_instructions ? [order.special_instructions] : undefined,
        };

        // Store kitchen notification
        await supabase.from('kitchen_notifications').insert({
          order_id: order.id,
          notification_data: kitchenNotification,
          priority: kitchenNotification.priority,
          created_at: new Date().toISOString(),
        });
      } catch (error) {
        console.error('Failed to send kitchen notification:', error);
      }
    },
    [supabase]
  );

  /**
   * Mark notification as read
   */
  const markAsRead = useCallback(
    async (notificationId: string) => {
      try {
        await supabase.from('staff_notifications').update({ read: true }).eq('id', notificationId);

        setNotifications(prev =>
          prev.map(n => (n.id === notificationId ? { ...n, read: true } : n))
        );

        setUnreadCount(prev => Math.max(0, prev - 1));
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    },
    [supabase]
  );

  /**
   * Mark all notifications as read
   */
  const markAllAsRead = useCallback(async () => {
    if (!staffId) return;

    try {
      await supabase
        .from('staff_notifications')
        .update({ read: true })
        .eq('staff_id', staffId)
        .eq('read', false);

      setNotifications(prev => prev.map(n => ({ ...n, read: true })));

      setUnreadCount(0);
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  }, [staffId, supabase]);

  /**
   * Delete notification
   */
  const deleteNotification = useCallback(
    async (notificationId: string) => {
      try {
        await supabase.from('staff_notifications').delete().eq('id', notificationId);

        setNotifications(prev => prev.filter(n => n.id !== notificationId));
      } catch (error) {
        console.error('Failed to delete notification:', error);
      }
    },
    [supabase]
  );

  /**
   * Setup real-time subscriptions
   */
  const setupSubscriptions = useCallback(() => {
    if (!staffId || !role || isSubscribedRef.current) return;

    // Clean up existing subscriptions
    subscriptionsRef.current.forEach(sub => {
      try {
        sub.unsubscribe();
      } catch (error) {
        console.warn('Error unsubscribing:', error);
      }
    });
    subscriptionsRef.current = [];

    const uniqueId = channelIdRef.current;

    // Subscribe to new orders
    if (role.notificationTypes.includes('new_order')) {
      const newOrderSub = supabase
        .channel(`new-orders-${staffId}-${uniqueId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'orders',
          },
          handleNewOrder
        )
        .subscribe();

      subscriptionsRef.current.push(newOrderSub);
    }

    // Subscribe to order updates
    const orderUpdateSub = supabase
      .channel(`order-updates-${staffId}-${uniqueId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'orders',
        },
        handleOrderUpdate
      )
      .subscribe();

    subscriptionsRef.current.push(orderUpdateSub);

    // Subscribe to delivery assignments
    if (role.permissions.includes('delivery_access')) {
      const deliverySub = supabase
        .channel(`delivery-assignments-${staffId}-${uniqueId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'delivery_assignments',
          },
          handleDeliveryAssignment
        )
        .subscribe();

      subscriptionsRef.current.push(deliverySub);
    }

    // Subscribe to staff notifications
    const notificationSub = supabase
      .channel(`staff-notifications-${staffId}-${uniqueId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'staff_notifications',
          filter: `staff_id=eq.${staffId}`,
        },
        payload => {
          const newNotification = payload.new;
          const notification: StaffNotification = {
            id: newNotification.id,
            type: newNotification.type,
            title: newNotification.title,
            message: newNotification.message,
            orderId: newNotification.order_id,
            priority: newNotification.priority,
            timestamp: newNotification.created_at,
            read: newNotification.read,
            actionRequired: newNotification.action_required,
            metadata: newNotification.metadata,
          };

          setNotifications(prev => [notification, ...prev]);
          setUnreadCount(prev => prev + 1);
          playNotificationSound(notification.priority);
        }
      )
      .subscribe();

    subscriptionsRef.current.push(notificationSub);
    isSubscribedRef.current = true;
  }, [
    staffId,
    role,
    supabase,
    handleNewOrder,
    handleOrderUpdate,
    handleDeliveryAssignment,
    playNotificationSound,
  ]);

  /**
   * Toggle sound notifications
   */
  const toggleSound = useCallback(() => {
    setSoundEnabled(prev => !prev);
  }, []);

  /**
   * Get notifications by type
   */
  const getNotificationsByType = useCallback(
    (type: StaffNotification['type']) => {
      return notifications.filter(n => n.type === type);
    },
    [notifications]
  );

  /**
   * Get urgent notifications
   */
  const getUrgentNotifications = useCallback(() => {
    return notifications.filter(n => n.priority === 'urgent' && !n.read);
  }, [notifications]);

  // Initialize on mount
  useEffect(() => {
    if (staffId && role) {
      fetchNotifications();
      setupSubscriptions();
    }

    return () => {
      // Clean up subscriptions on unmount
      isSubscribedRef.current = false;
      subscriptionsRef.current.forEach(sub => {
        try {
          sub.unsubscribe();
        } catch (error) {
          console.warn('Error during cleanup:', error);
        }
      });
      subscriptionsRef.current = [];
    };
  }, [staffId, role, fetchNotifications, setupSubscriptions]);

  return {
    notifications,
    unreadCount,
    loading,
    error,
    soundEnabled,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    toggleSound,
    getNotificationsByType,
    getUrgentNotifications,
    refreshNotifications: fetchNotifications,
  };
}

export default useStaffNotifications;
