import React, { memo, useState } from 'react';
import OrderDashboard from './OrderDashboard';
import ReportsPage from '../pages/ReportsPage';
import NavigationSidebar from './NavigationSidebar';
import OrderFlow from './OrderFlow';
import { ThemeSwitcher } from './ThemeSwitcher';
import ContentContainer from './ui/ContentContainer';
import { useTheme } from '../contexts/theme-context';

interface RefactoredMainLayoutProps {
  className?: string;
  onLogout?: () => void;
}

export const RefactoredMainLayout = memo<RefactoredMainLayoutProps>(({ className = '', onLogout }) => {
  const { resolvedTheme } = useTheme();
  const [currentView, setCurrentView] = React.useState<'dashboard' | 'reports'>('dashboard');

  // Handle navigation
  const handleViewChange = (view: 'dashboard' | 'reports') => {
    setCurrentView(view);
  };

  // Handle logout
  const handleLogout = () => {
    if (onLogout) {
      onLogout();
    } else {
      localStorage.removeItem("pos-user");
      window.location.reload();
    }
  };

  // Render the appropriate page component based on currentView
  const renderCurrentView = () => {
    switch (currentView) {
        case 'dashboard':
          return <OrderDashboard />;
        case 'reports':
          return <ReportsPage />;
        default:
          return <OrderDashboard />;
      }
  };

  return (
    <div className={`flex h-screen transition-all duration-300 overflow-hidden ${
      resolvedTheme === 'light' 
        ? 'bg-slate-50' 
        : 'bg-slate-900'
    } ${className}`}>
      {/* Navigation Sidebar */}
      <NavigationSidebar 
        currentView={currentView}
        onViewChange={handleViewChange}
        onLogout={handleLogout}
      />

      {/* Main Content Area with Container */}
      <div className="flex-1 flex flex-col overflow-hidden min-h-0">
        <ContentContainer className="flex-1 min-h-0 overflow-auto">
          <div className="h-full min-h-[600px] sm:min-h-[500px] md:min-h-[400px]">
            {renderCurrentView()}
          </div>
        </ContentContainer>
      </div>

      {/* Order Flow */}
      <OrderFlow />
    </div>
  );
});

RefactoredMainLayout.displayName = 'RefactoredMainLayout';

export default RefactoredMainLayout;