'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { GlassCard } from './glass-components';

// Generic loading spinner
export function LoadingSpinner({ className, size = 'md' }: { className?: string; size?: 'sm' | 'md' | 'lg' }) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
  };

  return (
    <div className={cn('animate-spin rounded-full border-4 border-primary border-t-transparent', sizeClasses[size], className)} />
  );
}

// Menu item skeleton
export function MenuItemSkeleton() {
  return (
    <GlassCard className="p-0 overflow-hidden">
      <div className="animate-pulse">
        <div className="bg-muted h-48 w-full" />
        <div className="p-6 space-y-3">
          <div className="h-6 bg-muted rounded w-3/4" />
          <div className="h-4 bg-muted rounded w-full" />
          <div className="h-4 bg-muted rounded w-2/3" />
          <div className="flex justify-between items-center mt-4">
            <div className="h-6 bg-muted rounded w-16" />
            <div className="h-10 bg-muted rounded w-24" />
          </div>
        </div>
      </div>
    </GlassCard>
  );
}

// Category skeleton
export function CategorySkeleton() {
  return (
    <GlassCard className="p-0 overflow-hidden">
      <div className="animate-pulse">
        <div className="bg-muted h-48 w-full" />
        <div className="p-6 space-y-3">
          <div className="h-6 bg-muted rounded w-3/4" />
          <div className="h-4 bg-muted rounded w-full" />
          <div className="h-10 bg-muted rounded w-full mt-4" />
        </div>
      </div>
    </GlassCard>
  );
}

// Cart item skeleton
export function CartItemSkeleton() {
  return (
    <div className="flex items-center space-x-4 p-4 border-b">
      <div className="animate-pulse">
        <div className="bg-muted h-16 w-16 rounded" />
      </div>
      <div className="flex-1 space-y-2">
        <div className="animate-pulse">
          <div className="h-4 bg-muted rounded w-3/4" />
          <div className="h-3 bg-muted rounded w-1/2 mt-1" />
        </div>
      </div>
      <div className="animate-pulse">
        <div className="h-6 bg-muted rounded w-16" />
      </div>
    </div>
  );
}

// Full page loading
export function PageLoading({ message = 'Loading...' }: { message?: string }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 flex items-center justify-center">
      <div className="text-center">
        <LoadingSpinner size="lg" className="mx-auto mb-4" />
        <p className="text-lg text-muted-foreground">{message}</p>
      </div>
    </div>
  );
}

// Menu grid loading
export function MenuGridLoading({ itemCount = 6 }: { itemCount?: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {Array.from({ length: itemCount }).map((_, i) => (
        <MenuItemSkeleton key={i} />
      ))}
    </div>
  );
}

// Category grid loading
export function CategoryGridLoading({ itemCount = 6 }: { itemCount?: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {Array.from({ length: itemCount }).map((_, i) => (
        <CategorySkeleton key={i} />
      ))}
    </div>
  );
}

// Inline loading with text
export function InlineLoading({ text = 'Loading...', className }: { text?: string; className?: string }) {
  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <LoadingSpinner size="sm" />
      <span className="text-sm text-muted-foreground">{text}</span>
    </div>
  );
}

// Button loading state
export function ButtonLoading({ children, loading, ...props }: React.ButtonHTMLAttributes<HTMLButtonElement> & { loading?: boolean }) {
  return (
    <button {...props} disabled={loading || props.disabled}>
      {loading ? (
        <div className="flex items-center space-x-2">
          <LoadingSpinner size="sm" />
          <span>Loading...</span>
        </div>
      ) : (
        children
      )}
    </button>
  );
}