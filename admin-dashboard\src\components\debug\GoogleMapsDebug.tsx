'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { GoogleMapsService, GeocodingService, GeometryService, MapConfigService } from '@/lib/google-maps-service'

interface DiagnosticResult {
  test: string
  status: 'pending' | 'success' | 'error'
  message: string
  details?: any
}

export const GoogleMapsDebug = () => {
  const [results, setResults] = useState<DiagnosticResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const mapRef = useRef<HTMLDivElement>(null)
  const [map, setMap] = useState<google.maps.Map | null>(null)

  const updateResult = (test: string, status: DiagnosticResult['status'], message: string, details?: any) => {
    setResults(prev => {
      const existing = prev.find(r => r.test === test)
      if (existing) {
        existing.status = status
        existing.message = message
        existing.details = details
        return [...prev]
      } else {
        return [...prev, { test, status, message, details }]
      }
    })
  }

  const runDiagnostics = async () => {
    setIsRunning(true)
    setResults([])

    // Test 1: Environment Variables
    updateResult('env-vars', 'pending', 'Checking environment variables...')
    try {
      const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
      if (apiKey) {
        updateResult('env-vars', 'success', `API Key found: ${apiKey.substring(0, 10)}...`)
      } else {
        updateResult('env-vars', 'error', 'NEXT_PUBLIC_GOOGLE_MAPS_API_KEY not found')
      }
    } catch (error) {
      updateResult('env-vars', 'error', `Error checking env vars: ${error}`)
    }

    // Test 2: Google Maps Service Loading
    updateResult('maps-service', 'pending', 'Loading Google Maps Service...')
    try {
      const google = await GoogleMapsService.loadGoogleMaps()
      updateResult('maps-service', 'success', 'Google Maps Service loaded successfully', {
        version: google.maps.version,
        libraries: Object.keys(google.maps)
      })

      // Test 3: Map Creation
      updateResult('map-creation', 'pending', 'Creating map instance...')
      try {
        if (mapRef.current) {
          const mapOptions = MapConfigService.getDeliveryZoneMapOptions()
          const mapInstance = new google.maps.Map(mapRef.current, mapOptions)
          setMap(mapInstance)
          updateResult('map-creation', 'success', 'Map instance created successfully')

          // Test 4: Drawing Manager
          updateResult('drawing-manager', 'pending', 'Creating drawing manager...')
          try {
            const drawingOptions = MapConfigService.getDrawingManagerOptions()
            const drawingManager = new google.maps.drawing.DrawingManager(drawingOptions)
            drawingManager.setMap(mapInstance)
            updateResult('drawing-manager', 'success', 'Drawing manager created successfully')
          } catch (error) {
            updateResult('drawing-manager', 'error', `Drawing manager error: ${error}`)
          }

          // Test 5: Places Service
          updateResult('places-service', 'pending', 'Testing Places service...')
          try {
            const placesService = new google.maps.places.PlacesService(mapInstance)
            updateResult('places-service', 'success', 'Places service available')
          } catch (error) {
            updateResult('places-service', 'error', `Places service error: ${error}`)
          }

          // Test 6: Geometry Library
          updateResult('geometry-lib', 'pending', 'Testing Geometry library...')
          try {
            const point1 = new google.maps.LatLng(37.7749, -122.4194)
            const point2 = new google.maps.LatLng(37.7849, -122.4094)
            const distance = google.maps.geometry.spherical.computeDistanceBetween(point1, point2)
            updateResult('geometry-lib', 'success', `Geometry library working. Distance: ${distance.toFixed(2)}m`)
          } catch (error) {
            updateResult('geometry-lib', 'error', `Geometry library error: ${error}`)
          }
        } else {
          updateResult('map-creation', 'error', 'Map container not found')
        }
      } catch (error) {
        updateResult('map-creation', 'error', `Map creation error: ${error}`)
      }
    } catch (error) {
      updateResult('maps-service', 'error', `Google Maps loading error: ${error}`)
    }

    // Test 7: Geocoding Service
    updateResult('geocoding', 'pending', 'Testing geocoding service...')
    try {
      const result = await GeocodingService.geocodeAddress('San Francisco, CA')
      updateResult('geocoding', 'success', `Geocoding successful. Found ${result.results.length} results`)
    } catch (error) {
      updateResult('geocoding', 'error', `Geocoding error: ${error}`)
    }

    // Test 8: Geolocation
    updateResult('geolocation', 'pending', 'Testing browser geolocation...')
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          updateResult('geolocation', 'success', `Geolocation successful: ${position.coords.latitude}, ${position.coords.longitude}`)
        },
        (error) => {
          updateResult('geolocation', 'error', `Geolocation error: ${error.message}`)
        },
        { timeout: 10000 }
      )
    } else {
      updateResult('geolocation', 'error', 'Geolocation not supported by browser')
    }

    setIsRunning(false)
  }

  const testMapInteraction = () => {
    if (map) {
      // Add a test marker
      const marker = new google.maps.Marker({
        position: { lat: 37.7749, lng: -122.4194 },
        map: map,
        title: 'Test Marker',
        draggable: true
      })

      // Add click listener
      map.addListener('click', (e: google.maps.MapMouseEvent) => {
        console.log('Map clicked at:', e.latLng?.toJSON())
        updateResult('map-interaction', 'success', `Map click detected at: ${e.latLng?.toJSON()}`)
      })

      updateResult('map-interaction', 'success', 'Map interaction test setup complete. Click on the map!')
    }
  }

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success': return '✅'
      case 'error': return '❌'
      case 'pending': return '⏳'
      default: return '❓'
    }
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Google Maps Diagnostics</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-2">
            <Button onClick={runDiagnostics} disabled={isRunning}>
              {isRunning ? 'Running Diagnostics...' : 'Run Diagnostics'}
            </Button>
            <Button onClick={testMapInteraction} disabled={!map} variant="outline">
              Test Map Interaction
            </Button>
          </div>

          {results.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-semibold">Diagnostic Results:</h3>
              {results.map((result, index) => (
                <Alert key={index} variant={result.status === 'error' ? 'destructive' : 'default'}>
                  <AlertDescription>
                    <div className="flex items-start space-x-2">
                      <span>{getStatusIcon(result.status)}</span>
                      <div className="flex-1">
                        <div className="font-medium">{result.test}</div>
                        <div className="text-sm">{result.message}</div>
                        {result.details && (
                          <details className="mt-1">
                            <summary className="text-xs cursor-pointer">Details</summary>
                            <pre className="text-xs mt-1 p-2 bg-gray-100 rounded overflow-auto">
                              {JSON.stringify(result.details, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          )}

          <div className="mt-4">
            <h3 className="font-semibold mb-2">Test Map:</h3>
            <div
              ref={mapRef}
              className="w-full h-64 border rounded-lg"
              style={{ minHeight: '256px' }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 