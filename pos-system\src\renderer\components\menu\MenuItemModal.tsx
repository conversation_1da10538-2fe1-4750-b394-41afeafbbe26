import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { useTheme } from '../../contexts/theme-context';
import { menuService, MenuItem, Ingredient } from '../../services/MenuService';

interface SelectedIngredient {
  ingredient: Ingredient;
  quantity: number;
}

interface MenuItemModalProps {
  isOpen: boolean;
  onClose: () => void;
  menuItem: MenuItem | null;
  orderType?: 'pickup' | 'delivery';
  onAddToCart: (item: MenuItem, quantity: number, customizations: SelectedIngredient[], notes: string) => void;
  isCustomizable?: boolean;
}

export const MenuItemModal: React.FC<MenuItemModalProps> = ({
  isOpen,
  onClose,
  menuItem,
  orderType = 'delivery',
  onAddToCart,
  isCustomizable = false
}) => {
  const { resolvedTheme } = useTheme();
  const [selectedIngredients, setSelectedIngredients] = useState<SelectedIngredient[]>([]);
  const [availableIngredients, setAvailableIngredients] = useState<Ingredient[]>([]);
  const [quantity, setQuantity] = useState(1);
  const [notes, setNotes] = useState("");
  const [loading, setLoading] = useState(false);
  const [ingredientCategories, setIngredientCategories] = useState<{[key: string]: Ingredient[]}>({});

  useEffect(() => {
    if (isOpen && menuItem) {
      setSelectedIngredients([]);
      setQuantity(1);
      setNotes("");
      loadAvailableIngredients();
    }
  }, [isOpen, menuItem]);

  const loadAvailableIngredients = async () => {
    if (!menuItem) return;
    
    setLoading(true);
    try {
      // Only load ingredients for customizable items
      if (menuItem.is_customizable === true) {
        // Load ingredient categories first
        const categories = await menuService.getIngredientCategories();
        
        let allIngredients: Ingredient[] = [];
        
        if (categories.length === 0) {
          // Fallback to all ingredients if no categories
          const ingredients = await menuService.getIngredients();
          allIngredients = ingredients;
          setAvailableIngredients(ingredients);
          
          const categorized = ingredients.reduce((acc, ingredient) => {
            const categoryName = ingredient.ingredient_category?.name || 'Other';
            if (!acc[categoryName]) {
              acc[categoryName] = [];
            }
            acc[categoryName].push(ingredient);
            return acc;
          }, {} as {[key: string]: Ingredient[]});
          
          setIngredientCategories(categorized);
        } else {
          // Load ingredients by category
          const categorizedIngredients: {[key: string]: Ingredient[]} = {};
          
          for (const category of categories) {
            const categoryIngredients = await menuService.getIngredientsByCategory(category.id);
            
            if (categoryIngredients.length > 0) {
              categorizedIngredients[category.name] = categoryIngredients;
              allIngredients = [...allIngredients, ...categoryIngredients];
            }
          }
          
          setAvailableIngredients(allIngredients);
          setIngredientCategories(categorizedIngredients);
        }
        
        // Load default ingredients for this item
        const defaultIngredients = await menuService.getMenuItemIngredients(menuItem.id);
        const defaultSelected = defaultIngredients
          .filter(mi => mi.is_default)
          .map(mi => {
            const ingredient = allIngredients.find(ing => ing.id === mi.ingredient_id);
            return ingredient ? {
              ingredient,
              quantity: mi.quantity
            } : null;
          })
          .filter(Boolean) as SelectedIngredient[];
        
        setSelectedIngredients(defaultSelected);
      } else {
        // Clear ingredients for non-customizable items
        setAvailableIngredients([]);
        setIngredientCategories({});
        setSelectedIngredients([]);
      }
    } catch (error) {
      console.error('Error loading ingredients:', error);
      toast.error('Failed to load customization options');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !menuItem) return null;

  const handleIngredientToggle = (ingredient: Ingredient, isSelected: boolean) => {
    if (isSelected) {
      // Check if we've reached the max ingredients limit
      if (menuItem.max_ingredients && selectedIngredients.length >= menuItem.max_ingredients) {
        toast.error(`Maximum ${menuItem.max_ingredients} ingredients allowed`);
        return;
      }
      
      setSelectedIngredients(prev => [
        ...prev,
        { ingredient, quantity: 1 }
      ]);
    } else {
      setSelectedIngredients(prev => 
        prev.filter(si => si.ingredient.id !== ingredient.id)
      );
    }
  };

  const handleIngredientQuantityChange = (ingredientId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      setSelectedIngredients(prev => 
        prev.filter(si => si.ingredient.id !== ingredientId)
      );
    } else {
      setSelectedIngredients(prev =>
        prev.map(si => 
          si.ingredient.id === ingredientId 
            ? { ...si, quantity: newQuantity }
            : si
        )
      );
    }
  };

  const handleAddToCart = () => {
    onAddToCart(menuItem, quantity, selectedIngredients, notes);
    onClose();
    toast.success(`${menuItem.name} added to cart!`);
  };

  // Calculate total price with order type consideration
  const getBasePrice = () => {
    if (!menuItem) return 0;
    if (orderType === 'pickup' && menuItem.pickup_price) {
      return menuItem.pickup_price;
    } else if (orderType === 'delivery' && menuItem.delivery_price) {
      return menuItem.delivery_price;
    }
    return menuItem.base_price || menuItem.price;
  };

  const basePrice = getBasePrice();
  const ingredientPrice = selectedIngredients.reduce((sum, si) => 
    sum + (si.ingredient.price * si.quantity), 0
  );
  const totalPrice = (basePrice + ingredientPrice) * quantity;

  const isIngredientSelected = (ingredient: Ingredient) => {
    return selectedIngredients.some(si => si.ingredient.id === ingredient.id);
  };

  const getIngredientQuantity = (ingredient: Ingredient) => {
    const selected = selectedIngredients.find(si => si.ingredient.id === ingredient.id);
    return selected ? selected.quantity : 0;
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className={`w-full max-w-2xl max-h-[90vh] rounded-3xl shadow-2xl border overflow-hidden ${
        resolvedTheme === 'dark'
          ? 'bg-gray-800/95 border-gray-700/50 backdrop-blur-xl'
          : 'bg-white/95 border-gray-200/50 backdrop-blur-xl'
      }`}>
        {/* Header */}
        <div className="p-6 border-b border-gray-200/20">
          <div className="flex justify-between items-start">
            <div>
              <h2 className={`text-2xl font-bold ${
                resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {menuItem.name}
              </h2>
              <p className={`text-sm mt-1 ${
                resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
              }`}>
                {menuItem.description}
              </p>
              <div className="flex items-center gap-4 mt-2">
                <p className="text-lg font-semibold text-green-500">
                  €{basePrice.toFixed(2)}
                </p>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                }`}>
                  {orderType === 'pickup' ? 'Pickup' : 'Delivery'} Price
                </span>
                <span className={`text-sm px-2 py-1 rounded-full ${
                  resolvedTheme === 'dark' 
                    ? 'bg-blue-600/20 text-blue-400' 
                    : 'bg-blue-100 text-blue-600'
                }`}>
                  {menuItem.preparation_time} min
                </span>
                {menuItem.is_customizable && (
                  <span className={`text-sm px-2 py-1 rounded-full ${
                    resolvedTheme === 'dark' 
                      ? 'bg-purple-600/20 text-purple-400' 
                      : 'bg-purple-100 text-purple-600'
                  }`}>
                    Customizable
                  </span>
                )}
              </div>
            </div>
            <button
              onClick={onClose}
              className={`p-2 rounded-xl transition-all duration-200 ${
                resolvedTheme === 'dark'
                  ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700/50'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100/50'
              }`}
            >
              <span className="text-2xl">×</span>
            </button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-6">
          {/* Customizable Ingredients */}
          {menuItem.is_customizable && (
            <div className="mb-6">
              <h3 className={`text-lg font-semibold mb-4 ${
                resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                Customize Your {menuItem.name}
                {menuItem.max_ingredients && (
                  <span className={`text-sm font-normal ml-2 ${
                    resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                  }`}>
                    (Max {menuItem.max_ingredients} ingredients)
                  </span>
                )}
              </h3>

              {loading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map(i => (
                    <div key={i} className={`h-16 rounded-xl animate-pulse ${
                      resolvedTheme === 'dark' ? 'bg-gray-700/30' : 'bg-gray-200/50'
                    }`} />
                  ))}
                </div>
              ) : (
                <div className="space-y-6">
                  {Object.entries(ingredientCategories).map(([categoryName, ingredients]) => (
                    <div key={categoryName}>
                      <h4 className={`text-md font-medium mb-3 ${
                        resolvedTheme === 'dark' ? 'text-gray-200' : 'text-gray-800'
                      }`}>
                        {categoryName}
                      </h4>
                      <div className="grid grid-cols-1 gap-2">
                        {ingredients.map((ingredient) => {
                          const isSelected = isIngredientSelected(ingredient);
                          const currentQuantity = getIngredientQuantity(ingredient);
                          
                          return (
                            <div
                              key={ingredient.id}
                              className={`p-3 rounded-xl border transition-all duration-200 ${
                                isSelected
                                  ? resolvedTheme === 'dark'
                                    ? 'bg-blue-600/20 border-blue-500/50'
                                    : 'bg-blue-50 border-blue-300/50'
                                  : resolvedTheme === 'dark'
                                    ? 'bg-gray-700/20 border-gray-600/30 hover:bg-gray-700/40'
                                    : 'bg-gray-50/50 border-gray-200/50 hover:bg-gray-100/50'
                              }`}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                  <input
                                    type="checkbox"
                                    checked={isSelected}
                                    onChange={(e) => handleIngredientToggle(ingredient, e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                  />
                                  <div>
                                    <span className={`font-medium ${
                                      resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
                                    }`}>
                                      {ingredient.name}
                                    </span>
                                    {ingredient.description && (
                                      <p className={`text-sm ${
                                        resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                                      }`}>
                                        {ingredient.description}
                                      </p>
                                    )}
                                  </div>
                                </div>
                                
                                <div className="flex items-center space-x-3">
                                  {ingredient.price > 0 && (
                                    <span className="text-green-500 font-medium">
                                      +€{ingredient.price.toFixed(2)}
                                    </span>
                                  )}
                                  
                                  {isSelected && (
                                    <div className="flex items-center space-x-2">
                                      <button
                                        onClick={() => handleIngredientQuantityChange(ingredient.id, currentQuantity - 1)}
                                        className={`w-8 h-8 rounded-full border flex items-center justify-center ${
                                          resolvedTheme === 'dark'
                                            ? 'border-gray-600 hover:bg-gray-600/50'
                                            : 'border-gray-300 hover:bg-gray-100'
                                        }`}
                                      >
                                        -
                                      </button>
                                      <span className={`w-8 text-center font-medium ${
                                        resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
                                      }`}>
                                        {currentQuantity}
                                      </span>
                                      <button
                                        onClick={() => handleIngredientQuantityChange(ingredient.id, currentQuantity + 1)}
                                        className={`w-8 h-8 rounded-full border flex items-center justify-center ${
                                          resolvedTheme === 'dark'
                                            ? 'border-gray-600 hover:bg-gray-600/50'
                                            : 'border-gray-300 hover:bg-gray-100'
                                        }`}
                                      >
                                        +
                                      </button>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Special Instructions */}
          <div className="mb-6">
            <label className={`block text-sm font-medium mb-2 ${
              resolvedTheme === 'dark' ? 'text-gray-200' : 'text-gray-700'
            }`}>
              Special Instructions (Optional)
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className={`w-full p-3 rounded-xl border focus:ring-2 focus:ring-blue-500 transition-all ${
                resolvedTheme === 'dark'
                  ? 'bg-gray-700/50 border-gray-600 text-white placeholder-gray-400'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
              rows={3}
              placeholder="Any special requests..."
            />
          </div>

          {/* Quantity */}
          <div className="mb-6">
            <label className={`block text-sm font-medium mb-2 ${
              resolvedTheme === 'dark' ? 'text-gray-200' : 'text-gray-700'
            }`}>
              Quantity
            </label>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                className={`w-10 h-10 rounded-full border flex items-center justify-center ${
                  resolvedTheme === 'dark'
                    ? 'border-gray-600 hover:bg-gray-700/50'
                    : 'border-gray-300 hover:bg-gray-100'
                }`}
              >
                -
              </button>
              <span className={`text-xl font-semibold w-8 text-center ${
                resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {quantity}
              </span>
              <button
                onClick={() => setQuantity(quantity + 1)}
                className={`w-10 h-10 rounded-full border flex items-center justify-center ${
                  resolvedTheme === 'dark'
                    ? 'border-gray-600 hover:bg-gray-700/50'
                    : 'border-gray-300 hover:bg-gray-100'
                }`}
              >
                +
              </button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200/20 p-6">
          <div className="flex justify-between items-center mb-4">
            <span className={`text-lg font-semibold ${
              resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              Total:
            </span>
            <span className="text-2xl font-bold text-green-500">
              €{totalPrice.toFixed(2)}
            </span>
          </div>
          
          {selectedIngredients.length > 0 && (
            <div className={`text-sm mb-4 ${
              resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
            }`}>
              {selectedIngredients.length} ingredient{selectedIngredients.length !== 1 ? 's' : ''} selected
              {ingredientPrice > 0 && ` (+€${ingredientPrice.toFixed(2)})`}
            </div>
          )}
          
          <button
            onClick={handleAddToCart}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-xl font-semibold hover:bg-blue-700 transition-all duration-200 transform hover:scale-[1.02] active:scale-98"
          >
            Add to Cart
          </button>
        </div>
      </div>
    </div>
  );
};