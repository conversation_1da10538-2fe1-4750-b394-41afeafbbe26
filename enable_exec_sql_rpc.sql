-- Enable exec_sql RPC function for full SQL execution via MCP tools
-- This function allows executing arbitrary SQL commands through the Supabase API

CREATE OR REPLACE FUNCTION exec_sql(sql text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result json;
BEGIN
    -- Execute the SQL and return results as JSON
    EXECUTE sql;
    
    -- For queries that return data, we need to handle them differently
    -- This is a simplified version - you may need to adjust based on your needs
    IF sql ILIKE 'SELECT%' THEN
        EXECUTE 'SELECT json_agg(row_to_json(t)) FROM (' || sql || ') t' INTO result;
        RETURN COALESCE(result, '[]'::json);
    ELSE
        -- For non-SELECT queries, return success status
        RETURN '{"success": true}'::json;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        -- Return error information
        RETURN json_build_object(
            'error', SQLERRM,
            'sqlstate', SQLSTATE
        );
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION exec_sql(text) TO authenticated;

-- <PERSON> execute permission to service role
GRANT EXECUTE ON FUNCTION exec_sql(text) TO service_role;

-- Optional: Grant to anon role if you want anonymous access (not recommended for production)
-- GRANT EXECUTE ON FUNCTION exec_sql(text) TO anon;