{"name": "@creperie/shared-services", "version": "1.0.0", "description": "Shared business logic services for Creperie ecosystem", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src --ext .ts"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "winston": "^3.11.0", "redis": "^4.6.10", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.2", "jest": "^29.7.0", "@types/jest": "^29.5.8", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0"}, "peerDependencies": {"@creperie/shared": "*"}}