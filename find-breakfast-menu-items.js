const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function findBreakfastMenuItems() {
  try {
    console.log('🔍 Searching for Breakfast category and its menu items...\n');

    // First, find the Breakfast category
    const { data: categories, error: categoryError } = await supabase
      .from('categories')
      .select('id, name_en, name_el')
      .ilike('name_en', '%breakfast%');

    if (categoryError) {
      console.error('❌ Error finding Breakfast category:', categoryError);
      return;
    }

    if (!categories || categories.length === 0) {
      console.log('❌ No Breakfast category found');
      return;
    }

    console.log('📂 Found Breakfast category:');
    categories.forEach(cat => {
      console.log(`   ID: ${cat.id}`);
      console.log(`   Name (EN): ${cat.name_en}`);
      console.log(`   Name (EL): ${cat.name_el}`);
    });

    const breakfastCategoryId = categories[0].id;
    console.log(`\n🔍 Looking for menu items in category: ${breakfastCategoryId}\n`);

    // Find all menu items in the Breakfast category
    const { data: menuItems, error: menuItemsError } = await supabase
      .from('menu_items')
      .select('*')
      .eq('category_id', breakfastCategoryId);

    if (menuItemsError) {
      console.error('❌ Error finding menu items:', menuItemsError);
      return;
    }

    if (!menuItems || menuItems.length === 0) {
      console.log('✅ No menu items found in Breakfast category');
      return;
    }

    console.log(`📋 Found ${menuItems.length} menu items in Breakfast category:\n`);
    
    menuItems.forEach((item, index) => {
      console.log(`${index + 1}. Menu Item:`);
      console.log(`   ID: ${item.id}`);
      console.log(`   Name (EN): ${item.name_en || 'N/A'}`);
      console.log(`   Name (EL): ${item.name_el || 'N/A'}`);
      console.log(`   Description: ${item.description_en || item.description_el || 'N/A'}`);
      console.log(`   Price: ${item.price || 'N/A'}`);
      console.log(`   Available: ${item.is_available ? 'Yes' : 'No'}`);
      console.log(`   Created: ${item.created_at}`);
      console.log('   ---');
    });

    console.log('\n💡 To delete the Breakfast category, you need to either:');
    console.log('   1. Delete these menu items, OR');
    console.log('   2. Move these menu items to another category');
    console.log('\n🔧 You can do this by updating the category_id field in the menu_items table');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the function
findBreakfastMenuItems();