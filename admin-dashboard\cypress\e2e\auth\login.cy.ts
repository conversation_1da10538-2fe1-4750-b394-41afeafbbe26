describe('Admin Authentication', () => {
  beforeEach(() => {
    cy.visit('/auth/signin');
  });

  describe('Login Page', () => {
    it('should display login form', () => {
      cy.get('[data-testid="signin-form"]').should('be.visible');
      cy.get('[data-testid="email-input"]').should('be.visible');
      cy.get('[data-testid="password-input"]').should('be.visible');
      cy.get('[data-testid="signin-button"]').should('be.visible');
      
      // Check page title and branding
      cy.title().should('include', 'Admin Login');
      cy.get('[data-testid="logo"]').should('be.visible');
    });

    it('should show validation errors for empty fields', () => {
      cy.get('[data-testid="signin-button"]').click();
      
      cy.get('[data-testid="email-error"]')
        .should('be.visible')
        .and('contain.text', 'Email is required');
      
      cy.get('[data-testid="password-error"]')
        .should('be.visible')
        .and('contain.text', 'Password is required');
    });

    it('should show validation error for invalid email format', () => {
      cy.get('[data-testid="email-input"]').type('invalid-email');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="signin-button"]').click();
      
      cy.get('[data-testid="email-error"]')
        .should('be.visible')
        .and('contain.text', 'Please enter a valid email');
    });

    it('should show error for invalid credentials', () => {
      // Mock failed login response
      cy.intercept('POST', '/api/auth/signin', {
        statusCode: 401,
        body: { error: 'Invalid login credentials' }
      }).as('failedLogin');

      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('wrongpassword');
      cy.get('[data-testid="signin-button"]').click();
      
      cy.wait('@failedLogin');
      
      cy.get('[data-testid="error-message"]')
        .should('be.visible')
        .and('contain.text', 'Invalid login credentials');
    });

    it('should show error for non-admin users', () => {
      // Mock non-admin user response
      cy.intercept('POST', '/api/auth/signin', {
        statusCode: 403,
        body: { error: 'Access denied. Admin role required.' }
      }).as('nonAdminLogin');

      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="signin-button"]').click();
      
      cy.wait('@nonAdminLogin');
      
      cy.get('[data-testid="error-message"]')
        .should('be.visible')
        .and('contain.text', 'Access denied. Admin role required.');
    });

    it('should successfully login with valid admin credentials', () => {
      // Mock successful login response
      cy.intercept('POST', '/api/auth/signin', {
        statusCode: 200,
        body: {
          user: {
            id: 'admin-1',
            email: '<EMAIL>',
            role: 'admin'
          },
          session: {
            access_token: 'mock-token',
            expires_in: 3600
          }
        }
      }).as('successfulLogin');

      cy.get('[data-testid="email-input"]').type(Cypress.env('TEST_USER_EMAIL'));
      cy.get('[data-testid="password-input"]').type(Cypress.env('TEST_USER_PASSWORD'));
      cy.get('[data-testid="signin-button"]').click();
      
      cy.wait('@successfulLogin');
      
      // Should redirect to dashboard
      cy.url().should('include', '/dashboard');
      cy.get('[data-testid="dashboard-header"]').should('be.visible');
    });

    it('should show loading state during login', () => {
      // Mock delayed response
      cy.intercept('POST', '/api/auth/signin', (req) => {
        req.reply((res) => {
          res.delay(2000);
          res.send({
            statusCode: 200,
            body: {
              user: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
              session: { access_token: 'mock-token', expires_in: 3600 }
            }
          });
        });
      }).as('delayedLogin');

      cy.get('[data-testid="email-input"]').type(Cypress.env('TEST_USER_EMAIL'));
      cy.get('[data-testid="password-input"]').type(Cypress.env('TEST_USER_PASSWORD'));
      cy.get('[data-testid="signin-button"]').click();
      
      // Check loading state
      cy.get('[data-testid="signin-button"]')
        .should('be.disabled')
        .and('contain.text', 'Signing in...');
      
      cy.get('[data-testid="loading-spinner"]').should('be.visible');
      
      cy.wait('@delayedLogin');
      
      // Should redirect after loading
      cy.url().should('include', '/dashboard');
    });

    it('should handle network errors gracefully', () => {
      // Mock network error
      cy.intercept('POST', '/api/auth/signin', { forceNetworkError: true }).as('networkError');

      cy.get('[data-testid="email-input"]').type(Cypress.env('TEST_USER_EMAIL'));
      cy.get('[data-testid="password-input"]').type(Cypress.env('TEST_USER_PASSWORD'));
      cy.get('[data-testid="signin-button"]').click();
      
      cy.wait('@networkError');
      
      cy.get('[data-testid="error-message"]')
        .should('be.visible')
        .and('contain.text', 'Network error. Please check your connection.');
    });

    it('should toggle password visibility', () => {
      cy.get('[data-testid="password-input"]').should('have.attr', 'type', 'password');
      
      cy.get('[data-testid="password-toggle"]').click();
      cy.get('[data-testid="password-input"]').should('have.attr', 'type', 'text');
      
      cy.get('[data-testid="password-toggle"]').click();
      cy.get('[data-testid="password-input"]').should('have.attr', 'type', 'password');
    });

    it('should have forgot password link', () => {
      cy.get('[data-testid="forgot-password-link"]')
        .should('be.visible')
        .and('contain.text', 'Forgot Password?')
        .and('have.attr', 'href', '/auth/forgot-password');
    });

    it('should be accessible', () => {
      cy.checkA11y();
      
      // Test keyboard navigation
      cy.get('[data-testid="email-input"]').focus();
      cy.focused().should('have.attr', 'data-testid', 'email-input');
      
      cy.tab();
      cy.focused().should('have.attr', 'data-testid', 'password-input');
      
      cy.tab();
      cy.focused().should('have.attr', 'data-testid', 'password-toggle');
      
      cy.tab();
      cy.focused().should('have.attr', 'data-testid', 'signin-button');
    });

    it('should be responsive', () => {
      cy.testResponsive();
    });
  });

  describe('Session Management', () => {
    it('should redirect authenticated users to dashboard', () => {
      // Mock existing session
      cy.intercept('GET', '/api/auth/session', {
        statusCode: 200,
        body: {
          session: {
            user: { id: 'admin-1', email: '<EMAIL>', role: 'admin' },
            expires: '2024-12-31T23:59:59.999Z'
          }
        }
      }).as('existingSession');

      cy.visit('/auth/signin');
      cy.wait('@existingSession');
      
      // Should redirect to dashboard
      cy.url().should('include', '/dashboard');
    });

    it('should handle session expiry', () => {
      // Mock expired session
      cy.intercept('GET', '/api/auth/session', {
        statusCode: 401,
        body: { error: 'Session expired' }
      }).as('expiredSession');

      cy.visit('/dashboard');
      cy.wait('@expiredSession');
      
      // Should redirect to login
      cy.url().should('include', '/auth/signin');
      cy.get('[data-testid="error-message"]')
        .should('be.visible')
        .and('contain.text', 'Your session has expired. Please log in again.');
    });
  });

  describe('Security Features', () => {
    it('should implement rate limiting', () => {
      // Mock rate limit response
      cy.intercept('POST', '/api/auth/signin', {
        statusCode: 429,
        body: { error: 'Too many login attempts. Please try again later.' }
      }).as('rateLimited');

      // Attempt multiple logins
      for (let i = 0; i < 5; i++) {
        cy.get('[data-testid="email-input"]').clear().type('<EMAIL>');
        cy.get('[data-testid="password-input"]').clear().type('wrongpassword');
        cy.get('[data-testid="signin-button"]').click();
        cy.wait(500);
      }
      
      cy.wait('@rateLimited');
      
      cy.get('[data-testid="error-message"]')
        .should('be.visible')
        .and('contain.text', 'Too many login attempts');
    });

    it('should clear form data on page refresh', () => {
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('password123');
      
      cy.reload();
      
      cy.get('[data-testid="email-input"]').should('have.value', '');
      cy.get('[data-testid="password-input"]').should('have.value', '');
    });

    it('should not expose sensitive data in DOM', () => {
      cy.get('[data-testid="password-input"]').type('secretpassword');
      
      // Check that password is not visible in DOM
      cy.get('body').should('not.contain.text', 'secretpassword');
      
      // Check that no sensitive data is in data attributes
      cy.get('[data-testid="password-input"]')
        .should('not.have.attr', 'data-value')
        .and('not.have.attr', 'value', 'secretpassword');
    });
  });
});