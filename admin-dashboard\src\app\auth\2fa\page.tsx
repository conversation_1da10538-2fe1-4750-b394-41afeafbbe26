'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useSearchParams } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import Link from 'next/link'

import {
  GlassCard as Card,
  GlassCardContent as CardContent,
  GlassCardDescription as CardDescription,
  GlassCardHeader as CardHeader,
  GlassCardTitle as CardTitle,
} from '@/components/ui/glass-components'
import { GlassButton as Button } from '@/components/ui/glass-components'
import { GlassInput as Input } from '@/components/ui/glass-components'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { useAuth } from '@/contexts/auth-context'
import { User } from '../../../../../shared/auth/types'
import { verifyTwoFACode } from '../../../../../shared/auth/two-fa-utils'
import { getUserProfile } from '../../../../../shared/auth/auth-utils'
import { createSession } from '../../../../../shared/auth/session-utils'

const twoFactorSchema = z.object({
  code: z
    .string()
    .length(6, 'Verification code must be 6 digits')
    .regex(/^\d+$/, 'Code must contain only numbers'),
})

type TwoFactorFormData = z.infer<typeof twoFactorSchema>

export default function TwoFactorPage() {
  const { t } = useTranslation()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { login } = useAuth()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [userId, setUserId] = useState<string | null>(null)

  const form = useForm<TwoFactorFormData>({
    resolver: zodResolver(twoFactorSchema),
    defaultValues: {
      code: '',
    },
  })

  useEffect(() => {
    const userIdParam = searchParams.get('userId')
    if (!userIdParam) {
      router.push('/auth/login')
      return
    }
    setUserId(userIdParam)
  }, [searchParams, router])

  const onSubmit = async (data: TwoFactorFormData) => {
    if (!userId) {
      setError('Invalid session')
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      // Verify 2FA code
      const otpResult = await verifyTwoFACode({
        user_id: userId,
        code: data.code,
        is_backup_code: false,
      })

      if (!otpResult.success) {
        setError(otpResult.message || t('auth.twoFactor.errors.invalidCode'))
        return
      }

      // Create session
      const session = await createSession({
        user_id: userId,
        platform: 'admin-dashboard',
        ip_address: '', // Will be set by the server
        user_agent: navigator.userAgent,
      })

      if (!session) {
        setError('Failed to create session')
        return
      }

      // Get user profile
      const profileResult = await getUserProfile(userId)

      if (!profileResult.success || !profileResult.data) {
        setError('Failed to load user profile')
        return
      }

      const userData: User = {
        id: userId,
        created_at: profileResult.data.created_at,
        updated_at: profileResult.data.updated_at,
      }

      // Store session token
      sessionStorage.setItem('auth_session', session.session_token)

      login(userData, profileResult.data)
      router.push('/dashboard')
    } catch (err) {
      console.error('2FA verification error:', err)
      setError(t('auth.twoFactor.errors.invalidCode'))
    } finally {
      setIsLoading(false)
    }
  }

  if (!userId) {
    return null // Will redirect to login
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-100 via-gray-100 to-zinc-100 dark:from-slate-900 dark:via-gray-900 dark:to-zinc-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card variant="success">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center text-white">
              {t('auth.twoFactor.title')}
            </CardTitle>
            <CardDescription className="text-center text-gray-200">
              {t('auth.twoFactor.subtitle')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('auth.twoFactor.code')}</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="123456"
                          maxLength={6}
                          className="text-center text-lg tracking-widest"
                          {...field}
                          disabled={isLoading}
                          autoComplete="one-time-code"
                          inputMode="numeric"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {error && (
                  <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">{error}</div>
                )}

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isLoading || !form.watch('code') || form.watch('code').length !== 6}
                  variant="success"
                >
                  {isLoading ? t('common.loading') : t('auth.twoFactor.verify')}
                </Button>

                <div className="text-center">
                  <Link href="/auth/login" className="text-sm text-blue-600 hover:text-blue-500">
                    {t('auth.twoFactor.backToLogin')}
                  </Link>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
