# Setup Real-time Notifications System
# PowerShell script to deploy Supabase Edge Functions and configure real-time features

param(
    [string]$SupabaseProjectRef = "",
    [string]$SupabaseAccessToken = "",
    [switch]$SkipMigration = $false,
    [switch]$SkipFunctions = $false,
    [switch]$Verbose = $false
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Colors for output
$Green = "Green"
$Yellow = "Yellow"
$Red = "Red"
$Blue = "Blue"

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Test-SupabaseCLI {
    try {
        $version = supabase --version 2>$null
        if ($version) {
            Write-ColorOutput "✓ Supabase CLI found: $version" $Green
            return $true
        }
    } catch {
        Write-ColorOutput "✗ Supabase CLI not found. Please install it first." $Red
        Write-ColorOutput "  Install: npm install -g supabase" $Yellow
        return $false
    }
    return $false
}

function Test-ProjectStructure {
    $requiredPaths = @(
        "supabase/functions",
        "supabase/migrations"
    )
    
    foreach ($path in $requiredPaths) {
        if (-not (Test-Path $path)) {
            Write-ColorOutput "✗ Required directory not found: $path" $Red
            return $false
        }
    }
    
    Write-ColorOutput "✓ Project structure validated" $Green
    return $true
}

function Deploy-EdgeFunction {
    param(
        [string]$FunctionName,
        [string]$ProjectRef,
        [string]$AccessToken
    )
    
    Write-ColorOutput "Deploying Edge Function: $FunctionName" $Blue
    
    try {
        if ($ProjectRef -and $AccessToken) {
            $result = supabase functions deploy $FunctionName --project-ref $ProjectRef --token $AccessToken
        } else {
            $result = supabase functions deploy $FunctionName
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ Successfully deployed $FunctionName" $Green
            return $true
        } else {
            Write-ColorOutput "✗ Failed to deploy $FunctionName" $Red
            return $false
        }
    } catch {
        Write-ColorOutput "✗ Error deploying $FunctionName: $($_.Exception.Message)" $Red
        return $false
    }
}

function Run-Migration {
    param(
        [string]$ProjectRef,
        [string]$AccessToken
    )
    
    Write-ColorOutput "Running database migration..." $Blue
    
    try {
        if ($ProjectRef -and $AccessToken) {
            $result = supabase db push --project-ref $ProjectRef --token $AccessToken
        } else {
            $result = supabase db push
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ Database migration completed successfully" $Green
            return $true
        } else {
            Write-ColorOutput "✗ Database migration failed" $Red
            return $false
        }
    } catch {
        Write-ColorOutput "✗ Error running migration: $($_.Exception.Message)" $Red
        return $false
    }
}

function Set-EnvironmentVariables {
    Write-ColorOutput "Setting up environment variables..." $Blue
    
    $envVars = @(
        "SUPABASE_URL",
        "SUPABASE_ANON_KEY",
        "SUPABASE_SERVICE_ROLE_KEY",
        "RESEND_API_KEY",
        "TWILIO_ACCOUNT_SID",
        "TWILIO_AUTH_TOKEN",
        "TWILIO_PHONE_NUMBER",
        "VAPID_PUBLIC_KEY",
        "VAPID_PRIVATE_KEY",
        "FRONTEND_URL"
    )
    
    $missingVars = @()
    
    foreach ($var in $envVars) {
        $value = [Environment]::GetEnvironmentVariable($var)
        if (-not $value) {
            $missingVars += $var
        } elseif ($Verbose) {
            Write-ColorOutput "✓ $var is set" $Green
        }
    }
    
    if ($missingVars.Count -gt 0) {
        Write-ColorOutput "⚠ Missing environment variables:" $Yellow
        foreach ($var in $missingVars) {
            Write-ColorOutput "  - $var" $Yellow
        }
        Write-ColorOutput "Please set these variables before deploying functions." $Yellow
        return $false
    }
    
    Write-ColorOutput "✓ All required environment variables are set" $Green
    return $true
}

function Test-EdgeFunctions {
    Write-ColorOutput "Testing Edge Functions..." $Blue
    
    $functions = @(
        "send-email-notification",
        "send-sms-notification", 
        "send-push-notification",
        "order-status-trigger"
    )
    
    $allPassed = $true
    
    foreach ($func in $functions) {
        $funcPath = "supabase/functions/$func/index.ts"
        if (Test-Path $funcPath) {
            Write-ColorOutput "✓ Function found: $func" $Green
        } else {
            Write-ColorOutput "✗ Function missing: $func" $Red
            $allPassed = $false
        }
    }
    
    return $allPassed
}

function Show-PostDeploymentInstructions {
    Write-ColorOutput "`n🎉 Real-time Notifications Setup Complete!" $Green
    Write-ColorOutput "`nNext Steps:" $Blue
    Write-ColorOutput "1. Configure webhook in Supabase Dashboard:" $Yellow
    Write-ColorOutput "   - Go to Database > Webhooks" $Yellow
    Write-ColorOutput "   - Create webhook for 'orders' table" $Yellow
    Write-ColorOutput "   - Set URL to: https://[project-ref].supabase.co/functions/v1/order-status-trigger" $Yellow
    Write-ColorOutput "   - Enable for UPDATE events" $Yellow
    
    Write-ColorOutput "2. Test the notification system:" $Yellow
    Write-ColorOutput "   - Create a test order" $Yellow
    Write-ColorOutput "   - Update order status" $Yellow
    Write-ColorOutput "   - Verify notifications are sent" $Yellow
    
    Write-ColorOutput "3. Configure VAPID keys for push notifications:" $Yellow
    Write-ColorOutput "   - Generate VAPID keys: npx web-push generate-vapid-keys" $Yellow
    Write-ColorOutput "   - Set VAPID_PUBLIC_KEY and VAPID_PRIVATE_KEY environment variables" $Yellow
    
    Write-ColorOutput "4. Set up external service credentials:" $Yellow
    Write-ColorOutput "   - Resend API key for email notifications" $Yellow
    Write-ColorOutput "   - Twilio credentials for SMS notifications" $Yellow
    
    Write-ColorOutput "5. Enable Realtime in Supabase Dashboard:" $Yellow
    Write-ColorOutput "   - Go to Settings > API" $Yellow
    Write-ColorOutput "   - Enable Realtime for required tables" $Yellow
    
    Write-ColorOutput "`nDocumentation:" $Blue
    Write-ColorOutput "- Supabase Realtime: https://supabase.com/docs/guides/realtime" $Yellow
    Write-ColorOutput "- Edge Functions: https://supabase.com/docs/guides/functions" $Yellow
    Write-ColorOutput "- Push Notifications: https://developer.mozilla.org/en-US/docs/Web/API/Push_API" $Yellow
}

# Main execution
Write-ColorOutput "🚀 Setting up Real-time Notifications System" $Blue
Write-ColorOutput "=" * 50 $Blue

# Validate prerequisites
if (-not (Test-SupabaseCLI)) {
    exit 1
}

if (-not (Test-ProjectStructure)) {
    exit 1
}

if (-not (Test-EdgeFunctions)) {
    Write-ColorOutput "Some Edge Functions are missing. Please ensure all functions are created." $Red
    exit 1
}

# Check environment variables
if (-not $SkipFunctions) {
    Set-EnvironmentVariables
}

# Run database migration
if (-not $SkipMigration) {
    Write-ColorOutput "`n📊 Running Database Migration" $Blue
    Write-ColorOutput "-" * 30 $Blue
    
    if (-not (Run-Migration $SupabaseProjectRef $SupabaseAccessToken)) {
        Write-ColorOutput "Migration failed. Aborting deployment." $Red
        exit 1
    }
}

# Deploy Edge Functions
if (-not $SkipFunctions) {
    Write-ColorOutput "`n⚡ Deploying Edge Functions" $Blue
    Write-ColorOutput "-" * 30 $Blue
    
    $functions = @(
        "send-email-notification",
        "send-sms-notification",
        "send-push-notification",
        "order-status-trigger"
    )
    
    $deploymentResults = @()
    
    foreach ($func in $functions) {
        $success = Deploy-EdgeFunction $func $SupabaseProjectRef $SupabaseAccessToken
        $deploymentResults += @{ Function = $func; Success = $success }
        
        if (-not $success) {
            Write-ColorOutput "⚠ Continuing with other functions..." $Yellow
        }
        
        Start-Sleep -Seconds 2  # Brief pause between deployments
    }
    
    # Summary of deployments
    Write-ColorOutput "`n📋 Deployment Summary" $Blue
    Write-ColorOutput "-" * 20 $Blue
    
    $successCount = 0
    foreach ($result in $deploymentResults) {
        if ($result.Success) {
            Write-ColorOutput "✓ $($result.Function)" $Green
            $successCount++
        } else {
            Write-ColorOutput "✗ $($result.Function)" $Red
        }
    }
    
    Write-ColorOutput "`nDeployed: $successCount/$($functions.Count) functions" $Blue
    
    if ($successCount -lt $functions.Count) {
        Write-ColorOutput "⚠ Some functions failed to deploy. Check the logs above." $Yellow
    }
}

# Show post-deployment instructions
Show-PostDeploymentInstructions

Write-ColorOutput "`n✨ Setup script completed!" $Green