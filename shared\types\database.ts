export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      app_configurations: {
        Row: {
          app_name: string
          applies_to_branch: string | null
          config_key: string
          config_value: <PERSON><PERSON>
          created_at: string | null
          environment: string
          id: string
          is_active: boolean
          is_sensitive: boolean
          updated_at: string | null
          updated_by: string | null
          version: string | null
        }
        Insert: {
          app_name: string
          applies_to_branch?: string | null
          config_key: string
          config_value: J<PERSON>
          created_at?: string | null
          environment?: string
          id?: string
          is_active?: boolean
          is_sensitive?: boolean
          updated_at?: string | null
          updated_by?: string | null
          version?: string | null
        }
        Update: {
          app_name?: string
          applies_to_branch?: string | null
          config_key?: string
          config_value?: Json
          created_at?: string | null
          environment?: string
          id?: string
          is_active?: boolean
          is_sensitive?: boolean
          updated_at?: string | null
          updated_by?: string | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "app_configurations_applies_to_branch_fkey"
            columns: ["applies_to_branch"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          },
        ]
      }
      customers: {
        Row: {
          address: string | null
          created_at: string | null
          email: string | null
          id: string
          loyalty_points: number | null
          name: string | null
          notes: string | null
          phone: string | null
          postal_code: string | null
          ringer_name: string | null
          total_orders: number | null
          updated_at: string | null
        }
        Insert: {
          address?: string | null
          created_at?: string | null
          email?: string | null
          id?: string
          loyalty_points?: number | null
          name?: string | null
          notes?: string | null
          phone?: string | null
          postal_code?: string | null
          ringer_name?: string | null
          total_orders?: number | null
          updated_at?: string | null
        }
        Update: {
          address?: string | null
          created_at?: string | null
          email?: string | null
          id?: string
          loyalty_points?: number | null
          name?: string | null
          notes?: string | null
          phone?: string | null
          postal_code?: string | null
          ringer_name?: string | null
          total_orders?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      delivery_zones: {
        Row: {
          id: string
          name: string
          description: string | null
          coordinates: Json
          delivery_fee: number
          minimum_order_amount: number
          estimated_delivery_time_min: number
          estimated_delivery_time_max: number
          is_active: boolean
          priority: number
          color: string | null
          branch_id: string | null
          created_at: string | null
          updated_at: string | null
          created_by: string | null
          updated_by: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          coordinates: Json
          delivery_fee?: number
          minimum_order_amount?: number
          estimated_delivery_time_min?: number
          estimated_delivery_time_max?: number
          is_active?: boolean
          priority?: number
          color?: string | null
          branch_id?: string | null
          created_at?: string | null
          updated_at?: string | null
          created_by?: string | null
          updated_by?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          coordinates?: Json
          delivery_fee?: number
          minimum_order_amount?: number
          estimated_delivery_time_min?: number
          estimated_delivery_time_max?: number
          is_active?: boolean
          priority?: number
          color?: string | null
          branch_id?: string | null
          created_at?: string | null
          updated_at?: string | null
          created_by?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "delivery_zones_branch_id_fkey"
            columns: ["branch_id"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          }
        ]
      }
      subcategories: {
        Row: {
          allergens: string[] | null
          category_id: string
          created_at: string | null
          description_el: string | null
          description_en: string | null
          display_order: number | null
          id: string
          image_url: string | null
          is_available: boolean | null
          name_el: string
          name_en: string
          preparation_time: number | null
          price: number
          updated_at: string | null
        }
        Insert: {
          allergens?: string[] | null
          category_id: string
          created_at?: string | null
          description_el?: string | null
          description_en?: string | null
          display_order?: number | null
          id?: string
          image_url?: string | null
          is_available?: boolean | null
          name_el: string
          name_en: string
          preparation_time?: number | null
          price: number
          updated_at?: string | null
        }
        Update: {
          allergens?: string[] | null
          category_id?: string
          created_at?: string | null
          description_el?: string | null
          description_en?: string | null
          display_order?: number | null
          id?: string
          image_url?: string | null
          is_available?: boolean | null
          name_el?: string
          name_en?: string
          preparation_time?: number | null
          price?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subcategories_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      menu_categories: {
        Row: {
          created_at: string | null
          description_el: string | null
          description_en: string | null
          display_order: number | null
          id: string
          is_active: boolean | null
          name_el: string
          name_en: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description_el?: string | null
          description_en?: string | null
          display_order?: number | null
          id?: string
          is_active?: boolean | null
          name_el: string
          name_en: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description_el?: string | null
          description_en?: string | null
          display_order?: number | null
          id?: string
          is_active?: boolean | null
          name_el?: string
          name_en?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      orders: {
        Row: {
          created_at: string | null
          customer_email: string | null
          customer_id: string | null
          customer_name: string | null
          customer_phone: string | null
          delivery_address: string | null
          delivery_fee: number | null
          delivery_zone_id: string | null
          discount_amount: number | null
          id: string
          local_id: number | null
          notes: string | null
          order_type: string
          payment_method: string | null
          payment_status: string | null
          pickup_discount: number | null
          pricing_calculated_at: string | null
          pricing_version: string | null
          service_fee: number | null
          staff_id: string | null
          status: string
          subtotal: number | null
          table_number: number | null
          tax_amount: number | null
          total_amount: number
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          customer_email?: string | null
          customer_id?: string | null
          customer_name?: string | null
          customer_phone?: string | null
          delivery_address?: string | null
          delivery_fee?: number | null
          delivery_zone_id?: string | null
          discount_amount?: number | null
          id?: string
          local_id?: number | null
          notes?: string | null
          order_type?: string
          payment_method?: string | null
          payment_status?: string | null
          pickup_discount?: number | null
          pricing_calculated_at?: string | null
          pricing_version?: string | null
          service_fee?: number | null
          staff_id?: string | null
          status?: string
          subtotal?: number | null
          table_number?: number | null
          tax_amount?: number | null
          total_amount: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          customer_email?: string | null
          customer_id?: string | null
          customer_name?: string | null
          customer_phone?: string | null
          delivery_address?: string | null
          delivery_fee?: number | null
          delivery_zone_id?: string | null
          discount_amount?: number | null
          id?: string
          local_id?: number | null
          notes?: string | null
          order_type?: string
          payment_method?: string | null
          payment_status?: string | null
          pickup_discount?: number | null
          pricing_calculated_at?: string | null
          pricing_version?: string | null
          service_fee?: number | null
          staff_id?: string | null
          status?: string
          subtotal?: number | null
          table_number?: number | null
          tax_amount?: number | null
          total_amount?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "orders_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_delivery_zone_id_fkey"
            columns: ["delivery_zone_id"]
            isOneToOne: false
            referencedRelation: "delivery_zones"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_staff_id_fkey"
            columns: ["staff_id"]
            isOneToOne: false
            referencedRelation: "staff"
            referencedColumns: ["id"]
          },
        ]
      }
      order_pricing_history: {
        Row: {
          calculated_by: string | null
          calculation_timestamp: string | null
          calculation_type: string
          delivery_fee: number | null
          delivery_zone_id: string | null
          delivery_zone_name: string | null
          id: string
          order_id: string
          order_type: string
          pickup_discount: number | null
          pricing_rules_applied: Json | null
          service_fee: number | null
          subtotal: number
          tax_amount: number | null
          total_amount: number
        }
        Insert: {
          calculated_by?: string | null
          calculation_timestamp?: string | null
          calculation_type: string
          delivery_fee?: number | null
          delivery_zone_id?: string | null
          delivery_zone_name?: string | null
          id?: string
          order_id: string
          order_type: string
          pickup_discount?: number | null
          pricing_rules_applied?: Json | null
          service_fee?: number | null
          subtotal: number
          tax_amount?: number | null
          total_amount: number
        }
        Update: {
          calculated_by?: string | null
          calculation_timestamp?: string | null
          calculation_type?: string
          delivery_fee?: number | null
          delivery_zone_id?: string | null
          delivery_zone_name?: string | null
          id?: string
          order_id?: string
          order_type?: string
          pickup_discount?: number | null
          pricing_rules_applied?: Json | null
          service_fee?: number | null
          subtotal?: number
          tax_amount?: number | null
          total_amount?: number
        }
        Relationships: [
          {
            foreignKeyName: "order_pricing_history_delivery_zone_id_fkey"
            columns: ["delivery_zone_id"]
            isOneToOne: false
            referencedRelation: "delivery_zones"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_pricing_history_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
        ]
      }
      order_items: {
        Row: {
          created_at: string | null
          customizations: Json | null
          id: string
          subcategory_id: string
          order_id: string
          quantity: number
          unit_price: number
        }
        Insert: {
          created_at?: string | null
          customizations?: Json | null
          id?: string
          subcategory_id: string
          order_id: string
          quantity: number
          unit_price: number
        }
        Update: {
          created_at?: string | null
          customizations?: Json | null
          id?: string
          subcategory_id?: string
          order_id?: string
          quantity?: number
          unit_price?: number
        }
        Relationships: [
          {
            foreignKeyName: "order_items_subcategory_id_fkey"
            columns: ["subcategory_id"]
            isOneToOne: false
            referencedRelation: "subcategories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
        ]
      }
      pos_configurations: {
        Row: {
          branch_id: string | null
          config_key: string
          config_type: string
          config_value: Json
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean
          last_sync_at: string | null
          sync_status: string
          terminal_id: string
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          branch_id?: string | null
          config_key: string
          config_type: string
          config_value: Json
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean
          last_sync_at?: string | null
          sync_status?: string
          terminal_id: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          branch_id?: string | null
          config_key?: string
          config_type?: string
          config_value?: Json
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean
          last_sync_at?: string | null
          sync_status?: string
          terminal_id?: string
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pos_configurations_branch_id_fkey"
            columns: ["branch_id"]
            isOneToOne: false
            referencedRelation: "branches"
            referencedColumns: ["id"]
          },
        ]
      }
      restaurant_settings: {
        Row: {
          category: string
          created_at: string | null
          description: string | null
          id: string
          is_public: boolean
          requires_restart: boolean
          setting_key: string
          setting_type: string
          setting_value: Json
          updated_at: string | null
          updated_by: string | null
          validation_rules: Json | null
        }
        Insert: {
          category: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean
          requires_restart?: boolean
          setting_key: string
          setting_type: string
          setting_value: Json
          updated_at?: string | null
          updated_by?: string | null
          validation_rules?: Json | null
        }
        Update: {
          category?: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean
          requires_restart?: boolean
          setting_key?: string
          setting_type?: string
          setting_value?: Json
          updated_at?: string | null
          updated_by?: string | null
          validation_rules?: Json | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (Database["public"]["Tables"] & Database["public"]["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (Database["public"]["Tables"] &
        Database["public"]["Views"])
    ? (Database["public"]["Tables"] &
        Database["public"]["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof Database["public"]["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof Database["public"]["Tables"]
    ? Database["public"]["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof Database["public"]["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof Database["public"]["Tables"]
    ? Database["public"]["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never