import React from 'react';
import { useTheme } from '../../contexts/theme-context';

interface CustomizationOption {
  id: string;
  name: string;
  price: number;
}

interface Customization {
  id: string;
  name: string;
  required: boolean;
  options: CustomizationOption[];
}

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  preparationTime: number;
  image?: string;
  customizations?: Customization[];
}

interface MenuItemCardProps {
  item: MenuItem;
  orderType?: 'pickup' | 'delivery';
  onSelect: () => void;
}

export const MenuItemCard: React.FC<MenuItemCardProps> = ({ item, orderType = 'delivery', onSelect }) => {
  const { resolvedTheme } = useTheme();

  return (
    <div
      onClick={onSelect}
      className={`p-4 rounded-xl border cursor-pointer transition-all duration-200 hover:scale-[1.02] ${
        resolvedTheme === 'dark'
          ? 'bg-gray-700/30 border-gray-600/30 hover:bg-gray-700/50 hover:border-gray-500/50'
          : 'bg-white/30 border-gray-200/30 hover:bg-white/50 hover:border-gray-300/50'
      }`}
    >
      <div className="text-4xl mb-3 text-center">{item.image}</div>
      <h3 className={`font-semibold mb-2 ${
        resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
      }`}>
        {item.name}
      </h3>
      <p className={`text-sm mb-3 line-clamp-2 ${
        resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
      }`}>
        {item.description}
      </p>
      <div className="flex justify-between items-center">
        <div className="flex flex-col">
          <span className={`font-bold ${
            resolvedTheme === 'dark' ? 'text-emerald-400' : 'text-emerald-600'
          }`}>
            €{item.price.toFixed(2)}
          </span>
          <span className={`text-xs ${
            resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
          }`}>
            {orderType === 'pickup' ? 'Pickup' : 'Delivery'}
          </span>
        </div>
        <span className={`text-xs px-2 py-1 rounded-full ${
          resolvedTheme === 'dark'
            ? 'bg-gray-600/50 text-gray-300'
            : 'bg-gray-100/50 text-gray-600'
        }`}>
          {item.preparationTime} min
        </span>
      </div>
    </div>
  );
};