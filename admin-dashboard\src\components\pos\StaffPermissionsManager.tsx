'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import {
  Users,
  Shield,
  Settings,
  Plus,
  Edit,
  Trash2,
  Copy,
  Download,
  Upload,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Eye,
  EyeOff
} from 'lucide-react'

interface StaffMember {
  id: string
  staff_code: string
  first_name: string
  last_name: string
  email: string
  phone?: string
  role?: {
    id: string
    name: string
    display_name: string
  }
  is_active: boolean
  created_at: string
  last_login_at?: string | null
  assigned_terminals?: string[]
}

interface Permission {
  key: string
  name: string
  description: string
  category: string
  default_value: boolean
}

interface StaffPermission {
  id: string
  staff_id: string
  permission_key: string
  permission_value: boolean
  terminal_id: string | null
  sync_status: 'pending' | 'synced' | 'failed'
  last_sync_at: string | null
  created_at: string
}

interface PermissionTemplate {
  id: string
  name: string
  description: string
  permissions: Record<string, boolean>
  is_default: boolean
}

export default function StaffPermissionsManager() {
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([])
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [staffPermissions, setStaffPermissions] = useState<StaffPermission[]>([])
  const [permissionTemplates, setPermissionTemplates] = useState<PermissionTemplate[]>([])
  const [selectedStaff, setSelectedStaff] = useState<string>('')
  const [selectedTerminal, setSelectedTerminal] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterRole, setFilterRole] = useState('all')
  const [filterStatus, setFilterStatus] = useState('all')
  const [showTemplateModal, setShowTemplateModal] = useState(false)
  const [showBulkModal, setShowBulkModal] = useState(false)
  const [loading, setLoading] = useState(true)
  const [syncing, setSyncing] = useState(false)

  // Permission categories for organization
  const permissionCategories = [
    { id: 'pos_operations', name: 'POS Operations', icon: Settings },
    { id: 'order_management', name: 'Order Management', icon: Users },
    { id: 'payment_processing', name: 'Payment Processing', icon: Shield },
    { id: 'inventory_access', name: 'Inventory Access', icon: Eye },
    { id: 'reporting', name: 'Reporting', icon: Download },
    { id: 'system_admin', name: 'System Administration', icon: Settings }
  ]

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      await Promise.all([
        loadStaffMembers(),
        loadPermissions(),
        loadStaffPermissions(),
        loadPermissionTemplates()
      ])
    } catch (error) {
      console.error('Failed to load data:', error)
      toast.error('Failed to load staff permissions data')
    } finally {
      setLoading(false)
    }
  }

  const loadStaffMembers = async () => {
    try {
      const response = await fetch('/api/pos/staff')
      if (response.ok) {
        const data = await response.json()
        setStaffMembers(data.data || [])
      }
    } catch (error) {
      console.error('Failed to load staff members:', error)
    }
  }

  const loadPermissions = async () => {
    try {
      const response = await fetch('/api/pos/permissions')
      if (response.ok) {
        const data = await response.json()
        setPermissions(data.permissions || [])
      }
    } catch (error) {
      console.error('Failed to load permissions:', error)
    }
  }

  const loadStaffPermissions = async () => {
    try {
      const response = await fetch('/api/pos/staff-permissions')
      if (response.ok) {
        const data = await response.json()
        setStaffPermissions(data.permissions || [])
      }
    } catch (error) {
      console.error('Failed to load staff permissions:', error)
    }
  }

  const loadPermissionTemplates = async () => {
    try {
      const response = await fetch('/api/pos/permission-templates')
      if (response.ok) {
        const data = await response.json()
        setPermissionTemplates(data.templates || [])
      }
    } catch (error) {
      console.error('Failed to load permission templates:', error)
    }
  }

  const updateStaffPermission = async (
    staffId: string, 
    permissionKey: string, 
    value: boolean, 
    terminalId: string | null = null
  ) => {
    try {
      const response = await fetch('/api/pos/sync-permissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          staff_id: staffId,
          permission_key: permissionKey,
          permission_value: value,
          terminal_id: terminalId,
          force_sync: false
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          toast.success(`Permission ${permissionKey} updated successfully`)
          await loadStaffPermissions()
        } else {
          toast.error(`Failed to update permission: ${result.errors?.join(', ')}`)
        }
      } else {
        toast.error('Failed to update permission')
      }
    } catch (error) {
      console.error('Failed to update staff permission:', error)
      toast.error('Failed to update permission')
    }
  }

  const applyTemplate = async (staffId: string, templateId: string) => {
    try {
      setSyncing(true)
      const template = permissionTemplates.find(t => t.id === templateId)
      if (!template) {
        toast.error('Template not found')
        return
      }

      const promises = Object.entries(template.permissions).map(([key, value]) =>
        updateStaffPermission(staffId, key, value, selectedTerminal === 'all' ? null : selectedTerminal)
      )

      await Promise.all(promises)
      toast.success(`Applied template "${template.name}" successfully`)
    } catch (error) {
      console.error('Failed to apply template:', error)
      toast.error('Failed to apply template')
    } finally {
      setSyncing(false)
    }
  }

  const syncAllPermissions = async () => {
    try {
      setSyncing(true)
      const response = await fetch('/api/pos/sync-permissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'sync_all',
          terminal_id: selectedTerminal === 'all' ? null : selectedTerminal,
          force_sync: true
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          toast.success('All permissions synchronized successfully')
          await loadStaffPermissions()
        } else {
          toast.error(`Sync failed: ${result.errors?.join(', ')}`)
        }
      } else {
        toast.error('Failed to sync permissions')
      }
    } catch (error) {
      console.error('Failed to sync permissions:', error)
      toast.error('Failed to sync permissions')
    } finally {
      setSyncing(false)
    }
  }

  const getStaffPermissionValue = (staffId: string, permissionKey: string): boolean => {
    const permission = staffPermissions.find(
      p => p.staff_id === staffId && 
           p.permission_key === permissionKey &&
           (selectedTerminal === 'all' || p.terminal_id === selectedTerminal || p.terminal_id === null)
    )
    return permission?.permission_value ?? false
  }

  const getPermissionSyncStatus = (staffId: string, permissionKey: string): 'pending' | 'synced' | 'failed' => {
    const permission = staffPermissions.find(
      p => p.staff_id === staffId && 
           p.permission_key === permissionKey &&
           (selectedTerminal === 'all' || p.terminal_id === selectedTerminal || p.terminal_id === null)
    )
    return permission?.sync_status ?? 'synced'
  }

  const filteredStaffMembers = staffMembers.filter(staff => {
    const matchesSearch = 
      staff.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staff.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staff.staff_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staff.email.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesRole = filterRole === 'all' || staff.role?.name === filterRole
    const matchesStatus = filterStatus === 'all' || 
      (filterStatus === 'active' && staff.is_active) ||
      (filterStatus === 'inactive' && !staff.is_active)
    
    return matchesSearch && matchesRole && matchesStatus
  })

  const groupedPermissions = permissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = []
    }
    acc[permission.category].push(permission)
    return acc
  }, {} as Record<string, Permission[]>)

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Staff Permissions</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Manage staff permissions and access control for POS terminals
          </p>
        </div>
        
        <div className="flex gap-2 mt-4 sm:mt-0">
          <button
            onClick={() => setShowTemplateModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Copy className="w-4 h-4" />
            Templates
          </button>
          
          <button
            onClick={() => setShowBulkModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            <Users className="w-4 h-4" />
            Bulk Actions
          </button>
          
          <button
            onClick={syncAllPermissions}
            disabled={syncing}
            className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
          >
            {syncing ? (
              <div className="w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
            ) : (
              <Shield className="w-4 h-4" />
            )}
            {syncing ? 'Syncing...' : 'Sync All'}
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search Staff
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search by name, code, or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Role
            </label>
            <select
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Roles</option>
              <option value="admin">Admin</option>
              <option value="manager">Manager</option>
              <option value="cashier">Cashier</option>
              <option value="kitchen">Kitchen Staff</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status
            </label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Terminal
            </label>
            <select
              value={selectedTerminal}
              onChange={(e) => setSelectedTerminal(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Terminals</option>
              <option value="terminal-001">Terminal 001</option>
              <option value="terminal-002">Terminal 002</option>
              <option value="terminal-003">Terminal 003</option>
            </select>
          </div>
        </div>
      </div>

      {/* Permissions Matrix */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Permissions Matrix</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Manage individual permissions for each staff member
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="sticky left-0 bg-gray-50 dark:bg-gray-700 px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600">
                  Staff Member
                </th>
                {Object.entries(groupedPermissions).map(([category, perms]) => (
                  <th key={category} colSpan={perms.length} className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600">
                    {permissionCategories.find(c => c.id === category)?.name || category}
                  </th>
                ))}
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
              <tr className="bg-gray-50 dark:bg-gray-700">
                <th className="sticky left-0 bg-gray-50 dark:bg-gray-700 px-6 py-2 border-r border-gray-200 dark:border-gray-600"></th>
                {Object.entries(groupedPermissions).map(([category, perms]) =>
                  perms.map((permission) => (
                    <th key={permission.key} className="px-2 py-2 text-xs text-gray-500 dark:text-gray-400 border-r border-gray-200 dark:border-gray-600 min-w-[80px]">
                      <div className="transform -rotate-45 origin-bottom-left whitespace-nowrap">
                        {permission.name}
                      </div>
                    </th>
                  ))
                )}
                <th className="px-6 py-2"></th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredStaffMembers.map((staff) => (
                <tr key={staff.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="sticky left-0 bg-white dark:bg-gray-800 px-6 py-4 border-r border-gray-200 dark:border-gray-600">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                            {staff.first_name[0]}{staff.last_name[0]}
                          </span>
                        </div>
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {staff.first_name} {staff.last_name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {staff.staff_code} • {staff.role?.display_name || staff.role?.name || 'No Role'}
                        </div>
                      </div>
                      {!staff.is_active && (
                        <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                          Inactive
                        </span>
                      )}
                    </div>
                  </td>

                  {Object.entries(groupedPermissions).map(([category, perms]) =>
                    perms.map((permission) => {
                      const hasPermission = getStaffPermissionValue(staff.id, permission.key)
                      const syncStatus = getPermissionSyncStatus(staff.id, permission.key)

                      return (
                        <td key={permission.key} className="px-2 py-4 text-center border-r border-gray-200 dark:border-gray-600">
                          <div className="flex flex-col items-center gap-1">
                            <button
                              onClick={() => updateStaffPermission(
                                staff.id,
                                permission.key,
                                !hasPermission,
                                selectedTerminal === 'all' ? null : selectedTerminal
                              )}
                              disabled={!staff.is_active}
                              className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors ${
                                hasPermission
                                  ? 'bg-green-500 border-green-500 text-white'
                                  : 'bg-white border-gray-300 hover:border-gray-400'
                              } ${!staff.is_active ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                            >
                              {hasPermission && <CheckCircle className="w-4 h-4" />}
                            </button>

                            {/* Sync Status Indicator */}
                            <div className={`w-2 h-2 rounded-full ${
                              syncStatus === 'synced' ? 'bg-green-400' :
                              syncStatus === 'pending' ? 'bg-yellow-400' :
                              'bg-red-400'
                            }`} title={`Sync status: ${syncStatus}`} />
                          </div>
                        </td>
                      )
                    })
                  )}

                  <td className="px-6 py-4 text-center">
                    <div className="flex items-center justify-center gap-2">
                      <button
                        onClick={() => setSelectedStaff(staff.id)}
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        title="Edit permissions"
                      >
                        <Edit className="w-4 h-4" />
                      </button>

                      <button
                        onClick={() => {/* Copy permissions */}}
                        className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                        title="Copy permissions"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredStaffMembers.length === 0 && (
          <div className="text-center py-12">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No staff members found</h3>
            <p className="text-gray-600 dark:text-gray-400">
              Try adjusting your search criteria or add new staff members.
            </p>
          </div>
        )}
      </div>

      {/* Permission Templates Modal */}
      {showTemplateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Permission Templates</h3>
              <button
                onClick={() => setShowTemplateModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              {permissionTemplates.map((template) => (
                <div key={template.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">{template.name}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{template.description}</p>
                    </div>
                    {template.is_default && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        Default
                      </span>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {Object.values(template.permissions).filter(Boolean).length} permissions enabled
                    </div>

                    <div className="flex gap-2">
                      <button
                        onClick={() => {/* View template details */}}
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        <Eye className="w-4 h-4" />
                      </button>

                      <button
                        onClick={() => {
                          if (selectedStaff) {
                            applyTemplate(selectedStaff, template.id)
                            setShowTemplateModal(false)
                          } else {
                            toast.error('Please select a staff member first')
                          }
                        }}
                        className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                      >
                        Apply
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
              <button
                onClick={() => {/* Create new template */}}
                className="w-full flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <Plus className="w-4 h-4" />
                Create New Template
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Actions Modal */}
      {showBulkModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Bulk Actions</h3>
              <button
                onClick={() => setShowBulkModal(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              <button
                onClick={() => {/* Export permissions */}}
                className="w-full flex items-center gap-3 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg text-left hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <Download className="w-5 h-5 text-blue-600" />
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">Export Permissions</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Download permissions as CSV</div>
                </div>
              </button>

              <button
                onClick={() => {/* Import permissions */}}
                className="w-full flex items-center gap-3 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg text-left hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <Upload className="w-5 h-5 text-green-600" />
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">Import Permissions</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Upload permissions from CSV</div>
                </div>
              </button>

              <button
                onClick={() => {/* Reset all permissions */}}
                className="w-full flex items-center gap-3 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg text-left hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <AlertTriangle className="w-5 h-5 text-red-600" />
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">Reset All Permissions</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Reset to default permissions</div>
                </div>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
