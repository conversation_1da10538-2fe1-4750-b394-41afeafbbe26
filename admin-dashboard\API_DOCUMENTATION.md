# Enhanced POS Manager API Documentation

## Overview

This document provides comprehensive API documentation for the Enhanced POS Manager system, including all endpoints, request/response formats, authentication requirements, and integration guidelines.

## 🔐 Authentication

All API endpoints require authentication using Supabase JWT tokens.

### Headers Required
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### Authentication Flow
1. User authenticates through Supabase Auth
2. JWT token is included in all API requests
3. Server validates token and user permissions
4. API responds with requested data or error

## 📡 Base URL

```
Development: http://localhost:3001/api
Production: https://your-domain.com/api
```

## 🏪 Restaurant Settings API

### Get Restaurant Settings
```http
GET /api/restaurant/settings
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "restaurant_001",
    "name": "La Crêperie Moderne",
    "phone": "+33 1 23 45 67 89",
    "address": "123 Rue de la Paix, 75001 Paris, France",
    "email": "<EMAIL>",
    "website": "https://creperie.fr",
    "cuisineType": "French",
    "operatingHours": {
      "monday": "9:00 AM - 10:00 PM",
      "tuesday": "9:00 AM - 10:00 PM",
      "wednesday": "9:00 AM - 10:00 PM",
      "thursday": "9:00 AM - 10:00 PM",
      "friday": "9:00 AM - 11:00 PM",
      "saturday": "8:00 AM - 11:00 PM",
      "sunday": "8:00 AM - 10:00 PM"
    },
    "serviceOptions": ["Dine-in", "Takeout", "Delivery"],
    "updatedAt": "2024-01-01T12:00:00Z"
  }
}
```

### Update Restaurant Settings
```http
PUT /api/restaurant/settings
```

**Request Body:**
```json
{
  "name": "Updated Restaurant Name",
  "phone": "+33 1 23 45 67 89",
  "address": "New Address",
  "email": "<EMAIL>",
  "website": "https://newwebsite.com",
  "cuisineType": "French",
  "operatingHours": {
    "monday": "9:00 AM - 10:00 PM"
  },
  "serviceOptions": ["Dine-in", "Takeout"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Restaurant settings updated successfully",
  "data": {
    "id": "restaurant_001",
    "updatedAt": "2024-01-01T12:00:00Z"
  }
}
```

## 🖥️ Terminal Management API

### List All Terminals
```http
GET /api/terminals
```

**Query Parameters:**
- `status` (optional): Filter by status (online, offline, maintenance)
- `limit` (optional): Number of results (default: 50)
- `offset` (optional): Pagination offset (default: 0)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "770c7b00",
      "name": "POS Terminal 770c7b00",
      "status": "online",
      "lastHeartbeat": "2024-01-01T12:00:00Z",
      "healthScore": 95,
      "location": "Main Counter",
      "version": "2.1.0",
      "metrics": {
        "cpu": 15.5,
        "memory": 68.2,
        "queue": 0,
        "latency": 45
      }
    }
  ],
  "pagination": {
    "total": 1,
    "limit": 50,
    "offset": 0
  }
}
```

### Get Terminal Details
```http
GET /api/terminals/:id
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "770c7b00",
    "name": "POS Terminal 770c7b00",
    "status": "online",
    "lastHeartbeat": "2024-01-01T12:00:00Z",
    "healthScore": 95,
    "location": "Main Counter",
    "version": "2.1.0",
    "settings": {
      "autoSync": true,
      "syncInterval": 30,
      "theme": "light",
      "language": "en"
    },
    "metrics": {
      "cpu": 15.5,
      "memory": 68.2,
      "queue": 0,
      "latency": 45,
      "uptime": 86400
    }
  }
}
```

### Update Terminal Settings
```http
PUT /api/terminals/:id
```

**Request Body:**
```json
{
  "name": "Updated Terminal Name",
  "location": "New Location",
  "settings": {
    "autoSync": true,
    "syncInterval": 60,
    "theme": "dark",
    "language": "fr"
  }
}
```

### Terminal Heartbeat
```http
POST /api/terminals/:id/heartbeat
```

**Request Body:**
```json
{
  "status": "online",
  "metrics": {
    "cpu": 15.5,
    "memory": 68.2,
    "queue": 0,
    "latency": 45
  }
}
```

## 👥 Staff Management API

### List Staff Members
```http
GET /api/staff
```

**Query Parameters:**
- `role` (optional): Filter by role (admin, manager, cashier, etc.)
- `status` (optional): Filter by status (active, inactive)
- `limit` (optional): Number of results (default: 50)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "staff_001",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "role": "manager",
      "status": "active",
      "permissions": ["pos_access", "inventory_manage", "reports_view"],
      "schedule": {
        "monday": "9:00-17:00",
        "tuesday": "9:00-17:00"
      },
      "createdAt": "2024-01-01T12:00:00Z"
    }
  ]
}
```

### Create Staff Member
```http
POST /api/staff
```

**Request Body:**
```json
{
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "role": "cashier",
  "permissions": ["pos_access"],
  "schedule": {
    "monday": "9:00-17:00",
    "tuesday": "9:00-17:00"
  }
}
```

### Update Staff Member
```http
PUT /api/staff/:id
```

### Delete Staff Member
```http
DELETE /api/staff/:id
```

## 📦 Inventory Sync API

### Get Sync Status
```http
GET /api/inventory/sync-status
```

**Response:**
```json
{
  "success": true,
  "data": {
    "lastSync": "2024-01-01T12:00:00Z",
    "status": "completed",
    "progress": 100,
    "itemsProcessed": 150,
    "itemsTotal": 150,
    "conflicts": 0,
    "errors": 0
  }
}
```

### Trigger Manual Sync
```http
POST /api/inventory/sync
```

**Request Body:**
```json
{
  "terminalId": "770c7b00",
  "syncType": "full",
  "priority": "high"
}
```

### Get Sync Conflicts
```http
GET /api/inventory/sync-conflicts
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "conflict_001",
      "itemId": "item_123",
      "terminalId": "770c7b00",
      "conflictType": "quantity_mismatch",
      "localValue": 50,
      "remoteValue": 45,
      "timestamp": "2024-01-01T12:00:00Z",
      "status": "pending"
    }
  ]
}
```

### Get Sync Metrics
```http
GET /api/inventory/sync-metrics
```

**Query Parameters:**
- `timeRange` (optional): Time range for metrics (1h, 24h, 7d, 30d)
- `terminalId` (optional): Filter by specific terminal

**Response:**
```json
{
  "success": true,
  "data": {
    "successRate": 0.95,
    "avgSyncTime": 1250,
    "totalSyncs": 100,
    "failedSyncs": 5,
    "throughputPerHour": 24,
    "pendingOperations": 0,
    "metrics": [
      {
        "timestamp": "2024-01-01T12:00:00Z",
        "duration": 1200,
        "status": "success",
        "itemsProcessed": 150
      }
    ]
  }
}
```

## 🍽️ Menu Management API

### List Menu Categories
```http
GET /api/menu/categories
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "cat_001",
      "name": "Sweet Crêpes",
      "description": "Delicious sweet crepes with various toppings",
      "type": "standard",
      "displayOrder": 1,
      "active": true,
      "featured": false,
      "itemCount": 12,
      "createdAt": "2024-01-01T12:00:00Z",
      "updatedAt": "2024-01-01T12:00:00Z"
    }
  ]
}
```

### Create Menu Category
```http
POST /api/menu/categories
```

**Request Body:**
```json
{
  "name": "New Category",
  "description": "Category description",
  "type": "standard",
  "displayOrder": 5,
  "active": true,
  "featured": false
}
```

### Update Menu Category
```http
PUT /api/menu/categories/:id
```

### Delete Menu Category
```http
DELETE /api/menu/categories/:id
```

## 📊 Analytics API

### Get System Analytics
```http
GET /api/analytics/system
```

**Query Parameters:**
- `timeRange`: Time range (1h, 24h, 7d, 30d)
- `metrics`: Comma-separated list of metrics

**Response:**
```json
{
  "success": true,
  "data": {
    "timeRange": "24h",
    "metrics": {
      "totalOrders": 150,
      "totalRevenue": 2500.50,
      "avgOrderValue": 16.67,
      "syncOperations": 48,
      "systemUptime": 99.8
    },
    "charts": {
      "ordersByHour": [
        {"hour": "00:00", "orders": 0},
        {"hour": "01:00", "orders": 0}
      ],
      "revenueByHour": [
        {"hour": "00:00", "revenue": 0},
        {"hour": "01:00", "revenue": 0}
      ]
    }
  }
}
```

### Get Terminal Analytics
```http
GET /api/analytics/terminals/:id
```

## 🔧 System Management API

### Get System Health
```http
GET /api/system/health
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "uptime": 86400,
    "version": "2.0.0",
    "database": {
      "status": "connected",
      "responseTime": 15
    },
    "services": {
      "auth": "healthy",
      "sync": "healthy",
      "notifications": "healthy"
    },
    "resources": {
      "cpu": 25.5,
      "memory": 68.2,
      "disk": 45.8
    }
  }
}
```

### Get System Logs
```http
GET /api/system/logs
```

**Query Parameters:**
- `level` (optional): Log level (error, warn, info, debug)
- `limit` (optional): Number of logs (default: 100)
- `since` (optional): ISO timestamp for logs since

## 🚨 Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request parameters",
    "details": {
      "field": "email",
      "reason": "Invalid email format"
    }
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Common Error Codes
- `AUTHENTICATION_REQUIRED` (401)
- `PERMISSION_DENIED` (403)
- `RESOURCE_NOT_FOUND` (404)
- `VALIDATION_ERROR` (400)
- `RATE_LIMIT_EXCEEDED` (429)
- `INTERNAL_SERVER_ERROR` (500)

## 📈 Rate Limiting

- **Standard endpoints**: 100 requests per minute
- **Authentication endpoints**: 10 requests per minute
- **Sync endpoints**: 20 requests per minute
- **Analytics endpoints**: 50 requests per minute

## 🔄 Webhooks

### Sync Completion Webhook
```http
POST /your-webhook-url
```

**Payload:**
```json
{
  "event": "sync.completed",
  "terminalId": "770c7b00",
  "syncId": "sync_001",
  "status": "success",
  "duration": 1250,
  "itemsProcessed": 150,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Terminal Status Change Webhook
```http
POST /your-webhook-url
```

**Payload:**
```json
{
  "event": "terminal.status_changed",
  "terminalId": "770c7b00",
  "oldStatus": "online",
  "newStatus": "offline",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 🧪 Testing

### API Testing with cURL

```bash
# Get restaurant settings
curl -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     http://localhost:3001/api/restaurant/settings

# Update restaurant settings
curl -X PUT \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"name": "New Name"}' \
     http://localhost:3001/api/restaurant/settings

# Get terminal status
curl -H "Authorization: Bearer <token>" \
     http://localhost:3001/api/terminals/770c7b00
```

### Postman Collection
A Postman collection is available at `/docs/postman/enhanced-pos-manager.json`

---

**Last Updated**: January 2024
**API Version**: 2.0.0
**Status**: Production Ready ✅
