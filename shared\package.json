{"name": "@small-business-pos/shared-auth", "version": "1.0.0", "description": "Shared authentication utilities for Small Business POS system", "main": "auth/index.ts", "types": "auth/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "keywords": ["authentication", "supabase", "pos", "rbac", "2fa", "otp", "pin"], "author": "Small Business POS Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.39.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "hi-base32": "^0.5.1", "jsonwebtoken": "^9.0.2", "qrcode": "^1.5.3", "speakeasy": "^2.0.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "winston-elasticsearch": "^0.17.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.0", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "typescript": "^5.3.0"}, "peerDependencies": {"typescript": ">=4.9.0"}, "exports": {".": {"import": "./auth/index.ts", "require": "./auth/index.ts", "types": "./auth/index.ts"}, "./auth": {"import": "./auth/index.ts", "require": "./auth/index.ts", "types": "./auth/index.ts"}, "./auth/*": {"import": "./auth/*.ts", "require": "./auth/*.ts", "types": "./auth/*.ts"}}, "files": ["auth/", "README.md", "package.json"], "repository": {"type": "git", "url": "git+https://github.com/your-org/small-business-pos.git", "directory": "shared"}, "bugs": {"url": "https://github.com/your-org/small-business-pos/issues"}, "homepage": "https://github.com/your-org/small-business-pos#readme", "engines": {"node": ">=18.0.0"}}