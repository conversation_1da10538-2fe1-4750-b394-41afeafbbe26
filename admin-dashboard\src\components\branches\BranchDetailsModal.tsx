'use client'

import React, { useState, useEffect } from 'react'
import { 
  X, 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  Clock, 
  Users, 
  Settings,
  Edit,
  Building2,
  Calendar,
  DollarSign,
  Truck,
  Store,
  UtensilsCrossed
} from 'lucide-react'

interface Branch {
  id: string
  name: string
  code: string
  display_name: string
  description: string
  address_line1: string
  address_line2?: string
  city: string
  state: string
  postal_code: string
  country: string
  phone?: string
  email?: string
  website?: string
  manager_id?: string
  status: 'active' | 'inactive' | 'temporarily_closed' | 'coming_soon' | 'permanently_closed'
  is_active: boolean
  has_delivery: boolean
  has_pickup: boolean
  has_dine_in: boolean
  has_drive_through: boolean
  seating_capacity: number
  parking_spaces: number
  delivery_radius: number
  delivery_fee: number
  minimum_order_amount: number
  timezone: string
  created_at: string
  updated_at: string
}

interface BranchDetailsModalProps {
  branch: Branch
  onClose: () => void
  onEdit: () => void
}

const statusColors = {
  active: 'bg-green-100 text-green-800',
  inactive: 'bg-gray-100 text-gray-800',
  temporarily_closed: 'bg-yellow-100 text-yellow-800',
  coming_soon: 'bg-blue-100 text-blue-800',
  permanently_closed: 'bg-red-100 text-red-800'
}

const statusLabels = {
  active: 'Active',
  inactive: 'Inactive',
  temporarily_closed: 'Temporarily Closed',
  coming_soon: 'Coming Soon',
  permanently_closed: 'Permanently Closed'
}

export default function BranchDetailsModal({ branch, onClose, onEdit }: BranchDetailsModalProps) {
  const [activeTab, setActiveTab] = useState('overview')

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getServiceIcons = () => {
    const services = []
    if (branch.has_delivery) services.push({ icon: Truck, label: 'Delivery', color: 'text-blue-600' })
    if (branch.has_pickup) services.push({ icon: Store, label: 'Pickup', color: 'text-green-600' })
    if (branch.has_dine_in) services.push({ icon: UtensilsCrossed, label: 'Dine-in', color: 'text-purple-600' })
    if (branch.has_drive_through) services.push({ icon: '🚗', label: 'Drive-Through', color: 'text-orange-600' })
    return services
  }

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Basic Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-500">Branch Name</label>
            <p className="text-gray-900 font-medium">{branch.name}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">Display Name</label>
            <p className="text-gray-900 font-medium">{branch.display_name}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">Branch Code</label>
            <p className="text-gray-900 font-mono">{branch.code}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">Status</label>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[branch.status]}`}>
              {statusLabels[branch.status]}
            </span>
          </div>
        </div>
        {branch.description && (
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-500">Description</label>
            <p className="text-gray-900">{branch.description}</p>
          </div>
        )}
      </div>

      {/* Location Information */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Location
        </h3>
        <div className="space-y-2">
          <p className="text-gray-900">{branch.address_line1}</p>
          {branch.address_line2 && <p className="text-gray-900">{branch.address_line2}</p>}
          <p className="text-gray-900">{branch.city}, {branch.state} {branch.postal_code}</p>
          <p className="text-gray-600">{branch.country}</p>
          <p className="text-sm text-gray-500">Timezone: {branch.timezone}</p>
        </div>
      </div>

      {/* Contact Information */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Phone className="h-5 w-5" />
          Contact Information
        </h3>
        <div className="space-y-3">
          {branch.phone && (
            <div className="flex items-center gap-3">
              <Phone className="h-4 w-4 text-gray-500" />
              <span className="text-gray-900">{branch.phone}</span>
            </div>
          )}
          {branch.email && (
            <div className="flex items-center gap-3">
              <Mail className="h-4 w-4 text-gray-500" />
              <span className="text-gray-900">{branch.email}</span>
            </div>
          )}
          {branch.website && (
            <div className="flex items-center gap-3">
              <Globe className="h-4 w-4 text-gray-500" />
              <a href={branch.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                {branch.website}
              </a>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderServicesTab = () => (
    <div className="space-y-6">
      {/* Available Services */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Services</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {getServiceIcons().map((service, index) => (
            <div key={index} className="flex items-center gap-3 p-3 bg-white rounded-lg border">
              {typeof service.icon === 'string' ? (
                <span className="text-2xl">{service.icon}</span>
              ) : (
                <service.icon className={`h-6 w-6 ${service.color}`} />
              )}
              <span className="font-medium text-gray-900">{service.label}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Operational Details */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Operational Details
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              <Users className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-gray-900">Seating</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">{branch.seating_capacity}</p>
            <p className="text-sm text-gray-500">seats available</p>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-blue-600">🅿️</span>
              <span className="font-medium text-gray-900">Parking</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">{branch.parking_spaces}</p>
            <p className="text-sm text-gray-500">parking spaces</p>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              <Truck className="h-5 w-5 text-green-600" />
              <span className="font-medium text-gray-900">Delivery Radius</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">{branch.delivery_radius}</p>
            <p className="text-sm text-gray-500">miles</p>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="h-5 w-5 text-green-600" />
              <span className="font-medium text-gray-900">Delivery Fee</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">${branch.delivery_fee}</p>
            <p className="text-sm text-gray-500">per order</p>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="h-5 w-5 text-purple-600" />
              <span className="font-medium text-gray-900">Minimum Order</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">${branch.minimum_order_amount}</p>
            <p className="text-sm text-gray-500">for delivery</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSystemTab = () => (
    <div className="space-y-6">
      {/* System Information */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          System Information
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-500">Branch ID</label>
            <p className="text-gray-900 font-mono text-sm">{branch.id}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">Created</label>
            <p className="text-gray-900">{formatDate(branch.created_at)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">Last Updated</label>
            <p className="text-gray-900">{formatDate(branch.updated_at)}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">Active Status</label>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              branch.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {branch.is_active ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="p-4 bg-white border rounded-lg hover:bg-gray-50 transition-colors text-left">
            <div className="flex items-center gap-3">
              <Clock className="h-6 w-6 text-blue-600" />
              <div>
                <p className="font-medium text-gray-900">Manage Hours</p>
                <p className="text-sm text-gray-500">Set operating hours and special schedules</p>
              </div>
            </div>
          </button>

          <button className="p-4 bg-white border rounded-lg hover:bg-gray-50 transition-colors text-left">
            <div className="flex items-center gap-3">
              <Users className="h-6 w-6 text-green-600" />
              <div>
                <p className="font-medium text-gray-900">Staff Assignments</p>
                <p className="text-sm text-gray-500">Manage staff assigned to this branch</p>
              </div>
            </div>
          </button>

          <button className="p-4 bg-white border rounded-lg hover:bg-gray-50 transition-colors text-left">
            <div className="flex items-center gap-3">
              <Settings className="h-6 w-6 text-purple-600" />
              <div>
                <p className="font-medium text-gray-900">POS Configuration</p>
                <p className="text-sm text-gray-500">Configure POS system settings</p>
              </div>
            </div>
          </button>

          <button className="p-4 bg-white border rounded-lg hover:bg-gray-50 transition-colors text-left">
            <div className="flex items-center gap-3">
              <MapPin className="h-6 w-6 text-orange-600" />
              <div>
                <p className="font-medium text-gray-900">Delivery Zones</p>
                <p className="text-sm text-gray-500">Manage delivery areas and zones</p>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{branch.display_name || branch.name}</h2>
            <p className="text-gray-600 mt-1">Branch Code: {branch.code}</p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={onEdit}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <Edit className="h-4 w-4" />
              Edit Branch
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="h-6 w-6 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'overview'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('services')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'services'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Services & Operations
            </button>
            <button
              onClick={() => setActiveTab('system')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'system'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              System & Actions
            </button>
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[70vh]">
          {activeTab === 'overview' && renderOverviewTab()}
          {activeTab === 'services' && renderServicesTab()}
          {activeTab === 'system' && renderSystemTab()}
        </div>
      </div>
    </div>
  )
}