import React, { useEffect, useState } from 'react';
import { useTheme } from '../../contexts/theme-context';
import { MenuItemCard } from './MenuItemCard';
import { menuService, MenuItem } from '../../services/MenuService';

interface MenuItemGridProps {
  selectedCategory: string;
  selectedSubcategory?: string;
  orderType?: 'pickup' | 'delivery';
  onItemSelect: (item: MenuItem) => void;
}

export const MenuItemGrid: React.FC<MenuItemGridProps> = ({
  selectedCategory,
  selectedSubcategory = '',
  orderType = 'delivery',
  onItemSelect
}) => {
  const { resolvedTheme } = useTheme();
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadMenuItems = async () => {
      setLoading(true);
      setError(null);
      
      try {
        let items: MenuItem[] = [];
        
        if (selectedCategory === 'all') {
          items = await menuService.getMenuItems();
        } else if (selectedCategory === 'featured') {
          const allItems = await menuService.getMenuItems();
          items = allItems.filter(item => item.is_featured || false);
        } else {
          items = await menuService.getMenuItemsByCategory(selectedCategory);
        }
        
        // Filter by subcategory if selected
        if (selectedSubcategory) {
          const isCustomizable = selectedSubcategory.includes('customizable');
          if (isCustomizable) {
            items = items.filter(item => item.is_customizable);
          } else {
            items = items.filter(item => !item.is_customizable);
          }
        }
        
        setMenuItems(items);
      } catch (error) {
        console.error('Error loading menu items:', error);
        setError('Failed to load menu items. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    loadMenuItems();

    // Temporarily disable real-time subscription to fix the error
    // TODO: Fix subscription management and re-enable
    /*
    // Subscribe to menu updates with proper cleanup
    let unsubscribe: (() => void) | null = null;
    
    try {
      unsubscribe = menuService.subscribeToMenuUpdates(() => {
        loadMenuItems();
      });
    } catch (error) {
      console.error('Error subscribing to menu updates:', error);
    }

    // Cleanup function
    return () => {
      if (unsubscribe) {
        try {
          unsubscribe();
        } catch (error) {
          console.error('Error unsubscribing from menu updates:', error);
        }
      }
    };
    */
  }, [selectedCategory, selectedSubcategory]);

  if (loading) {
    return (
      <div className="flex-1 p-4 overflow-y-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3, 4, 5, 6].map(i => (
            <div
              key={i}
              className={`h-32 rounded-xl animate-pulse ${
                resolvedTheme === 'dark' ? 'bg-gray-700/30' : 'bg-gray-200/50'
              }`}
            />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 p-4 overflow-y-auto flex items-center justify-center">
        <div className="text-center">
          <div className={`text-6xl mb-4 ${
            resolvedTheme === 'dark' ? 'text-gray-600' : 'text-gray-400'
          }`}>
            😞
          </div>
          <p className={`text-lg font-medium mb-2 ${
            resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
          }`}>
            {error}
          </p>
          <p className={`text-sm ${
            resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
          }`}>
            Please check your connection and try again
          </p>
        </div>
      </div>
    );
  }

  if (menuItems.length === 0) {
    return (
      <div className="flex-1 p-4 overflow-y-auto flex items-center justify-center">
        <div className="text-center">
          <div className={`text-6xl mb-4 ${
            resolvedTheme === 'dark' ? 'text-gray-600' : 'text-gray-400'
          }`}>
            🍽️
          </div>
          <p className={`text-lg font-medium mb-2 ${
            resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
          }`}>
            No items found
          </p>
          <p className={`text-sm ${
            resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
          }`}>
            Try selecting a different category
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-4 overflow-y-auto">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {menuItems.map((item) => {
          // Calculate price based on order type
          let displayPrice = item.price;
          if (orderType === 'pickup' && item.pickup_price) {
            displayPrice = item.pickup_price;
          } else if (orderType === 'delivery' && item.delivery_price) {
            displayPrice = item.delivery_price;
          }

          return (
            <MenuItemCard
              key={item.id}
              item={{
                id: item.id,
                name: item.name || item.name_en || 'Unknown Item',
                description: item.description || '',
                price: displayPrice,
                category: item.category || item.category_id,
                preparationTime: item.preparation_time || 0,
                image: item.image_url || ''
              }}
              orderType={orderType}
              onSelect={() => onItemSelect(item)}
            />
          );
        })}
      </div>
    </div>
  );
};