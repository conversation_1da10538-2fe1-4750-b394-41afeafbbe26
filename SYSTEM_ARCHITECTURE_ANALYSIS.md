# Creperie Management System - Complete Architecture Analysis

## Overview

This document provides a comprehensive analysis of the Creperie Management System, a sophisticated enterprise-level restaurant management ecosystem built as a monorepo. The system includes multiple interconnected applications for complete restaurant operations management.

## System Architecture Diagrams

### 1. High-Level System Architecture

```mermaid
graph TB
    %% Root Workspace
    ROOT[🏗️ Creperie Management System<br/>Monorepo Workspace<br/>TypeScript • npm workspaces]
    
    %% Core Applications
    ADMIN[🖥️ Admin Dashboard<br/>Next.js 14 • React<br/>Port: 3001<br/>shadcn/ui • Tailwind]
    WEB[🌐 Customer Web<br/>Next.js 14 • PWA<br/>Port: 3000<br/>Online Ordering]
    POS[💻 POS System<br/>Electron • React<br/>Windows Desktop<br/>Offline Capable]
    MOBILE[📱 Customer Mobile<br/>React Native<br/>iOS • Android<br/>Expo]
    
    %% Shared Infrastructure
    SHARED[📦 Shared Utilities<br/>TypeScript<br/>Auth • Security • Types]
    DATABASE[🗄️ Database<br/>Schemas & Config]
    SUPABASE[☁️ Supabase Backend<br/>PostgreSQL • Real-time<br/>Auth • Storage]
    
    %% Supabase Components
    MIGRATIONS[📋 Migrations<br/>Database Schema<br/>RLS Policies]
    FUNCTIONS[⚡ Edge Functions<br/>Notifications<br/>Triggers • Webhooks]
    REALTIME[🔄 Real-time Engine<br/>WebSocket<br/>Live Updates]
    
    %% External Services
    GOOGLE[🗺️ Google Maps API<br/>Delivery Zones<br/>Location Services]
    NOTIFICATIONS[📢 Notification Services<br/>Push • Email • SMS<br/>Twilio • Resend]
    
    %% Testing & Documentation
    TESTS[🧪 Tests<br/>Cross-app Testing<br/>Integration • E2E]
    DOCS[📚 Documentation<br/>Guides • Setup<br/>Training Materials]
    SCRIPTS[⚙️ Scripts<br/>Build • Deploy<br/>PowerShell]
    
    %% Workspace Structure
    ROOT --> ADMIN
    ROOT --> WEB
    ROOT --> POS
    ROOT --> MOBILE
    ROOT --> SHARED
    ROOT --> DATABASE
    ROOT --> SUPABASE
    ROOT --> TESTS
    ROOT --> DOCS
    ROOT --> SCRIPTS
    
    %% Shared Dependencies
    ADMIN -.->|imports| SHARED
    WEB -.->|imports| SHARED
    POS -.->|imports| SHARED
    MOBILE -.->|imports| SHARED
    
    %% Supabase Structure
    SUPABASE --> MIGRATIONS
    SUPABASE --> FUNCTIONS
    SUPABASE --> REALTIME
    
    %% Database Connections
    ADMIN -->|Supabase Client<br/>API Routes| SUPABASE
    WEB -->|Supabase Client<br/>Server Actions| SUPABASE
    POS -->|Supabase Client<br/>Sync Service| SUPABASE
    MOBILE -->|Supabase Client<br/>React Native| SUPABASE
    
    %% Real-time Subscriptions
    REALTIME -.->|Order Updates| ADMIN
    REALTIME -.->|Order Tracking| WEB
    REALTIME -.->|Live Sync| POS
    REALTIME -.->|Push Notifications| MOBILE
    
    %% External Integrations
    ADMIN -->|Maps Integration| GOOGLE
    WEB -->|Location Services| GOOGLE
    FUNCTIONS -->|Send Notifications| NOTIFICATIONS
    
    %% Inter-Application Communication
    ADMIN -.->|Staff Management| POS
    WEB -.->|Order Data| ADMIN
    MOBILE -.->|Customer Orders| WEB
    POS -.->|Order Processing| ADMIN
    
    %% Authentication Flow
    SHARED -->|Auth Utils| ADMIN
    SHARED -->|Auth Utils| POS
    SHARED -->|Security| SUPABASE
    
    %% Data Flow Patterns
    FUNCTIONS -.->|Triggers| REALTIME
    MIGRATIONS -.->|Schema| SUPABASE
    
    %% Styling
    classDef application fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef infrastructure fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef external fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef shared fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef database fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class ADMIN,WEB,POS,MOBILE application
    class SUPABASE,REALTIME,FUNCTIONS infrastructure
    class GOOGLE,NOTIFICATIONS external
    class SHARED,TESTS,DOCS,SCRIPTS shared
    class DATABASE,MIGRATIONS database
```

### 2. Technical Data Flow & Communication Patterns

```mermaid
graph TB
    %% User Interfaces
    ADMIN_UI[👨‍💼 Admin Dashboard UI<br/>Next.js 14 • React<br/>Restaurant Management]
    WEB_UI[🛒 Customer Web UI<br/>Next.js 14 • PWA<br/>Online Ordering]
    POS_UI[🏪 POS Interface<br/>Electron • React<br/>In-Store Operations]
    MOBILE_UI[📱 Mobile App UI<br/>React Native<br/>Customer Orders]

    %% API Layers
    ADMIN_API[🔌 Admin API Routes<br/>/api/customers<br/>/api/menu<br/>/api/orders<br/>/api/staff]
    WEB_API[🔌 Customer API<br/>Server Actions<br/>Order Processing]
    POS_IPC[⚡ Electron IPC<br/>Main ↔ Renderer<br/>Auth • Sync • Orders]

    %% Shared Services Layer
    AUTH_SERVICE[🔐 Authentication Service<br/>Email/Password • OTP • PIN<br/>Session Management<br/>2FA Support]
    SECURITY_SERVICE[🛡️ Security Service<br/>Audit Logging<br/>GDPR Compliance<br/>PCI DSS]
    SYNC_SERVICE[🔄 Sync Service<br/>Real-time Updates<br/>Offline Support<br/>Conflict Resolution]

    %% Supabase Backend
    SUPABASE_AUTH[🔑 Supabase Auth<br/>User Management<br/>JWT Tokens<br/>RLS Policies]
    SUPABASE_DB[🗄️ PostgreSQL Database<br/>Orders • Customers<br/>Menu • Staff<br/>Branches]
    SUPABASE_RT[📡 Real-time Engine<br/>WebSocket Subscriptions<br/>Live Data Sync]
    SUPABASE_STORAGE[💾 Storage<br/>Images • Files<br/>Menu Photos]

    %% Edge Functions
    ORDER_TRIGGER[⚡ Order Status Trigger<br/>Status Change Events<br/>Notification Dispatch]
    EMAIL_FUNC[📧 Email Notifications<br/>Order Confirmations<br/>Status Updates]
    SMS_FUNC[📱 SMS Notifications<br/>Order Alerts<br/>Delivery Updates]
    PUSH_FUNC[🔔 Push Notifications<br/>Mobile Alerts<br/>Real-time Updates]

    %% External Services
    GOOGLE_MAPS[🗺️ Google Maps<br/>Delivery Zones<br/>Location Services<br/>Route Optimization]
    TWILIO[📞 Twilio<br/>SMS Service<br/>Voice Calls]
    RESEND[📬 Resend<br/>Email Service<br/>Transactional Emails]

    %% Local Storage (POS)
    SQLITE[💽 SQLite (POS)<br/>Local Database<br/>Offline Operations<br/>Sync Queue]

    %% UI to API Connections
    ADMIN_UI --> ADMIN_API
    WEB_UI --> WEB_API
    POS_UI --> POS_IPC
    MOBILE_UI -.->|Direct Connection| SUPABASE_AUTH

    %% API to Services
    ADMIN_API --> AUTH_SERVICE
    ADMIN_API --> SECURITY_SERVICE
    WEB_API --> AUTH_SERVICE
    POS_IPC --> SYNC_SERVICE
    POS_IPC --> AUTH_SERVICE

    %% Services to Supabase
    AUTH_SERVICE --> SUPABASE_AUTH
    SECURITY_SERVICE --> SUPABASE_DB
    SYNC_SERVICE --> SUPABASE_RT
    ADMIN_API --> SUPABASE_DB
    WEB_API --> SUPABASE_DB
    MOBILE_UI --> SUPABASE_DB

    %% Real-time Subscriptions
    SUPABASE_RT -.->|Order Updates| ADMIN_UI
    SUPABASE_RT -.->|Order Tracking| WEB_UI
    SUPABASE_RT -.->|Live Sync| POS_UI
    SUPABASE_RT -.->|Push Events| MOBILE_UI

    %% Database Triggers to Edge Functions
    SUPABASE_DB -->|Order Changes| ORDER_TRIGGER
    ORDER_TRIGGER --> EMAIL_FUNC
    ORDER_TRIGGER --> SMS_FUNC
    ORDER_TRIGGER --> PUSH_FUNC

    %% Edge Functions to External Services
    EMAIL_FUNC --> RESEND
    SMS_FUNC --> TWILIO
    PUSH_FUNC -.->|FCM/APNS| MOBILE_UI

    %% External Integrations
    ADMIN_API --> GOOGLE_MAPS
    WEB_API --> GOOGLE_MAPS

    %% POS Offline Capabilities
    POS_IPC --> SQLITE
    SQLITE -.->|Sync Queue| SYNC_SERVICE

    %% Platform-Specific Configurations
    subgraph "Platform Configs"
        WEB_CONFIG[Web: 10 events/sec<br/>Session persistence<br/>URL detection]
        MOBILE_CONFIG[Mobile: 5 events/sec<br/>Battery optimization<br/>AsyncStorage]
        DESKTOP_CONFIG[Desktop: 15 events/sec<br/>High-frequency updates<br/>Local storage]
    end

    SUPABASE_RT -.-> WEB_CONFIG
    SUPABASE_RT -.-> MOBILE_CONFIG
    SUPABASE_RT -.-> DESKTOP_CONFIG

    %% Data Flow Labels
    ADMIN_API -.->|CRUD Operations| SUPABASE_DB
    WEB_API -.->|Order Processing| SUPABASE_DB
    POS_IPC -.->|Transaction Data| SUPABASE_DB
    MOBILE_UI -.->|Customer Data| SUPABASE_DB

    %% Styling
    classDef ui fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef api fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef service fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef database fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef external fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef function fill:#f1f8e9,stroke:#689f38,stroke-width:2px

    class ADMIN_UI,WEB_UI,POS_UI,MOBILE_UI ui
    class ADMIN_API,WEB_API,POS_IPC api
    class AUTH_SERVICE,SECURITY_SERVICE,SYNC_SERVICE service
    class SUPABASE_AUTH,SUPABASE_DB,SUPABASE_RT,SUPABASE_STORAGE,SQLITE database
    class GOOGLE_MAPS,TWILIO,RESEND external
    class ORDER_TRIGGER,EMAIL_FUNC,SMS_FUNC,PUSH_FUNC function
```

## Technology Stack Summary

### Core Applications
| Application | Technology | Port | Purpose |
|-------------|------------|------|---------|
| **Admin Dashboard** | Next.js 14, React, TypeScript | 3001 | Restaurant management interface |
| **Customer Web** | Next.js 14, PWA, React | 3000 | Public-facing ordering website |
| **POS System** | Electron, React, TypeScript | - | Windows desktop point-of-sale |
| **Customer Mobile** | React Native, Expo | - | iOS/Android mobile app |

### Shared Infrastructure
- **Language**: TypeScript across all platforms
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: React Query/TanStack Query
- **Form Handling**: React Hook Form with Zod validation
- **Internationalization**: i18next for multi-language support
- **Backend**: Supabase (PostgreSQL with real-time subscriptions)
- **Authentication**: Supabase Auth with custom utilities
- **Testing**: Jest, Cypress for E2E testing

## Key Features by Application

### 🖥️ Admin Dashboard
- Branch management with admin setup
- Menu and inventory management
- Order tracking and analytics
- Staff management with role-based access
- Google Maps integration for delivery zones
- Real-time notifications
- Multi-language support (English/Greek)

### 🌐 Customer Web
- Progressive Web App (PWA) capabilities
- Menu browsing with categories
- Shopping cart and checkout
- Customer authentication and profiles
- Real-time order tracking
- Address and payment management
- Multi-language support

### 💻 POS System
- Order taking and processing
- Customer search and management
- Real-time synchronization with cloud database
- Offline capability with SQLite
- Staff authentication
- Payment processing integration
- Receipt printing
- Kitchen display integration

### 📱 Customer Mobile
- Mobile ordering
- Loyalty program integration
- Push notifications
- Location-based features
- Order tracking
- User authentication
- Favorites management

## Architecture Highlights

### 🔄 Real-time Synchronization
- **Platform-specific event rates**:
  - Web: 10 events/second
  - Mobile: 5 events/second (battery optimization)
  - Desktop: 15 events/second (high-frequency updates)
- WebSocket subscriptions for live order updates
- Offline-capable POS with sync queue

### 🔐 Authentication Architecture
- Centralized auth service supporting:
  - Email/Password authentication
  - OTP (One-Time Password)
  - PIN authentication
  - Two-Factor Authentication (2FA)
- Platform-specific configurations
- Session management with comprehensive audit logging

### 🛡️ Security & Compliance
- **GDPR compliance** with data protection utilities
- **PCI DSS compliance** for payment processing
- Comprehensive audit logging and security middleware
- Row-level security (RLS) policies in Supabase
- Greek data protection compliance

### 📡 Communication Patterns
1. **Admin Dashboard** → API Routes → Supabase
2. **Customer Web** → Server Actions → Supabase  
3. **POS System** → Electron IPC → Sync Service → Supabase
4. **Mobile App** → Direct Supabase connection

### 🔔 Notification System
- Database triggers → Edge functions → External services
- Multi-channel notifications (push, email, SMS)
- Integration with Twilio (SMS) and Resend (Email)
- Real-time staff notifications in admin dashboard

## Development Workflow

### Workspace Commands (from root)
```bash
npm run dev          # Start all applications in development mode
npm run build        # Build all applications
npm run lint         # Run linting across all workspaces
npm run test         # Run tests across all workspaces
```

### Individual Application Commands
```bash
npm run admin:dev    # Admin Dashboard (port 3001)
npm run web:dev      # Customer Website (port 3000)
npm run pos:dev      # POS System
npm run mobile:dev   # Mobile App
npm run db:migrate   # Database migrations
npm run db:seed      # Database seeding
```

## External Integrations

- **Google Maps API**: Delivery zones, location services, route optimization
- **Twilio**: SMS notifications and voice calls
- **Resend**: Transactional email service
- **Firebase**: Push notifications for mobile app

## Database Structure

The system uses Supabase (PostgreSQL) with:
- **Real-time subscriptions** for live data sync
- **Row-level security (RLS)** policies
- **Edge functions** for serverless operations
- **Comprehensive migrations** for schema management
- **Audit logging** for security and compliance

## Conclusion

This Creperie Management System represents a modern, scalable, and comprehensive solution for restaurant operations. The monorepo architecture ensures code reusability and consistency across platforms, while the real-time capabilities and offline support provide a robust user experience for both staff and customers.

The system's emphasis on security, compliance, and multi-platform support makes it suitable for enterprise-level restaurant operations with complex requirements for data protection, real-time operations, and multi-channel customer engagement.
