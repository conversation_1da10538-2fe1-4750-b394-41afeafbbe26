import React, { useState } from 'react';
import { useTheme } from '../contexts/theme-context';

interface SalesData {
  date: string;
  orders: number;
  revenue: number;
  avgOrderValue: number;
}

interface TopItem {
  name: string;
  quantity: number;
  revenue: number;
}

const ReportsPage: React.FC = () => {
  const { resolvedTheme } = useTheme();
  const [selectedPeriod, setSelectedPeriod] = useState('today');

  // Mock data
  const salesData: SalesData[] = [
    { date: '2024-01-15', orders: 45, revenue: 1250.50, avgOrderValue: 27.79 },
    { date: '2024-01-14', orders: 38, revenue: 980.25, avgOrderValue: 25.80 },
    { date: '2024-01-13', orders: 52, revenue: 1450.75, avgOrderValue: 27.90 },
    { date: '2024-01-12', orders: 41, revenue: 1100.00, avgOrderValue: 26.83 },
    { date: '2024-01-11', orders: 47, revenue: 1320.25, avgOrderValue: 28.09 },
  ];

  const topItems: TopItem[] = [
    { name: 'Margherita Pizza', quantity: 25, revenue: 324.75 },
    { name: 'Pepperoni Pizza', quantity: 22, revenue: 329.78 },
    { name: 'Caesar Salad', quantity: 18, revenue: 161.82 },
    { name: 'Chicken Wings', quantity: 15, revenue: 224.85 },
    { name: 'Coca Cola', quantity: 35, revenue: 104.65 },
  ];

  const todayStats = {
    totalOrders: 45,
    totalRevenue: 1250.50,
    avgOrderValue: 27.79,
    completionRate: 95.6
  };

  return (
    <div className={`min-h-screen p-6 ${resolvedTheme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Sales Reports</h1>
          <p className={`${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Track your restaurant's performance and sales analytics
          </p>
        </div>

        {/* Period Selector */}
        <div className="mb-6">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className={`px-4 py-2 rounded-lg border ${
              resolvedTheme === 'dark' 
                ? 'bg-gray-800 border-gray-700 text-white' 
                : 'bg-white border-gray-300 text-gray-900'
            } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
          >
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
          </select>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className={`p-6 rounded-lg ${resolvedTheme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
            <h3 className={`text-sm font-medium ${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
              Total Orders
            </h3>
            <p className="text-3xl font-bold text-blue-600">{todayStats.totalOrders}</p>
            <p className={`text-sm ${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
              +12% from yesterday
            </p>
          </div>

          <div className={`p-6 rounded-lg ${resolvedTheme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
            <h3 className={`text-sm font-medium ${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
              Total Revenue
            </h3>
            <p className="text-3xl font-bold text-green-600">${todayStats.totalRevenue.toFixed(2)}</p>
            <p className={`text-sm ${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
              +8% from yesterday
            </p>
          </div>

          <div className={`p-6 rounded-lg ${resolvedTheme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
            <h3 className={`text-sm font-medium ${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
              Avg Order Value
            </h3>
            <p className="text-3xl font-bold text-purple-600">${todayStats.avgOrderValue.toFixed(2)}</p>
            <p className={`text-sm ${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
              -2% from yesterday
            </p>
          </div>

          <div className={`p-6 rounded-lg ${resolvedTheme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
            <h3 className={`text-sm font-medium ${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
              Completion Rate
            </h3>
            <p className="text-3xl font-bold text-orange-600">{todayStats.completionRate}%</p>
            <p className={`text-sm ${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
              +1.2% from yesterday
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Sales Trend */}
          <div className={`p-6 rounded-lg ${resolvedTheme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
            <h3 className="text-xl font-semibold mb-4">Sales Trend (Last 5 Days)</h3>
            <div className="space-y-4">
              {salesData.map((data, index) => (
                <div key={data.date} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{new Date(data.date).toLocaleDateString()}</p>
                    <p className={`text-sm ${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                      {data.orders} orders
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">${data.revenue.toFixed(2)}</p>
                    <p className={`text-sm ${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                      Avg: ${data.avgOrderValue.toFixed(2)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Top Selling Items */}
          <div className={`p-6 rounded-lg ${resolvedTheme === 'dark' ? 'bg-gray-800' : 'bg-white'} shadow-sm`}>
            <h3 className="text-xl font-semibold mb-4">Top Selling Items</h3>
            <div className="space-y-4">
              {topItems.map((item, index) => (
                <div key={item.name} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium mr-3 ${
                      index === 0 ? 'bg-yellow-100 text-yellow-800' :
                      index === 1 ? 'bg-gray-100 text-gray-800' :
                      index === 2 ? 'bg-orange-100 text-orange-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {index + 1}
                    </span>
                    <div>
                      <p className="font-medium">{item.name}</p>
                      <p className={`text-sm ${resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                        {item.quantity} sold
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">${item.revenue.toFixed(2)}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Export Options */}
        <div className="mt-8 flex justify-end space-x-4">
          <button className={`px-4 py-2 rounded-lg border ${
            resolvedTheme === 'dark' 
              ? 'border-gray-700 text-gray-300 hover:bg-gray-800' 
              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          } transition-colors`}>
            Export CSV
          </button>
          <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
            Generate PDF Report
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReportsPage;