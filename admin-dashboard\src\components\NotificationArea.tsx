'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function NotificationArea() {
  const router = useRouter();
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const notifications = [
    {
      id: 1,
      type: 'order',
      color: 'bg-blue-400',
      title: 'New order received',
      description: 'Order #ORD-004 - €12.50',
      time: '2 minutes ago'
    },
    {
      id: 2,
      type: 'payment',
      color: 'bg-green-400',
      title: 'Payment confirmed',
      description: 'Order #ORD-003 processed',
      time: '5 minutes ago'
    },
    {
      id: 3,
      type: 'inventory',
      color: 'bg-yellow-400',
      title: 'Low stock alert',
      description: 'Nutella running low',
      time: '1 hour ago'
    },
    {
      id: 4,
      type: 'review',
      color: 'bg-purple-400',
      title: 'Customer review',
      description: '5-star rating received',
      time: '3 hours ago'
    },
    {
      id: 5,
      type: 'system',
      color: 'bg-red-400',
      title: 'System maintenance',
      description: 'Scheduled for tonight',
      time: '4 hours ago'
    }
  ];

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Implement search functionality
      console.log('Searching for:', searchQuery);
    }
  };

  const handleThemeToggle = () => {
    setIsDarkMode(!isDarkMode);
    // Implement theme toggle logic
  };

  const handleLogout = () => {
    // Implement logout logic
    router.push('/login');
  };

  const markAllAsRead = () => {
    setIsNotificationsOpen(false);
  };

  return (
    <div className="absolute top-6 right-6 z-20">
      {/* Main controls */}
      <div className="backdrop-blur-md border rounded-full shadow-lg transition-all duration-1000 ease-out bg-white/10 border-white/20">
        <div className="flex items-center p-2 transition-all duration-1000 ease-out space-x-4">
          {/* Search input */}
          <div className="relative overflow-hidden">
            <form onSubmit={handleSearch}>
              <input
                placeholder="Search dashboard..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`bg-transparent border-none outline-none text-sm px-3 py-2 transition-all duration-1000 ease-out text-white placeholder-white/60 ${
                  isSearchExpanded 
                    ? 'w-64 opacity-100 px-3' 
                    : 'w-0 opacity-0 px-0'
                }`}
                style={{ 
                  transform: isSearchExpanded ? 'translateX(0)' : 'translateX(-100%)' 
                }}
              />
            </form>
          </div>

          {/* Search button */}
          <button
            onClick={() => setIsSearchExpanded(!isSearchExpanded)}
            className="w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-500 ease-out flex items-center justify-center bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </button>

          {/* Notifications button */}
          <button
            onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
            className="relative w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 flex items-center justify-center bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
            </svg>
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {notifications.length}
            </span>
          </button>

          {/* User profile button */}
          <button
            onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
            className="w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-500 ease-out flex items-center justify-center overflow-hidden bg-white/10 border-white/20 hover:bg-white/20"
          >
            <img
              alt="User Profile"
              className="w-full h-full object-cover"
              src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
            />
          </button>

          {/* Theme toggle button */}
          <button
            onClick={handleThemeToggle}
            className="group w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-500 ease-out flex items-center justify-center relative overflow-hidden bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white"
          >
            {/* Moon icon (dark mode) */}
            <div className={`absolute inset-0 flex items-center justify-center transition-all duration-500 ${isDarkMode ? 'opacity-0 rotate-180' : 'opacity-100 rotate-0'}`}>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
              </svg>
            </div>
            {/* Sun icon (light mode) */}
            <div className={`absolute inset-0 flex items-center justify-center transition-all duration-500 ${isDarkMode ? 'opacity-100 rotate-0' : 'opacity-0 rotate-180'}`}>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            </div>
          </button>
        </div>
      </div>

      {/* Notifications dropdown */}
      <div className={`backdrop-blur-md border rounded-2xl shadow-lg mt-2 overflow-hidden transition-all duration-1000 ease-out bg-white/10 border-white/20 ${
        isNotificationsOpen 
          ? 'max-h-96 opacity-100 transform translate-y-0' 
          : 'max-h-0 opacity-0 transform -translate-y-2'
      }`}>
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-white">Notifications</h3>
            <button
              onClick={markAllAsRead}
              className="text-xs transition-colors duration-200 text-white/60 hover:text-white/80"
            >
              Mark all read
            </button>
          </div>
          <div className="max-h-56 overflow-y-auto space-y-2 pr-2">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className="flex items-start space-x-3 p-2 rounded-lg transition-all duration-200 hover:bg-white/5"
              >
                <div className={`w-2 h-2 ${notification.color} rounded-full mt-2 flex-shrink-0`}></div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-white/90">{notification.title}</p>
                  <p className="text-xs text-white/60">{notification.description}</p>
                  <p className="text-xs text-white/40">{notification.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* User menu dropdown */}
      <div className={`backdrop-blur-md border rounded-2xl shadow-lg mt-2 overflow-hidden transition-all duration-1000 ease-out bg-white/10 border-white/20 ${
        isUserMenuOpen 
          ? 'max-h-96 opacity-100 transform translate-y-0' 
          : 'max-h-0 opacity-0 transform -translate-y-2'
      }`}>
        <div className="p-4 space-y-1">
          <button className="w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-300 flex items-center space-x-3 text-white/80 hover:text-white hover:bg-white/10">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span>Profile Settings</span>
          </button>
          <button className="w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-300 flex items-center space-x-3 text-white/80 hover:text-white hover:bg-white/10">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <span>Preferences</span>
          </button>
          <button
            onClick={handleLogout}
            className="w-full text-left px-3 py-2 rounded-lg text-sm text-red-300 hover:text-red-200 hover:bg-red-500/20 transition-all duration-300 flex items-center space-x-3"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            <span>Logout</span>
          </button>
        </div>
      </div>
    </div>
  );
}