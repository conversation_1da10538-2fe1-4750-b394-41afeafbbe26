import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { GlassCard, GlassButton } from '@/components/ui/glass-components';

export const metadata: Metadata = {
  title: 'My Favorites | Delicious Crepes & Waffles',
  description: 'View and manage your favorite menu items.',
};

export default function FavoritesPage() {
  // Mock user data - in a real app, this would come from the AuthProvider and API
  const user = {
    id: 'user123',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/images/avatar.jpg',
  };

  // Mock favorites data
  const favorites = [
    {
      id: 'item1',
      name: 'Nutella & Strawberry Crepe',
      description:
        'Sweet crepe filled with Nutella and fresh strawberries, topped with powdered sugar.',
      price: 8.99,
      image: '/images/menu/nutella-strawberry-crepe.jpg',
      category: 'Sweet Crepes',
    },
    {
      id: 'item2',
      name: 'Ham & Cheese Savory Crepe',
      description:
        'Savory crepe filled with premium ham, melted cheese, and a touch of dijon mustard.',
      price: 9.99,
      image: '/images/menu/ham-cheese-crepe.jpg',
      category: 'Savory Crepes',
    },
    {
      id: 'item3',
      name: 'Belgian Chocolate Waffle',
      description:
        'Authentic Belgian waffle topped with premium chocolate sauce and chocolate shavings.',
      price: 7.99,
      image: '/images/menu/chocolate-waffle.jpg',
      category: 'Waffles',
    },
    {
      id: 'item4',
      name: 'Fresh Fruit Waffle',
      description: 'Belgian waffle topped with seasonal fresh fruits and a drizzle of honey.',
      price: 8.49,
      image: '/images/menu/fruit-waffle.jpg',
      category: 'Waffles',
    },
  ];

  return (
    <main className="min-h-screen py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">My Favorites</h1>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <GlassCard>
              <div className="p-6">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="relative h-16 w-16 rounded-full overflow-hidden">
                    <Image src={user.avatar} alt={user.name} fill className="object-cover" />
                  </div>
                  <div>
                    <h2 className="font-semibold">{user.name}</h2>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                  </div>
                </div>

                <nav className="space-y-2">
                  <Link
                    href="/profile"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Personal Information
                  </Link>
                  <Link
                    href="/profile/addresses"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Addresses
                  </Link>
                  <Link
                    href="/profile/payment-methods"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Payment Methods
                  </Link>
                  <Link
                    href="/profile/favorites"
                    className="block p-2 rounded-md bg-primary/10 text-primary font-medium"
                  >
                    Favorites
                  </Link>
                  <Link
                    href="/profile/settings"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Settings
                  </Link>
                  <Link
                    href="/orders"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Order History
                  </Link>
                </nav>
              </div>
            </GlassCard>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {favorites.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {favorites.map(item => (
                  <GlassCard key={item.id}>
                    <div className="relative">
                      <div className="h-48 w-full relative rounded-t-lg overflow-hidden">
                        <Image src={item.image} alt={item.name} fill className="object-cover" />
                      </div>
                      <button
                        className="absolute top-4 right-4 h-8 w-8 bg-white/80 dark:bg-black/50 rounded-full flex items-center justify-center text-red-500"
                        aria-label="Remove from favorites"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </button>
                      <div className="p-5">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-semibold text-lg">{item.name}</h3>
                            <p className="text-sm text-muted-foreground">{item.category}</p>
                          </div>
                          <div className="font-semibold">€{item.price.toFixed(2)}</div>
                        </div>
                        <p className="mt-2 text-muted-foreground text-sm line-clamp-2">
                          {item.description}
                        </p>
                        <div className="mt-4 flex justify-between items-center">
                          <Link
                            href={`/menu/${item.category.toLowerCase().replace(/\s+/g, '-')}/${item.id}`}
                          >
                            <GlassButton variant="secondary" size="sm">
                              View Details
                            </GlassButton>
                          </Link>
                          <GlassButton variant="primary" size="sm">
                            Add to Cart
                          </GlassButton>
                        </div>
                      </div>
                    </div>
                  </GlassCard>
                ))}
              </div>
            ) : (
              <GlassCard>
                <div className="p-8 text-center">
                  <div className="mx-auto w-16 h-16 mb-4 text-muted-foreground">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                      />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">No favorites yet</h3>
                  <p className="text-muted-foreground mb-6">
                    You haven't added any items to your favorites yet. Browse our menu and click the
                    heart icon to add items you love!
                  </p>
                  <Link href="/menu">
                    <GlassButton variant="primary">Browse Menu</GlassButton>
                  </Link>
                </div>
              </GlassCard>
            )}

            {favorites.length > 0 && (
              <div className="mt-8">
                <GlassCard>
                  <div className="p-6">
                    <h2 className="text-xl font-semibold mb-4">Quick Order</h2>
                    <p className="text-muted-foreground mb-4">
                      Want to order your favorite items quickly? Add them to your cart with one
                      click!
                    </p>
                    <div className="flex space-x-4">
                      <GlassButton variant="primary">Add All to Cart</GlassButton>
                      <Link href="/menu">
                        <GlassButton variant="secondary">Browse More Items</GlassButton>
                      </Link>
                    </div>
                  </div>
                </GlassCard>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}
