import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    const terminalId = searchParams.get('terminal_id')

    if (terminalId) {
      // Get specific terminal status
      const { data: terminal, error } = await supabase
        .from('pos_terminals')
        .select('*')
        .eq('terminal_id', terminalId)
        .single()

      if (error) {
        return NextResponse.json(
          { error: 'Terminal not found' },
          { status: 404 }
        )
      }

      // Get recent heartbeats for this terminal
      const { data: heartbeats } = await supabase
        .from('pos_heartbeats')
        .select('*')
        .eq('terminal_id', terminalId)
        .order('timestamp', { ascending: false })
        .limit(10)

      return NextResponse.json({
        terminal,
        recent_heartbeats: heartbeats || [],
        is_online: terminal.status === 'online' && 
                   new Date(terminal.last_heartbeat).getTime() > Date.now() - 60000, // Online if heartbeat within 1 minute
        last_seen: terminal.last_heartbeat
      })
    } else {
      // Get all terminals status
      const { data: terminals, error } = await supabase
        .from('pos_terminals')
        .select('*')
        .order('name')

      if (error) {
        return NextResponse.json(
          { error: 'Failed to fetch terminals' },
          { status: 500 }
        )
      }

      // Calculate online status for each terminal
      const terminalsWithStatus = terminals.map(terminal => ({
        ...terminal,
        is_online: terminal.status === 'online' && 
                   new Date(terminal.last_heartbeat).getTime() > Date.now() - 60000,
        last_seen: terminal.last_heartbeat
      }))

      // Calculate summary statistics
      const totalTerminals = terminals.length
      const onlineTerminals = terminalsWithStatus.filter(t => t.is_online).length
      const offlineTerminals = totalTerminals - onlineTerminals

      return NextResponse.json({
        terminals: terminalsWithStatus,
        summary: {
          total: totalTerminals,
          online: onlineTerminals,
          offline: offlineTerminals,
          online_percentage: totalTerminals > 0 ? (onlineTerminals / totalTerminals) * 100 : 0
        }
      })
    }
  } catch (error) {
    console.error('Terminal status error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { terminal_id, action } = await request.json()

    if (!terminal_id || !action) {
      return NextResponse.json(
        { error: 'terminal_id and action are required' },
        { status: 400 }
      )
    }

    // Handle different actions
    switch (action) {
      case 'restart':
        // TODO: Implement terminal restart command
        return NextResponse.json({
          success: true,
          message: 'Restart command sent to terminal',
          terminal_id,
          action
        })

      case 'sync':
        // TODO: Implement force sync command
        return NextResponse.json({
          success: true,
          message: 'Sync command sent to terminal',
          terminal_id,
          action
        })

      case 'update_config':
        // TODO: Implement configuration update
        return NextResponse.json({
          success: true,
          message: 'Configuration update sent to terminal',
          terminal_id,
          action
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Terminal action error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
