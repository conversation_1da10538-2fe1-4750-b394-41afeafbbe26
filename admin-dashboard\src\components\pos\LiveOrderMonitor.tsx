'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import {
  <PERSON>Cart,
  Clock,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Users,
  MapPin,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Eye,
  Filter,
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  Timer,
  CreditCard,
  Package,
  Star,
  ArrowUp,
  ArrowDown
} from 'lucide-react'

interface LiveOrder {
  id: string
  order_number: string
  terminal_id: string
  terminal_name: string
  customer_name?: string
  order_type: 'dine_in' | 'takeaway' | 'delivery'
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled'
  items: OrderItem[]
  total_amount: number
  tax_amount: number
  discount_amount: number
  payment_method: string
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
  created_at: string
  updated_at: string
  estimated_completion: string
  actual_completion?: string
  staff_id: string
  staff_name: string
  table_number?: string
  special_instructions?: string
}

interface OrderItem {
  id: string
  name: string
  quantity: number
  unit_price: number
  total_price: number
  category: string
  modifications?: string[]
}

interface OrderMetrics {
  total_orders_today: number
  completed_orders_today: number
  pending_orders: number
  average_order_value: number
  total_revenue_today: number
  average_preparation_time: number
  peak_hour: string
  busiest_terminal: string
  popular_items: PopularItem[]
  hourly_sales: HourlySales[]
}

interface PopularItem {
  name: string
  quantity_sold: number
  revenue: number
}

interface HourlySales {
  hour: number
  orders: number
  revenue: number
}

interface TerminalPerformance {
  terminal_id: string
  terminal_name: string
  orders_count: number
  revenue: number
  avg_order_time: number
  efficiency_score: number
  status: 'excellent' | 'good' | 'average' | 'poor'
}

export default function LiveOrderMonitor() {
  const [liveOrders, setLiveOrders] = useState<LiveOrder[]>([])
  const [orderMetrics, setOrderMetrics] = useState<OrderMetrics>({
    total_orders_today: 0,
    completed_orders_today: 0,
    pending_orders: 0,
    average_order_value: 0,
    total_revenue_today: 0,
    average_preparation_time: 0,
    peak_hour: '',
    busiest_terminal: '',
    popular_items: [],
    hourly_sales: []
  })
  const [terminalPerformance, setTerminalPerformance] = useState<TerminalPerformance[]>([])
  const [selectedTerminal, setSelectedTerminal] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedOrderType, setSelectedOrderType] = useState<string>('all')
  const [timeRange, setTimeRange] = useState<string>('today')
  const [showAnalytics, setShowAnalytics] = useState(false)
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadData()
    
    // Set up real-time updates every 3 seconds
    const interval = setInterval(() => {
      if (autoRefresh) {
        loadData()
      }
    }, 3000)

    return () => clearInterval(interval)
  }, [selectedTerminal, selectedStatus, selectedOrderType, timeRange, autoRefresh])

  const loadData = async () => {
    try {
      setLoading(true)
      await Promise.all([
        loadLiveOrders(),
        loadOrderMetrics(),
        loadTerminalPerformance()
      ])
    } catch (error) {
      console.error('Failed to load order monitoring data:', error)
      toast.error('Failed to load order monitoring data')
    } finally {
      setLoading(false)
    }
  }

  const loadLiveOrders = async () => {
    try {
      const params = new URLSearchParams()
      if (selectedTerminal !== 'all') params.append('terminal_id', selectedTerminal)
      if (selectedStatus !== 'all') params.append('status', selectedStatus)
      if (selectedOrderType !== 'all') params.append('order_type', selectedOrderType)
      params.append('time_range', timeRange)

      const response = await fetch(`/api/orders/live?${params}`)
      if (response.ok) {
        const data = await response.json()
        setLiveOrders(data.orders || [])
      }
    } catch (error) {
      console.error('Failed to load live orders:', error)
    }
  }

  const loadOrderMetrics = async () => {
    try {
      const params = new URLSearchParams()
      params.append('time_range', timeRange)
      if (selectedTerminal !== 'all') params.append('terminal_id', selectedTerminal)

      const response = await fetch(`/api/orders/metrics?${params}`)
      if (response.ok) {
        const data = await response.json()
        setOrderMetrics(data.metrics || orderMetrics)
      }
    } catch (error) {
      console.error('Failed to load order metrics:', error)
    }
  }

  const loadTerminalPerformance = async () => {
    try {
      const params = new URLSearchParams()
      params.append('time_range', timeRange)

      const response = await fetch(`/api/orders/terminal-performance?${params}`)
      if (response.ok) {
        const data = await response.json()
        setTerminalPerformance(data.performance || [])
      }
    } catch (error) {
      console.error('Failed to load terminal performance:', error)
    }
  }

  const updateOrderStatus = async (orderId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/orders/${orderId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus })
      })

      if (response.ok) {
        toast.success(`Order status updated to ${newStatus}`)
        await loadData()
      } else {
        toast.error('Failed to update order status')
      }
    } catch (error) {
      console.error('Failed to update order status:', error)
      toast.error('Failed to update order status')
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'preparing':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
      case 'ready':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'pending':
        return <AlertTriangle className="w-4 h-4 text-orange-500" />
      default:
        return <Activity className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300'
      case 'cancelled':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300'
      case 'preparing':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300'
      case 'ready':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300'
      case 'pending':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-300'
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  const getOrderTypeIcon = (type: string) => {
    switch (type) {
      case 'dine_in':
        return <Users className="w-4 h-4" />
      case 'takeaway':
        return <Package className="w-4 h-4" />
      case 'delivery':
        return <MapPin className="w-4 h-4" />
      default:
        return <ShoppingCart className="w-4 h-4" />
    }
  }

  const getPerformanceColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300'
      case 'good':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300'
      case 'average':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300'
      case 'poor':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300'
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString()
  }

  const getTimeSinceOrder = (createdAt: string) => {
    const now = new Date()
    const orderTime = new Date(createdAt)
    const diffMs = now.getTime() - orderTime.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    
    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    const diffHours = Math.floor(diffMins / 60)
    return `${diffHours}h ${diffMins % 60}m ago`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Live Order Monitor</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Real-time order tracking and performance monitoring across all terminals
          </p>
        </div>
        
        <div className="flex gap-2 mt-4 sm:mt-0">
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
              autoRefresh 
                ? 'bg-green-600 text-white hover:bg-green-700' 
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            <RefreshCw className={`w-4 h-4 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh: {autoRefresh ? 'On' : 'Off'}
          </button>
          
          <button
            onClick={() => setShowAnalytics(!showAnalytics)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
              showAnalytics 
                ? 'bg-blue-600 text-white hover:bg-blue-700' 
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            <BarChart3 className="w-4 h-4" />
            Analytics
          </button>
        </div>
      </div>

      {/* Order Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Orders</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{orderMetrics.total_orders_today}</p>
            </div>
            <ShoppingCart className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Revenue Today</p>
              <p className="text-2xl font-bold text-green-600">{formatCurrency(orderMetrics.total_revenue_today)}</p>
            </div>
            <DollarSign className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Order Value</p>
              <p className="text-2xl font-bold text-purple-600">{formatCurrency(orderMetrics.average_order_value)}</p>
            </div>
            <TrendingUp className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Orders</p>
              <p className="text-2xl font-bold text-orange-600">{orderMetrics.pending_orders}</p>
            </div>
            <Clock className="w-8 h-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Terminal
            </label>
            <select
              value={selectedTerminal}
              onChange={(e) => setSelectedTerminal(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Terminals</option>
              {terminalPerformance.map((terminal) => (
                <option key={terminal.terminal_id} value={terminal.terminal_id}>
                  {terminal.terminal_name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status
            </label>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="preparing">Preparing</option>
              <option value="ready">Ready</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Order Type
            </label>
            <select
              value={selectedOrderType}
              onChange={(e) => setSelectedOrderType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Types</option>
              <option value="dine_in">Dine In</option>
              <option value="takeaway">Takeaway</option>
              <option value="delivery">Delivery</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Time Range
            </label>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="today">Today</option>
              <option value="yesterday">Yesterday</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={loadData}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <RefreshCw className="w-4 h-4 mx-auto" />
            </button>
          </div>
        </div>
      </div>

      {/* Terminal Performance */}
      {showAnalytics && (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Terminal Performance</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Performance metrics for each terminal
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
            {terminalPerformance.map((terminal) => (
              <div key={terminal.terminal_id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white">{terminal.terminal_name}</h4>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPerformanceColor(terminal.status)}`}>
                    {terminal.status}
                  </span>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Orders:</span>
                    <span className="font-medium">{terminal.orders_count}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Revenue:</span>
                    <span className="font-medium">{formatCurrency(terminal.revenue)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Avg Time:</span>
                    <span className="font-medium">{terminal.avg_order_time}min</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Efficiency:</span>
                    <span className="font-medium">{terminal.efficiency_score}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Live Orders */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Live Orders</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Real-time order tracking and management
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 p-6">
          {liveOrders.map((order) => (
            <div key={order.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium text-gray-900 dark:text-white">#{order.order_number}</h4>
                    {getOrderTypeIcon(order.order_type)}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">{order.terminal_name}</p>
                </div>

                <div className="flex flex-col items-end gap-2">
                  <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                    {getStatusIcon(order.status)}
                    {order.status}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {getTimeSinceOrder(order.created_at)}
                  </span>
                </div>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Customer:</span>
                  <span className="font-medium">{order.customer_name || 'Walk-in'}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Staff:</span>
                  <span className="font-medium">{order.staff_name}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Total:</span>
                  <span className="font-medium">{formatCurrency(order.total_amount)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Payment:</span>
                  <span className={`font-medium ${
                    order.payment_status === 'paid' ? 'text-green-600' :
                    order.payment_status === 'failed' ? 'text-red-600' :
                    'text-yellow-600'
                  }`}>
                    {order.payment_status}
                  </span>
                </div>
                {order.table_number && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Table:</span>
                    <span className="font-medium">{order.table_number}</span>
                  </div>
                )}
              </div>

              <div className="mb-4">
                <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Items ({order.items.length})</h5>
                <div className="space-y-1 max-h-24 overflow-y-auto">
                  {order.items.map((item) => (
                    <div key={item.id} className="flex justify-between text-xs">
                      <span className="text-gray-600 dark:text-gray-400">
                        {item.quantity}x {item.name}
                      </span>
                      <span className="font-medium">{formatCurrency(item.total_price)}</span>
                    </div>
                  ))}
                </div>
              </div>

              {order.special_instructions && (
                <div className="mb-4 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded">
                  <p className="text-xs text-yellow-700 dark:text-yellow-400">
                    <strong>Special Instructions:</strong> {order.special_instructions}
                  </p>
                </div>
              )}

              <div className="flex gap-2">
                {order.status === 'pending' && (
                  <button
                    onClick={() => updateOrderStatus(order.id, 'preparing')}
                    className="flex-1 px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                  >
                    Start Preparing
                  </button>
                )}

                {order.status === 'preparing' && (
                  <button
                    onClick={() => updateOrderStatus(order.id, 'ready')}
                    className="flex-1 px-3 py-2 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                  >
                    Mark Ready
                  </button>
                )}

                {order.status === 'ready' && (
                  <button
                    onClick={() => updateOrderStatus(order.id, 'completed')}
                    className="flex-1 px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
                  >
                    Complete
                  </button>
                )}

                <button
                  onClick={() => {/* View order details */}}
                  className="px-3 py-2 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500"
                >
                  <Eye className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>

        {liveOrders.length === 0 && (
          <div className="text-center py-12">
            <ShoppingCart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No orders found</h3>
            <p className="text-gray-600 dark:text-gray-400">
              No orders match your current filters. Try adjusting your search criteria.
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
