'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import {
  Package,
  Refresh<PERSON><PERSON>,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Eye,
  Edit,
  Download,
  Search,
  BarChart3,
  Settings
} from 'lucide-react'

interface InventoryItem {
  id: string
  sku: string
  name: string
  category: string
  current_stock: number
  reserved_stock: number
  available_stock: number
  reorder_level: number
  max_stock: number
  unit_cost: number
  selling_price: number
  last_updated: string
  sync_status: 'synced' | 'pending' | 'conflict' | 'failed'
  terminal_stocks: TerminalStock[]
}

interface TerminalStock {
  terminal_id: string
  terminal_name: string
  stock_level: number
  last_sync: string
  sync_status: 'synced' | 'pending' | 'conflict' | 'failed'
  conflict_reason?: string
}

interface SyncConflict {
  id: string
  item_id: string
  item_name: string
  terminal_id: string
  admin_value: number
  terminal_value: number
  conflict_type: 'stock_level' | 'price' | 'availability'
  created_at: string
  resolution_strategy: 'admin_wins' | 'terminal_wins' | 'manual' | 'average'
  resolved: boolean
}

interface SyncMetrics {
  total_items: number
  synced_items: number
  pending_items: number
  conflict_items: number
  failed_items: number
  last_sync: string
  sync_success_rate: number
  avg_sync_time: number
}

export default function InventorySyncManager() {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([])
  const [syncConflicts, setSyncConflicts] = useState<SyncConflict[]>([])
  const [syncMetrics, setSyncMetrics] = useState<SyncMetrics>({
    total_items: 0,
    synced_items: 0,
    pending_items: 0,
    conflict_items: 0,
    failed_items: 0,
    last_sync: '',
    sync_success_rate: 0,
    avg_sync_time: 0
  })
  const [selectedTerminal, setSelectedTerminal] = useState<string>('all')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [syncStatusFilter, setSyncStatusFilter] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [showConflictModal, setShowConflictModal] = useState(false)
  const [selectedConflict, setSelectedConflict] = useState<SyncConflict | null>(null)
  const [loading, setLoading] = useState(true)
  const [syncing, setSyncing] = useState(false)
  const [autoSyncEnabled, setAutoSyncEnabled] = useState(true)

  useEffect(() => {
    loadData()
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(() => {
      if (autoSyncEnabled) {
        loadData()
      }
    }, 30000)

    return () => clearInterval(interval)
  }, [selectedTerminal, selectedCategory, syncStatusFilter, autoSyncEnabled])

  const loadData = async () => {
    try {
      setLoading(true)
      await Promise.all([
        loadInventoryItems(),
        loadSyncConflicts(),
        loadSyncMetrics()
      ])
    } catch (error) {
      console.error('Failed to load data:', error)
      toast.error('Failed to load inventory sync data')
    } finally {
      setLoading(false)
    }
  }

  const loadInventoryItems = async () => {
    try {
      const params = new URLSearchParams()
      if (selectedTerminal !== 'all') params.append('terminal_id', selectedTerminal)
      if (selectedCategory !== 'all') params.append('category', selectedCategory)
      if (syncStatusFilter !== 'all') params.append('sync_status', syncStatusFilter)
      if (searchTerm) params.append('search', searchTerm)

      const response = await fetch(`/api/inventory/sync-status?${params}`)
      if (response.ok) {
        const data = await response.json()
        setInventoryItems(data.items || [])
      }
    } catch (error) {
      console.error('Failed to load inventory items:', error)
    }
  }

  const loadSyncConflicts = async () => {
    try {
      const response = await fetch('/api/inventory/sync-conflicts')
      if (response.ok) {
        const data = await response.json()
        setSyncConflicts(data.conflicts || [])
      }
    } catch (error) {
      console.error('Failed to load sync conflicts:', error)
    }
  }

  const loadSyncMetrics = async () => {
    try {
      const response = await fetch('/api/inventory/sync-metrics')
      if (response.ok) {
        const data = await response.json()
        setSyncMetrics(data.metrics || syncMetrics)
      }
    } catch (error) {
      console.error('Failed to load sync metrics:', error)
    }
  }

  const syncInventory = async (itemId?: string, terminalId?: string) => {
    try {
      setSyncing(true)
      const response = await fetch('/api/inventory/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          item_id: itemId,
          terminal_id: terminalId || (selectedTerminal === 'all' ? null : selectedTerminal),
          force_sync: true
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          toast.success(itemId ? 'Item synchronized successfully' : 'Inventory synchronized successfully')
          await loadData()
        } else {
          toast.error(`Sync failed: ${result.errors?.join(', ')}`)
        }
      } else {
        toast.error('Failed to sync inventory')
      }
    } catch (error) {
      console.error('Failed to sync inventory:', error)
      toast.error('Failed to sync inventory')
    } finally {
      setSyncing(false)
    }
  }

  const resolveConflict = async (conflictId: string, resolution: string, customValue?: number) => {
    try {
      const response = await fetch('/api/inventory/resolve-conflict', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conflict_id: conflictId,
          resolution_strategy: resolution,
          custom_value: customValue
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          toast.success('Conflict resolved successfully')
          await loadData()
          setShowConflictModal(false)
          setSelectedConflict(null)
        } else {
          toast.error(`Failed to resolve conflict: ${result.error}`)
        }
      } else {
        toast.error('Failed to resolve conflict')
      }
    } catch (error) {
      console.error('Failed to resolve conflict:', error)
      toast.error('Failed to resolve conflict')
    }
  }

  const exportInventoryData = async () => {
    try {
      const response = await fetch('/api/inventory/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          terminal_id: selectedTerminal === 'all' ? null : selectedTerminal,
          include_sync_status: true
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `inventory-sync-${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        toast.success('Inventory data exported successfully')
      } else {
        toast.error('Failed to export inventory data')
      }
    } catch (error) {
      console.error('Failed to export inventory:', error)
      toast.error('Failed to export inventory data')
    }
  }

  const getSyncStatusIcon = (status: string) => {
    switch (status) {
      case 'synced':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'conflict':
        return <AlertTriangle className="w-4 h-4 text-orange-500" />
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <RefreshCw className="w-4 h-4 text-gray-500" />
    }
  }

  const getSyncStatusColor = (status: string) => {
    switch (status) {
      case 'synced':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300'
      case 'pending':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300'
      case 'conflict':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-300'
      case 'failed':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300'
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = 
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.sku.toLowerCase().includes(searchTerm.toLowerCase())
    
    return matchesSearch
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Inventory Synchronization</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor and manage inventory sync between admin dashboard and POS terminals
          </p>
        </div>
        
        <div className="flex gap-2 mt-4 sm:mt-0">
          <button
            onClick={() => setAutoSyncEnabled(!autoSyncEnabled)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
              autoSyncEnabled 
                ? 'bg-green-600 text-white hover:bg-green-700' 
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            <RefreshCw className={`w-4 h-4 ${autoSyncEnabled ? 'animate-spin' : ''}`} />
            Auto Sync: {autoSyncEnabled ? 'On' : 'Off'}
          </button>
          
          <button
            onClick={exportInventoryData}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Download className="w-4 h-4" />
            Export
          </button>
          
          <button
            onClick={() => syncInventory()}
            disabled={syncing}
            className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
          >
            {syncing ? (
              <div className="w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
            ) : (
              <RefreshCw className="w-4 h-4" />
            )}
            {syncing ? 'Syncing...' : 'Sync All'}
          </button>
        </div>
      </div>

      {/* Sync Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Items</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{syncMetrics.total_items}</p>
            </div>
            <Package className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Sync Success Rate</p>
              <p className="text-2xl font-bold text-green-600">{(syncMetrics.sync_success_rate * 100).toFixed(1)}%</p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Syncs</p>
              <p className="text-2xl font-bold text-yellow-600">{syncMetrics.pending_items}</p>
            </div>
            <Clock className="w-8 h-8 text-yellow-500" />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Conflicts</p>
              <p className="text-2xl font-bold text-orange-600">{syncMetrics.conflict_items}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search Items
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search by name or SKU..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Terminal
            </label>
            <select
              value={selectedTerminal}
              onChange={(e) => setSelectedTerminal(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Terminals</option>
              <option value="terminal-001">Terminal 001</option>
              <option value="terminal-002">Terminal 002</option>
              <option value="terminal-003">Terminal 003</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Category
            </label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Categories</option>
              <option value="food">Food</option>
              <option value="beverages">Beverages</option>
              <option value="desserts">Desserts</option>
              <option value="sides">Sides</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Sync Status
            </label>
            <select
              value={syncStatusFilter}
              onChange={(e) => setSyncStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Status</option>
              <option value="synced">Synced</option>
              <option value="pending">Pending</option>
              <option value="conflict">Conflicts</option>
              <option value="failed">Failed</option>
            </select>
          </div>
        </div>
      </div>

      {/* Sync Conflicts Alert */}
      {syncConflicts.length > 0 && (
        <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-5 h-5 text-orange-600" />
              <div>
                <h3 className="font-medium text-orange-800 dark:text-orange-200">
                  {syncConflicts.length} Sync Conflict{syncConflicts.length > 1 ? 's' : ''} Detected
                </h3>
                <p className="text-sm text-orange-700 dark:text-orange-300">
                  Some inventory items have conflicting values between admin and terminals
                </p>
              </div>
            </div>
            <button
              onClick={() => setShowConflictModal(true)}
              className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700"
            >
              Resolve Conflicts
            </button>
          </div>
        </div>
      )}

      {/* Inventory Items Table */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Inventory Items</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Monitor sync status for all inventory items across terminals
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Item
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Stock Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Sync Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Terminal Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Last Updated
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredItems.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">{item.name}</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        SKU: {item.sku} • {item.category}
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 dark:text-white">
                      <div>Available: {item.available_stock}</div>
                      <div className="text-gray-500 dark:text-gray-400">
                        Reserved: {item.reserved_stock}
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4">
                    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getSyncStatusColor(item.sync_status)}`}>
                      {getSyncStatusIcon(item.sync_status)}
                      {item.sync_status}
                    </span>
                  </td>

                  <td className="px-6 py-4">
                    <div className="space-y-1">
                      {item.terminal_stocks.map((terminal) => (
                        <div key={terminal.terminal_id} className="flex items-center gap-2 text-xs">
                          <span className="text-gray-600 dark:text-gray-400">{terminal.terminal_name}:</span>
                          <span className="font-medium">{terminal.stock_level}</span>
                          {getSyncStatusIcon(terminal.sync_status)}
                        </div>
                      ))}
                    </div>
                  </td>

                  <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                    {new Date(item.last_updated).toLocaleString()}
                  </td>

                  <td className="px-6 py-4 text-center">
                    <div className="flex items-center justify-center gap-2">
                      <button
                        onClick={() => syncInventory(item.id)}
                        disabled={syncing}
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        title="Sync item"
                      >
                        <RefreshCw className="w-4 h-4" />
                      </button>

                      <button
                        onClick={() => {/* View details */}}
                        className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                        title="View details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>

                      <button
                        onClick={() => {/* Edit item */}}
                        className="text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300"
                        title="Edit item"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No inventory items found</h3>
            <p className="text-gray-600 dark:text-gray-400">
              Try adjusting your search criteria or check your inventory setup.
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
