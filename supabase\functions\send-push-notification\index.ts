/**
 * Supabase Edge Function: Send Push Notification
 * Handles sending web push notifications for real-time order updates
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

interface PushRequest {
  userId: string;
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  image?: string;
  data?: Record<string, any>;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
  orderId?: string;
  type: string;
  url?: string;
  requireInteraction?: boolean;
  silent?: boolean;
}

interface PushSubscription {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Push notification templates
const getOrderNotificationData = (type: string, orderData: any) => {
  const baseData = {
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    requireInteraction: true,
    data: {
      orderId: orderData.orderNumber,
      type,
      url: `/orders/${orderData.orderNumber}`,
      timestamp: new Date().toISOString(),
    },
  };

  switch (type) {
    case 'order_confirmed':
      return {
        ...baseData,
        title: '🥞 Order Confirmed!',
        body: `Your order #${orderData.orderNumber} has been confirmed and is being prepared.`,
        actions: [
          { action: 'view', title: 'View Order', icon: '/icons/view.png' },
          { action: 'track', title: 'Track Order', icon: '/icons/track.png' }
        ]
      };

    case 'order_preparing':
      return {
        ...baseData,
        title: '👨‍🍳 Order Being Prepared',
        body: `Our chefs are now preparing your delicious crepes! Order #${orderData.orderNumber}`,
        image: '/images/kitchen-preparing.jpg',
        actions: [
          { action: 'track', title: 'Track Progress', icon: '/icons/track.png' }
        ]
      };

    case 'order_ready':
      return {
        ...baseData,
        title: '✅ Order Ready!',
        body: `Great news! Your order #${orderData.orderNumber} is ready for pickup.`,
        requireInteraction: true,
        actions: [
          { action: 'directions', title: 'Get Directions', icon: '/icons/directions.png' },
          { action: 'call', title: 'Call Store', icon: '/icons/phone.png' }
        ]
      };

    case 'order_out_for_delivery':
      return {
        ...baseData,
        title: '🚗 Order On The Way!',
        body: `Your order #${orderData.orderNumber} is out for delivery. ${orderData.estimatedDeliveryTime ? `ETA: ${orderData.estimatedDeliveryTime}` : ''}`,
        actions: [
          { action: 'track', title: 'Track Delivery', icon: '/icons/track.png' },
          { action: 'contact', title: 'Contact Driver', icon: '/icons/phone.png' }
        ]
      };

    case 'order_delivered':
      return {
        ...baseData,
        title: '🎉 Order Delivered!',
        body: `Your order #${orderData.orderNumber} has been delivered. Enjoy your meal!`,
        image: '/images/delivered.jpg',
        actions: [
          { action: 'rate', title: 'Rate Order', icon: '/icons/star.png' },
          { action: 'reorder', title: 'Order Again', icon: '/icons/reorder.png' }
        ]
      };

    case 'order_cancelled':
      return {
        ...baseData,
        title: '❌ Order Cancelled',
        body: `Your order #${orderData.orderNumber} has been cancelled. We apologize for any inconvenience.`,
        actions: [
          { action: 'support', title: 'Contact Support', icon: '/icons/support.png' },
          { action: 'reorder', title: 'Place New Order', icon: '/icons/reorder.png' }
        ]
      };

    case 'delivery_update':
      return {
        ...baseData,
        title: '📍 Delivery Update',
        body: `Your driver is ${orderData.driverStatus || 'on the way'}. Order #${orderData.orderNumber}`,
        silent: false,
        actions: [
          { action: 'track', title: 'Track Live', icon: '/icons/track.png' }
        ]
      };

    default:
      return {
        ...baseData,
        title: '📱 Order Update',
        body: `Update for order #${orderData.orderNumber}`,
      };
  }
};

const sendWebPush = async (subscription: PushSubscription, payload: any) => {
  const vapidPublicKey = Deno.env.get('VAPID_PUBLIC_KEY');
  const vapidPrivateKey = Deno.env.get('VAPID_PRIVATE_KEY');
  const vapidSubject = Deno.env.get('VAPID_SUBJECT') || 'mailto:<EMAIL>';
  
  if (!vapidPublicKey || !vapidPrivateKey) {
    throw new Error('VAPID keys are not properly configured');
  }

  // Import web-push library (this would need to be available in the Deno environment)
  // For now, we'll use a direct implementation or fetch to a push service
  
  // This is a simplified implementation - in production, you'd use a proper web-push library
  const response = await fetch(subscription.endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `vapid t=${await generateVAPIDToken(vapidPrivateKey, vapidPublicKey, vapidSubject, subscription.endpoint)}, k=${vapidPublicKey}`,
      'TTL': '86400', // 24 hours
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to send push notification: ${response.status} ${error}`);
  }

  return { success: true, status: response.status };
};

// Simplified VAPID token generation (in production, use a proper library)
const generateVAPIDToken = async (privateKey: string, publicKey: string, subject: string, audience: string) => {
  // This is a placeholder - implement proper VAPID JWT generation
  // You would typically use a JWT library for this
  const header = btoa(JSON.stringify({ typ: 'JWT', alg: 'ES256' }));
  const payload = btoa(JSON.stringify({
    aud: new URL(audience).origin,
    exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
    sub: subject
  }));
  
  // In a real implementation, you'd sign this with the private key
  return `${header}.${payload}.signature`;
};

const getUserPushSubscriptions = async (supabase: any, userId: string) => {
  const { data, error } = await supabase
    .from('push_subscriptions')
    .select('*')
    .eq('user_id', userId)
    .eq('is_active', true);

  if (error) {
    throw new Error(`Failed to fetch push subscriptions: ${error.message}`);
  }

  return data || [];
};

const logNotification = async (supabase: any, data: {
  userId: string;
  type: string;
  channel: string;
  status: string;
  orderId?: string;
  errorMessage?: string;
  subscriptionId?: string;
}) => {
  try {
    await supabase
      .from('notification_logs')
      .insert({
        user_id: data.userId,
        type: data.type,
        channel: data.channel,
        status: data.status,
        order_id: data.orderId,
        error_message: data.errorMessage,
        subscription_id: data.subscriptionId,
        sent_at: new Date().toISOString(),
      });
  } catch (error) {
    console.error('Failed to log notification:', error);
  }
};

const updateSubscriptionStatus = async (supabase: any, subscriptionId: string, isActive: boolean) => {
  try {
    await supabase
      .from('push_subscriptions')
      .update({ 
        is_active: isActive,
        last_used: isActive ? new Date().toISOString() : undefined
      })
      .eq('id', subscriptionId);
  } catch (error) {
    console.error('Failed to update subscription status:', error);
  }
};

const checkUserPushPreferences = async (supabase: any, userId: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('preferences')
      .eq('user_id', userId)
      .single();

    if (error || !data) {
      console.warn(`Could not fetch push preferences for user ${userId}:`, error);
      return true; // Default to sending if we can't verify preferences
    }

    return data.preferences?.pushNotifications !== false;
  } catch (error) {
    console.error('Error checking push preferences:', error);
    return true;
  }
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { 
      userId, 
      title, 
      body, 
      icon, 
      badge, 
      image, 
      data, 
      actions, 
      orderId, 
      type, 
      url, 
      requireInteraction, 
      silent,
      orderData,
      skipPreferenceCheck
    } = await req.json();

    if (!userId || !type) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: userId, type' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check user push preferences unless explicitly skipped
    if (!skipPreferenceCheck) {
      const pushEnabled = await checkUserPushPreferences(supabaseClient, userId);
      if (!pushEnabled) {
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: 'Push notifications disabled for user',
            skipped: true
          }),
          { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
    }

    // Get user's push subscriptions
    const subscriptions = await getUserPushSubscriptions(supabaseClient, userId);
    
    if (subscriptions.length === 0) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'No active push subscriptions found for user',
          skipped: true
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Generate notification payload
    let notificationPayload;
    if (orderData && type.startsWith('order_')) {
      notificationPayload = getOrderNotificationData(type, orderData);
    } else {
      notificationPayload = {
        title: title || 'The Small Creperie',
        body: body || 'You have a new notification',
        icon: icon || '/icons/icon-192x192.png',
        badge: badge || '/icons/badge-72x72.png',
        image,
        data: {
          ...data,
          orderId,
          type,
          url: url || '/',
          timestamp: new Date().toISOString(),
        },
        actions,
        requireInteraction,
        silent,
      };
    }

    const results = [];
    let successCount = 0;
    let failureCount = 0;

    // Send to all user's subscriptions
    for (const subscription of subscriptions) {
      try {
        const pushSubscription = {
          endpoint: subscription.endpoint,
          keys: {
            p256dh: subscription.p256dh_key,
            auth: subscription.auth_key,
          },
        };

        await sendWebPush(pushSubscription, notificationPayload);
        
        // Update subscription last used
        await updateSubscriptionStatus(supabaseClient, subscription.id, true);
        
        // Log successful notification
        await logNotification(supabaseClient, {
          userId,
          type,
          channel: 'push',
          status: 'sent',
          orderId,
          subscriptionId: subscription.id,
        });

        results.push({ subscriptionId: subscription.id, success: true });
        successCount++;

      } catch (error) {
        console.error(`Failed to send push to subscription ${subscription.id}:`, error);
        
        // If subscription is invalid, mark as inactive
        if (error.message.includes('410') || error.message.includes('invalid')) {
          await updateSubscriptionStatus(supabaseClient, subscription.id, false);
        }
        
        // Log failed notification
        await logNotification(supabaseClient, {
          userId,
          type,
          channel: 'push',
          status: 'failed',
          orderId,
          subscriptionId: subscription.id,
          errorMessage: error.message,
        });

        results.push({ 
          subscriptionId: subscription.id, 
          success: false, 
          error: error.message 
        });
        failureCount++;
      }
    }

    return new Response(
      JSON.stringify({ 
        success: successCount > 0,
        message: `Push notifications sent: ${successCount} successful, ${failureCount} failed`,
        totalSubscriptions: subscriptions.length,
        successCount,
        failureCount,
        results
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error sending push notification:', error);

    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});