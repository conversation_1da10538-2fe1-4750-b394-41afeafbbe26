import React, { memo, useState, useCallback } from 'react';
import { POSGlassButton, POSGlassModal } from './ui/pos-glass-components';
import { MenuModal } from './modals/MenuModal';
import { CustomerSearchModal } from './modals/CustomerSearchModal';
import { AddCustomerModal } from './modals/AddCustomerModal';
import { FloatingActionButton } from './ui/FloatingActionButton';
import { useOrderStore } from '../hooks/useOrderStore';
import toast from 'react-hot-toast';

interface OrderFlowProps {
  className?: string;
}

interface Customer {
  id: string;
  phone: string;
  name: string;
  email?: string;
  address?: string;
  postal_code?: string;
  floor_number?: string;
  notes?: string;
  name_on_ringer?: string;
  addresses?: Array<{
    id: string;
    street_address: string;
    city: string;
    postal_code?: string;
    floor_number?: string;
    notes?: string;
    address_type: string;
    is_default: boolean;
    created_at: string;
  }>;
}

/**
 * Complete Order Flow Component
 * Handles the full order creation workflow from type selection to completion
 */
const OrderFlow = memo<OrderFlowProps>(({ className = '' }) => {
  // Modal states
  const [isOrderTypeModalOpen, setIsOrderTypeModalOpen] = useState(false);
  const [isCustomerSearchModalOpen, setIsCustomerSearchModalOpen] = useState(false);
  const [isAddCustomerModalOpen, setIsAddCustomerModalOpen] = useState(false);
  const [isMenuModalOpen, setIsMenuModalOpen] = useState(false);

  // Order flow states
  const [selectedOrderType, setSelectedOrderType] = useState<'pickup' | 'delivery' | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedAddress, setSelectedAddress] = useState<any>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [isProcessingOrder, setIsProcessingOrder] = useState(false);

  // Order store for managing orders
  const { createOrder } = useOrderStore();

  // Reset all flow states
  const resetFlow = useCallback(() => {
    setIsOrderTypeModalOpen(false);
    setIsCustomerSearchModalOpen(false);
    setIsAddCustomerModalOpen(false);
    setIsMenuModalOpen(false);
    setSelectedOrderType(null);
    setSelectedCustomer(null);
    setSelectedAddress(null);
    setIsTransitioning(false);
  }, []);

  const handleStartNewOrder = useCallback(() => {
    resetFlow();
    setIsOrderTypeModalOpen(true);
  }, [resetFlow]);

  const handleSelectOrderType = useCallback(async (type: 'pickup' | 'delivery') => {
    setIsTransitioning(true);
    setSelectedOrderType(type);

    // Smooth transition with loading state
    await new Promise(resolve => setTimeout(resolve, 300));

    setIsOrderTypeModalOpen(false);

    if (type === 'pickup') {
      // For pickup orders, create a default customer and go directly to menu
      const pickupCustomer: Customer = {
        id: 'pickup-customer',
        name: 'Walk-in Customer',
        phone: '',
        email: '',
        addresses: []
      };
      setSelectedCustomer(pickupCustomer);
      setIsMenuModalOpen(true);
    } else {
      // For delivery orders, show customer search modal
      setIsCustomerSearchModalOpen(true);
    }

    setIsTransitioning(false);
  }, []);

  const handleCustomerSelected = useCallback((customer: Customer) => {
    setSelectedCustomer(customer);
    setIsCustomerSearchModalOpen(false);

    // If customer has addresses, use the first one or let them select
    if (customer.addresses && customer.addresses.length > 0) {
      setSelectedAddress(customer.addresses[0]);
    }

    setIsMenuModalOpen(true);
  }, []);

  const handleAddNewCustomer = useCallback(() => {
    setIsCustomerSearchModalOpen(false);
    setIsAddCustomerModalOpen(true);
  }, []);

  const handleCustomerAdded = useCallback((newCustomer: Customer) => {
    setSelectedCustomer(newCustomer);
    setIsAddCustomerModalOpen(false);

    // If new customer has addresses, use the first one
    if (newCustomer.addresses && newCustomer.addresses.length > 0) {
      setSelectedAddress(newCustomer.addresses[0]);
    }

    setIsMenuModalOpen(true);
    toast.success('Customer added successfully!');
  }, []);

  const handleMenuModalClose = useCallback(() => {
    resetFlow();
  }, [resetFlow]);

  // Handle order completion from menu
  const handleOrderComplete = useCallback(async (orderData: any) => {
    setIsProcessingOrder(true);
    
    try {
      // Calculate delivery details
      let deliveryAddress = null;
      let deliveryFee = 0;
      
      if (selectedOrderType === 'delivery' && selectedAddress) {
        deliveryAddress = `${selectedAddress.street_address}, ${selectedAddress.city}`;
        if (selectedAddress.postal_code) {
          deliveryAddress += ` ${selectedAddress.postal_code}`;
        }
        if (selectedAddress.floor_number) {
          deliveryAddress += `, Floor: ${selectedAddress.floor_number}`;
        }
        deliveryFee = 2.50; // Default delivery fee - could be fetched from admin settings
      }

      // Create order object matching the expected interface
      const orderToCreate = {
        orderNumber: `ORD-${Date.now().toString().slice(-6)}`,
        customerName: selectedCustomer?.name || 'Walk-in Customer',
        customerPhone: selectedCustomer?.phone || '',
        orderType: (selectedOrderType === 'pickup' ? 'takeaway' : 'delivery') as 'takeaway' | 'delivery',
        status: 'pending' as const,
        items: orderData.items.map((item: any) => ({
          id: item.id || `item-${Date.now()}-${Math.random()}`,
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          notes: item.notes || '',
          customizations: item.customizations || []
        })),
        totalAmount: orderData.total + deliveryFee,
        deliveryAddress,
        deliveryFee,
        notes: orderData.notes || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        paymentStatus: (orderData.paymentData ? 'completed' : 'pending') as 'pending' | 'completed' | 'processing' | 'failed' | 'refunded',
        paymentMethod: orderData.paymentData?.method || undefined,
        paymentTransactionId: orderData.paymentData?.transactionId || undefined,
        estimatedTime: 15, // Default 15 minutes - could be calculated based on items
        // Additional fields for Supabase sync
        customer_id: selectedCustomer?.id !== 'pickup-customer' ? selectedCustomer?.id : null,
        customer_email: selectedCustomer?.email || null,
        tax_amount: Math.round(orderData.total * 0.24 * 100) / 100, // Greek VAT 24%
        discount_amount: 0,
        table_number: selectedOrderType === 'pickup' ? null : undefined
      };

      // Create order using the store method (handles both local storage and Supabase sync)
      const result = await createOrder(orderToCreate);

      if (result.success) {
        toast.success(`Order ${orderToCreate.orderNumber} created successfully!`);
        
        // Additional success feedback for delivery orders
        if (selectedOrderType === 'delivery' && deliveryAddress) {
          setTimeout(() => {
            toast.success(`Delivery to: ${deliveryAddress}`, { duration: 4000 });
          }, 1000);
        }
        
        resetFlow();
      } else {
        toast.error('Failed to create order. Please try again.');
        if ('error' in result) {
          console.error('Order creation failed:', result.error);
        }
      }
    } catch (error) {
      console.error('Error creating order:', error);
      toast.error('Failed to create order. Please try again.');
    } finally {
      setIsProcessingOrder(false);
    }
  }, [selectedCustomer, selectedOrderType, selectedAddress, createOrder, resetFlow]);

  return (
    <div className={`order-flow ${className}`}>
      {/* Floating Action Button for New Order */}
      <FloatingActionButton
        onClick={handleStartNewOrder}
        aria-label="Start New Order"
      />

      {/* Order Type Selection Modal */}
      <POSGlassModal
        isOpen={isOrderTypeModalOpen}
        onClose={() => setIsOrderTypeModalOpen(false)}
        title="Select Order Type"
      >
        <div className="space-y-4">
          {isTransitioning ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white/60"></div>
              <span className="ml-3 text-white/70">Setting up order...</span>
            </div>
          ) : (
            <>
              <POSGlassButton
                size="large"
                onClick={() => handleSelectOrderType('pickup')}
                className="w-full transform transition-all duration-200 hover:scale-105 hover:shadow-lg"
              >
                <span className="text-2xl mr-3">🏪</span>
                <span className="text-lg font-medium">Pickup Order</span>
              </POSGlassButton>
              
              <POSGlassButton
                size="large"
                onClick={() => handleSelectOrderType('delivery')}
                className="w-full transform transition-all duration-200 hover:scale-105 hover:shadow-lg"
              >
                <span className="text-2xl mr-3">🚚</span>
                <span className="text-lg font-medium">Delivery Order</span>
              </POSGlassButton>
            </>
          )}
        </div>
      </POSGlassModal>

      {/* Customer Search Modal */}
      <CustomerSearchModal
        isOpen={isCustomerSearchModalOpen}
        onClose={() => setIsCustomerSearchModalOpen(false)}
        onCustomerSelected={handleCustomerSelected}
        onAddNewCustomer={handleAddNewCustomer}
      />

      {/* Add Customer Modal */}
      <AddCustomerModal
        isOpen={isAddCustomerModalOpen}
        onClose={() => setIsAddCustomerModalOpen(false)}
        onCustomerAdded={handleCustomerAdded}
      />

      {/* Menu Modal */}
      {isMenuModalOpen && selectedOrderType && selectedCustomer && (
        <MenuModal
          isOpen={isMenuModalOpen}
          onClose={handleMenuModalClose}
          selectedCustomer={selectedCustomer}
          selectedAddress={selectedAddress}
          orderType={selectedOrderType}
          onOrderComplete={handleOrderComplete}
          isProcessingOrder={isProcessingOrder}
        />
      )}
    </div>
  );
});

OrderFlow.displayName = 'OrderFlow';

export default OrderFlow;