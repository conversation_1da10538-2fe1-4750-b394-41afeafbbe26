'use client';

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  fallbackSrc?: string;
  className?: string;
  containerClassName?: string;
  loadingClassName?: string;
  errorClassName?: string;
  onLoad?: () => void;
  onError?: () => void;
}

export const LazyImage = ({
  src,
  alt,
  fallbackSrc = '/images/placeholder.jpg',
  className = '',
  containerClassName = '',
  loadingClassName = '',
  errorClassName = '',
  onLoad,
  onError,
  ...props
}: LazyImageProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.();
  };

  const imageSrc = hasError ? fallbackSrc : src;

  return (
    <div
      ref={containerRef}
      className={cn(
        'relative overflow-hidden bg-gray-200 dark:bg-gray-800',
        containerClassName
      )}
    >
      {isInView && (
        <>
          {isLoading && (
            <div
              className={cn(
                'absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-900',
                loadingClassName
              )}
            >
              <div className="animate-pulse">
                <div className="w-8 h-8 bg-gray-300 dark:bg-gray-700 rounded-full animate-bounce"></div>
              </div>
            </div>
          )}
          
          <img
            ref={imgRef}
            src={imageSrc}
            alt={alt}
            onLoad={handleLoad}
            onError={handleError}
            className={cn(
              'transition-opacity duration-300',
              isLoading ? 'opacity-0' : 'opacity-100',
              hasError && errorClassName,
              className
            )}
            loading="lazy"
            {...props}
          />
          
          {hasError && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-900 text-gray-500 dark:text-gray-400">
              <div className="text-center">
                <span className="text-2xl mb-2 block">📷</span>
                <span className="text-sm">Image not available</span>
              </div>
            </div>
          )}
        </>
      )}
      
      {!isInView && (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse"></div>
      )}
    </div>
  );
};

export default LazyImage;
