/**
 * Staff Notifications Hook
 * Manages real-time notifications for staff members
 */

'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import type { StaffNotification, NotificationType, UserRole } from '../types/notifications';
import { supabase } from '@/lib/supabase';

interface UseStaffNotificationsReturn {
  notifications: StaffNotification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  clearAll: () => Promise<void>;
  playSound: (type: string) => void;
  toggleSound: (enabled: boolean) => void;
  refreshNotifications: () => Promise<void>;
}

/**
 * Hook for managing staff notifications
 * @param userId - The user ID
 * @param userRole - The user's role
 * @returns Staff notifications state and methods
 */
export const useStaffNotifications = (
  userId?: string,
  userRole?: UserRole
): UseStaffNotificationsReturn => {
  const [notifications, setNotifications] = useState<StaffNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const channelIdRef = useRef<string>(crypto.randomUUID());
  const channelRef = useRef<any>(null);

  // Initialize audio
  useEffect(() => {
    if (typeof window !== 'undefined') {
      audioRef.current = new Audio('/sounds/notification.mp3');
      audioRef.current.volume = 0.5;
    }
  }, []);

  /**
   * Fetch notifications from database
   */
  const fetchNotifications = useCallback(async () => {
    if (!userId || !userRole) {
      setLoading(false);
      return;
    }

    try {
      setError(null);
      
      const { data, error: fetchError } = await supabase
        .from('staff_notifications')
        .select('*')
        .contains('target_roles', [userRole])
        .order('created_at', { ascending: false })
        .limit(50);

      if (fetchError) {
        throw fetchError;
      }

      const formattedNotifications: StaffNotification[] = (data || []).map((item: any) => ({
        id: item.id,
        type: item.type as NotificationType,
        title: item.title,
        message: item.message,
        timestamp: item.created_at,
        read: item.read || false,
        priority: item.priority || 'normal',
        userId: item.user_id,
        orderId: item.order_id,
        actionRequired: item.action_required || false,
        expiresAt: item.expires_at,
        metadata: item.metadata || {},
        targetRoles: item.target_roles || [],
        department: item.department,
        soundAlert: item.sound_alert || false,
        autoExpire: item.auto_expire || false,
        orderDetails: item.order_details
      }));

      setNotifications(formattedNotifications);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch notifications');
    } finally {
      setLoading(false);
    }
  }, [userId, userRole]);

  /**
   * Mark notification as read
   */
  const markAsRead = useCallback(async (id: string) => {
    try {
      const { error: updateError } = await supabase
        .from('staff_notifications')
        .update({ read: true, read_at: new Date().toISOString() })
        .eq('id', id);

      if (updateError) {
        throw updateError;
      }

      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, read: true }
            : notification
        )
      );
    } catch (err) {
      console.error('Error marking notification as read:', err);
    }
  }, []);

  /**
   * Mark all notifications as read
   */
  const markAllAsRead = useCallback(async () => {
    if (!userId || !userRole) return;

    try {
      const unreadIds = notifications
        .filter(n => !n.read)
        .map(n => n.id);

      if (unreadIds.length === 0) return;

      const { error: updateError } = await supabase
        .from('staff_notifications')
        .update({ read: true, read_at: new Date().toISOString() })
        .in('id', unreadIds);

      if (updateError) {
        throw updateError;
      }

      setNotifications(prev => 
        prev.map(notification => ({ ...notification, read: true }))
      );
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
    }
  }, [notifications, userId, userRole]);

  /**
   * Delete a notification
   */
  const deleteNotification = useCallback(async (id: string) => {
    try {
      const { error: deleteError } = await supabase
        .from('staff_notifications')
        .delete()
        .eq('id', id);

      if (deleteError) {
        throw deleteError;
      }

      setNotifications(prev => prev.filter(n => n.id !== id));
    } catch (err) {
      console.error('Error deleting notification:', err);
    }
  }, []);

  /**
   * Clear all notifications
   */
  const clearAll = useCallback(async () => {
    if (!userId || !userRole) return;

    try {
      const notificationIds = notifications.map(n => n.id);
      
      if (notificationIds.length === 0) return;

      const { error: deleteError } = await supabase
        .from('staff_notifications')
        .delete()
        .in('id', notificationIds);

      if (deleteError) {
        throw deleteError;
      }

      setNotifications([]);
    } catch (err) {
      console.error('Error clearing notifications:', err);
    }
  }, [notifications, userId, userRole]);

  /**
   * Play notification sound
   */
  const playSound = useCallback((_type: string = 'notification') => {
    if (soundEnabled && audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.play().catch(err => {
        console.warn('Could not play notification sound:', err);
      });
    }
  }, [soundEnabled]);

  /**
   * Toggle sound notifications
   */
  const toggleSound = useCallback((enabled: boolean) => {
    setSoundEnabled(enabled);
    localStorage.setItem('staff-notifications-sound', enabled.toString());
  }, []);

  /**
   * Refresh notifications
   */
  const refreshNotifications = useCallback(async () => {
    setLoading(true);
    await fetchNotifications();
  }, [fetchNotifications]);

  // Load sound preference
  useEffect(() => {
    const savedSoundPref = localStorage.getItem('staff-notifications-sound');
    if (savedSoundPref !== null) {
      setSoundEnabled(savedSoundPref === 'true');
    }
  }, []);

  // Initial fetch
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Set up real-time subscription with enhanced cleanup
  useEffect(() => {
    if (!userId || !userRole) return;

    let isSubscribed = true;
    
    const setupSubscription = async () => {
      try {
        // Clean up existing channel
        if (channelRef.current) {
          try {
            await supabase.removeChannel(channelRef.current);
            console.log('Previous staff notification channel removed');
          } catch (error) {
            console.warn('Error removing previous channel:', error);
          }
        }

        // Only proceed if component is still mounted
        if (!isSubscribed) return;

        const uniqueId = channelIdRef.current;
        const channelName = `staff-notifications-${userId}-${userRole}-${uniqueId}`;
        
        console.log('Creating staff notification channel:', channelName);
        
        const channel = supabase
          .channel(channelName)
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'staff_notifications',
              filter: `target_roles.cs.{${userRole}}`
            },
            (payload: any) => {
              // Double-check subscription is still active
              if (!isSubscribed) return;
              
              const newNotification = payload.new as any;
              const formattedNotification: StaffNotification = {
                id: newNotification.id,
                type: newNotification.type,
                title: newNotification.title,
                message: newNotification.message,
                timestamp: newNotification.created_at,
                read: false,
                priority: newNotification.priority || 'normal',
                userId: newNotification.user_id,
                orderId: newNotification.order_id,
                actionRequired: newNotification.action_required || false,
                expiresAt: newNotification.expires_at,
                metadata: newNotification.metadata || {},
                targetRoles: newNotification.target_roles || [],
                department: newNotification.department,
                soundAlert: newNotification.sound_alert || false,
                autoExpire: newNotification.auto_expire || false,
                orderDetails: newNotification.order_details
              };

              setNotifications(prev => [formattedNotification, ...prev]);
              
              // Play sound for new notifications
              if (formattedNotification.soundAlert) {
                playSound('notification');
              }
            }
          )
          .subscribe();

        channelRef.current = channel;
        console.log('Staff notification subscription established');
      } catch (error) {
        console.error('Error setting up staff notification subscription:', error);
      }
    };

    setupSubscription();

    return () => {
      isSubscribed = false;
      
      if (channelRef.current) {
        const cleanup = async () => {
          try {
            await supabase.removeChannel(channelRef.current);
            console.log('Staff notification channel cleanup completed');
          } catch (error) {
            console.warn('Error during staff notification cleanup:', error);
          }
        };
        cleanup();
        channelRef.current = null;
      }
    };
  }, [userId, userRole, playSound]);

  const unreadCount = notifications.filter(n => !n.read).length;

  return {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
    playSound,
    toggleSound,
    refreshNotifications
  };
};