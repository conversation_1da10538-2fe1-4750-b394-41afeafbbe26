# Enhanced POS Manager Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Enhanced POS Manager system to production environments, including setup, configuration, monitoring, and maintenance procedures.

## 🏗️ System Requirements

### Minimum Requirements
- **CPU**: 2 cores, 2.4 GHz
- **RAM**: 4 GB
- **Storage**: 20 GB SSD
- **Network**: 100 Mbps internet connection
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+

### Recommended Requirements
- **CPU**: 4 cores, 3.0 GHz
- **RAM**: 8 GB
- **Storage**: 50 GB SSD
- **Network**: 1 Gbps internet connection
- **Load Balancer**: For high availability

### Software Dependencies
- **Node.js**: 18.0.0 or higher
- **npm**: 9.0.0 or higher
- **PostgreSQL**: 14.0 or higher
- **Redis**: 6.0 or higher (optional, for caching)
- **Nginx**: 1.20+ (for reverse proxy)

## 🚀 Deployment Options

### Option 1: Vercel Deployment (Recommended)

#### Prerequisites
- Vercel account
- GitHub repository
- Supabase project

#### Steps

1. **Prepare Repository**
   ```bash
   git clone <repository-url>
   cd admin-dashboard
   npm install
   npm run build
   ```

2. **Configure Environment Variables**
   Create `.env.production` file:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   NEXTAUTH_SECRET=your_nextauth_secret
   NEXTAUTH_URL=https://your-domain.vercel.app
   ```

3. **Deploy to Vercel**
   ```bash
   npm install -g vercel
   vercel login
   vercel --prod
   ```

4. **Configure Custom Domain**
   - Add custom domain in Vercel dashboard
   - Update DNS records
   - Configure SSL certificate

### Option 2: Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  pos-manager:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_SUPABASE_URL=${SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: pos_manager
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

#### Deployment Commands
```bash
docker-compose up -d
docker-compose logs -f pos-manager
```

### Option 3: Traditional Server Deployment

#### Server Setup (Ubuntu 20.04)

1. **Install Dependencies**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y

   # Install Node.js
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # Install PM2
   sudo npm install -g pm2

   # Install Nginx
   sudo apt install nginx -y
   ```

2. **Application Setup**
   ```bash
   # Clone repository
   git clone <repository-url> /var/www/pos-manager
   cd /var/www/pos-manager/admin-dashboard

   # Install dependencies
   npm ci --production

   # Build application
   npm run build

   # Set permissions
   sudo chown -R www-data:www-data /var/www/pos-manager
   ```

3. **PM2 Configuration**
   Create `ecosystem.config.js`:
   ```javascript
   module.exports = {
     apps: [{
       name: 'pos-manager',
       script: 'npm',
       args: 'start',
       cwd: '/var/www/pos-manager/admin-dashboard',
       instances: 'max',
       exec_mode: 'cluster',
       env: {
         NODE_ENV: 'production',
         PORT: 3000
       }
     }]
   }
   ```

   Start application:
   ```bash
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

4. **Nginx Configuration**
   Create `/etc/nginx/sites-available/pos-manager`:
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;

       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

   Enable site:
   ```bash
   sudo ln -s /etc/nginx/sites-available/pos-manager /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl restart nginx
   ```

5. **SSL Certificate (Let's Encrypt)**
   ```bash
   sudo apt install certbot python3-certbot-nginx
   sudo certbot --nginx -d your-domain.com
   ```

## 🗄️ Database Setup

### Supabase Setup (Recommended)

1. **Create Supabase Project**
   - Go to https://supabase.com
   - Create new project
   - Note down URL and API keys

2. **Database Schema**
   Run the following SQL in Supabase SQL editor:
   ```sql
   -- Create tables
   CREATE TABLE terminals (
     id TEXT PRIMARY KEY,
     name TEXT NOT NULL,
     status TEXT DEFAULT 'offline',
     last_heartbeat TIMESTAMP WITH TIME ZONE,
     health_score INTEGER DEFAULT 0,
     location TEXT,
     version TEXT,
     settings JSONB DEFAULT '{}',
     metrics JSONB DEFAULT '{}',
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   CREATE TABLE staff (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     first_name TEXT NOT NULL,
     last_name TEXT NOT NULL,
     email TEXT UNIQUE NOT NULL,
     role TEXT NOT NULL,
     status TEXT DEFAULT 'active',
     permissions TEXT[] DEFAULT '{}',
     schedule JSONB DEFAULT '{}',
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   CREATE TABLE menu_categories (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     name TEXT NOT NULL,
     description TEXT,
     type TEXT DEFAULT 'standard',
     display_order INTEGER DEFAULT 0,
     active BOOLEAN DEFAULT true,
     featured BOOLEAN DEFAULT false,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   CREATE TABLE pos_settings_sync_history (
     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
     terminal_id TEXT REFERENCES terminals(id),
     sync_status TEXT NOT NULL,
     sync_duration INTEGER,
     synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- Create indexes
   CREATE INDEX idx_terminals_status ON terminals(status);
   CREATE INDEX idx_staff_role ON staff(role);
   CREATE INDEX idx_menu_categories_active ON menu_categories(active);
   CREATE INDEX idx_sync_history_terminal ON pos_settings_sync_history(terminal_id);
   ```

3. **Row Level Security (RLS)**
   ```sql
   -- Enable RLS
   ALTER TABLE terminals ENABLE ROW LEVEL SECURITY;
   ALTER TABLE staff ENABLE ROW LEVEL SECURITY;
   ALTER TABLE menu_categories ENABLE ROW LEVEL SECURITY;
   ALTER TABLE pos_settings_sync_history ENABLE ROW LEVEL SECURITY;

   -- Create policies
   CREATE POLICY "Allow authenticated users to read terminals" ON terminals
     FOR SELECT USING (auth.role() = 'authenticated');

   CREATE POLICY "Allow authenticated users to manage staff" ON staff
     FOR ALL USING (auth.role() = 'authenticated');

   CREATE POLICY "Allow authenticated users to manage menu" ON menu_categories
     FOR ALL USING (auth.role() = 'authenticated');
   ```

### Self-Hosted PostgreSQL

1. **Install PostgreSQL**
   ```bash
   sudo apt install postgresql postgresql-contrib
   sudo systemctl start postgresql
   sudo systemctl enable postgresql
   ```

2. **Create Database and User**
   ```bash
   sudo -u postgres psql
   CREATE DATABASE pos_manager;
   CREATE USER pos_user WITH PASSWORD 'secure_password';
   GRANT ALL PRIVILEGES ON DATABASE pos_manager TO pos_user;
   \q
   ```

3. **Configure Connection**
   Update `postgresql.conf` and `pg_hba.conf` for remote connections if needed.

## 🔧 Configuration

### Environment Variables

#### Production Environment Variables
```env
# Application
NODE_ENV=production
PORT=3000
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-super-secret-key

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Database (if self-hosted)
DATABASE_URL=postgresql://pos_user:password@localhost:5432/pos_manager

# Redis (optional)
REDIS_URL=redis://localhost:6379

# Monitoring
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info

# Email (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### Security Configuration

1. **Firewall Setup**
   ```bash
   sudo ufw allow ssh
   sudo ufw allow 80
   sudo ufw allow 443
   sudo ufw enable
   ```

2. **SSL/TLS Configuration**
   - Use strong cipher suites
   - Enable HSTS headers
   - Configure secure cookies

3. **API Rate Limiting**
   ```javascript
   // In your API routes
   import rateLimit from 'express-rate-limit'

   const limiter = rateLimit({
     windowMs: 15 * 60 * 1000, // 15 minutes
     max: 100 // limit each IP to 100 requests per windowMs
   })
   ```

## 📊 Monitoring and Logging

### Application Monitoring

1. **PM2 Monitoring**
   ```bash
   pm2 monit
   pm2 logs pos-manager
   pm2 status
   ```

2. **System Monitoring**
   ```bash
   # Install monitoring tools
   sudo apt install htop iotop nethogs

   # Monitor resources
   htop
   df -h
   free -m
   ```

### Log Management

1. **Application Logs**
   ```bash
   # PM2 logs
   pm2 logs pos-manager --lines 100

   # Nginx logs
   sudo tail -f /var/log/nginx/access.log
   sudo tail -f /var/log/nginx/error.log
   ```

2. **Log Rotation**
   ```bash
   # Configure logrotate
   sudo nano /etc/logrotate.d/pos-manager
   ```

   ```
   /var/log/pos-manager/*.log {
       daily
       missingok
       rotate 52
       compress
       delaycompress
       notifempty
       create 644 www-data www-data
   }
   ```

### Health Checks

1. **Application Health Check**
   ```bash
   curl -f http://localhost:3000/api/health || exit 1
   ```

2. **Database Health Check**
   ```bash
   pg_isready -h localhost -p 5432 -U pos_user
   ```

## 🔄 Backup and Recovery

### Database Backup

1. **Automated Backup Script**
   ```bash
   #!/bin/bash
   # backup.sh
   DATE=$(date +%Y%m%d_%H%M%S)
   BACKUP_DIR="/var/backups/pos-manager"
   
   mkdir -p $BACKUP_DIR
   
   # Database backup
   pg_dump -h localhost -U pos_user pos_manager > $BACKUP_DIR/db_backup_$DATE.sql
   
   # Compress backup
   gzip $BACKUP_DIR/db_backup_$DATE.sql
   
   # Remove backups older than 30 days
   find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
   ```

2. **Schedule Backup**
   ```bash
   # Add to crontab
   crontab -e
   
   # Daily backup at 2 AM
   0 2 * * * /path/to/backup.sh
   ```

### Application Backup

1. **Code Backup**
   ```bash
   tar -czf /var/backups/pos-manager/app_backup_$(date +%Y%m%d).tar.gz /var/www/pos-manager
   ```

2. **Configuration Backup**
   ```bash
   cp /etc/nginx/sites-available/pos-manager /var/backups/pos-manager/
   cp ecosystem.config.js /var/backups/pos-manager/
   ```

## 🚨 Troubleshooting

### Common Issues

1. **Application Won't Start**
   ```bash
   # Check logs
   pm2 logs pos-manager
   
   # Check port availability
   sudo netstat -tlnp | grep :3000
   
   # Check environment variables
   pm2 env pos-manager
   ```

2. **Database Connection Issues**
   ```bash
   # Test connection
   psql -h localhost -U pos_user -d pos_manager
   
   # Check PostgreSQL status
   sudo systemctl status postgresql
   ```

3. **High Memory Usage**
   ```bash
   # Monitor memory
   free -m
   
   # Restart application
   pm2 restart pos-manager
   
   # Check for memory leaks
   pm2 monit
   ```

### Performance Optimization

1. **Database Optimization**
   ```sql
   -- Analyze query performance
   EXPLAIN ANALYZE SELECT * FROM terminals;
   
   -- Update statistics
   ANALYZE;
   
   -- Vacuum database
   VACUUM ANALYZE;
   ```

2. **Application Optimization**
   ```bash
   # Enable gzip compression in Nginx
   gzip on;
   gzip_types text/plain text/css application/json application/javascript;
   
   # Enable caching
   location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

## 📞 Support and Maintenance

### Regular Maintenance Tasks

1. **Weekly Tasks**
   - Review application logs
   - Check system resources
   - Verify backup integrity
   - Update security patches

2. **Monthly Tasks**
   - Database maintenance (VACUUM, ANALYZE)
   - Review performance metrics
   - Update dependencies
   - Security audit

3. **Quarterly Tasks**
   - Full system backup
   - Disaster recovery testing
   - Performance optimization
   - Security assessment

### Emergency Procedures

1. **Application Crash**
   ```bash
   pm2 restart pos-manager
   pm2 logs pos-manager --lines 50
   ```

2. **Database Issues**
   ```bash
   sudo systemctl restart postgresql
   pg_isready -h localhost -p 5432
   ```

3. **High Load**
   ```bash
   # Scale application
   pm2 scale pos-manager +2
   
   # Monitor resources
   htop
   iotop
   ```

---

**Last Updated**: January 2024
**Version**: 2.0.0
**Status**: Production Ready ✅
