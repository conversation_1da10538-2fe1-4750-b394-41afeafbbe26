#!/usr/bin/env node

// Remove All Menu Items - MCP Implementation
// Uses the Supabase MCP wrapper for better database operations

const supabase = require('./supabase-mcp-wrapper.js');

async function removeAllMenuItemsWithMCP() {
  console.log('🚀 Starting Menu Items Removal using MCP Tools\n');

  try {
    // Test connection first
    console.log('🔍 Testing MCP connection...');
    const connectionTest = await supabase.testConnection();
    if (!connectionTest) {
      console.error('❌ MCP connection failed. Please check your configuration.');
      return;
    }
    console.log('✅ MCP connection successful\n');

    // Step 1: Check current menu items
    console.log('📋 Checking existing menu items...');
    const checkQuery = `
      SELECT 
        id, 
        name_en, 
        name_el, 
        category_id,
        price,
        is_available,
        created_at
      FROM menu_items 
      ORDER BY created_at ASC
    `;

    const checkResult = await supabase.executeSQL(null, checkQuery);
    
    if (checkResult.error) {
      console.error('❌ Error checking menu items:', checkResult.error);
      return;
    }

    const menuItems = checkResult.data || [];
    
    if (menuItems.length === 0) {
      console.log('✅ No menu items found in the database. Nothing to delete.');
      return;
    }

    console.log(`📊 Found ${menuItems.length} menu items to delete:\n`);
    
    // Display menu items with details
    menuItems.forEach((item, index) => {
      const name = item.name_en || item.name_el || 'Unnamed';
      const price = item.price ? `€${item.price}` : 'No price';
      const status = item.is_available ? '✅ Available' : '❌ Unavailable';
      console.log(`${index + 1}. ${name} (${price}) - ${status}`);
      console.log(`   ID: ${item.id}`);
      console.log(`   Category ID: ${item.category_id}`);
      console.log(`   Created: ${new Date(item.created_at).toLocaleDateString()}`);
      console.log('');
    });

    // Step 2: Check for related data that might be affected
    console.log('🔍 Checking for related data dependencies...');
    
    const dependencyChecks = [
      {
        name: 'Menu Item Ingredients',
        query: 'SELECT COUNT(*) as count FROM menu_item_ingredients'
      },
      {
        name: 'Order Items',
        query: 'SELECT COUNT(*) as count FROM order_items'
      }
    ];

    for (const check of dependencyChecks) {
      const result = await supabase.executeSQL(null, check.query);
      if (result.data && result.data.length > 0) {
        const count = result.data[0].count;
        console.log(`   ${check.name}: ${count} records`);
        if (count > 0) {
          console.log(`   ⚠️  Warning: ${count} ${check.name.toLowerCase()} will be affected`);
        }
      }
    }

    console.log('');

    // Step 3: Delete menu items using MCP
    console.log('🗑️  Starting deletion process using MCP...');
    
    const deleteQuery = `
      DELETE FROM menu_items 
      WHERE id IS NOT NULL
      RETURNING id, name_en, name_el
    `;

    const deleteResult = await supabase.executeSQL(null, deleteQuery);
    
    if (deleteResult.error) {
      console.error('❌ Error deleting menu items:', deleteResult.error);
      
      // Try alternative deletion method
      console.log('🔄 Trying alternative deletion method...');
      const simpleDeleteQuery = 'DELETE FROM menu_items';
      const altResult = await supabase.executeSQL(null, simpleDeleteQuery);
      
      if (altResult.error) {
        console.error('❌ Alternative deletion also failed:', altResult.error);
        return;
      } else {
        console.log('✅ Alternative deletion successful');
      }
    } else {
      const deletedItems = deleteResult.data || [];
      console.log(`✅ Successfully deleted ${deletedItems.length} menu items`);
      
      if (deletedItems.length > 0) {
        console.log('\n📋 Deleted items:');
        deletedItems.forEach((item, index) => {
          const name = item.name_en || item.name_el || 'Unnamed';
          console.log(`   ${index + 1}. ${name} (ID: ${item.id})`);
        });
      }
    }

    // Step 4: Verify deletion
    console.log('\n🔍 Verifying deletion...');
    const verifyResult = await supabase.executeSQL(null, 'SELECT COUNT(*) as count FROM menu_items');
    
    if (verifyResult.data && verifyResult.data.length > 0) {
      const remainingCount = verifyResult.data[0].count;
      if (remainingCount === 0) {
        console.log('✅ Verification successful: All menu items have been deleted');
      } else {
        console.log(`⚠️  Warning: ${remainingCount} menu items still remain`);
      }
    }

    // Step 5: Summary and next steps
    console.log('\n📝 Summary:');
    console.log(`   - Original count: ${menuItems.length} menu items`);
    console.log('   - Deletion method: MCP SQL execution');
    console.log('   - Status: Complete');
    
    console.log('\n💡 What this accomplishes:');
    console.log('   ✓ Removes foreign key constraints preventing category deletion');
    console.log('   ✓ Allows deletion of "Breakfast" and other categories');
    console.log('   ✓ Preserves all categories, subcategories, and ingredients');
    console.log('   ✓ Uses MCP tools for reliable database operations');
    
    console.log('\n🔄 Next steps:');
    console.log('   1. Refresh your admin dashboard');
    console.log('   2. Try deleting the "Breakfast" category');
    console.log('   3. The deletion should now work without foreign key errors');
    console.log('   4. You can now manage categories freely');

    console.log('\n🎉 Menu items removal completed successfully using MCP tools!');

  } catch (error) {
    console.error('❌ Unexpected error during MCP operation:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('   - Check your Supabase configuration');
    console.log('   - Verify MCP tools are working: node supabase-mcp-wrapper.js');
    console.log('   - Check database permissions');
  }
}

// Run the function if called directly
if (require.main === module) {
  removeAllMenuItemsWithMCP();
}

module.exports = { removeAllMenuItemsWithMCP };