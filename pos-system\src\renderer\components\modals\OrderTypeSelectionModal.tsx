import React, { memo } from 'react';
import { useTheme } from '../../contexts/theme-context';

interface OrderTypeSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOrderTypeSelect: (type: 'pickup' | 'delivery') => void;
}

export const OrderTypeSelectionModal = memo<OrderTypeSelectionModalProps>(({
  isOpen,
  onClose,
  onOrderTypeSelect
}) => {
  const { resolvedTheme } = useTheme();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal Content */}
      <div className={`relative z-10 w-full max-w-md mx-4 rounded-2xl border backdrop-blur-xl ${
        resolvedTheme === 'light'
          ? 'bg-white/95 border-gray-200/60 shadow-2xl'
          : 'bg-gray-900/95 border-white/20 shadow-2xl shadow-black/50'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200/20">
          <h2 className={`text-xl font-bold ${
            resolvedTheme === 'light' ? 'text-gray-900' : 'text-white'
          }`}>
            Select Order Type
          </h2>
          <button
            onClick={onClose}
            className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
              resolvedTheme === 'light'
                ? 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                : 'hover:bg-white/10 text-gray-400 hover:text-white'
            }`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Pickup Option */}
          <button
            onClick={() => onOrderTypeSelect('pickup')}
            className={`w-full p-6 rounded-xl border-2 transition-all duration-200 text-left group ${
              resolvedTheme === 'light'
                ? 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 hover:border-blue-400 hover:shadow-lg'
                : 'bg-gradient-to-r from-blue-900/30 to-blue-800/30 border-blue-400/30 hover:border-blue-400/60 hover:shadow-lg hover:shadow-blue-400/20'
            }`}
          >
            <div className="flex items-center gap-4">
              <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                resolvedTheme === 'light'
                  ? 'bg-blue-500 text-white'
                  : 'bg-blue-600 text-blue-100'
              }`}>
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div>
                <h3 className={`text-lg font-semibold mb-1 ${
                  resolvedTheme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  Pickup Order
                </h3>
                <p className={`text-sm ${
                  resolvedTheme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  Customer collects order from store
                </p>
              </div>
            </div>
          </button>

          {/* Delivery Option */}
          <button
            onClick={() => onOrderTypeSelect('delivery')}
            className={`w-full p-6 rounded-xl border-2 transition-all duration-200 text-left group ${
              resolvedTheme === 'light'
                ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-200 hover:border-green-400 hover:shadow-lg'
                : 'bg-gradient-to-r from-green-900/30 to-green-800/30 border-green-400/30 hover:border-green-400/60 hover:shadow-lg hover:shadow-green-400/20'
            }`}
          >
            <div className="flex items-center gap-4">
              <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                resolvedTheme === 'light'
                  ? 'bg-green-500 text-white'
                  : 'bg-green-600 text-green-100'
              }`}>
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4" />
                </svg>
              </div>
              <div>
                <h3 className={`text-lg font-semibold mb-1 ${
                  resolvedTheme === 'light' ? 'text-gray-900' : 'text-white'
                }`}>
                  Delivery Order
                </h3>
                <p className={`text-sm ${
                  resolvedTheme === 'light' ? 'text-gray-600' : 'text-gray-300'
                }`}>
                  Order delivered to customer address
                </p>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
});

OrderTypeSelectionModal.displayName = 'OrderTypeSelectionModal'; 