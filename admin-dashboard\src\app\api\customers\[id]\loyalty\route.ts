import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: customerId } = await params;
    const supabase = createServerSupabaseClient()
    const body = await request.json()
    
    const {
      action, // 'award' or 'redeem'
      points,
      description,
      order_id
    } = body
    
    // Validation
    if (!action || !points || points <= 0) {
      return NextResponse.json(
        { error: 'Action and positive points amount are required' },
        { status: 400 }
      )
    }
    
    if (!['award', 'redeem'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be either "award" or "redeem"' },
        { status: 400 }
      )
    }
    
    // Get current loyalty points balance
    const { data: loyaltyAccount, error: loyaltyError } = await supabase
      .from('loyalty_points')
      .select('points_balance')
      .eq('user_id', customerId)
      .single()
    
    if (loyaltyError || !loyaltyAccount) {
      return NextResponse.json(
        { error: 'Customer loyalty account not found' },
        { status: 404 }
      )
    }
    
    const currentBalance = loyaltyAccount.points_balance
    
    // Check if redeeming and has sufficient balance
    if (action === 'redeem' && currentBalance < points) {
      return NextResponse.json(
        { error: 'Insufficient loyalty points balance' },
        { status: 400 }
      )
    }
    
    // Calculate new balance
    const newBalance = action === 'award' 
      ? currentBalance + points 
      : currentBalance - points
    
    // Start transaction
    const { error: updateError } = await supabase
      .from('loyalty_points')
      .update({
        points_balance: newBalance,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', customerId)
    
    if (updateError) {
      return NextResponse.json(
        { error: 'Failed to update loyalty points' },
        { status: 500 }
      )
    }
    
    // Create transaction record
    const transactionData = {
      customer_id: customerId,
      points_earned: action === 'award' ? points : 0,
      points_redeemed: action === 'redeem' ? points : 0,
      transaction_type: action === 'award' ? 'earned' : 'redeemed',
      description: description || `Points ${action}ed`,
      order_id: order_id || null
    }
    
    const { error: transactionError } = await supabase
      .from('loyalty_point_transactions')
      .insert(transactionData)
    
    if (transactionError) {
      // Don't fail the operation, but log the error
    }
    
    return NextResponse.json({
      data: {
        previous_balance: currentBalance,
        points_changed: action === 'award' ? points : -points,
        new_balance: newBalance,
        action,
        description: transactionData.description
      },
      message: `Successfully ${action}ed ${points} loyalty points`
    })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: customerId } = await params;
    const supabase = createServerSupabaseClient()
    
    // Get loyalty points balance
    const { data: loyaltyAccount, error: loyaltyError } = await supabase
      .from('loyalty_points')
      .select('*')
      .eq('user_id', customerId)
      .single()
    
    if (loyaltyError || !loyaltyAccount) {
      return NextResponse.json(
        { error: 'Customer loyalty account not found' },
        { status: 404 }
      )
    }
    
    // Get transaction history
    const { data: transactions, error: transactionsError } = await supabase
      .from('loyalty_point_transactions')
      .select('*')
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false })
      .limit(50)
    
    if (transactionsError) {
    }
    
    // Calculate statistics
    const totalEarned = transactions?.reduce((sum, t) => sum + (t.points_earned || 0), 0) || 0
    const totalRedeemed = transactions?.reduce((sum, t) => sum + (t.points_redeemed || 0), 0) || 0
    const recentTransactions = transactions?.slice(0, 10) || []
    
    return NextResponse.json({
      data: {
        loyalty_account: loyaltyAccount,
        statistics: {
          current_balance: loyaltyAccount.points_balance,
          total_earned: totalEarned,
          total_redeemed: totalRedeemed,
          total_transactions: transactions?.length || 0
        },
        recent_transactions: recentTransactions,
        all_transactions: transactions || []
      }
    })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 