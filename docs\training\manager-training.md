# Manager Training Course

## Course Overview

This comprehensive training course will teach restaurant managers how to effectively use The Small Creperie Admin Dashboard to manage all aspects of their operations.

**Duration**: 4-6 hours (can be completed over multiple sessions)
**Level**: Beginner to Intermediate
**Prerequisites**: Basic computer skills, restaurant management experience

## Learning Objectives

By the end of this course, you will be able to:
- Navigate the admin dashboard confidently
- Configure and manage all restaurant settings
- Set up and manage POS systems
- Create and update menu items across all platforms
- Manage staff accounts and permissions
- Monitor business analytics and performance
- Handle system administration tasks
- Troubleshoot common issues

## Course Structure

### Module 1: Introduction and Setup (45 minutes)

#### 1.1 System Overview
- Understanding the multi-platform architecture
- How the admin dashboard controls all systems
- Real-time synchronization concepts

#### 1.2 First Login and Navigation
**Hands-on Exercise**: Log into the admin dashboard
1. Open your browser and go to http://localhost:3001
2. Enter your admin credentials
3. Take a tour of the main navigation:
   - Dashboard (home page with analytics)
   - POS (point-of-sale configuration)
   - Web (customer web app settings)
   - App (mobile app settings)
   - Menu (centralized menu management)
   - Staff (employee management)
   - Branches (location management)
   - System (administrative controls)

#### 1.3 Dashboard Overview
**Key Features**:
- Real-time metrics (revenue, orders, customers)
- Sales trends and performance charts
- System health monitoring
- Quick access to common tasks

**Practice Activity**: 
- Identify today's key metrics
- Navigate between different time ranges (24h, 7d, 30d)
- Locate system health indicators

### Module 2: Restaurant Settings Management (60 minutes)

#### 2.1 Basic Restaurant Information
Navigate to **Settings** → **Restaurant Information**

**Configure**:
- Restaurant name and description
- Contact information (phone, email, address)
- Business hours for each day of the week
- Holiday hours and special events
- Social media links

**Hands-on Exercise**: Update your restaurant's basic information
```
Practice Scenario: Your restaurant is extending hours on weekends.
Task: Update Saturday and Sunday hours from 9 AM - 9 PM to 8 AM - 10 PM
```

#### 2.2 Tax Configuration
Navigate to **POS** → **Tax Settings**

**Configure**:
- Tax rates (percentage, fixed, or compound)
- Tax-inclusive vs. tax-exclusive pricing
- Multiple tax rates for different items
- Tax exemptions for special cases

**Hands-on Exercise**: Set up tax rates
```
Practice Scenario: Your location has a 8.5% sales tax and 2% local tax.
Task: Configure both tax rates and set them to apply automatically
```

#### 2.3 Payment Methods
Navigate to **POS** → **Payment Processing**

**Configure**:
- Credit/debit card processing
- Cash handling settings
- Digital payments (Apple Pay, Google Pay)
- Gift card system
- Split payment options

**Important**: Payment processor integration requires setup with your payment provider.

### Module 3: POS System Configuration (90 minutes)

#### 3.1 Terminal Management
Navigate to **POS** → **Terminal Management**

**Key Concepts**:
- Each POS terminal has a unique ID
- Terminals can have different configurations
- Real-time sync status monitoring

**Configure**:
- Add new terminals
- Assign terminals to specific locations
- Set terminal-specific settings
- Monitor sync status

**Hands-on Exercise**: Add a new POS terminal
```
Practice Scenario: You're adding a second register.
Task: 
1. Add terminal "REGISTER-02"
2. Assign it to the main counter
3. Copy settings from "REGISTER-01"
4. Verify sync status
```

#### 3.2 Display and Hardware Settings
Navigate to **POS** → **Display Settings** and **Hardware Config**

**Display Settings**:
- Screen brightness and timeout
- Touch sensitivity
- Display orientation
- Theme customization

**Hardware Configuration**:
- Receipt printers (network, USB, Bluetooth)
- Cash drawers
- Barcode scanners
- Card readers
- Kitchen display systems

**Hands-on Exercise**: Configure receipt printer
```
Practice Scenario: Setting up a network receipt printer.
Task:
1. Add printer with IP address *************
2. Set paper width to 80mm
3. Test print functionality
4. Configure automatic printing for orders
```

#### 3.3 Staff Permissions and Access
Navigate to **POS** → **Staff Access**

**Permission Levels**:
- **Cashier**: Basic sales operations
- **Supervisor**: Discounts, refunds, reports
- **Manager**: Full access, settings changes
- **Admin**: System administration

**Configure**:
- Set permission levels for each staff member
- POS PIN codes for secure access
- Manager override requirements
- Time clock integration

**Hands-on Exercise**: Set up staff permissions
```
Practice Scenario: New employee needs cashier access.
Task:
1. Create staff account for "John Smith"
2. Set role to "Cashier"
3. Generate POS PIN code
4. Set manager override for discounts over 10%
```

### Module 4: Menu Management (75 minutes)

#### 4.1 Menu Structure and Categories
Navigate to **Menu** → **Categories**

**Understanding Menu Hierarchy**:
- Categories (e.g., "Sweet Crepes", "Savory Crepes", "Beverages")
- Items within categories
- Modifiers and options
- Pricing tiers

**Hands-on Exercise**: Create a new menu category
```
Practice Scenario: Adding a new "Seasonal Specials" category.
Task:
1. Create new category "Seasonal Specials"
2. Set display order (show first)
3. Add category description
4. Set availability schedule (weekends only)
```

#### 4.2 Adding and Editing Menu Items
Navigate to **Menu** → **Items**

**Item Configuration**:
- Basic information (name, description, price)
- Category assignment
- Dietary information (vegetarian, gluten-free, etc.)
- Images and photos
- Availability settings
- Modifiers and customizations

**Hands-on Exercise**: Add a new menu item
```
Practice Scenario: Adding "Autumn Apple Cinnamon Crepe"
Task:
1. Create new item in "Seasonal Specials"
2. Set price at $12.95
3. Add description highlighting seasonal ingredients
4. Mark as vegetarian-friendly
5. Upload product image
6. Set availability for fall months only
```

#### 4.3 Multi-Platform Synchronization
**Understanding Sync Process**:
- Changes made in admin dashboard sync automatically
- Real-time updates to POS systems
- Customer apps update within minutes
- Web app reflects changes immediately

**Verification Process**:
1. Make changes in admin dashboard
2. Check POS system for updates
3. Verify customer web app shows changes
4. Test mobile app synchronization

**Hands-on Exercise**: Test menu synchronization
```
Practice Scenario: Temporarily mark an item as "Sold Out"
Task:
1. Mark "Classic Nutella Crepe" as unavailable
2. Verify it disappears from POS system
3. Check that customer web app shows "Sold Out"
4. Re-enable the item and verify sync
```

### Module 5: Staff Management (60 minutes)

#### 5.1 Creating Staff Accounts
Navigate to **Staff** → **Manage Staff**

**Account Setup**:
- Personal information (name, email, phone)
- Employment details (position, start date, wage)
- Contact and emergency information
- Profile photo

**Hands-on Exercise**: Create a staff account
```
Practice Scenario: Hiring a new server.
Task:
1. Add "Sarah Johnson" as new staff member
2. Set position as "Server"
3. Add contact information
4. Set start date as next Monday
```

#### 5.2 Role-Based Permissions
**Available Roles**:
- **Owner/Manager**: Full system access
- **Assistant Manager**: Most functions, limited system admin
- **Supervisor**: Staff management, advanced POS functions
- **Cashier**: Basic POS operations
- **Server**: Order taking, basic functions
- **Kitchen Staff**: Kitchen display, order management

**Permission Matrix**:
| Function | Cashier | Server | Supervisor | Manager |
|----------|---------|--------|------------|---------|
| Process Orders | ✅ | ✅ | ✅ | ✅ |
| Apply Discounts | ❌ | ❌ | ✅ | ✅ |
| Process Refunds | ❌ | ❌ | ✅ | ✅ |
| View Reports | ❌ | ❌ | ✅ | ✅ |
| Manage Staff | ❌ | ❌ | ✅ | ✅ |
| System Settings | ❌ | ❌ | ❌ | ✅ |

#### 5.3 Scheduling and Time Management
Navigate to **Staff** → **Scheduling**

**Features**:
- Weekly schedule creation
- Shift assignments
- Time clock integration
- Break and meal period tracking
- Overtime monitoring

**Hands-on Exercise**: Create a weekly schedule
```
Practice Scenario: Creating next week's staff schedule.
Task:
1. Assign morning shifts (8 AM - 2 PM)
2. Assign evening shifts (2 PM - 8 PM)
3. Ensure adequate coverage for peak hours
4. Assign weekend staff appropriately
```

### Module 6: Analytics and Reporting (60 minutes)

#### 6.1 Understanding Dashboard Metrics
Return to **Dashboard**

**Key Metrics**:
- **Daily Revenue**: Total sales for the current day
- **Orders**: Number of orders processed
- **Average Order Value**: Revenue divided by number of orders
- **Customer Count**: Unique customers served
- **Preparation Time**: Average time from order to completion
- **System Uptime**: Technical system availability

#### 6.2 Sales Analytics
**Sales Trends**:
- Daily, weekly, monthly revenue patterns
- Peak hours identification
- Seasonal variations
- Year-over-year comparisons

**Top Performing Items**:
- Best-selling menu items
- Revenue contribution analysis
- Profit margin analysis
- Seasonal performance

**Hands-on Exercise**: Analyze sales data
```
Practice Scenario: Identifying your best-performing items.
Task:
1. Review last month's top-selling items
2. Identify peak sales hours
3. Compare weekend vs. weekday performance
4. Note any seasonal trends
```

#### 6.3 Customer Analytics
**Customer Insights**:
- New vs. returning customers
- Customer loyalty program participation
- Order frequency patterns
- Preferred ordering methods (in-store, online, mobile)

#### 6.4 Staff Performance
**Performance Metrics**:
- Sales per staff member
- Order processing speed
- Customer satisfaction ratings
- Schedule adherence

### Module 7: System Administration (45 minutes)

#### 7.1 System Health Monitoring
Navigate to **System** → **Health Monitoring**

**Monitor**:
- Application status (Admin Dashboard, POS, Web, Mobile)
- Database connectivity
- Real-time sync status
- Error rates and performance metrics

#### 7.2 Feature Flags and Maintenance
Navigate to **System** → **Feature Flags**

**Feature Management**:
- Enable/disable new features
- Platform-specific feature control
- Beta testing capabilities
- Gradual feature rollouts

**Maintenance Mode**:
- Schedule maintenance windows
- Communicate with customers during downtime
- Platform-specific maintenance

#### 7.3 Backup and Security
Navigate to **System** → **Backups**

**Backup Management**:
- Automated daily backups
- Manual backup triggers
- Backup verification
- Restore procedures

**Security Settings**:
- User session management
- Access logging
- Security audit reports
- Password policies

### Module 8: Troubleshooting and Support (30 minutes)

#### 8.1 Common Issues and Solutions

**POS System Not Syncing**:
1. Check internet connection
2. Verify Supabase connectivity
3. Restart POS application
4. Check for system updates

**Menu Items Not Appearing**:
1. Verify item is marked as "Available"
2. Check category visibility settings
3. Confirm time-based availability
4. Review platform-specific settings

**Staff Cannot Access POS**:
1. Verify staff account is active
2. Check role permissions
3. Confirm POS PIN is correct
4. Review shift schedule assignments

#### 8.2 Getting Help
**Support Resources**:
- Built-in help documentation
- Video tutorials
- Support ticket system
- Emergency contact information

**Escalation Process**:
1. Check common issues guide
2. Review relevant documentation
3. Contact technical support
4. Provide system logs if requested

## Certification Requirements

To complete this training course:

### Knowledge Assessment (20 questions)
- System navigation and basic operations
- Settings configuration
- Staff and menu management
- Analytics interpretation
- Troubleshooting procedures

### Practical Demonstration
Complete these tasks under supervision:
1. Add a new menu item and verify sync across platforms
2. Create a staff account with appropriate permissions
3. Configure POS terminal settings
4. Generate and interpret a sales report
5. Handle a common troubleshooting scenario

### Ongoing Training
- Monthly system updates and new features
- Quarterly performance reviews
- Annual comprehensive refresher training

## Additional Resources

- [Quick Reference Guide](./quick-reference.md)
- [Video Tutorial Library](./video-tutorials.md)
- [Advanced Features Guide](./advanced-features.md)
- [Mobile Training Supplement](./mobile-training.md)

## Course Completion

Congratulations! You've completed the Manager Training Course. You should now feel confident managing your restaurant operations through the admin dashboard.

**Next Steps**:
1. Practice daily operations with the system
2. Train your staff using the [Staff Onboarding Guide](./staff-onboarding.md)
3. Explore advanced features as your confidence grows
4. Stay updated with system improvements and new features

**Remember**: The admin dashboard is designed to make your life easier. Don't hesitate to explore and experiment with features in a test environment before implementing changes during busy periods. 