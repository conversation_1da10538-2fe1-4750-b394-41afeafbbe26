// Enhanced Error Handling Utilities for POS System
import { ERROR_MESSAGES } from '../constants';

// Error types for better categorization
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NETWORK = 'NETWORK',
  DATABASE = 'DATABASE',
  BUSINESS_LOGIC = 'BUSINESS_LOGIC',
  SYSTEM = 'SYSTEM',
  UNKNOWN = 'UNKNOWN',
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

// Enhanced error interface
export interface POSError {
  type: ErrorType;
  severity: ErrorSeverity;
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  context?: {
    userId?: string;
    orderId?: string;
    action?: string;
    component?: string;
  };
  stack?: string;
}

// Error factory class
export class ErrorFactory {
  static create(
    type: ErrorType,
    severity: ErrorSeverity,
    code: string,
    message: string,
    details?: any,
    context?: POSError['context']
  ): POSError {
    return {
      type,
      severity,
      code,
      message,
      details,
      context,
      timestamp: new Date().toISOString(),
      stack: new Error().stack,
    };
  }

  static validation(message: string, details?: any): POSError {
    return this.create(
      ErrorType.VALIDATION,
      ErrorSeverity.MEDIUM,
      'VALIDATION_ERROR',
      message,
      details
    );
  }

  static authentication(message: string = ERROR_MESSAGES.INVALID_CREDENTIALS): POSError {
    return this.create(
      ErrorType.AUTHENTICATION,
      ErrorSeverity.HIGH,
      'AUTH_ERROR',
      message
    );
  }

  static network(message: string = ERROR_MESSAGES.NETWORK_ERROR): POSError {
    return this.create(
      ErrorType.NETWORK,
      ErrorSeverity.MEDIUM,
      'NETWORK_ERROR',
      message
    );
  }

  static database(message: string, details?: any): POSError {
    return this.create(
      ErrorType.DATABASE,
      ErrorSeverity.HIGH,
      'DATABASE_ERROR',
      message,
      details
    );
  }

  static businessLogic(message: string, details?: any): POSError {
    return this.create(
      ErrorType.BUSINESS_LOGIC,
      ErrorSeverity.MEDIUM,
      'BUSINESS_ERROR',
      message,
      details
    );
  }

  static system(message: string, details?: any): POSError {
    return this.create(
      ErrorType.SYSTEM,
      ErrorSeverity.CRITICAL,
      'SYSTEM_ERROR',
      message,
      details
    );
  }
}

// Error handler class
export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorLog: POSError[] = [];

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  // Handle and log errors
  handle(error: POSError | Error | unknown): POSError {
    let posError: POSError;

    if (this.isPOSError(error)) {
      posError = error;
    } else if (error instanceof Error) {
      posError = this.convertError(error);
    } else {
      posError = ErrorFactory.create(
        ErrorType.UNKNOWN,
        ErrorSeverity.MEDIUM,
        'UNKNOWN_ERROR',
        ERROR_MESSAGES.UNKNOWN_ERROR,
        error
      );
    }

    this.logError(posError);
    return posError;
  }

  // Convert standard Error to POSError
  private convertError(error: Error): POSError {
    // Detect error type based on message or properties
    let type = ErrorType.UNKNOWN;
    let severity = ErrorSeverity.MEDIUM;

    if (error.message.includes('network') || error.message.includes('fetch')) {
      type = ErrorType.NETWORK;
    } else if (error.message.includes('auth') || error.message.includes('unauthorized')) {
      type = ErrorType.AUTHENTICATION;
      severity = ErrorSeverity.HIGH;
    } else if (error.message.includes('validation') || error.message.includes('invalid')) {
      type = ErrorType.VALIDATION;
    } else if (error.message.includes('database') || error.message.includes('sql')) {
      type = ErrorType.DATABASE;
      severity = ErrorSeverity.HIGH;
    }

    return ErrorFactory.create(
      type,
      severity,
      'CONVERTED_ERROR',
      error.message,
      { originalError: error.name },
      undefined
    );
  }

  // Check if error is POSError
  private isPOSError(error: any): error is POSError {
    return error && typeof error === 'object' && 'type' in error && 'severity' in error;
  }

  // Log error to console and storage
  private logError(error: POSError): void {
    // Add to in-memory log
    this.errorLog.push(error);

    // Keep only last 100 errors in memory
    if (this.errorLog.length > 100) {
      this.errorLog = this.errorLog.slice(-100);
    }

    // Log to console based on severity
    const logMessage = `[${error.type}] ${error.message}`;
    
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        console.error('🚨 CRITICAL:', logMessage, error);
        break;
      case ErrorSeverity.HIGH:
        console.error('❌ ERROR:', logMessage, error);
        break;
      case ErrorSeverity.MEDIUM:
        console.warn('⚠️ WARNING:', logMessage, error);
        break;
      case ErrorSeverity.LOW:
        console.info('ℹ️ INFO:', logMessage, error);
        break;
    }

    // In production, you might want to send critical errors to a logging service
    if (error.severity === ErrorSeverity.CRITICAL) {
      this.reportCriticalError(error);
    }
  }

  // Report critical errors (placeholder for external logging service)
  private reportCriticalError(error: POSError): void {
    // TODO: Implement external error reporting
    // This could send errors to Sentry, LogRocket, or custom logging service
    console.error('Critical error reported:', error);
  }

  // Get recent errors for debugging
  getRecentErrors(count: number = 10): POSError[] {
    return this.errorLog.slice(-count);
  }

  // Clear error log
  clearErrors(): void {
    this.errorLog = [];
  }

  // Get user-friendly error message
  getUserMessage(error: POSError): string {
    switch (error.type) {
      case ErrorType.NETWORK:
        return 'Please check your internet connection and try again.';
      case ErrorType.AUTHENTICATION:
        return 'Please log in again to continue.';
      case ErrorType.VALIDATION:
        return error.message; // Validation messages are usually user-friendly
      case ErrorType.DATABASE:
        return 'There was a problem saving your data. Please try again.';
      case ErrorType.BUSINESS_LOGIC:
        return error.message; // Business logic messages are usually user-friendly
      default:
        return 'Something went wrong. Please try again or contact support.';
    }
  }
}

// Utility functions for common error scenarios
export const handleAsyncError = async <T>(
  operation: () => Promise<T>,
  context?: POSError['context']
): Promise<{ data?: T; error?: POSError }> => {
  try {
    const data = await operation();
    return { data };
  } catch (error) {
    const posError = ErrorHandler.getInstance().handle(error);
    if (context) {
      posError.context = { ...posError.context, ...context };
    }
    return { error: posError };
  }
};

export const handleSyncError = (error: unknown, operation: string): POSError => {
  const posError = ErrorHandler.getInstance().handle(error);
  posError.context = { ...posError.context, action: operation };
  return posError;
};

export const handleValidationError = (field: string, message: string): POSError => {
  return ErrorFactory.validation(`${field}: ${message}`, { field });
};

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();
