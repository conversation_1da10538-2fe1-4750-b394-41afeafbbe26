'use client'

import React, { useState, useEffect } from 'react'
import { X, Save } from 'lucide-react'

interface IngredientModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (ingredient: any) => void
  ingredient?: any
  categories?: any[]
}

const IngredientModal = ({
  isOpen,
  onClose,
  onSave,
  ingredient,
  categories = []
}: IngredientModalProps) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: 0,
    pickup_price: 0,
    delivery_price: 0,
    stock_quantity: 0,
    min_stock_level: 0,
    unit: 'unit',
    category_id: '',
    allergens: [] as string[],
    is_available: true,
    display_order: 0
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (ingredient) {
      setFormData({
        name: ingredient.name || '',
        description: ingredient.description || '',
        price: ingredient.price || 0,
        pickup_price: ingredient.pickup_price || ingredient.price || 0,
        delivery_price: ingredient.delivery_price || ingredient.price || 0,
        stock_quantity: ingredient.stock_quantity || 0,
        min_stock_level: ingredient.min_stock_level || 0,
        unit: ingredient.unit || 'unit',
        category_id: ingredient.category_id || '',
        allergens: ingredient.allergens || [],
        is_available: ingredient.is_available ?? true,
        display_order: ingredient.display_order || 0
      })
    } else {
      setFormData({
        name: '',
        description: '',
        price: 0,
        pickup_price: 0,
        delivery_price: 0,
        stock_quantity: 0,
        min_stock_level: 0,
        unit: 'unit',
        category_id: categories[0]?.id || '',
        allergens: [],
        is_available: true,
        display_order: 0
      })
    }
    setErrors({})
  }, [ingredient, isOpen, categories])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    const newErrors: Record<string, string> = {}
    
    if (!formData.name.trim()) {
      newErrors.name = 'Ingredient name is required'
    }
    
    if (formData.name.length > 100) {
      newErrors.name = 'Ingredient name must be less than 100 characters'
    }

    if (formData.price < 0) {
      newErrors.price = 'Price must be non-negative'
    }

    if (formData.pickup_price < 0) {
      newErrors.pickup_price = 'Pickup price must be non-negative'
    }

    if (formData.delivery_price < 0) {
      newErrors.delivery_price = 'Delivery price must be non-negative'
    }

    if (formData.stock_quantity < 0) {
      newErrors.stock_quantity = 'Stock quantity must be non-negative'
    }

    if (formData.min_stock_level < 0) {
      newErrors.min_stock_level = 'Min stock level must be non-negative'
    }

    if (!formData.category_id) {
      newErrors.category_id = 'Please select a category'
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    onSave({
      ...formData,
      id: ingredient?.id
    })
    onClose()
  }

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const handleAllergenToggle = (allergen: string) => {
    setFormData(prev => ({
      ...prev,
      allergens: prev.allergens.includes(allergen)
        ? prev.allergens.filter(a => a !== allergen)
        : [...prev.allergens, allergen]
    }))
  }

  const commonAllergens = [
    'Gluten', 'Dairy', 'Eggs', 'Nuts', 'Peanuts', 'Soy', 'Sesame', 'Shellfish'
  ]

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" data-cy="ingredient-modal">
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            {ingredient ? 'Edit Ingredient' : 'Add Ingredient'}
          </h2>
          <button
            onClick={onClose}
            data-cy="modal-close"
            aria-label="Close modal"
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Name *
              </label>
              <input
                type="text"
                id="name"
                data-cy="ingredient-name-input"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.name
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                    : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700'
                } text-gray-900 dark:text-white`}
                placeholder="Enter ingredient name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400" data-cy="validation-error">
                  {errors.name}
                </p>
              )}
            </div>

            <div>
              <label htmlFor="category_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Category *
              </label>
              <select
                id="category_id"
                data-cy="ingredient-category-select"
                value={formData.category_id}
                onChange={(e) => handleChange('category_id', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.category_id
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                    : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700'
                } text-gray-900 dark:text-white`}
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.category_id && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400" data-cy="validation-error">
                  {errors.category_id}
                </p>
              )}
            </div>
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <textarea
              id="description"
              data-cy="ingredient-description-input"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="Enter ingredient description"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="pickup_price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Pickup Price * 🏪
              </label>
              <input
                type="number"
                id="pickup_price"
                data-cy="ingredient-pickup-price-input"
                value={formData.pickup_price}
                onChange={(e) => handleChange('pickup_price', parseFloat(e.target.value) || 0)}
                step="0.01"
                min="0"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.pickup_price
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                    : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700'
                } text-gray-900 dark:text-white`}
                placeholder="0.00"
              />
              {errors.pickup_price && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400" data-cy="validation-error">
                  {errors.pickup_price}
                </p>
              )}
            </div>

            <div>
              <label htmlFor="delivery_price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Delivery Price * 🚚
              </label>
              <input
                type="number"
                id="delivery_price"
                data-cy="ingredient-delivery-price-input"
                value={formData.delivery_price}
                onChange={(e) => handleChange('delivery_price', parseFloat(e.target.value) || 0)}
                step="0.01"
                min="0"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.delivery_price
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                    : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700'
                } text-gray-900 dark:text-white`}
                placeholder="0.00"
              />
              {errors.delivery_price && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400" data-cy="validation-error">
                  {errors.delivery_price}
                </p>
              )}
            </div>

            <div>
              <label htmlFor="price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Base Price (Legacy)
              </label>
              <input
                type="number"
                id="price"
                data-cy="ingredient-price-input"
                value={formData.price}
                onChange={(e) => {
                  const newPrice = parseFloat(e.target.value) || 0;
                  handleChange('price', newPrice);
                  // Auto-sync with pickup/delivery if they're empty
                  if (formData.pickup_price === 0) handleChange('pickup_price', newPrice);
                  if (formData.delivery_price === 0) handleChange('delivery_price', newPrice);
                }}
                step="0.01"
                min="0"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.price
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                    : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700'
                } text-gray-900 dark:text-white`}
                placeholder="0.00"
              />
              {errors.price && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400" data-cy="validation-error">
                  {errors.price}
                </p>
              )}
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Auto-fills pickup/delivery if empty
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

            <div>
              <label htmlFor="stock_quantity" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Stock Quantity *
              </label>
              <input
                type="number"
                id="stock_quantity"
                data-cy="ingredient-stock-input"
                value={formData.stock_quantity}
                onChange={(e) => handleChange('stock_quantity', parseInt(e.target.value) || 0)}
                min="0"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.stock_quantity
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                    : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700'
                } text-gray-900 dark:text-white`}
                placeholder="0"
              />
              {errors.stock_quantity && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400" data-cy="validation-error">
                  {errors.stock_quantity}
                </p>
              )}
            </div>

            <div>
              <label htmlFor="min_stock_level" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Min Stock Level *
              </label>
              <input
                type="number"
                id="min_stock_level"
                data-cy="ingredient-min-stock-input"
                value={formData.min_stock_level}
                onChange={(e) => handleChange('min_stock_level', parseInt(e.target.value) || 0)}
                min="0"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.min_stock_level
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                    : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700'
                } text-gray-900 dark:text-white`}
                placeholder="0"
              />
              {errors.min_stock_level && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400" data-cy="validation-error">
                  {errors.min_stock_level}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="unit" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Unit
              </label>
              <select
                id="unit"
                data-cy="ingredient-unit-select"
                value={formData.unit}
                onChange={(e) => handleChange('unit', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="unit">Unit</option>
                <option value="kg">Kilogram</option>
                <option value="g">Gram</option>
                <option value="l">Liter</option>
                <option value="ml">Milliliter</option>
                <option value="cup">Cup</option>
                <option value="tbsp">Tablespoon</option>
                <option value="tsp">Teaspoon</option>
              </select>
            </div>

            <div>
              <label htmlFor="display_order" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Display Order
              </label>
              <input
                type="number"
                id="display_order"
                data-cy="ingredient-order-input"
                value={formData.display_order}
                onChange={(e) => handleChange('display_order', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="0"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Allergens
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {commonAllergens.map(allergen => (
                <label key={allergen} className="flex items-center">
                  <input
                    type="checkbox"
                    data-cy={`allergen-${allergen.toLowerCase()}-checkbox`}
                    checked={formData.allergens.includes(allergen)}
                    onChange={() => handleAllergenToggle(allergen)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    {allergen}
                  </span>
                </label>
              ))}
            </div>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_available"
              data-cy="ingredient-available-checkbox"
              checked={formData.is_available}
              onChange={(e) => handleChange('is_available', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_available" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Available
            </label>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              data-cy="cancel-button"
              className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              data-cy="save-button"
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
            >
              <Save className="h-4 w-4 mr-2" />
              {ingredient ? 'Update' : 'Save'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default IngredientModal