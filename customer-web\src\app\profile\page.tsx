import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { GlassCard, GlassButton, GlassInput } from '@/components/ui/glass-components';
import { Breadcrumb } from '@/components/navigation/breadcrumb';

export const metadata: Metadata = {
  title: 'My Profile | Delicious Crepes & Waffles',
  description: 'Manage your profile, addresses, and preferences.',
};

export default function ProfilePage() {
  // Mock user data - in a real app, this would come from the AuthProvider
  const user = {
    id: 'user123',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+30 ************',
    avatar: '/images/avatar.jpg',
    addresses: [
      {
        id: 'addr1',
        name: 'Home',
        address: '123 Main St, Apt 4B',
        city: 'Athens',
        postalCode: '12345',
        isDefault: true,
      },
      {
        id: 'addr2',
        name: 'Work',
        address: '456 Office Blvd, Floor 3',
        city: 'Athens',
        postalCode: '12346',
        isDefault: false,
      },
    ],

    preferences: {
      language: 'en',
      theme: 'light',
      notifications: {
        email: true,
        push: true,
        sms: false,
      },
    },
  };

  return (
    <main className="min-h-screen py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <Breadcrumb className="mb-6" />
        <h1 className="text-3xl font-bold mb-8">My Profile</h1>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation - Takes up 1/4 of the space on large screens */}
          <div className="lg:col-span-1">
            <GlassCard>
              <div className="p-6">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="relative h-16 w-16 rounded-full overflow-hidden">
                    <Image src={user.avatar} alt={user.name} fill className="object-cover" />
                  </div>
                  <div>
                    <h2 className="font-semibold">{user.name}</h2>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                  </div>
                </div>

                <nav className="space-y-2">
                  <Link
                    href="/profile"
                    className="block p-2 rounded-md bg-primary/10 text-primary font-medium"
                  >
                    Personal Information
                  </Link>
                  <Link
                    href="/profile/addresses"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Addresses
                  </Link>
                  <Link
                    href="/profile/payment-methods"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Payment Methods
                  </Link>
                  <Link
                    href="/profile/favorites"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Favorites
                  </Link>
                  <Link
                    href="/profile/settings"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Settings
                  </Link>
                  <Link
                    href="/orders"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Order History
                  </Link>
                </nav>
              </div>
            </GlassCard>
          </div>

          {/* Main Content - Takes up 3/4 of the space on large screens */}
          <div className="lg:col-span-3 space-y-8">
            {/* Personal Information */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-6">Personal Information</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <GlassInput
                    label="Full Name"
                    defaultValue={user.name}
                    placeholder="Your full name"
                  />

                  <GlassInput
                    label="Email Address"
                    defaultValue={user.email}
                    placeholder="Your email address"
                    type="email"
                    disabled
                  />

                  <GlassInput
                    label="Phone Number"
                    defaultValue={user.phone}
                    placeholder="Your phone number"
                    type="tel"
                  />

                  <div className="md:col-span-2">
                    <h3 className="font-medium mb-2">Profile Picture</h3>
                    <div className="flex items-center space-x-4">
                      <div className="relative h-24 w-24 rounded-full overflow-hidden">
                        <Image src={user.avatar} alt={user.name} fill className="object-cover" />
                      </div>
                      <div>
                        <GlassButton variant="secondary" size="sm">
                          Change Picture
                        </GlassButton>
                        <p className="text-sm text-muted-foreground mt-2">
                          Recommended: Square image, at least 200x200 pixels
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end">
                  <GlassButton variant="primary">Save Changes</GlassButton>
                </div>
              </div>
            </GlassCard>

            {/* Default Address */}
            <GlassCard>
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold">Default Address</h2>
                  <Link href="/profile/addresses">
                    <GlassButton variant="secondary" size="sm">
                      Manage Addresses
                    </GlassButton>
                  </Link>
                </div>

                {user.addresses
                  .filter(addr => addr.isDefault)
                  .map(address => (
                    <div key={address.id} className="border border-input rounded-lg p-4">
                      <div className="flex justify-between">
                        <h3 className="font-medium">{address.name}</h3>
                        <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                          Default
                        </span>
                      </div>
                      <p className="text-muted-foreground mt-2">
                        {address.address}, {address.city}, {address.postalCode}
                      </p>
                    </div>
                  ))}
              </div>
            </GlassCard>

            {/* Preferences */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-6">Preferences</h2>

                <div className="space-y-6">
                  <div>
                    <h3 className="font-medium mb-3">Language</h3>
                    <div className="flex space-x-4">
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="language"
                          className="h-4 w-4 text-primary"
                          defaultChecked={user.preferences.language === 'en'}
                        />

                        <span>English</span>
                      </label>

                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="language"
                          className="h-4 w-4 text-primary"
                          defaultChecked={user.preferences.language === 'el'}
                        />

                        <span>Greek</span>
                      </label>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-3">Theme</h3>
                    <div className="flex space-x-4">
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="theme"
                          className="h-4 w-4 text-primary"
                          defaultChecked={user.preferences.theme === 'light'}
                        />

                        <span>Light</span>
                      </label>

                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="theme"
                          className="h-4 w-4 text-primary"
                          defaultChecked={user.preferences.theme === 'dark'}
                        />

                        <span>Dark</span>
                      </label>

                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="theme"
                          className="h-4 w-4 text-primary"
                          defaultChecked={user.preferences.theme === 'system'}
                        />

                        <span>System</span>
                      </label>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-3">Notifications</h3>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-primary rounded"
                          defaultChecked={user.preferences.notifications.email}
                        />

                        <span>Email Notifications</span>
                      </label>

                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-primary rounded"
                          defaultChecked={user.preferences.notifications.push}
                        />

                        <span>Push Notifications</span>
                      </label>

                      <label className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-primary rounded"
                          defaultChecked={user.preferences.notifications.sms}
                        />

                        <span>SMS Notifications</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="mt-6 flex justify-end">
                  <GlassButton variant="primary">Save Preferences</GlassButton>
                </div>
              </div>
            </GlassCard>

            {/* Account Security */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-6">Account Security</h2>

                <div className="space-y-6">
                  <div>
                    <h3 className="font-medium mb-3">Change Password</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <GlassInput
                        label="Current Password"
                        type="password"
                        placeholder="Enter your current password"
                      />

                      <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <GlassInput
                          label="New Password"
                          type="password"
                          placeholder="Enter new password"
                        />

                        <GlassInput
                          label="Confirm New Password"
                          type="password"
                          placeholder="Confirm new password"
                        />
                      </div>
                    </div>

                    <div className="mt-4 flex justify-end">
                      <GlassButton variant="primary">Update Password</GlassButton>
                    </div>
                  </div>
                </div>
              </div>
            </GlassCard>

            {/* Delete Account */}
            <GlassCard className="border border-red-200 dark:border-red-900">
              <div className="p-6">
                <h2 className="text-xl font-semibold text-red-600 dark:text-red-400 mb-2">
                  Delete Account
                </h2>
                <p className="text-muted-foreground mb-4">
                  Once you delete your account, there is no going back. Please be certain.
                </p>
                <GlassButton variant="danger">Delete Account</GlassButton>
              </div>
            </GlassCard>
          </div>
        </div>
      </div>
    </main>
  );
}
