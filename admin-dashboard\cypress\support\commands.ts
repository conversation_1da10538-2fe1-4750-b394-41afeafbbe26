/// <reference types="cypress" />

// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Authentication Commands
Cypress.Commands.add('loginAsAdmin', () => {
  cy.session('admin-session', () => {
    cy.visit('/auth/signin');
    
    // Wait for the page to load
    cy.get('[data-testid="signin-form"]', { timeout: 10000 }).should('be.visible');
    
    // Fill in credentials
    cy.get('[data-testid="email-input"]')
      .type(Cypress.env('TEST_USER_EMAIL'));
    
    cy.get('[data-testid="password-input"]')
      .type(Cypress.env('TEST_USER_PASSWORD'));
    
    // Submit form
    cy.get('[data-testid="signin-button"]').click();
    
    // Wait for redirect to dashboard
    cy.url().should('include', '/dashboard');
    cy.get('[data-testid="dashboard-header"]').should('be.visible');
  });
});

// Data seeding command
Cypress.Commands.add('seedTestData', () => {
  cy.task('seedDatabase');
});

// Page load waiting command
Cypress.Commands.add('waitForPageLoad', () => {
  // Wait for main content to be visible
  cy.get('main', { timeout: 10000 }).should('be.visible');
  
  // Wait for any loading spinners to disappear
  cy.get('[data-testid="loading-spinner"]').should('not.exist');
  
  // Wait for network requests to complete
  cy.wait(500); // Small buffer for any remaining async operations
});

// Accessibility testing command
Cypress.Commands.add('checkA11y', () => {
  // Check for basic accessibility issues
  cy.get('img').each(($img) => {
    cy.wrap($img).should('have.attr', 'alt');
  });
  
  // Check for proper heading hierarchy
  cy.get('h1').should('have.length.at.most', 1);
  
  // Check for form labels
  cy.get('input[type="text"], input[type="email"], input[type="password"], textarea, select')
    .each(($input) => {
      const id = $input.attr('id');
      if (id) {
        cy.get(`label[for="${id}"]`).should('exist');
      } else {
        cy.wrap($input).should('have.attr', 'aria-label');
      }
    });
  
  // Check for proper button text or aria-labels
  cy.get('button').each(($button) => {
    const text = $button.text().trim();
    const ariaLabel = $button.attr('aria-label');
    expect(text || ariaLabel).to.not.be.empty;
  });
});

// Responsive design testing command
Cypress.Commands.add('testResponsive', () => {
  const viewports = [
    { width: 375, height: 667, name: 'mobile' },
    { width: 768, height: 1024, name: 'tablet' },
    { width: 1280, height: 720, name: 'desktop' },
    { width: 1920, height: 1080, name: 'large-desktop' }
  ];
  
  viewports.forEach((viewport) => {
    cy.viewport(viewport.width, viewport.height);
    cy.wait(500); // Allow time for responsive changes
    
    // Check that main content is visible
    cy.get('main').should('be.visible');
    
    // Check that navigation is accessible
    if (viewport.width < 768) {
      // Mobile: check for hamburger menu
      cy.get('[data-testid="mobile-menu-button"]').should('be.visible');
    } else {
      // Desktop: check for full navigation
      cy.get('[data-testid="desktop-navigation"]').should('be.visible');
    }
    
    // Take screenshot for visual regression testing
    cy.screenshot(`responsive-${viewport.name}`);
  });
  
  // Reset to default viewport
  cy.viewport(1280, 720);
});

// Custom command for form testing
Cypress.Commands.add('fillForm', (formData: Record<string, string>) => {
  Object.entries(formData).forEach(([field, value]) => {
    cy.get(`[data-testid="${field}-input"]`)
      .clear()
      .type(value);
  });
});

// Custom command for table testing
Cypress.Commands.add('checkTableData', (expectedData: string[][]) => {
  expectedData.forEach((rowData, rowIndex) => {
    rowData.forEach((cellData, cellIndex) => {
      cy.get(`[data-testid="table-row-${rowIndex}"] [data-testid="table-cell-${cellIndex}"]`)
        .should('contain.text', cellData);
    });
  });
});

// Custom command for API testing
Cypress.Commands.add('apiRequest', (method: string, url: string, body?: any) => {
  return cy.request({
    method,
    url: `${Cypress.config('baseUrl')}${url}`,
    body,
    headers: {
      'Content-Type': 'application/json'
    },
    failOnStatusCode: false
  });
});

// Custom command for waiting for specific API calls
Cypress.Commands.add('waitForApi', (alias: string, timeout = 10000) => {
  cy.wait(alias, { timeout });
});

// Custom command for testing modals
Cypress.Commands.add('openModal', (triggerSelector: string) => {
  cy.get(triggerSelector).click();
  cy.get('[data-testid="modal"]').should('be.visible');
  cy.get('[data-testid="modal-backdrop"]').should('be.visible');
});

Cypress.Commands.add('closeModal', () => {
  cy.get('[data-testid="modal-close"]').click();
  cy.get('[data-testid="modal"]').should('not.exist');
});

// Custom command for testing notifications
Cypress.Commands.add('checkNotification', (message: string, type = 'success') => {
  cy.get(`[data-testid="notification-${type}"]`)
    .should('be.visible')
    .and('contain.text', message);
});

// Custom command for testing loading states
Cypress.Commands.add('checkLoadingState', (selector: string) => {
  cy.get(selector).should('be.visible');
  cy.get('[data-testid="loading-spinner"]').should('be.visible');
  cy.get('[data-testid="loading-spinner"]').should('not.exist');
  cy.get(selector).should('be.visible');
});

// Custom command for testing error states
Cypress.Commands.add('checkErrorState', (errorMessage: string) => {
  cy.get('[data-testid="error-message"]')
    .should('be.visible')
    .and('contain.text', errorMessage);
});

// Custom command for testing pagination
Cypress.Commands.add('testPagination', (totalPages: number) => {
  // Test next page
  if (totalPages > 1) {
    cy.get('[data-testid="pagination-next"]').click();
    cy.url().should('include', 'page=2');
    
    // Test previous page
    cy.get('[data-testid="pagination-prev"]').click();
    cy.url().should('include', 'page=1');
    
    // Test specific page
    if (totalPages > 2) {
      cy.get('[data-testid="pagination-page-3"]').click();
      cy.url().should('include', 'page=3');
    }
  }
});

// Custom command for testing search functionality
Cypress.Commands.add('testSearch', (searchTerm: string, expectedResults: number) => {
  cy.get('[data-testid="search-input"]')
    .clear()
    .type(searchTerm);
  
  cy.get('[data-testid="search-button"]').click();
  
  cy.get('[data-testid="search-results"]')
    .should('be.visible');
  
  if (expectedResults > 0) {
    cy.get('[data-testid="search-result-item"]')
      .should('have.length', expectedResults);
  } else {
    cy.get('[data-testid="no-results"]')
      .should('be.visible');
  }
});

// Custom command for testing file uploads
Cypress.Commands.add('uploadFile', (selector: string, fileName: string, fileType = 'image/jpeg') => {
  cy.get(selector).selectFile({
    contents: Cypress.Buffer.from('file contents'),
    fileName,
    mimeType: fileType
  });
});

// Extend Cypress namespace with custom commands
declare global {
  namespace Cypress {
    interface Chainable {
      fillForm(formData: Record<string, string>): Chainable<void>;
      checkTableData(expectedData: string[][]): Chainable<void>;
      apiRequest(method: string, url: string, body?: any): Chainable<any>;
      waitForApi(alias: string, timeout?: number): Chainable<void>;
      openModal(triggerSelector: string): Chainable<void>;
      closeModal(): Chainable<void>;
      checkNotification(message: string, type?: string): Chainable<void>;
      checkLoadingState(selector: string): Chainable<void>;
      checkErrorState(errorMessage: string): Chainable<void>;
      testPagination(totalPages: number): Chainable<void>;
      testSearch(searchTerm: string, expectedResults: number): Chainable<void>;
      uploadFile(selector: string, fileName: string, fileType?: string): Chainable<void>;
    }
  }
}