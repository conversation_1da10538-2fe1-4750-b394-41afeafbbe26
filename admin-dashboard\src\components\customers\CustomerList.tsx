'use client'

import React from 'react'

import { But<PERSON> } from '@/components/ui/button'
import { GlassCard } from '@/components/ui/glass-components'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Star, 
  Gift, 
  Mail,
  Phone,
  MapPin,
  Calendar,
  ShoppingBag,
  Crown,
  TrendingUp,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'

interface CustomerAddress {
  id: string
  customer_id: string
  address_type: 'home' | 'work' | 'other'
  street_address: string
  postal_code?: string
  city?: string
  is_default: boolean
  created_at: string
  updated_at: string
}

interface Customer {
  id: string
  name: string
  ringer_name?: string
  phone: string
  email?: string
  address?: string
  postal_code?: string
  notes?: string
  loyalty_points?: number
  total_orders?: number
  created_at: string
  updated_at: string
  addresses?: CustomerAddress[]
  is_vip?: boolean
}

interface CustomerListProps {
  customers: Customer[]
  loading: boolean
  onViewCustomer: (customer: Customer) => void
  onEditCustomer: (customer: Customer) => void
  onDeleteCustomer: (customer: Customer) => void
}

export function CustomerList({ 
  customers, 
  loading, 
  onViewCustomer, 
  onEditCustomer, 
  onDeleteCustomer 
}: CustomerListProps) {
  if (loading) {
    return (
      <GlassCard className="p-6">
        <div className="flex items-center justify-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        </div>
      </GlassCard>
    )
  }

  if (customers.length === 0) {
    return (
      <GlassCard className="p-6">
        <div className="text-center py-8">
          <Users className="h-12 w-12 text-white/40 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white/80 mb-2">No customers found</h3>
          <p className="text-white/60">Get started by adding your first customer.</p>
        </div>
      </GlassCard>
    )
  }

  return (
    <div className="space-y-4">
      {customers.map((customer) => (
        <GlassCard key={customer.id} className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-3">
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-semibold text-white">
                    {customer.name}
                  </h3>
                  {customer.is_vip && (
                    <Crown className="h-4 w-4 text-yellow-400" />
                  )}
                </div>
                {customer.ringer_name && customer.ringer_name !== customer.name && (
                  <Badge variant="outline" className="text-xs">
                    Ringer: {customer.ringer_name}
                  </Badge>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center gap-2 text-white/70">
                  <Phone className="h-4 w-4" />
                  <span>{customer.phone}</span>
                </div>
                
                {customer.email && (
                  <div className="flex items-center gap-2 text-white/70">
                    <Mail className="h-4 w-4" />
                    <span>{customer.email}</span>
                  </div>
                )}
                
                {customer.address && (
                  <div className="flex items-center gap-2 text-white/70">
                    <MapPin className="h-4 w-4" />
                    <span className="truncate">{customer.address}</span>
                  </div>
                )}
                
                <div className="flex items-center gap-2 text-white/70">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(customer.created_at).toLocaleDateString()}</span>
                </div>
              </div>

              <div className="flex items-center gap-6 mt-4 text-sm">
                {customer.total_orders !== undefined && (
                  <div className="flex items-center gap-2 text-white/70">
                    <ShoppingBag className="h-4 w-4" />
                    <span>{customer.total_orders} orders</span>
                  </div>
                )}
                
                {customer.loyalty_points !== undefined && (
                  <div className="flex items-center gap-2 text-white/70">
                    <Star className="h-4 w-4" />
                    <span>{customer.loyalty_points} points</span>
                  </div>
                )}
                
                {customer.addresses && customer.addresses.length > 0 && (
                  <div className="flex items-center gap-2 text-white/70">
                    <MapPin className="h-4 w-4" />
                    <span>{customer.addresses.length} address{customer.addresses.length !== 1 ? 'es' : ''}</span>
                  </div>
                )}
              </div>

              {customer.notes && (
                <div className="mt-3 p-3 bg-white/5 rounded-lg">
                  <p className="text-white/70 text-sm">{customer.notes}</p>
                </div>
              )}
            </div>

            <div className="flex gap-2 ml-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewCustomer(customer)}
                className="text-white/60 hover:text-white hover:bg-white/10"
              >
                <Eye className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEditCustomer(customer)}
                className="text-white/60 hover:text-white hover:bg-white/10"
              >
                <Edit className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDeleteCustomer(customer)}
                className="text-white/60 hover:text-red-400 hover:bg-red-400/10"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </GlassCard>
      ))}
    </div>
  )
}