-- Fix Menu RLS Policies for Development Access
-- Drops conflicting policies and adds permissive development access

-- =============================================
-- DROP CONFLICTING POLICIES
-- =============================================

-- Drop original restrictive admin policies that conflict with development access
DROP POLICY IF EXISTS "Admin full access to menu categories" ON menu_categories;
DROP POLICY IF EXISTS "Admin full access to ingredients" ON ingredients;
DROP POLICY IF EXISTS "Admin full access to menu items" ON subcategories;

-- Drop the development policies that require authentication
DROP POLICY IF EXISTS "Dev: Any authenticated user can manage menu categories" ON menu_categories;
DROP POLICY IF EXISTS "Dev: Any authenticated user can manage ingredient categories" ON ingredient_categories;
DROP POLICY IF EXISTS "Dev: Any authenticated user can manage ingredients" ON ingredients;
DROP POLICY IF EXISTS "Dev: Any authenticated user can manage menu items" ON subcategories;
DROP POLICY IF EXISTS "Dev: Any authenticated user can manage menu item ingredients" ON menu_item_ingredients;
DROP POLICY IF EXISTS "Dev: Any authenticated user can manage customization presets" ON customization_presets;
DROP POLICY IF EXISTS "Dev: Any authenticated user can read sync queue" ON menu_sync_queue;

-- =============================================
-- CREATE PERMISSIVE DEVELOPMENT POLICIES
-- =============================================

-- Menu Categories - Full development access (no auth required)
CREATE POLICY "Dev: Full access to menu categories" ON menu_categories
  FOR ALL USING (
    current_setting('app.environment', true) = 'development'
  );

-- Ingredient Categories - Full development access
CREATE POLICY "Dev: Full access to ingredient categories" ON ingredient_categories
  FOR ALL USING (
    current_setting('app.environment', true) = 'development'
  );

-- Ingredients - Full development access
CREATE POLICY "Dev: Full access to ingredients" ON ingredients
  FOR ALL USING (
    current_setting('app.environment', true) = 'development'
  );

-- Menu Items - Full development access
CREATE POLICY "Dev: Full access to menu items" ON subcategories
  FOR ALL USING (
    current_setting('app.environment', true) = 'development'
  );

-- Menu Item Ingredients - Full development access
CREATE POLICY "Dev: Full access to menu item ingredients" ON menu_item_ingredients
  FOR ALL USING (
    current_setting('app.environment', true) = 'development'
  );

-- Customization Presets - Full development access
CREATE POLICY "Dev: Full access to customization presets" ON customization_presets
  FOR ALL USING (
    current_setting('app.environment', true) = 'development'
  );

-- Menu Sync Queue - Read access in development
CREATE POLICY "Dev: Read access to sync queue" ON menu_sync_queue
  FOR SELECT USING (
    current_setting('app.environment', true) = 'development'
  );

-- =============================================
-- SERVICE ROLE POLICIES (Keep existing)
-- =============================================
-- These provide admin access through service role key

CREATE POLICY "Service role full access to menu categories" ON menu_categories
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role full access to ingredient categories" ON ingredient_categories
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role full access to ingredients" ON ingredients
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role full access to menu items" ON subcategories
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role full access to menu item ingredients" ON menu_item_ingredients
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role full access to customization presets" ON customization_presets
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Keep existing service role sync queue policy
-- (Already exists from previous migration)

-- =============================================
-- PRODUCTION-READY POLICIES (For Future Use)
-- =============================================
-- These will be activated when switching to production mode

CREATE POLICY "Prod: Admin access to menu categories" ON menu_categories
  FOR ALL USING (
    current_setting('app.environment', true) = 'production' AND
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles up
      JOIN roles r ON up.role_id = r.id
      WHERE up.user_id = auth.uid() 
        AND r.name IN ('admin', 'manager')
        AND up.is_active = true
        AND r.is_active = true
    )
  );

CREATE POLICY "Prod: Admin access to ingredient categories" ON ingredient_categories
  FOR ALL USING (
    current_setting('app.environment', true) = 'production' AND
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles up
      JOIN roles r ON up.role_id = r.id
      WHERE up.user_id = auth.uid() 
        AND r.name IN ('admin', 'manager')
        AND up.is_active = true
        AND r.is_active = true
    )
  );

CREATE POLICY "Prod: Admin access to ingredients" ON ingredients
  FOR ALL USING (
    current_setting('app.environment', true) = 'production' AND
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles up
      JOIN roles r ON up.role_id = r.id
      WHERE up.user_id = auth.uid() 
        AND r.name IN ('admin', 'manager')
        AND up.is_active = true
        AND r.is_active = true
    )
  );

CREATE POLICY "Prod: Admin access to menu items" ON subcategories
  FOR ALL USING (
    current_setting('app.environment', true) = 'production' AND
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles up
      JOIN roles r ON up.role_id = r.id
      WHERE up.user_id = auth.uid() 
        AND r.name IN ('admin', 'manager')
        AND up.is_active = true
        AND r.is_active = true
    )
  );

-- =============================================
-- PUBLIC READ POLICIES (Always Active)
-- =============================================
-- These allow public read access to active/available items

CREATE POLICY "Public read menu categories" ON menu_categories
  FOR SELECT USING (is_active = true);

CREATE POLICY "Public read ingredient categories" ON ingredient_categories
  FOR SELECT USING (is_active = true);

CREATE POLICY "Public read ingredients" ON ingredients
  FOR SELECT USING (is_available = true);

CREATE POLICY "Public read menu items" ON subcategories
  FOR SELECT USING (is_available = true);

CREATE POLICY "Public read menu item ingredients" ON menu_item_ingredients
  FOR SELECT USING (true);

CREATE POLICY "Public read customization presets" ON customization_presets
  FOR SELECT USING (true);

-- =============================================
-- HELPER FUNCTIONS
-- =============================================

-- Function to test current access level
CREATE OR REPLACE FUNCTION test_menu_access()
RETURNS TABLE(
  table_name text,
  can_select boolean,
  can_insert boolean,
  can_update boolean,
  can_delete boolean,
  environment text,
  auth_role text
) AS $$
DECLARE
  test_category_id UUID;
  test_ingredient_cat_id UUID;
  test_ingredient_id UUID;
  test_menu_item_id UUID;
BEGIN
  -- Return environment and auth info
  environment := current_setting('app.environment', true);
  auth_role := COALESCE(auth.role()::text, 'anonymous');
  
  -- Test menu_categories
  table_name := 'menu_categories';
  BEGIN
    -- Test SELECT
    SELECT id INTO test_category_id FROM menu_categories LIMIT 1;
    can_select := true;
  EXCEPTION WHEN OTHERS THEN
    can_select := false;
  END;
  
  BEGIN
    -- Test INSERT
    INSERT INTO menu_categories (name, description, category_type, display_order) 
    VALUES ('Test Category', 'Test Description', 'standard', 999)
    RETURNING id INTO test_category_id;
    can_insert := true;
    
    -- Test UPDATE
    UPDATE menu_categories SET description = 'Updated Test' WHERE id = test_category_id;
    can_update := true;
    
    -- Test DELETE
    DELETE FROM menu_categories WHERE id = test_category_id;
    can_delete := true;
    
  EXCEPTION WHEN OTHERS THEN
    can_insert := false;
    can_update := false;
    can_delete := false;
    -- Clean up if partial success
    DELETE FROM menu_categories WHERE id = test_category_id;
  END;
  
  RETURN NEXT;
  
  -- Test other tables similarly...
  -- (Simplified for now - can be expanded)
  
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION test_menu_access() TO anon;
GRANT EXECUTE ON FUNCTION test_menu_access() TO authenticated;

-- =============================================
-- ENVIRONMENT MANAGEMENT
-- =============================================

-- Ensure development mode is active
SELECT set_config('app.environment', 'development', false);

-- =============================================
-- LOGGING AND VERIFICATION
-- =============================================

DO $$
BEGIN
  RAISE NOTICE 'Menu RLS policies updated for development access';
  RAISE NOTICE 'Current environment: %', current_setting('app.environment', true);
  RAISE NOTICE 'Development policies allow full access without authentication';
  RAISE NOTICE 'Use SELECT * FROM test_menu_access() to verify access';
  RAISE NOTICE 'Use SELECT toggle_menu_dev_mode(false) to switch to production mode';
END $$;