import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { notFound } from 'next/navigation';
import { GlassCard, GlassButton, GlassBadge, GlassDivider } from '@/components/ui/glass-components';

type Props = {
  params: { id: string };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  return {
    title: `Order ${params.id} | Delicious Crepes & Waffles`,
    description: 'View your order details and status.',
  };
}

// This would normally fetch from your API or Supabase
const getOrder = async (orderId: string) => {
  // Mock order data - in a real app, this would be fetched from your database
  const orders = {
    'ORD-1234': {
      id: 'ORD-1234',
      date: '2023-11-15T18:30:00Z',
      status: 'delivered',
      statusHistory: [
        { status: 'pending', timestamp: '2023-11-15T18:30:00Z' },
        { status: 'processing', timestamp: '2023-11-15T18:35:00Z' },
        { status: 'delivering', timestamp: '2023-11-15T18:50:00Z' },
        { status: 'delivered', timestamp: '2023-11-15T19:15:00Z' },
      ],

      items: [
        {
          id: 'nutella-strawberry',
          name: 'Nutella & Strawberry',
          price: 7.5,
          quantity: 2,
          image: '/images/nutella-strawberry.jpg',
        },
        {
          id: 'ham-cheese',
          name: 'Ham & Cheese',
          price: 8.5,
          quantity: 1,
          image: '/images/ham-cheese.jpg',
        },
        { id: 'coffee', name: 'Coffee', price: 2.5, quantity: 2, image: '/images/coffee.jpg' },
      ],

      subtotal: 28.5,
      tax: 3.71,
      deliveryFee: 2.5,
      total: 34.71,
      deliveryAddress: '123 Main St, Apt 4B, Athens, 12345',
      deliveryInstructions: 'Please ring doorbell twice',
      paymentMethod: 'Cash on Delivery',
      customerName: 'John Doe',
      customerPhone: '+30 ************',
      customerEmail: '<EMAIL>',
    },
    'ORD-1235': {
      id: 'ORD-1235',
      date: '2023-11-10T12:15:00Z',
      status: 'delivered',
      statusHistory: [
        { status: 'pending', timestamp: '2023-11-10T12:15:00Z' },
        { status: 'processing', timestamp: '2023-11-10T12:20:00Z' },
        { status: 'delivering', timestamp: '2023-11-10T12:35:00Z' },
        { status: 'delivered', timestamp: '2023-11-10T13:00:00Z' },
      ],

      items: [
        {
          id: 'spinach-feta',
          name: 'Spinach & Feta',
          price: 8.0,
          quantity: 1,
          image: '/images/spinach-feta.jpg',
        },
        {
          id: 'classic-waffle',
          name: 'Classic Waffle',
          price: 6.5,
          quantity: 1,
          image: '/images/classic-waffle.jpg',
        },
        {
          id: 'orange-juice',
          name: 'Orange Juice',
          price: 3.0,
          quantity: 1,
          image: '/images/orange-juice.jpg',
        },
      ],

      subtotal: 17.5,
      tax: 2.28,
      deliveryFee: 2.5,
      total: 22.28,
      deliveryAddress: '456 Oak St, Athens, 12345',
      deliveryInstructions: '',
      paymentMethod: 'Credit Card',
      customerName: 'Jane Smith',
      customerPhone: '+30 ************',
      customerEmail: '<EMAIL>',
    },
    'ORD-1236': {
      id: 'ORD-1236',
      date: '2023-11-05T19:45:00Z',
      status: 'delivered',
      statusHistory: [
        { status: 'pending', timestamp: '2023-11-05T19:45:00Z' },
        { status: 'processing', timestamp: '2023-11-05T19:50:00Z' },
        { status: 'delivering', timestamp: '2023-11-05T20:05:00Z' },
        { status: 'delivered', timestamp: '2023-11-05T20:30:00Z' },
      ],

      items: [
        {
          id: 'banana-caramel',
          name: 'Banana Caramel',
          price: 7.0,
          quantity: 1,
          image: '/images/banana-caramel.jpg',
        },
        {
          id: 'chocolate-waffle',
          name: 'Chocolate Waffle',
          price: 7.5,
          quantity: 1,
          image: '/images/chocolate-waffle.jpg',
        },
        {
          id: 'hot-chocolate',
          name: 'Hot Chocolate',
          price: 3.5,
          quantity: 2,
          image: '/images/hot-chocolate.jpg',
        },
      ],

      subtotal: 21.5,
      tax: 2.8,
      deliveryFee: 2.5,
      total: 26.8,
      deliveryAddress: '789 Pine St, Athens, 12345',
      deliveryInstructions: 'Leave at the door',
      paymentMethod: 'Cash on Delivery',
      customerName: 'Alex Johnson',
      customerPhone: '+30 ************',
      customerEmail: '<EMAIL>',
    },
  };

  return orders[orderId as keyof typeof orders] || null;
};

// Function to format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Function to get status badge variant
const getStatusVariant = (status: string) => {
  switch (status) {
    case 'pending':
      return 'warning';
    case 'processing':
      return 'info';
    case 'delivering':
      return 'primary';
    case 'delivered':
      return 'success';
    case 'cancelled':
      return 'danger';
    default:
      return 'secondary';
  }
};

export default async function OrderDetailsPage({ params }: Props) {
  const order = await getOrder(params.id);

  if (!order) {
    notFound();
  }

  return (
    <main className="min-h-screen py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Order {order.id}</h1>

          <div className="mt-4 md:mt-0">
            <Link href="/orders">
              <GlassButton variant="secondary">Back to Orders</GlassButton>
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Details - Takes up 2/3 of the space on large screens */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Status */}
            <GlassCard>
              <div className="p-6">
                <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
                  <div>
                    <h2 className="text-xl font-semibold">Order Status</h2>
                    <p className="text-muted-foreground">Placed on {formatDate(order.date)}</p>
                  </div>

                  <GlassBadge variant={getStatusVariant(order.status)} className="mt-2 md:mt-0">
                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                  </GlassBadge>
                </div>

                {/* Status Timeline */}
                <div className="relative">
                  {order.statusHistory.map((status, index) => (
                    <div key={index} className="mb-6 relative pl-8">
                      <div className="absolute left-0 top-1 h-4 w-4 rounded-full bg-primary"></div>
                      {index < order.statusHistory.length - 1 && (
                        <div className="absolute left-2 top-5 h-full w-0.5 bg-primary/30"></div>
                      )}
                      <div>
                        <h3 className="font-medium">
                          {status.status.charAt(0).toUpperCase() + status.status.slice(1)}
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(status.timestamp)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </GlassCard>

            {/* Order Items */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-4">Order Items</h2>

                <div className="space-y-6">
                  {order.items.map(item => (
                    <div key={item.id} className="flex items-center space-x-4">
                      <div className="relative h-20 w-20 rounded-md overflow-hidden flex-shrink-0">
                        <Image src={item.image} alt={item.name} fill className="object-cover" />
                      </div>

                      <div className="flex-grow">
                        <h3 className="font-medium">{item.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          ${item.price.toFixed(2)} each
                        </p>
                      </div>

                      <div className="text-right">
                        <div className="font-medium">
                          ${(item.price * item.quantity).toFixed(2)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Quantity: {item.quantity}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </GlassCard>

            {/* Delivery Information */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-4">Delivery Information</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium mb-2">Delivery Address</h3>
                    <p className="text-muted-foreground">{order.deliveryAddress}</p>

                    {order.deliveryInstructions && (
                      <div className="mt-4">
                        <h3 className="font-medium mb-2">Delivery Instructions</h3>
                        <p className="text-muted-foreground">{order.deliveryInstructions}</p>
                      </div>
                    )}
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">Contact Information</h3>
                    <p className="text-muted-foreground">{order.customerName}</p>
                    <p className="text-muted-foreground">{order.customerPhone}</p>
                    <p className="text-muted-foreground">{order.customerEmail}</p>
                  </div>
                </div>
              </div>
            </GlassCard>
          </div>

          {/* Order Summary - Takes up 1/3 of the space on large screens */}
          <div>
            <GlassCard className="sticky top-24">
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-4">Order Summary</h2>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Subtotal</span>
                    <span>${order.subtotal.toFixed(2)}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Tax (13%)</span>
                    <span>${order.tax.toFixed(2)}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Delivery Fee</span>
                    <span>${order.deliveryFee.toFixed(2)}</span>
                  </div>

                  <GlassDivider />

                  <div className="flex justify-between font-bold text-lg">
                    <span>Total</span>
                    <span>${order.total.toFixed(2)}</span>
                  </div>

                  <div className="mt-2">
                    <span className="text-muted-foreground">Payment Method:</span>
                    <span className="ml-2">{order.paymentMethod}</span>
                  </div>
                </div>

                <div className="mt-6 space-y-3">
                  <GlassButton variant="primary" size="lg" className="w-full">
                    Reorder
                  </GlassButton>

                  {order.status === 'delivered' && (
                    <GlassButton variant="secondary" size="lg" className="w-full">
                      Leave a Review
                    </GlassButton>
                  )}
                </div>
              </div>
            </GlassCard>
          </div>
        </div>
      </div>
    </main>
  );
}
