import React from 'react';

interface EditOptionsModalProps {
  isOpen: boolean;
  orderCount: number;
  onEditInfo: () => void;
  onEditOrder: () => void;
  onClose: () => void;
}

export const EditOptionsModal: React.FC<EditOptionsModalProps> = ({
  isOpen,
  orderCount,
  onEditInfo,
  onEditOrder,
  onClose
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-2xl p-6 w-full max-w-md border border-gray-200/50 dark:border-white/10">
        <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
          Edit Options
        </h3>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          Choose what you want to edit for {orderCount} selected order(s).
        </p>
        
        <div className="space-y-3 mb-6">
          {/* Edit Customer Info Option */}
          <button
            onClick={onEditInfo}
            className="w-full p-4 rounded-lg border text-left transition-all duration-200 border-blue-200 bg-blue-50 hover:bg-blue-100 text-blue-900 dark:border-blue-400/30 dark:bg-blue-500/10 dark:hover:bg-blue-500/20 dark:text-blue-200"
          >
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-blue-500/20 dark:bg-blue-500/30 flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div>
                <div className="font-medium">Edit Customer Info</div>
                <div className="text-sm opacity-75">Update customer name, phone, address</div>
              </div>
            </div>
          </button>

          {/* Edit Order Items Option */}
          <button
            onClick={onEditOrder}
            className="w-full p-4 rounded-lg border text-left transition-all duration-200 border-green-200 bg-green-50 hover:bg-green-100 text-green-900 dark:border-green-400/30 dark:bg-green-500/10 dark:hover:bg-green-500/20 dark:text-green-200"
          >
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-green-500/20 dark:bg-green-500/30 flex items-center justify-center">
                <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
              </div>
              <div>
                <div className="font-medium">Edit Order Items</div>
                <div className="text-sm opacity-75">Modify items, quantities, notes</div>
              </div>
            </div>
          </button>
        </div>
        
        <div className="flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditOptionsModal; 