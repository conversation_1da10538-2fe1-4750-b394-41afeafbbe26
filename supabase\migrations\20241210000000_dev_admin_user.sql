-- Development Admin User Setup
-- This migration creates a development admin user for testing purposes

-- Insert development admin user into auth.users (this would normally be done through Supabase Auth)
-- Note: In production, users should be created through the Supabase Auth API

-- First, let's create the roles if they don't exist
INSERT INTO roles (id, name, description, level, is_active) VALUES 
  ('11111111-1111-1111-1111-111111111111', 'admin', 'System Administrator', 100, true),
  ('22222222-2222-2222-2222-222222222222', 'manager', 'Branch Manager', 80, true),
  ('33333333-3333-3333-3333-333333333333', 'staff', 'Staff Member', 50, true)
ON CONFLICT (id) DO NOTHING;

-- Create permissions if they don't exist
INSERT INTO permissions (id, name, description, category) VALUES 
  ('11111111-1111-1111-1111-111111111111', 'admin.user_management', 'Manage users and roles', 'admin'),
  ('22222222-2222-2222-2222-222222222222', 'admin.branch_management', 'Manage branches and locations', 'admin'),
  ('33333333-3333-3333-3333-333333333333', 'manager.staff_management', 'Manage staff in assigned branches', 'manager'),
  ('44444444-4444-4444-4444-444444444444', 'staff.pos_access', 'Access POS system', 'staff')
ON CONFLICT (id) DO NOTHING;

-- Assign permissions to admin role
INSERT INTO role_permissions (role_id, permission_id) VALUES 
  ('11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111'),
  ('11111111-1111-1111-1111-111111111111', '22222222-2222-2222-2222-222222222222'),
  ('11111111-1111-1111-1111-111111111111', '33333333-3333-3333-3333-333333333333'),
  ('11111111-1111-1111-1111-111111111111', '44444444-4444-4444-4444-444444444444')
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Assign permissions to manager role
INSERT INTO role_permissions (role_id, permission_id) VALUES 
  ('22222222-2222-2222-2222-222222222222', '33333333-3333-3333-3333-333333333333'),
  ('22222222-2222-2222-2222-222222222222', '44444444-4444-4444-4444-444444444444')
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Assign permissions to staff role
INSERT INTO role_permissions (role_id, permission_id) VALUES 
  ('33333333-3333-3333-3333-333333333333', '44444444-4444-4444-4444-444444444444')
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Create a development admin user profile
-- Note: The actual auth.users entry should be created through Supabase Auth
-- This is just the profile data
INSERT INTO user_profiles (
  id, 
  user_id, 
  full_name, 
  role_id, 
  is_active, 
  created_at, 
  updated_at
) VALUES (
  'dev-admin-profile-id',
  'dev-admin-user-id', -- This should match the auth.users.id
  'Development Admin',
  '11111111-1111-1111-1111-111111111111', -- admin role
  true,
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Create staff record for the admin user
INSERT INTO staff (
  id,
  user_id,
  staff_code,
  first_name,
  last_name,
  email,
  role_id,
  branch_id,
  department,
  employment_type,
  hire_date,
  is_active,
  can_login_pos,
  can_login_admin,
  created_at
) VALUES (
  'dev-admin-staff-id',
  'dev-admin-user-id',
  'ADMIN001',
  'Development',
  'Admin',
  '<EMAIL>',
  '11111111-1111-1111-1111-111111111111',
  '11111111-1111-1111-1111-111111111111', -- main branch
  'management',
  'full-time',
  '2024-01-01',
  true,
  true,
  true,
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Add a comment explaining how to create the actual auth user
COMMENT ON TABLE user_profiles IS 'To create the actual auth user, use the Supabase Auth API or dashboard to create a user with email: <EMAIL> and password: dev123456, then update the user_id fields above with the actual auth.users.id'; 