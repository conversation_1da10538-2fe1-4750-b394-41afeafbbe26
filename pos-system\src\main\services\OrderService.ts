import Database from 'better-sqlite3';
import { BaseService } from './BaseService';

// Database row interfaces
interface OrderRow {
  id: string;
  order_number?: string;
  customer_name?: string;
  customer_phone?: string;
  customer_email?: string;
  items: string; // JSON string
  total_amount: number;
  status: 'pending' | 'preparing' | 'ready' | 'completed';
  order_type?: 'dine-in' | 'takeaway' | 'delivery';
  table_number?: string;
  delivery_address?: string;
  special_instructions?: string;
  created_at: string;
  updated_at: string;
  estimated_time?: number;
  supabase_id?: string;
  sync_status: 'synced' | 'pending' | 'failed';
  payment_status?: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
  payment_method?: string;
  payment_transaction_id?: string;
}

interface OrderFilters {
  status?: string;
  fromDate?: string;
  toDate?: string;
  customerId?: string;
  paymentStatus?: string;
  date?: string;
  limit?: number;
  offset?: number;
}

export interface Order {
  id: string;
  order_number?: string;
  customer_name?: string;
  customer_phone?: string;
  customer_email?: string;
  items: OrderItem[];
  total_amount: number;
  status: 'pending' | 'preparing' | 'ready' | 'completed';
  order_type?: 'dine-in' | 'takeaway' | 'delivery';
  table_number?: string;
  delivery_address?: string;
  special_instructions?: string;
  created_at: string;
  updated_at: string;
  estimated_time?: number;
  supabase_id?: string;
  sync_status: 'synced' | 'pending' | 'failed';
  payment_status?: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
  payment_method?: string;
  payment_transaction_id?: string;
}

export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
}

export class OrderService extends BaseService {
  constructor(database: Database.Database) {
    super(database);
  }

  createOrder(orderData: Partial<Order>): Order {
    return this.executeTransaction(() => {
      this.validateRequired(orderData, ['items', 'total_amount', 'status']);

      const order: Order = {
        id: this.generateId(),
        order_number: orderData.order_number || this.generateOrderNumber(),
        customer_name: orderData.customer_name,
        customer_phone: orderData.customer_phone,
        customer_email: orderData.customer_email,
        items: orderData.items || [],
        total_amount: orderData.total_amount || 0,
        status: orderData.status || 'pending',
        order_type: orderData.order_type || 'takeaway',
        table_number: orderData.table_number,
        delivery_address: orderData.delivery_address,
        special_instructions: orderData.special_instructions,
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp(),
        estimated_time: orderData.estimated_time,
        supabase_id: orderData.supabase_id,
        sync_status: 'pending',
        payment_status: orderData.payment_status || 'pending',
        payment_method: orderData.payment_method,
        payment_transaction_id: orderData.payment_transaction_id
      };

      const stmt = this.db.prepare(`
        INSERT INTO orders (
          id, order_number, customer_name, customer_phone, customer_email,
          items, total_amount, status, order_type, table_number,
          delivery_address, special_instructions, created_at, updated_at,
          estimated_time, supabase_id, sync_status, payment_status,
          payment_method, payment_transaction_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      stmt.run(
        order.id, order.order_number, order.customer_name, order.customer_phone,
        order.customer_email, JSON.stringify(order.items), order.total_amount,
        order.status, order.order_type, order.table_number, order.delivery_address,
        order.special_instructions, order.created_at, order.updated_at,
        order.estimated_time, order.supabase_id, order.sync_status,
        order.payment_status, order.payment_method, order.payment_transaction_id
      );

      return order;
    });
  }

  getOrder(id: string): Order | null {
    const stmt = this.db.prepare('SELECT * FROM orders WHERE id = ?');
    const row = stmt.get(id) as OrderRow | undefined;
    
    if (!row) return null;
    
    return this.mapRowToOrder(row);
  }

  getAllOrders(filters?: OrderFilters): Order[] {
    let query = 'SELECT * FROM orders';
    const params: (string | number)[] = [];
    const conditions: string[] = [];

    if (filters?.status) {
      conditions.push('status = ?');
      params.push(filters.status);
    }

    if (filters?.date) {
      conditions.push('date(created_at) = ?');
      params.push(filters.date);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY created_at DESC';

    if (filters?.limit) {
      query += ' LIMIT ?';
      params.push(filters.limit);
      
      if (filters?.offset) {
        query += ' OFFSET ?';
        params.push(filters.offset);
      }
    }

    const stmt = this.db.prepare(query);
    const rows = stmt.all(...params) as OrderRow[];
    
    return rows.map(row => this.mapRowToOrder(row));
  }

  updateOrder(id: string, updates: Partial<Order>): Order | null {
    return this.executeTransaction(() => {
      const existingOrder = this.getOrder(id);
      if (!existingOrder) return null;

      const updatedOrder: Order = {
        ...existingOrder,
        ...updates,
        updated_at: this.getCurrentTimestamp(),
        sync_status: 'pending' as const
      };

      const stmt = this.db.prepare(`
        UPDATE orders SET
          customer_name = ?, customer_phone = ?, customer_email = ?,
          items = ?, total_amount = ?, status = ?, order_type = ?,
          table_number = ?, delivery_address = ?, special_instructions = ?,
          updated_at = ?, estimated_time = ?, supabase_id = ?,
          sync_status = ?, payment_status = ?, payment_method = ?,
          payment_transaction_id = ?
        WHERE id = ?
      `);

      stmt.run(
        updatedOrder.customer_name, updatedOrder.customer_phone, updatedOrder.customer_email,
        JSON.stringify(updatedOrder.items), updatedOrder.total_amount, updatedOrder.status,
        updatedOrder.order_type, updatedOrder.table_number, updatedOrder.delivery_address,
        updatedOrder.special_instructions, updatedOrder.updated_at, updatedOrder.estimated_time,
        updatedOrder.supabase_id, updatedOrder.sync_status, updatedOrder.payment_status,
        updatedOrder.payment_method, updatedOrder.payment_transaction_id, id
      );

      return updatedOrder;
    });
  }

  deleteOrder(id: string): boolean {
    return this.executeTransaction(() => {
      const stmt = this.db.prepare('DELETE FROM orders WHERE id = ?');
      const result = stmt.run(id);
      return result.changes > 0;
    });
  }

  updateOrderStatus(id: string, status: Order['status']): boolean {
    return this.executeTransaction(() => {
      const stmt = this.db.prepare(`
        UPDATE orders SET 
          status = ?, 
          updated_at = ?, 
          sync_status = 'pending'
        WHERE id = ?
      `);
      
      const result = stmt.run(status, this.getCurrentTimestamp(), id);
      return result.changes > 0;
    });
  }

  getOrdersByStatus(status: Order['status']): Order[] {
    return this.getAllOrders({ status });
  }

  getTodaysOrders(): Order[] {
    const today = new Date().toISOString().split('T')[0];
    return this.getAllOrders({ fromDate: today });
  }

  private generateOrderNumber(): string {
    const today = new Date();
    const prefix = today.toISOString().slice(0, 10).replace(/-/g, '');
    const timestamp = Date.now().toString().slice(-6);
    return `ORD-${prefix}-${timestamp}`;
  }

  private mapRowToOrder(row: OrderRow): Order {
    return {
      id: row.id,
      order_number: row.order_number,
      customer_name: row.customer_name,
      customer_phone: row.customer_phone,
      customer_email: row.customer_email,
      items: JSON.parse(row.items),
      total_amount: row.total_amount,
      status: row.status,
      order_type: row.order_type,
      table_number: row.table_number,
      delivery_address: row.delivery_address,
      special_instructions: row.special_instructions,
      created_at: row.created_at,
      updated_at: row.updated_at,
      estimated_time: row.estimated_time,
      supabase_id: row.supabase_id,
      sync_status: row.sync_status as 'synced' | 'pending' | 'failed',
      payment_status: row.payment_status,
      payment_method: row.payment_method,
      payment_transaction_id: row.payment_transaction_id
    };
  }
}