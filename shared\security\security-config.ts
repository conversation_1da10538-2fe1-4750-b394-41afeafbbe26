/**
 * Comprehensive Security Configuration for Creperie Digital Ecosystem
 * 
 * This file defines security policies, configurations, and constants
 * that are used across all applications in the ecosystem.
 */

// Security Headers Configuration
export const SECURITY_HEADERS = {
  // Content Security Policy
  CSP: {
    'default-src': ["'self'"],
    'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'", 'https://js.stripe.com'],
    'style-src': ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
    'font-src': ["'self'", 'https://fonts.gstatic.com'],
    'img-src': ["'self'", 'data:', 'https:', 'blob:'],
    'connect-src': ["'self'", 'https://*.supabase.co', 'wss://*.supabase.co'],
    'frame-src': ["'none'"],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-ancestors': ["'none'"],
    'upgrade-insecure-requests': true,
  },
  
  // HTTP Security Headers
  HTTP: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=(self)',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  },
} as const;

// Rate Limiting Configuration
export const RATE_LIMITS = {
  // Authentication endpoints
  AUTH: {
    LOGIN: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // 5 attempts per window
      message: 'Too many login attempts, please try again later',
      skipSuccessfulRequests: true,
    },
    REGISTER: {
      windowMs: 60 * 60 * 1000, // 1 hour
      max: 3, // 3 registrations per hour per IP
      message: 'Too many registration attempts, please try again later',
    },
    PASSWORD_RESET: {
      windowMs: 60 * 60 * 1000, // 1 hour
      max: 3, // 3 password reset attempts per hour
      message: 'Too many password reset attempts, please try again later',
    },
    OTP_REQUEST: {
      windowMs: 5 * 60 * 1000, // 5 minutes
      max: 3, // 3 OTP requests per 5 minutes
      message: 'Too many OTP requests, please try again later',
    },
  },
  
  // API endpoints
  API: {
    GENERAL: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // 100 requests per 15 minutes
      message: 'Too many requests, please try again later',
    },
    SEARCH: {
      windowMs: 60 * 1000, // 1 minute
      max: 30, // 30 search requests per minute
      message: 'Too many search requests, please slow down',
    },
    UPLOAD: {
      windowMs: 60 * 60 * 1000, // 1 hour
      max: 10, // 10 file uploads per hour
      message: 'Too many file uploads, please try again later',
    },
  },
  
  // Payment endpoints
  PAYMENT: {
    PROCESS: {
      windowMs: 60 * 60 * 1000, // 1 hour
      max: 10, // 10 payment attempts per hour
      message: 'Too many payment attempts, please contact support',
    },
  },
} as const;

// Input Validation Rules
export const VALIDATION_RULES = {
  // User input validation
  USER: {
    EMAIL: {
      pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      maxLength: 254,
      required: true,
    },
    PHONE: {
      pattern: /^\+?[1-9]\d{1,14}$/, // E.164 format
      maxLength: 15,
      required: true,
    },
    PASSWORD: {
      minLength: 8,
      maxLength: 128,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    },
    PIN: {
      length: 4,
      pattern: /^\d{4}$/,
      required: true,
    },
    NAME: {
      minLength: 2,
      maxLength: 50,
      pattern: /^[a-zA-ZÀ-ÿ\s'-]+$/,
      required: true,
    },
  },
  
  // Business data validation
  BUSINESS: {
    PRODUCT_NAME: {
      minLength: 2,
      maxLength: 100,
      pattern: /^[a-zA-ZÀ-ÿ0-9\s'-.,()]+$/,
      required: true,
    },
    PRICE: {
      min: 0.01,
      max: 999.99,
      pattern: /^\d+(\.\d{1,2})?$/,
      required: true,
    },
    ADDRESS: {
      minLength: 10,
      maxLength: 200,
      required: true,
    },
  },
  
  // Payment validation
  PAYMENT: {
    CARD_NUMBER: {
      pattern: /^\d{13,19}$/,
      required: true,
    },
    CVV: {
      pattern: /^\d{3,4}$/,
      required: true,
    },
    EXPIRY: {
      pattern: /^(0[1-9]|1[0-2])\/\d{2}$/,
      required: true,
    },
  },
} as const;

// Encryption Configuration
export const ENCRYPTION_CONFIG = {
  // AES encryption for sensitive data
  AES: {
    algorithm: 'aes-256-gcm',
    keyLength: 32,
    ivLength: 16,
    tagLength: 16,
  },
  
  // Password hashing
  BCRYPT: {
    saltRounds: 12,
  },
  
  // JWT configuration
  JWT: {
    algorithm: 'HS256' as const,
    expiresIn: '24h',
    refreshExpiresIn: '7d',
    issuer: 'creperie-system',
    audience: 'creperie-users',
  },
} as const;

// Audit Logging Configuration
export const AUDIT_CONFIG = {
  // Events to log
  EVENTS: {
    AUTH: [
      'login_attempt',
      'login_success',
      'login_failure',
      'logout',
      'password_change',
      'password_reset_request',
      'password_reset_success',
      'account_locked',
      'account_unlocked',
      'two_fa_enabled',
      'two_fa_disabled',
      'session_expired',
    ],
    DATA: [
      'user_created',
      'user_updated',
      'user_deleted',
      'product_created',
      'product_updated',
      'product_deleted',
      'order_created',
      'order_updated',
      'order_cancelled',
      'payment_processed',
      'payment_failed',
    ],
    SYSTEM: [
      'system_startup',
      'system_shutdown',
      'database_backup',
      'security_alert',
      'rate_limit_exceeded',
      'suspicious_activity',
    ],
    ADMIN: [
      'admin_login',
      'admin_action',
      'settings_changed',
      'user_role_changed',
      'system_configuration_changed',
    ],
  },
  
  // Log retention periods (in days)
  RETENTION: {
    AUTH_LOGS: 90,
    DATA_LOGS: 365,
    SYSTEM_LOGS: 30,
    ADMIN_LOGS: 365,
    SECURITY_LOGS: 730, // 2 years
  },
} as const;

// GDPR Compliance Configuration
export const GDPR_CONFIG = {
  // Data retention periods
  RETENTION_PERIODS: {
    USER_DATA: 365 * 3, // 3 years after last activity
    ORDER_DATA: 365 * 7, // 7 years for tax purposes
    PAYMENT_DATA: 365 * 7, // 7 years for financial records
    MARKETING_DATA: 365 * 2, // 2 years or until consent withdrawn
    LOG_DATA: 365 * 2, // 2 years for security logs
  },
  
  // Data processing lawful bases
  LAWFUL_BASIS: {
    CONSENT: 'consent',
    CONTRACT: 'contract',
    LEGAL_OBLIGATION: 'legal_obligation',
    VITAL_INTERESTS: 'vital_interests',
    PUBLIC_TASK: 'public_task',
    LEGITIMATE_INTERESTS: 'legitimate_interests',
  },
  
  // Data categories
  DATA_CATEGORIES: {
    PERSONAL: 'personal_data',
    SENSITIVE: 'sensitive_data',
    FINANCIAL: 'financial_data',
    BEHAVIORAL: 'behavioral_data',
    TECHNICAL: 'technical_data',
  },
} as const;

// PCI DSS Compliance Configuration
export const PCI_DSS_CONFIG = {
  // Card data handling rules
  CARD_DATA: {
    // Never store these fields
    PROHIBITED_STORAGE: [
      'full_track_data',
      'card_verification_code',
      'pin_verification_value',
    ],
    
    // Mask PAN when displaying
    PAN_MASKING: {
      visibleDigits: 4, // Last 4 digits
      maskCharacter: '*',
    },
    
    // Encryption requirements
    ENCRYPTION: {
      algorithm: 'AES-256',
      keyRotationDays: 90,
    },
  },
  
  // Network security requirements
  NETWORK: {
    TLS_VERSION: '1.2',
    CIPHER_SUITES: [
      'TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384',
      'TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256',
    ],
  },
} as const;

// Security Monitoring Configuration
export const MONITORING_CONFIG = {
  // Suspicious activity patterns
  SUSPICIOUS_PATTERNS: {
    MULTIPLE_FAILED_LOGINS: {
      threshold: 5,
      timeWindow: 15 * 60 * 1000, // 15 minutes
    },
    RAPID_API_CALLS: {
      threshold: 100,
      timeWindow: 60 * 1000, // 1 minute
    },
    UNUSUAL_LOCATION: {
      enabled: true,
      alertOnNewCountry: true,
    },
    LARGE_DATA_EXPORT: {
      threshold: 1000, // records
      requireApproval: true,
    },
  },
  
  // Alert thresholds
  ALERTS: {
    CRITICAL: {
      responseTime: 5 * 60, // 5 minutes
      escalationTime: 15 * 60, // 15 minutes
    },
    HIGH: {
      responseTime: 30 * 60, // 30 minutes
      escalationTime: 2 * 60 * 60, // 2 hours
    },
    MEDIUM: {
      responseTime: 4 * 60 * 60, // 4 hours
      escalationTime: 24 * 60 * 60, // 24 hours
    },
  },
} as const;

// Export all configurations
export const SECURITY_CONFIG = {
  HEADERS: SECURITY_HEADERS,
  RATE_LIMITS,
  VALIDATION: VALIDATION_RULES,
  ENCRYPTION: ENCRYPTION_CONFIG,
  AUDIT: AUDIT_CONFIG,
  GDPR: GDPR_CONFIG,
  PCI_DSS: PCI_DSS_CONFIG,
  MONITORING: MONITORING_CONFIG,
} as const;

export default SECURITY_CONFIG;