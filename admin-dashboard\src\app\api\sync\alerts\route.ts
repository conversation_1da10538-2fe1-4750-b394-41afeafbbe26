import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    const unacknowledged = searchParams.get('unacknowledged') === 'true'
    const severity = searchParams.get('severity')
    const alertType = searchParams.get('alert_type')
    const limit = parseInt(searchParams.get('limit') || '50')

    // For now, generate mock alerts based on system status
    // In a real implementation, these would come from a dedicated alerts table
    const alerts = await generateSystemAlerts(supabase, unacknowledged, severity, alertType, limit)

    // Calculate summary statistics
    const totalAlerts = alerts.length
    const criticalAlerts = alerts.filter(alert => alert.severity === 'critical').length
    const warningAlerts = alerts.filter(alert => alert.severity === 'warning').length
    const infoAlerts = alerts.filter(alert => alert.severity === 'info').length
    const unacknowledgedAlerts = alerts.filter(alert => !alert.acknowledged).length

    return NextResponse.json({
      alerts,
      summary: {
        total: totalAlerts,
        unacknowledged: unacknowledgedAlerts,
        critical: criticalAlerts,
        warning: warningAlerts,
        info: infoAlerts
      },
      filters: {
        unacknowledged,
        severity,
        alert_type: alertType,
        limit
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Sync alerts error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { alert_id, action } = await request.json()

    if (!alert_id || !action) {
      return NextResponse.json(
        { error: 'alert_id and action are required' },
        { status: 400 }
      )
    }

    let result = { success: false, message: '' }

    switch (action) {
      case 'acknowledge':
        // In a real implementation, this would update an alerts table
        result = { success: true, message: 'Alert acknowledged successfully' }
        break

      case 'dismiss':
        // In a real implementation, this would mark the alert as dismissed
        result = { success: true, message: 'Alert dismissed successfully' }
        break

      case 'resolve':
        // In a real implementation, this would mark the alert as resolved
        result = { success: true, message: 'Alert resolved successfully' }
        break

      default:
        return NextResponse.json(
          { error: 'Invalid action. Must be one of: acknowledge, dismiss, resolve' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      ...result,
      alert_id,
      action,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Sync alerts POST error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function generateSystemAlerts(supabase: any, unacknowledged: boolean, severity: string | null, alertType: string | null, limit: number) {
  const alerts = []
  const now = new Date()

  try {
    // Check for failed sync operations
    const { data: failedSyncs } = await supabase
      .from('pos_settings_sync_history')
      .select('*')
      .eq('sync_status', 'failed')
      .gte('created_at', new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString())
      .limit(10)

    if (failedSyncs && failedSyncs.length > 0) {
      alerts.push({
        id: 'sync-failures-001',
        type: 'sync_failure',
        severity: failedSyncs.length > 5 ? 'critical' : 'warning',
        title: 'Sync Failures Detected',
        message: `${failedSyncs.length} sync operation(s) have failed in the last 24 hours`,
        details: {
          failed_count: failedSyncs.length,
          affected_terminals: [...new Set(failedSyncs.map(s => s.terminal_id))],
          sync_types: [...new Set(failedSyncs.map(s => s.sync_type))]
        },
        created_at: failedSyncs[0].created_at,
        acknowledged: false,
        actions: ['retry_failed', 'investigate', 'acknowledge']
      })
    }

    // Check for offline terminals
    const { data: terminals } = await supabase
      .from('pos_terminals')
      .select('*')
      .lt('last_heartbeat', new Date(now.getTime() - 10 * 60 * 1000).toISOString()) // 10 minutes ago

    if (terminals && terminals.length > 0) {
      alerts.push({
        id: 'offline-terminals-001',
        type: 'terminal_offline',
        severity: 'warning',
        title: 'Terminals Offline',
        message: `${terminals.length} terminal(s) appear to be offline`,
        details: {
          offline_terminals: terminals.map(t => ({
            terminal_id: t.terminal_id,
            name: t.name,
            last_seen: t.last_heartbeat
          }))
        },
        created_at: now.toISOString(),
        acknowledged: false,
        actions: ['check_connection', 'restart_terminal', 'acknowledge']
      })
    }

    // Check for low stock ingredients
    const { data: lowStockIngredients } = await supabase
      .from('ingredients')
      .select('id, name, stock_quantity, min_stock_level')
      .lte('stock_quantity', supabase.raw('min_stock_level'))
      .gt('min_stock_level', 0)

    if (lowStockIngredients && lowStockIngredients.length > 0) {
      const outOfStock = lowStockIngredients.filter(ing => ing.stock_quantity === 0)
      
      alerts.push({
        id: 'low-stock-001',
        type: 'inventory_alert',
        severity: outOfStock.length > 0 ? 'critical' : 'warning',
        title: 'Low Stock Alert',
        message: `${lowStockIngredients.length} ingredient(s) are running low, ${outOfStock.length} are out of stock`,
        details: {
          low_stock_items: lowStockIngredients.map(ing => ({
            id: ing.id,
            name: ing.name,
            current_stock: ing.stock_quantity,
            min_level: ing.min_stock_level
          })),
          out_of_stock_count: outOfStock.length
        },
        created_at: now.toISOString(),
        acknowledged: false,
        actions: ['reorder_stock', 'update_levels', 'acknowledge']
      })
    }

    // Check for pending sync operations
    const { data: pendingSyncs } = await supabase
      .from('pos_settings_sync_history')
      .select('*')
      .eq('sync_status', 'pending')
      .lt('created_at', new Date(now.getTime() - 30 * 60 * 1000).toISOString()) // 30 minutes ago

    if (pendingSyncs && pendingSyncs.length > 0) {
      alerts.push({
        id: 'pending-syncs-001',
        type: 'sync_delay',
        severity: 'info',
        title: 'Delayed Sync Operations',
        message: `${pendingSyncs.length} sync operation(s) have been pending for over 30 minutes`,
        details: {
          pending_count: pendingSyncs.length,
          oldest_pending: pendingSyncs.reduce((oldest, sync) => 
            new Date(sync.created_at) < new Date(oldest.created_at) ? sync : oldest
          ).created_at
        },
        created_at: now.toISOString(),
        acknowledged: false,
        actions: ['force_sync', 'cancel_pending', 'acknowledge']
      })
    }

    // Check for high sync failure rate
    const { data: recentSyncs } = await supabase
      .from('pos_settings_sync_history')
      .select('sync_status')
      .gte('created_at', new Date(now.getTime() - 60 * 60 * 1000).toISOString()) // Last hour

    if (recentSyncs && recentSyncs.length > 10) {
      const failureRate = (recentSyncs.filter(s => s.sync_status === 'failed').length / recentSyncs.length) * 100
      
      if (failureRate > 20) {
        alerts.push({
          id: 'high-failure-rate-001',
          type: 'performance_alert',
          severity: failureRate > 50 ? 'critical' : 'warning',
          title: 'High Sync Failure Rate',
          message: `Sync failure rate is ${Math.round(failureRate)}% in the last hour`,
          details: {
            failure_rate: failureRate,
            total_operations: recentSyncs.length,
            failed_operations: recentSyncs.filter(s => s.sync_status === 'failed').length,
            time_period: '1 hour'
          },
          created_at: now.toISOString(),
          acknowledged: false,
          actions: ['investigate_failures', 'check_connectivity', 'acknowledge']
        })
      }
    }

  } catch (error) {
    console.error('Error generating system alerts:', error)
    
    // Add a system error alert
    alerts.push({
      id: 'system-error-001',
      type: 'system_error',
      severity: 'critical',
      title: 'System Monitoring Error',
      message: 'Unable to retrieve complete system status',
      details: {
        error_message: error instanceof Error ? error.message : 'Unknown error'
      },
      created_at: now.toISOString(),
      acknowledged: false,
      actions: ['check_system', 'acknowledge']
    })
  }

  // Apply filters
  let filteredAlerts = alerts

  if (unacknowledged) {
    filteredAlerts = filteredAlerts.filter(alert => !alert.acknowledged)
  }

  if (severity) {
    filteredAlerts = filteredAlerts.filter(alert => alert.severity === severity)
  }

  if (alertType) {
    filteredAlerts = filteredAlerts.filter(alert => alert.type === alertType)
  }

  // Apply limit
  return filteredAlerts.slice(0, limit)
}
