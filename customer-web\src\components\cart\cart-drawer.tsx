'use client';

import React from 'react';
import { X, Minus, Plus, ShoppingBag, ArrowRight } from 'lucide-react';
import { useCartContext } from '@/providers/cart-provider';
import { GlassCard, GlassButton } from '@/components/ui/glass-components';
import { OptimizedImage } from '@/components/ui/optimized-image';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface CartDrawerProps {
  className?: string;
}

export function CartDrawer({ className }: CartDrawerProps) {
  const { 
    cart, 
    isCartOpen, 
    setIsCartOpen, 
    updateItem, 
    removeItem, 
    itemCount 
  } = useCartContext();

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(itemId);
    } else {
      updateItem(itemId, newQuantity);
    }
  };

  const handleClose = () => {
    setIsCartOpen(false);
  };

  if (!isCartOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300"
        onClick={handleClose}
      />
      
      {/* Drawer */}
      <div className={cn(
        'fixed right-0 top-0 h-full w-full max-w-md bg-background/95 backdrop-blur-md',
        'border-l border-border shadow-2xl z-50',
        'transform transition-transform duration-300 ease-in-out',
        'translate-x-0',
        className
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          <div className="flex items-center space-x-2">
            <ShoppingBag className="w-5 h-5" />
            <h2 className="text-lg font-semibold">
              Cart ({itemCount} {itemCount === 1 ? 'item' : 'items'})
            </h2>
          </div>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-muted rounded-full transition-colors"
            aria-label="Close cart"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Cart Content */}
        <div className="flex flex-col h-full">
          {cart.items.length === 0 ? (
            /* Empty Cart */
            <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
              <ShoppingBag className="w-16 h-16 text-muted-foreground/50 mb-4" />
              <h3 className="text-lg font-medium text-muted-foreground mb-2">
                Your cart is empty
              </h3>
              <p className="text-sm text-muted-foreground mb-6">
                Add some delicious items to get started!
              </p>
              <Link href="/menu" onClick={handleClose}>
                <GlassButton variant="primary">
                  Browse Menu
                </GlassButton>
              </Link>
            </div>
          ) : (
            /* Cart Items */
            <>
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {cart.items.map((item) => (
                  <GlassCard key={item.id} className="p-4">
                    <div className="flex items-start space-x-3">
                      {/* Item Image */}
                      <div className="relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                        <OptimizedImage
                          src={item.menuItem.image_url || '/placeholder-food.jpg'}
                          alt={item.menuItem.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      
                      {/* Item Details */}
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm truncate">
                          {item.menuItem.name}
                        </h4>
                        {item.variant && (
                          <p className="text-xs text-muted-foreground">
                            {item.variant.name}
                          </p>
                        )}
                        {item.specialInstructions && (
                          <p className="text-xs text-muted-foreground italic">
                            {item.specialInstructions}
                          </p>
                        )}
                        <p className="text-sm font-semibold text-primary">
                          €{item.price.toFixed(2)}
                        </p>
                      </div>
                      
                      {/* Quantity Controls */}
                      <div className="flex flex-col items-end space-y-2">
                        <button
                          onClick={() => removeItem(item.id)}
                          className="text-xs text-muted-foreground hover:text-destructive transition-colors"
                        >
                          Remove
                        </button>
                        
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                            className="w-6 h-6 rounded-full bg-muted hover:bg-muted/80 flex items-center justify-center transition-colors"
                            disabled={item.quantity <= 1}
                          >
                            <Minus className="w-3 h-3" />
                          </button>
                          
                          <span className="text-sm font-medium w-8 text-center">
                            {item.quantity}
                          </span>
                          
                          <button
                            onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                            className="w-6 h-6 rounded-full bg-muted hover:bg-muted/80 flex items-center justify-center transition-colors"
                          >
                            <Plus className="w-3 h-3" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </GlassCard>
                ))}
              </div>
              
              {/* Cart Summary */}
              <div className="border-t border-border p-4 space-y-4">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>€{cart.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax (24%):</span>
                    <span>€{cart.tax.toFixed(2)}</span>
                  </div>
                  {cart.deliveryFee > 0 && (
                    <div className="flex justify-between">
                      <span>Delivery:</span>
                      <span>€{cart.deliveryFee.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="flex justify-between font-semibold text-base border-t border-border pt-2">
                    <span>Total:</span>
                    <span>€{cart.total.toFixed(2)}</span>
                  </div>
                </div>
                
                {/* Action Buttons */}
                <div className="space-y-2">
                  <Link href="/cart" onClick={handleClose} className="block">
                    <GlassButton variant="outline" className="w-full">
                      View Full Cart
                    </GlassButton>
                  </Link>
                  
                  <Link href="/checkout" onClick={handleClose} className="block">
                    <GlassButton variant="primary" className="w-full">
                      Checkout
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </GlassButton>
                  </Link>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
}

export default CartDrawer;