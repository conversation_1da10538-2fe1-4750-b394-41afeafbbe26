{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "baseUrl": ".", "paths": {"@/*": ["./auth/*"], "@/auth": ["./auth/index.ts"], "@/auth/*": ["./auth/*"]}, "types": ["node"]}, "include": ["auth/**/*", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}