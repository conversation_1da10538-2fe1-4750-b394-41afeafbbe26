import React from 'react';
import { Edit, Trash2, MapPin } from 'lucide-react';
import { useTheme } from '../../contexts/theme-context';

interface Address {
  id: string;
  street: string;
  postal_code: string;
  floor?: string;
  notes?: string;
  delivery_instructions?: string;
}

interface Customer {
  id: string;
  name: string;
  phone_number: string;
  email?: string;
  addresses?: Address[];
}

interface AddressSelectionCardProps {
  address: Address;
  customer: Customer;
  onEdit?: (address: Address) => void;
  onDelete?: (address: Address) => void;
  className?: string;
}

export const AddressSelectionCard: React.FC<AddressSelectionCardProps> = ({
  address,
  customer,
  onEdit,
  onDelete,
  className = ''
}) => {
  const { resolvedTheme } = useTheme();

  return (
    <div className={`p-6 rounded-2xl border transition-all duration-300 backdrop-blur-sm ${
      resolvedTheme === 'dark'
        ? 'bg-gray-700/30 border-gray-600/30 hover:bg-gray-700/50 hover:border-gray-500/50'
        : 'bg-white/30 border-gray-200/30 hover:bg-white/50 hover:border-gray-300/50'
    } ${className}`}>
      {/* Address Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
            resolvedTheme === 'dark' ? 'bg-blue-600/20' : 'bg-blue-100/60'
          }`}>
            <MapPin className="w-5 h-5 text-blue-500" />
          </div>
          <div>
            <h3 className={`font-semibold ${
              resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              Delivery Address
            </h3>
            <p className={`text-sm ${
              resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
            }`}>
              {customer.name}
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        {(onEdit || onDelete) && (
          <div className="flex space-x-2">
            {onEdit && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(address);
                }}
                className={`p-2 rounded-lg transition-all duration-200 ${
                  resolvedTheme === 'dark'
                    ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-600/50'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100/50'
                }`}
              >
                <Edit className="w-4 h-4" />
              </button>
            )}
            {onDelete && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(address);
                }}
                className={`p-2 rounded-lg transition-all duration-200 ${
                  resolvedTheme === 'dark'
                    ? 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
                    : 'text-red-500 hover:text-red-700 hover:bg-red-100/50'
                }`}
              >
                <Trash2 className="w-4 h-4" />
              </button>
            )}
          </div>
        )}
      </div>

      {/* Address Details */}
      <div className="space-y-2">
        <div className={`font-medium ${
          resolvedTheme === 'dark' ? 'text-gray-200' : 'text-gray-800'
        }`}>
          {address.street}
        </div>
        
        {address.postal_code && (
          <div className={`text-sm ${
            resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Postal Code: {address.postal_code}
          </div>
        )}

        {address.floor && (
          <div className={`text-sm ${
            resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Floor: {address.floor}
          </div>
        )}

        {address.notes && (
          <div className={`text-sm ${
            resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
          }`}>
            Notes: {address.notes}
          </div>
        )}

        {address.delivery_instructions && (
          <div className={`text-sm italic ${
            resolvedTheme === 'dark' ? 'text-blue-400' : 'text-blue-600'
          }`}>
            Delivery Instructions: {address.delivery_instructions}
          </div>
        )}
      </div>

      {/* Selection Indicator */}
      <div className="mt-4 pt-4 border-t border-gray-200/20">
        <div className={`text-center text-sm font-medium ${
          resolvedTheme === 'dark' ? 'text-blue-400' : 'text-blue-600'
        }`}>
          Click to select this address
        </div>
      </div>
    </div>
  );
}; 