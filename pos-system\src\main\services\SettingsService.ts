import Database from 'better-sqlite3';
import { BaseService } from './BaseService';

// Database row interfaces
interface SettingsRow {
  id: string;
  setting_category: SettingCategory;
  setting_key: string;
  setting_value: string;
  last_sync: string;
  created_at: string;
  updated_at: string;
}

interface POSConfigRow {
  id: string;
  terminal_id: string;
  config_key: string;
  config_value: string;
  last_sync: string;
  created_at: string;
  updated_at: string;
}

interface SettingsFilter {
  category?: SettingCategory;
  key?: string;
}

export interface LocalSettings {
  id: string;
  setting_category: string; // 'terminal', 'printer', 'tax', 'discount', 'receipt', 'payment', 'inventory', 'staff', 'restaurant'
  setting_key: string;
  setting_value: string; // JSON string
  last_sync: string;
  created_at: string;
  updated_at: string;
}

export interface POSLocalConfig {
  id: string;
  terminal_id: string;
  config_key: string;
  config_value: string;
  last_sync: string;
  created_at: string;
  updated_at: string;
}

export type SettingCategory = 
  | 'terminal' 
  | 'printer' 
  | 'tax' 
  | 'discount' 
  | 'receipt' 
  | 'payment' 
  | 'inventory' 
  | 'staff' 
  | 'restaurant';

export class SettingsService extends BaseService {
  constructor(database: Database.Database) {
    super(database);
  }

  // Local Settings Management
  setSetting(category: SettingCategory, key: string, value: unknown): void {
    this.executeTransaction(() => {
      const setting: LocalSettings = {
        id: this.generateId(),
        setting_category: category,
        setting_key: key,
        setting_value: JSON.stringify(value),
        last_sync: this.getCurrentTimestamp(),
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      };

      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO local_settings (
          id, setting_category, setting_key, setting_value, 
          last_sync, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      stmt.run(
        setting.id, setting.setting_category, setting.setting_key,
        setting.setting_value, setting.last_sync, setting.created_at,
        setting.updated_at
      );
    });
  }

  getSetting<T = unknown>(category: SettingCategory, key: string, defaultValue?: T): T | null {
    const stmt = this.db.prepare(`
      SELECT setting_value FROM local_settings 
      WHERE setting_category = ? AND setting_key = ?
    `);

    const row = stmt.get(category, key) as SettingsRow | undefined;
    
    if (!row) {
      return defaultValue !== undefined ? defaultValue : null;
    }
    
    try {
      return JSON.parse(row.setting_value);
    } catch (error) {
      console.error('Error parsing setting value:', error);
      return defaultValue !== undefined ? defaultValue : null;
    }
  }

  getAllSettings(category?: SettingCategory): LocalSettings[] {
    let query = 'SELECT * FROM local_settings';
    const params: (string | number)[] = [];

    if (category) {
      query += ' WHERE setting_category = ?';
      params.push(category);
    }

    query += ' ORDER BY setting_category, setting_key';

    const stmt = this.db.prepare(query);
    const rows = stmt.all(...params) as SettingsRow[];
    
    return rows.map(row => this.mapRowToSetting(row));
  }

  deleteSetting(category: SettingCategory, key: string): boolean {
    const stmt = this.db.prepare(`
      DELETE FROM local_settings 
      WHERE setting_category = ? AND setting_key = ?
    `);
    
    const result = stmt.run(category, key);
    return result.changes > 0;
  }

  clearCategorySettings(category: SettingCategory): number {
    const stmt = this.db.prepare(`
      DELETE FROM local_settings 
      WHERE setting_category = ?
    `);
    
    const result = stmt.run(category);
    return result.changes;
  }

  // POS Configuration Management
  setPOSConfig(terminalId: string, configKey: string, configValue: unknown): void {
    this.executeTransaction(() => {
      const config: POSLocalConfig = {
        id: this.generateId(),
        terminal_id: terminalId,
        config_key: configKey,
        config_value: JSON.stringify(configValue),
        last_sync: this.getCurrentTimestamp(),
        created_at: this.getCurrentTimestamp(),
        updated_at: this.getCurrentTimestamp()
      };

      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO pos_local_config (
          id, terminal_id, config_key, config_value, 
          last_sync, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      stmt.run(
        config.id, config.terminal_id, config.config_key,
        config.config_value, config.last_sync, config.created_at,
        config.updated_at
      );
    });
  }

  getPOSConfig<T = unknown>(terminalId: string, configKey: string, defaultValue?: T): T | null {
    const stmt = this.db.prepare(`
      SELECT config_value FROM pos_local_config 
      WHERE terminal_id = ? AND config_key = ?
    `);

    const row = stmt.get(terminalId, configKey) as POSConfigRow | undefined;
    
    if (!row) {
      return defaultValue !== undefined ? defaultValue : null;
    }
    
    try {
      return JSON.parse(row.config_value);
    } catch (error) {
      console.error('Error parsing config value:', error);
      return defaultValue !== undefined ? defaultValue : null;
    }
  }

  getAllPOSConfigs(terminalId?: string): POSLocalConfig[] {
    let query = 'SELECT * FROM pos_local_config';
    const params: (string | number)[] = [];

    if (terminalId) {
      query += ' WHERE terminal_id = ?';
      params.push(terminalId);
    }

    query += ' ORDER BY terminal_id, config_key';

    const stmt = this.db.prepare(query);
    const rows = stmt.all(...params) as POSConfigRow[];
    
    return rows.map(row => this.mapRowToPOSConfig(row));
  }

  deletePOSConfig(terminalId: string, configKey: string): boolean {
    const stmt = this.db.prepare(`
      DELETE FROM pos_local_config 
      WHERE terminal_id = ? AND config_key = ?
    `);
    
    const result = stmt.run(terminalId, configKey);
    return result.changes > 0;
  }

  clearTerminalConfigs(terminalId: string): number {
    const stmt = this.db.prepare(`
      DELETE FROM pos_local_config 
      WHERE terminal_id = ?
    `);
    
    const result = stmt.run(terminalId);
    return result.changes;
  }

  // Bulk operations for settings sync
  bulkUpdateSettings(settings: Array<{
    category: SettingCategory;
    key: string;
    value: unknown;
  }>): void {
    this.executeTransaction(() => {
      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO local_settings (
          id, setting_category, setting_key, setting_value, 
          last_sync, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      const now = this.getCurrentTimestamp();

      for (const setting of settings) {
        stmt.run(
          this.generateId(),
          setting.category,
          setting.key,
          JSON.stringify(setting.value),
          now,
          now,
          now
        );
      }
    });
  }

  bulkUpdatePOSConfigs(configs: Array<{
    terminalId: string;
    configKey: string;
    configValue: unknown;
  }>): void {
    this.executeTransaction(() => {
      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO pos_local_config (
          id, terminal_id, config_key, config_value, 
          last_sync, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      const now = this.getCurrentTimestamp();

      for (const config of configs) {
        stmt.run(
          this.generateId(),
          config.terminalId,
          config.configKey,
          JSON.stringify(config.configValue),
          now,
          now,
          now
        );
      }
    });
  }

  // Legacy compatibility methods for main.ts
  async getSettings(): Promise<Record<string, any>> {
    const settings = this.getAllSettings();
    const result: Record<string, any> = {};

    for (const setting of settings) {
      const key = `${setting.setting_category}.${setting.setting_key}`;
      try {
        result[key] = JSON.parse(setting.setting_value);
      } catch (error) {
        result[key] = setting.setting_value;
      }
    }

    return result;
  }

  async updateSettings(settings: Record<string, any>): Promise<void> {
    this.executeTransaction(() => {
      for (const [key, value] of Object.entries(settings)) {
        const [category, settingKey] = key.split('.');
        if (category && settingKey) {
          this.setSetting(category as SettingCategory, settingKey, value);
        }
      }
    });
  }

  // Main window reference for sync service compatibility
  setMainWindow(mainWindow: any): void {
    // This method is for compatibility with sync service
    // The SettingsService doesn't need the main window reference
    // but we provide this method to satisfy the interface
  }

  // Export/Import functionality
  exportSettings(): {
    local_settings: LocalSettings[];
    pos_configs: POSLocalConfig[];
  } {
    return {
      local_settings: this.getAllSettings(),
      pos_configs: this.getAllPOSConfigs()
    };
  }

  importSettings(data: {
    local_settings?: LocalSettings[];
    pos_configs?: POSLocalConfig[];
  }): void {
    this.executeTransaction(() => {
      // Import local settings
      if (data.local_settings) {
        const settingsToImport = data.local_settings.map(setting => ({
          category: setting.setting_category as SettingCategory,
          key: setting.setting_key,
          value: JSON.parse(setting.setting_value)
        }));
        
        this.bulkUpdateSettings(settingsToImport);
      }

      // Import POS configs
      if (data.pos_configs) {
        const configsToImport = data.pos_configs.map(config => ({
          terminalId: config.terminal_id,
          configKey: config.config_key,
          configValue: JSON.parse(config.config_value)
        }));
        
        this.bulkUpdatePOSConfigs(configsToImport);
      }
    });
  }

  private mapRowToSetting(row: SettingsRow): LocalSettings {
    return {
      id: row.id,
      setting_category: row.setting_category,
      setting_key: row.setting_key,
      setting_value: row.setting_value,
      last_sync: row.last_sync,
      created_at: row.created_at,
      updated_at: row.updated_at
    };
  }

  private mapRowToPOSConfig(row: POSConfigRow): POSLocalConfig {
    return {
      id: row.id,
      terminal_id: row.terminal_id,
      config_key: row.config_key,
      config_value: row.config_value,
      last_sync: row.last_sync,
      created_at: row.created_at,
      updated_at: row.updated_at
    };
  }
}