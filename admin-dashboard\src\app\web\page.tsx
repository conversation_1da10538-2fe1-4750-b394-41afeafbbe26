'use client';

import { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/theme-context';
import { 
  Globe,
  Palette,
  ShoppingCart,
  Bell,
  Shield,
  Zap,
  Settings,
  RefreshCw,
  Save,
  Eye,
  Layout,
  Users,
  CreditCard,
  MapPin,
  Clock,
  Monitor,
  Smartphone,
  MessageSquare,
  Search,
  Lock
} from 'lucide-react'
import { toast } from 'react-hot-toast'

// Types for our enhanced settings
interface WebConfiguration {
  id: string
  config_section: string
  config_key: string
  config_value: any
  data_type: string
  is_public: boolean
  requires_restart: boolean
  description?: string
  created_at: string
  updated_at: string
}

export default function WebPage() {
  const { isDarkTheme } = useTheme()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('branding')
  const [webConfigs, setWebConfigs] = useState<WebConfiguration[]>([])

  // Enhanced Web App Settings
  const [brandingSettings, setBrandingSettings] = useState({
    logo_url: '/images/logo.png',
    favicon_url: '/images/favicon.ico',
    brand_color_primary: '#F59E0B',
    brand_color_secondary: '#6B7280',
    brand_color_accent: '#EF4444',
    company_tagline: 'Delicious crepes made with love',
    font_family: 'Inter',
    theme_mode: 'light'
  })

  const [featuresSettings, setFeaturesSettings] = useState({
    enable_online_ordering: true,
    enable_user_accounts: true,
    enable_loyalty_program: true,
    enable_reviews: true,
    enable_newsletter_signup: true,
    enable_social_login: true,
    enable_animations: true
  })

  const [contentSettings, setContentSettings] = useState({
    homepage_hero_title: 'Welcome to Delicious Bites',
    homepage_hero_subtitle: 'Fresh crepes and delicious treats',
    contact_phone: '+****************',
    contact_email: '<EMAIL>',
    about_us_text: 'We are passionate about serving the best crepes in town'
  })

  const [seoSettings, setSeoSettings] = useState({
    meta_title: 'Delicious Bites - Fresh Crepes & More',
    meta_description: 'Order fresh crepes, waffles, and delicious treats online',
    meta_keywords: 'crepes, waffles, food delivery, online ordering'
  })

  const [orderingSettings, setOrderingSettings] = useState({
    enable_online_ordering: true,
    enable_delivery: true,
    enable_pickup: true,
    enable_scheduling: true,
    min_order_amount: 15.00,
    max_order_amount: 500.00,
    delivery_radius: 10,
    pickup_time_slots: '15,30,45,60',
    delivery_time_slots: '30,45,60,90',
    auto_accept_orders: false,
    order_confirmation_required: true,
    guest_checkout_enabled: true
  })

  const [paymentSettings, setPaymentSettings] = useState({
    stripe_enabled: true,
    paypal_enabled: false,
    apple_pay_enabled: true,
    google_pay_enabled: true,
    cash_on_delivery: true,
    card_on_delivery: false,
    tip_suggestions: '15,18,20,25',
    service_fee_percentage: 0,
    processing_fee_percentage: 2.9,
    minimum_tip_amount: 1.00
  })


  const [notificationSettings, setNotificationSettings] = useState({
    email_notifications: true,
    sms_notifications: false,
    push_notifications: true,
    order_status_emails: true,
    marketing_emails: false,
    abandoned_cart_emails: true,
    review_request_emails: true,
    newsletter_signup_popup: true,
    notification_sound: true,
    desktop_notifications: true
  })

  const [securitySettings, setSecuritySettings] = useState({
    ssl_enabled: true,
    rate_limiting_enabled: true,
    captcha_enabled: false,
    two_factor_auth: false,
    session_timeout: 3600,
    password_requirements: 'medium',
    data_retention_days: 365,
    cookie_consent_required: true,
    privacy_policy_url: '',
    terms_of_service_url: ''
  })

  useEffect(() => {
    loadWebSettings()
  }, [])

  const loadWebSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/web-configurations')
      const result = await response.json()
      
      if (response.ok) {
        setWebConfigs(result.data)
        
        // Load existing configurations into state
        result.data.forEach((config: WebConfiguration) => {
          const key = config.config_key
          const value = config.config_value

          // Update respective settings based on section
          switch (config.config_section) {
            case 'branding':
              setBrandingSettings(prev => ({ ...prev, [key]: value }))
              break
            case 'features':
              setFeaturesSettings(prev => ({ ...prev, [key]: value }))
              break
            case 'content':
              setContentSettings(prev => ({ ...prev, [key]: value }))
              break
            case 'seo':
              setSeoSettings(prev => ({ ...prev, [key]: value }))
              break
            case 'ordering':
              setOrderingSettings(prev => ({ ...prev, [key]: value }))
              break
            case 'payment':
              setPaymentSettings(prev => ({ ...prev, [key]: value }))
              break
            case 'notifications':
              setNotificationSettings(prev => ({ ...prev, [key]: value }))
              break
            case 'security':
              setSecuritySettings(prev => ({ ...prev, [key]: value }))
              break
          }
        })
      } else {
        toast.error('Failed to load web settings')
      }
    } catch (error) {
      console.error('Error loading web settings:', error)
      toast.error('Error loading web settings')
    } finally {
      setLoading(false)
    }
  }

  const saveWebConfiguration = async (section: string, settings: any) => {
    try {
      setSaving(true)
      
      // Convert settings to the format expected by the API
      const configurations = Object.entries(settings).map(([key, value]) => ({
        key,
        value,
        data_type: typeof value === 'boolean' ? 'boolean' : 
                  typeof value === 'number' ? 'number' : 'string',
        description: `${section} setting for ${key}`,
        is_public: true
      }))
      
      const response = await fetch('/api/web-configurations/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section,
          configurations
        })
      })
      
      const result = await response.json()
      
      if (response.ok) {
        toast.success(`${section} settings saved successfully!`)
        await loadWebSettings() // Reload to get updated data
      } else {
        throw new Error(result.error || 'Failed to save settings')
      }
      
    } catch (error) {
      console.error('Error saving web configuration:', error)
      toast.error('Failed to save settings')
    } finally {
      setSaving(false)
    }
  }

  const tabs = [
    { id: 'branding', name: 'Branding & Theme', icon: Palette },
    { id: 'features', name: 'Features & Toggles', icon: Zap },
    { id: 'content', name: 'Content Management', icon: MessageSquare },
    { id: 'seo', name: 'SEO & Meta Tags', icon: Search },
    { id: 'ordering', name: 'Ordering Settings', icon: ShoppingCart },
    { id: 'payment', name: 'Payment Options', icon: CreditCard },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'security', name: 'Security & Privacy', icon: Shield },
    { id: 'preview', name: 'Live Preview', icon: Eye }
  ]

  if (loading) {
    return (
      <>
        <div className="p-8 flex items-center justify-center">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-500" />
            <p className="text-gray-600 dark:text-gray-400">Loading Web App Configuration...</p>
          </div>
        </div>
      </>
    )
  }

  return (
      <div className="p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8">
        {/* Header */}
        <div>
          <h1 className={`text-3xl font-bold transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>Customer Web App Settings</h1>
          <p className={`mt-1 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>
            Configure your customer-facing web application with branding, features, and content management
          </p>
        </div>
        
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-end mb-8">
            <div>
              <div className="flex space-x-3">
                <button
                  onClick={() => loadWebSettings()}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </button>
                <button
                  onClick={() => window.open('https://your-restaurant-web.com', '_blank')}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Live Site
                </button>
              </div>
            </div>
          </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:w-64">
            <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
              isDarkTheme
                ? 'bg-white/10 border-white/20'
                : 'bg-black/10 border-black/20'
            }`}>
              <div className="space-y-3">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-4 py-3 rounded-xl text-left transition-all duration-300 ${
                        activeTab === tab.id
                          ? isDarkTheme
                            ? 'bg-white/20 border border-white/30 text-white'
                            : 'bg-black/20 border border-black/30 text-black'
                          : isDarkTheme
                            ? 'text-white/70 hover:bg-white/10 hover:text-white'
                            : 'text-black/70 hover:bg-black/10 hover:text-black'
                      }`}
                    >
                      <Icon className="h-5 w-5 mr-3" />
                      {tab.name}
                    </button>
                  )
                })}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Branding & Theme Tab */}
            {activeTab === 'branding' && (
              <div className="backdrop-blur-md bg-white/10 border border-white/20 rounded-2xl p-8">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-3xl font-bold text-white">Branding & Theme</h2>
                </div>

                <div className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Primary Brand Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={brandingSettings.brand_color_primary}
                          onChange={(e) => setBrandingSettings(prev => ({
                            ...prev,
                            brand_color_primary: e.target.value
                          }))}
                          className="w-12 h-10 rounded-xl border-2 border-white/20 bg-transparent"
                        />
                        <input
                          type="text"
                          value={brandingSettings.brand_color_primary}
                          onChange={(e) => setBrandingSettings(prev => ({
                            ...prev,
                            brand_color_primary: e.target.value
                          }))}
                          className="flex-1 px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                          placeholder="#F59E0B"
                        />
                      </div>
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Secondary Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={brandingSettings.brand_color_secondary}
                          onChange={(e) => setBrandingSettings(prev => ({
                            ...prev,
                            brand_color_secondary: e.target.value
                          }))}
                          className="w-12 h-10 rounded-xl border-2 border-white/20 bg-transparent"
                        />
                        <input
                          type="text"
                          value={brandingSettings.brand_color_secondary}
                          onChange={(e) => setBrandingSettings(prev => ({
                            ...prev,
                            brand_color_secondary: e.target.value
                          }))}
                          className="flex-1 px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                          placeholder="#6B7280"
                        />
                      </div>
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Logo URL
                      </label>
                      <input
                        type="url"
                        value={brandingSettings.logo_url}
                        onChange={(e) => setBrandingSettings(prev => ({
                          ...prev,
                          logo_url: e.target.value
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                        placeholder="https://your-domain.com/logo.png"
                      />
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Favicon URL
                      </label>
                      <input
                        type="url"
                        value={brandingSettings.favicon_url}
                        onChange={(e) => setBrandingSettings(prev => ({
                          ...prev,
                          favicon_url: e.target.value
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                        placeholder="https://your-domain.com/favicon.ico"
                      />
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Font Family
                      </label>
                      <select
                        value={brandingSettings.font_family}
                        onChange={(e) => setBrandingSettings(prev => ({
                          ...prev,
                          font_family: e.target.value
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                      >
                        <option value="Inter" className="bg-gray-800">Inter</option>
                        <option value="Roboto" className="bg-gray-800">Roboto</option>
                        <option value="Open Sans" className="bg-gray-800">Open Sans</option>
                        <option value="Lato" className="bg-gray-800">Lato</option>
                        <option value="Poppins" className="bg-gray-800">Poppins</option>
                        <option value="Montserrat" className="bg-gray-800">Montserrat</option>
                      </select>
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Accent Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={brandingSettings.brand_color_accent}
                          onChange={(e) => setBrandingSettings(prev => ({
                            ...prev,
                            brand_color_accent: e.target.value
                          }))}
                          className="w-12 h-10 rounded-xl border-2 border-white/20 bg-transparent"
                        />
                        <input
                          type="text"
                          value={brandingSettings.brand_color_accent}
                          onChange={(e) => setBrandingSettings(prev => ({
                            ...prev,
                            brand_color_accent: e.target.value
                          }))}
                          className="flex-1 px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                          placeholder="#EF4444"
                        />
                      </div>
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Company Tagline
                      </label>
                      <input
                        type="text"
                        value={brandingSettings.company_tagline}
                        onChange={(e) => setBrandingSettings(prev => ({
                          ...prev,
                          company_tagline: e.target.value
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                        placeholder="Delicious crepes made with love"
                      />
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Theme Mode
                      </label>
                      <select
                        value={brandingSettings.theme_mode}
                        onChange={(e) => setBrandingSettings(prev => ({
                          ...prev,
                          theme_mode: e.target.value
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                      >
                        <option value="light" className="bg-gray-800">Light</option>
                        <option value="dark" className="bg-gray-800">Dark</option>
                        <option value="auto" className="bg-gray-800">Auto</option>
                      </select>
                    </div>
                  </div>


                  <div className="flex justify-end">
                    <button
                      onClick={() => saveWebConfiguration('branding', brandingSettings)}
                      disabled={saving}
                      className="flex items-center px-8 py-4 backdrop-blur-md bg-gradient-to-r from-blue-500/20 to-purple-600/20 border border-white/30 text-white rounded-2xl hover:from-blue-500/30 hover:to-purple-600/30 transition-all duration-300 disabled:opacity-50"
                    >
                      {saving ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
                      Save Branding Settings
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Ordering Settings Tab */}
            {activeTab === 'ordering' && (
              <div className="backdrop-blur-md bg-white/10 border border-white/20 rounded-2xl p-8">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-3xl font-bold text-white">Ordering Settings</h2>
                </div>

                <div className="space-y-8">
                  <div className="space-y-6">
                    <h3 className="text-xl font-semibold text-white">Order Types</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {[
                        { key: 'enable_online_ordering', label: 'Online Ordering', desc: 'Allow customers to place orders online' },
                        { key: 'enable_delivery', label: 'Delivery Service', desc: 'Offer delivery to customers' },
                        { key: 'enable_pickup', label: 'Pickup Orders', desc: 'Allow customers to pick up orders' },
                        { key: 'enable_scheduling', label: 'Scheduled Orders', desc: 'Allow customers to schedule orders for later' },
                        { key: 'auto_accept_orders', label: 'Auto-Accept Orders', desc: 'Automatically accept incoming orders' },
                        { key: 'order_confirmation_required', label: 'Order Confirmation', desc: 'Require email confirmation for orders' },
                        { key: 'guest_checkout_enabled', label: 'Guest Checkout', desc: 'Allow ordering without account creation' },
                      ].map((option) => (
                        <div key={option.key} className="flex items-center justify-between p-6 backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl">
                          <div>
                            <h4 className="font-medium text-white">{option.label}</h4>
                            <p className="text-sm text-white/60">{option.desc}</p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={orderingSettings[option.key as keyof typeof orderingSettings] as boolean}
                              onChange={(e) => setOrderingSettings(prev => ({
                                ...prev,
                                [option.key]: e.target.checked
                              }))}
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-white/20 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600/80 backdrop-blur-md"></div>
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Minimum Order Amount ($)
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        value={orderingSettings.min_order_amount}
                        onChange={(e) => setOrderingSettings(prev => ({
                          ...prev,
                          min_order_amount: parseFloat(e.target.value) || 0
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                      />
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Maximum Order Amount ($)
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        value={orderingSettings.max_order_amount}
                        onChange={(e) => setOrderingSettings(prev => ({
                          ...prev,
                          max_order_amount: parseFloat(e.target.value) || 0
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                      />
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Delivery Radius (miles)
                      </label>
                      <input
                        type="number"
                        value={orderingSettings.delivery_radius}
                        onChange={(e) => setOrderingSettings(prev => ({
                          ...prev,
                          delivery_radius: parseInt(e.target.value) || 0
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                      />
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Pickup Time Slots (minutes)
                      </label>
                      <input
                        type="text"
                        value={orderingSettings.pickup_time_slots}
                        onChange={(e) => setOrderingSettings(prev => ({
                          ...prev,
                          pickup_time_slots: e.target.value
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                        placeholder="15,30,45,60"
                      />
                      <p className="text-xs text-white/40 mt-2">Comma-separated values in minutes</p>
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Delivery Time Slots (minutes)
                      </label>
                      <input
                        type="text"
                        value={orderingSettings.delivery_time_slots}
                        onChange={(e) => setOrderingSettings(prev => ({
                          ...prev,
                          delivery_time_slots: e.target.value
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                        placeholder="30,45,60,90"
                      />
                      <p className="text-xs text-white/40 mt-2">Comma-separated values in minutes</p>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={() => saveWebConfiguration('ordering', orderingSettings)}
                      disabled={saving}
                      className="flex items-center px-8 py-4 backdrop-blur-md bg-gradient-to-r from-blue-500/20 to-purple-600/20 border border-white/30 text-white rounded-2xl hover:from-blue-500/30 hover:to-purple-600/30 transition-all duration-300 disabled:opacity-50"
                    >
                      {saving ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
                      Save Ordering Settings
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Additional tabs would be implemented here with the same glassmorphism styling... */}
          </div>
        </div>
      </div>
    </div>
  )
}