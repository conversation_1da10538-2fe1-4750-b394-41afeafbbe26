/**
 * Core application types for the customer-web project
 */

// User and Authentication Types
export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
  role?: 'customer' | 'admin';
  emailVerified?: boolean;
}

export interface UserProfile extends User {
  addresses?: Address[];
  paymentMethods?: PaymentMethod[];
  favorites?: MenuItem[];
  orders?: Order[];
  preferences?: UserPreferences;
}

export interface UserPreferences {
  language?: string;
  theme?: 'light' | 'dark' | 'system';
  notifications?: {
    push?: boolean;
    email?: boolean;
    sms?: boolean;
    orderUpdates?: boolean;
    promotions?: boolean;
    newsletter?: boolean;
  };
}

// Menu and Product Types
export interface Category {
  id: string;
  name: string;
  description?: string;
  image_url?: string;
  slug: string;
  order?: number;
  created_at: string;
  updated_at: string;
}

export interface MenuItem {
  id: string;
  name: string;
  description?: string;
  price: number;
  pickup_price?: number;
  delivery_price?: number;
  image_url?: string;
  category_id: string;
  category?: Category;
  ingredients?: string[];
  allergens?: string[];
  nutritionalInfo?: NutritionalInfo;
  isVegetarian?: boolean;
  isVegan?: boolean;
  isGlutenFree?: boolean;
  isDairyFree?: boolean;
  isNutFree?: boolean;
  variants?: MenuItemVariant[];
  available: boolean;
  featured?: boolean;
  slug: string;
  is_customizable?: boolean;
  max_ingredients?: number;
  base_price?: number;
  created_at: string;
  updated_at: string;
}

export interface MenuItemVariant {
  id: string;
  name: string;
  price: number;
  description?: string;
}

export interface NutritionalInfo {
  calories?: number;
  protein?: number;
  carbs?: number;
  fat?: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
}

// Order Types
export interface CartItem {
  id: string;
  menuItem: MenuItem;
  quantity: number;
  variant?: MenuItemVariant;
  specialInstructions?: string;
  price: number; // Price including variant if selected
  customizations?: Array<{
    ingredient: {
      id: string;
      name: string;
      price: number;
      pickup_price: number;
      delivery_price: number;
    };
    quantity: number;
  }>;
  orderType?: 'pickup' | 'delivery';
}

export interface Cart {
  items: CartItem[];
  subtotal: number;
  tax: number;
  deliveryFee: number;
  tip?: number;
  total: number;
}

export interface Order {
  id: string;
  user_id: string;
  items: OrderItem[];
  status: OrderStatus;
  subtotal: number;
  tax: number;
  deliveryFee: number;
  tip?: number;
  total: number;
  paymentMethod: PaymentMethod;
  paymentStatus: 'pending' | 'paid' | 'failed';
  deliveryMethod: 'delivery' | 'pickup';
  deliveryAddress?: Address;
  deliveryInstructions?: string;
  deliveryTime?: string;
  created_at: string;
  updated_at: string;
  estimated_delivery_time?: string;
}

export interface OrderItem {
  id: string;
  order_id: string;
  subcategory_id: string;
  menuItem?: MenuItem;
  quantity: number;
  price: number;
  variant_name?: string;
  variant_price?: number;
  specialInstructions?: string;
  created_at: string;
  updated_at: string;
}

export type OrderStatus =
  | 'pending'
  | 'confirmed'
  | 'preparing'
  | 'ready'
  | 'out_for_delivery'
  | 'delivered'
  | 'completed'
  | 'cancelled';

// Address and Payment Types
export interface Address {
  id: string;
  user_id: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault?: boolean;
  label?: 'home' | 'work' | 'other';
  created_at: string;
  updated_at: string;
}

export interface PaymentMethod {
  id: string;
  user_id: string;
  type: 'card' | 'cash' | 'paypal';
  // For cards
  cardBrand?: string;
  last4?: string;
  expiryMonth?: string;
  expiryYear?: string;
  cardholderName?: string;
  isDefault?: boolean;
  created_at: string;
  updated_at: string;
}

// Delivery Zone Types
export interface DeliveryZone {
  id: string;
  name: string;
  description?: string;
  postalCodes: string[];
  deliveryFee: number;
  minimumOrderAmount: number;
  estimatedDeliveryTime: string;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface GeoCoordinate {
  lat: number;
  lng: number;
}

// UI Component Types
export interface GlassVariant {
  variant:
    | 'primary'
    | 'secondary'
    | 'success'
    | 'danger'
    | 'warning'
    | 'info'
    | 'crepe'
    | 'chocolate'
    | 'strawberry'
    | 'blueberry';
}

export interface GlassCardProps extends GlassVariant {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export interface GlassButtonProps extends GlassVariant {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

export interface GlassInputProps extends GlassVariant {
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  name?: string;
  id?: string;
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

export interface GlassModalProps extends GlassVariant {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
}

// PWA Types
export interface PWAInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed'; platform: string }>;
}

// Notification Types
export interface PushSubscription {
  endpoint: string;
  expirationTime: number | null;
  keys: {
    p256dh: string;
    auth: string;
  };
}

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  body: string;
  icon?: string;
  image?: string;
  data?: any;
  read: boolean;
  created_at: string;
}

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  error?: {
    message: string;
    code?: string;
    status?: number;
  };
  status: 'success' | 'error';
}

// Theme Types
export type ThemeMode = 'light' | 'dark' | 'system';

// Language Types
export type Language = 'en' | 'el';
