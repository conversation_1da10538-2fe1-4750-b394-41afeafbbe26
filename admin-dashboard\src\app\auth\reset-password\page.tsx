'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useSearch<PERSON>ara<PERSON> } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import Link from 'next/link'

import {
  GlassCard as Card,
  GlassCardContent as CardContent,
  GlassCardDescription as CardDescription,
  GlassCardHeader as CardHeader,
  GlassCardTitle as CardTitle,
} from '@/components/ui/glass-components'
import { GlassButton as Button } from '@/components/ui/glass-components'
import { GlassInput as Input } from '@/components/ui/glass-components'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { requestPasswordReset, resetPassword } from '../../../../../shared/auth/auth-utils'

const resetRequestSchema = z.object({
  email: z.string().email('Invalid email address').min(1, 'Email is required'),
})

const resetPasswordSchema = z
  .object({
    password: z.string().min(8, 'Password must be at least 8 characters'),
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  })

type ResetRequestFormData = z.infer<typeof resetRequestSchema>
type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>

export default function ResetPasswordPage() {
  const { t } = useTranslation()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isResetMode, setIsResetMode] = useState(false)

  const token = searchParams.get('token')
  const email = searchParams.get('email')

  // Determine if we're in reset mode (have token) or request mode
  useState(() => {
    if (token && email) {
      setIsResetMode(true)
    }
  })

  const requestForm = useForm<ResetRequestFormData>({
    resolver: zodResolver(resetRequestSchema),
    defaultValues: {
      email: email || '',
    },
  })

  const resetForm = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  })

  const onRequestSubmit = async (data: ResetRequestFormData) => {
    try {
      setIsLoading(true)
      setError(null)
      setSuccess(null)

      const result = await requestPasswordReset({
        email: data.email,
        platform: 'admin-dashboard',
      })

      if (!result.success) {
        setError(result.error || 'Failed to send reset email')
        return
      }

      setSuccess(t('auth.resetPassword.success'))
    } catch (err) {
      console.error('Password reset request error:', err)
      setError(t('auth.resetPassword.errors.resetFailed'))
    } finally {
      setIsLoading(false)
    }
  }

  const onResetSubmit = async (data: ResetPasswordFormData) => {
    if (!token || !email) {
      setError('Invalid reset link')
      return
    }

    try {
      setIsLoading(true)
      setError(null)
      setSuccess(null)

      const result = await resetPassword({
        new_password: data.password,
      })

      if (!result.success) {
        setError(result.error || t('auth.resetPassword.errors.resetFailed'))
        return
      }

      setSuccess('Password updated successfully')

      // Redirect to login after successful reset
      setTimeout(() => {
        router.push('/auth/login')
      }, 2000)
    } catch (err) {
      console.error('Password reset error:', err)
      setError(t('auth.resetPassword.errors.resetFailed'))
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-black py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card variant="secondary">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center text-white">
              {t('auth.resetPassword.title')}
            </CardTitle>
            <CardDescription className="text-center text-gray-200">
              {isResetMode ? 'Enter your new password below' : t('auth.resetPassword.subtitle')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {!isResetMode ? (
              // Request Reset Form
              <Form {...requestForm}>
                <form onSubmit={requestForm.handleSubmit(onRequestSubmit)} className="space-y-4">
                  <FormField
                    control={requestForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('auth.resetPassword.email')}</FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            {...field}
                            disabled={isLoading}
                            autoComplete="email"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {error && (
                    <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">{error}</div>
                  )}

                  {success && (
                    <div className="text-sm text-green-600 bg-green-50 p-3 rounded-md">
                      {success}
                    </div>
                  )}

                  <Button type="submit" className="w-full" disabled={isLoading} variant="secondary">
                    {isLoading ? t('common.loading') : t('auth.resetPassword.sendReset')}
                  </Button>

                  <div className="text-center">
                    <Link href="/auth/login" className="text-sm text-blue-600 hover:text-blue-500">
                      {t('auth.resetPassword.backToLogin')}
                    </Link>
                  </div>
                </form>
              </Form>
            ) : (
              // Reset Password Form
              <Form {...resetForm}>
                <form onSubmit={resetForm.handleSubmit(onResetSubmit)} className="space-y-4">
                  <FormField
                    control={resetForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('auth.resetPassword.newPassword')}</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="••••••••"
                            {...field}
                            disabled={isLoading}
                            autoComplete="new-password"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={resetForm.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('auth.resetPassword.confirmPassword')}</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="••••••••"
                            {...field}
                            disabled={isLoading}
                            autoComplete="new-password"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {error && (
                    <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">{error}</div>
                  )}

                  {success && (
                    <div className="text-sm text-green-600 bg-green-50 p-3 rounded-md">
                      {success}
                    </div>
                  )}

                  <Button type="submit" className="w-full" disabled={isLoading} variant="secondary">
                    {isLoading ? t('common.loading') : t('auth.resetPassword.updatePassword')}
                  </Button>

                  <div className="text-center">
                    <Link href="/auth/login" className="text-sm text-blue-600 hover:text-blue-500">
                      {t('auth.resetPassword.backToLogin')}
                    </Link>
                  </div>
                </form>
              </Form>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
