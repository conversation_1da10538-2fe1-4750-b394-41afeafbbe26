import { NextRequest, NextResponse } from 'next/server';

const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY || '';

async function searchGooglePlacesAPI(query: string, location?: { latitude: number; longitude: number }, radius?: number) {
  try {
    // Build the API URL for Google Places API Text Search
    const baseUrl = 'https://maps.googleapis.com/maps/api/place/textsearch/json';
    const params = new URLSearchParams({
      query: query,
      key: GOOGLE_MAPS_API_KEY,
      language: 'en',
      region: 'gr' // Greece region bias
    });

    // Add location bias if provided
    if (location && location.latitude && location.longitude) {
      params.append('location', `${location.latitude},${location.longitude}`);
      if (radius) {
        params.append('radius', radius.toString());
      }
    }


    const response = await fetch(`${baseUrl}?${params.toString()}`);
    
    if (!response.ok) {
      throw new Error(`Google Places API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
      throw new Error(`Google Places API status: ${data.status} - ${data.error_message || 'Unknown error'}`);
    }

    // Transform Google Places API response to our format
    const places = (data.results || []).slice(0, 5).map((place: any) => ({
      place_id: place.place_id,
      name: place.name,
      formatted_address: place.formatted_address,
      location: {
        lat: place.geometry?.location?.lat || 0,
        lng: place.geometry?.location?.lng || 0
      },
      types: place.types || []
    }));

    return places;
  } catch (error) {
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { query, location, radius } = await request.json();

    if (!query) {
      return NextResponse.json(
        { error: 'Missing query parameter' },
        { status: 400 }
      );
    }

    if (!GOOGLE_MAPS_API_KEY) {
      return NextResponse.json(
        { error: 'Google Maps API key not configured' },
        { status: 500 }
      );
    }

    try {
      // Use real Google Places API
      const places = await searchGooglePlacesAPI(query, location, radius);
      
      return NextResponse.json({ places });
    } catch (mapsError) {
      const errorMessage = mapsError instanceof Error ? mapsError.message : 'Unknown error';
      return NextResponse.json(
        { error: `Failed to search places: ${errorMessage}` },
        { status: 500 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to search places' },
      { status: 500 }
    );
  }
} 