{"name": "@creperie/admin-dashboard", "version": "0.1.0", "description": "Admin dashboard for Creperie management system - SaaS platform for restaurant operations", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS=--no-deprecation next dev -p 3001", "build": "cross-env NODE_OPTIONS=--no-deprecation next build", "start": "next start -p 3001", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "clean": "rm -rf .next out dist", "setup:google-maps": "node setup-google-maps.js"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.0", "@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-dropdown-menu": "^2.1.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.0", "@radix-ui/react-select": "^2.1.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.0", "@radix-ui/react-tooltip": "^1.1.0", "@simplewebauthn/browser": "^13.1.0", "@simplewebauthn/server": "^13.1.1", "@simplewebauthn/types": "^12.0.0", "@simplewebauthn/typescript-types": "^8.3.4", "@stagewise-plugins/react": "^0.4.9", "@stagewise/toolbar-next": "^0.4.9", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.45.0", "@tanstack/react-query": "^5.51.0", "@tanstack/react-table": "^8.20.0", "@types/google.maps": "^3.58.1", "@types/qrcode": "^1.5.5", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "i18next": "^23.12.0", "i18next-browser-languagedetector": "^8.0.0", "lucide-react": "^0.427.0", "next": "^15.3.3", "next-themes": "^0.3.0", "qrcode": "^1.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.52.0", "react-hot-toast": "^2.4.0", "react-i18next": "^15.0.0", "recharts": "^2.15.3", "tailwind-merge": "^2.5.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.23.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.0", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "20.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "cypress": "^14.5.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.41", "prettier": "^3.3.0", "supabase": "^2.26.9", "tailwindcss": "^3.4.0", "typescript": "^5.5.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["nextjs", "react", "typescript", "tailwindcss", "shadcn-ui", "supabase", "admin-dashboard", "creperie", "restaurant-management"]}