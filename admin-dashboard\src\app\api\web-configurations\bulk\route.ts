import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const body = await request.json()
    const { section, configurations, branch_id } = body

    if (!section || !configurations || !Array.isArray(configurations)) {
      return NextResponse.json(
        { error: 'Section and configurations array are required' },
        { status: 400 }
      )
    }

    const upsertData = configurations.map(config => ({
      config_section: section,
      config_key: config.key,
      config_value: config.value,
      data_type: config.data_type || 'string',
      description: config.description || '',
      branch_id: branch_id || null,
      is_public: config.is_public !== false,
      requires_restart: config.requires_restart || false
    }))

    const { data, error } = await supabase
      .from('web_configurations')
      .upsert(upsertData, {
        onConflict: 'config_section,config_key,branch_id',
        ignoreDuplicates: false
      })
      .select()

    if (error) {
      console.error('Error bulk upserting web configurations:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ 
      data,
      message: `Successfully updated ${data.length} configurations for ${section} section`
    })
  } catch (error) {
    console.error('Unexpected error in bulk upsert:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const body = await request.json()
    const { updates } = body

    if (!updates || !Array.isArray(updates)) {
      return NextResponse.json(
        { error: 'Updates array is required' },
        { status: 400 }
      )
    }

    const results = []
    
    for (const update of updates) {
      const { id, config_value, description, is_public, requires_restart } = update
      
      if (!id) {
        continue
      }

      const { data, error } = await supabase
        .from('web_configurations')
        .update({
          config_value,
          description,
          is_public,
          requires_restart,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error(`Error updating configuration ${id}:`, error)
        results.push({ id, error: error.message })
      } else {
        results.push({ id, data })
      }
    }

    return NextResponse.json({ 
      results,
      message: `Processed ${results.length} configuration updates`
    })
  } catch (error) {
    console.error('Unexpected error in bulk update:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}