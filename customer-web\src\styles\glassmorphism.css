:root {
  /* Base glassmorphism variables */
  --glass-background: rgba(255, 255, 255, 0.15);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-blur: 8px;
  --glass-border-radius: 10px;
  --glass-transition: all 0.3s ease;

  /* Variant-specific variables */
  --glass-primary-background: rgba(255, 193, 7, 0.15);
  --glass-primary-border: rgba(255, 193, 7, 0.3);
  --glass-primary-shadow: 0 8px 32px 0 rgba(255, 193, 7, 0.2);

  --glass-secondary-background: rgba(108, 117, 125, 0.15);
  --glass-secondary-border: rgba(108, 117, 125, 0.3);
  --glass-secondary-shadow: 0 8px 32px 0 rgba(108, 117, 125, 0.2);

  --glass-success-background: rgba(40, 167, 69, 0.15);
  --glass-success-border: rgba(40, 167, 69, 0.3);
  --glass-success-shadow: 0 8px 32px 0 rgba(40, 167, 69, 0.2);

  --glass-danger-background: rgba(220, 53, 69, 0.15);
  --glass-danger-border: rgba(220, 53, 69, 0.3);
  --glass-danger-shadow: 0 8px 32px 0 rgba(220, 53, 69, 0.2);

  --glass-warning-background: rgba(255, 193, 7, 0.15);
  --glass-warning-border: rgba(255, 193, 7, 0.3);
  --glass-warning-shadow: 0 8px 32px 0 rgba(255, 193, 7, 0.2);

  --glass-info-background: rgba(23, 162, 184, 0.15);
  --glass-info-border: rgba(23, 162, 184, 0.3);
  --glass-info-shadow: 0 8px 32px 0 rgba(23, 162, 184, 0.2);

  /* Creperie theme glass variables */
  --glass-crepe-background: rgba(188, 143, 143, 0.15);
  --glass-crepe-border: rgba(188, 143, 143, 0.3);
  --glass-crepe-shadow: 0 8px 32px 0 rgba(188, 143, 143, 0.2);

  --glass-chocolate-background: rgba(139, 69, 19, 0.15);
  --glass-chocolate-border: rgba(139, 69, 19, 0.3);
  --glass-chocolate-shadow: 0 8px 32px 0 rgba(139, 69, 19, 0.2);

  --glass-strawberry-background: rgba(220, 20, 60, 0.15);
  --glass-strawberry-border: rgba(220, 20, 60, 0.3);
  --glass-strawberry-shadow: 0 8px 32px 0 rgba(220, 20, 60, 0.2);

  --glass-blueberry-background: rgba(65, 105, 225, 0.15);
  --glass-blueberry-border: rgba(65, 105, 225, 0.3);
  --glass-blueberry-shadow: 0 8px 32px 0 rgba(65, 105, 225, 0.2);
}

.dark {
  /* Dark mode adjustments */
  --glass-background: rgba(0, 0, 0, 0.25);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);

  --glass-primary-background: rgba(255, 193, 7, 0.1);
  --glass-primary-border: rgba(255, 193, 7, 0.2);
  --glass-primary-shadow: 0 8px 32px 0 rgba(255, 193, 7, 0.15);

  --glass-secondary-background: rgba(108, 117, 125, 0.1);
  --glass-secondary-border: rgba(108, 117, 125, 0.2);
  --glass-secondary-shadow: 0 8px 32px 0 rgba(108, 117, 125, 0.15);

  --glass-success-background: rgba(40, 167, 69, 0.1);
  --glass-success-border: rgba(40, 167, 69, 0.2);
  --glass-success-shadow: 0 8px 32px 0 rgba(40, 167, 69, 0.15);

  --glass-danger-background: rgba(220, 53, 69, 0.1);
  --glass-danger-border: rgba(220, 53, 69, 0.2);
  --glass-danger-shadow: 0 8px 32px 0 rgba(220, 53, 69, 0.15);

  --glass-warning-background: rgba(255, 193, 7, 0.1);
  --glass-warning-border: rgba(255, 193, 7, 0.2);
  --glass-warning-shadow: 0 8px 32px 0 rgba(255, 193, 7, 0.15);

  --glass-info-background: rgba(23, 162, 184, 0.1);
  --glass-info-border: rgba(23, 162, 184, 0.2);
  --glass-info-shadow: 0 8px 32px 0 rgba(23, 162, 184, 0.15);

  /* Creperie theme glass variables - dark mode */
  --glass-crepe-background: rgba(188, 143, 143, 0.1);
  --glass-crepe-border: rgba(188, 143, 143, 0.2);
  --glass-crepe-shadow: 0 8px 32px 0 rgba(188, 143, 143, 0.15);

  --glass-chocolate-background: rgba(139, 69, 19, 0.1);
  --glass-chocolate-border: rgba(139, 69, 19, 0.2);
  --glass-chocolate-shadow: 0 8px 32px 0 rgba(139, 69, 19, 0.15);

  --glass-strawberry-background: rgba(220, 20, 60, 0.1);
  --glass-strawberry-border: rgba(220, 20, 60, 0.2);
  --glass-strawberry-shadow: 0 8px 32px 0 rgba(220, 20, 60, 0.15);

  --glass-blueberry-background: rgba(65, 105, 225, 0.1);
  --glass-blueberry-border: rgba(65, 105, 225, 0.2);
  --glass-blueberry-shadow: 0 8px 32px 0 rgba(65, 105, 225, 0.15);
}

/* Base glass container class */
.glass-container {
  background: var(--glass-background);
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid var(--glass-border);
  border-radius: var(--glass-border-radius);
  box-shadow: var(--glass-shadow);
  transition: var(--glass-transition);
}

/* Glass card component */
.glass-card {
  @apply glass-container p-4 relative overflow-hidden;
}

/* Glass card variants */
.glass-primary {
  background: var(--glass-primary-background);
  border: 1px solid var(--glass-primary-border);
  box-shadow: var(--glass-primary-shadow);
}

.glass-secondary {
  background: var(--glass-secondary-background);
  border: 1px solid var(--glass-secondary-border);
  box-shadow: var(--glass-secondary-shadow);
}

.glass-success {
  background: var(--glass-success-background);
  border: 1px solid var(--glass-success-border);
  box-shadow: var(--glass-success-shadow);
}

.glass-danger {
  background: var(--glass-danger-background);
  border: 1px solid var(--glass-danger-border);
  box-shadow: var(--glass-danger-shadow);
}

.glass-warning {
  background: var(--glass-warning-background);
  border: 1px solid var(--glass-warning-border);
  box-shadow: var(--glass-warning-shadow);
}

.glass-info {
  background: var(--glass-info-background);
  border: 1px solid var(--glass-info-border);
  box-shadow: var(--glass-info-shadow);
}

/* Creperie theme glass variants */
.glass-crepe {
  background: var(--glass-crepe-background);
  border: 1px solid var(--glass-crepe-border);
  box-shadow: var(--glass-crepe-shadow);
}

.glass-chocolate {
  background: var(--glass-chocolate-background);
  border: 1px solid var(--glass-chocolate-border);
  box-shadow: var(--glass-chocolate-shadow);
}

.glass-strawberry {
  background: var(--glass-strawberry-background);
  border: 1px solid var(--glass-strawberry-border);
  box-shadow: var(--glass-strawberry-shadow);
}

.glass-blueberry {
  background: var(--glass-blueberry-background);
  border: 1px solid var(--glass-blueberry-border);
  box-shadow: var(--glass-blueberry-shadow);
}

/* Interactive states */
.glass-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 40px 0 rgba(31, 38, 135, 0.4);
}

.glass-container:active {
  transform: translateY(0);
  box-shadow: 0 5px 20px 0 rgba(31, 38, 135, 0.3);
}

/* Glass navbar */
.glass-navbar {
  @apply glass-container sticky top-0 z-50 px-4 py-2;
  background: var(--glass-background);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Glass button */
.glass-button {
  @apply glass-container px-4 py-2 cursor-pointer inline-block text-center;
  transition: all 0.3s ease;
}

.glass-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(31, 38, 135, 0.4);
}

.glass-button:active {
  transform: translateY(0);
  box-shadow: 0 5px 15px rgba(31, 38, 135, 0.3);
}

/* Glass input */
.glass-input {
  @apply glass-container px-4 py-2 w-full;
  background: var(--glass-background);
  color: inherit;
  outline: none;
}

.glass-input:focus {
  border-color: var(--glass-primary-border);
  box-shadow: 0 0 0 2px var(--glass-primary-background);
}

/* Glass modal */
.glass-modal {
  @apply glass-container p-6 max-w-md mx-auto my-8;
  background: var(--glass-background);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
}

/* Glass divider */
.glass-divider {
  height: 1px;
  background: var(--glass-border);
  margin: 1rem 0;
}

/* Glass badge */
.glass-badge {
  @apply glass-container inline-flex items-center justify-center px-2 py-1 text-xs font-medium rounded-full;
}

/* Glass tooltip */
.glass-tooltip {
  @apply glass-container absolute z-50 px-2 py-1 text-sm;
  max-width: 200px;
}
