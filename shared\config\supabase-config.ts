// Shared Supabase Configuration for all platforms
// This ensures consistent configuration across POS, Admin Dashboard, Customer Web, and Mobile apps

export interface SupabaseConfig {
  url: string;
  anonKey: string;
  serviceRoleKey?: string;
  projectRef: string;
  region: string;
}

export interface SupabaseClientOptions {
  auth?: {
    autoRefreshToken?: boolean;
    persistSession?: boolean;
    detectSessionInUrl?: boolean;
    storage?: any; // For React Native AsyncStorage
  };
  realtime?: {
    params?: {
      eventsPerSecond?: number;
    };
  };
  global?: {
    headers?: Record<string, string>;
  };
}

// Centralized Supabase configuration
export const SUPABASE_CONFIG: SupabaseConfig = {
  url: 'https://voiwzwyfnkzvcffuxpwl.supabase.co',
  anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0MDU2MzIsImV4cCI6MjA2NDk4MTYzMn0.RvabQ6EGP1gBb0w8J8_VpaqqXgkpEFR7Nbbkmw11wNA',
  serviceRoleKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMTg0NjgzMywiZXhwIjoyMDQ3NDIyODMzfQ.qPWQJLa-C8vPOZBq9FjvC0yKjG6kN8N8jQZ9XGZ8Z0k',
  projectRef: 'voiwzwyfnkzvcffuxpwl',
  region: 'eu-central-1'
};

// Platform-specific client options
export const CLIENT_OPTIONS: Record<string, SupabaseClientOptions> = {
  // Next.js Web Applications (Admin Dashboard, Customer Web)
  web: {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  },

  // React Native Mobile Application
  mobile: {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false, // Not applicable for mobile
    },
    realtime: {
      params: {
        eventsPerSecond: 5, // Lower for mobile to conserve battery
      },
    },
  },

  // Electron POS System
  desktop: {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
    },
    realtime: {
      params: {
        eventsPerSecond: 15, // Higher for POS real-time updates
      },
    },
  },

  // Server-side operations (API routes, background jobs)
  server: {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
      detectSessionInUrl: false,
    },
  },
};

// Environment-based configuration getter
export const getSupabaseConfig = (platform: 'web' | 'mobile' | 'desktop' | 'server' = 'web') => {
  // Try to get from environment variables first
  const envUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 
                 process.env.SUPABASE_URL || 
                 process.env.VITE_SUPABASE_URL;
                 
  const envAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 
                     process.env.SUPABASE_ANON_KEY || 
                     process.env.VITE_SUPABASE_ANON_KEY;

  const envServiceKey = process.env.NEXT_PUBLIC_SUPABASE_SECRET || 
                        process.env.SUPABASE_SERVICE_ROLE_KEY || 
                        process.env.SUPABASE_SECRET;

  return {
    url: envUrl || SUPABASE_CONFIG.url,
    anonKey: envAnonKey || SUPABASE_CONFIG.anonKey,
    serviceRoleKey: envServiceKey || SUPABASE_CONFIG.serviceRoleKey,
    options: CLIENT_OPTIONS[platform] || CLIENT_OPTIONS.web,
  };
};

// Database connection test utility
export const testSupabaseConnection = async (url: string, key: string): Promise<{
  success: boolean;
  error?: string;
  latency?: number;
}> => {
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${url}/rest/v1/`, {
      method: 'HEAD',
      headers: {
        'apikey': key,
        'Authorization': `Bearer ${key}`,
      },
    });

    const latency = Date.now() - startTime;

    if (response.ok) {
      return { success: true, latency };
    } else {
      return { 
        success: false, 
        error: `HTTP ${response.status}: ${response.statusText}`,
        latency 
      };
    }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error',
      latency: Date.now() - startTime 
    };
  }
};

// Validate configuration
export const validateSupabaseConfig = (config: SupabaseConfig): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!config.url || !config.url.startsWith('https://')) {
    errors.push('Invalid Supabase URL');
  }

  if (!config.anonKey || config.anonKey.length < 100) {
    errors.push('Invalid anonymous key');
  }

  if (!config.projectRef || config.projectRef.length < 10) {
    errors.push('Invalid project reference');
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

// Export table names for consistency across platforms
export const SUPABASE_TABLES = {
  // Core business tables
  BRANCHES: 'branches',
  CUSTOMERS: 'customers',
  CUSTOMER_ADDRESSES: 'customer_addresses',
  ORDERS: 'orders',
  ORDER_ITEMS: 'order_items',
  MENU_CATEGORIES: 'menu_categories',
  MENU_ITEMS: 'subcategories',
  INGREDIENTS: 'ingredients',

  // POS system tables
  POS_TERMINALS: 'pos_terminals',
  POS_HEARTBEATS: 'pos_heartbeats',
  POS_CONFIGURATIONS: 'pos_configurations',
  POS_SETTINGS_SYNC_HISTORY: 'pos_settings_sync_history',

  // Admin configuration tables
  WEB_CONFIGURATIONS: 'web_configurations',
  APP_CONFIGURATIONS_ENHANCED: 'app_configurations_enhanced',
  PUSH_NOTIFICATION_SETTINGS: 'push_notification_settings',

  // Analytics tables
  USER_ANALYTICS: 'user_analytics',
  DELIVERY_ZONE_ANALYTICS: 'delivery_zone_analytics',

  // Auth and user management
  PROFILES: 'profiles',
  USER_PROFILES: 'user_profiles',
  ROLES: 'roles',
  STAFF: 'staff',
} as const;

// Export function names for consistency
export const SUPABASE_FUNCTIONS = {
  GET_WEB_CONFIG: 'get_web_config',
  GET_APP_CONFIG: 'get_app_config',
  LOG_USER_EVENT: 'log_user_event',
  CALCULATE_ZONE_ANALYTICS: 'calculate_zone_analytics',
  SEARCH_CUSTOMERS_ENHANCED: 'search_customers_enhanced',
} as const;

// Real-time channel prefixes
export const REALTIME_CHANNELS = {
  ORDERS: 'orders_channel',
  POS_HEARTBEAT: 'pos_heartbeat_channel',
  MENU_UPDATES: 'menu_updates_channel',
  CUSTOMER_UPDATES: 'customer_updates_channel',
  NOTIFICATIONS: 'notifications_channel',
} as const;