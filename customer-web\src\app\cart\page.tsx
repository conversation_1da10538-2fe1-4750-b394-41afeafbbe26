import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { GlassCard, GlassButton, GlassDivider } from '@/components/ui/glass-components';
import { Breadcrumb } from '@/components/navigation/breadcrumb';

export const metadata: Metadata = {
  title: 'Cart | Delicious Crepes & Waffles',
  description: 'Review your order and proceed to checkout.',
};

// This would be replaced with actual cart functionality using the CartProvider
export default function CartPage() {
  // Mock cart data - in a real app, this would come from the CartProvider
  const cartItems = [
    {
      id: 'nutella-strawberry',
      name: 'Nutella & Strawberry',
      price: 7.5,
      quantity: 2,
      image: '/images/nutella-strawberry.jpg',
      category: 'sweet-crepes',
    },
    {
      id: 'ham-cheese',
      name: 'Ham & Cheese',
      price: 8.5,
      quantity: 1,
      image: '/images/ham-cheese.jpg',
      category: 'savory-crepes',
    },
    {
      id: 'coffee',
      name: 'Coffee',
      price: 2.5,
      quantity: 2,
      image: '/images/coffee.jpg',
      category: 'beverages',
    },
  ];

  const subtotal = cartItems.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const tax = subtotal * 0.13; // 13% tax
  const deliveryFee = 2.5;
  const total = subtotal + tax + deliveryFee;

  // Check if cart is empty
  const isCartEmpty = cartItems.length === 0;

  return (
    <main className="min-h-screen py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <Breadcrumb className="mb-6" />
        <h1 className="text-3xl font-bold mb-8">Your Cart</h1>

        {isCartEmpty ? (
          <GlassCard className="text-center py-12">
            <h2 className="text-2xl font-semibold mb-4">Your cart is empty</h2>
            <p className="text-muted-foreground mb-8">
              Looks like you haven't added any items to your cart yet.
            </p>
            <Link href="/menu">
              <GlassButton variant="primary" size="lg">
                Browse Menu
              </GlassButton>
            </Link>
          </GlassCard>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items - Takes up 2/3 of the space on large screens */}
            <div className="lg:col-span-2 space-y-4">
              <GlassCard>
                <div className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Cart Items</h2>

                  <div className="space-y-6">
                    {cartItems.map(item => (
                      <div key={item.id} className="flex items-center space-x-4">
                        <div className="relative h-20 w-20 rounded-md overflow-hidden flex-shrink-0">
                          <Image src={item.image} alt={item.name} fill className="object-cover" />
                        </div>

                        <div className="flex-grow">
                          <h3 className="font-medium">{item.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            ${item.price.toFixed(2)} each
                          </p>
                        </div>

                        <div className="flex items-center space-x-2">
                          <button className="h-8 w-8 rounded-full bg-muted flex items-center justify-center hover:bg-muted/80 transition-colors">
                            <span>-</span>
                          </button>

                          <span className="w-8 text-center">{item.quantity}</span>

                          <button className="h-8 w-8 rounded-full bg-muted flex items-center justify-center hover:bg-muted/80 transition-colors">
                            <span>+</span>
                          </button>
                        </div>

                        <div className="text-right w-20">
                          <div className="font-medium">
                            ${(item.price * item.quantity).toFixed(2)}
                          </div>
                          <button className="text-sm text-red-500 hover:text-red-700 transition-colors">
                            Remove
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </GlassCard>

              <GlassCard>
                <div className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Special Instructions</h2>
                  <textarea
                    className="w-full p-3 rounded-md bg-background border border-input focus:border-primary focus:ring-1 focus:ring-primary transition-colors"
                    rows={3}
                    placeholder="Any special instructions for your order? Allergies, preferences, etc."
                  ></textarea>
                </div>
              </GlassCard>
            </div>

            {/* Order Summary - Takes up 1/3 of the space on large screens */}
            <div>
              <GlassCard className="sticky top-24">
                <div className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Order Summary</h2>

                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Subtotal</span>
                      <span>${subtotal.toFixed(2)}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Tax (13%)</span>
                      <span>${tax.toFixed(2)}</span>
                    </div>

                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Delivery Fee</span>
                      <span>${deliveryFee.toFixed(2)}</span>
                    </div>

                    <GlassDivider />

                    <div className="flex justify-between font-bold text-lg">
                      <span>Total</span>
                      <span>${total.toFixed(2)}</span>
                    </div>
                  </div>

                  <div className="mt-6 space-y-3">
                    <Link href="/checkout">
                      <GlassButton variant="primary" size="lg" className="w-full">
                        Proceed to Checkout
                      </GlassButton>
                    </Link>

                    <Link href="/menu">
                      <GlassButton variant="secondary" size="lg" className="w-full">
                        Continue Shopping
                      </GlassButton>
                    </Link>
                  </div>
                </div>
              </GlassCard>
            </div>
          </div>
        )}
      </div>
    </main>
  );
}
