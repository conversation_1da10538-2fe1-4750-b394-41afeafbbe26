'use client';

import React, { useState, useEffect } from 'react';
import { X, Plus, Minus, ShoppingCart } from 'lucide-react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useOrderType } from '@/contexts/OrderTypeContext';

interface Ingredient {
  id: string;
  name: string;
  description: string;
  price: number;
  pickup_price: number;
  delivery_price: number;
  category_id: string;
  category_name: string;
  image_url?: string;
  is_available: boolean;
  allergens?: string[];
}

interface IngredientCategory {
  id: string;
  name: string;
  description: string;
  color_code: string;
}

interface SelectedIngredient {
  ingredient: Ingredient;
  quantity: number;
}

interface IngredientsModalProps {
  isOpen: boolean;
  onClose: () => void;
  menuItem: {
    id: string;
    name: string;
    base_price: number;
    max_ingredients?: number;
  };
  onAddToCart: (customization: {
    selectedIngredients: SelectedIngredient[];
    totalPrice: number;
    itemName: string;
  }) => void;
}

const IngredientsModal: React.FC<IngredientsModalProps> = ({
  isOpen,
  onClose,
  menuItem,
  onAddToCart,
}) => {
  const { orderType } = useOrderType();
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [categories, setCategories] = useState<IngredientCategory[]>([]);
  const [selectedIngredients, setSelectedIngredients] = useState<SelectedIngredient[]>([]);
  const [activeCategory, setActiveCategory] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const supabase = createClientComponentClient();

  // Helper function to get the correct price based on order type
  const getIngredientPrice = (ingredient: Ingredient): number => {
    return orderType === 'pickup' ? ingredient.pickup_price : ingredient.delivery_price;
  };

  useEffect(() => {
    if (isOpen) {
      loadIngredientsAndCategories();
    }
  }, [isOpen]);

  const loadIngredientsAndCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load ingredient categories
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('ingredient_categories')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      if (categoriesError) throw categoriesError;

      // Load ingredients
      const { data: ingredientsData, error: ingredientsError } = await supabase
        .from('ingredients')
        .select(
          `
          *,
          ingredient_categories(name, color_code)
        `
        )
        .eq('is_available', true)
        .order('display_order');

      if (ingredientsError) throw ingredientsError;

      // Transform ingredients data
      const transformedIngredients =
        ingredientsData?.map(ingredient => ({
          ...ingredient,
          category_name: ingredient.ingredient_categories?.name || 'Unknown',
        })) || [];

      setCategories(categoriesData || []);
      setIngredients(transformedIngredients);

      // Set first category as active
      if (categoriesData && categoriesData.length > 0) {
        setActiveCategory(categoriesData[0].id);
      }
    } catch (err) {
      console.error('Error loading ingredients:', err);
      setError('Failed to load ingredients. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const filteredIngredients = ingredients.filter(
    ingredient => ingredient.category_id === activeCategory
  );

  const addIngredient = (ingredient: Ingredient) => {
    const maxIngredients = menuItem.max_ingredients || 10;
    const totalIngredients = selectedIngredients.reduce((sum, item) => sum + item.quantity, 0);

    if (totalIngredients >= maxIngredients) {
      return; // Don't add if max limit reached
    }

    const existingIndex = selectedIngredients.findIndex(
      item => item.ingredient.id === ingredient.id
    );

    if (existingIndex >= 0) {
      // Increase quantity
      const updated = [...selectedIngredients];
      updated[existingIndex].quantity += 1;
      setSelectedIngredients(updated);
    } else {
      // Add new ingredient
      setSelectedIngredients([
        ...selectedIngredients,
        {
          ingredient,
          quantity: 1,
        },
      ]);
    }
  };

  const removeIngredient = (ingredientId: string) => {
    const existingIndex = selectedIngredients.findIndex(
      item => item.ingredient.id === ingredientId
    );

    if (existingIndex >= 0) {
      const updated = [...selectedIngredients];
      if (updated[existingIndex].quantity > 1) {
        updated[existingIndex].quantity -= 1;
      } else {
        updated.splice(existingIndex, 1);
      }
      setSelectedIngredients(updated);
    }
  };

  const getTotalAdditionalPrice = () => {
    return selectedIngredients.reduce(
      (sum, item) => sum + getIngredientPrice(item.ingredient) * item.quantity,
      0
    );
  };

  const getTotalPrice = () => {
    return menuItem.base_price + getTotalAdditionalPrice();
  };

  const getTotalIngredients = () => {
    return selectedIngredients.reduce((sum, item) => sum + item.quantity, 0);
  };

  const getSelectedQuantity = (ingredientId: string) => {
    const selected = selectedIngredients.find(item => item.ingredient.id === ingredientId);
    return selected ? selected.quantity : 0;
  };

  const handleAddToCart = () => {
    // Create custom item name
    const ingredientNames = selectedIngredients.map(item => item.ingredient.name).join(', ');
    const customItemName = `${menuItem.name}${ingredientNames ? ` with ${ingredientNames}` : ''}`;

    onAddToCart({
      selectedIngredients,
      totalPrice: getTotalPrice(),
      itemName: customItemName,
    });

    // Reset state
    setSelectedIngredients([]);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="bg-white rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Customize Your {menuItem.name}</h2>
            <p className="text-gray-600 mt-1">Select ingredients to create your perfect dish</p>
            <div className="flex items-center mt-2">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                orderType === 'pickup' 
                  ? 'bg-blue-100 text-blue-800' 
                  : 'bg-green-100 text-green-800'
              }`}>
                {orderType === 'pickup' ? '🏪 Pickup' : '🚚 Delivery'} Pricing
              </span>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="h-6 w-6 text-gray-600" />
          </button>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-red-600 text-lg">{error}</p>
              <button
                onClick={loadIngredientsAndCategories}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Try Again
              </button>
            </div>
          </div>
        ) : (
          <div className="flex h-[calc(90vh-200px)]">
            {/* Category Sidebar */}
            <div className="w-48 bg-gray-50 border-r border-gray-200 overflow-y-auto">
              <div className="p-4">
                <h3 className="text-sm font-semibold text-gray-900 mb-3">Categories</h3>
                <div className="space-y-2">
                  {categories.map(category => (
                    <button
                      key={category.id}
                      onClick={() => setActiveCategory(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                        activeCategory === category.id
                          ? 'bg-blue-100 text-blue-700 font-medium'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: category.color_code }}
                        ></div>
                        <span>{category.name}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Ingredients Grid */}
            <div className="flex-1 overflow-y-auto">
              <div className="p-6">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {filteredIngredients.map(ingredient => {
                    const selectedQuantity = getSelectedQuantity(ingredient.id);

                    return (
                      <div
                        key={ingredient.id}
                        className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow"
                      >
                        {ingredient.image_url && (
                          <img
                            src={ingredient.image_url}
                            alt={ingredient.name}
                            className="w-full h-24 object-cover rounded-lg mb-3"
                          />
                        )}

                        <h4 className="font-semibold text-gray-900 mb-1">{ingredient.name}</h4>

                        {ingredient.description && (
                          <p className="text-xs text-gray-600 mb-2">{ingredient.description}</p>
                        )}

                        <div className="flex items-center justify-between">
                          <div className="flex flex-col">
                            <span className="text-lg font-bold text-green-600">
                              +${getIngredientPrice(ingredient).toFixed(2)}
                            </span>
                            <span className="text-xs text-gray-500 capitalize">
                              {orderType} price
                            </span>
                          </div>

                          <div className="flex items-center space-x-2">
                            {selectedQuantity > 0 && (
                              <button
                                onClick={() => removeIngredient(ingredient.id)}
                                className="w-8 h-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center hover:bg-red-200 transition-colors"
                              >
                                <Minus className="h-4 w-4" />
                              </button>
                            )}

                            {selectedQuantity > 0 && (
                              <span className="text-sm font-medium min-w-[20px] text-center">
                                {selectedQuantity}
                              </span>
                            )}

                            <button
                              onClick={() => addIngredient(ingredient)}
                              disabled={getTotalIngredients() >= (menuItem.max_ingredients || 10)}
                              className="w-8 h-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center hover:bg-green-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <Plus className="h-4 w-4" />
                            </button>
                          </div>
                        </div>

                        {ingredient.allergens && ingredient.allergens.length > 0 && (
                          <div className="mt-2">
                            <div className="flex flex-wrap gap-1">
                              {ingredient.allergens.map(allergen => (
                                <span
                                  key={allergen}
                                  className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full"
                                >
                                  {allergen}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="border-t border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-gray-600">
                Base Price: ${menuItem.base_price.toFixed(2)}
              </div>
              {getTotalAdditionalPrice() > 0 && (
                <div className="text-sm text-gray-600">
                  Ingredients: +${getTotalAdditionalPrice().toFixed(2)}
                </div>
              )}
              <div className="text-lg font-bold text-gray-900">
                Total: ${getTotalPrice().toFixed(2)}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {getTotalIngredients()}/{menuItem.max_ingredients || 10} ingredients selected
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAddToCart}
                className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <ShoppingCart className="h-5 w-5" />
                <span>Add to Cart</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IngredientsModal;
