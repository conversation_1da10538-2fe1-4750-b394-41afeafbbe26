{"common": {"appName": "C<PERSON><PERSON>ie", "loading": "Loading...", "error": "An error occurred", "retry": "Retry", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "back": "Back", "next": "Next", "submit": "Submit", "close": "Close", "yes": "Yes", "no": "No", "or": "or", "and": "and", "required": "Required", "optional": "Optional", "search": "Search", "filter": "Filter", "sort": "Sort", "all": "All", "none": "None", "more": "More", "less": "Less", "show": "Show", "hide": "<PERSON>de", "add": "Add", "remove": "Remove", "continue": "Continue", "goBack": "Go Back", "goHome": "Go Home", "tryAgain": "Try Again", "offline": "You are offline", "online": "You are back online", "installApp": "Install App", "shareApp": "Share App", "rateApp": "Rate App", "contactUs": "Contact Us", "aboutUs": "About Us", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "cookiePolicy": "<PERSON><PERSON>", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System", "notifications": "Notifications", "settings": "Settings", "profile": "Profile", "account": "Account", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "changePassword": "Change Password", "verifyEmail": "<PERSON><PERSON><PERSON>", "resendVerification": "Resend Verification", "emailVerified": "<PERSON><PERSON>", "emailNotVerified": "Email Not Verified", "passwordChanged": "Password Changed", "passwordNotChanged": "Password Not Changed", "passwordReset": "Password Reset", "passwordResetFailed": "Password Reset Failed", "passwordResetSent": "Password Reset Email <PERSON>", "passwordResetNotSent": "Password Reset Email Not Sent", "invalidCredentials": "Invalid Credentials", "invalidEmail": "<PERSON><PERSON><PERSON>", "invalidPassword": "Invalid Password", "invalidCode": "Invalid Code", "invalidToken": "Invalid <PERSON>", "invalidRequest": "Invalid Request", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "notFound": "Not Found", "serverError": "Server Error", "networkError": "Network Error", "timeoutError": "Timeout Error", "unknownError": "Unknown Error"}, "nav": {"home": "Home", "menu": "<PERSON><PERSON>", "cart": "<PERSON><PERSON>", "orders": "Orders", "account": "Account", "favorites": "Favorites", "about": "About", "contact": "Contact"}, "home": {"hero": {"title": "Delicious Crepes Delivered to Your Door", "subtitle": "Sweet or savory, we've got you covered", "cta": "Order Now", "viewMenu": "View Menu"}, "features": {"title": "Why Choose Us", "delivery": {"title": "Fast Delivery", "description": "Get your crepes delivered hot and fresh"}, "quality": {"title": "Premium Quality", "description": "Made with the finest ingredients"}, "variety": {"title": "Wide Variety", "description": "Sweet, savory, and everything in between"}}, "popular": {"title": "Popular Items", "viewAll": "View All"}, "categories": {"title": "Browse Categories", "viewAll": "View All"}, "cta": {"title": "Ready to Order?", "description": "Satisfy your cravings with our delicious crepes", "button": "Order Now"}, "testimonials": {"title": "What Our Customers Say"}, "download": {"title": "Download Our App", "description": "Order faster and earn rewards", "android": "Get it on Google Play", "ios": "Download on the App Store"}}, "menu": {"title": "Our Menu", "categories": "Categories", "search": "Search menu items...", "filter": "Filter", "sort": "Sort", "sortOptions": {"nameAsc": "Name (A-Z)", "nameDesc": "Name (Z-A)", "priceAsc": "Price (Low to High)", "priceDesc": "Price (High to Low)"}, "filterOptions": {"all": "All Items", "vegetarian": "Vegetarian", "vegan": "Vegan", "glutenFree": "Gluten Free", "dairyFree": "Dairy Free", "nutFree": "Nut Free"}, "noResults": "No menu items found", "tryAgain": "Please try a different search term or filter", "itemDetails": {"ingredients": "Ingredients", "allergens": "Allergens", "nutritionalInfo": "Nutritional Info", "calories": "Calories", "protein": "<PERSON><PERSON>", "carbs": "<PERSON><PERSON>", "fat": "Fat", "addToCart": "Add to Cart", "customize": "Customize", "quantity": "Quantity", "specialInstructions": "Special Instructions", "variants": "Variants", "selectVariant": "Select a variant"}}, "cart": {"title": "Your Cart", "empty": "Your cart is empty", "startShopping": "Start Shopping", "subtotal": "Subtotal", "deliveryFee": "Delivery Fee", "tax": "Tax", "total": "Total", "checkout": "Checkout", "continueShopping": "Continue Shopping", "remove": "Remove", "edit": "Edit", "quantity": "Quantity", "clearCart": "Clear Cart", "confirmClear": "Are you sure you want to clear your cart?", "minimumOrder": "Minimum order amount is {{amount}}", "itemAdded": "Item added to cart", "itemRemoved": "Item removed from cart", "itemUpdated": "Item updated in cart"}, "checkout": {"title": "Checkout", "deliveryMethod": "Delivery Method", "delivery": "Delivery", "pickup": "Pickup", "deliveryAddress": "Delivery Address", "deliveryTime": "Delivery Time", "deliveryInstructions": "Delivery Instructions", "contactInfo": "Contact Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "paymentMethod": "Payment Method", "cash": "Cash on Delivery", "card": "Credit/Debit Card", "orderSummary": "Order Summary", "placeOrder": "Place Order", "orderPlaced": "Order Placed Successfully", "orderFailed": "Failed to Place Order", "tryAgain": "Please try again", "backToCart": "Back to Cart", "viewOrder": "View Order", "continueToPayment": "Continue to Payment", "paymentProcessing": "Processing Payment", "paymentSuccess": "Payment Successful", "paymentFailed": "Payment Failed", "addressOutsideDeliveryZone": "Address is outside our delivery zone", "selectDeliveryZone": "Please select a valid delivery address", "tip": "Tip", "addTip": "Add Tip", "customTip": "Custom Tip", "saveInfoForNextTime": "Save information for next time"}, "orders": {"title": "Your Orders", "noOrders": "You haven't placed any orders yet", "startOrdering": "Start Ordering", "orderDetails": "Order Details", "orderNumber": "Order #", "orderDate": "Order Date", "orderStatus": "Order Status", "orderTotal": "Order Total", "viewDetails": "View Details", "reorder": "Reorder", "trackOrder": "Track Order", "cancelOrder": "Cancel Order", "confirmCancel": "Are you sure you want to cancel this order?", "orderCancelled": "Order Cancelled", "orderCancellationFailed": "Failed to Cancel Order", "contactSupport": "Contact Support", "status": {"pending": "Pending", "confirmed": "Confirmed", "preparing": "Preparing", "ready": "Ready for Pickup", "outForDelivery": "Out for Delivery", "delivered": "Delivered", "completed": "Completed", "cancelled": "Cancelled"}}, "account": {"title": "Your Account", "profile": {"title": "Profile", "edit": "Edit Profile", "save": "Save Changes", "cancel": "Cancel", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "address": "Address", "profileUpdated": "Profile Updated", "profileUpdateFailed": "Failed to Update Profile"}, "addresses": {"title": "Saved Addresses", "add": "Add Address", "edit": "Edit Address", "delete": "Delete Address", "confirmDelete": "Are you sure you want to delete this address?", "addressDeleted": "Address Deleted", "addressDeletionFailed": "Failed to Delete Address", "noAddresses": "You haven't saved any addresses yet", "addNew": "Add New Address", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "city": "City", "state": "State", "postalCode": "Postal Code", "country": "Country", "addressAdded": "Address Added", "addressAdditionFailed": "Failed to Add Address", "addressUpdated": "Address Updated", "addressUpdateFailed": "Failed to Update Address", "makeDefault": "Make Default", "default": "<PERSON><PERSON><PERSON>"}, "paymentMethods": {"title": "Payment Methods", "add": "Add Payment Method", "edit": "Edit Payment Method", "delete": "Delete Payment Method", "confirmDelete": "Are you sure you want to delete this payment method?", "paymentMethodDeleted": "Payment Method Deleted", "paymentMethodDeletionFailed": "Failed to Delete Payment Method", "noPaymentMethods": "You haven't saved any payment methods yet", "addNew": "Add New Payment Method", "cardNumber": "Card Number", "cardholderName": "Cardholder Name", "expiryDate": "Expiry Date", "cvv": "CVV", "paymentMethodAdded": "Payment Method Added", "paymentMethodAdditionFailed": "Failed to Add Payment Method", "paymentMethodUpdated": "Payment Method Updated", "paymentMethodUpdateFailed": "Failed to Update Payment Method", "makeDefault": "Make Default", "default": "<PERSON><PERSON><PERSON>"}, "favorites": {"title": "Favorites", "noFavorites": "You haven't added any favorites yet", "addSome": "Add Some Favorites", "remove": "Remove from Favorites", "confirmRemove": "Are you sure you want to remove this item from your favorites?", "favoriteRemoved": "Favorite Removed", "favoriteRemovalFailed": "Failed to Remove Favorite"}, "settings": {"title": "Settings", "language": "Language", "theme": "Theme", "notifications": "Notifications", "pushNotifications": "Push Notifications", "emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "orderUpdates": "Order Updates", "promotions": "Promotions", "newsletter": "Newsletter", "settingsUpdated": "Settings Updated", "settingsUpdateFailed": "Failed to Update Settings"}, "security": {"title": "Security", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "passwordChanged": "Password Changed", "passwordChangeFailed": "Failed to Change Password", "twoFactorAuth": "Two-Factor Authentication", "enable": "Enable", "disable": "Disable", "twoFactorEnabled": "Two-Factor Authentication Enabled", "twoFactorDisabled": "Two-Factor Authentication Disabled", "twoFactorEnableFailed": "Failed to Enable Two-Factor Authentication", "twoFactorDisableFailed": "Failed to Disable Two-Factor Authentication"}, "deleteAccount": {"title": "Delete Account", "warning": "This action cannot be undone", "confirm": "Are you sure you want to delete your account?", "enterPassword": "Enter your password to confirm", "accountDeleted": "Account Deleted", "accountDeletionFailed": "Failed to Delete Account"}}, "auth": {"login": {"title": "<PERSON><PERSON>", "email": "Email", "password": "Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "noAccount": "Don't have an account?", "register": "Register", "loginWith": "Or login with", "google": "Google", "facebook": "Facebook", "apple": "Apple", "loginSuccess": "Login Successful", "loginFailed": "Login Failed"}, "register": {"title": "Register", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "phone": "Phone", "termsAndConditions": "I agree to the Terms and Conditions", "privacyPolicy": "I agree to the Privacy Policy", "marketing": "I want to receive marketing emails", "hasAccount": "Already have an account?", "login": "<PERSON><PERSON>", "registerWith": "Or register with", "google": "Google", "facebook": "Facebook", "apple": "Apple", "registerSuccess": "Registration Successful", "registerFailed": "Registration Failed", "verifyEmail": "Please verify your email"}, "forgotPassword": {"title": "Forgot Password", "email": "Email", "submit": "Submit", "back": "Back to Login", "resetSent": "Password Reset Email <PERSON>", "resetFailed": "Failed to Send Password Reset Email", "checkEmail": "Please check your email for instructions"}, "resetPassword": {"title": "Reset Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "submit": "Reset Password", "back": "Back to Login", "resetSuccess": "Password Reset Successful", "resetFailed": "Failed to Reset Password", "login": "Login with New Password"}, "verifyEmail": {"title": "<PERSON><PERSON><PERSON>", "verifying": "Verifying your email...", "success": "Email Verified Successfully", "failed": "Failed to Verify Email", "alreadyVerified": "Email Already Verified", "resend": "Resend Verification Email", "resendSuccess": "Verification Email Resent", "resendFailed": "Failed to Resend Verification Email", "continue": "Continue to Login"}, "twoFactor": {"title": "Two-Factor Authentication", "code": "Enter Code", "submit": "Verify", "resend": "Resend Code", "back": "Back to Login", "verifySuccess": "Verification Successful", "verifyFailed": "Verification Failed", "resendSuccess": "Code Resent", "resendFailed": "Failed to Resend Code"}, "logout": {"title": "Logout", "confirm": "Are you sure you want to logout?", "logoutSuccess": "Logout Successful", "logoutFailed": "Logout Failed"}}, "about": {"title": "About Us", "story": {"title": "Our Story", "content": "Founded in 2020, Creperie started as a small family business with a passion for creating delicious crepes. What began as a small cart in the local market has grown into a beloved establishment, serving both sweet and savory crepes to our loyal customers."}, "mission": {"title": "Our Mission", "content": "Our mission is to bring the authentic taste of French crepes to our community, using only the freshest ingredients and traditional recipes. We strive to create a warm and welcoming environment where customers can enjoy a taste of France right in their neighborhood."}, "values": {"title": "Our Values", "quality": {"title": "Quality", "content": "We use only the finest ingredients in our crepes, sourced from local suppliers whenever possible."}, "tradition": {"title": "Tradition", "content": "Our recipes are based on traditional French techniques, passed down through generations."}, "community": {"title": "Community", "content": "We are committed to giving back to our community and creating a welcoming space for all."}, "sustainability": {"title": "Sustainability", "content": "We are dedicated to reducing our environmental impact through sustainable practices."}}, "team": {"title": "Our Team", "content": "Our team of passionate chefs and staff are dedicated to providing you with the best crepe experience possible. From our kitchen to your table, we put our heart and soul into every crepe we make."}}, "contact": {"title": "Contact Us", "form": {"name": "Name", "email": "Email", "phone": "Phone", "subject": "Subject", "message": "Message", "submit": "Send Message", "success": "Message Sent Successfully", "failed": "Failed to Send Message"}, "info": {"title": "Contact Information", "address": "123 Main Street, Athens, Greece", "phone": "+30 ************", "email": "<EMAIL>", "hours": "Opening Hours", "monday": "Monday: 9:00 AM - 10:00 PM", "tuesday": "Tuesday: 9:00 AM - 10:00 PM", "wednesday": "Wednesday: 9:00 AM - 10:00 PM", "thursday": "Thursday: 9:00 AM - 10:00 PM", "friday": "Friday: 9:00 AM - 11:00 PM", "saturday": "Saturday: 10:00 AM - 11:00 PM", "sunday": "Sunday: 10:00 AM - 10:00 PM"}, "faq": {"title": "Frequently Asked Questions", "q1": "Do you offer delivery?", "a1": "Yes, we offer delivery within a 5km radius of our location. You can order through our website or mobile app.", "q2": "What payment methods do you accept?", "a2": "We accept cash, credit/debit cards, and mobile payments.", "q3": "Do you cater for events?", "a3": "Yes, we offer catering services for events of all sizes. Please contact us for more information.", "q4": "Do you have vegetarian options?", "a4": "Yes, we have a variety of vegetarian options available on our menu.", "q5": "Do you have gluten-free options?", "a5": "Yes, we offer gluten-free crepes upon request."}}, "footer": {"company": "Company", "about": "About Us", "contact": "Contact Us", "careers": "Careers", "press": "Press", "blog": "Blog", "legal": "Legal", "terms": "Terms of Service", "privacy": "Privacy Policy", "cookies": "<PERSON><PERSON>", "help": "Help", "faq": "FAQ", "support": "Support", "shipping": "Shipping", "returns": "Returns", "social": "Social", "facebook": "Facebook", "twitter": "Twitter", "instagram": "Instagram", "youtube": "YouTube", "newsletter": "Newsletter", "subscribePrompt": "Subscribe to our newsletter", "emailPlaceholder": "Enter your email", "subscribe": "Subscribe", "copyright": "© 2023 Creperie. All rights reserved.", "poweredBy": "Powered by", "designedBy": "Designed by"}, "errors": {"404": {"title": "Page Not Found", "message": "The page you are looking for does not exist", "back": "Go Back", "home": "Go Home"}, "500": {"title": "Server Error", "message": "Something went wrong on our end", "retry": "Try Again", "contact": "Contact Support"}, "offline": {"title": "You're Offline", "message": "Please check your internet connection", "retry": "Try Again"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Must be at least {{min}} characters", "maxLength": "Must be at most {{max}} characters", "passwordMatch": "Passwords do not match", "invalidPhone": "Please enter a valid phone number", "invalidPostalCode": "Please enter a valid postal code", "invalidCreditCard": "Please enter a valid credit card number", "invalidCVV": "Please enter a valid CVV", "invalidExpiryDate": "Please enter a valid expiry date", "termsRequired": "You must agree to the terms and conditions"}}, "pwa": {"install": {"title": "Install Our App", "message": "Install our app for a better experience", "button": "Install", "cancel": "Not Now", "installed": "App Installed Successfully", "installFailed": "Failed to Install App"}, "update": {"title": "Update Available", "message": "A new version of our app is available", "button": "Update", "cancel": "Later", "updated": "App Updated Successfully", "updateFailed": "Failed to Update App"}, "offline": {"title": "Offline Mode", "message": "You are currently offline. Some features may be unavailable.", "button": "Okay"}}}