// POS Terminal Heartbeat Service
// Handles terminal registration, heartbeat monitoring, and sync status reporting

import { getSupabaseClient, SUPABASE_CONFIG } from '../../shared/supabase-config'
import { app } from 'electron'
import * as os from 'os'
import * as crypto from 'crypto'

// Import the database manager for settings
import { DatabaseManager } from '../database';

// Settings structure
interface SettingsCollection {
  [category: string]: Record<string, unknown>;
}

export interface HeartbeatData {
  terminal_id: string
  timestamp: string
  status: 'online' | 'offline' | 'error'
  version: string
  uptime: number
  memory_usage: number
  cpu_usage: number
  settings_hash: string
  sync_status: 'synced' | 'pending' | 'failed'
  pending_updates: number
}

export interface TerminalInfo {
  terminal_id: string
  name: string
  location: string
  ip_address: string
  mac_address?: string
  version: string
}

export class HeartbeatService {
  private heartbeatInterval: NodeJS.Timeout | null = null
  private terminalInfo: TerminalInfo | null = null
  private syncStatus: 'synced' | 'pending' | 'failed' = 'synced'
  private pendingUpdates: number = 0
  private settingsHash: string = ''
  private isRunning: boolean = false
  private startTime: number = Date.now()
  private supabase = getSupabaseClient()
  private databaseManager: DatabaseManager | null = null

  setDatabaseManager(databaseManager: DatabaseManager): void {
    this.databaseManager = databaseManager
  }

  async initialize(): Promise<void> {
    
    try {
      // Generate or load terminal information
      await this.setupTerminalInfo()
      
      // Register terminal with admin dashboard
      await this.registerTerminal()
      
      // Start heartbeat monitoring
      this.startHeartbeat()
      
    } catch (error) {
      console.error('Failed to initialize HeartbeatService:', error)
      throw error
    }
  }

  private async setupTerminalInfo(): Promise<void> {
    // Generate consistent terminal ID based on machine characteristics
    const hostname = os.hostname()
    const platform = os.platform()
    const arch = os.arch()
    const networkInterfaces = os.networkInterfaces()
    
    // Get primary MAC address
    let macAddress = ''
    for (const [interfaceName, addresses] of Object.entries(networkInterfaces)) {
      if (addresses && !interfaceName.includes('lo') && !interfaceName.includes('loopback')) {
        const physicalInterface = addresses.find(addr => !addr.internal && addr.mac !== '00:00:00:00:00:00')
        if (physicalInterface) {
          macAddress = physicalInterface.mac
          break
        }
      }
    }

    // Generate terminal ID from machine fingerprint
    const machineFingerprint = `${hostname}-${platform}-${arch}-${macAddress}`
    const terminalId = `terminal-${crypto.createHash('md5').update(machineFingerprint).digest('hex').substring(0, 8)}`

    // Get IP address
    let ipAddress = '127.0.0.1'
    try {
      const interfaces = os.networkInterfaces()
      for (const [name, addresses] of Object.entries(interfaces)) {
        if (addresses && !name.includes('lo')) {
          const address = addresses.find(addr => addr.family === 'IPv4' && !addr.internal)
          if (address) {
            ipAddress = address.address
            break
          }
        }
      }
    } catch (error) {
      console.warn('Could not determine IP address, using localhost:', error)
    }

    this.terminalInfo = {
      terminal_id: terminalId,
      name: `POS Terminal ${terminalId.split('-')[1]}`,
      location: hostname || 'Unknown Location',
      ip_address: ipAddress,
      mac_address: macAddress || undefined,
      version: app.getVersion() || '1.0.0'
    }

  }

  private async registerTerminal(): Promise<void> {
    if (!this.terminalInfo) {
      throw new Error('Terminal info not initialized')
    }

    try {
      // Direct database registration instead of using Edge Function
      const { data, error } = await this.supabase
        .from('pos_terminals')
        .upsert({
          terminal_id: this.terminalInfo.terminal_id,
          name: this.terminalInfo.name,
          location: this.terminalInfo.location,
          ip_address: this.terminalInfo.ip_address,
          mac_address: this.terminalInfo.mac_address,
          status: 'online',
          last_heartbeat: new Date().toISOString(),
          version: this.terminalInfo.version,
          uptime: 0,
          settings_version: 1,
          is_active: true
        }, {
          onConflict: 'terminal_id'
        })

      if (error) {
        throw new Error(`Registration failed: ${error.message}`)
      }

      console.log('Terminal registered successfully:', this.terminalInfo.terminal_id)

    } catch (error) {
      console.error('Terminal registration failed:', error)
      // Don't throw here - allow POS to continue working offline
    }
  }

  private async applyInitialSettings(settings: SettingsCollection): Promise<void> {
    try {
      if (!this.databaseManager) {
        console.warn('DatabaseManager not available, skipping initial settings')
        return
      }
      
      // Update settings through database manager
      await this.databaseManager.updateLocalSettings('terminal', settings)

      this.updateSettingsHash(settings)
      
    } catch (error) {
      console.error('Failed to apply initial settings:', error)
    }
  }

  private startHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }

    // Send heartbeat every 30 seconds
    this.heartbeatInterval = setInterval(async () => {
      await this.sendHeartbeat()
    }, 30000)

    // Send initial heartbeat immediately
    this.sendHeartbeat()
    this.isRunning = true
  }

  private async sendHeartbeat(): Promise<void> {
    if (!this.terminalInfo) {
      console.warn('Cannot send heartbeat: terminal info not initialized')
      return
    }

    try {
      const heartbeatData: HeartbeatData = {
        terminal_id: this.terminalInfo.terminal_id,
        timestamp: new Date().toISOString(),
        status: 'online',
        version: this.terminalInfo.version,
        uptime: Math.floor((Date.now() - this.startTime) / 1000),
        memory_usage: this.getMemoryUsage(),
        cpu_usage: await this.getCpuUsage(),
        settings_hash: this.settingsHash,
        sync_status: this.syncStatus,
        pending_updates: this.pendingUpdates
      }

      // Update terminal status directly in database
      const { error: terminalError } = await this.supabase
        .from('pos_terminals')
        .update({
          status: 'online',
          last_heartbeat: heartbeatData.timestamp,
          uptime: heartbeatData.uptime
        })
        .eq('terminal_id', this.terminalInfo.terminal_id)

      if (terminalError) {
        throw new Error(`Terminal update failed: ${terminalError.message}`)
      }

      // Insert heartbeat record
      const { error: heartbeatError } = await this.supabase
        .from('pos_heartbeats')
        .insert({
          terminal_id: heartbeatData.terminal_id,
          timestamp: heartbeatData.timestamp,
          status: heartbeatData.status,
          version: heartbeatData.version,
          uptime: heartbeatData.uptime,
          memory_usage: heartbeatData.memory_usage,
          cpu_usage: heartbeatData.cpu_usage,
          settings_hash: heartbeatData.settings_hash,
          sync_status: heartbeatData.sync_status,
          pending_updates: heartbeatData.pending_updates
        })

      if (heartbeatError) {
        console.warn('Failed to insert heartbeat record:', heartbeatError.message)
        // Don't throw here - terminal status update succeeded
      }

      console.log('Heartbeat sent successfully for terminal:', this.terminalInfo.terminal_id)

    } catch (error) {
      console.error('Heartbeat failed:', error)
      // Don't throw - POS should continue working even if heartbeat fails
    }
  }

  private async handlePendingSettings(settingsToSync: SettingsCollection): Promise<void> {
    try {
      
      if (!this.databaseManager) {
        console.warn('DatabaseManager not available, skipping pending settings')
        return
      }
      
      // Update settings through database manager
      await this.databaseManager.updateLocalSettings('terminal', settingsToSync)

      // Update settings hash and sync status
      this.updateSettingsHash(settingsToSync)
      this.setSyncStatus('synced')
      this.pendingUpdates = 0


      // Notify admin dashboard that sync completed
      await this.reportSyncSuccess()

    } catch (error: unknown) {
      console.error('Failed to apply pending settings:', error)
      this.setSyncStatus('failed')
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      await this.reportSyncFailure(errorMessage)
    }
  }

  private async reportSyncSuccess(): Promise<void> {
    if (!this.terminalInfo) return

    try {
      // This would call a specific endpoint to mark sync as successful
      const response = await fetch(`${SUPABASE_CONFIG.url}/rest/v1/pos_configurations`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SUPABASE_CONFIG.anonKey}`,
          'apikey': SUPABASE_CONFIG.anonKey,
          'Prefer': 'return=minimal'
        },
        body: JSON.stringify({
          sync_status: 'synced',
          last_sync_at: new Date().toISOString()
        })
      })

      if (!response.ok) {
        console.warn('Failed to report sync success:', response.statusText)
      }
    } catch (error) {
      console.warn('Failed to report sync success:', error)
    }
  }

  private async reportSyncFailure(errorMessage: string): Promise<void> {
    if (!this.terminalInfo) return

    try {
      const response = await fetch(`${SUPABASE_CONFIG.url}/rest/v1/pos_configurations`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SUPABASE_CONFIG.anonKey}`,
          'apikey': SUPABASE_CONFIG.anonKey,
          'Prefer': 'return=minimal'
        },
        body: JSON.stringify({
          sync_status: 'failed'
        })
      })

      if (!response.ok) {
        console.warn('Failed to report sync failure:', response.statusText)
      }
    } catch (error) {
      console.warn('Failed to report sync failure:', error)
    }
  }

  private getMemoryUsage(): number {
    const totalMemory = os.totalmem()
    const freeMemory = os.freemem()
    const usedMemory = totalMemory - freeMemory
    return Math.round((usedMemory / totalMemory) * 100)
  }

  private async getCpuUsage(): Promise<number> {
    // Simple CPU usage calculation
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage()
      setTimeout(() => {
        const currentUsage = process.cpuUsage(startUsage)
        const totalUsage = currentUsage.user + currentUsage.system
        const cpuPercent = Math.round((totalUsage / 1000000) * 100) // Convert to percentage
        resolve(Math.min(cpuPercent, 100)) // Cap at 100%
      }, 100)
    })
  }

  private updateSettingsHash(settings: SettingsCollection): void {
    const settingsString = JSON.stringify(settings, Object.keys(settings).sort())
    this.settingsHash = crypto.createHash('md5').update(settingsString).digest('hex')
  }

  // Public methods for other services to interact with
  setSyncStatus(status: 'synced' | 'pending' | 'failed'): void {
    this.syncStatus = status
  }

  setPendingUpdates(count: number): void {
    this.pendingUpdates = count
  }

  getTerminalId(): string {
    return this.terminalInfo?.terminal_id || 'unknown'
  }

  getSyncStatus(): 'synced' | 'pending' | 'failed' {
    return this.syncStatus
  }

  stop(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
    this.isRunning = false
  }

  isActive(): boolean {
    return this.isRunning
  }

  // Force a heartbeat (useful for testing or immediate sync)
  async forceHeartbeat(): Promise<void> {
    await this.sendHeartbeat()
  }
}

// No default export needed - use named export