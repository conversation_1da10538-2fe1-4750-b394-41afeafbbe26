{"validCustomer": {"id": "customer-123", "email": "<EMAIL>", "password": "SecurePass123!", "fullName": "<PERSON>", "phone": "+**********", "address": {"street": "123 Main St", "city": "New York", "state": "NY", "zipCode": "10001", "country": "USA"}, "preferences": {"dietaryRestrictions": ["vegetarian"], "allergens": ["nuts"], "favoriteCategories": ["pizza", "salads"]}, "orderHistory": [{"id": "order-456", "date": "2024-01-15T10:30:00Z", "total": 25.99, "status": "delivered"}], "createdAt": "2024-01-01T00:00:00Z", "emailVerified": true, "phoneVerified": true}, "newCustomer": {"email": "<EMAIL>", "password": "NewPass123!", "confirmPassword": "NewPass123!", "fullName": "<PERSON>", "phone": "+**********", "agreeToTerms": true, "subscribeToNewsletter": false}, "invalidCredentials": {"email": "<EMAIL>", "password": "wrongpassword"}, "weakPassword": {"email": "<EMAIL>", "password": "123", "fullName": "Test User", "phone": "+**********"}, "invalidEmail": {"email": "invalid-email", "password": "ValidPass123!", "fullName": "Test User", "phone": "+**********"}, "existingEmail": {"email": "<EMAIL>", "password": "AnotherPass123!", "fullName": "Another User", "phone": "+1555666777"}, "invalidPhone": {"email": "<EMAIL>", "password": "ValidPass123!", "fullName": "Test User", "phone": "invalid-phone"}, "authResponses": {"loginSuccess": {"user": {"id": "customer-123", "email": "<EMAIL>", "fullName": "<PERSON>", "phone": "+**********", "emailVerified": true, "phoneVerified": true}, "session": {"accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "refreshToken": "refresh_token_here", "expiresAt": "2024-12-31T23:59:59Z"}, "message": "Login successful"}, "loginInvalidCredentials": {"error": "Invalid email or password", "code": "INVALID_CREDENTIALS", "statusCode": 401}, "loginAccountLocked": {"error": "Account temporarily locked due to multiple failed login attempts", "code": "ACCOUNT_LOCKED", "statusCode": 423, "retryAfter": 900}, "loginRateLimit": {"error": "Too many login attempts. Please try again later.", "code": "RATE_LIMIT_EXCEEDED", "statusCode": 429, "retryAfter": 300}, "registerSuccess": {"user": {"id": "customer-789", "email": "<EMAIL>", "fullName": "<PERSON>", "phone": "+**********", "emailVerified": false, "phoneVerified": false}, "message": "Registration successful. Please check your email to verify your account.", "verificationEmailSent": true}, "registerEmailExists": {"error": "An account with this email already exists", "code": "EMAIL_ALREADY_EXISTS", "statusCode": 409}, "registerWeakPassword": {"error": "Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character", "code": "WEAK_PASSWORD", "statusCode": 400}, "registerInvalidEmail": {"error": "Please provide a valid email address", "code": "INVALID_EMAIL", "statusCode": 400}, "registerInvalidPhone": {"error": "Please provide a valid phone number", "code": "INVALID_PHONE", "statusCode": 400}, "logoutSuccess": {"message": "Logout successful"}, "sessionValid": {"user": {"id": "customer-123", "email": "<EMAIL>", "fullName": "<PERSON>", "phone": "+**********", "emailVerified": true, "phoneVerified": true}, "session": {"accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "expiresAt": "2024-12-31T23:59:59Z"}}, "sessionExpired": {"error": "Session expired. Please login again.", "code": "SESSION_EXPIRED", "statusCode": 401}, "sessionInvalid": {"error": "Invalid session token", "code": "INVALID_SESSION", "statusCode": 401}, "refreshSuccess": {"session": {"accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9_new...", "refreshToken": "new_refresh_token_here", "expiresAt": "2024-12-31T23:59:59Z"}}, "refreshFailed": {"error": "Invalid refresh token", "code": "INVALID_REFRESH_TOKEN", "statusCode": 401}, "serverError": {"error": "Internal server error", "code": "INTERNAL_ERROR", "statusCode": 500}, "networkError": {"error": "Network connection failed", "code": "NETWORK_ERROR", "statusCode": 0}}, "validationErrors": {"emptyEmail": "Email is required", "emptyPassword": "Password is required", "emptyFullName": "Full name is required", "emptyPhone": "Phone number is required", "invalidEmailFormat": "Please enter a valid email address", "passwordTooShort": "Password must be at least 8 characters long", "passwordMismatch": "Passwords do not match", "termsNotAccepted": "You must accept the terms and conditions", "invalidPhoneFormat": "Please enter a valid phone number"}, "testScenarios": {"socialLogin": {"google": {"email": "<EMAIL>", "name": "Google User", "picture": "https://example.com/avatar.jpg", "provider": "google"}, "facebook": {"email": "<EMAIL>", "name": "Facebook User", "picture": "https://example.com/fb-avatar.jpg", "provider": "facebook"}}, "passwordReset": {"validEmail": "<EMAIL>", "invalidEmail": "<EMAIL>", "resetToken": "reset_token_123", "newPassword": "NewSecurePass123!"}, "emailVerification": {"verificationToken": "verify_token_456", "expiredToken": "expired_token_789", "invalidToken": "invalid_token_000"}, "twoFactorAuth": {"enabled": false, "backupCodes": [], "totpSecret": null}}}