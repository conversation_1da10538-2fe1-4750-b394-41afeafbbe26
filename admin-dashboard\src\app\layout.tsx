import { <PERSON>ada<PERSON> } from 'next'
import '../styles/globals.css'
import { AuthProvider } from '@/contexts/auth-context'
import { I18nProvider } from '@/components/i18n-provider'
import { StagewiseToolbar } from '@stagewise/toolbar-next'
import { ReactPlugin } from '@stagewise-plugins/react'
import { ThemeProvider } from '@/contexts/theme-context'
import { ThemeWrapper } from '@/components/theme-wrapper'
import { GlobalScrollbar } from '@/components/global-scrollbar'
import AdminLayout from '@/components/AdminLayout'
import { ErrorBoundary } from '@/components/error-boundary'

export const metadata: Metadata = {
  title: {
    default: 'Creperie Admin Dashboard',
    template: '%s | Creperie Admin',
  },
  description:
    'Administrative dashboard for managing creperie operations, orders, inventory, and analytics.',
  keywords: ['creperie', 'admin', 'dashboard', 'restaurant', 'management'],
  authors: [
    {
      name: 'Creperie Management Team',
    },
  ],

  creator: '<PERSON>reperie Management System',
  publisher: 'Creperie',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'Creperie Admin Dashboard',
    description: 'Administrative dashboard for managing creperie operations',
    siteName: 'Creperie Admin',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Creperie Admin Dashboard',
    description: 'Administrative dashboard for managing creperie operations',
  },
  robots: {
    index: false,
    // Admin dashboard should not be indexed
    follow: false,
    googleBot: {
      index: false,
      follow: false,
    },
  },
}
export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="transition-colors duration-1000">
        <ErrorBoundary>
          <I18nProvider>
            <AuthProvider>
              <ThemeProvider>
                <GlobalScrollbar />
                <ThemeWrapper>
                  {/* <StagewiseToolbar config={{ plugins: [ReactPlugin] }} /> */}
                  <AdminLayout>
                    {children}
                  </AdminLayout>
                </ThemeWrapper>
              </ThemeProvider>
            </AuthProvider>
          </I18nProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}
