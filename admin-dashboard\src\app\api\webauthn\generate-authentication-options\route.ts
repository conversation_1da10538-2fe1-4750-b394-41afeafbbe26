import { NextRequest, NextResponse } from 'next/server'
import { generateAuthenticationOptions } from '@simplewebauthn/server'
import type { GenerateAuthenticationOptionsOpts } from '@simplewebauthn/server'

// Configuration
const rpID = process.env.WEBAUTHN_RP_ID || 'localhost'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      userVerification = 'preferred',
      allowCredentials = [],
      timeout = 60000,
    } = body

    const options: GenerateAuthenticationOptionsOpts = {
      rpID,
      timeout,
      userVerification,
      allowCredentials: allowCredentials.map((cred: any) => ({
        id: cred.id,
        transports: cred.transports,
      })),
    }

    const authenticationOptions = await generateAuthenticationOptions(options)

    // TODO: Store challenge in session/database for verification
    // For now, we'll store it in a simple in-memory store
    ;(global as any).webauthnAuthChallenges = (global as any).webauthnAuthChallenges || new Map()
    ;(global as any).webauthnAuthChallenges.set(authenticationOptions.challenge, {
      challenge: authenticationOptions.challenge,
      timestamp: Date.now(),
    })

    return NextResponse.json(authenticationOptions)
  } catch (error) {
    console.error('Error generating authentication options:', error)
    return NextResponse.json(
      { error: 'Failed to generate authentication options' },
      { status: 500 }
    )
  }
} 