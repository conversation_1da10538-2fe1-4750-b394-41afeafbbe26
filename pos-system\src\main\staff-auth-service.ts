import { supabase } from '../shared/supabase'
import * as crypto from 'crypto'
import { DatabaseManager } from './database'

export interface StaffAuthResult {
  success: boolean
  sessionId?: string
  staffId?: string
  role?: string
  permissions?: string[]
  error?: string
}

export interface StaffSession {
  id: string
  staff_id: string
  role: string
  permissions: string[]
  terminal_id?: string
  login_at: string
  expires_at: string
}

export interface StaffMember {
  id: string
  staff_code: string
  first_name: string
  last_name: string
  email: string
  role: {
    id: string
    name: string
    display_name: string
    level: number
  }
  branch_id?: string
  department?: string
  is_active: boolean
  can_login_pos: boolean
  last_login_at?: string
}

export class StaffAuthService {
  private db: DatabaseManager
  private currentSession: StaffSession | null = null

  constructor() {
    this.db = new DatabaseManager()
  }

  async initialize(): Promise<void> {
    await this.db.initialize()
  }

  /**
   * Authenticate staff member with PIN
   */
  async authenticateWithPIN(pin: string, terminalId?: string): Promise<StaffAuthResult> {
    try {
      // Verify PIN using Supabase function
      const { data: pinResult, error: pinError } = await supabase
        .rpc('verify_staff_pin', { staff_pin: pin })

      if (pinError || !pinResult?.success) {
        await this.logActivity(null, 'login', 'authentication', null, 'login', {
          method: 'pin',
          result: 'failed',
          error: 'Invalid PIN'
        }, 'failed')

        return {
          success: false,
          error: 'Invalid PIN or staff member not found'
        }
      }

      // Get full staff information
      const { data: staffData, error: staffError } = await supabase
        .from('staff')
        .select(`
          *,
          role:roles(*)
        `)
        .eq('id', pinResult.staff_id)
        .eq('is_active', true)
        .eq('can_login_pos', true)
        .single()

      if (staffError || !staffData) {
        await this.logActivity(null, 'login', 'authentication', null, 'login', {
          method: 'pin',
          result: 'failed',
          error: 'Staff not found or not active'
        }, 'failed')

        return {
          success: false,
          error: 'Staff member not found or access denied'
        }
      }

      // Get staff permissions
      const permissions = await this.getStaffPermissions(staffData.id)

      // Create session
      const sessionId = crypto.randomUUID()
      const expiresAt = new Date(Date.now() + 8 * 60 * 60 * 1000) // 8 hours

      // Store session in Supabase
      const { error: sessionError } = await supabase
        .from('staff_sessions')
        .insert({
          id: sessionId,
          staff_id: staffData.id,
          branch_id: staffData.branch_id,
          terminal_id: terminalId,
          session_token: this.generateSessionToken(),
          platform: 'pos-system',
          login_method: 'pin',
          expires_at: expiresAt.toISOString(),
          is_active: true
        })

      if (sessionError) {
        console.error('Error creating session:', sessionError)
        return {
          success: false,
          error: 'Failed to create session'
        }
      }

      // Store session locally
      await this.db.createStaffSession(
        staffData.id,
        this.hashPin(pin),
        staffData.role.name as 'admin' | 'staff'
      )

      // Update last login
      await supabase
        .from('staff')
        .update({ last_login_at: new Date().toISOString() })
        .eq('id', staffData.id)

      // Set current session
      this.currentSession = {
        id: sessionId,
        staff_id: staffData.id,
        role: staffData.role.name,
        permissions,
        terminal_id: terminalId,
        login_at: new Date().toISOString(),
        expires_at: expiresAt.toISOString()
      }

      // Log successful login
      await this.logActivity(staffData.id, 'login', 'authentication', null, 'login', {
        method: 'pin',
        terminal_id: terminalId,
        role: staffData.role.name
      }, 'success')

      return {
        success: true,
        sessionId,
        staffId: staffData.id,
        role: staffData.role.name,
        permissions
      }

    } catch (error) {
      console.error('Authentication error:', error)
      return {
        success: false,
        error: 'Authentication failed'
      }
    }
  }

  /**
   * Get staff permissions (role + individual overrides)
   */
  async getStaffPermissions(staffId: string): Promise<string[]> {
    try {
      // Get role permissions
      const { data: rolePermissions, error: roleError } = await supabase
        .from('staff')
        .select(`
          role:roles(
            role_permissions(
              permission:permissions(name)
            )
          )
        `)
        .eq('id', staffId)
        .single()

      let permissions: string[] = []

      if (!roleError && rolePermissions?.role) {
        const rolePerms = rolePermissions.role as any
        if (rolePerms.role_permissions) {
          permissions = rolePerms.role_permissions.map(
            (rp: any) => rp.permission.name
          )
        }
      }

      // Get individual permission overrides
      const { data: individualPermissions, error: individualError } = await supabase
        .from('staff_permissions')
        .select(`
          granted,
          permission:permissions(name)
        `)
        .eq('staff_id', staffId)
        .or('expires_at.is.null,expires_at.gt.now()')

      if (!individualError && individualPermissions) {
        individualPermissions.forEach((sp: any) => {
          if (sp.granted) {
            // Grant permission
            if (!permissions.includes(sp.permission.name)) {
              permissions.push(sp.permission.name)
            }
          } else {
            // Revoke permission
            permissions = permissions.filter(p => p !== sp.permission.name)
          }
        })
      }

      return permissions

    } catch (error) {
      console.error('Error getting staff permissions:', error)
      return []
    }
  }

  /**
   * Check if current staff has specific permission
   */
  async hasPermission(permission: string): Promise<boolean> {
    if (!this.currentSession) {
      return false
    }

    return this.currentSession.permissions.includes(permission)
  }

  /**
   * Check if current staff has any of the specified permissions
   */
  async hasAnyPermission(permissions: string[]): Promise<boolean> {
    if (!this.currentSession) {
      return false
    }

    return permissions.some(permission => 
      this.currentSession!.permissions.includes(permission)
    )
  }

  /**
   * Get current session
   */
  getCurrentSession(): StaffSession | null {
    return this.currentSession
  }

  /**
   * Get current staff member
   */
  async getCurrentStaff(): Promise<StaffMember | null> {
    if (!this.currentSession) {
      return null
    }

    try {
      const { data: staffData, error } = await supabase
        .from('staff')
        .select(`
          *,
          role:roles(*)
        `)
        .eq('id', this.currentSession.staff_id)
        .single()

      if (error || !staffData) {
        return null
      }

      return {
        id: staffData.id,
        staff_code: staffData.staff_code,
        first_name: staffData.first_name,
        last_name: staffData.last_name,
        email: staffData.email,
        role: {
          id: staffData.role.id,
          name: staffData.role.name,
          display_name: staffData.role.display_name,
          level: staffData.role.level
        },
        branch_id: staffData.branch_id,
        department: staffData.department,
        is_active: staffData.is_active,
        can_login_pos: staffData.can_login_pos,
        last_login_at: staffData.last_login_at
      }

    } catch (error) {
      console.error('Error getting current staff:', error)
      return null
    }
  }

  /**
   * Logout current session
   */
  async logout(): Promise<void> {
    if (!this.currentSession) {
      return
    }

    try {
      // Update session in Supabase
      await supabase
        .from('staff_sessions')
        .update({
          is_active: false,
          logout_at: new Date().toISOString(),
          logout_reason: 'manual'
        })
        .eq('id', this.currentSession.id)

      // Log logout activity
      await this.logActivity(
        this.currentSession.staff_id,
        'logout',
        'authentication',
        null,
        'logout',
        {
          session_duration: Date.now() - new Date(this.currentSession.login_at).getTime(),
          method: 'manual'
        },
        'success'
      )

      // End local session
      const localSession = await this.db.getActiveSession()
      if (localSession) {
        await this.db.endSession(localSession.id)
      }

      this.currentSession = null

    } catch (error) {
      console.error('Error during logout:', error)
    }
  }

  /**
   * Force logout (admin action)
   */
  async forceLogout(reason: string = 'admin_forced'): Promise<void> {
    if (!this.currentSession) {
      return
    }

    try {
      await supabase
        .from('staff_sessions')
        .update({
          is_active: false,
          logout_at: new Date().toISOString(),
          logout_reason: reason,
          forced_logout: true
        })
        .eq('id', this.currentSession.id)

      // Log forced logout
      await this.logActivity(
        this.currentSession.staff_id,
        'logout',
        'authentication',
        null,
        'force_logout',
        { reason },
        'success'
      )

      this.currentSession = null

    } catch (error) {
      console.error('Error during force logout:', error)
    }
  }

  /**
   * Validate current session
   */
  async validateSession(): Promise<boolean> {
    if (!this.currentSession) {
      return false
    }

    // Check if session expired
    if (new Date() > new Date(this.currentSession.expires_at)) {
      await this.logout()
      return false
    }

    try {
      // Verify session exists in database
      const { data: sessionData, error } = await supabase
        .from('staff_sessions')
        .select('is_active')
        .eq('id', this.currentSession.id)
        .single()

      if (error || !sessionData?.is_active) {
        this.currentSession = null
        return false
      }

      return true

    } catch (error) {
      console.error('Error validating session:', error)
      return false
    }
  }

  /**
   * Track staff activity
   */
  async trackActivity(
    activityType: string,
    resourceType: string | null,
    resourceId: string | null,
    action: string,
    details: Record<string, any> = {},
    result: string = 'success'
  ): Promise<void> {
    if (!this.currentSession) {
      return
    }

    await this.logActivity(
      this.currentSession.staff_id,
      activityType,
      resourceType,
      resourceId,
      action,
      details,
      result
    )
  }

  /**
   * Log staff activity
   */
  private async logActivity(
    staffId: string | null,
    activityType: string,
    resourceType: string | null,
    resourceId: string | null,
    action: string,
    details: Record<string, any> = {},
    result: string = 'success'
  ): Promise<void> {
    try {
      await supabase.rpc('log_staff_activity', {
        staff_uuid: staffId,
        session_uuid: this.currentSession?.id || null,
        activity_type_param: activityType,
        resource_type_param: resourceType,
        resource_id_param: resourceId,
        action_param: action,
        details_param: details,
        result_param: result
      })
    } catch (error) {
      console.error('Error logging activity:', error)
    }
  }

  /**
   * Generate secure session token
   */
  private generateSessionToken(): string {
    return crypto.randomBytes(32).toString('hex')
  }

  /**
   * Hash PIN for local storage
   */
  private hashPin(pin: string): string {
    return crypto.createHash('sha256').update(pin).digest('hex')
  }

  /**
   * Get all active staff sessions (admin function)
   */
  async getActiveSessions(): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('staff_sessions')
        .select(`
          *,
          staff:staff_id(
            staff_code,
            first_name,
            last_name,
            role:roles(display_name)
          )
        `)
        .eq('is_active', true)
        .order('login_at', { ascending: false })

      if (error) throw error
      return data || []

    } catch (error) {
      console.error('Error getting active sessions:', error)
      return []
    }
  }

  /**
   * Force logout all sessions for a specific staff member
   */
  async forceLogoutStaff(staffId: string, reason: string = 'admin_action'): Promise<void> {
    try {
      await supabase
        .from('staff_sessions')
        .update({
          is_active: false,
          logout_at: new Date().toISOString(),
          logout_reason: reason,
          forced_logout: true
        })
        .eq('staff_id', staffId)
        .eq('is_active', true)

    } catch (error) {
      console.error('Error forcing logout for staff:', error)
    }
  }
}

export default StaffAuthService