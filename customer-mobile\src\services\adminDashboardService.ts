import Constants from 'expo-constants';
import { supabase } from '../utils/supabase';

// Admin Dashboard API configuration
const ADMIN_API_URL = Constants.expoConfig?.extra?.ADMIN_API_URL || 'http://localhost:3001/api';
const ADMIN_DASHBOARD_URL = Constants.expoConfig?.extra?.ADMIN_DASHBOARD_URL || 'http://localhost:3001';

export interface AppConfig {
  id: string;
  app_name: string;
  app_version: string;
  maintenance_mode: boolean;
  force_update: boolean;
  min_supported_version: string;
  features: {
    loyalty_program: boolean;
    biometric_auth: boolean;
    voice_ordering: boolean;
    offline_mode: boolean;
    dark_mode: boolean;
    push_notifications: boolean;
    location_services: boolean;
    social_login: boolean;
  };
  theme: {
    primary_color: string;
    secondary_color: string;
    accent_color: string;
    background_color: string;
    text_color: string;
  };
  business_hours: {
    monday: { open: string; close: string; is_open: boolean };
    tuesday: { open: string; close: string; is_open: boolean };
    wednesday: { open: string; close: string; is_open: boolean };
    thursday: { open: string; close: string; is_open: boolean };
    friday: { open: string; close: string; is_open: boolean };
    saturday: { open: string; close: string; is_open: boolean };
    sunday: { open: string; close: string; is_open: boolean };
  };
  contact_info: {
    phone: string;
    email: string;
    address: string;
    website: string;
  };
  social_media: {
    facebook: string;
    instagram: string;
    twitter: string;
    tiktok: string;
  };
  delivery_settings: {
    delivery_fee: number;
    free_delivery_threshold: number;
    delivery_radius: number;
    estimated_delivery_time: number;
  };
  payment_methods: {
    cash: boolean;
    card: boolean;
    digital_wallet: boolean;
    bank_transfer: boolean;
  };
  created_at: string;
  updated_at: string;
}

export interface AppAnalytics {
  total_users: number;
  active_users_today: number;
  active_users_week: number;
  active_users_month: number;
  total_orders: number;
  orders_today: number;
  orders_week: number;
  orders_month: number;
  revenue_today: number;
  revenue_week: number;
  revenue_month: number;
  average_order_value: number;
  popular_items: Array<{
    item_id: string;
    item_name: string;
    order_count: number;
  }>;
  user_retention_rate: number;
  app_rating: number;
  crash_rate: number;
  load_time_avg: number;
}

class AdminDashboardService {
  private static instance: AdminDashboardService;

  public static getInstance(): AdminDashboardService {
    if (!AdminDashboardService.instance) {
      AdminDashboardService.instance = new AdminDashboardService();
    }
    return AdminDashboardService.instance;
  }

  // Fetch app configuration from admin dashboard
  async getAppConfig(): Promise<AppConfig | null> {
    try {
      const { data, error } = await supabase
        .from('app_config')
        .select('*')
        .eq('app_type', 'customer_mobile')
        .single();

      if (error) {
        console.error('Error fetching app config:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getAppConfig:', error);
      return null;
    }
  }

  // Update app configuration (admin only)
  async updateAppConfig(config: Partial<AppConfig>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('app_config')
        .update({
          ...config,
          updated_at: new Date().toISOString(),
        })
        .eq('app_type', 'customer_mobile');

      if (error) {
        console.error('Error updating app config:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateAppConfig:', error);
      return false;
    }
  }

  // Send app analytics to admin dashboard
  async sendAnalytics(analyticsData: {
    user_id?: string;
    event_type: string;
    event_data: any;
    timestamp: string;
    app_version: string;
    device_info: {
      platform: string;
      os_version: string;
      app_version: string;
      device_model: string;
    };
  }): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('app_analytics')
        .insert({
          ...analyticsData,
          app_type: 'customer_mobile',
        });

      if (error) {
        console.error('Error sending analytics:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in sendAnalytics:', error);
      return false;
    }
  }

  // Get app analytics (admin only)
  async getAppAnalytics(timeframe: 'today' | 'week' | 'month' | 'year' = 'month'): Promise<AppAnalytics | null> {
    try {
      // This would typically call an admin API endpoint that aggregates analytics data
      const { data, error } = await supabase
        .rpc('get_customer_app_analytics', { timeframe });

      if (error) {
        console.error('Error fetching app analytics:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getAppAnalytics:', error);
      return null;
    }
  }

  // Check for app updates
  async checkForUpdates(currentVersion: string): Promise<{
    hasUpdate: boolean;
    latestVersion: string;
    isForced: boolean;
    updateUrl?: string;
    releaseNotes?: string;
  }> {
    try {
      const config = await this.getAppConfig();
      
      if (!config) {
        return {
          hasUpdate: false,
          latestVersion: currentVersion,
          isForced: false,
        };
      }

      const hasUpdate = this.compareVersions(currentVersion, config.app_version) < 0;
      const isForced = config.force_update && 
        this.compareVersions(currentVersion, config.min_supported_version) < 0;

      return {
        hasUpdate,
        latestVersion: config.app_version,
        isForced,
        updateUrl: `${ADMIN_DASHBOARD_URL}/downloads/customer-mobile`,
        releaseNotes: `Update to version ${config.app_version}`,
      };
    } catch (error) {
      console.error('Error checking for updates:', error);
      return {
        hasUpdate: false,
        latestVersion: currentVersion,
        isForced: false,
      };
    }
  }

  // Send crash reports to admin dashboard
  async sendCrashReport(crashData: {
    error_message: string;
    stack_trace: string;
    user_id?: string;
    app_version: string;
    device_info: any;
    timestamp: string;
  }): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('crash_reports')
        .insert({
          ...crashData,
          app_type: 'customer_mobile',
        });

      if (error) {
        console.error('Error sending crash report:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in sendCrashReport:', error);
      return false;
    }
  }

  // Send user feedback to admin dashboard
  async sendUserFeedback(feedback: {
    user_id: string;
    rating: number;
    comment: string;
    category: 'bug' | 'feature_request' | 'general' | 'complaint' | 'compliment';
    app_version: string;
    timestamp: string;
  }): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_feedback')
        .insert({
          ...feedback,
          app_type: 'customer_mobile',
        });

      if (error) {
        console.error('Error sending user feedback:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in sendUserFeedback:', error);
      return false;
    }
  }

  // Helper method to compare version strings
  private compareVersions(version1: string, version2: string): number {
    const v1parts = version1.split('.').map(Number);
    const v2parts = version2.split('.').map(Number);
    
    for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
      const v1part = v1parts[i] || 0;
      const v2part = v2parts[i] || 0;
      
      if (v1part < v2part) return -1;
      if (v1part > v2part) return 1;
    }
    
    return 0;
  }
}

export default AdminDashboardService;
