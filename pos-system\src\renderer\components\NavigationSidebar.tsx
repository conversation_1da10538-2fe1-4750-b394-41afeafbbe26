import React from "react";
import { useTheme } from '../contexts/theme-context';
import { ThemeSwitcher } from './ThemeSwitcher';

interface NavigationItem {
  id: string;
  title: string;
  icon: string;
  color: string;
}

interface NavigationSidebarProps {
  currentView: 'dashboard' | 'reports';
  onViewChange: (view: 'dashboard' | 'reports') => void;
  onLogout: () => void;
}

const NavigationSidebar: React.FC<NavigationSidebarProps> = ({
  currentView,
  onViewChange,
  onLogout
}) => {
  const { resolvedTheme } = useTheme();

  const navItems: NavigationItem[] = [
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: '📊',
      color: 'blue'
    },
    {
      id: 'reports',
      title: 'Reports',
      icon: '📈',
      color: 'purple'
    }
  ];

  const getColorClasses = (color: string, isActive: boolean) => {
    const colors = {
      blue: isActive ? 'bg-blue-500/30 text-blue-200' : 'hover:bg-blue-500/20 hover:text-blue-300',
      green: isActive ? 'bg-green-500/30 text-green-200' : 'hover:bg-green-500/20 hover:text-green-300',
      purple: isActive ? 'bg-purple-500/30 text-purple-200' : 'hover:bg-purple-500/20 hover:text-purple-300',
      orange: isActive ? 'bg-orange-500/30 text-orange-200' : 'hover:bg-orange-500/20 hover:text-orange-300'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const getIconGlow = (color: string, isActive: boolean) => {
    const glowColors = {
      blue: isActive ? 'text-blue-400 drop-shadow-[0_0_8px_rgba(59,130,246,0.8)]' : 'hover:text-blue-400 hover:drop-shadow-[0_0_8px_rgba(59,130,246,0.8)]',
      green: isActive ? 'text-green-400 drop-shadow-[0_0_8px_rgba(34,197,94,0.8)]' : 'hover:text-green-400 hover:drop-shadow-[0_0_8px_rgba(34,197,94,0.8)]',
      purple: isActive ? 'text-purple-400 drop-shadow-[0_0_8px_rgba(168,85,247,0.8)]' : 'hover:text-purple-400 hover:drop-shadow-[0_0_8px_rgba(168,85,247,0.8)]',
      orange: isActive ? 'text-orange-400 drop-shadow-[0_0_8px_rgba(251,146,60,0.8)]' : 'hover:text-orange-400 hover:drop-shadow-[0_0_8px_rgba(251,146,60,0.8)]'
    };
    return glowColors[color as keyof typeof glowColors] || glowColors.blue;
  };

  const getIcon = (id: string) => {
    const icons = {
      dashboard: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 21V7a2 2 0 012-2h4a2 2 0 012 2v14" />
        </svg>
      ),

      reports: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),

    };
    return icons[id as keyof typeof icons] || icons.dashboard;
  };

  return (
    <div className="fixed left-0 top-1/2 transform -translate-y-1/2 z-50 transition-all duration-300">
      <div className={`rounded-r-[1.25rem] p-2 sm:p-3 shadow-lg border-r ${
        resolvedTheme === 'light'
          ? 'bg-white/90 border-gray-200'
          : 'bg-gray-800/90 border-gray-600'
      }`}>
        <nav className="flex flex-col space-y-2 sm:space-y-3">
          {/* Theme Switcher */}
          <div className="flex justify-center">
            <ThemeSwitcher />
          </div>

          {/* Divider */}
          <div className={`w-4 sm:w-6 h-px mx-auto ${
            resolvedTheme === 'light' ? 'bg-black/20' : 'bg-white/20'
          }`}></div>

          {/* Navigation Items */}
          {navItems.map((item) => (
            <button
              key={item.id}
              onClick={() => onViewChange(item.id as 'dashboard' | 'reports')}
              className={`w-8 h-8 sm:w-10 sm:h-10 rounded-xl flex items-center justify-center transition-all duration-300 transform hover:scale-110 active:scale-95 ${
                currentView === item.id
                  ? `bg-white/10 backdrop-blur-sm border border-white/20 ${getIconGlow(item.color, true)}`
                  : `${resolvedTheme === 'light' ? 'text-gray-400' : 'text-gray-500'} ${getIconGlow(item.color, false)}`
              }`}
              title={item.title}
            >
              {getIcon(item.id)}
            </button>
          ))}

          {/* Divider */}
          <div className={`w-6 h-px mx-auto ${
            resolvedTheme === 'light' ? 'bg-black/20' : 'bg-white/20'
          }`}></div>

          {/* Logout Button */}
          <button
            onClick={onLogout}
            className={`w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 ${
              resolvedTheme === 'light' ? 'text-gray-400' : 'text-gray-500'
            } hover:text-red-400 hover:drop-shadow-[0_0_8px_rgba(239,68,68,0.8)]`}
            title="Logout"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </button>
        </nav>
      </div>
    </div>
  );
};

export default NavigationSidebar;