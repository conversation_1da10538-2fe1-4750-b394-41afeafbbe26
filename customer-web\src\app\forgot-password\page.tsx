import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { GlassCard, GlassButton, GlassInput } from '@/components/ui/glass-components';

export const metadata: Metadata = {
  title: 'Forgot Password | Delicious Crepes & Waffles',
  description: 'Reset your password to regain access to your account.',
};

export default function ForgotPasswordPage() {
  return (
    <main className="min-h-screen py-12 px-4 flex items-center justify-center">
      <div className="max-w-md w-full">
        <div className="text-center mb-8">
          <Link href="/" className="inline-block">
            <Image
              src="/images/logo.svg"
              alt="Delicious Crepes & Waffles"
              width={150}
              height={60}
              className="mx-auto"
            />
          </Link>
          <h1 className="text-3xl font-bold mt-6">Forgot Password</h1>
          <p className="text-muted-foreground mt-2">
            Enter your email address and we'll send you a link to reset your password
          </p>
        </div>

        <GlassCard>
          <div className="p-6">
            <form>
              <div className="space-y-4">
                <GlassInput
                  label="Email Address"
                  type="email"
                  placeholder="Enter your email address"
                  required
                />

                <GlassButton variant="primary" className="w-full">
                  Send Reset Link
                </GlassButton>
              </div>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-muted-foreground">
                Remembered your password?{' '}
                <Link href="/login" className="text-primary hover:underline">
                  Back to login
                </Link>
              </p>
            </div>
          </div>
        </GlassCard>

        <div className="mt-8 text-center">
          <p className="text-sm text-muted-foreground">
            Need help?{' '}
            <Link href="/contact" className="text-primary hover:underline">
              Contact Support
            </Link>
          </p>
        </div>
      </div>
    </main>
  );
}
