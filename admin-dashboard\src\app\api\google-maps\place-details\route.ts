import { NextRequest, NextResponse } from 'next/server';

const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY || '';

async function getGooglePlaceDetails(place_id: string) {
  try {
    // Build the API URL for Google Places API Place Details
    const baseUrl = 'https://maps.googleapis.com/maps/api/place/details/json';
    const params = new URLSearchParams({
      place_id: place_id,
      key: GOOGLE_MAPS_API_KEY,
      fields: 'place_id,formatted_address,address_components,geometry,name',
      language: 'en'
    });


    const response = await fetch(`${baseUrl}?${params.toString()}`);
    
    if (!response.ok) {
      throw new Error(`Google Places Details API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.status !== 'OK') {
      throw new Error(`Google Places Details API status: ${data.status} - ${data.error_message || 'Unknown error'}`);
    }

    return data;
  } catch (error) {
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { place_id } = await request.json();

    if (!place_id) {
      return NextResponse.json(
        { error: 'Missing place_id parameter' },
        { status: 400 }
      );
    }

    if (!GOOGLE_MAPS_API_KEY) {
      return NextResponse.json(
        { error: 'Google Maps API key not configured' },
        { status: 500 }
      );
    }

    try {
      // Use real Google Places Details API
      const placeDetails = await getGooglePlaceDetails(place_id);
      
      return NextResponse.json(placeDetails);
    } catch (detailsError) {
      const errorMessage = detailsError instanceof Error ? detailsError.message : 'Unknown error';
      return NextResponse.json(
        { error: `Failed to get place details: ${errorMessage}` },
        { status: 500 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to get place details' },
      { status: 500 }
    );
  }
} 