import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';

interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
}

interface EditOrderItemsModalProps {
  isOpen: boolean;
  orderCount: number;
  initialItems: OrderItem[];
  onSave: (items: OrderItem[], orderNotes?: string) => void;
  onClose: () => void;
}

export const EditOrderItemsModal: React.FC<EditOrderItemsModalProps> = ({
  isOpen,
  orderCount,
  initialItems,
  onSave,
  onClose
}) => {
  const [items, setItems] = useState<OrderItem[]>(initialItems);
  const [orderNotes, setOrderNotes] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setItems([...initialItems]);
      setOrderNotes('');
    }
  }, [isOpen, initialItems]);

  if (!isOpen) return null;

  const handleSave = async () => {
    // Validate items
    const validItems = items.filter(item => item.quantity > 0);
    
    if (validItems.length === 0) {
      toast.error('At least one item must have a quantity greater than 0');
      return;
    }

    setIsSaving(true);
    
    try {
      // Simulate save delay
      await new Promise(resolve => setTimeout(resolve, 500));
      onSave(validItems, orderNotes);
      setIsSaving(false);
    } catch (error) {
      setIsSaving(false);
      toast.error('Failed to save order items');
    }
  };

  const handleClose = () => {
    setItems([...initialItems]); // Reset form
    setOrderNotes('');
    onClose();
  };

  const updateItemQuantity = (itemId: string, quantity: number) => {
    setItems(prev => prev.map(item => 
      item.id === itemId 
        ? { ...item, quantity: Math.max(0, quantity) }
        : item
    ));
  };

  const updateItemNotes = (itemId: string, notes: string) => {
    setItems(prev => prev.map(item => 
      item.id === itemId 
        ? { ...item, notes }
        : item
    ));
  };

  const removeItem = (itemId: string) => {
    setItems(prev => prev.filter(item => item.id !== itemId));
  };

  const calculateTotal = () => {
    return items.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-2xl p-6 w-full max-w-2xl border border-gray-200/50 dark:border-white/10 max-h-[90vh] overflow-hidden flex flex-col">
        <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
          Edit Order Items
        </h3>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          Modify items and quantities for {orderCount} selected order(s).
        </p>
        
        <div className="flex-1 overflow-y-auto space-y-4 mb-6">
          {/* Order Items */}
          <div className="space-y-3">
            {items.map((item) => (
              <div key={item.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50/50 dark:bg-gray-800/50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-white">{item.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">${item.price.toFixed(2)} each</p>
                  </div>
                  
                  {/* Quantity Controls */}
                  <div className="flex items-center gap-3">
                    <button
                      onClick={() => updateItemQuantity(item.id, item.quantity - 1)}
                      className="w-8 h-8 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors flex items-center justify-center"
                    >
                      -
                    </button>
                    
                    <input
                      type="number"
                      value={item.quantity}
                      onChange={(e) => updateItemQuantity(item.id, parseInt(e.target.value) || 0)}
                      className="w-16 text-center p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      min="0"
                    />
                    
                    <button
                      onClick={() => updateItemQuantity(item.id, item.quantity + 1)}
                      className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors flex items-center justify-center"
                    >
                      +
                    </button>
                    
                    <button
                      onClick={() => removeItem(item.id)}
                      className="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-600 dark:hover:text-red-400 transition-colors flex items-center justify-center"
                    >
                      ×
                    </button>
                  </div>
                </div>
                
                {/* Item Notes */}
                <div>
                  <input
                    type="text"
                    value={item.notes || ''}
                    onChange={(e) => updateItemNotes(item.id, e.target.value)}
                    placeholder="Add special instructions for this item..."
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-sm"
                  />
                </div>
                
                {/* Item Total */}
                <div className="mt-2 text-right">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    Subtotal: ${(item.price * item.quantity).toFixed(2)}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Order Notes */}
          <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Order Notes
            </label>
            <textarea
              value={orderNotes}
              onChange={(e) => setOrderNotes(e.target.value)}
              placeholder="Add general notes for this order..."
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none"
              rows={3}
              maxLength={500}
            />
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {orderNotes.length}/500 characters
            </div>
          </div>
        </div>

        {/* Total and Actions */}
        <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
          <div className="flex items-center justify-between mb-4">
            <span className="text-lg font-semibold text-gray-900 dark:text-white">
              Total: ${calculateTotal().toFixed(2)}
            </span>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {items.filter(item => item.quantity > 0).length} items
            </span>
          </div>
          
          <div className="flex gap-3">
            <button
              onClick={handleClose}
              disabled={isSaving}
              className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={isSaving || items.filter(item => item.quantity > 0).length === 0}
              className={`
                flex-1 px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2
                ${(items.filter(item => item.quantity > 0).length === 0 || isSaving)
                  ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  : 'bg-green-500 hover:bg-green-600 text-white'
                }
              `}
            >
              {isSaving && (
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              )}
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditOrderItemsModal; 