/**
 * WebAuthn Service for Biometric Authentication
 * 
 * This service provides comprehensive WebAuthn functionality including:
 * - Biometric authentication (Touch ID, Face ID, Windows Hello)
 * - Hardware security keys (<PERSON><PERSON><PERSON><PERSON>, etc.)
 * - Passwordless authentication
 * - Passkey management
 */

import {
  startRegistration,
  startAuthentication,
  browserSupportsWebAuthn,
  browserSupportsWebAuthnAutofill,
  platformAuthenticatorIsAvailable,
} from '@simplewebauthn/browser'
import type {
  RegistrationResponseJSON,
  AuthenticationResponseJSON,
  PublicKeyCredentialCreationOptionsJSON,
  PublicKeyCredentialRequestOptionsJSON,
} from '@simplewebauthn/types'

// Types
export interface WebAuthnCredential {
  id: string
  publicKey: string
  counter: number
  deviceType: 'singleDevice' | 'multiDevice'
  backedUp: boolean
  transports?: AuthenticatorTransport[]
  createdAt: Date
  lastUsed: Date
  nickname?: string
  aaguid?: string
}

export interface BiometricCapabilities {
  webAuthnSupported: boolean
  platformAuthenticatorAvailable: boolean
  conditionalMediationSupported: boolean
  userVerifyingPlatformAuthenticatorAvailable: boolean
  availableAuthenticators: string[]
}

export interface WebAuthnRegistrationOptions {
  userId: string
  userEmail: string
  userName: string
  userDisplayName: string
  requireResidentKey?: boolean
  userVerification?: UserVerificationRequirement
  authenticatorSelection?: AuthenticatorSelectionCriteria
  attestation?: AttestationConveyancePreference
}

export interface WebAuthnAuthenticationOptions {
  userVerification?: UserVerificationRequirement
  allowCredentials?: PublicKeyCredentialDescriptor[]
  timeout?: number
}

class WebAuthnService {
  private readonly rpID: string
  private readonly rpName: string
  private readonly origin: string

  constructor() {
    this.rpID = process.env.NEXT_PUBLIC_WEBAUTHN_RP_ID || 'localhost'
    this.rpName = process.env.NEXT_PUBLIC_WEBAUTHN_RP_NAME || 'Creperie Admin'
    this.origin = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'
  }

  /**
   * Check browser and device biometric capabilities
   */
  async getBiometricCapabilities(): Promise<BiometricCapabilities> {
    const webAuthnSupported = browserSupportsWebAuthn()
    const platformAuthenticatorAvailable = await platformAuthenticatorIsAvailable()
    const conditionalMediationSupported = await browserSupportsWebAuthnAutofill()
    
    // Check for specific authenticator types
    const availableAuthenticators: string[] = []
    
    if (webAuthnSupported) {
      availableAuthenticators.push('webauthn')
    }
    
    if (platformAuthenticatorAvailable) {
      // Detect platform-specific authenticators
      const userAgent = navigator.userAgent.toLowerCase()
      
      if (userAgent.includes('mac')) {
        availableAuthenticators.push('touch-id', 'face-id')
      } else if (userAgent.includes('win')) {
        availableAuthenticators.push('windows-hello')
      } else if (userAgent.includes('android')) {
        availableAuthenticators.push('android-biometric')
      } else if (userAgent.includes('iphone') || userAgent.includes('ipad')) {
        availableAuthenticators.push('face-id', 'touch-id')
      }
    }

    return {
      webAuthnSupported,
      platformAuthenticatorAvailable,
      conditionalMediationSupported,
      userVerifyingPlatformAuthenticatorAvailable: platformAuthenticatorAvailable,
      availableAuthenticators,
    }
  }

  /**
   * Generate registration options for WebAuthn credential creation
   */
  async generateRegistrationOptions(
    options: WebAuthnRegistrationOptions
  ): Promise<PublicKeyCredentialCreationOptionsJSON> {
    const { userId, userEmail, userName, userDisplayName } = options

    // Call backend API to generate registration options
    const response = await fetch('/api/webauthn/generate-registration-options', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        userEmail,
        userName,
        userDisplayName,
        requireResidentKey: options.requireResidentKey ?? true,
        userVerification: options.userVerification ?? 'preferred',
        authenticatorSelection: options.authenticatorSelection ?? {
          authenticatorAttachment: 'platform',
          userVerification: 'preferred',
          requireResidentKey: true,
        },
        attestation: options.attestation ?? 'direct',
      }),
    })

    if (!response.ok) {
      throw new Error(`Failed to generate registration options: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Register a new WebAuthn credential (biometric or security key)
   */
  async registerCredential(
    options: WebAuthnRegistrationOptions,
    nickname?: string
  ): Promise<{ success: boolean; credential?: WebAuthnCredential; error?: string }> {
    try {
      // Check browser support
      if (!browserSupportsWebAuthn()) {
        throw new Error('WebAuthn is not supported in this browser')
      }

      // Generate registration options from server
      const registrationOptions = await this.generateRegistrationOptions(options)

      // Start WebAuthn registration process
      const registrationResponse = await startRegistration({ optionsJSON: registrationOptions })

      // Verify registration with server
      const verificationResponse = await fetch('/api/webauthn/verify-registration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: options.userId,
          registrationResponse,
          nickname,
        }),
      })

      const verificationResult = await verificationResponse.json()

      if (verificationResult.verified) {
        return {
          success: true,
          credential: {
            id: verificationResult.credentialID,
            publicKey: verificationResult.credentialPublicKey,
            counter: verificationResult.counter,
            deviceType: verificationResult.credentialDeviceType,
            backedUp: verificationResult.credentialBackedUp,
            transports: registrationResponse.response.transports as AuthenticatorTransport[],
            createdAt: new Date(),
            lastUsed: new Date(),
            nickname,
            aaguid: verificationResult.aaguid,
          },
        }
      } else {
        return {
          success: false,
          error: 'Failed to verify registration',
        }
      }
    } catch (error) {
      console.error('WebAuthn registration error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Registration failed',
      }
    }
  }

  /**
   * Generate authentication options for WebAuthn login
   */
  async generateAuthenticationOptions(
    options: WebAuthnAuthenticationOptions = {}
  ): Promise<PublicKeyCredentialRequestOptionsJSON> {
    const response = await fetch('/api/webauthn/generate-authentication-options', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userVerification: options.userVerification ?? 'preferred',
        allowCredentials: options.allowCredentials,
        timeout: options.timeout ?? 60000,
      }),
    })

    if (!response.ok) {
      throw new Error(`Failed to generate authentication options: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Authenticate using WebAuthn (biometric or security key)
   */
  async authenticateWithWebAuthn(
    options: WebAuthnAuthenticationOptions = {}
  ): Promise<{ success: boolean; userId?: string; credentialId?: string; error?: string }> {
    try {
      // Check browser support
      if (!browserSupportsWebAuthn()) {
        throw new Error('WebAuthn is not supported in this browser')
      }

      // Generate authentication options from server
      const authenticationOptions = await this.generateAuthenticationOptions(options)

      // Start WebAuthn authentication process
      const authenticationResponse = await startAuthentication({ optionsJSON: authenticationOptions })

      // Verify authentication with server
      const verificationResponse = await fetch('/api/webauthn/verify-authentication', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          authenticationResponse,
        }),
      })

      const verificationResult = await verificationResponse.json()

      if (verificationResult.verified) {
        return {
          success: true,
          userId: verificationResult.userId,
          credentialId: verificationResult.credentialId,
        }
      } else {
        return {
          success: false,
          error: 'Authentication verification failed',
        }
      }
    } catch (error) {
      console.error('WebAuthn authentication error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      }
    }
  }

  /**
   * Authenticate with conditional UI (autofill)
   */
  async authenticateWithConditionalUI(): Promise<{
    success: boolean
    userId?: string
    credentialId?: string
    error?: string
  }> {
    try {
      // Check if conditional mediation is supported
      if (!(await browserSupportsWebAuthnAutofill())) {
        throw new Error('Conditional UI is not supported in this browser')
      }

      // Generate authentication options
      const authenticationOptions = await this.generateAuthenticationOptions({
        userVerification: 'preferred',
      })

      // Start authentication with conditional UI
      const authenticationResponse = await startAuthentication({
        optionsJSON: authenticationOptions,
        useBrowserAutofill: true
      })

      // Verify authentication with server
      const verificationResponse = await fetch('/api/webauthn/verify-authentication', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          authenticationResponse,
        }),
      })

      const verificationResult = await verificationResponse.json()

      if (verificationResult.verified) {
        return {
          success: true,
          userId: verificationResult.userId,
          credentialId: verificationResult.credentialId,
        }
      } else {
        return {
          success: false,
          error: 'Authentication verification failed',
        }
      }
    } catch (error) {
      console.error('Conditional UI authentication error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      }
    }
  }

  /**
   * Get user's registered credentials
   */
  async getUserCredentials(userId: string): Promise<WebAuthnCredential[]> {
    try {
      const response = await fetch(`/api/webauthn/credentials/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch credentials: ${response.statusText}`)
      }

      return response.json()
    } catch (error) {
      console.error('Error fetching user credentials:', error)
      return []
    }
  }

  /**
   * Delete a WebAuthn credential
   */
  async deleteCredential(credentialId: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/webauthn/credentials/${credentialId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      return response.ok
    } catch (error) {
      console.error('Error deleting credential:', error)
      return false
    }
  }

  /**
   * Update credential nickname
   */
  async updateCredentialNickname(credentialId: string, nickname: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/webauthn/credentials/${credentialId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nickname }),
      })

      return response.ok
    } catch (error) {
      console.error('Error updating credential nickname:', error)
      return false
    }
  }

  /**
   * Check if user has any registered credentials
   */
  async hasRegisteredCredentials(userId: string): Promise<boolean> {
    const credentials = await this.getUserCredentials(userId)
    return credentials.length > 0
  }

  /**
   * Get human-readable authenticator name
   */
  getAuthenticatorName(aaguid?: string, userAgent?: string): string {
    // Common AAGUID mappings
    const aaguidMap: Record<string, string> = {
      '08987058-cadc-4b81-b6e1-30de50dcbe96': 'Windows Hello',
      'dd4ec289-e01d-41c9-bb89-70fa845d4bf2': 'Chrome Touch ID',
      '39c6f1e3-8f03-4b4b-8b4b-8b4b8b4b8b4b': 'Touch ID',
      '6d44ba9b-f6ec-2e49-b930-0c8fe920cb73': 'Chrome on Android',
    }

    if (aaguid && aaguidMap[aaguid]) {
      return aaguidMap[aaguid]
    }

    // Fallback to user agent detection
    const ua = userAgent || navigator.userAgent.toLowerCase()
    if (ua.includes('mac')) return 'Touch ID / Face ID'
    if (ua.includes('win')) return 'Windows Hello'
    if (ua.includes('android')) return 'Android Biometric'
    if (ua.includes('iphone') || ua.includes('ipad')) return 'Face ID / Touch ID'

    return 'Biometric Authenticator'
  }
}

// Export singleton instance
export const webAuthnService = new WebAuthnService()
export default webAuthnService 