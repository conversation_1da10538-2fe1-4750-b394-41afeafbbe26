import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: customerId } = await params;
    const body = await request.json();
    
    
    const {
      address,
      postal_code,
      floor_number,
      notes,
      address_type = 'delivery',
      is_default = false
    } = body;

    if (!address) {
      return NextResponse.json(
        { success: false, error: 'Address is required' },
        { status: 400 }
      );
    }


    // Parse the address to extract street and city
    const addressParts = address.trim().split(',');
    const streetAddress = addressParts[0]?.trim() || address.trim();
    
    // Extract city more intelligently - remove postal codes and numbers
    let city = 'Athens'; // default
    if (addressParts.length > 1) {
      const cityPart = addressParts[addressParts.length - 1]?.trim() || '';
      // Remove postal codes (numbers) from the city part
      const cityWithoutPostal = cityPart.replace(/\d+/g, '').trim();
      city = cityWithoutPostal || 'Athens';
    }

    const supabase = createServerSupabaseClient();

    // Insert the new address into customer_addresses table
    const { data: newAddress, error: insertError } = await supabase
      .from('customer_addresses')
      .insert({
        customer_id: customerId,
        street_address: streetAddress,
        city: city,
        postal_code: postal_code?.trim() || null,
        floor_number: floor_number?.trim() || null,
        notes: notes?.trim() || null,
        address_type,
        is_default
      })
      .select()
      .single();


    if (insertError) {
      return NextResponse.json(
        { success: false, error: `Failed to save address: ${insertError.message}` },
        { status: 500 }
      );
    }


    // If this is set as default, update other addresses to not be default
    if (is_default) {
      await supabase
        .from('customer_addresses')
        .update({ is_default: false })
        .eq('customer_id', customerId)
        .neq('id', newAddress.id);
    }

    return NextResponse.json({
      success: true,
      address: newAddress,
      message: 'Address added successfully'
    });

  } catch (error) {
    return NextResponse.json(
      { success: false, error: `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve all addresses for a customer
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: customerId } = await params;

    const supabase = createServerSupabaseClient();

    const { data: addresses, error } = await supabase
      .from('customer_addresses')
      .select('*')
      .eq('customer_id', customerId)
      .order('is_default', { ascending: false })
      .order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json(
        { success: false, error: `Failed to fetch addresses: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      addresses: addresses || []
    });

  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 