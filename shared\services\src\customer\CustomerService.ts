/**
 * Customer Service - Centralized customer business logic
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { z } from 'zod';
import { StructuredLogger } from '../../../logging/src/StructuredLogger';

// Validation schemas
const CustomerSchema = z.object({
  id: z.string().uuid().optional(),
  phone: z.string().min(10).max(15),
  name: z.string().min(1).max(100),
  email: z.string().email().optional(),
  address: z.string().optional(),
  postal_code: z.string().optional(),
  floor_number: z.string().optional(),
  notes: z.string().optional(),
  name_on_ringer: z.string().optional()
});

const AddressSchema = z.object({
  id: z.string().uuid().optional(),
  customer_id: z.string().uuid(),
  street_address: z.string().min(1),
  city: z.string().min(1),
  postal_code: z.string().optional(),
  floor_number: z.string().optional(),
  notes: z.string().optional(),
  address_type: z.enum(['delivery', 'billing', 'other']).default('delivery'),
  is_default: z.boolean().default(false)
});

export type Customer = z.infer<typeof CustomerSchema>;
export type CustomerAddress = z.infer<typeof AddressSchema>;

export interface CustomerSearchParams {
  phone?: string;
  name?: string;
  email?: string;
  limit?: number;
  offset?: number;
}

export interface CustomerCreateParams extends Omit<Customer, 'id'> {}
export interface CustomerUpdateParams extends Partial<Customer> {
  id: string;
}

export class CustomerService {
  private supabase: SupabaseClient;
  protected logger: StructuredLogger;

  constructor(supabaseUrl: string, supabaseKey: string, logger: StructuredLogger) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.logger = logger;
  }

  /**
   * Search customers by phone, name, or email
   */
  async searchCustomers(params: CustomerSearchParams): Promise<Customer[]> {
    try {
      this.logger.info('Searching customers', { params });

      let query = this.supabase
        .from('customers')
        .select('*');

      if (params.phone) {
        query = query.ilike('phone', `%${params.phone}%`);
      }

      if (params.name) {
        query = query.ilike('name', `%${params.name}%`);
      }

      if (params.email) {
        query = query.ilike('email', `%${params.email}%`);
      }

      if (params.limit) {
        query = query.limit(params.limit);
      }

      if (params.offset) {
        query = query.range(params.offset, params.offset + (params.limit || 10) - 1);
      }

      const { data, error } = await query;

      if (error) {
        this.logger.error('Customer search failed', { error, params });
        throw new Error(`Customer search failed: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      this.logger.error('Customer search error', { error, params });
      throw error;
    }
  }

  /**
   * Get customer by ID with addresses
   */
  async getCustomerById(id: string): Promise<Customer & { addresses?: CustomerAddress[] } | null> {
    try {
      this.logger.info('Getting customer by ID', { id });

      const { data: customer, error } = await this.supabase
        .from('customers')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') { // Not found
          return null;
        }
        this.logger.error('Get customer failed', { error, id });
        throw new Error(`Get customer failed: ${error.message}`);
      }

      // Get customer addresses
      const { data: addresses, error: addressError } = await this.supabase
        .from('customer_addresses')
        .select('*')
        .eq('customer_id', id);

      if (addressError) {
        this.logger.warn('Failed to get customer addresses', { addressError, id });
      }

      return {
        ...customer,
        addresses: addresses || []
      };
    } catch (error) {
      this.logger.error('Get customer error', { error, id });
      throw error;
    }
  }

  /**
   * Create new customer
   */
  async createCustomer(customerData: CustomerCreateParams): Promise<Customer> {
    try {
      // Validate input
      const validatedData = CustomerSchema.parse(customerData);
      this.logger.info('Creating customer', { phone: validatedData.phone });

      // Check if customer with phone already exists
      const existing = await this.searchCustomers({ phone: validatedData.phone, limit: 1 });
      if (existing.length > 0) {
        throw new Error('Customer with this phone number already exists');
      }

      const { data, error } = await this.supabase
        .from('customers')
        .insert([validatedData])
        .select()
        .single();

      if (error) {
        this.logger.error('Customer creation failed', { error, customerData });
        throw new Error(`Customer creation failed: ${error.message}`);
      }

      this.logger.info('Customer created successfully', { id: data.id });
      return data;
    } catch (error) {
      this.logger.error('Create customer error', { error, customerData });
      throw error;
    }
  }

  /**
   * Update customer
   */
  async updateCustomer(params: CustomerUpdateParams): Promise<Customer> {
    try {
      const { id, ...updateData } = params;
      const validatedData = CustomerSchema.partial().parse(updateData);
      this.logger.info('Updating customer', { id });

      const { data, error } = await this.supabase
        .from('customers')
        .update({
          ...validatedData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        this.logger.error('Customer update failed', { error, id });
        throw new Error(`Customer update failed: ${error.message}`);
      }

      this.logger.info('Customer updated successfully', { id });
      return data;
    } catch (error) {
      this.logger.error('Update customer error', { error, params });
      throw error;
    }
  }

  /**
   * Add address to customer
   */
  async addCustomerAddress(addressData: Omit<CustomerAddress, 'id'>): Promise<CustomerAddress> {
    try {
      const validatedData = AddressSchema.parse(addressData);
      this.logger.info('Adding customer address', { customerId: validatedData.customer_id });

      // If this is set as default, update other addresses
      if (validatedData.is_default) {
        await this.supabase
          .from('customer_addresses')
          .update({ is_default: false })
          .eq('customer_id', validatedData.customer_id);
      }

      const { data, error } = await this.supabase
        .from('customer_addresses')
        .insert([validatedData])
        .select()
        .single();

      if (error) {
        this.logger.error('Address creation failed', { error, addressData });
        throw new Error(`Address creation failed: ${error.message}`);
      }

      this.logger.info('Address created successfully', { id: data.id });
      return data;
    } catch (error) {
      this.logger.error('Add address error', { error, addressData });
      throw error;
    }
  }

  /**
   * Update customer address
   */
  async updateCustomerAddress(
    addressId: string, 
    customerId: string, 
    updateData: Partial<CustomerAddress>
  ): Promise<CustomerAddress> {
    try {
      const validatedData = AddressSchema.partial().parse(updateData);
      this.logger.info('Updating customer address', { addressId, customerId });

      // If setting as default, update other addresses
      if (validatedData.is_default) {
        await this.supabase
          .from('customer_addresses')
          .update({ is_default: false })
          .eq('customer_id', customerId)
          .neq('id', addressId);
      }

      const { data, error } = await this.supabase
        .from('customer_addresses')
        .update({
          ...validatedData,
          updated_at: new Date().toISOString()
        })
        .eq('id', addressId)
        .eq('customer_id', customerId)
        .select()
        .single();

      if (error) {
        this.logger.error('Address update failed', { error, addressId });
        throw new Error(`Address update failed: ${error.message}`);
      }

      this.logger.info('Address updated successfully', { addressId });
      return data;
    } catch (error) {
      this.logger.error('Update address error', { error, addressId });
      throw error;
    }
  }

  /**
   * Delete customer address
   */
  async deleteCustomerAddress(addressId: string, customerId: string): Promise<void> {
    try {
      this.logger.info('Deleting customer address', { addressId, customerId });

      // Check if address exists and belongs to customer
      const { data: address, error: fetchError } = await this.supabase
        .from('customer_addresses')
        .select('*')
        .eq('id', addressId)
        .eq('customer_id', customerId)
        .single();

      if (fetchError || !address) {
        throw new Error('Address not found or access denied');
      }

      // If this is the default address, set another as default
      if (address.is_default) {
        const { data: otherAddresses } = await this.supabase
          .from('customer_addresses')
          .select('id')
          .eq('customer_id', customerId)
          .neq('id', addressId)
          .limit(1);

        if (otherAddresses && otherAddresses.length > 0) {
          await this.supabase
            .from('customer_addresses')
            .update({ is_default: true })
            .eq('id', otherAddresses[0].id);
        }
      }

      const { error } = await this.supabase
        .from('customer_addresses')
        .delete()
        .eq('id', addressId)
        .eq('customer_id', customerId);

      if (error) {
        this.logger.error('Address deletion failed', { error, addressId });
        throw new Error(`Address deletion failed: ${error.message}`);
      }

      this.logger.info('Address deleted successfully', { addressId });
    } catch (error) {
      this.logger.error('Delete address error', { error, addressId });
      throw error;
    }
  }
}