/**
 * Express Logging Middleware
 * Replaces console.log with structured logging for Express applications
 */

import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { StructuredLogger } from '../StructuredLogger';

interface RequestWithLogger extends Request {
  logger: StructuredLogger;
  startTime: number;
}

export interface ExpressLoggerConfig {
  serviceName: string;
  skipPaths?: string[];
  includeBody?: boolean;
  includeHeaders?: boolean;
  maxBodySize?: number;
}

/**
 * Create Express logging middleware
 */
export function createExpressLogger(config: ExpressLoggerConfig) {
  const logger = new StructuredLogger(config.serviceName);
  const {
    skipPaths = ['/health', '/metrics'],
    includeBody = false,
    includeHeaders = false,
    maxBodySize = 1024
  } = config;

  return (req: RequestWithLogger, res: Response, next: NextFunction) => {
    // Skip logging for specified paths
    if (skipPaths.some(path => req.path.startsWith(path))) {
      return next();
    }

    const requestId = uuidv4();
    const startTime = Date.now();
    
    // Create request-specific logger with correlation ID
    req.logger = logger.child({
      requestId,
      correlationId: req.headers['x-correlation-id'] as string || requestId,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress
    });

    req.startTime = startTime;

    // Add request ID to response headers
    res.set('X-Request-ID', requestId);

    // Log incoming request
    const requestContext: any = {
      method: req.method,
      url: req.originalUrl,
      query: req.query,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      contentType: req.get('Content-Type'),
      contentLength: req.get('Content-Length')
    };

    if (includeHeaders) {
      requestContext.headers = req.headers;
    }

    if (includeBody && req.body) {
      const bodyStr = JSON.stringify(req.body);
      if (bodyStr.length <= maxBodySize) {
        requestContext.body = req.body;
      } else {
        requestContext.bodyTruncated = bodyStr.substring(0, maxBodySize) + '...';
      }
    }

    req.logger.info('Incoming request', requestContext);

    // Override res.json to log response
    const originalJson = res.json;
    res.json = function(body: any) {
      const duration = Date.now() - startTime;
      
      const responseContext: any = {
        statusCode: res.statusCode,
        duration_ms: duration,
        contentType: res.get('Content-Type'),
        contentLength: res.get('Content-Length')
      };

      // Log response body for errors or if configured
      if (res.statusCode >= 400 || includeBody) {
        const bodyStr = JSON.stringify(body);
        if (bodyStr.length <= maxBodySize) {
          responseContext.responseBody = body;
        } else {
          responseContext.responseBodyTruncated = bodyStr.substring(0, maxBodySize) + '...';
        }
      }

      req.logger.api(req.method, req.originalUrl, res.statusCode, duration, responseContext);
      
      return originalJson.call(this, body);
    };

    // Log response when finished (for non-JSON responses)
    res.on('finish', () => {
      if (!res.headersSent || res.get('Content-Type')?.includes('application/json')) {
        return; // Already logged in json override
      }

      const duration = Date.now() - startTime;
      req.logger.api(req.method, req.originalUrl, res.statusCode, duration);
    });

    // Log errors
    res.on('error', (error) => {
      req.logger.error('Response error', error, {
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode
      });
    });

    next();
  };
}

/**
 * Error logging middleware (should be used after other middleware)
 */
export function errorLogger(serviceName: string) {
  const logger = new StructuredLogger(serviceName);

  return (error: Error, req: RequestWithLogger, res: Response, next: NextFunction) => {
    const requestLogger = req.logger || logger;
    
    requestLogger.error('Unhandled error', error, {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode || 500,
      stack: error.stack
    });

    next(error);
  };
}