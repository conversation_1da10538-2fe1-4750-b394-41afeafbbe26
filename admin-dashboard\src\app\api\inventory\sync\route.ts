import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { item_id, terminal_id, force_sync, sync_type } = await request.json()

    if (!terminal_id) {
      return NextResponse.json(
        { error: 'terminal_id is required' },
        { status: 400 }
      )
    }

    const syncOperations = []
    const errors = []

    if (item_id) {
      // Sync specific item
      try {
        // Get the ingredient details
        const { data: ingredient, error: ingredientError } = await supabase
          .from('ingredients')
          .select('*')
          .eq('id', item_id)
          .single()

        if (ingredientError || !ingredient) {
          errors.push(`Ingredient not found: ${item_id}`)
        } else {
          // Create sync operation
          const { data: syncOp, error: syncError } = await supabase
            .from('pos_settings_sync_history')
            .insert({
              terminal_id,
              sync_type: sync_type || 'inventory_item',
              sync_status: 'pending',
              created_at: new Date().toISOString(),
              metadata: {
                ingredient_id: ingredient.id,
                ingredient_name: ingredient.name,
                sync_data: {
                  name: ingredient.name,
                  stock_quantity: ingredient.stock_quantity,
                  min_stock_level: ingredient.min_stock_level,
                  is_available: ingredient.is_available,
                  category_id: ingredient.category_id
                },
                force_sync,
                initiated_by: 'admin_dashboard'
              }
            })
            .select()
            .single()

          if (syncError) {
            errors.push(`Failed to create sync operation for ${ingredient.name}: ${syncError.message}`)
          } else {
            syncOperations.push(syncOp)
          }
        }
      } catch (error) {
        errors.push(`Error syncing item ${item_id}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    } else {
      // Sync all inventory items
      try {
        // Get all ingredients
        const { data: ingredients, error: ingredientsError } = await supabase
          .from('ingredients')
          .select('*')
          .order('name')

        if (ingredientsError) {
          errors.push(`Failed to fetch ingredients: ${ingredientsError.message}`)
        } else if (ingredients) {
          // Create sync operations for all ingredients
          for (const ingredient of ingredients) {
            try {
              const { data: syncOp, error: syncError } = await supabase
                .from('pos_settings_sync_history')
                .insert({
                  terminal_id,
                  sync_type: sync_type || 'inventory_full',
                  sync_status: 'pending',
                  created_at: new Date().toISOString(),
                  metadata: {
                    ingredient_id: ingredient.id,
                    ingredient_name: ingredient.name,
                    sync_data: {
                      name: ingredient.name,
                      stock_quantity: ingredient.stock_quantity,
                      min_stock_level: ingredient.min_stock_level,
                      is_available: ingredient.is_available,
                      category_id: ingredient.category_id
                    },
                    force_sync,
                    initiated_by: 'admin_dashboard',
                    batch_sync: true
                  }
                })
                .select()
                .single()

              if (syncError) {
                errors.push(`Failed to create sync operation for ${ingredient.name}: ${syncError.message}`)
              } else {
                syncOperations.push(syncOp)
              }
            } catch (error) {
              errors.push(`Error creating sync operation for ${ingredient.name}: ${error instanceof Error ? error.message : 'Unknown error'}`)
            }
          }
        }
      } catch (error) {
        errors.push(`Error during full inventory sync: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    // Update terminal sync status
    if (syncOperations.length > 0) {
      try {
        await supabase
          .from('pos_terminals')
          .update({
            pending_updates: syncOperations.length,
            sync_status: 'syncing',
            updated_at: new Date().toISOString()
          })
          .eq('terminal_id', terminal_id)
      } catch (error) {
        console.error('Failed to update terminal sync status:', error)
      }
    }

    const success = syncOperations.length > 0
    const message = item_id 
      ? `Item sync ${success ? 'initiated' : 'failed'}`
      : `Inventory sync ${success ? 'initiated' : 'failed'} - ${syncOperations.length} items queued`

    return NextResponse.json({
      success,
      message,
      sync_operations: syncOperations,
      errors: errors.length > 0 ? errors : undefined,
      summary: {
        total_operations: syncOperations.length,
        failed_operations: errors.length,
        terminal_id,
        sync_type: sync_type || (item_id ? 'inventory_item' : 'inventory_full'),
        force_sync
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Inventory sync error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    const terminalId = searchParams.get('terminal_id')
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '50')

    // Get recent inventory sync operations
    let query = supabase
      .from('pos_settings_sync_history')
      .select('*')
      .in('sync_type', ['inventory_item', 'inventory_full', 'inventory'])
      .order('created_at', { ascending: false })
      .limit(limit)

    if (terminalId) {
      query = query.eq('terminal_id', terminalId)
    }

    if (status) {
      query = query.eq('sync_status', status)
    }

    const { data: operations, error } = await query

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch sync operations' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      operations: operations || [],
      summary: {
        total: operations?.length || 0,
        pending: operations?.filter(op => op.sync_status === 'pending').length || 0,
        in_progress: operations?.filter(op => op.sync_status === 'in_progress').length || 0,
        completed: operations?.filter(op => op.sync_status === 'success').length || 0,
        failed: operations?.filter(op => op.sync_status === 'failed').length || 0
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Inventory sync GET error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
