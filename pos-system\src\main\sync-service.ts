import { DatabaseManager, Order, SyncQueue } from './database';
import { BrowserWindow } from 'electron';
import { SupabaseClient } from '@supabase/supabase-js';
import { getSupabaseClient } from '../shared/supabase-config';

export interface SyncStatus {
  isOnline: boolean;
  lastSync: string | null;
  pendingItems: number;
  syncInProgress: boolean;
  error: string | null;
  terminalHealth: number;
  settingsVersion: number;
  menuVersion: number;
}

export interface EnhancedSyncRequest {
  terminal_id: string;
  sync_types: ('staff_permissions' | 'hardware_config' | 'menu_availability' | 'restaurant_settings')[];
  force_sync?: boolean;
  version_check?: boolean;
}

export interface TerminalHeartbeatData {
  terminal_id: string;
  status: 'online' | 'offline' | 'syncing' | 'error' | 'maintenance';
  version: string;
  uptime: number;
  memory_usage: number;
  cpu_usage: number;
  disk_usage: number;
  settings_hash: string;
  sync_status: 'synced' | 'pending' | 'failed';
  pending_updates: number;
  network_info: {
    ip_address: string;
    connection_quality: 'excellent' | 'good' | 'poor' | 'disconnected';
    latency_ms: number;
  };
  performance_metrics: {
    avg_response_time_ms: number;
    orders_per_hour: number;
    error_rate: number;
  };
}

export class SyncService {
  private supabase: SupabaseClient;
  private dbManager: DatabaseManager;
  private mainWindow: BrowserWindow | null = null;
  private syncInterval: NodeJS.Timeout | null = null;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private realtimeChannel: any = null;
  private enhancedSyncChannels: Map<string, any> = new Map();
  private isOnline: boolean = false;
  private syncInProgress: boolean = false;
  private lastSync: string | null = null;
  private terminalId: string = 'terminal-001';
  private settingsVersion: number = 0;
  private menuVersion: number = 0;
  private systemMetrics = {
    cpu_usage: 0,
    memory_usage: 0,
    disk_usage: 0,
    uptime: 0
  };
  private networkMetrics: {
    ip_address: string;
    connection_quality: 'excellent' | 'good' | 'poor' | 'disconnected';
    latency_ms: number;
  } = {
    ip_address: 'unknown',
    connection_quality: 'disconnected',
    latency_ms: 0
  };
  private performanceMetrics = {
    avg_response_time_ms: 0,
    orders_per_hour: 0,
    error_rate: 0
  };

  constructor(dbManager: DatabaseManager, terminalId?: string) {
    this.dbManager = dbManager;
    this.supabase = getSupabaseClient();
    this.terminalId = terminalId || process.env.TERMINAL_ID || 'terminal-001';
    this.setupNetworkMonitoring();
    this.setupEnhancedSync();
    this.startSystemMonitoring();
  }

  setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window;
  }

  private setupNetworkMonitoring(): void {
    // Check network status periodically
    setInterval(() => {
      this.checkNetworkStatus();
    }, 5000); // Check every 5 seconds
  }

  private setupEnhancedSync(): void {
    // Setup enhanced sync subscriptions
    this.setupEnhancedSyncSubscriptions();
  }

  private startSystemMonitoring(): void {
    // Start system metrics collection
    setInterval(() => {
      this.collectSystemMetrics();
    }, 10000); // Collect every 10 seconds

    // Start heartbeat
    this.startHeartbeat();
  }

  private async setupEnhancedSyncSubscriptions(): Promise<void> {
    try {
      // Subscribe to staff permissions sync
      await this.subscribeToEnhancedSync('staff_permissions_sync');

      // Subscribe to hardware configurations
      await this.subscribeToEnhancedSync('pos_hardware_configurations');

      // Subscribe to enhanced sync queue
      await this.subscribeToEnhancedSync('pos_sync_queue_enhanced');

      console.log('✅ Enhanced sync subscriptions established');
    } catch (error) {
      console.error('❌ Failed to setup enhanced sync subscriptions:', error);
    }
  }

  private async subscribeToEnhancedSync(tableName: string): Promise<void> {
    try {
      const channelName = `enhanced_sync_${tableName}_${this.terminalId}`;

      let channel = this.supabase.channel(channelName);

      switch (tableName) {
        case 'staff_permissions_sync':
          channel = channel.on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'staff_permissions_sync',
              filter: `terminal_id=eq.${this.terminalId},terminal_id=is.null`
            },
            (payload) => this.handleStaffPermissionsSync(payload)
          );
          break;

        case 'pos_hardware_configurations':
          channel = channel.on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'pos_hardware_configurations',
              filter: `terminal_id=eq.${this.terminalId}`
            },
            (payload) => this.handleHardwareConfigSync(payload)
          );
          break;

        case 'pos_sync_queue_enhanced':
          channel = channel.on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'pos_sync_queue_enhanced',
              filter: `target_terminal_id=eq.${this.terminalId},target_terminal_id=is.null`
            },
            (payload) => this.handleEnhancedSyncQueue(payload)
          );
          break;
      }

      await channel.subscribe();
      this.enhancedSyncChannels.set(tableName, channel);

    } catch (error) {
      console.error(`❌ Failed to subscribe to enhanced sync for ${tableName}:`, error);
    }
  }

  private async handleStaffPermissionsSync(payload: any): Promise<void> {
    try {
      console.log('🔄 Staff permissions sync received:', payload);

      const { eventType, new: newRecord } = payload;

      if (eventType === 'INSERT' || eventType === 'UPDATE') {
        const { staff_id, permission_key, permission_value } = newRecord;

        // Update local staff permissions
        await this.updateLocalStaffPermission(staff_id, permission_key, permission_value);

        // Notify renderer
        this.notifyRenderer('staff-permission-update', {
          staff_id,
          permission_key,
          permission_value,
          timestamp: new Date().toISOString()
        });

        console.log(`✅ Staff permission updated: ${permission_key} = ${permission_value} for staff ${staff_id}`);
      }
    } catch (error) {
      console.error('❌ Failed to handle staff permissions sync:', error);
    }
  }

  private async handleHardwareConfigSync(payload: any): Promise<void> {
    try {
      console.log('🔧 Hardware configuration sync received:', payload);

      const { eventType, new: newRecord } = payload;

      if (eventType === 'INSERT' || eventType === 'UPDATE') {
        const { hardware_type, hardware_config, requires_restart } = newRecord;

        // Update local hardware configuration
        await this.updateLocalHardwareConfig(hardware_type, hardware_config);

        // Notify renderer
        this.notifyRenderer('hardware-config-update', {
          hardware_type,
          hardware_config,
          requires_restart,
          timestamp: new Date().toISOString()
        });

        // If restart is required, notify user
        if (requires_restart) {
          this.notifyRenderer('restart-required', {
            reason: `Hardware configuration updated: ${hardware_type}`,
            hardware_type
          });
        }

        console.log(`✅ Hardware configuration updated: ${hardware_type}`);
      }
    } catch (error) {
      console.error('❌ Failed to handle hardware configuration sync:', error);
    }
  }

  private async handleEnhancedSyncQueue(payload: any): Promise<void> {
    try {
      console.log('📋 Enhanced sync queue update received:', payload);

      const { eventType, new: newRecord } = payload;

      if (eventType === 'INSERT' && newRecord.sync_status === 'pending') {
        // Process the sync queue item
        await this.processEnhancedSyncItem(newRecord);
      }
    } catch (error) {
      console.error('❌ Failed to handle enhanced sync queue:', error);
    }
  }

  private async processEnhancedSyncItem(syncItem: any): Promise<void> {
    try {
      const { sync_type, data_changes, resource_id } = syncItem;

      switch (sync_type) {
        case 'menu_availability':
          await this.handleMenuAvailabilitySync(data_changes);
          break;

        case 'restaurant_settings':
          await this.handleRestaurantSettingsSync(data_changes);
          break;

        case 'inventory_update':
          await this.handleInventoryUpdateSync(data_changes);
          break;

        default:
          console.warn(`Unknown sync type: ${sync_type}`);
      }

      // Mark as processed (this would typically be done by the edge function)
      console.log(`✅ Processed sync item: ${sync_type}`);

    } catch (error) {
      console.error(`❌ Failed to process sync item:`, error);
    }
  }

  private async handleMenuAvailabilitySync(data: any): Promise<void> {
    // Update local menu availability
    this.notifyRenderer('menu-availability-update', data);
  }

  private async handleRestaurantSettingsSync(data: any): Promise<void> {
    // Update local restaurant settings
    this.notifyRenderer('restaurant-settings-update', data);
  }

  private async handleInventoryUpdateSync(data: any): Promise<void> {
    // Update local inventory
    this.notifyRenderer('inventory-update', data);
  }

  private async updateLocalStaffPermission(staffId: string, permissionKey: string, permissionValue: boolean): Promise<void> {
    // Update local database with staff permission
    // This would integrate with your local staff management system
    console.log(`Updating local staff permission: ${staffId} - ${permissionKey} = ${permissionValue}`);
  }

  private async updateLocalHardwareConfig(hardwareType: string, config: any): Promise<void> {
    // Update local hardware configuration
    // This would integrate with your hardware management system
    console.log(`Updating local hardware config: ${hardwareType}`, config);
  }

  private async checkNetworkStatus(): Promise<void> {
    try {
      // Test Supabase connectivity by making a simple query
      const { data, error } = await this.supabase
        .from('orders')
        .select('count')
        .limit(1);
      
      const wasOnline = this.isOnline;
      this.isOnline = !error;
      
      // Only notify if status changed
      if (wasOnline !== this.isOnline) {
        this.notifyRenderer('network-status', { isOnline: this.isOnline });
        
        if (this.isOnline) {
        } else {
        }
      }
    } catch (error) {
      const wasOnline = this.isOnline;
      this.isOnline = false;
      
      if (wasOnline !== this.isOnline) {
        this.notifyRenderer('network-status', { isOnline: this.isOnline });
      }
    }
  }

  async startSync(): Promise<void> {
    if (this.syncInProgress || !this.isOnline) {
      return;
    }

    this.syncInProgress = true;
    this.notifyRenderer('sync-status', await this.getSyncStatus());

    try {
      
      // Test Supabase connection first
      const connectionTest = await this.testConnection();
      if (!connectionTest.success) {
        throw new Error(`Supabase connection failed: ${connectionTest.error}`);
      }

      // Sync local changes to remote
      await this.syncLocalToRemote();
      
      // Sync remote changes to local
      await this.syncRemoteToLocal();
      
      // Clean up old sync queue items
      await this.dbManager.clearOldSyncQueue();
      
      // Setup real-time subscriptions
      this.setupRealtimeSubscriptions();
      
      this.lastSync = new Date().toISOString();
      this.notifyRenderer('sync-complete', { timestamp: this.lastSync });
    } catch (error) {
      console.error('Sync failed:', error);
      this.notifyRenderer('sync-error', { error: (error as Error).message });
    } finally {
      this.syncInProgress = false;
      this.notifyRenderer('sync-status', await this.getSyncStatus());
    }
  }

  private async syncLocalToRemote(): Promise<void> {
    const syncQueue = await this.dbManager.getSyncQueue();
    
    for (const item of syncQueue) {
      try {
        await this.processSyncQueueItem(item);
        await this.dbManager.updateSyncQueueItem(item.id, true);
      } catch (error) {
        console.error(`Failed to sync item ${item.id}:`, error);
        await this.dbManager.updateSyncQueueItem(item.id, false, (error as Error).message);
      }
    }
  }

  private async processSyncQueueItem(item: SyncQueue): Promise<void> {
    const data = JSON.parse(item.data);
    
    switch (item.table_name) {
      case 'orders':
        await this.syncOrder(item.operation, item.record_id, data);
        break;
      default:
        throw new Error(`Unknown table: ${item.table_name}`);
    }
  }

  private async syncOrder(operation: string, recordId: string, data: any): Promise<void> {
    switch (operation) {
      case 'insert':
        // Insert order to Supabase
        const { data: insertedOrder, error: insertError } = await this.supabase
          .from('orders')
          .insert({
            customer_name: data.customer_name,
            customer_email: data.customer_email || null,
            customer_phone: data.customer_phone || null,
            order_type: data.order_type || 'takeaway',
            status: data.status,
            total_amount: data.total_amount,
            tax_amount: data.tax_amount || (data.total_amount * 0.1), // Default 10% tax
            discount_amount: data.discount_amount || null,
            payment_status: data.payment_status || 'pending',
            payment_method: data.payment_method || null,
            notes: data.notes || null,
            table_number: data.table_number || null,
            estimated_ready_time: data.estimated_ready_time || null,
            created_at: data.created_at || new Date().toISOString(),
            updated_at: data.updated_at || new Date().toISOString()
          })
          .select()
          .single();
        
        if (insertError) throw insertError;
        
        // Update local order with Supabase ID
        if (insertedOrder) {
          await this.dbManager.updateOrderSupabaseId(recordId, insertedOrder.id);
        }
        break;
        
      case 'update':
        // Get the order from local database to get supabase_id
        const localOrder = await this.dbManager.getOrderById(recordId);
        if (!localOrder || !localOrder.supabase_id) {
          throw new Error('Cannot update: Order not found or missing supabase_id');
        }
        
        // Update order in Supabase
        const { error: updateError } = await this.supabase
          .from('orders')
          .update({
            ...data,
            updated_at: new Date().toISOString()
          })
          .eq('id', localOrder.supabase_id);
        
        if (updateError) throw updateError;
        break;
        
      case 'delete':
        const orderToDelete = await this.dbManager.getOrderById(recordId);
        if (orderToDelete && orderToDelete.supabase_id) {
          // Delete order from Supabase
          const { error: deleteError } = await this.supabase
            .from('orders')
            .delete()
            .eq('id', orderToDelete.supabase_id);
          
          if (deleteError) throw deleteError;
        }
        break;
    }
  }

  private async syncRemoteToLocal(): Promise<void> {
    try {
      // Get the timestamp of the last sync
      const lastSyncTime = this.lastSync || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(); // Default to 24 hours ago
      
      // Fetch updated orders from Supabase
      const { data: remoteOrders, error } = await this.supabase
        .from('orders')
        .select('*')
        .gte('updated_at', lastSyncTime)
        .order('updated_at', { ascending: true });
      
      if (error) throw error;
      
      if (remoteOrders && remoteOrders.length > 0) {
        for (const remoteOrder of remoteOrders) {
          await this.mergeRemoteOrder(remoteOrder);
        }
        
        // Notify renderer about new orders
        this.notifyRenderer('orders-updated', { count: remoteOrders.length });
      }
    } catch (error) {
      console.error('Failed to sync remote to local:', error);
      throw error;
    }
  }

  private async mergeRemoteOrder(remoteOrder: any): Promise<void> {
    // Check if we have a local order with this supabase_id
    const localOrders = await this.dbManager.getOrders();
    const existingOrder = localOrders.find(order => order.supabase_id === remoteOrder.id);
    
    if (existingOrder) {
      // Update existing order if remote is newer
      const remoteUpdated = new Date(remoteOrder.updated_at);
      const localUpdated = new Date(existingOrder.updated_at);
      
      if (remoteUpdated > localUpdated) {
        // Update local order with remote data
        await this.dbManager.updateOrderStatus(existingOrder.id, remoteOrder.status);
      }
    } else {
      // Create new local order from remote data
      const newOrder = {
        customer_name: remoteOrder.customer_name,
        items: remoteOrder.items,
        total_amount: remoteOrder.total_amount,
        status: remoteOrder.status,
        supabase_id: remoteOrder.id
      };
      
      await this.dbManager.insertOrder(newOrder);
    }
  }

  private collectSystemMetrics(): void {
    try {
      // Collect system metrics (simplified for demo)
      this.systemMetrics = {
        cpu_usage: Math.random() * 100, // Replace with actual CPU monitoring
        memory_usage: Math.random() * 100, // Replace with actual memory monitoring
        disk_usage: Math.random() * 100, // Replace with actual disk monitoring
        uptime: process.uptime()
      };

      // Collect network metrics
      const connectionQuality: 'excellent' | 'good' | 'poor' | 'disconnected' = this.isOnline ? 'excellent' : 'disconnected';
      this.networkMetrics = {
        ip_address: this.getLocalIPAddress(),
        connection_quality: connectionQuality,
        latency_ms: this.isOnline ? Math.random() * 50 : 0
      };

      // Collect performance metrics
      this.performanceMetrics = {
        avg_response_time_ms: Math.random() * 100,
        orders_per_hour: Math.floor(Math.random() * 50),
        error_rate: Math.random() * 5
      };
    } catch (error) {
      console.error('❌ Failed to collect system metrics:', error);
    }
  }

  private getLocalIPAddress(): string {
    try {
      const { networkInterfaces } = require('os');
      const nets = networkInterfaces();

      for (const name of Object.keys(nets)) {
        for (const net of nets[name]) {
          if (net.family === 'IPv4' && !net.internal) {
            return net.address;
          }
        }
      }
      return 'unknown';
    } catch {
      return 'unknown';
    }
  }

  private startHeartbeat(): void {
    // Send heartbeat every 30 seconds
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat();
    }, 30000);
  }

  private async sendHeartbeat(): Promise<void> {
    try {
      if (!this.isOnline) return;

      const heartbeatData: TerminalHeartbeatData = {
        terminal_id: this.terminalId,
        status: this.syncInProgress ? 'syncing' : 'online',
        version: process.env.APP_VERSION || '1.0.0',
        uptime: this.systemMetrics.uptime,
        memory_usage: this.systemMetrics.memory_usage,
        cpu_usage: this.systemMetrics.cpu_usage,
        disk_usage: this.systemMetrics.disk_usage,
        settings_hash: await this.calculateSettingsHash(),
        sync_status: this.syncInProgress ? 'pending' : 'synced',
        pending_updates: (await this.dbManager.getSyncQueue()).length,
        network_info: this.networkMetrics,
        performance_metrics: this.performanceMetrics
      };

      // Send heartbeat to admin dashboard
      const adminDashboardUrl = process.env.ADMIN_DASHBOARD_URL || 'http://localhost:3001';
      const response = await fetch(`${adminDashboardUrl}/api/pos/terminal-heartbeat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(heartbeatData)
      });

      if (!response.ok) {
        console.warn('⚠️ Heartbeat failed:', response.statusText);
      }
    } catch (error) {
      console.error('❌ Failed to send heartbeat:', error);
    }
  }

  private async calculateSettingsHash(): Promise<string> {
    try {
      // Calculate a hash of current settings for change detection
      const settings = await this.dbManager.getLocalSettings();
      return JSON.stringify(settings).length.toString(); // Simplified hash
    } catch {
      return '0';
    }
  }

  async getSyncStatus(): Promise<SyncStatus> {
    const syncQueue = await this.dbManager.getSyncQueue();

    return {
      isOnline: this.isOnline,
      lastSync: this.lastSync,
      pendingItems: syncQueue.length,
      syncInProgress: this.syncInProgress,
      error: null,
      terminalHealth: this.calculateTerminalHealth(),
      settingsVersion: this.settingsVersion,
      menuVersion: this.menuVersion
    };
  }

  private calculateTerminalHealth(): number {
    // Calculate overall terminal health based on various factors
    let health = 1.0;

    if (!this.isOnline) health *= 0.5;
    if (this.systemMetrics.cpu_usage > 80) health *= 0.8;
    if (this.systemMetrics.memory_usage > 90) health *= 0.7;
    if (this.performanceMetrics.error_rate > 10) health *= 0.6;

    return Math.max(0, health);
  }

  startAutoSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    // Auto-sync every 30 seconds when online
    this.syncInterval = setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.startSync();
      }
    }, 30000);
  }

  stopAutoSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  private notifyRenderer(channel: string, data: any): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(channel, data);
    }
  }

  // Setup real-time subscriptions
  setupRealtimeSubscriptions(): void {
    if (!this.isOnline || !this.supabase) return;
    
    try {
      // Clean up existing subscriptions
      if (this.realtimeChannel) {
        this.supabase.removeChannel(this.realtimeChannel);
      }

      // Setup new real-time subscription for orders
      this.realtimeChannel = this.supabase
        .channel('pos_orders_sync')
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'orders' },
          (payload) => {
            this.handleRealtimeOrderChange(payload);
          }
        )
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'customers' },
          (payload) => {
            this.handleRealtimeCustomerChange(payload);
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
          }
        });
    } catch (error) {
      console.error('Failed to setup real-time subscriptions:', error);
    }
  }

  private async handleRealtimeOrderChange(payload: any): Promise<void> {
    try {
      switch (payload.eventType) {
        case 'INSERT':
        case 'UPDATE':
          await this.mergeRemoteOrder(payload.new);
          this.notifyRenderer('order-realtime-update', payload.new);
          break;
          
        case 'DELETE':
          // Handle order deletion if needed
          this.notifyRenderer('order-realtime-delete', payload.old);
          break;
      }
    } catch (error) {
      console.error('Failed to handle real-time change:', error);
    }
  }

  private async handleRealtimeCustomerChange(payload: any): Promise<void> {
    try {
      // Customer changes don't need immediate sync to local DB
      // but we can notify the renderer for UI updates
      this.notifyRenderer('customer-realtime-update', payload);
    } catch (error) {
      console.error('Failed to handle real-time customer change:', error);
    }
  }

  private async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const { data, error } = await this.supabase
        .from('orders')
        .select('count')
        .limit(1);
      
      if (error) {
        return {
          success: false,
          error: error.message,
        };
      }
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async forceSync(): Promise<void> {
    if (this.syncInProgress) {
      return;
    }
    
    await this.startSync();
  }

  getNetworkStatus(): boolean {
    return this.isOnline;
  }

  async requestEnhancedSync(syncTypes: string[], forceSync: boolean = false): Promise<void> {
    try {
      if (!this.isOnline) {
        throw new Error('Cannot sync while offline');
      }

      const syncRequest: EnhancedSyncRequest = {
        terminal_id: this.terminalId,
        sync_types: syncTypes as any,
        force_sync: forceSync,
        version_check: true
      };

      // Call the enhanced sync edge function
      const { data, error } = await this.supabase.functions.invoke('sync-pos-settings', {
        body: {
          ...syncRequest,
          action: 'sync'
        }
      });

      if (error) throw error;

      console.log('✅ Enhanced sync completed:', data);

      // Notify renderer of sync completion
      this.notifyRenderer('enhanced-sync-complete', data);

    } catch (error) {
      console.error('❌ Enhanced sync failed:', error);
      this.notifyRenderer('enhanced-sync-error', { error: (error as Error).message });
    }
  }

  async syncStaffPermissions(forceSync: boolean = false): Promise<void> {
    await this.requestEnhancedSync(['staff_permissions'], forceSync);
  }

  async syncHardwareConfig(forceSync: boolean = false): Promise<void> {
    await this.requestEnhancedSync(['hardware_config'], forceSync);
  }

  async syncMenuAvailability(forceSync: boolean = false): Promise<void> {
    await this.requestEnhancedSync(['menu_availability'], forceSync);
  }

  async syncAllEnhanced(forceSync: boolean = false): Promise<void> {
    await this.requestEnhancedSync([
      'staff_permissions',
      'hardware_config',
      'menu_availability',
      'restaurant_settings'
    ], forceSync);
  }

  // Event emission methods for renderer communication
  private emitToRenderer(eventName: string, data?: any) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(eventName, data);
    }
  }

  private emitSettingsUpdate(data: any) {
    this.emitToRenderer('settings-update', data);
  }

  private emitStaffPermissionUpdate(data: any) {
    this.emitToRenderer('staff-permission-update', data);
  }

  private emitHardwareConfigUpdate(data: any) {
    this.emitToRenderer('hardware-config-update', data);
  }

  private emitRestartRequired(data: any) {
    this.emitToRenderer('restart-required', data);
  }

  private emitSyncError(error: any) {
    this.emitToRenderer('sync-error', error);
  }

  private emitSyncComplete(data: any) {
    this.emitToRenderer('sync-complete', data);
  }

  private emitSyncStatus(status: any) {
    this.emitToRenderer('sync-status', status);
  }

  private emitNetworkStatus(status: any) {
    this.emitToRenderer('network-status', status);
  }

  // Add method to force sync all types
  async forceSyncAll(): Promise<void> {
    try {
      console.log('🔄 Force syncing all data types...');

      // Use the existing syncAllEnhanced method with force sync enabled
      await this.syncAllEnhanced(true);

      this.emitSyncComplete({
        message: 'All data types synced successfully',
        timestamp: new Date().toISOString(),
        syncTypes: ['staff_permissions', 'hardware_config', 'menu_availability', 'restaurant_settings']
      });

      console.log('✅ Force sync completed successfully');
    } catch (error) {
      console.error('❌ Force sync failed:', error);
      this.emitSyncError({
        message: 'Force sync failed',
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  // Cleanup method for shutdown
  cleanup(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.realtimeChannel && this.supabase) {
      this.supabase.removeChannel(this.realtimeChannel);
      this.realtimeChannel = null;
    }

    // Cleanup enhanced sync channels
    for (const [tableName, channel] of this.enhancedSyncChannels) {
      this.supabase.removeChannel(channel);
    }
    this.enhancedSyncChannels.clear();
  }
}