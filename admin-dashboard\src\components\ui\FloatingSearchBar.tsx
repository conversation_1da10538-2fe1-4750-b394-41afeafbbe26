'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Search, X, Command } from 'lucide-react'
import { GlassCard } from './glass-components'

interface SearchResult {
  id: string
  title: string
  description: string
  type: 'menu' | 'customer' | 'order' | 'setting' | 'page'
  icon?: string
  action?: () => void
}

interface FloatingSearchBarProps {
  isOpen: boolean
  onClose: () => void
  onSearch?: (query: string) => void
}

export function FloatingSearchBar({ isOpen, onClose, onSearch }: FloatingSearchBarProps) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [selectedIndex, setSelectedIndex] = useState(0)
  const inputRef = useRef<HTMLInputElement>(null)

  // Mock search results - replace with actual search logic
  const mockResults: SearchResult[] = [
    { id: '1', title: 'Dashboard', description: 'Main dashboard overview', type: 'page', icon: '📊' },
    { id: '2', title: 'Orders', description: 'Manage orders and delivery', type: 'page', icon: '🛒' },
    { id: '3', title: 'Menu Management', description: 'Edit subcategories and categories', type: 'menu', icon: '🍽️' },
    { id: '4', title: 'Customer Database', description: 'View and edit customer information', type: 'customer', icon: '👥' },
    { id: '5', title: 'Payment Settings', description: 'Configure payment methods', type: 'setting', icon: '💳' },
    { id: '6', title: 'Staff Management', description: 'Manage team members and roles', type: 'setting', icon: '👨‍💼' },
  ]

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen])

  useEffect(() => {
    if (query.trim()) {
      const filtered = mockResults.filter(
        result =>
          result.title.toLowerCase().includes(query.toLowerCase()) ||
          result.description.toLowerCase().includes(query.toLowerCase())
      )
      setResults(filtered)
      setSelectedIndex(0)
    } else {
      setResults(mockResults.slice(0, 4)) // Show popular/recent items
    }
  }, [query])

  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'Escape':
        onClose()
        break
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => (prev + 1) % results.length)
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => (prev - 1 + results.length) % results.length)
        break
      case 'Enter':
        e.preventDefault()
        if (results[selectedIndex]) {
          results[selectedIndex].action?.()
          onClose()
        }
        break
    }
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 animate-in fade-in duration-300"
        onClick={onClose}
      />
      
      {/* Search Modal */}
      <div className="fixed top-20 left-1/2 transform -translate-x-1/2 w-full max-w-2xl mx-4 z-50 animate-in slide-in-from-top duration-500">
        <GlassCard className="overflow-hidden glass-glow">
          {/* Search Input */}
          <div className="flex items-center p-4 border-b border-white/10">
            <Search className="h-5 w-5 text-white/60 mr-3" />
            <input
              ref={inputRef}
              type="text"
              placeholder="Search dashboard, orders, customers..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              className="flex-1 bg-transparent text-white placeholder-white/50 outline-none text-lg"
            />
            <div className="flex items-center gap-2 text-white/40 text-sm">
              <kbd className="px-2 py-1 rounded bg-white/10 text-xs">⌘</kbd>
              <kbd className="px-2 py-1 rounded bg-white/10 text-xs">K</kbd>
            </div>
            <button
              onClick={onClose}
              className="ml-3 p-1 rounded-lg hover:bg-white/10 transition-colors"
            >
              <X className="h-4 w-4 text-white/60" />
            </button>
          </div>

          {/* Search Results */}
          <div className="max-h-96 overflow-y-auto">
            {results.length > 0 ? (
              <div className="p-2">
                {!query && (
                  <div className="px-3 py-2 text-sm text-white/60 font-medium">
                    Quick Actions
                  </div>
                )}
                {results.map((result, index) => (
                  <button
                    key={result.id}
                    onClick={() => {
                      result.action?.()
                      onClose()
                    }}
                    className={`w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all duration-300 hover:scale-[1.02] ${
                      index === selectedIndex 
                        ? 'bg-white/20 glass-glow' 
                        : 'hover:bg-white/10'
                    }`}
                  >
                    <span className="text-2xl">{result.icon}</span>
                    <div className="flex-1">
                      <div className="text-white font-medium">{result.title}</div>
                      <div className="text-white/60 text-sm">{result.description}</div>
                    </div>
                    <div className={`text-xs px-2 py-1 rounded-full ${
                      result.type === 'page' ? 'bg-blue-500/20 text-blue-400' :
                      result.type === 'menu' ? 'bg-green-500/20 text-green-400' :
                      result.type === 'customer' ? 'bg-purple-500/20 text-purple-400' :
                      result.type === 'order' ? 'bg-orange-500/20 text-orange-400' :
                      'bg-gray-500/20 text-gray-400'
                    }`}>
                      {result.type}
                    </div>
                  </button>
                ))}
              </div>
            ) : query ? (
              <div className="p-8 text-center text-white/60">
                <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No results found for "{query}"</p>
                <p className="text-sm mt-2">Try searching for orders, customers, or subcategories</p>
              </div>
            ) : (
              <div className="p-8 text-center text-white/60">
                <Command className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Start typing to search</p>
                <p className="text-sm mt-2">Find anything in your dashboard quickly</p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-3 border-t border-white/10 flex items-center justify-between text-xs text-white/40">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <kbd className="px-1.5 py-0.5 rounded bg-white/10">↑</kbd>
                <kbd className="px-1.5 py-0.5 rounded bg-white/10">↓</kbd>
                Navigate
              </span>
              <span className="flex items-center gap-1">
                <kbd className="px-1.5 py-0.5 rounded bg-white/10">⏎</kbd>
                Select
              </span>
              <span className="flex items-center gap-1">
                <kbd className="px-1.5 py-0.5 rounded bg-white/10">esc</kbd>
                Close
              </span>
            </div>
            <span>Powered by Creperie AI</span>
          </div>
        </GlassCard>
      </div>
    </>
  )
}