import React from 'react';

interface SelectedCustomization {
  customizationId: string;
  optionId: string;
  name: string;
  price: number;
}

interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
  menuItemId?: string;
  basePrice?: number;
  customizations?: SelectedCustomization[];
  totalPrice?: number;
}

interface CustomerInfo {
  name: string;
  phone: string;
  address: string;
}

interface CartSummaryProps {
  cartItems: OrderItem[];
  orderType: 'dine-in' | 'takeaway' | 'delivery';
  customerInfo: CustomerInfo;
  onEditCustomer: () => void;
  onUpdateQuantity: (itemId: string, newQuantity: number) => void;
  onRemoveItem: (itemId: string) => void;
  onPlaceOrder: () => void;
}

export const CartSummary: React.FC<CartSummaryProps> = ({
  cartItems,
  orderType,
  customerInfo,
  onEditCustomer,
  onUpdateQuantity,
  onRemoveItem,
  onPlaceOrder
}) => {
  const subtotal = cartItems.reduce((sum, item) => sum + (item.totalPrice || item.price * item.quantity), 0);
  const taxRate = 0.1; // 10% tax
  const tax = subtotal * taxRate;
  const deliveryFee = orderType === 'delivery' ? 5.00 : 0;
  const total = subtotal + tax + deliveryFee;

  if (cartItems.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Order Summary</h2>
        <div className="text-center text-gray-500 py-8">
          <p>Your cart is empty</p>
          <p className="text-sm mt-2">Add items to get started</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold text-gray-900 mb-4">Order Summary</h2>

      {/* Order Type */}
      <div className="mb-4 p-3 bg-blue-50 rounded-lg">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-blue-700">Order Type:</span>
          <span className="text-sm font-semibold text-blue-800 capitalize">
            {orderType.replace('-', ' ')}
          </span>
        </div>
      </div>

      {/* Customer Info */}
      <div className="mb-6 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-medium text-gray-900">Customer</h3>
          <button
            onClick={onEditCustomer}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
          >
            Edit
          </button>
        </div>
        <div className="text-sm text-gray-700 space-y-1">
          <p><span className="font-medium">Name:</span> {customerInfo.name || 'Not provided'}</p>
          <p><span className="font-medium">Phone:</span> {customerInfo.phone || 'Not provided'}</p>
          {customerInfo.address && (
            <p>
              <span className="font-medium">
                {orderType === 'delivery' ? 'Address:' : 
                 orderType === 'dine-in' ? 'Table:' : 'Notes:'}
              </span> {customerInfo.address}
            </p>
          )}
        </div>
      </div>

      {/* Cart Items */}
      <div className="space-y-4 mb-6">
        {cartItems.map((item) => (
          <div key={item.id} className="border-b pb-4">
            <div className="flex justify-between items-start mb-2">
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{item.name}</h4>
                {item.customizations && item.customizations.length > 0 && (
                  <div className="text-sm text-gray-600 mt-1">
                    {item.customizations.map((customization, index) => (
                      <span key={index}>
                        {customization.name}
                        {customization.price > 0 && ` (+$${customization.price.toFixed(2)})`}
                        {index < item.customizations!.length - 1 && ', '}
                      </span>
                    ))}
                  </div>
                )}
                {item.notes && (
                  <p className="text-sm text-gray-600 italic mt-1">
                    Note: {item.notes}
                  </p>
                )}
              </div>
              <button
                onClick={() => onRemoveItem(item.id)}
                className="text-red-500 hover:text-red-700 ml-2"
              >
                ×
              </button>
            </div>
            
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => onUpdateQuantity(item.id, Math.max(1, item.quantity - 1))}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  -
                </button>
                <span className="w-8 text-center font-medium">{item.quantity}</span>
                <button
                  onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  +
                </button>
              </div>
              <span className="font-semibold text-gray-900">
                ${(item.totalPrice || item.price * item.quantity).toFixed(2)}
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Order Totals */}
      <div className="space-y-2 mb-6">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Subtotal:</span>
          <span className="text-gray-900">${subtotal.toFixed(2)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Tax (10%):</span>
          <span className="text-gray-900">${tax.toFixed(2)}</span>
        </div>
        {deliveryFee > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Delivery Fee:</span>
            <span className="text-gray-900">${deliveryFee.toFixed(2)}</span>
          </div>
        )}
        <div className="border-t pt-2">
          <div className="flex justify-between text-lg font-bold">
            <span className="text-gray-900">Total:</span>
            <span className="text-gray-900">${total.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* Place Order Button */}
      <button
        onClick={onPlaceOrder}
        disabled={!customerInfo.name || !customerInfo.phone || (orderType === 'delivery' && !customerInfo.address)}
        className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
      >
        Place Order
      </button>

      {(!customerInfo.name || !customerInfo.phone || (orderType === 'delivery' && !customerInfo.address)) && (
        <p className="text-sm text-red-600 mt-2 text-center">
          Please provide customer information to place order
        </p>
      )}
    </div>
  );
};