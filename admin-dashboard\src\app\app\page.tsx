'use client';

import React, { useState, useEffect } from 'react';
import TestLayout from '@/components/TestLayout';
import { toast } from 'react-hot-toast';
import { useTheme } from '@/contexts/theme-context';
import { 
  Smartphone,
  Bell,
  Settings,
  Monitor,
  Users,
  RefreshCw,
  Save,
  Eye,
  Star,
  Download,
  MessageSquare,
  Globe,
  Shield,
  Zap,
  Apple,
  Play,
  Package
} from 'lucide-react';

// Types for our enhanced app settings
interface AppConfiguration {
  id: string
  platform: string
  config_section: string
  config_key: string
  config_value: any
  data_type: string
  is_feature_flag: boolean
  rollout_percentage: number
  description?: string
  created_at: string
  updated_at: string
}

interface PushNotificationSetting {
  id: string
  notification_type: string
  platform: string
  title_template: string
  body_template: string
  is_enabled: boolean
  priority: number
  created_at: string
  updated_at: string
}

export default function AppPage() {
  const { isDarkTheme } = useTheme()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [selectedPlatform, setSelectedPlatform] = useState('both')
  const [appConfigs, setAppConfigs] = useState<AppConfiguration[]>([])
  const [pushNotificationSettings, setPushNotificationSettings] = useState<PushNotificationSetting[]>([])

  // App Statistics (these would come from your analytics API)
  const appStats = [
    { label: 'Active Users', value: '8,432', change: '+18%', icon: '📱' },
    { label: 'Downloads', value: '25,671', change: '+25%', icon: '⬇️' },
    { label: 'App Rating', value: '4.8', change: '+0.2', icon: '⭐' },
    { label: 'Daily Orders', value: '342', change: '+12%', icon: '🛍️' }
  ]

  // Enhanced App Settings
  const [storeMetadataSettings, setStoreMetadataSettings] = useState({
    ios: {
      app_name: 'Delicious Bites',
      app_subtitle: 'Fresh Food Delivery',
      keywords: 'food,delivery,crepes,restaurant',
      category: 'Food & Drink'
    },
    android: {
      app_name: 'Delicious Bites',
      short_description: 'Order fresh crepes and food',
      category: 'FOOD_AND_DRINK'
    }
  })

  const [featuresSettings, setFeaturesSettings] = useState({
    enable_push_notifications: true,
    enable_location_services: true,
    enable_biometric_auth: true,
    enable_offline_mode: false,
    enable_voice_ordering: false,
    enable_ar_menu: false,
    enable_social_sharing: true,
    enable_dark_mode: true
  })

  const [notificationSettings, setNotificationSettings] = useState({
    order_updates: true,
    promotional_messages: true,
    reminder_notifications: true,
    security_alerts: true,
    app_updates: true
  })

  const [appearanceSettings, setAppearanceSettings] = useState({
    primary_color: '#F59E0B',
    secondary_color: '#6B7280',
    theme_mode: 'system',
    enable_haptic_feedback: true,
    animation_speed: 'normal'
  })

  const [performanceSettings, setPerformanceSettings] = useState({
    cache_duration: 300,
    image_quality: 'high',
    enable_analytics: true,
    crash_reporting: true,
    performance_monitoring: true,
    max_offline_storage: 50
  })

  useEffect(() => {
    loadAppSettings()
  }, [selectedPlatform])

  const loadAppSettings = async () => {
    try {
      setLoading(true)
      
      const [configResponse, pushResponse] = await Promise.all([
        fetch(`/api/app-configurations-enhanced?platform=${selectedPlatform}`),
        fetch(`/api/push-notifications?platform=${selectedPlatform}`)
      ])
      
      const configResult = await configResponse.json()
      const pushResult = await pushResponse.json()
      
      if (configResponse.ok) {
        setAppConfigs(configResult.data)
        
        // Load existing configurations into state
        configResult.data.forEach((config: AppConfiguration) => {
          const key = config.config_key
          const value = config.config_value

          // Update respective settings based on section
          switch (config.config_section) {
            case 'store_metadata':
              if (config.platform === 'ios') {
                setStoreMetadataSettings(prev => ({ 
                  ...prev, 
                  ios: { ...prev.ios, [key]: value }
                }))
              } else if (config.platform === 'android') {
                setStoreMetadataSettings(prev => ({ 
                  ...prev, 
                  android: { ...prev.android, [key]: value }
                }))
              }
              break
            case 'features':
              setFeaturesSettings(prev => ({ ...prev, [key]: value }))
              break
            case 'notifications':
              setNotificationSettings(prev => ({ ...prev, [key]: value }))
              break
            case 'appearance':
              setAppearanceSettings(prev => ({ ...prev, [key]: value }))
              break
            case 'performance':
              setPerformanceSettings(prev => ({ ...prev, [key]: value }))
              break
          }
        })
      }
      
      if (pushResponse.ok) {
        setPushNotificationSettings(pushResult.data)
      }
      
    } catch (error) {
      console.error('Error loading app settings:', error)
      toast.error('Error loading app settings')
    } finally {
      setLoading(false)
    }
  }

  const saveAppConfiguration = async (section: string, settings: any, platform: string = 'both') => {
    try {
      setSaving(true)
      
      // Convert settings to the format expected by the API
      const configurations = Object.entries(settings).map(([key, value]) => ({
        platform,
        config_section: section,
        config_key: key,
        config_value: value,
        data_type: typeof value === 'boolean' ? 'boolean' : 
                  typeof value === 'number' ? 'number' : 'string',
        description: `${section} setting for ${key}`
      }))
      
      const promises = configurations.map(config => 
        fetch('/api/app-configurations-enhanced', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(config)
        })
      )
      
      const responses = await Promise.all(promises)
      const results = await Promise.all(responses.map(r => r.json()))
      
      const allSuccessful = responses.every(r => r.ok)
      
      if (allSuccessful) {
        toast.success(`${section} settings saved successfully!`)
        await loadAppSettings() // Reload to get updated data
      } else {
        throw new Error('Some settings failed to save')
      }
      
    } catch (error) {
      console.error('Error saving app configuration:', error)
      toast.error('Failed to save settings')
    } finally {
      setSaving(false)
    }
  }

  const tabs = [
    { id: 'overview', name: 'Overview & Stats', icon: Monitor },
    { id: 'store-metadata', name: 'Store Metadata', icon: Package },
    { id: 'features', name: 'Features & Toggles', icon: Zap },
    { id: 'notifications', name: 'Push Notifications', icon: Bell },
    { id: 'appearance', name: 'Appearance & Theme', icon: Smartphone },
    { id: 'performance', name: 'Performance', icon: Settings }
  ]

  if (loading) {
    return (
      <TestLayout>
        <div className="p-8 flex items-center justify-center">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-500" />
            <p className="text-gray-600 dark:text-gray-400">Loading Mobile App Configuration...</p>
          </div>
        </div>
      </TestLayout>
    )
  }

  return (
    <TestLayout>
      <div className="p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Mobile App Management</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Configure and manage your iOS and Android applications with app store metadata, features, and settings
          </p>
        </div>
        
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-end mb-8">
            <div>
              <div className="flex space-x-3">
                <div className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800">
                  <select
                    value={selectedPlatform}
                    onChange={(e) => setSelectedPlatform(e.target.value)}
                    className="bg-transparent text-gray-900 dark:text-white border-none outline-none cursor-pointer"
                  >
                    <option value="both" className="bg-white dark:bg-gray-800">Both Platforms</option>
                    <option value="ios" className="bg-white dark:bg-gray-800">iOS Only</option>
                    <option value="android" className="bg-white dark:bg-gray-800">Android Only</option>
                  </select>
                </div>
                <button
                  onClick={() => loadAppSettings()}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </button>
              </div>
            </div>
          </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:w-64">
            <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
              isDarkTheme
                ? 'bg-white/10 border-white/20'
                : 'bg-black/10 border-black/20'
            }`}>
              <div className="space-y-3">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-4 py-3 rounded-xl text-left transition-all duration-300 ${
                        activeTab === tab.id
                          ? isDarkTheme
                            ? 'bg-white/20 border border-white/30 text-white'
                            : 'bg-black/20 border border-black/30 text-black'
                          : isDarkTheme
                            ? 'text-white/70 hover:bg-white/10 hover:text-white'
                            : 'text-black/70 hover:bg-black/10 hover:text-black'
                      }`}
                    >
                      <Icon className="h-5 w-5 mr-3" />
                      {tab.name}
                    </button>
                  )
                })}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Overview & Stats Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* App Stats */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {appStats.map((stat, index) => (
                    <div
                      key={index}
                      className="backdrop-blur-md bg-white/10 border border-white/20 rounded-2xl p-6 hover:bg-white/15 transition-all duration-300"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <div className="text-2xl">{stat.icon}</div>
                        <div className={`text-sm px-2 py-1 rounded-full ${
                          stat.change.startsWith('+') 
                            ? 'bg-green-500/20 text-green-300' 
                            : 'bg-red-500/20 text-red-300'
                        }`}>
                          {stat.change}
                        </div>
                      </div>
                      <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                      <div className="text-white/70 text-sm">{stat.label}</div>
                    </div>
                  ))}
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* App Store Status */}
                  <div className="backdrop-blur-md bg-white/10 border border-white/20 rounded-2xl p-6">
                    <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                      <Package className="h-6 w-6 mr-3" />
                      App Store Status
                    </h3>
                    <div className="space-y-6">
                      <div className="flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10">
                        <div className="flex items-center">
                          <Apple className="h-6 w-6 mr-3 text-white" />
                          <div>
                            <div className="text-white font-medium">iOS App Store</div>
                            <div className="text-white/60 text-sm">Version 2.1.4</div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span className="text-green-300">Live</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between p-4 rounded-xl bg-white/5 border border-white/10">
                        <div className="flex items-center">
                          <Play className="h-6 w-6 mr-3 text-white" />
                          <div>
                            <div className="text-white font-medium">Google Play Store</div>
                            <div className="text-white/60 text-sm">Version 2.1.4</div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                          <span className="text-green-300">Live</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="backdrop-blur-md bg-white/10 border border-white/20 rounded-2xl p-6">
                    <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                      <Zap className="h-6 w-6 mr-3" />
                      Quick Actions
                    </h3>
                    <div className="space-y-4">
                      <button 
                        onClick={() => setActiveTab('notifications')}
                        className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-xl font-semibold hover:from-blue-600 hover:to-purple-700 transition-all duration-300 flex items-center justify-center"
                      >
                        <Bell className="h-4 w-4 mr-2" />
                        Manage Push Notifications
                      </button>
                      <button 
                        onClick={() => setActiveTab('features')}
                        className="w-full bg-gradient-to-r from-green-500 to-teal-600 text-white py-3 rounded-xl font-semibold hover:from-green-600 hover:to-teal-700 transition-all duration-300 flex items-center justify-center"
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Configure Features
                      </button>
                      <button 
                        onClick={() => setActiveTab('appearance')}
                        className="w-full bg-gradient-to-r from-orange-500 to-red-600 text-white py-3 rounded-xl font-semibold hover:from-orange-600 hover:to-red-700 transition-all duration-300 flex items-center justify-center"
                      >
                        <Smartphone className="h-4 w-4 mr-2" />
                        Update App Theme
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Store Metadata Tab */}
            {activeTab === 'store-metadata' && (
              <div className="backdrop-blur-md bg-white/10 border border-white/20 rounded-2xl p-8">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-3xl font-bold text-white">App Store Metadata</h2>
                </div>

                <div className="space-y-8">
                  {/* iOS Settings */}
                  <div className="space-y-6">
                    <h3 className="text-xl font-semibold text-white flex items-center">
                      <Apple className="h-6 w-6 mr-3" />
                      iOS App Store
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                        <label className="block text-sm font-medium text-white mb-4">
                          App Name
                        </label>
                        <input
                          type="text"
                          value={storeMetadataSettings.ios.app_name}
                          onChange={(e) => setStoreMetadataSettings(prev => ({
                            ...prev,
                            ios: { ...prev.ios, app_name: e.target.value }
                          }))}
                          className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                          placeholder="Delicious Bites"
                        />
                      </div>

                      <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                        <label className="block text-sm font-medium text-white mb-4">
                          App Subtitle
                        </label>
                        <input
                          type="text"
                          value={storeMetadataSettings.ios.app_subtitle}
                          onChange={(e) => setStoreMetadataSettings(prev => ({
                            ...prev,
                            ios: { ...prev.ios, app_subtitle: e.target.value }
                          }))}
                          className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                          placeholder="Fresh Food Delivery"
                        />
                      </div>

                      <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                        <label className="block text-sm font-medium text-white mb-4">
                          Keywords
                        </label>
                        <input
                          type="text"
                          value={storeMetadataSettings.ios.keywords}
                          onChange={(e) => setStoreMetadataSettings(prev => ({
                            ...prev,
                            ios: { ...prev.ios, keywords: e.target.value }
                          }))}
                          className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                          placeholder="food,delivery,crepes,restaurant"
                        />
                      </div>

                      <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                        <label className="block text-sm font-medium text-white mb-4">
                          Category
                        </label>
                        <select
                          value={storeMetadataSettings.ios.category}
                          onChange={(e) => setStoreMetadataSettings(prev => ({
                            ...prev,
                            ios: { ...prev.ios, category: e.target.value }
                          }))}
                          className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                        >
                          <option value="Food & Drink" className="bg-gray-800">Food & Drink</option>
                          <option value="Business" className="bg-gray-800">Business</option>
                          <option value="Lifestyle" className="bg-gray-800">Lifestyle</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Android Settings */}
                  <div className="space-y-6">
                    <h3 className="text-xl font-semibold text-white flex items-center">
                      <Play className="h-6 w-6 mr-3" />
                      Google Play Store
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                        <label className="block text-sm font-medium text-white mb-4">
                          App Name
                        </label>
                        <input
                          type="text"
                          value={storeMetadataSettings.android.app_name}
                          onChange={(e) => setStoreMetadataSettings(prev => ({
                            ...prev,
                            android: { ...prev.android, app_name: e.target.value }
                          }))}
                          className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                          placeholder="Delicious Bites"
                        />
                      </div>

                      <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                        <label className="block text-sm font-medium text-white mb-4">
                          Short Description
                        </label>
                        <input
                          type="text"
                          value={storeMetadataSettings.android.short_description}
                          onChange={(e) => setStoreMetadataSettings(prev => ({
                            ...prev,
                            android: { ...prev.android, short_description: e.target.value }
                          }))}
                          className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                          placeholder="Order fresh crepes and food"
                        />
                      </div>

                      <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                        <label className="block text-sm font-medium text-white mb-4">
                          Category
                        </label>
                        <select
                          value={storeMetadataSettings.android.category}
                          onChange={(e) => setStoreMetadataSettings(prev => ({
                            ...prev,
                            android: { ...prev.android, category: e.target.value }
                          }))}
                          className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                        >
                          <option value="FOOD_AND_DRINK" className="bg-gray-800">Food & Drink</option>
                          <option value="BUSINESS" className="bg-gray-800">Business</option>
                          <option value="LIFESTYLE" className="bg-gray-800">Lifestyle</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-4">
                    <button
                      onClick={() => saveAppConfiguration('store_metadata', storeMetadataSettings.ios, 'ios')}
                      disabled={saving}
                      className="flex items-center px-6 py-3 backdrop-blur-md bg-gradient-to-r from-blue-500/20 to-gray-600/20 border border-white/30 text-white rounded-2xl hover:from-blue-500/30 hover:to-gray-600/30 transition-all duration-300 disabled:opacity-50"
                    >
                      {saving ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Apple className="h-4 w-4 mr-2" />}
                      Save iOS Settings
                    </button>
                    <button
                      onClick={() => saveAppConfiguration('store_metadata', storeMetadataSettings.android, 'android')}
                      disabled={saving}
                      className="flex items-center px-6 py-3 backdrop-blur-md bg-gradient-to-r from-green-500/20 to-blue-600/20 border border-white/30 text-white rounded-2xl hover:from-green-500/30 hover:to-blue-600/30 transition-all duration-300 disabled:opacity-50"
                    >
                      {saving ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Play className="h-4 w-4 mr-2" />}
                      Save Android Settings
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Features Tab */}
            {activeTab === 'features' && (
              <div className="backdrop-blur-md bg-white/10 border border-white/20 rounded-2xl p-8">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-3xl font-bold text-white">App Features & Toggles</h2>
                </div>

                <div className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      { key: 'enable_push_notifications', label: 'Push Notifications', desc: 'Enable push notifications for order updates and promotions', icon: '🔔' },
                      { key: 'enable_location_services', label: 'Location Services', desc: 'Use GPS for delivery tracking and nearby restaurant detection', icon: '📍' },
                      { key: 'enable_biometric_auth', label: 'Biometric Authentication', desc: 'Allow fingerprint and face recognition login', icon: '🔐' },
                      { key: 'enable_offline_mode', label: 'Offline Mode', desc: 'Enable basic functionality when offline', icon: '📵' },
                      { key: 'enable_voice_ordering', label: 'Voice Ordering', desc: 'Voice-activated ordering system', icon: '🎤' },
                      { key: 'enable_ar_menu', label: 'AR Menu', desc: 'Augmented reality menu visualization', icon: '👓' },
                      { key: 'enable_social_sharing', label: 'Social Sharing', desc: 'Share orders and reviews on social media', icon: '📱' },
                      { key: 'enable_dark_mode', label: 'Dark Mode', desc: 'Support for dark theme', icon: '🌙' },
                    ].map((feature) => (
                      <div key={feature.key} className="flex items-center justify-between p-6 backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl">
                        <div className="flex items-center">
                          <span className="text-2xl mr-4">{feature.icon}</span>
                          <div>
                            <h4 className="font-medium text-white">{feature.label}</h4>
                            <p className="text-sm text-white/60">{feature.desc}</p>
                          </div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={featuresSettings[feature.key as keyof typeof featuresSettings] as boolean}
                            onChange={(e) => setFeaturesSettings(prev => ({
                              ...prev,
                              [feature.key]: e.target.checked
                            }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-white/20 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600/80 backdrop-blur-md"></div>
                        </label>
                      </div>
                    ))}
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={() => saveAppConfiguration('features', featuresSettings)}
                      disabled={saving}
                      className="flex items-center px-8 py-4 backdrop-blur-md bg-gradient-to-r from-blue-500/20 to-purple-600/20 border border-white/30 text-white rounded-2xl hover:from-blue-500/30 hover:to-purple-600/30 transition-all duration-300 disabled:opacity-50"
                    >
                      {saving ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
                      Save Features Settings
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Push Notifications Tab */}
            {activeTab === 'notifications' && (
              <div className="backdrop-blur-md bg-white/10 border border-white/20 rounded-2xl p-8">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-3xl font-bold text-white">Push Notification Settings</h2>
                </div>

                <div className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      { key: 'order_updates', label: 'Order Status Updates', desc: 'Notify customers about order progress and delivery status', icon: '📦' },
                      { key: 'promotional_messages', label: 'Promotional Messages', desc: 'Send special offers and discounts to customers', icon: '🎁' },
                      { key: 'reminder_notifications', label: 'Reminder Notifications', desc: 'Cart abandonment and reorder reminders', icon: '⏰' },
                      { key: 'security_alerts', label: 'Security Alerts', desc: 'Login attempts and account security notifications', icon: '🔒' },
                      { key: 'app_updates', label: 'App Update Notifications', desc: 'Notify users about new app versions and features', icon: '📱' },
                    ].map((notification) => (
                      <div key={notification.key} className="flex items-center justify-between p-6 backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl">
                        <div className="flex items-center">
                          <span className="text-2xl mr-4">{notification.icon}</span>
                          <div>
                            <h4 className="font-medium text-white">{notification.label}</h4>
                            <p className="text-sm text-white/60">{notification.desc}</p>
                          </div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notificationSettings[notification.key as keyof typeof notificationSettings] as boolean}
                            onChange={(e) => setNotificationSettings(prev => ({
                              ...prev,
                              [notification.key]: e.target.checked
                            }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-white/20 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600/80 backdrop-blur-md"></div>
                        </label>
                      </div>
                    ))}
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={() => saveAppConfiguration('notifications', notificationSettings)}
                      disabled={saving}
                      className="flex items-center px-8 py-4 backdrop-blur-md bg-gradient-to-r from-blue-500/20 to-purple-600/20 border border-white/30 text-white rounded-2xl hover:from-blue-500/30 hover:to-purple-600/30 transition-all duration-300 disabled:opacity-50"
                    >
                      {saving ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
                      Save Notification Settings
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Appearance Tab */}
            {activeTab === 'appearance' && (
              <div className="backdrop-blur-md bg-white/10 border border-white/20 rounded-2xl p-8">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-3xl font-bold text-white">App Appearance & Theme</h2>
                </div>

                <div className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Primary Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={appearanceSettings.primary_color}
                          onChange={(e) => setAppearanceSettings(prev => ({
                            ...prev,
                            primary_color: e.target.value
                          }))}
                          className="w-12 h-10 rounded-xl border-2 border-white/20 bg-transparent"
                        />
                        <input
                          type="text"
                          value={appearanceSettings.primary_color}
                          onChange={(e) => setAppearanceSettings(prev => ({
                            ...prev,
                            primary_color: e.target.value
                          }))}
                          className="flex-1 px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                          placeholder="#F59E0B"
                        />
                      </div>
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Secondary Color
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          value={appearanceSettings.secondary_color}
                          onChange={(e) => setAppearanceSettings(prev => ({
                            ...prev,
                            secondary_color: e.target.value
                          }))}
                          className="w-12 h-10 rounded-xl border-2 border-white/20 bg-transparent"
                        />
                        <input
                          type="text"
                          value={appearanceSettings.secondary_color}
                          onChange={(e) => setAppearanceSettings(prev => ({
                            ...prev,
                            secondary_color: e.target.value
                          }))}
                          className="flex-1 px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                          placeholder="#6B7280"
                        />
                      </div>
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Theme Mode
                      </label>
                      <select
                        value={appearanceSettings.theme_mode}
                        onChange={(e) => setAppearanceSettings(prev => ({
                          ...prev,
                          theme_mode: e.target.value
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                      >
                        <option value="system" className="bg-gray-800">System Default</option>
                        <option value="light" className="bg-gray-800">Light Mode</option>
                        <option value="dark" className="bg-gray-800">Dark Mode</option>
                      </select>
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Animation Speed
                      </label>
                      <select
                        value={appearanceSettings.animation_speed}
                        onChange={(e) => setAppearanceSettings(prev => ({
                          ...prev,
                          animation_speed: e.target.value
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                      >
                        <option value="slow" className="bg-gray-800">Slow</option>
                        <option value="normal" className="bg-gray-800">Normal</option>
                        <option value="fast" className="bg-gray-800">Fast</option>
                        <option value="none" className="bg-gray-800">No Animations</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-6">
                    <h3 className="text-xl font-semibold text-white">User Experience Settings</h3>
                    <div className="flex items-center justify-between p-6 backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl">
                      <div>
                        <h4 className="font-medium text-white">Enable Haptic Feedback</h4>
                        <p className="text-sm text-white/60">Provide tactile feedback for user interactions</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={appearanceSettings.enable_haptic_feedback}
                          onChange={(e) => setAppearanceSettings(prev => ({
                            ...prev,
                            enable_haptic_feedback: e.target.checked
                          }))}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-white/20 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600/80 backdrop-blur-md"></div>
                      </label>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={() => saveAppConfiguration('appearance', appearanceSettings)}
                      disabled={saving}
                      className="flex items-center px-8 py-4 backdrop-blur-md bg-gradient-to-r from-blue-500/20 to-purple-600/20 border border-white/30 text-white rounded-2xl hover:from-blue-500/30 hover:to-purple-600/30 transition-all duration-300 disabled:opacity-50"
                    >
                      {saving ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
                      Save Appearance Settings
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Performance Tab */}
            {activeTab === 'performance' && (
              <div className="backdrop-blur-md bg-white/10 border border-white/20 rounded-2xl p-8">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-3xl font-bold text-white">Performance Settings</h2>
                </div>

                <div className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Cache Duration (seconds)
                      </label>
                      <input
                        type="number"
                        value={performanceSettings.cache_duration}
                        onChange={(e) => setPerformanceSettings(prev => ({
                          ...prev,
                          cache_duration: parseInt(e.target.value) || 300
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                        min="60"
                        max="3600"
                      />
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Image Quality
                      </label>
                      <select
                        value={performanceSettings.image_quality}
                        onChange={(e) => setPerformanceSettings(prev => ({
                          ...prev,
                          image_quality: e.target.value
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                      >
                        <option value="low" className="bg-gray-800">Low (Faster Loading)</option>
                        <option value="medium" className="bg-gray-800">Medium</option>
                        <option value="high" className="bg-gray-800">High (Better Quality)</option>
                      </select>
                    </div>

                    <div className="backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl p-6">
                      <label className="block text-sm font-medium text-white mb-4">
                        Max Offline Storage (MB)
                      </label>
                      <input
                        type="number"
                        value={performanceSettings.max_offline_storage}
                        onChange={(e) => setPerformanceSettings(prev => ({
                          ...prev,
                          max_offline_storage: parseInt(e.target.value) || 50
                        }))}
                        className="w-full px-4 py-3 backdrop-blur-md bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/40 focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
                        min="10"
                        max="500"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    {[
                      { key: 'enable_analytics', label: 'Enable Analytics', desc: 'Collect usage analytics for app improvement' },
                      { key: 'crash_reporting', label: 'Crash Reporting', desc: 'Automatically report app crashes for debugging' },
                      { key: 'performance_monitoring', label: 'Performance Monitoring', desc: 'Monitor app performance and identify bottlenecks' },
                    ].map((setting) => (
                      <div key={setting.key} className="flex items-center justify-between p-6 backdrop-blur-md bg-white/5 border border-white/10 rounded-2xl">
                        <div>
                          <h4 className="font-medium text-white">{setting.label}</h4>
                          <p className="text-sm text-white/60">{setting.desc}</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={performanceSettings[setting.key as keyof typeof performanceSettings] as boolean}
                            onChange={(e) => setPerformanceSettings(prev => ({
                              ...prev,
                              [setting.key]: e.target.checked
                            }))}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-white/20 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600/80 backdrop-blur-md"></div>
                        </label>
                      </div>
                    ))}
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={() => saveAppConfiguration('performance', performanceSettings)}
                      disabled={saving}
                      className="flex items-center px-8 py-4 backdrop-blur-md bg-gradient-to-r from-blue-500/20 to-purple-600/20 border border-white/30 text-white rounded-2xl hover:from-blue-500/30 hover:to-purple-600/30 transition-all duration-300 disabled:opacity-50"
                    >
                      {saving ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
                      Save Performance Settings
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Additional tabs would be implemented here with the same glassmorphism styling... */}
          </div>
        </div>
      </div>
      </div>
    </TestLayout>
  )
}