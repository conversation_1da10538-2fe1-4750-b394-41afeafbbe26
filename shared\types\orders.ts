export type OrderStatus = 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled';
export type OrderType = 'dine-in' | 'takeaway' | 'delivery';
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
export type PaymentMethod = 'cash' | 'card' | 'digital';

export interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
  options?: Record<string, any>[];
}

export interface Order {
  id: string;
  orderNumber: string;
  status: OrderStatus;
  items: OrderItem[];
  totalAmount: number;
  customerName?: string;
  customerPhone?: string;
  orderType: OrderType;
  tableNumber?: string;
  address?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  estimatedTime?: number; // in minutes
  paymentStatus?: PaymentStatus;
  paymentMethod?: PaymentMethod;
  paymentTransactionId?: string;
} 