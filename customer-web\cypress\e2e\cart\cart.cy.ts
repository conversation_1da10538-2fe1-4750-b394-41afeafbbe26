/// <reference types="cypress" />

describe('Customer Cart Management', () => {
  beforeEach(() => {
    // Clear any existing data
    cy.clearAllCookies();
    cy.clearAllLocalStorage();
    cy.clearAllSessionStorage();
    
    // Set up API interceptors
    cy.intercept('GET', '/api/cart', { fixture: 'orders/orders.json' }).as('getCart');
    cy.intercept('POST', '/api/cart/add', { statusCode: 200 }).as('addToCart');
    cy.intercept('PUT', '/api/cart/update', { statusCode: 200 }).as('updateCart');
    cy.intercept('DELETE', '/api/cart/remove', { statusCode: 200 }).as('removeFromCart');
    cy.intercept('DELETE', '/api/cart/clear', { statusCode: 200 }).as('clearCart');
    cy.intercept('GET', '/api/menu', { fixture: 'menu/menu-items.json' }).as('getMenu');
    
    // Login as customer
    cy.loginAsCustomer();
  });

  describe('Cart Page Display', () => {
    it('should display empty cart message when cart is empty', () => {
      cy.intercept('GET', '/api/cart', {
        body: { items: [], total: 0, itemCount: 0 }
      }).as('getEmptyCart');
      
      cy.visitCartPage();
      cy.wait('@getEmptyCart');
      
      cy.get('[data-testid="empty-cart-message"]')
        .should('be.visible')
        .and('contain', 'Your cart is empty');
      
      cy.get('[data-testid="continue-shopping-button"]')
        .should('be.visible')
        .and('contain', 'Continue Shopping');
      
      cy.get('[data-testid="cart-items-list"]').should('not.exist');
      cy.get('[data-testid="checkout-button"]').should('not.exist');
    });

    it('should display cart with items', () => {
      cy.visitCartPage();
      cy.wait('@getCart');
      
      // Check page title and heading
      cy.title().should('contain', 'Cart');
      cy.get('h1').should('contain', 'Your Cart');
      
      // Check main sections
      cy.get('[data-testid="cart-items-list"]').should('be.visible');
      cy.get('[data-testid="cart-summary"]').should('be.visible');
      cy.get('[data-testid="checkout-button"]').should('be.visible');
      
      // Check cart items
      cy.get('[data-testid="cart-item"]').should('have.length.greaterThan', 0);
    });

    it('should display cart item details correctly', () => {
      cy.visitCartPage();
      cy.wait('@getCart');
      
      cy.fixture('orders/orders').then((orderData) => {
        const firstItem = orderData.cart.items[0];
        
        cy.get('[data-testid="cart-item"]').first().within(() => {
          cy.get('[data-testid="item-image"]').should('be.visible');
          cy.get('[data-testid="item-name"]').should('contain', firstItem.name);
          cy.get('[data-testid="item-price"]').should('contain', `$${firstItem.price}`);
          cy.get('[data-testid="item-quantity"]').should('contain', firstItem.quantity);
          cy.get('[data-testid="item-total"]').should('contain', `$${firstItem.total}`);
          
          // Check quantity controls
          cy.get('[data-testid="quantity-decrease"]').should('be.visible');
          cy.get('[data-testid="quantity-increase"]').should('be.visible');
          cy.get('[data-testid="remove-item"]').should('be.visible');
        });
      });
    });

    it('should display customizations for items', () => {
      cy.visitCartPage();
      cy.wait('@getCart');
      
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="item-customizations"]').should('be.visible');
        cy.get('[data-testid="customization-item"]').should('have.length.greaterThan', 0);
        
        cy.get('[data-testid="customization-item"]').first().within(() => {
          cy.get('[data-testid="customization-name"]').should('be.visible');
          cy.get('[data-testid="customization-price"]').should('be.visible');
        });
      });
    });
  });

  describe('Quantity Management', () => {
    beforeEach(() => {
      cy.visitCartPage();
      cy.wait('@getCart');
    });

    it('should increase item quantity', () => {
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="item-quantity"]').invoke('text').then((initialQuantity) => {
          cy.get('[data-testid="quantity-increase"]').click();
          
          cy.wait('@updateCart');
          
          const expectedQuantity = parseInt(initialQuantity) + 1;
          cy.get('[data-testid="item-quantity"]').should('contain', expectedQuantity);
        });
      });
      
      cy.checkNotification('Cart updated', 'success');
    });

    it('should decrease item quantity', () => {
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="item-quantity"]').invoke('text').then((initialQuantity) => {
          if (parseInt(initialQuantity) > 1) {
            cy.get('[data-testid="quantity-decrease"]').click();
            
            cy.wait('@updateCart');
            
            const expectedQuantity = parseInt(initialQuantity) - 1;
            cy.get('[data-testid="item-quantity"]').should('contain', expectedQuantity);
          }
        });
      });
    });

    it('should disable decrease button when quantity is 1', () => {
      // First, set quantity to 1
      cy.updateCartItemQuantity(1, 1);
      
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="item-quantity"]').should('contain', '1');
        cy.get('[data-testid="quantity-decrease"]').should('be.disabled');
      });
    });

    it('should update item total when quantity changes', () => {
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="item-price"]').invoke('text').then((priceText) => {
          const price = parseFloat(priceText.replace('$', ''));
          
          cy.get('[data-testid="quantity-increase"]').click();
          cy.wait('@updateCart');
          
          cy.get('[data-testid="item-quantity"]').invoke('text').then((quantityText) => {
            const quantity = parseInt(quantityText);
            const expectedTotal = (price * quantity).toFixed(2);
            
            cy.get('[data-testid="item-total"]').should('contain', `$${expectedTotal}`);
          });
        });
      });
    });

    it('should handle quantity input directly', () => {
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="quantity-input"]').clear().type('5');
        cy.get('[data-testid="quantity-input"]').blur();
        
        cy.wait('@updateCart');
        
        cy.get('[data-testid="item-quantity"]').should('contain', '5');
      });
    });

    it('should validate quantity input', () => {
      cy.get('[data-testid="cart-item"]').first().within(() => {
        // Test negative number
        cy.get('[data-testid="quantity-input"]').clear().type('-1');
        cy.get('[data-testid="quantity-input"]').blur();
        
        cy.get('[data-testid="quantity-error"]')
          .should('be.visible')
          .and('contain', 'Quantity must be at least 1');
        
        // Test zero
        cy.get('[data-testid="quantity-input"]').clear().type('0');
        cy.get('[data-testid="quantity-input"]').blur();
        
        cy.get('[data-testid="quantity-error"]')
          .should('be.visible')
          .and('contain', 'Quantity must be at least 1');
        
        // Test large number
        cy.get('[data-testid="quantity-input"]').clear().type('100');
        cy.get('[data-testid="quantity-input"]').blur();
        
        cy.get('[data-testid="quantity-error"]')
          .should('be.visible')
          .and('contain', 'Maximum quantity is 50');
      });
    });
  });

  describe('Item Removal', () => {
    beforeEach(() => {
      cy.visitCartPage();
      cy.wait('@getCart');
    });

    it('should remove item from cart', () => {
      cy.get('[data-testid="cart-item"]').should('have.length.greaterThan', 0);
      
      const initialItemCount = Cypress.$('[data-testid="cart-item"]').length;
      
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="remove-item"]').click();
      });
      
      cy.wait('@removeFromCart');
      
      cy.get('[data-testid="cart-item"]').should('have.length', initialItemCount - 1);
      cy.checkNotification('Item removed from cart', 'success');
    });

    it('should show confirmation dialog before removing item', () => {
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="remove-item"]').click();
      });
      
      cy.get('[data-testid="remove-confirmation-modal"]').should('be.visible');
      cy.get('[data-testid="confirm-remove"]').should('be.visible');
      cy.get('[data-testid="cancel-remove"]').should('be.visible');
      
      cy.get('[data-testid="confirm-remove"]').click();
      
      cy.wait('@removeFromCart');
      cy.get('[data-testid="remove-confirmation-modal"]').should('not.exist');
    });

    it('should cancel item removal', () => {
      const initialItemCount = Cypress.$('[data-testid="cart-item"]').length;
      
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="remove-item"]').click();
      });
      
      cy.get('[data-testid="remove-confirmation-modal"]').should('be.visible');
      cy.get('[data-testid="cancel-remove"]').click();
      
      cy.get('[data-testid="remove-confirmation-modal"]').should('not.exist');
      cy.get('[data-testid="cart-item"]').should('have.length', initialItemCount);
    });

    it('should handle remove item errors', () => {
      cy.intercept('DELETE', '/api/cart/remove', {
        statusCode: 400,
        body: { error: 'Unable to remove item' }
      }).as('removeError');
      
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="remove-item"]').click();
      });
      
      cy.get('[data-testid="confirm-remove"]').click();
      cy.wait('@removeError');
      
      cy.checkNotification('Unable to remove item', 'error');
    });
  });

  describe('Cart Summary', () => {
    beforeEach(() => {
      cy.visitCartPage();
      cy.wait('@getCart');
    });

    it('should display cart totals correctly', () => {
      cy.get('[data-testid="cart-summary"]').within(() => {
        cy.get('[data-testid="subtotal"]').should('be.visible');
        cy.get('[data-testid="tax-amount"]').should('be.visible');
        cy.get('[data-testid="delivery-fee"]').should('be.visible');
        cy.get('[data-testid="total-amount"]').should('be.visible');
      });
    });

    it('should calculate totals correctly', () => {
      cy.fixture('orders/orders').then((orderData) => {
        const cart = orderData.cart;
        
        cy.get('[data-testid="subtotal"]').should('contain', `$${cart.subtotal}`);
        cy.get('[data-testid="tax-amount"]').should('contain', `$${cart.tax}`);
        cy.get('[data-testid="delivery-fee"]').should('contain', `$${cart.deliveryFee}`);
        cy.get('[data-testid="total-amount"]').should('contain', `$${cart.total}`);
      });
    });

    it('should update totals when items change', () => {
      cy.get('[data-testid="total-amount"]').invoke('text').then((initialTotal) => {
        cy.get('[data-testid="cart-item"]').first().within(() => {
          cy.get('[data-testid="quantity-increase"]').click();
        });
        
        cy.wait('@updateCart');
        
        cy.get('[data-testid="total-amount"]').should('not.contain', initialTotal);
      });
    });

    it('should show item count', () => {
      cy.get('[data-testid="item-count"]').should('be.visible');
      
      cy.fixture('orders/orders').then((orderData) => {
        const totalItems = orderData.cart.items.reduce((sum, item) => sum + item.quantity, 0);
        cy.get('[data-testid="item-count"]').should('contain', `${totalItems} items`);
      });
    });
  });

  describe('Promo Codes', () => {
    beforeEach(() => {
      cy.visitCartPage();
      cy.wait('@getCart');
    });

    it('should apply valid promo code', () => {
      cy.intercept('POST', '/api/cart/promo', {
        statusCode: 200,
        body: { discount: 10.00, code: 'SAVE10' }
      }).as('applyPromo');
      
      cy.get('[data-testid="promo-code-input"]').type('SAVE10');
      cy.get('[data-testid="apply-promo-button"]').click();
      
      cy.wait('@applyPromo');
      
      cy.get('[data-testid="promo-discount"]')
        .should('be.visible')
        .and('contain', '-$10.00');
      
      cy.get('[data-testid="applied-promo-code"]')
        .should('be.visible')
        .and('contain', 'SAVE10');
      
      cy.checkNotification('Promo code applied', 'success');
    });

    it('should handle invalid promo code', () => {
      cy.intercept('POST', '/api/cart/promo', {
        statusCode: 400,
        body: { error: 'Invalid promo code' }
      }).as('invalidPromo');
      
      cy.get('[data-testid="promo-code-input"]').type('INVALID');
      cy.get('[data-testid="apply-promo-button"]').click();
      
      cy.wait('@invalidPromo');
      
      cy.checkNotification('Invalid promo code', 'error');
      cy.get('[data-testid="promo-discount"]').should('not.exist');
    });

    it('should remove applied promo code', () => {
      // First apply a promo code
      cy.intercept('POST', '/api/cart/promo', {
        statusCode: 200,
        body: { discount: 10.00, code: 'SAVE10' }
      }).as('applyPromo');
      
      cy.get('[data-testid="promo-code-input"]').type('SAVE10');
      cy.get('[data-testid="apply-promo-button"]').click();
      cy.wait('@applyPromo');
      
      // Then remove it
      cy.intercept('DELETE', '/api/cart/promo', { statusCode: 200 }).as('removePromo');
      
      cy.get('[data-testid="remove-promo"]').click();
      
      cy.wait('@removePromo');
      
      cy.get('[data-testid="promo-discount"]').should('not.exist');
      cy.get('[data-testid="applied-promo-code"]').should('not.exist');
      cy.get('[data-testid="promo-code-input"]').should('have.value', '');
    });

    it('should validate promo code input', () => {
      cy.get('[data-testid="apply-promo-button"]').should('be.disabled');
      
      cy.get('[data-testid="promo-code-input"]').type('SA');
      cy.get('[data-testid="promo-error"]')
        .should('be.visible')
        .and('contain', 'Promo code must be at least 3 characters');
      
      cy.get('[data-testid="promo-code-input"]').type('VE10');
      cy.get('[data-testid="promo-error"]').should('not.exist');
      cy.get('[data-testid="apply-promo-button"]').should('not.be.disabled');
    });
  });

  describe('Delivery Options', () => {
    beforeEach(() => {
      cy.visitCartPage();
      cy.wait('@getCart');
    });

    it('should display delivery options', () => {
      cy.get('[data-testid="delivery-options"]').should('be.visible');
      
      cy.get('[data-testid="delivery-option-delivery"]').should('be.visible');
      cy.get('[data-testid="delivery-option-pickup"]').should('be.visible');
    });

    it('should select delivery option', () => {
      cy.get('[data-testid="delivery-option-delivery"]').click();
      
      cy.get('[data-testid="delivery-option-delivery"]')
        .should('have.class', 'selected')
        .and('have.attr', 'aria-checked', 'true');
      
      cy.get('[data-testid="delivery-fee"]').should('be.visible');
    });

    it('should select pickup option', () => {
      cy.get('[data-testid="delivery-option-pickup"]').click();
      
      cy.get('[data-testid="delivery-option-pickup"]')
        .should('have.class', 'selected')
        .and('have.attr', 'aria-checked', 'true');
      
      cy.get('[data-testid="delivery-fee"]').should('contain', '$0.00');
    });

    it('should update total when delivery option changes', () => {
      cy.get('[data-testid="total-amount"]').invoke('text').then((initialTotal) => {
        cy.get('[data-testid="delivery-option-pickup"]').click();
        
        cy.get('[data-testid="total-amount"]').should('not.contain', initialTotal);
      });
    });

    it('should show estimated delivery time', () => {
      cy.get('[data-testid="delivery-option-delivery"]').click();
      
      cy.get('[data-testid="estimated-delivery-time"]')
        .should('be.visible')
        .and('contain', 'minutes');
    });

    it('should show pickup location for pickup option', () => {
      cy.get('[data-testid="delivery-option-pickup"]').click();
      
      cy.get('[data-testid="pickup-location"]')
        .should('be.visible')
        .and('contain', 'Restaurant Address');
    });
  });

  describe('Clear Cart', () => {
    beforeEach(() => {
      cy.visitCartPage();
      cy.wait('@getCart');
    });

    it('should clear entire cart', () => {
      cy.get('[data-testid="clear-cart-button"]').click();
      
      cy.get('[data-testid="clear-cart-confirmation"]').should('be.visible');
      cy.get('[data-testid="confirm-clear-cart"]').click();
      
      cy.wait('@clearCart');
      
      cy.get('[data-testid="empty-cart-message"]').should('be.visible');
      cy.checkNotification('Cart cleared', 'success');
    });

    it('should cancel clear cart', () => {
      const initialItemCount = Cypress.$('[data-testid="cart-item"]').length;
      
      cy.get('[data-testid="clear-cart-button"]').click();
      cy.get('[data-testid="clear-cart-confirmation"]').should('be.visible');
      cy.get('[data-testid="cancel-clear-cart"]').click();
      
      cy.get('[data-testid="clear-cart-confirmation"]').should('not.exist');
      cy.get('[data-testid="cart-item"]').should('have.length', initialItemCount);
    });
  });

  describe('Continue Shopping', () => {
    it('should navigate back to menu from empty cart', () => {
      cy.intercept('GET', '/api/cart', {
        body: { items: [], total: 0, itemCount: 0 }
      }).as('getEmptyCart');
      
      cy.visitCartPage();
      cy.wait('@getEmptyCart');
      
      cy.get('[data-testid="continue-shopping-button"]').click();
      
      cy.url().should('include', '/menu');
    });

    it('should navigate back to menu from cart with items', () => {
      cy.visitCartPage();
      cy.wait('@getCart');
      
      cy.get('[data-testid="continue-shopping-link"]').click();
      
      cy.url().should('include', '/menu');
    });
  });

  describe('Checkout Navigation', () => {
    beforeEach(() => {
      cy.visitCartPage();
      cy.wait('@getCart');
    });

    it('should navigate to checkout', () => {
      cy.get('[data-testid="checkout-button"]').click();
      
      cy.url().should('include', '/checkout');
    });

    it('should disable checkout button for empty cart', () => {
      cy.intercept('GET', '/api/cart', {
        body: { items: [], total: 0, itemCount: 0 }
      }).as('getEmptyCart');
      
      cy.visit('/cart');
      cy.wait('@getEmptyCart');
      
      cy.get('[data-testid="checkout-button"]').should('not.exist');
    });

    it('should show minimum order warning', () => {
      cy.fixture('orders/orders').then((orderData) => {
        // Mock cart with total below minimum
        const lowTotalCart = {
          ...orderData.cart,
          total: 5.00,
          subtotal: 5.00
        };
        
        cy.intercept('GET', '/api/cart', { body: lowTotalCart }).as('getLowTotalCart');
        
        cy.visit('/cart');
        cy.wait('@getLowTotalCart');
        
        cy.get('[data-testid="minimum-order-warning"]')
          .should('be.visible')
          .and('contain', 'Minimum order amount is $15.00');
        
        cy.get('[data-testid="checkout-button"]').should('be.disabled');
      });
    });
  });

  describe('Item Customization', () => {
    beforeEach(() => {
      cy.visitCartPage();
      cy.wait('@getCart');
    });

    it('should edit item customizations', () => {
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="edit-customizations"]').click();
      });
      
      cy.get('[data-testid="customization-modal"]').should('be.visible');
      
      // Make changes to customizations
      cy.get('[data-testid="customization-option"]').first().click();
      cy.get('[data-testid="save-customizations"]').click();
      
      cy.wait('@updateCart');
      
      cy.get('[data-testid="customization-modal"]').should('not.exist');
      cy.checkNotification('Item updated', 'success');
    });

    it('should cancel customization changes', () => {
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="edit-customizations"]').click();
      });
      
      cy.get('[data-testid="customization-modal"]').should('be.visible');
      
      cy.get('[data-testid="customization-option"]').first().click();
      cy.get('[data-testid="cancel-customizations"]').click();
      
      cy.get('[data-testid="customization-modal"]').should('not.exist');
      // Verify no API call was made
      cy.get('@updateCart.all').should('have.length', 0);
    });

    it('should update price when customizations change', () => {
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="item-total"]').invoke('text').then((initialTotal) => {
          cy.get('[data-testid="edit-customizations"]').click();
          
          cy.get('[data-testid="customization-modal"]').within(() => {
            // Add a paid customization
            cy.get('[data-testid="customization-option"][data-price="2.00"]').click();
            cy.get('[data-testid="save-customizations"]').click();
          });
          
          cy.wait('@updateCart');
          
          cy.get('[data-testid="item-total"]').should('not.contain', initialTotal);
        });
      });
    });
  });

  describe('Loading States', () => {
    it('should show loading state while fetching cart', () => {
      cy.intercept('GET', '/api/cart', (req) => {
        req.reply((res) => {
          res.delay(2000);
          res.send({ fixture: 'orders/orders.json' });
        });
      }).as('slowCart');
      
      cy.visitCartPage();
      
      cy.get('[data-testid="cart-loading"]').should('be.visible');
      cy.get('[data-testid="loading-skeleton"]').should('be.visible');
      
      cy.wait('@slowCart');
      
      cy.get('[data-testid="cart-loading"]').should('not.exist');
      cy.get('[data-testid="cart-items-list"]').should('be.visible');
    });

    it('should show loading state when updating quantities', () => {
      cy.visitCartPage();
      cy.wait('@getCart');
      
      cy.intercept('PUT', '/api/cart/update', (req) => {
        req.reply((res) => {
          res.delay(1000);
          res.send({ statusCode: 200 });
        });
      }).as('slowUpdate');
      
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="quantity-increase"]').click();
        
        cy.get('[data-testid="quantity-loading"]').should('be.visible');
        cy.get('[data-testid="quantity-controls"]').should('be.disabled');
      });
      
      cy.wait('@slowUpdate');
      
      cy.get('[data-testid="quantity-loading"]').should('not.exist');
    });
  });

  describe('Error Handling', () => {
    it('should handle cart loading error', () => {
      cy.intercept('GET', '/api/cart', {
        statusCode: 500,
        body: { error: 'Internal server error' }
      }).as('cartError');
      
      cy.visitCartPage();
      cy.wait('@cartError');
      
      cy.get('[data-testid="error-message"]')
        .should('be.visible')
        .and('contain', 'Unable to load cart');
      
      cy.get('[data-testid="retry-button"]').should('be.visible');
    });

    it('should retry loading cart on error', () => {
      cy.intercept('GET', '/api/cart', {
        statusCode: 500,
        body: { error: 'Internal server error' }
      }).as('cartError');
      
      cy.visitCartPage();
      cy.wait('@cartError');
      
      // Set up successful retry
      cy.intercept('GET', '/api/cart', { fixture: 'orders/orders.json' }).as('cartRetry');
      
      cy.get('[data-testid="retry-button"]').click();
      
      cy.wait('@cartRetry');
      
      cy.get('[data-testid="cart-items-list"]').should('be.visible');
      cy.get('[data-testid="error-message"]').should('not.exist');
    });

    it('should handle update quantity errors', () => {
      cy.visitCartPage();
      cy.wait('@getCart');
      
      cy.intercept('PUT', '/api/cart/update', {
        statusCode: 400,
        body: { error: 'Item out of stock' }
      }).as('updateError');
      
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="quantity-increase"]').click();
      });
      
      cy.wait('@updateError');
      
      cy.checkNotification('Item out of stock', 'error');
    });

    it('should handle network errors gracefully', () => {
      cy.intercept('GET', '/api/cart', { forceNetworkError: true }).as('networkError');
      
      cy.visitCartPage();
      cy.wait('@networkError');
      
      cy.get('[data-testid="error-message"]')
        .should('be.visible')
        .and('contain', 'Network error');
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      cy.visitCartPage();
      cy.wait('@getCart');
    });

    it('should be accessible', () => {
      cy.checkAccessibility();
    });

    it('should support keyboard navigation', () => {
      cy.checkKeyboardNavigation();
      
      // Test tab order through cart items
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="quantity-decrease"]').focus();
        cy.focused().should('have.attr', 'data-testid', 'quantity-decrease');
        
        cy.focused().tab();
        cy.focused().should('have.attr', 'data-testid', 'quantity-input');
        
        cy.focused().tab();
        cy.focused().should('have.attr', 'data-testid', 'quantity-increase');
      });
    });

    it('should have proper ARIA labels', () => {
      cy.get('[data-testid="cart-items-list"]')
        .should('have.attr', 'role', 'list');
      
      cy.get('[data-testid="cart-item"]').first()
        .should('have.attr', 'role', 'listitem');
      
      cy.get('[data-testid="quantity-input"]').first()
        .should('have.attr', 'aria-label')
        .and('contain', 'Quantity');
    });

    it('should announce cart updates to screen readers', () => {
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="quantity-increase"]').click();
      });
      
      cy.wait('@updateCart');
      
      cy.get('[data-testid="cart-announcement"]')
        .should('have.attr', 'aria-live', 'polite')
        .and('contain', 'Cart updated');
    });
  });

  describe('Responsive Design', () => {
    it('should work on different screen sizes', () => {
      cy.testResponsiveDesign();
      
      // Test mobile layout
      cy.viewport(375, 667);
      cy.visitCartPage();
      cy.wait('@getCart');
      
      cy.get('[data-testid="cart-items-list"]')
        .should('have.css', 'flex-direction', 'column');
      
      cy.get('[data-testid="cart-summary"]')
        .should('be.visible');
      
      // Test tablet layout
      cy.viewport(768, 1024);
      cy.get('[data-testid="cart-layout"]')
        .should('have.css', 'grid-template-columns')
        .and('match', /1fr/);
      
      // Test desktop layout
      cy.viewport(1280, 720);
      cy.get('[data-testid="cart-layout"]')
        .should('have.css', 'grid-template-columns')
        .and('match', /2fr 1fr/);
    });

    it('should show mobile-optimized cart summary', () => {
      cy.viewport(375, 667);
      cy.visitCartPage();
      cy.wait('@getCart');
      
      cy.get('[data-testid="mobile-cart-summary"]').should('be.visible');
      cy.get('[data-testid="mobile-checkout-button"]').should('be.visible');
    });
  });

  describe('Performance', () => {
    it('should load cart page quickly', () => {
      cy.visit('/cart');
      cy.measurePageLoadTime();
    });

    it('should optimize images', () => {
      cy.visitCartPage();
      cy.wait('@getCart');
      cy.checkImageOptimization();
    });

    it('should debounce quantity updates', () => {
      cy.visitCartPage();
      cy.wait('@getCart');
      
      cy.get('[data-testid="cart-item"]').first().within(() => {
        // Rapidly click increase button
        cy.get('[data-testid="quantity-increase"]')
          .click()
          .click()
          .click();
      });
      
      // Should only make one API call after debounce
      cy.wait('@updateCart');
      cy.get('@updateCart.all').should('have.length', 1);
    });
  });

  describe('Save for Later', () => {
    beforeEach(() => {
      cy.visitCartPage();
      cy.wait('@getCart');
    });

    it('should save item for later', () => {
      cy.intercept('POST', '/api/cart/save-for-later', { statusCode: 200 }).as('saveForLater');
      
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="save-for-later"]').click();
      });
      
      cy.wait('@saveForLater');
      
      cy.checkNotification('Item saved for later', 'success');
      
      // Item should be moved to saved items section
      cy.get('[data-testid="saved-items"]').should('be.visible');
      cy.get('[data-testid="saved-item"]').should('have.length.greaterThan', 0);
    });

    it('should move saved item back to cart', () => {
      cy.intercept('POST', '/api/cart/move-to-cart', { statusCode: 200 }).as('moveToCart');
      
      // First save an item
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="save-for-later"]').click();
      });
      
      // Then move it back
      cy.get('[data-testid="saved-item"]').first().within(() => {
        cy.get('[data-testid="move-to-cart"]').click();
      });
      
      cy.wait('@moveToCart');
      
      cy.checkNotification('Item moved to cart', 'success');
    });

    it('should remove saved item permanently', () => {
      cy.intercept('DELETE', '/api/cart/saved-item', { statusCode: 200 }).as('removeSaved');
      
      // First save an item
      cy.get('[data-testid="cart-item"]').first().within(() => {
        cy.get('[data-testid="save-for-later"]').click();
      });
      
      // Then remove it permanently
      cy.get('[data-testid="saved-item"]').first().within(() => {
        cy.get('[data-testid="remove-saved-item"]').click();
      });
      
      cy.get('[data-testid="confirm-remove-saved"]').click();
      
      cy.wait('@removeSaved');
      
      cy.checkNotification('Item removed', 'success');
    });
  });
});