 /// <reference types="cypress" />

describe('Customer Authentication - Registration', () => {
  beforeEach(() => {
    // Clear any existing sessions
    cy.clearAllCookies();
    cy.clearAllLocalStorage();
    cy.clearAllSessionStorage();
    
    // Set up API interceptors
    cy.intercept('POST', '/api/auth/register', { fixture: 'auth/customer-user.json' }).as('register');
    cy.intercept('POST', '/api/auth/verify-email', { fixture: 'auth/customer-user.json' }).as('verifyEmail');
    cy.intercept('GET', '/api/auth/session', { fixture: 'auth/customer-user.json' }).as('session');
  });

  describe('Registration Page Display', () => {
    it('should display registration form with all required elements', () => {
      cy.visit('/register');
      cy.waitForPageLoad();
      
      // Check page title and heading
      cy.title().should('contain', 'Register');
      cy.get('h1').should('contain', 'Create Account');
      
      // Check form elements
      cy.get('[data-testid="first-name-input"]').should('be.visible');
      cy.get('[data-testid="last-name-input"]').should('be.visible');
      cy.get('[data-testid="email-input"]').should('be.visible');
      cy.get('[data-testid="phone-input"]').should('be.visible');
      cy.get('[data-testid="password-input"]').should('be.visible');
      cy.get('[data-testid="confirm-password-input"]').should('be.visible');
      cy.get('[data-testid="register-button"]').should('be.visible').and('contain', 'Create Account');
      
      // Check additional elements
      cy.get('[data-testid="terms-checkbox"]').should('be.visible');
      cy.get('[data-testid="newsletter-checkbox"]').should('be.visible');
      cy.get('[data-testid="login-link"]').should('be.visible');
      cy.get('[data-testid="password-toggle"]').should('be.visible');
      cy.get('[data-testid="confirm-password-toggle"]').should('be.visible');
      
      // Check social registration options
      cy.get('[data-testid="google-register"]').should('be.visible');
      cy.get('[data-testid="facebook-register"]').should('be.visible');
    });

    it('should have proper form labels and placeholders', () => {
      cy.visit('/register');
      
      cy.get('[data-testid="first-name-input"]')
        .should('have.attr', 'placeholder', 'Enter your first name')
        .and('have.attr', 'type', 'text');
      
      cy.get('[data-testid="last-name-input"]')
        .should('have.attr', 'placeholder', 'Enter your last name')
        .and('have.attr', 'type', 'text');
      
      cy.get('[data-testid="email-input"]')
        .should('have.attr', 'placeholder', 'Enter your email')
        .and('have.attr', 'type', 'email');
      
      cy.get('[data-testid="phone-input"]')
        .should('have.attr', 'placeholder', '+****************')
        .and('have.attr', 'type', 'tel');
      
      cy.get('[data-testid="password-input"]')
        .should('have.attr', 'placeholder', 'Create a password')
        .and('have.attr', 'type', 'password');
      
      cy.get('[data-testid="confirm-password-input"]')
        .should('have.attr', 'placeholder', 'Confirm your password')
        .and('have.attr', 'type', 'password');
      
      // Check for proper labels
      cy.get('label[for="firstName"]').should('contain', 'First Name');
      cy.get('label[for="lastName"]').should('contain', 'Last Name');
      cy.get('label[for="email"]').should('contain', 'Email');
      cy.get('label[for="phone"]').should('contain', 'Phone Number');
      cy.get('label[for="password"]').should('contain', 'Password');
      cy.get('label[for="confirmPassword"]').should('contain', 'Confirm Password');
    });
  });

  describe('Form Validation', () => {
    beforeEach(() => {
      cy.visit('/register');
    });

    it('should show validation errors for empty required fields', () => {
      cy.get('[data-testid="register-button"]').click();
      
      cy.get('[data-testid="first-name-error"]')
        .should('be.visible')
        .and('contain', 'First name is required');
      
      cy.get('[data-testid="last-name-error"]')
        .should('be.visible')
        .and('contain', 'Last name is required');
      
      cy.get('[data-testid="email-error"]')
        .should('be.visible')
        .and('contain', 'Email is required');
      
      cy.get('[data-testid="password-error"]')
        .should('be.visible')
        .and('contain', 'Password is required');
      
      cy.get('[data-testid="terms-error"]')
        .should('be.visible')
        .and('contain', 'You must accept the terms and conditions');
    });

    it('should validate email format', () => {
      cy.get('[data-testid="email-input"]').type('invalid-email');
      cy.get('[data-testid="register-button"]').click();
      
      cy.get('[data-testid="email-error"]')
        .should('be.visible')
        .and('contain', 'Please enter a valid email address');
    });

    it('should validate phone number format', () => {
      cy.get('[data-testid="phone-input"]').type('invalid-phone');
      cy.get('[data-testid="register-button"]').click();
      
      cy.get('[data-testid="phone-error"]')
        .should('be.visible')
        .and('contain', 'Please enter a valid phone number');
    });

    it('should validate password strength', () => {
      cy.get('[data-testid="password-input"]').type('weak');
      cy.get('[data-testid="register-button"]').click();
      
      cy.get('[data-testid="password-error"]')
        .should('be.visible')
        .and('contain', 'Password must be at least 8 characters');
    });

    it('should validate password confirmation match', () => {
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="confirm-password-input"]').type('different123');
      cy.get('[data-testid="register-button"]').click();
      
      cy.get('[data-testid="confirm-password-error"]')
        .should('be.visible')
        .and('contain', 'Passwords do not match');
    });

    it('should show password strength indicator', () => {
      cy.get('[data-testid="password-input"]').type('weak');
      cy.get('[data-testid="password-strength"]')
        .should('be.visible')
        .and('contain', 'Weak');
      
      cy.get('[data-testid="password-input"]').clear().type('StrongPass123!');
      cy.get('[data-testid="password-strength"]')
        .should('be.visible')
        .and('contain', 'Strong');
    });

    it('should clear validation errors when user starts typing', () => {
      // Trigger validation errors
      cy.get('[data-testid="register-button"]').click();
      cy.get('[data-testid="email-error"]').should('be.visible');
      
      // Start typing and check errors clear
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="email-error"]').should('not.exist');
      
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="password-error"]').should('not.exist');
    });

    it('should disable submit button when form is invalid', () => {
      cy.get('[data-testid="register-button"]').should('not.be.disabled');
      
      cy.get('[data-testid="email-input"]').type('invalid-email');
      cy.get('[data-testid="register-button"]').should('be.disabled');
      
      cy.get('[data-testid="email-input"]').clear().type('<EMAIL>');
      cy.get('[data-testid="first-name-input"]').type('John');
      cy.get('[data-testid="last-name-input"]').type('Doe');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="confirm-password-input"]').type('password123');
      cy.get('[data-testid="terms-checkbox"]').check();
      cy.get('[data-testid="register-button"]').should('not.be.disabled');
    });
  });

  describe('Password Visibility Toggle', () => {
    beforeEach(() => {
      cy.visit('/register');
    });

    it('should toggle password visibility', () => {
      cy.get('[data-testid="password-input"]').type('secretpassword');
      
      // Initially password should be hidden
      cy.get('[data-testid="password-input"]').should('have.attr', 'type', 'password');
      
      // Click toggle to show password
      cy.get('[data-testid="password-toggle"]').click();
      cy.get('[data-testid="password-input"]').should('have.attr', 'type', 'text');
      
      // Click toggle to hide password again
      cy.get('[data-testid="password-toggle"]').click();
      cy.get('[data-testid="password-input"]').should('have.attr', 'type', 'password');
    });

    it('should toggle confirm password visibility', () => {
      cy.get('[data-testid="confirm-password-input"]').type('secretpassword');
      
      // Initially password should be hidden
      cy.get('[data-testid="confirm-password-input"]').should('have.attr', 'type', 'password');
      
      // Click toggle to show password
      cy.get('[data-testid="confirm-password-toggle"]').click();
      cy.get('[data-testid="confirm-password-input"]').should('have.attr', 'type', 'text');
      
      // Click toggle to hide password again
      cy.get('[data-testid="confirm-password-toggle"]').click();
      cy.get('[data-testid="confirm-password-input"]').should('have.attr', 'type', 'password');
    });
  });

  describe('Successful Registration', () => {
    it('should register successfully with valid data', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/register', {
          statusCode: 201,
          body: userData.authResponses.registrationSuccess
        }).as('registerSuccess');
        
        cy.visit('/register');
        
        const newUser = userData.newCustomer;
        cy.get('[data-testid="first-name-input"]').type(newUser.firstName);
        cy.get('[data-testid="last-name-input"]').type(newUser.lastName);
        cy.get('[data-testid="email-input"]').type(newUser.email);
        cy.get('[data-testid="phone-input"]').type(newUser.phone);
        cy.get('[data-testid="password-input"]').type(newUser.password);
        cy.get('[data-testid="confirm-password-input"]').type(newUser.password);
        cy.get('[data-testid="terms-checkbox"]').check();
        cy.get('[data-testid="register-button"]').click();
        
        cy.wait('@registerSuccess');
        
        // Should redirect to email verification page
        cy.url().should('include', '/verify-email');
        
        // Should show success notification
        cy.checkNotification('Registration successful! Please check your email.', 'success');
      });
    });

    it('should handle newsletter subscription', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/register', {
          statusCode: 201,
          body: userData.authResponses.registrationSuccess
        }).as('registerSuccess');
        
        cy.visit('/register');
        
        const newUser = userData.newCustomer;
        cy.get('[data-testid="first-name-input"]').type(newUser.firstName);
        cy.get('[data-testid="last-name-input"]').type(newUser.lastName);
        cy.get('[data-testid="email-input"]').type(newUser.email);
        cy.get('[data-testid="phone-input"]').type(newUser.phone);
        cy.get('[data-testid="password-input"]').type(newUser.password);
        cy.get('[data-testid="confirm-password-input"]').type(newUser.password);
        cy.get('[data-testid="terms-checkbox"]').check();
        cy.get('[data-testid="newsletter-checkbox"]').check();
        cy.get('[data-testid="register-button"]').click();
        
        cy.wait('@registerSuccess');
        
        // Verify newsletter subscription was included in request
        cy.get('@registerSuccess').should((interception) => {
          expect(interception.request.body).to.have.property('newsletter', true);
        });
      });
    });
  });

  describe('Registration Errors', () => {
    beforeEach(() => {
      cy.visit('/register');
    });

    it('should handle existing email error', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/register', {
          statusCode: 409,
          body: userData.authResponses.registrationExistingEmail
        }).as('existingEmail');
        
        const newUser = userData.newCustomer;
        cy.get('[data-testid="first-name-input"]').type(newUser.firstName);
        cy.get('[data-testid="last-name-input"]').type(newUser.lastName);
        cy.get('[data-testid="email-input"]').type('<EMAIL>');
        cy.get('[data-testid="phone-input"]').type(newUser.phone);
        cy.get('[data-testid="password-input"]').type(newUser.password);
        cy.get('[data-testid="confirm-password-input"]').type(newUser.password);
        cy.get('[data-testid="terms-checkbox"]').check();
        cy.get('[data-testid="register-button"]').click();
        
        cy.wait('@existingEmail');
        
        cy.checkErrorState('Email already exists');
        cy.get('[data-testid="email-error"]')
          .should('be.visible')
          .and('contain', 'This email is already registered');
      });
    });

    it('should handle weak password error', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/register', {
          statusCode: 400,
          body: userData.authResponses.registrationWeakPassword
        }).as('weakPassword');
        
        const newUser = userData.newCustomer;
        cy.get('[data-testid="first-name-input"]').type(newUser.firstName);
        cy.get('[data-testid="last-name-input"]').type(newUser.lastName);
        cy.get('[data-testid="email-input"]').type(newUser.email);
        cy.get('[data-testid="phone-input"]').type(newUser.phone);
        cy.get('[data-testid="password-input"]').type('weak');
        cy.get('[data-testid="confirm-password-input"]').type('weak');
        cy.get('[data-testid="terms-checkbox"]').check();
        cy.get('[data-testid="register-button"]').click();
        
        cy.wait('@weakPassword');
        
        cy.checkErrorState('Password does not meet requirements');
      });
    });

    it('should handle server error', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/register', {
          statusCode: 500,
          body: userData.authResponses.serverError
        }).as('serverError');
        
        const newUser = userData.newCustomer;
        cy.get('[data-testid="first-name-input"]').type(newUser.firstName);
        cy.get('[data-testid="last-name-input"]').type(newUser.lastName);
        cy.get('[data-testid="email-input"]').type(newUser.email);
        cy.get('[data-testid="phone-input"]').type(newUser.phone);
        cy.get('[data-testid="password-input"]').type(newUser.password);
        cy.get('[data-testid="confirm-password-input"]').type(newUser.password);
        cy.get('[data-testid="terms-checkbox"]').check();
        cy.get('[data-testid="register-button"]').click();
        
        cy.wait('@serverError');
        
        cy.checkErrorState('Something went wrong. Please try again.');
      });
    });

    it('should handle network error', () => {
      cy.intercept('POST', '/api/auth/register', { forceNetworkError: true }).as('networkError');
      
      cy.get('[data-testid="first-name-input"]').type('John');
      cy.get('[data-testid="last-name-input"]').type('Doe');
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="phone-input"]').type('+**********');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="confirm-password-input"]').type('password123');
      cy.get('[data-testid="terms-checkbox"]').check();
      cy.get('[data-testid="register-button"]').click();
      
      cy.wait('@networkError');
      
      cy.checkErrorState('Network error. Please check your connection.');
    });
  });

  describe('Loading States', () => {
    it('should show loading state during registration', () => {
      cy.intercept('POST', '/api/auth/register', (req) => {
        req.reply((res) => {
          res.delay(2000);
          res.send({ fixture: 'auth/customer-user.json' });
        });
      }).as('slowRegister');
      
      cy.visit('/register');
      
      cy.get('[data-testid="first-name-input"]').type('John');
      cy.get('[data-testid="last-name-input"]').type('Doe');
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="phone-input"]').type('+**********');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="confirm-password-input"]').type('password123');
      cy.get('[data-testid="terms-checkbox"]').check();
      cy.get('[data-testid="register-button"]').click();
      
      // Check loading state
      cy.get('[data-testid="register-button"]')
        .should('be.disabled')
        .and('contain', 'Creating Account...');
      
      cy.get('[data-testid="loading-spinner"]').should('be.visible');
      
      cy.wait('@slowRegister');
      
      // Loading state should be cleared
      cy.get('[data-testid="loading-spinner"]').should('not.exist');
    });
  });

  describe('Social Registration', () => {
    it('should handle Google registration', () => {
      cy.visit('/register');
      
      cy.get('[data-testid="google-register"]').click();
      
      // Should redirect to Google OAuth (we'll mock this in real tests)
      cy.url().should('include', 'google');
    });

    it('should handle Facebook registration', () => {
      cy.visit('/register');
      
      cy.get('[data-testid="facebook-register"]').click();
      
      // Should redirect to Facebook OAuth (we'll mock this in real tests)
      cy.url().should('include', 'facebook');
    });
  });

  describe('Navigation and Links', () => {
    beforeEach(() => {
      cy.visit('/register');
    });

    it('should navigate to login page', () => {
      cy.get('[data-testid="login-link"]').click();
      cy.url().should('include', '/login');
    });

    it('should open terms and conditions', () => {
      cy.get('[data-testid="terms-link"]').click();
      
      // Should open in new tab or modal
      cy.get('[data-testid="terms-modal"]').should('be.visible');
    });

    it('should open privacy policy', () => {
      cy.get('[data-testid="privacy-link"]').click();
      
      // Should open in new tab or modal
      cy.get('[data-testid="privacy-modal"]').should('be.visible');
    });

    it('should redirect authenticated users away from register page', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('GET', '/api/auth/session', {
          statusCode: 200,
          body: userData.authResponses.sessionValid
        }).as('authenticatedSession');
        
        cy.visit('/register');
        cy.wait('@authenticatedSession');
        
        // Should redirect away from register page
        cy.url().should('not.include', '/register');
      });
    });
  });

  describe('Email Verification Flow', () => {
    it('should handle email verification after registration', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/register', {
          statusCode: 201,
          body: userData.authResponses.registrationSuccess
        }).as('registerSuccess');
        
        cy.intercept('POST', '/api/auth/verify-email', {
          statusCode: 200,
          body: userData.authResponses.emailVerificationSuccess
        }).as('verifyEmailSuccess');
        
        // Complete registration
        cy.visit('/register');
        const newUser = userData.newCustomer;
        cy.get('[data-testid="first-name-input"]').type(newUser.firstName);
        cy.get('[data-testid="last-name-input"]').type(newUser.lastName);
        cy.get('[data-testid="email-input"]').type(newUser.email);
        cy.get('[data-testid="phone-input"]').type(newUser.phone);
        cy.get('[data-testid="password-input"]').type(newUser.password);
        cy.get('[data-testid="confirm-password-input"]').type(newUser.password);
        cy.get('[data-testid="terms-checkbox"]').check();
        cy.get('[data-testid="register-button"]').click();
        
        cy.wait('@registerSuccess');
        
        // Should be on verification page
        cy.url().should('include', '/verify-email');
        cy.get('[data-testid="verification-code-input"]').should('be.visible');
        
        // Enter verification code
        cy.get('[data-testid="verification-code-input"]').type('123456');
        cy.get('[data-testid="verify-button"]').click();
        
        cy.wait('@verifyEmailSuccess');
        
        // Should redirect to welcome page or dashboard
        cy.url().should('match', /\/(welcome|dashboard|menu)?$/);
        cy.checkNotification('Email verified successfully!', 'success');
      });
    });

    it('should handle resend verification email', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/resend-verification', {
          statusCode: 200,
          body: userData.authResponses.resendVerificationSuccess
        }).as('resendVerification');
        
        cy.visit('/verify-email');
        
        cy.get('[data-testid="resend-button"]').click();
        
        cy.wait('@resendVerification');
        
        cy.checkNotification('Verification email sent!', 'success');
        
        // Button should be disabled temporarily
        cy.get('[data-testid="resend-button"]').should('be.disabled');
      });
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      cy.visit('/register');
    });

    it('should be accessible', () => {
      cy.checkAccessibility();
    });

    it('should support keyboard navigation', () => {
      cy.checkKeyboardNavigation();
      
      // Test tab order
      cy.get('body').tab();
      cy.focused().should('have.attr', 'data-testid', 'first-name-input');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'last-name-input');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'email-input');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'phone-input');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'password-input');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'confirm-password-input');
    });

    it('should have proper ARIA labels', () => {
      cy.get('[data-testid="first-name-input"]')
        .should('have.attr', 'aria-label', 'First name')
        .and('have.attr', 'aria-required', 'true');
      
      cy.get('[data-testid="email-input"]')
        .should('have.attr', 'aria-label', 'Email address')
        .and('have.attr', 'aria-required', 'true');
      
      cy.get('[data-testid="password-input"]')
        .should('have.attr', 'aria-label', 'Password')
        .and('have.attr', 'aria-required', 'true');
      
      cy.get('[data-testid="terms-checkbox"]')
        .should('have.attr', 'aria-label', 'Accept terms and conditions')
        .and('have.attr', 'aria-required', 'true');
    });

    it('should announce errors to screen readers', () => {
      cy.get('[data-testid="register-button"]').click();
      
      cy.get('[data-testid="email-error"]')
        .should('have.attr', 'role', 'alert')
        .and('have.attr', 'aria-live', 'polite');
    });

    it('should have proper form fieldset and legend', () => {
      cy.get('[data-testid="personal-info-fieldset"]')
        .should('be.visible')
        .find('legend')
        .should('contain', 'Personal Information');
      
      cy.get('[data-testid="account-info-fieldset"]')
        .should('be.visible')
        .find('legend')
        .should('contain', 'Account Information');
    });
  });

  describe('Responsive Design', () => {
    it('should work on different screen sizes', () => {
      cy.testResponsiveDesign();
      
      // Test mobile layout
      cy.viewport(375, 667);
      cy.visit('/register');
      
      cy.get('[data-testid="register-form"]').should('be.visible');
      cy.get('[data-testid="social-register-buttons"]').should('be.visible');
      
      // Test tablet layout
      cy.viewport(768, 1024);
      cy.get('[data-testid="register-form"]').should('be.visible');
      
      // Test desktop layout
      cy.viewport(1280, 720);
      cy.get('[data-testid="register-form"]').should('be.visible');
    });

    it('should stack form fields properly on mobile', () => {
      cy.viewport(375, 667);
      cy.visit('/register');
      
      // Check that name fields stack on mobile
      cy.get('[data-testid="name-row"]')
        .should('have.css', 'flex-direction', 'column');
    });
  });

  describe('Security', () => {
    it('should clear form data on page refresh', () => {
      cy.visit('/register');
      
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('password123');
      
      cy.reload();
      
      cy.get('[data-testid="email-input"]').should('have.value', '');
      cy.get('[data-testid="password-input"]').should('have.value', '');
    });

    it('should not expose sensitive data in DOM', () => {
      cy.visit('/register');
      
      cy.get('[data-testid="password-input"]').type('secretpassword');
      
      // Password should not be visible in DOM when type="password"
      cy.get('[data-testid="password-input"]')
        .should('have.attr', 'type', 'password')
        .and('not.contain', 'secretpassword');
    });

    it('should prevent form submission on Enter key when invalid', () => {
      cy.visit('/register');
      
      cy.get('[data-testid="email-input"]').type('invalid-email{enter}');
      
      // Should not submit form
      cy.get('[data-testid="email-error"]').should('be.visible');
      cy.url().should('include', '/register');
    });

    it('should validate CSRF protection', () => {
      cy.visit('/register');
      
      // Check for CSRF token in form
      cy.get('input[name="_token"]').should('exist');
    });
  });

  describe('Performance', () => {
    it('should load registration page quickly', () => {
      cy.visit('/register');
      cy.measurePageLoadTime();
    });

    it('should optimize images', () => {
      cy.visit('/register');
      cy.checkImageOptimization();
    });

    it('should debounce validation checks', () => {
      cy.visit('/register');
      
      // Type rapidly and ensure validation doesn't fire on every keystroke
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      
      // Validation should only run after user stops typing
      cy.wait(500);
      cy.get('[data-testid="email-error"]').should('not.exist');
    });
  });
});