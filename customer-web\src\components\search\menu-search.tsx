'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Search, X, Filter } from 'lucide-react';
import { useMenuItems, useCategories } from '@/hooks/api-hooks';
import { cn } from '@/lib/utils';
import { GlassCard, GlassButton, GlassInput } from '@/components/ui/glass-components';
import MenuItemCard from '@/app/menu/[category]/menu-item-card';
import { MenuGridLoading } from '@/components/ui/loading-states';
import { EmptyState } from '@/components/ui/error-states';

interface MenuSearchProps {
  className?: string;
  onItemSelect?: (item: any) => void;
  showFilters?: boolean;
}

interface SearchFilters {
  category: string;
  priceRange: [number, number];
  dietary: string[];
  sortBy: 'name' | 'price' | 'popularity';
}

const DIETARY_OPTIONS = [
  { id: 'vegetarian', label: 'Vegetarian' },
  { id: 'vegan', label: 'Vegan' },
  { id: 'gluten-free', label: 'Gluten Free' },
  { id: 'dairy-free', label: 'Dairy Free' },
  { id: 'nut-free', label: 'Nut Free' },
];

const SORT_OPTIONS = [
  { value: 'name', label: 'Name (A-Z)' },
  { value: 'price', label: 'Price (Low to High)' },
  { value: 'popularity', label: 'Most Popular' },
];

export function MenuSearch({ className, onItemSelect, showFilters = true }: MenuSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    category: 'all',
    priceRange: [0, 50],
    dietary: [],
    sortBy: 'name',
  });

  const { categories, loading: categoriesLoading } = useCategories();
  const { menuItems: allMenuItems, loading: itemsLoading } = useMenuItems();

  // Filter and search logic
  const filteredItems = useMemo(() => {
    if (!allMenuItems) return [];

    let items = allMenuItems;

    // Text search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      items = items.filter(item => 
        item.name.toLowerCase().includes(query) ||
        item.description?.toLowerCase().includes(query) ||
        item.ingredients?.some((ing: string) => ing.toLowerCase().includes(query))
      );
    }

    // Category filter
    if (filters.category !== 'all') {
      items = items.filter(item => item.category_id === filters.category);
    }

    // Price range filter
    items = items.filter(item => 
      item.price >= filters.priceRange[0] && item.price <= filters.priceRange[1]
    );

    // Dietary filters
    if (filters.dietary.length > 0) {
      items = items.filter(item => 
        filters.dietary.every(diet => {
          switch (diet) {
            case 'vegetarian':
              return item.isVegetarian;
            case 'vegan':
              return item.isVegan;
            case 'gluten-free':
              return item.isGlutenFree;
            case 'dairy-free':
              return item.isDairyFree;
            case 'nut-free':
              return item.isNutFree;
            default:
              return false;
          }
        })
      );
    }

    // Sorting
    items.sort((a, b) => {
      switch (filters.sortBy) {
        case 'price':
          return a.price - b.price;
        case 'popularity':
          return (b.popularity || 0) - (a.popularity || 0);
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return items;
  }, [allMenuItems, searchQuery, filters]);

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      category: 'all',
      priceRange: [0, 50],
      dietary: [],
      sortBy: 'name',
    });
    setSearchQuery('');
  };

  const hasActiveFilters = 
    filters.category !== 'all' ||
    filters.priceRange[0] > 0 ||
    filters.priceRange[1] < 50 ||
    filters.dietary.length > 0 ||
    searchQuery.trim() !== '';

  if (categoriesLoading || itemsLoading) {
    return <MenuGridLoading />;
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Search Bar */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search menu items, ingredients..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-10 py-3 rounded-lg border border-input bg-background/50 backdrop-blur-sm focus:border-primary focus:ring-1 focus:ring-primary transition-colors"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 hover:bg-muted rounded-full transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
        
        {showFilters && (
          <GlassButton
            variant="secondary"
            onClick={() => setShowFilterPanel(!showFilterPanel)}
            className={cn('flex items-center gap-2', {
              'bg-primary/10 text-primary': hasActiveFilters
            })}
          >
            <Filter className="h-4 w-4" />
            Filters
            {hasActiveFilters && (
              <span className="bg-primary text-primary-foreground text-xs px-1.5 py-0.5 rounded-full">
                {[filters.category !== 'all', filters.dietary.length > 0, searchQuery.trim()].filter(Boolean).length}
              </span>
            )}
          </GlassButton>
        )}
      </div>

      {/* Filter Panel */}
      {showFilters && showFilterPanel && (
        <GlassCard className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium mb-2">Category</label>
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="w-full p-2 rounded-md border border-input bg-background"
              >
                <option value="all">All Categories</option>
                {categories?.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Price Range */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Price Range: ${filters.priceRange[0]} - ${filters.priceRange[1]}
              </label>
              <div className="space-y-2">
                <input
                  type="range"
                  min="0"
                  max="50"
                  value={filters.priceRange[0]}
                  onChange={(e) => handleFilterChange('priceRange', [Number(e.target.value), filters.priceRange[1]])}
                  className="w-full"
                />
                <input
                  type="range"
                  min="0"
                  max="50"
                  value={filters.priceRange[1]}
                  onChange={(e) => handleFilterChange('priceRange', [filters.priceRange[0], Number(e.target.value)])}
                  className="w-full"
                />
              </div>
            </div>

            {/* Dietary Options */}
            <div>
              <label className="block text-sm font-medium mb-2">Dietary</label>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {DIETARY_OPTIONS.map(option => (
                  <label key={option.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={filters.dietary.includes(option.id)}
                      onChange={(e) => {
                        const newDietary = e.target.checked
                          ? [...filters.dietary, option.id]
                          : filters.dietary.filter(d => d !== option.id);
                        handleFilterChange('dietary', newDietary);
                      }}
                      className="rounded border-input"
                    />
                    <span className="text-sm">{option.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Sort By */}
            <div>
              <label className="block text-sm font-medium mb-2">Sort By</label>
              <select
                value={filters.sortBy}
                onChange={(e) => handleFilterChange('sortBy', e.target.value as SearchFilters['sortBy'])}
                className="w-full p-2 rounded-md border border-input bg-background"
              >
                {SORT_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Filter Actions */}
          <div className="flex justify-between items-center mt-6 pt-4 border-t">
            <span className="text-sm text-muted-foreground">
              {filteredItems.length} item{filteredItems.length !== 1 ? 's' : ''} found
            </span>
            
            {hasActiveFilters && (
              <GlassButton variant="secondary" size="sm" onClick={clearFilters}>
                Clear All Filters
              </GlassButton>
            )}
          </div>
        </GlassCard>
      )}

      {/* Results */}
      <div>
        {filteredItems.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredItems.map(item => (
              <div key={item.id} onClick={() => onItemSelect?.(item)}>
                <MenuItemCard
                  item={item}
                  categoryId={item.category_id}
                  categoryVariant="primary"
                />
              </div>
            ))}
          </div>
        ) : (
          <EmptyState
            title="No items found"
            message={searchQuery.trim() 
              ? `No menu items match "${searchQuery}" with the current filters.`
              : "No menu items match the current filters."
            }
            actionLabel="Clear Filters"
            onAction={clearFilters}
          />
        )}
      </div>
    </div>
  );
}