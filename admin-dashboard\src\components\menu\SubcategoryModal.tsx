'use client'

import React, { useState, useEffect } from 'react'
import { X, Save, Plus, Minus } from 'lucide-react'

interface SubcategoryModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (subcategory: any) => void
  subcategory?: any
  categories?: any[]
  ingredients?: any[]
}

const SubcategoryModal = ({
  isOpen,
  onClose,
  onSave,
  subcategory,
  categories = [],
  ingredients = []
}: SubcategoryModalProps) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category_id: '',
    pickup_price: 0,
    delivery_price: 0,
    is_active: true,
    is_featured: false,
    is_customizable: false,
    max_ingredients: 10,
    display_order: 0
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (subcategory) {
      setFormData({
        name: subcategory.name || '',
        description: subcategory.description || '',
        category_id: subcategory.category_id || '',
        pickup_price: subcategory.pickup_price || subcategory.price || 0,
        delivery_price: subcategory.delivery_price || subcategory.price || 0,
        is_active: subcategory.is_active ?? true,
        is_featured: subcategory.is_featured ?? false,
        is_customizable: subcategory.is_customizable ?? false,
        max_ingredients: subcategory.max_ingredients || 10,
        display_order: subcategory.display_order || 0
      })
    } else {
      setFormData({
        name: '',
        description: '',
        category_id: (categories && categories.length > 0) ? categories[0].id : '',
        pickup_price: 0,
        delivery_price: 0,
        is_active: true,
        is_featured: false,
        is_customizable: false,
        max_ingredients: 10,
        display_order: 0
      })
    }
    setErrors({})
  }, [subcategory, isOpen, categories])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    const newErrors: Record<string, string> = {}
    
    if (!formData.name.trim()) {
      newErrors.name = 'Subcategory name is required'
    }
    
    if (formData.name.length > 100) {
      newErrors.name = 'Subcategory name must be less than 100 characters'
    }

    if (!formData.category_id) {
      newErrors.category_id = 'Please select a parent category'
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }

    onSave({
      ...formData,
      id: subcategory?.id
    })
    onClose()
  }

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }


  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" data-cy="subcategory-modal">
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            {subcategory ? 'Edit Subcategory' : 'Add Subcategory'}
          </h2>
          <button
            onClick={onClose}
            data-cy="modal-close"
            aria-label="Close modal"
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Name *
              </label>
              <input
                type="text"
                id="name"
                data-cy="subcategory-name-input"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.name
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                    : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700'
                } text-gray-900 dark:text-white`}
                placeholder="Enter subcategory name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400" data-cy="validation-error">
                  {errors.name}
                </p>
              )}
            </div>

            <div>
              <label htmlFor="category_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Parent Category *
              </label>
              <select
                id="category_id"
                data-cy="subcategory-category-select"
                value={formData.category_id}
                onChange={(e) => handleChange('category_id', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.category_id
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                    : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700'
                } text-gray-900 dark:text-white`}
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.category_id && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400" data-cy="validation-error">
                  {errors.category_id}
                </p>
              )}
            </div>
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <textarea
              id="description"
              data-cy="subcategory-description-input"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="Enter subcategory description"
            />
          </div>

          {/* Pricing Section */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Order Type Pricing</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="pickup_price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Pickup Price (€) *
                </label>
                <input
                  type="number"
                  id="pickup_price"
                  data-cy="subcategory-pickup-price-input"
                  value={formData.pickup_price}
                  onChange={(e) => handleChange('pickup_price', parseFloat(e.target.value) || 0)}
                  step="0.01"
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="0.00"
                />
              </div>

              <div>
                <label htmlFor="delivery_price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Delivery Price (€) *
                </label>
                <input
                  type="number"
                  id="delivery_price"
                  data-cy="subcategory-delivery-price-input"
                  value={formData.delivery_price}
                  onChange={(e) => handleChange('delivery_price', parseFloat(e.target.value) || 0)}
                  step="0.01"
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="0.00"
                />
              </div>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              Set different prices for pickup and delivery orders. This allows for flexible pricing strategies.
            </p>
          </div>

          <div>
            <label htmlFor="display_order" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Display Order
            </label>
            <input
              type="number"
              id="display_order"
              data-cy="subcategory-order-input"
              value={formData.display_order}
              onChange={(e) => handleChange('display_order', parseInt(e.target.value) || 0)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder="0"
            />
          </div>


          {/* Customization Section */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_customizable"
              data-cy="subcategory-customizable-checkbox"
              checked={formData.is_customizable}
              onChange={(e) => handleChange('is_customizable', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_customizable" className="ml-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Customizable
            </label>
          </div>

          <div className="flex items-center space-x-6">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active"
                data-cy="subcategory-active-checkbox"
                checked={formData.is_active}
                onChange={(e) => handleChange('is_active', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="is_active" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Active
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_featured"
                data-cy="subcategory-featured-checkbox"
                checked={formData.is_featured}
                onChange={(e) => handleChange('is_featured', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Featured
              </label>
            </div>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              data-cy="cancel-button"
              className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              data-cy="save-button"
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
            >
              <Save className="h-4 w-4 mr-2" />
              {subcategory ? 'Update' : 'Save'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default SubcategoryModal