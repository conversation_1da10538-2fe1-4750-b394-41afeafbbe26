'use client';

import React, { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useTheme } from '@/contexts/theme-context';

export default function FloatingNavbar() {
  const router = useRouter();
  const pathname = usePathname();
  const [isExpanded, setIsExpanded] = useState(true);
  const { isDarkTheme } = useTheme();

  const handleNavigation = (href: string) => {
    router.push(href);
  };

  const isActive = (href: string) => {
    return pathname === href || (href === '/dashboard' && pathname === '/');
  };

  const toggleNavigation = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="absolute top-6 left-1/2 transform -translate-x-1/2 z-10">
      <div 
        className="backdrop-blur-md border shadow-lg transition-all duration-500 ease-out bg-white/10 border-white/20" 
        style={{ width: '520px', height: '56px', borderRadius: '28px' }}
      >
        <div className="relative w-full h-full overflow-hidden">
          {/* Central toggle button */}
          <button
            onClick={toggleNavigation}
            className="absolute w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center z-10 bg-gradient-to-br from-white/25 to-white/15 border-white/35 hover:from-white/35 hover:to-white/25 text-white"
            style={{
              left: '50%',
              top: '50%',
              transform: 'translate(-50%, -50%)',
              boxShadow: 'rgba(255, 255, 255, 0.6) 0px 0px 20px, rgba(255, 255, 255, 0.4) 0px 1px 0px inset'
            }}
            title={isExpanded ? 'Hide Navigation' : 'Show Navigation'}
            aria-label={isExpanded ? 'Hide Navigation' : 'Show Navigation'}
          >
            <div
              className={`w-2 h-2 rounded-full bg-current transition-all duration-300 ${isExpanded ? 'animate-pulse' : 'animate-bounce'}`}
              style={{ boxShadow: 'rgba(255, 255, 255, 0.8) 0px 0px 8px' }}
            />
          </button>

          {/* Left section */}
          <div
            className={`absolute top-1/2 transform -translate-y-1/2 flex items-center space-x-4 transition-all duration-400 ease-out ${
              isExpanded ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-8'
            }`}
            style={{ left: '70px' }}
          >
            {/* Dashboard */}
            <button
              onClick={() => handleNavigation('/dashboard')}
              className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center ${
                isActive('/dashboard')
                  ? isDarkTheme
                    ? 'bg-white/20 border-white/40 text-white scale-110'
                    : 'bg-black/20 border-black/40 text-black scale-110'
                  : isDarkTheme
                    ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white scale-100'
                    : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black scale-100'
              }`}
              title="Dashboard"
              style={{ transitionDelay: '0ms' }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
              </svg>
            </button>

            {/* POS */}
            <button
              onClick={() => handleNavigation('/pos')}
              className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center ${
                isActive('/pos')
                  ? isDarkTheme
                    ? 'bg-white/20 border-white/40 text-white scale-110'
                    : 'bg-black/20 border-black/40 text-black scale-110'
                  : isDarkTheme
                    ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white scale-100'
                    : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black scale-100'
              }`}
              title="POS"
              style={{ transitionDelay: '50ms' }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
              </svg>
            </button>

            {/* Web */}
            <button
              onClick={() => handleNavigation('/web')}
              className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center ${
                isActive('/web')
                  ? isDarkTheme
                    ? 'bg-white/20 border-white/40 text-white scale-110'
                    : 'bg-black/20 border-black/40 text-black scale-110'
                  : isDarkTheme
                    ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white scale-100'
                    : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black scale-100'
              }`}
              title="Web"
              style={{ transitionDelay: '100ms' }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" strokeWidth="2"></circle>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2 12h20"></path>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
              </svg>
            </button>

            {/* App */}
            <button
              onClick={() => handleNavigation('/app')}
              className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center ${
                isActive('/app')
                  ? isDarkTheme
                    ? 'bg-white/20 border-white/40 text-white scale-110'
                    : 'bg-black/20 border-black/40 text-black scale-110'
                  : isDarkTheme
                    ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white scale-100'
                    : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black scale-100'
              }`}
              title="App"
              style={{ transitionDelay: '150ms' }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
              </svg>
            </button>

            {/* Users/Staff */}
            <button
              onClick={() => handleNavigation('/users')}
              className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center ${
                isActive('/users') || isActive('/staff')
                  ? isDarkTheme
                    ? 'bg-white/20 border-white/40 text-white scale-110'
                    : 'bg-black/20 border-black/40 text-black scale-110'
                  : isDarkTheme
                    ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white scale-100'
                    : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black scale-100'
              }`}
              title="Users & Staff"
              style={{ transitionDelay: '200ms' }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            </button>


          </div>

          {/* Right section */}
          <div
            className={`absolute top-1/2 transform -translate-y-1/2 flex items-center space-x-4 transition-all duration-400 ease-out ${
              isExpanded ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-8'
            }`}
            style={{ right: '70px' }}
          >
            {/* Orders */}
            <button
              onClick={() => handleNavigation('/orders')}
              className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center ${
                isActive('/orders')
                  ? isDarkTheme
                    ? 'bg-white/20 border-white/40 text-white scale-110'
                    : 'bg-black/20 border-black/40 text-black scale-110'
                  : isDarkTheme
                    ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white scale-100'
                    : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black scale-100'
              }`}
              title="Orders"
              style={{ transitionDelay: '0ms' }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
              </svg>
            </button>

            {/* Menu */}
            <button
              onClick={() => handleNavigation('/menu')}
              className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center ${
                isActive('/menu')
                  ? isDarkTheme
                    ? 'bg-white/20 border-white/40 text-white scale-110'
                    : 'bg-black/20 border-black/40 text-black scale-110'
                  : isDarkTheme
                    ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white scale-100'
                    : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black scale-100'
              }`}
              title="Menu"
              style={{ transitionDelay: '50ms' }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
              </svg>
            </button>

            {/* Customers */}
            <button
              onClick={() => handleNavigation('/customers')}
              className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center ${
                isActive('/customers')
                  ? isDarkTheme
                    ? 'bg-white/20 border-white/40 text-white scale-110'
                    : 'bg-black/20 border-black/40 text-black scale-110'
                  : isDarkTheme
                    ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white scale-100'
                    : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black scale-100'
              }`}
              title="Customers"
              style={{ transitionDelay: '150ms' }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </button>



            {/* System Admin */}
            <button
              onClick={() => handleNavigation('/admin')}
              className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center ${
                isActive('/admin')
                  ? isDarkTheme
                    ? 'bg-white/20 border-white/40 text-white scale-110'
                    : 'bg-black/20 border-black/40 text-black scale-110'
                  : isDarkTheme
                    ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white scale-100'
                    : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black scale-100'
              }`}
              title="System Admin"
              style={{ transitionDelay: '200ms' }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
              </svg>
            </button>

            {/* Map/Delivery Zones */}
            <button
              onClick={() => handleNavigation('/delivery-zones')}
              className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center ${
                isActive('/delivery-zones') || pathname.includes('/map')
                  ? isDarkTheme
                    ? 'bg-white/20 border-white/40 text-white scale-110'
                    : 'bg-black/20 border-black/40 text-black scale-110'
                  : isDarkTheme
                    ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white scale-100'
                    : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black scale-100'
              }`}
              title="Delivery Zones & Map"
              style={{ transitionDelay: '250ms' }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}