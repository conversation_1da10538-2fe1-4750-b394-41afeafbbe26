import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { GlassCard, GlassButton, GlassInput, GlassDivider } from '@/components/ui/glass-components';

export const metadata: Metadata = {
  title: 'Register | Delicious Crepes & Waffles',
  description: 'Create a new account to order delicious crepes and waffles.',
};

export default function RegisterPage() {
  return (
    <main className="min-h-screen py-12 px-4 flex items-center justify-center">
      <div className="max-w-md w-full">
        <div className="text-center mb-8">
          <Link href="/" className="inline-block">
            <Image
              src="/images/logo.svg"
              alt="Delicious Crepes & Waffles"
              width={150}
              height={60}
              className="mx-auto"
            />
          </Link>
          <h1 className="text-3xl font-bold mt-6">Create an Account</h1>
          <p className="text-muted-foreground mt-2">Join us for delicious treats</p>
        </div>

        <GlassCard>
          <div className="p-6">
            <form>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <GlassInput label="First Name" placeholder="Enter your first name" required />

                  <GlassInput label="Last Name" placeholder="Enter your last name" required />
                </div>

                <GlassInput
                  label="Email Address"
                  type="email"
                  placeholder="Enter your email address"
                  required
                />

                <GlassInput label="Phone Number" type="tel" placeholder="Enter your phone number" />

                <GlassInput
                  label="Password"
                  type="password"
                  placeholder="Create a password"
                  required
                />

                <GlassInput
                  label="Confirm Password"
                  type="password"
                  placeholder="Confirm your password"
                  required
                />

                <div className="space-y-2">
                  <label className="flex items-start space-x-2 cursor-pointer">
                    <input type="checkbox" className="h-4 w-4 mt-1 text-primary rounded" required />

                    <span className="text-sm">
                      I agree to the{' '}
                      <Link href="/terms" className="text-primary hover:underline">
                        Terms of Service
                      </Link>{' '}
                      and{' '}
                      <Link href="/privacy" className="text-primary hover:underline">
                        Privacy Policy
                      </Link>
                    </span>
                  </label>

                  <label className="flex items-start space-x-2 cursor-pointer">
                    <input type="checkbox" className="h-4 w-4 mt-1 text-primary rounded" />

                    <span className="text-sm">
                      I want to receive promotional emails about special offers and new products
                    </span>
                  </label>
                </div>

                <GlassButton variant="primary" className="w-full">
                  Create Account
                </GlassButton>
              </div>
            </form>

            <div className="my-6 flex items-center">
              <GlassDivider className="flex-1" />
              <span className="px-4 text-sm text-muted-foreground">or sign up with</span>
              <GlassDivider className="flex-1" />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <button className="flex items-center justify-center space-x-2 p-2 border border-input rounded-md hover:bg-muted transition-colors">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    fill="#4285F4"
                  />
                  <path
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    fill="#34A853"
                  />
                  <path
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    fill="#FBBC05"
                  />
                  <path
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    fill="#EA4335"
                  />
                </svg>
                <span>Google</span>
              </button>

              <button className="flex items-center justify-center space-x-2 p-2 border border-input rounded-md hover:bg-muted transition-colors">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 24 24"
                  fill="#1877F2"
                >
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                </svg>
                <span>Facebook</span>
              </button>
            </div>

            <div className="mt-6 text-center">
              <p className="text-sm text-muted-foreground">
                Already have an account?{' '}
                <Link href="/login" className="text-primary hover:underline">
                  Sign in
                </Link>
              </p>
            </div>
          </div>
        </GlassCard>

        <div className="mt-8 text-center text-sm text-muted-foreground">
          <p>By creating an account, you agree to our</p>
          <div className="flex items-center justify-center space-x-2 mt-1">
            <Link href="/terms" className="hover:underline">
              Terms of Service
            </Link>
            <span>•</span>
            <Link href="/privacy" className="hover:underline">
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
}
