import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';

// GET /api/delivery-zones - Fetch all delivery zones
export async function GET(request: NextRequest) {
  const supabase = createServerSupabaseClient();
  
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const status = searchParams.get('status') // 'active', 'inactive', 'all'
    const sortBy = searchParams.get('sortBy') || 'priority'
    const sortOrder = searchParams.get('sortOrder') || 'asc'

    // Build query
    let query = supabase
      .from('delivery_zones')
      .select('*')

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
    }

    if (status && status !== 'all') {
      query = query.eq('is_active', status === 'active')
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' })

    const { data, error } = await query

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch delivery zones' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      zones: data || []
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/delivery-zones - Create a new delivery zone
export async function POST(request: NextRequest) {
  const supabase = createServerSupabaseClient();
  
  try {
    const body = await request.json();
    const { 
      name, 
      description, 
      coordinates,
      delivery_fee,
      minimum_order_amount,
      estimated_delivery_time_min,
      estimated_delivery_time_max,
      is_active = true,
      priority = 5,
      color = '#3B82F6'
    } = body;

    // Validate required fields
    if (!name || !coordinates || coordinates.length < 3) {
      return NextResponse.json(
        { error: 'Name and at least 3 coordinate points are required' },
        { status: 400 }
      );
    }

    // Validate coordinates format
    const validCoordinates = coordinates.every((coord: any) => 
      typeof coord.lat === 'number' && 
      typeof coord.lng === 'number' &&
      coord.lat >= -90 && coord.lat <= 90 &&
      coord.lng >= -180 && coord.lng <= 180
    );

    if (!validCoordinates) {
      return NextResponse.json(
        { error: 'Invalid coordinate format' },
        { status: 400 }
      );
    }

    // Check if zone name already exists
    const { data: existingZone } = await supabase
      .from('delivery_zones')
      .select('id')
      .eq('name', name)
      .single();

    if (existingZone) {
      return NextResponse.json(
        { error: 'Zone with this name already exists' },
        { status: 409 }
      );
    }

    // Create delivery zone
    const { data, error } = await supabase
      .from('delivery_zones')
      .insert([
        {
          name,
          description,
          coordinates,
          delivery_fee: parseFloat(delivery_fee) || 0,
          minimum_order_amount: parseFloat(minimum_order_amount) || 0,
          estimated_delivery_time_min: parseInt(estimated_delivery_time_min) || 30,
          estimated_delivery_time_max: parseInt(estimated_delivery_time_max) || 60,
          is_active,
          priority: parseInt(priority) || 5,
          color
        }
      ])
      .select();

    if (error) {
      return NextResponse.json(
        { error: 'Failed to create delivery zone' },
        { status: 500 }
      );
    }

    return NextResponse.json(data[0], { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/delivery-zones - Update delivery zone
export async function PUT(request: NextRequest) {
  const supabase = createServerSupabaseClient();
  
  try {
    const body = await request.json();
    const { 
      id,
      name, 
      description, 
      coordinates,
      delivery_fee,
      minimum_order_amount,
      estimated_delivery_time_min,
      estimated_delivery_time_max,
      is_active,
      priority,
      color
    } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Zone ID is required' },
        { status: 400 }
      );
    }

    // Validate coordinates if provided
    if (coordinates) {
      const validCoordinates = coordinates.every((coord: any) => 
        typeof coord.lat === 'number' && 
        typeof coord.lng === 'number' &&
        coord.lat >= -90 && coord.lat <= 90 &&
        coord.lng >= -180 && coord.lng <= 180
      );

      if (!validCoordinates || coordinates.length < 3) {
        return NextResponse.json(
          { error: 'Invalid coordinate format or insufficient points' },
          { status: 400 }
        );
      }
    }

    // Update delivery zone
    const { data, error } = await supabase
      .from('delivery_zones')
      .update({
        name,
        description,
        coordinates,
        delivery_fee: delivery_fee ? parseFloat(delivery_fee) : undefined,
        minimum_order_amount: minimum_order_amount ? parseFloat(minimum_order_amount) : undefined,
        estimated_delivery_time_min: estimated_delivery_time_min ? parseInt(estimated_delivery_time_min) : undefined,
        estimated_delivery_time_max: estimated_delivery_time_max ? parseInt(estimated_delivery_time_max) : undefined,
        is_active,
        priority: priority ? parseInt(priority) : undefined,
        color,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select();

    if (error) {
      return NextResponse.json(
        { error: 'Failed to update delivery zone' },
        { status: 500 }
      );
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: 'Zone not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(data[0]);
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/delivery-zones - Delete specific delivery zone
export async function DELETE(request: NextRequest) {
  const supabase = createServerSupabaseClient();
  
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Zone ID is required' },
        { status: 400 }
      );
    }

    // Check if zone has active orders
    const { data: activeOrders } = await supabase
      .from('orders')
      .select('id')
      .eq('delivery_zone_id', id)
      .in('status', ['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery']);

    if (activeOrders && activeOrders.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete zone with active orders. Please complete or cancel existing orders first.' },
        { status: 400 }
      );
    }

    // Delete delivery zone
    const { data, error } = await supabase
      .from('delivery_zones')
      .delete()
      .eq('id', id)
      .select();

    if (error) {
      return NextResponse.json(
        { error: 'Failed to delete delivery zone' },
        { status: 500 }
      );
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: 'Zone not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Zone deleted successfully' });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}