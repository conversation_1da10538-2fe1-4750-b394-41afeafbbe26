import { NextRequest, NextResponse } from 'next/server'
import { generateRegistrationOptions } from '@simplewebauthn/server'
import type { GenerateRegistrationOptionsOpts } from '@simplewebauthn/server'

// Configuration
const rpID = process.env.WEBAUTHN_RP_ID || 'localhost'
const rpName = process.env.WEBAUTHN_RP_NAME || 'Creperie Admin Dashboard'
const origin = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      userId,
      userEmail,
      userName,
      userDisplayName,
      requireResidentKey = true,
      userVerification = 'preferred',
      authenticatorSelection,
      attestation = 'direct',
    } = body

    // Validate required fields
    if (!userId || !userEmail || !userName) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, userEmail, userName' },
        { status: 400 }
      )
    }

    // TODO: Get user's existing credentials from database
    // For now, we'll assume no existing credentials
    const existingCredentials: any[] = []

    const options: GenerateRegistrationOptionsOpts = {
      rpName,
      rpID,
      userID: new TextEncoder().encode(userId),
      userName: userEmail,
      userDisplayName: userDisplayName || userName,
      timeout: 60000,
      attestationType: attestation,
      excludeCredentials: existingCredentials.map(cred => ({
        id: cred.credentialID,
        transports: cred.transports,
      })),
      authenticatorSelection: authenticatorSelection || {
        authenticatorAttachment: 'platform',
        userVerification,
        requireResidentKey,
      },
      supportedAlgorithmIDs: [-7, -257], // ES256 and RS256
    }

    const registrationOptions = await generateRegistrationOptions(options)

    // TODO: Store challenge in session/database for verification
    // For now, we'll store it in a simple in-memory store
    ;(global as any).webauthnChallenges = (global as any).webauthnChallenges || new Map()
    ;(global as any).webauthnChallenges.set(userId, {
      challenge: registrationOptions.challenge,
      timestamp: Date.now(),
    })

    return NextResponse.json(registrationOptions)
  } catch (error) {
    console.error('Error generating registration options:', error)
    return NextResponse.json(
      { error: 'Failed to generate registration options' },
      { status: 500 }
    )
  }
} 