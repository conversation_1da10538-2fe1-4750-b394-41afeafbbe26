import { getSupabaseClient, handleSupabaseError } from './supabase-config';

// Re-export the client for backward compatibility
export const supabase = getSupabaseClient();

// Re-export error handler for backward compatibility
export { handleSupabaseError } from './supabase-config';

// Helper function to check if user is authenticated
export const checkAuth = async () => {
  const {
    data: { session },
    error,
  } = await supabase.auth.getSession()

  if (error) {
    console.error('Auth check error:', error)
    return null
  }

  return session
}

// Helper function to sign out
export const signOut = async () => {
  const { error } = await supabase.auth.signOut()

  if (error) {
    console.error('Sign out error:', error)
    throw error
  }
}

// Real-time subscription helper
export const subscribeToTable = (
  table: string,
  callback: (payload: any) => void,
  filter?: string
) => {
  const channel = supabase
    .channel(`${table}_changes`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table,
        filter,
      },
      callback
    )
    .subscribe()

  return channel
}

// Unsubscribe from real-time updates
export const unsubscribeFromChannel = (channel: any) => {
  supabase.removeChannel(channel)
}

// POS-specific helper functions
export const syncSettings = async () => {
  try {
    const { data, error } = await supabase
      .from('pos_configurations')
      .select('*')
      .single()

    if (error) {
      console.error('Settings sync error:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Settings sync error:', error)
    return null
  }
}

export const syncMenuItems = async () => {
  try {
    const { data, error } = await supabase
      .from('subcategories')
      .select(`
        *,
        menu_categories (*),
        menu_customizations (*)
      `)
      .eq('active', true)

    if (error) {
      console.error('Menu sync error:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Menu sync error:', error)
    return []
  }
}

export const syncDeliveryZones = async () => {
  try {
    const { data, error } = await supabase
      .from('delivery_zones')
      .select('*')
      .eq('active', true)

    if (error) {
      console.error('Delivery zones sync error:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Delivery zones sync error:', error)
    return []
  }
}

export default supabase