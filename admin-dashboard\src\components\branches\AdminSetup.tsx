'use client'

import React, { useState } from 'react'
import { Shield, Database, CheckCircle, AlertCircle } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import toast from 'react-hot-toast'

export default function AdminSetup() {
  const [loading, setLoading] = useState(false)
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const grantAdminPrivileges = async () => {
    setLoading(true)
    setStatus('idle')

    try {
      // Step 1: Check if user exists in staff table
      const { data: existingStaff, error: checkError } = await supabase
        .from('staff')
        .select('id, email, role_id')
        .eq('email', '<EMAIL>')
        .single()

      if (checkError && checkError.code !== 'PGRST116') {
        throw new Error(`Check error: ${checkError.message}`)
      }

      // Step 2: Get admin role ID
      const { data: adminRole, error: roleError } = await supabase
        .from('roles')
        .select('id, name')
        .eq('name', 'admin')
        .single()

      if (roleError) {
        throw new Error(`Role lookup error: ${roleError.message}`)
      }

      // Step 3: Insert or update staff record
      const staffData = {
        name: 'Tomi Kroparisi',
        first_name: 'Tomi',
        last_name: 'Kroparisi',
        email: '<EMAIL>',
        pin: '1234',
        pin_hash: '$2b$10$rQZ8kqKqKqKqKqKqKqKqKOeKqKqKqKqKqKqKqKqKqKqKqKqKqKqKq',
        role_id: adminRole.id,
        is_active: true,
        can_login_admin: true,
        can_login_pos: true,
        staff_code: 'ADMIN001'
      }

      let result
      if (existingStaff) {
        // Update existing staff
        result = await supabase
          .from('staff')
          .update({
            role_id: adminRole.id,
            is_active: true,
            can_login_admin: true,
            can_login_pos: true
          })
          .eq('email', '<EMAIL>')
      } else {
        // Insert new staff
        result = await supabase
          .from('staff')
          .insert([staffData])
      }

      if (result.error) {
        throw new Error(`Staff update error: ${result.error.message}`)
      }

      // Step 4: Also create user profile as backup
      await supabase
        .from('user_profiles')
        .upsert({
          email: '<EMAIL>',
          first_name: 'Tomi',
          last_name: 'Kroparisi',
          role: 'admin',
          is_active: true
        })

      setStatus('success')
      toast.success('Admin privileges granted successfully!')
      
      // Refresh the page after a short delay
      setTimeout(() => {
        window.location.reload()
      }, 2000)

    } catch (error: any) {
      console.error('Admin setup error:', error)
      setStatus('error')
      toast.error(`Failed to grant admin privileges: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="glass-container glass-primary p-6 rounded-xl max-w-md mx-auto">
      <div className="text-center">
        <Database className="h-12 w-12 text-blue-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold glass-text-primary mb-2">Admin Setup Required</h3>
        <p className="glass-text-secondary mb-6">
          Click the button below to grant admin privileges to your account.
        </p>
        
        <button
          onClick={grantAdminPrivileges}
          disabled={loading || status === 'success'}
          className={`glass-button glass-interactive w-full mb-4 ${
            status === 'success' ? 'bg-green-500/20' : 
            status === 'error' ? 'bg-red-500/20' : ''
          }`}
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Setting up admin access...
            </>
          ) : status === 'success' ? (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              Admin Access Granted!
            </>
          ) : status === 'error' ? (
            <>
              <AlertCircle className="h-4 w-4 mr-2" />
              Try Again
            </>
          ) : (
            <>
              <Shield className="h-4 w-4 mr-2" />
              Grant Admin Privileges
            </>
          )}
        </button>

        {status === 'success' && (
          <div className="p-4 bg-green-500/10 rounded-lg">
            <p className="text-sm glass-text-secondary">
              ✅ Admin privileges granted! The page will refresh automatically.
            </p>
          </div>
        )}

        {status === 'error' && (
          <div className="p-4 bg-red-500/10 rounded-lg">
            <p className="text-sm glass-text-secondary">
              ❌ Failed to grant admin privileges. Please check the console for details.
            </p>
          </div>
        )}
      </div>
    </div>
  )
} 