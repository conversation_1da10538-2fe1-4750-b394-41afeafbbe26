import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface SyncRequest {
  change: {
    id: string
    type: string
    operation: 'insert' | 'update' | 'delete'
    data: any
    timestamp: string
    retryCount: number
  }
  timestamp: string
  source: string
}

interface SyncResponse {
  success: boolean
  synced_to: string[]
  failed_apps: string[]
  conflicts: any[]
  message: string
}

serve(async (req) => {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  }

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Parse request body
    const syncRequest: SyncRequest = await req.json()
    
    console.log('🔄 Processing sync request:', syncRequest)

    // Validate the sync request
    if (!syncRequest.change || !syncRequest.source) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          message: 'Invalid sync request format' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const { change, source } = syncRequest
    const response: SyncResponse = {
      success: true,
      synced_to: [],
      failed_apps: [],
      conflicts: [],
      message: ''
    }

    // Log the sync activity
    await logSyncActivity(supabase, change, source)

    // Process the change based on its type
    switch (change.type) {
      case 'restaurant_settings':
        await syncRestaurantSettings(supabase, change, response)
        break
      case 'pos_configurations':
        await syncPOSConfigurations(supabase, change, response)
        break
      case 'app_configurations':
        await syncAppConfigurations(supabase, change, response)
        break
      case 'payment_settings':
        await syncPaymentSettings(supabase, change, response)
        break
      case 'feature_flags':
        await syncFeatureFlags(supabase, change, response)
        break
      default:
        response.success = false
        response.message = `Unknown change type: ${change.type}`
    }

    // Notify connected apps about the change via real-time
    await notifyConnectedApps(supabase, change, source)

    // Update menu synchronization if menu-related changes
    if (change.type === 'restaurant_settings' && change.data.setting_key?.includes('menu')) {
      await updateMenuSynchronization(supabase, change)
    }

    console.log('✅ Sync completed:', response)

    return new Response(
      JSON.stringify(response),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('❌ Sync error:', error)

    return new Response(
      JSON.stringify({ 
        success: false, 
        message: error.message || 'Internal server error' 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

// Log sync activity for audit trail
async function logSyncActivity(supabase: any, change: any, source: string) {
  await supabase
    .from('app_sync_logs')
    .insert({
      source_app: source,
      target_apps: ['customer-web', 'customer-mobile', 'pos-system'],
      change_type: change.type,
      change_data: change.data,
      sync_status: 'processing',
      timestamp: new Date().toISOString()
    })
}

// Sync restaurant settings to all apps
async function syncRestaurantSettings(supabase: any, change: any, response: SyncResponse) {
  try {
    // Apply the change to restaurant_settings table
    let query
    
    switch (change.operation) {
      case 'insert':
        query = supabase.from('restaurant_settings').insert(change.data)
        break
      case 'update':
        query = supabase.from('restaurant_settings')
          .update(change.data)
          .eq('setting_key', change.data.setting_key)
          .eq('category', change.data.category)
        break
      case 'delete':
        query = supabase.from('restaurant_settings')
          .delete()
          .eq('id', change.data.id)
        break
    }

    const { error } = await query

    if (error) {
      throw error
    }

    // Mark as synced to all restaurant apps
    response.synced_to.push('customer-web', 'customer-mobile', 'pos-system')
    response.message = `Restaurant settings synchronized to all apps`

  } catch (error) {
    response.failed_apps.push('customer-web', 'customer-mobile', 'pos-system')
    console.error('❌ Restaurant settings sync failed:', error)
  }
}

// Sync POS configurations
async function syncPOSConfigurations(supabase: any, change: any, response: SyncResponse) {
  try {
    // Apply the change to pos_configurations table
    let query
    
    switch (change.operation) {
      case 'insert':
        query = supabase.from('pos_configurations').insert({
          ...change.data,
          sync_status: 'synced',
          last_sync_at: new Date().toISOString()
        })
        break
      case 'update':
        query = supabase.from('pos_configurations')
          .update({
            ...change.data,
            sync_status: 'synced',
            last_sync_at: new Date().toISOString()
          })
          .eq('terminal_id', change.data.terminal_id)
          .eq('config_key', change.data.config_key)
        break
      case 'delete':
        query = supabase.from('pos_configurations')
          .delete()
          .eq('id', change.data.id)
        break
    }

    const { error } = await query

    if (error) {
      throw error
    }

    // Mark as synced to POS system
    response.synced_to.push('pos-system')
    response.message = `POS configuration synchronized`

    // Trigger push notification to specific terminal if applicable
    if (change.data.terminal_id) {
      await pushToTerminal(supabase, change.data.terminal_id, change)
    }

  } catch (error) {
    response.failed_apps.push('pos-system')
    console.error('❌ POS configuration sync failed:', error)
  }
}

// Sync app configurations
async function syncAppConfigurations(supabase: any, change: any, response: SyncResponse) {
  try {
    // Apply the change to app_configurations table
    let query
    
    switch (change.operation) {
      case 'insert':
        query = supabase.from('app_configurations').insert(change.data)
        break
      case 'update':
        query = supabase.from('app_configurations')
          .update(change.data)
          .eq('app_name', change.data.app_name)
          .eq('config_key', change.data.config_key)
        break
      case 'delete':
        query = supabase.from('app_configurations')
          .delete()
          .eq('id', change.data.id)
        break
    }

    const { error } = await query

    if (error) {
      throw error
    }

    // Mark as synced to specific app
    const targetApp = change.data.app_name
    response.synced_to.push(targetApp)
    response.message = `App configuration synchronized to ${targetApp}`

  } catch (error) {
    response.failed_apps.push(change.data.app_name || 'unknown')
    console.error('❌ App configuration sync failed:', error)
  }
}

// Sync payment settings
async function syncPaymentSettings(supabase: any, change: any, response: SyncResponse) {
  try {
    // Apply the change to payment_settings table
    let query
    
    switch (change.operation) {
      case 'insert':
        query = supabase.from('payment_settings').insert(change.data)
        break
      case 'update':
        query = supabase.from('payment_settings')
          .update(change.data)
          .eq('provider', change.data.provider)
        break
      case 'delete':
        query = supabase.from('payment_settings')
          .delete()
          .eq('id', change.data.id)
        break
    }

    const { error } = await query

    if (error) {
      throw error
    }

    // Payment settings affect all customer-facing apps and POS
    response.synced_to.push('customer-web', 'customer-mobile', 'pos-system')
    response.message = `Payment settings synchronized to all apps`

  } catch (error) {
    response.failed_apps.push('customer-web', 'customer-mobile', 'pos-system')
    console.error('❌ Payment settings sync failed:', error)
  }
}

// Sync feature flags
async function syncFeatureFlags(supabase: any, change: any, response: SyncResponse) {
  try {
    // Apply the change to feature_flags table
    let query
    
    switch (change.operation) {
      case 'insert':
        query = supabase.from('feature_flags').insert(change.data)
        break
      case 'update':
        query = supabase.from('feature_flags')
          .update(change.data)
          .eq('flag_key', change.data.flag_key)
        break
      case 'delete':
        query = supabase.from('feature_flags')
          .delete()
          .eq('id', change.data.id)
        break
    }

    const { error } = await query

    if (error) {
      throw error
    }

    // Feature flags sync to target apps specified in the flag
    const targetApps = change.data.target_apps || ['customer-web', 'customer-mobile', 'pos-system']
    response.synced_to.push(...targetApps)
    response.message = `Feature flags synchronized to ${targetApps.join(', ')}`

  } catch (error) {
    const targetApps = change.data.target_apps || ['customer-web', 'customer-mobile', 'pos-system']
    response.failed_apps.push(...targetApps)
    console.error('❌ Feature flags sync failed:', error)
  }
}

// Notify connected apps via real-time channels
async function notifyConnectedApps(supabase: any, change: any, source: string) {
  try {
    // Send real-time notification
    await supabase
      .channel('settings-sync')
      .send({
        type: 'broadcast',
        event: 'settings_updated',
        payload: {
          change_type: change.type,
          operation: change.operation,
          data: change.data,
          source: source,
          timestamp: new Date().toISOString()
        }
      })

    console.log('📡 Real-time notification sent')
  } catch (error) {
    console.error('❌ Failed to send real-time notification:', error)
  }
}

// Push configuration to specific POS terminal
async function pushToTerminal(supabase: any, terminalId: string, change: any) {
  try {
    // Update terminal sync status
    await supabase
      .from('pos_configurations')
      .update({
        sync_status: 'pushed',
        last_sync_at: new Date().toISOString()
      })
      .eq('terminal_id', terminalId)

    console.log(`📱 Configuration pushed to terminal ${terminalId}`)
  } catch (error) {
    console.error(`❌ Failed to push to terminal ${terminalId}:`, error)
  }
}

// Update menu synchronization status
async function updateMenuSynchronization(supabase: any, change: any) {
  try {
    await supabase
      .from('menu_synchronization')
      .insert({
        trigger_type: 'settings_change',
        sync_status: 'pending',
        apps_to_sync: ['customer-web', 'customer-mobile', 'pos-system'],
        change_details: change.data,
        created_at: new Date().toISOString()
      })

    console.log('🍽️ Menu synchronization triggered')
  } catch (error) {
    console.error('❌ Failed to trigger menu sync:', error)
  }
} 