/**
 * Notification Types for Admin Dashboard
 * Re-exports from shared types with admin-specific extensions
 */

import type {
  NotificationType,
  NotificationPriority,
  UserRole,
  BaseNotification,
  CustomerNotification,
  StaffNotification,
  NotificationPreferences,
  NotificationTemplate,
  PushSubscription
} from '../../../shared/types/notifications';

// Re-export for convenience
export type {
  NotificationType,
  NotificationPriority,
  UserRole,
  BaseNotification,
  CustomerNotification,
  StaffNotification,
  NotificationPreferences,
  NotificationTemplate,
  PushSubscription
};

// Admin-specific notification extensions
export interface AdminNotificationStats {
  total: number;
  unread: number;
  byType: Record<string, number>;
  byPriority: Record<string, number>;
  last24Hours: number;
}

export interface NotificationFilter {
  type?: NotificationType;
  priority?: NotificationPriority;
  read?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  userRole?: UserRole;
}

export interface NotificationAction {
  id: string;
  label: string;
  action: string;
  variant?: 'primary' | 'secondary' | 'danger';
}