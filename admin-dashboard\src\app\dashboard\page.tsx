'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { useTheme } from '@/contexts/theme-context'
import { GlassCard, GlassButton } from '@/components/ui/glass-components'
import { analyticsService, type ComprehensiveAnalytics } from '@/services/analytics-service'
import { 
  TrendingUp, 
  DollarSign, 
  ShoppingCart, 
  Users, 
  Clock, 
  Package, 
  Activity,
  BarChart3,
  Pie<PERSON>hart,
  LineChart,
  RefreshCw,
  AlertTriangle
} from 'lucide-react'
import {
  LineChart as RechartsLineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  Pie<PERSON>hart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts'

export default function DashboardPage() {
  const { user, isLoading, isAuthenticated, logout } = useAuth()
  const { isDarkTheme, toggleTheme } = useTheme()
  const router = useRouter()
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [isProfileOpen, setIsProfileOpen] = useState(false)
  const [isNotificationOpen, setIsNotificationOpen] = useState(false)
  const [analytics, setAnalytics] = useState<ComprehensiveAnalytics | null>(null)
  const [analyticsLoading, setAnalyticsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('7d')
  const [refreshing, setRefreshing] = useState(false)

  const loadAnalytics = async (range: string = timeRange) => {
    try {
      setAnalyticsLoading(true)
      const data = await analyticsService.getComprehensiveAnalytics(range)
      setAnalytics(data)
    } catch (err) {
      console.error('Failed to load analytics:', err)
    } finally {
      setAnalyticsLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await loadAnalytics()
    setRefreshing(false)
  }

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isLoading, isAuthenticated, router])

  useEffect(() => {
    let isSubscribed = true;
    let subscriptionCleanup: (() => void) | null = null;
    
    if (isAuthenticated) {
      loadAnalytics()

      // Set up real-time updates with improved deduplication
      const setupAnalyticsSubscription = async () => {
        try {
          // Add small delay to prevent rapid subscription creation during re-renders
          await new Promise(resolve => setTimeout(resolve, 100));
          
          if (!isSubscribed) return null; // Component unmounted during delay
          
          const subscription = await analyticsService.subscribeToRealTimeUpdates(() => {
            // Only update if component is still mounted
            if (isSubscribed) {
              loadAnalytics()
            }
          })

          return subscription;
        } catch (error) {
          console.error('Error setting up analytics subscription:', error);
          return null;
        }
      };

      setupAnalyticsSubscription().then(subscription => {
        if (subscription && isSubscribed) {
          subscriptionCleanup = () => {
            try {
              analyticsService.cleanupSubscription();
              console.log('Analytics subscription cleaned up');
            } catch (error) {
              console.warn('Error cleaning up analytics subscription:', error);
            }
          };
        }
      });

      return () => {
        isSubscribed = false;
        if (subscriptionCleanup) {
          subscriptionCleanup();
        }
      }
    }
    return undefined
  }, [isAuthenticated])

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center p-4">
        <GlassCard className="p-8 text-center max-w-md w-full">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h2 className="text-lg font-semibold glass-text-primary mb-2">Loading Dashboard</h2>
          <p className="glass-text-secondary">Please wait while we load your analytics data...</p>
        </GlassCard>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // Will redirect to login
  }

  const handleLogout = async () => {
    await logout()
    router.push('/auth/login')
  }

  return (
      <div className="p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className={`text-3xl font-bold tracking-tight transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>Dashboard</h1>
          <p className={`mt-2 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/80' : 'text-black/80'
          }`}>
            Welcome to your creperie management dashboard. Monitor operations, track performance,
            and manage your business.
          </p>
        </div>

        {/* Analytics Controls */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <select
              value={timeRange}
              onChange={(e) => {
                setTimeRange(e.target.value)
                loadAnalytics(e.target.value)
              }}
              className={`px-4 py-2 rounded-lg border transition-all duration-300 ${
                isDarkTheme 
                  ? 'bg-white/10 border-white/20 text-white' 
                  : 'bg-black/10 border-black/20 text-black'
              }`}
            >
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 ${
                isDarkTheme 
                  ? 'bg-white/10 border-white/20 text-white hover:bg-white/20' 
                  : 'bg-black/10 border-black/20 text-black hover:bg-black/20'
              }`}
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
          {analytics && (
            <p className={`text-sm ${isDarkTheme ? 'text-white/60' : 'text-black/60'}`}>
              Last updated: {new Date(analytics.lastUpdated).toLocaleString()}
            </p>
          )}
        </div>

        {/* Real-time Metrics */}
        {analyticsLoading ? (
          <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 mb-6 sm:mb-8">
            {[...Array(6)].map((_, i) => (
              <GlassCard key={i} className="p-4 sm:p-6 animate-pulse hover:scale-105 transition-all duration-500">
                <div className="h-4 bg-white/20 rounded mb-2 animate-pulse"></div>
                <div className="h-6 sm:h-8 bg-white/20 rounded mb-2 animate-pulse"></div>
                <div className="h-3 bg-white/20 rounded animate-pulse"></div>
              </GlassCard>
            ))}
          </div>
        ) : analytics ? (
          <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 mb-6 sm:mb-8">
            <GlassCard variant="primary" className="p-4 sm:p-6 group hover:scale-105 transition-all duration-500 ease-out cursor-pointer">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-xs sm:text-sm font-medium glass-text-primary group-hover:text-green-400 transition-colors duration-300">Total Revenue</h3>
                <DollarSign className="h-4 w-4 sm:h-5 sm:w-5 text-green-400 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500" />
              </div>
              <div className="text-xl sm:text-2xl font-bold glass-text-primary group-hover:scale-110 transition-all duration-300">
                €{analytics.sales.totalRevenue.toLocaleString()}
              </div>
              <p className={`text-xs flex items-center transition-all duration-300 ${
                analytics.sales.revenueGrowth >= 0 ? 'text-green-400' : 'text-red-400'
              }`}>
                <TrendingUp className="h-3 w-3 mr-1 group-hover:animate-bounce" />
                {analytics.sales.revenueGrowth > 0 ? '+' : ''}{analytics.sales.revenueGrowth.toFixed(1)}%
              </p>
            </GlassCard>

            <GlassCard variant="success" className="p-4 sm:p-6 group hover:scale-105 transition-all duration-500 ease-out cursor-pointer">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-xs sm:text-sm font-medium glass-text-primary group-hover:text-blue-400 transition-colors duration-300">Total Orders</h3>
                <ShoppingCart className="h-4 w-4 sm:h-5 sm:w-5 text-blue-400 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500" />
              </div>
              <div className="text-xl sm:text-2xl font-bold glass-text-primary group-hover:scale-110 transition-all duration-300">
                {analytics.sales.totalOrders.toLocaleString()}
              </div>
              <p className={`text-xs flex items-center transition-all duration-300 ${
                analytics.sales.orderGrowth >= 0 ? 'text-green-400' : 'text-red-400'
              }`}>
                <TrendingUp className="h-3 w-3 mr-1 group-hover:animate-bounce" />
                {analytics.sales.orderGrowth > 0 ? '+' : ''}{analytics.sales.orderGrowth.toFixed(1)}%
              </p>
            </GlassCard>

            <GlassCard variant="info" className="p-6 group hover:scale-105 transition-all duration-500 ease-out cursor-pointer">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium glass-text-primary group-hover:text-purple-400 transition-colors duration-300">Active Customers</h3>
                <Users className="h-5 w-5 text-purple-400 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500" />
              </div>
              <div className="text-2xl font-bold glass-text-primary group-hover:scale-110 transition-all duration-300">
                {analytics.customers.totalCustomers.toLocaleString()}
              </div>
              <p className="text-xs text-green-400 group-hover:animate-pulse transition-all duration-300">
                +{analytics.customers.newCustomers} new
              </p>
            </GlassCard>

            <GlassCard variant="warning" className="p-6">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium glass-text-primary">Avg Order Value</h3>
                <TrendingUp className="h-5 w-5 text-orange-400" />
              </div>
              <div className="text-2xl font-bold glass-text-primary">
                €{analytics.sales.avgOrderValue.toFixed(2)}
              </div>
              <p className="text-xs text-green-400">+2.3%</p>
            </GlassCard>

            <GlassCard variant="secondary" className="p-6">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium glass-text-primary">Avg Prep Time</h3>
                <Clock className="h-5 w-5 text-cyan-400" />
              </div>
              <div className="text-2xl font-bold glass-text-primary">
                {analytics.operations.avgPreparationTime.toFixed(1)} min
              </div>
              <p className="text-xs text-green-400">-0.5 min</p>
            </GlassCard>

            <GlassCard variant="info" className="p-6">
              <div className="flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium glass-text-primary">System Uptime</h3>
                <Activity className="h-5 w-5 text-emerald-400" />
              </div>
              <div className="text-2xl font-bold glass-text-primary">
                {analytics.systemHealth.uptime.toFixed(1)}%
              </div>
              <p className={`text-xs ${
                analytics.systemHealth.systemAlerts.length === 0 ? 'text-green-400' : 'text-red-400'
              }`}>
                {analytics.systemHealth.systemAlerts.length} alerts
              </p>
            </GlassCard>
          </div>
        ) : (
          <div className="grid gap-3 sm:gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 mb-6 sm:mb-8">
            <GlassCard className="p-6">
              <div className="flex items-center justify-center h-20">
                <AlertTriangle className="h-8 w-8 text-red-400" />
              </div>
              <p className="text-center text-red-400">Failed to load analytics</p>
            </GlassCard>
          </div>
        )}

        {/* Analytics Charts */}
        {analytics && (
          <div className="grid gap-6 mb-8">
            {/* Sales Trend Chart */}
            <GlassCard className="p-6 glass-hover-lift group">
              <div className="mb-4">
                <h3 className="text-lg font-semibold glass-text-primary">Sales Trend</h3>
                <p className="glass-text-secondary">Revenue and orders over time</p>
              </div>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={analytics.sales.salesByDay}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    {React.createElement(XAxis as any, {
                      dataKey: "date", 
                      stroke: "rgba(255,255,255,0.8)",
                      tickFormatter: (value: string) => new Date(value).toLocaleDateString()
                    })}
                    {React.createElement(YAxis as any, { stroke: "rgba(255,255,255,0.8)" })}
                    {React.createElement(Tooltip as any, {
                      contentStyle: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                      }
                    })}
                    {React.createElement(Area as any, {
                      type: "monotone",
                      dataKey: "revenue",
                      stroke: "#8884d8",
                      fill: "url(#colorRevenue)",
                      strokeWidth: 2
                    })}
                    <defs>
                      <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                        <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                      </linearGradient>
                    </defs>
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </GlassCard>

            {/* Peak Hours & Payment Methods */}
            <div className="grid gap-6 md:grid-cols-2">
              <GlassCard className="p-6">
                <div className="mb-4">
                  <h3 className="text-lg font-semibold glass-text-primary">Peak Hours</h3>
                  <p className="glass-text-secondary">Order volume by hour</p>
                </div>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={analytics.operations.peakHours}>
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                      {React.createElement(XAxis as any, {
                        dataKey: "hour", 
                        stroke: "rgba(255,255,255,0.8)",
                        tickFormatter: (value: string | number) => `${value}:00`
                      })}
                      {React.createElement(YAxis as any, { stroke: "rgba(255,255,255,0.8)" })}
                      {React.createElement(Tooltip as any, {
                        contentStyle: {
                          backgroundColor: 'rgba(0,0,0,0.8)',
                          border: '1px solid rgba(255,255,255,0.2)',
                          borderRadius: '8px',
                        },
                        labelFormatter: (value: string | number) => `${value}:00`
                      })}
                      {React.createElement(Bar as any, { dataKey: "orderCount", fill: "#82ca9d" })}
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </GlassCard>

              <GlassCard className="p-6">
                <div className="mb-4">
                  <h3 className="text-lg font-semibold glass-text-primary">Payment Methods</h3>
                  <p className="glass-text-secondary">Payment distribution</p>
                </div>
                <div className="space-y-4">
                  {analytics.sales.paymentMethodBreakdown.map((method, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="font-medium glass-text-primary">{method.method}</p>
                        <div className="w-full bg-white/20 rounded-full h-2 mt-1">
                          <div 
                            className="bg-blue-400 h-2 rounded-full" 
                            style={{ width: `${method.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="text-right ml-4">
                        <p className="font-medium glass-text-primary">{method.count}</p>
                        <p className="text-sm glass-text-secondary">{method.percentage.toFixed(1)}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </GlassCard>
            </div>

            {/* System Health & Inventory Status */}
            <div className="grid gap-6 md:grid-cols-2">
              <GlassCard className="p-6">
                <div className="mb-4">
                  <h3 className="text-lg font-semibold glass-text-primary">System Health</h3>
                  <p className="glass-text-secondary">Real-time system monitoring</p>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="glass-text-primary">API Response Time</span>
                    <span className="glass-text-primary">{analytics.systemHealth.apiResponseTime}ms</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="glass-text-primary">Database Connections</span>
                    <span className="glass-text-primary">{analytics.systemHealth.databaseConnections}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="glass-text-primary">Active Users</span>
                    <span className="glass-text-primary">
                      {Object.values(analytics.systemHealth.activeUsers).reduce((a, b) => a + b, 0)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="glass-text-primary">Error Rate</span>
                    <span className={`${analytics.systemHealth.errorRate > 5 ? 'text-red-400' : 'text-green-400'}`}>
                      {analytics.systemHealth.errorRate.toFixed(2)}%
                    </span>
                  </div>
                </div>
              </GlassCard>

              <GlassCard className="p-6">
                <div className="mb-4">
                  <h3 className="text-lg font-semibold glass-text-primary">Inventory Status</h3>
                  <p className="glass-text-secondary">Stock levels overview</p>
                </div>
                <div className="space-y-4">
                  {analytics.inventory.stockLevels.slice(0, 5).map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="font-medium glass-text-primary">{item.ingredient}</p>
                        <div className="w-full bg-white/20 rounded-full h-2 mt-1">
                          <div 
                            className={`h-2 rounded-full ${
                              item.status === 'critical' ? 'bg-red-400' :
                              item.status === 'low' ? 'bg-yellow-400' : 'bg-green-400'
                            }`}
                            style={{ width: `${(item.currentStock / item.maxStock) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="text-right ml-4">
                        <p className="font-medium glass-text-primary">{item.currentStock}</p>
                        <p className={`text-sm ${
                          item.status === 'critical' ? 'text-red-400' :
                          item.status === 'low' ? 'text-yellow-400' : 'text-green-400'
                        }`}>
                          {item.status}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </GlassCard>
            </div>
          </div>
        )}

        {/* Recent Activity */}
        <div className="grid gap-4 md:grid-cols-2">
          <GlassCard variant="primary" className="p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold glass-text-primary">Recent Orders</h3>
              <p className="glass-text-secondary">Latest orders from all locations</p>
            </div>
            <div>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium glass-text-primary">#ORD-001</p>
                    <p className="text-sm glass-text-secondary">Nutella Crepe + Coffee</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium glass-text-primary">€8.50</p>
                    <p className="text-sm glass-text-secondary">2 min ago</p>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium glass-text-primary">#ORD-002</p>
                    <p className="text-sm glass-text-secondary">Savory Ham & Cheese</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium glass-text-primary">€7.20</p>
                    <p className="text-sm glass-text-secondary">5 min ago</p>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium glass-text-primary">#ORD-003</p>
                    <p className="text-sm glass-text-secondary">Strawberry & Cream</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium glass-text-primary">€6.80</p>
                    <p className="text-sm glass-text-secondary">8 min ago</p>
                  </div>
                </div>
              </div>
            </div>
          </GlassCard>

          <GlassCard variant="secondary" className="p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold glass-text-primary">Quick Actions</h3>
              <p className="glass-text-secondary">Common management tasks</p>
            </div>
            <div className="grid gap-2">
              <GlassButton variant="primary" size="medium" className="justify-start">
                📋 View All Orders
              </GlassButton>
              <GlassButton variant="primary" size="medium" className="justify-start">
                🍽️ Manage Subcategories
              </GlassButton>
              <GlassButton variant="primary" size="medium" className="justify-start">
                📊 View Analytics
              </GlassButton>
              <GlassButton variant="primary" size="medium" className="justify-start">
                🏪 Manage Locations
              </GlassButton>
              <GlassButton variant="primary" size="medium" className="justify-start">
                👥 Staff Management
              </GlassButton>
            </div>
          </GlassCard>
        </div>
      </div>
  )
}
