﻿// Password Security Service
import { createHash } from 'crypto'

interface PasswordStrengthResult {
  score: number
  strength: 'very-weak' | 'weak' | 'fair' | 'good' | 'strong' | 'very-strong'
  feedback: string[]
  estimatedCrackTime: string
  isBreached: boolean
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
}

interface PolicyViolation {
  rule: string
  message: string
  severity: 'error' | 'warning' | 'info'
}

interface SecurityQuestion {
  id: string
  question: string
  category: 'personal' | 'historical' | 'preferences' | 'custom'
  difficulty: 'easy' | 'medium' | 'hard'
}

class PasswordSecurityService {
  private commonPasswords = new Set([
    'password', '123456', '123456789', 'qwerty', 'abc123', 'password123'
  ])

  async assessPasswordStrength(password: string): Promise<PasswordStrengthResult> {
    let score = 0
    const feedback: string[] = []

    if (password.length >= 12) score += 30
    if (/[a-z]/.test(password)) score += 15
    if (/[A-Z]/.test(password)) score += 15
    if (/\d/.test(password)) score += 15
    if (/[!@#$%^&*]/.test(password)) score += 15

    const strength = score >= 80 ? 'strong' : score >= 60 ? 'good' : 'weak'
    
    return {
      score,
      strength,
      feedback,
      estimatedCrackTime: 'Years',
      isBreached: false,
      riskLevel: 'low'
    }
  }

  async validatePassword(password: string): Promise<{ isValid: boolean; violations: PolicyViolation[] }> {
    const violations: PolicyViolation[] = []
    
    if (password.length < 8) {
      violations.push({
        rule: 'minLength',
        message: 'Password must be at least 8 characters',
        severity: 'error'
      })
    }

    return {
      isValid: violations.length === 0,
      violations
    }
  }

  generateSecurePassword(length: number = 16): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
    let password = ''
    for (let i = 0; i < length; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return password
  }

  generatePassphrase(wordCount: number = 4): string {
    const words = ['apple', 'banana', 'cherry', 'dragon', 'elephant']
    const selected = []
    for (let i = 0; i < wordCount; i++) {
      selected.push(words[Math.floor(Math.random() * words.length)])
    }
    return selected.join('') + '123!'
  }

  getSecurityQuestions(): SecurityQuestion[] {
    return [
      {
        id: 'sq_001',
        question: 'What was the name of your first pet?',
        category: 'personal',
        difficulty: 'easy'
      }
    ]
  }

  async initiatePasswordRecovery(email: string, method: string): Promise<{
    success: boolean
    token?: string
    message: string
  }> {
    // Simulate password recovery initiation
    return {
      success: true,
      token: 'recovery_' + Math.random().toString(36).substr(2, 9),
      message: 'Recovery email sent successfully'
    }
  }

  async verifyRecoveryToken(token: string): Promise<{
    isValid: boolean
    message: string
  }> {
    // Simulate token verification
    return {
      isValid: token.startsWith('recovery_'),
      message: token.startsWith('recovery_') ? 'Token is valid' : 'Invalid token'
    }
  }
}

export const passwordSecurityService = new PasswordSecurityService()
export type { PasswordStrengthResult, PolicyViolation, SecurityQuestion }
