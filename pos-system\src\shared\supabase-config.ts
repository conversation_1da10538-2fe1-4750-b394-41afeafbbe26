/**
 * Centralized Supabase Configuration for POS System
 * Ensures consistent configuration across all services
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { getSupabaseConfig } from '../../../shared/config/supabase-config';

// Get configuration from shared config to ensure consistency
const config = getSupabaseConfig('desktop');
export const SUPABASE_CONFIG = {
  url: config.url,
  anonKey: config.anonKey,
} as const;

// Validate configuration
if (!SUPABASE_CONFIG.url || !SUPABASE_CONFIG.anonKey) {
  throw new Error('Missing required Supabase environment variables: NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY');
}

let supabaseClient: SupabaseClient | null = null;

/**
 * Get or create the singleton Supabase client instance
 */
export function getSupabaseClient(): SupabaseClient {
  if (!supabaseClient) {
    supabaseClient = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.anonKey, {
      ...config.options,
      global: {
        headers: {
          'x-application-name': 'pos-system',
        }
      }
    });
  }
  
  return supabaseClient;
}

/**
 * Test Supabase connection
 */
export async function testSupabaseConnection(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const client = getSupabaseClient();
    const { data, error } = await client
      .from('orders')
      .select('count')
      .limit(1);
    
    if (error) {
      return {
        success: false,
        error: `Supabase connection test failed: ${error.message}`,
      };
    }
    
    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: `Supabase connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}

/**
 * Helper function to handle Supabase errors consistently
 */
export const handleSupabaseError = (error: any): string => {
  console.error('Supabase error:', error);

  if (error?.code === 'PGRST301') {
    return 'Resource not found';
  }

  if (error?.code === 'PGRST116') {
    return 'Invalid request parameters';
  }

  if (error?.code === '23505') {
    return 'This record already exists';
  }

  if (error?.code === '23503') {
    return 'Cannot delete this record as it is referenced by other data';
  }

  return error?.message || 'An unexpected error occurred';
};

export default getSupabaseClient; 