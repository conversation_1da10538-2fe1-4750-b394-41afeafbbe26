'use client';

import React, { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { ShoppingCart, Truck, Store } from 'lucide-react';

interface Ingredient {
  id: string;
  name: string;
  description: string;
  price: number;
  pickup_price: number;
  delivery_price: number;
  category_id: string;
  image_url?: string;
  is_available: boolean;
}

const IngredientPricingDemo: React.FC = () => {
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [orderType, setOrderType] = useState<'pickup' | 'delivery'>('pickup');
  const [selectedIngredients, setSelectedIngredients] = useState<{[key: string]: number}>({});
  const [loading, setLoading] = useState(true);

  const supabase = createClientComponentClient();

  useEffect(() => {
    loadIngredients();
  }, []);

  const loadIngredients = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('ingredients')
        .select('*')
        .eq('is_available', true)
        .limit(6)
        .order('display_order');

      if (error) throw error;
      setIngredients(data || []);
    } catch (error) {
      console.error('Error loading ingredients:', error);
    } finally {
      setLoading(false);
    }
  };

  const getIngredientPrice = (ingredient: Ingredient): number => {
    return orderType === 'pickup' ? ingredient.pickup_price : ingredient.delivery_price;
  };

  const updateQuantity = (ingredientId: string, change: number) => {
    setSelectedIngredients(prev => {
      const current = prev[ingredientId] || 0;
      const newQuantity = Math.max(0, current + change);
      if (newQuantity === 0) {
        const { [ingredientId]: removed, ...rest } = prev;
        return rest;
      }
      return { ...prev, [ingredientId]: newQuantity };
    });
  };

  const getTotalPrice = () => {
    return Object.entries(selectedIngredients).reduce((total, [ingredientId, quantity]) => {
      const ingredient = ingredients.find(i => i.id === ingredientId);
      if (!ingredient) return total;
      return total + (getIngredientPrice(ingredient) * quantity);
    }, 0);
  };

  const getPriceDifference = () => {
    return Object.entries(selectedIngredients).reduce((total, [ingredientId, quantity]) => {
      const ingredient = ingredients.find(i => i.id === ingredientId);
      if (!ingredient) return total;
      const pickupPrice = ingredient.pickup_price * quantity;
      const deliveryPrice = ingredient.delivery_price * quantity;
      return total + (deliveryPrice - pickupPrice);
    }, 0);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Pickup vs Delivery Ingredient Pricing
        </h1>
        <p className="text-gray-600">
          See how ingredient prices differ between pickup and delivery orders
        </p>
      </div>

      {/* Order Type Toggle */}
      <div className="flex justify-center mb-8">
        <div className="bg-gray-100 p-1 rounded-lg flex">
          <button
            onClick={() => setOrderType('pickup')}
            className={`flex items-center px-6 py-3 rounded-md transition-all ${
              orderType === 'pickup'
                ? 'bg-blue-600 text-white shadow-md'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Store className="h-5 w-5 mr-2" />
            Pickup
          </button>
          <button
            onClick={() => setOrderType('delivery')}
            className={`flex items-center px-6 py-3 rounded-md transition-all ${
              orderType === 'delivery'
                ? 'bg-green-600 text-white shadow-md'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Truck className="h-5 w-5 mr-2" />
            Delivery
          </button>
        </div>
      </div>

      {/* Ingredients Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {ingredients.map(ingredient => {
          const quantity = selectedIngredients[ingredient.id] || 0;
          const currentPrice = getIngredientPrice(ingredient);
          const otherPrice = orderType === 'pickup' ? ingredient.delivery_price : ingredient.pickup_price;
          const priceDiff = Math.abs(currentPrice - otherPrice);

          return (
            <div key={ingredient.id} className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
              <h3 className="font-semibold text-gray-900 mb-2">{ingredient.name}</h3>
              <p className="text-sm text-gray-600 mb-4">{ingredient.description}</p>
              
              <div className="space-y-2 mb-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Pickup:</span>
                  <span className={`font-medium ${orderType === 'pickup' ? 'text-blue-600' : 'text-gray-400'}`}>
                    ${ingredient.pickup_price.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Delivery:</span>
                  <span className={`font-medium ${orderType === 'delivery' ? 'text-green-600' : 'text-gray-400'}`}>
                    ${ingredient.delivery_price.toFixed(2)}
                  </span>
                </div>
                {priceDiff > 0 && (
                  <div className="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
                    ${priceDiff.toFixed(2)} difference
                  </div>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="text-lg font-bold text-gray-900">
                  ${currentPrice.toFixed(2)}
                  <span className="text-sm font-normal text-gray-500 ml-1">each</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => updateQuantity(ingredient.id, -1)}
                    disabled={quantity === 0}
                    className="w-8 h-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center hover:bg-red-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    -
                  </button>
                  <span className="w-8 text-center font-medium">{quantity}</span>
                  <button
                    onClick={() => updateQuantity(ingredient.id, 1)}
                    className="w-8 h-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center hover:bg-green-200 transition-colors"
                  >
                    +
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Cart Summary */}
      {Object.keys(selectedIngredients).length > 0 && (
        <div className="bg-gray-50 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
          
          <div className="space-y-2 mb-4">
            {Object.entries(selectedIngredients).map(([ingredientId, quantity]) => {
              const ingredient = ingredients.find(i => i.id === ingredientId);
              if (!ingredient) return null;
              
              return (
                <div key={ingredientId} className="flex justify-between items-center">
                  <span className="text-gray-700">
                    {ingredient.name} × {quantity}
                  </span>
                  <span className="font-medium">
                    ${(getIngredientPrice(ingredient) * quantity).toFixed(2)}
                  </span>
                </div>
              );
            })}
          </div>

          <div className="border-t border-gray-200 pt-4">
            <div className="flex justify-between items-center text-lg font-bold">
              <span>Total ({orderType}):</span>
              <span className={orderType === 'pickup' ? 'text-blue-600' : 'text-green-600'}>
                ${getTotalPrice().toFixed(2)}
              </span>
            </div>
            
            {getPriceDifference() !== 0 && (
              <div className="text-sm text-gray-600 mt-2">
                {getPriceDifference() > 0 ? (
                  <span className="text-orange-600">
                    +${getPriceDifference().toFixed(2)} more than pickup
                  </span>
                ) : (
                  <span className="text-green-600">
                    ${Math.abs(getPriceDifference()).toFixed(2)} less than delivery
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default IngredientPricingDemo;