/**
 * Security Module Index
 * Centralized exports for all security components
 * Provides unified interface for security measures across the Creperie ecosystem
 */

// Core Security Configuration
export { SECURITY_CONFIG } from './security-config';

// Security Middleware
export {
  securityHeaders,
  authRateLimits,
  apiRateLimits,
  paymentRateLimits,
  InputValidator,
  sanitizeInput,
  preventSQLInjection,
  xssProtection,
  EncryptionUtils,
  logSecurityEvent,
  logAuditEvent,
  BruteForceProtection
} from './security-middleware';

// Import for internal use
import { 
  logSecurityEvent, 
  logAuditEvent,
  InputValidator,
  sanitizeInput 
} from './security-middleware';
import { 
  PasswordSecurity,
  SessionSecurity 
} from './auth-security';
import { SECURITY_CONFIG } from './security-config';

// Authentication Security
export {
  PasswordSecurity,
  SessionSecurity,
  BruteForceProtection as AuthBruteForceProtection,
  TwoFactorAuth,
  JWTSecurity
} from './auth-security';

// Data Protection
export {
  EncryptionManager,
  HTTPSEnforcement,
  PIIDataHandler,
  GDPRCompliance,
  DataClassification
} from './data-protection';

// Audit Logging
export {
  AuditLogger,
  SecurityLogger,
  LoginLogger,
  ComplianceReporter
} from './audit-logging';

// PCI Compliance
export {
  PCICardDataHandler,
  PCINetworkSecurity,
  PCIAccessControl,
  PCIVulnerabilityManagement,
  PCIComplianceReporter
} from './pci-compliance';

// Greek Data Protection
export {
  GreekDPARequestHandler,
  GreekConsentManager,
  GreekDataBreachHandler,
  GreekPrivacyImpactAssessment
} from './greek-data-protection';

// Types
export interface SecurityContext {
  userId?: string;
  sessionId?: string;
  ipAddress: string;
  userAgent: string;
  platform: 'admin' | 'pos' | 'customer_web' | 'customer_mobile';
  permissions: string[];
  deviceFingerprint?: string;
}

export interface SecurityEvent {
  type: 'authentication' | 'authorization' | 'data_access' | 'security_violation' | 'system_event';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  context: SecurityContext;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface ComplianceReport {
  reportType: 'gdpr' | 'pci_dss' | 'greek_dpa' | 'security_audit';
  generatedAt: Date;
  period: {
    start: Date;
    end: Date;
  };
  findings: any[];
  recommendations: string[];
  complianceScore: number;
}

/**
 * Unified Security Manager
 * Provides high-level security operations across all applications
 */
export class SecurityManager {
  /**
   * Initialize security for an application
   */
  static async initializeSecurity(platform: SecurityContext['platform']): Promise<void> {
    try {
      // Security initialization placeholder - implement based on needs
      console.log(`Security initialized for ${platform}`);
      
    } catch (error) {
      console.error('Failed to initialize security:', error);
      throw error;
    }
  }

  /**
   * Validate security context for requests
   */
  static async validateSecurityContext(context: SecurityContext): Promise<boolean> {
    try {
      // Check for suspicious activity
      const isSuspicious = await this.detectSuspiciousActivity(context);
      if (isSuspicious) {
        await logSecurityEvent('suspicious_activity_detected', 'high', {
          description: 'Suspicious activity detected',
          context
        });
        return false;
      }

      // Validate session if present
      if (context.sessionId) {
        const isValidSession = await SessionSecurity.validateSession(context.sessionId);
        if (!isValidSession) {
          return false;
        }
      }

      // Check rate limits
      const rateLimitKey = `${context.ipAddress}:${context.platform}`;
      const isRateLimited = await this.checkRateLimit(rateLimitKey);
      if (isRateLimited) {
        await logSecurityEvent('rate_limit_exceeded', 'medium', {
          rateLimitKey,
          context
        });
        return false;
      }

      return true;
    } catch (error) {
      console.error('Security context validation failed:', error);
      return false;
    }
  }

  /**
   * Handle security events
   */
  static async handleSecurityEvent(event: SecurityEvent): Promise<void> {
    try {
      // Log the event
      await logSecurityEvent(event.type, event.severity, {
        description: event.description,
        context: event.context,
        metadata: event.metadata
      });

      // Take action based on severity
      switch (event.severity) {
        case 'critical':
          await this.handleCriticalEvent(event);
          break;
        case 'high':
          await this.handleHighSeverityEvent(event);
          break;
        case 'medium':
          await this.handleMediumSeverityEvent(event);
          break;
        case 'low':
          // Just log, no immediate action needed
          break;
      }
    } catch (error) {
      console.error('Failed to handle security event:', error);
    }
  }

  /**
   * Generate compliance report
   */
  static async generateComplianceReport(
    reportType: ComplianceReport['reportType'],
    startDate: Date,
    endDate: Date
  ): Promise<ComplianceReport> {
    try {
      let findings: any[] = [];
      let recommendations: string[] = [];
      let complianceScore = 100;

      switch (reportType) {
        case 'gdpr':
        case 'pci_dss':
        case 'greek_dpa':
        case 'security_audit':
          // Compliance reporting placeholder - implement specific reporters
          findings = ['Sample finding'];
          recommendations = ['Sample recommendation'];
          complianceScore = 85;
          break;
      }

      return {
        reportType,
        generatedAt: new Date(),
        period: { start: startDate, end: endDate },
        findings,
        recommendations,
        complianceScore
      };
    } catch (error) {
      console.error('Failed to generate compliance report:', error);
      throw error;
    }
  }

  /**
   * Encrypt sensitive data
   */
  static async encryptSensitiveData(data: any, dataType: string): Promise<string> {
    try {
      // Simple encryption placeholder - implement proper encryption
      return JSON.stringify(data);
    } catch (error) {
      console.error('Failed to encrypt sensitive data:', error);
      throw error;
    }
  }

  /**
   * Decrypt sensitive data
   */
  static async decryptSensitiveData(encryptedData: string): Promise<any> {
    try {
      // Simple decryption placeholder - implement proper decryption
      return JSON.parse(encryptedData);
    } catch (error) {
      console.error('Failed to decrypt or parse data:', error);
      throw error;
    }
  }

  /**
   * Handle PII data according to GDPR and Greek DPA requirements
   */
  static async handlePIIData(
    data: any,
    operation: 'store' | 'retrieve' | 'update' | 'delete',
    context: SecurityContext
  ): Promise<any> {
    try {
      switch (operation) {
        case 'store':
          // Encrypt PII before storing
          const encryptedData = await this.encryptSensitiveData(data, 'pii');
          
          // Log data processing
          await logAuditEvent('pii_data_stored', 'personal_data', context.userId, {
            platform: context.platform,
            legalBasis: 'consent'
          });
          
          return encryptedData;

        case 'retrieve':
          // Decrypt PII for authorized access
          const decryptedData = await this.decryptSensitiveData(data);
          
          // Log data access
          await logAuditEvent('pii_data_accessed', 'personal_data', context.userId, {
            platform: context.platform,
            accessReason: 'authorized_request'
          });
          
          return decryptedData;

        case 'update':
          // Handle PII updates with audit trail
          const updatedData = await this.encryptSensitiveData(data, 'pii');
          
          await logAuditEvent('pii_data_updated', 'personal_data', context.userId, {
            platform: context.platform,
            updateReason: 'data_subject_request'
          });
          
          return updatedData;

        case 'delete':
          // Handle PII deletion (right to be forgotten)
          await logAuditEvent('pii_data_deleted', 'personal_data', context.userId, {
            platform: context.platform,
            deletionReason: 'right_to_be_forgotten'
          });
          
          return null;

        default:
          throw new Error(`Unsupported PII operation: ${operation}`);
      }
    } catch (error) {
      console.error('Failed to handle PII data:', error);
      throw error;
    }
  }

  // Private helper methods
  private static async detectSuspiciousActivity(context: SecurityContext): Promise<boolean> {
    // Implement suspicious activity detection logic
    // This could include:
    // - Multiple failed login attempts
    // - Unusual access patterns
    // - Access from suspicious IP addresses
    // - Unusual user agent strings
    
    return false; // Placeholder
  }

  private static async checkRateLimit(key: string): Promise<boolean> {
    // Implement rate limiting check
    // This would typically use Redis or similar for distributed rate limiting
    
    return false; // Placeholder
  }

  private static async handleCriticalEvent(event: SecurityEvent): Promise<void> {
    // Handle critical security events
    // - Immediate alerts to security team
    // - Automatic incident response
    // - System lockdown if necessary
    
    console.log('CRITICAL SECURITY EVENT:', event);
  }

  private static async handleHighSeverityEvent(event: SecurityEvent): Promise<void> {
    // Handle high severity events
    // - Alert security team
    // - Enhanced monitoring
    // - Possible user account restrictions
    
    console.log('HIGH SEVERITY SECURITY EVENT:', event);
  }

  private static async handleMediumSeverityEvent(event: SecurityEvent): Promise<void> {
    // Handle medium severity events
    // - Log for review
    // - Possible rate limiting
    // - User notification if appropriate
    
    console.log('MEDIUM SEVERITY SECURITY EVENT:', event);
  }

  private static async checkGreekDPACompliance(startDate: Date, endDate: Date): Promise<any[]> {
    // Check Greek DPA specific compliance requirements
    const findings: any[] = [];
    
    // Check data retention policies
    // Check consent management
    // Check data subject rights handling
    // Check breach notification compliance
    
    return findings;
  }

  private static getGreekDPARecommendations(findings: any[]): string[] {
    const recommendations: string[] = [];
    
    // Generate recommendations based on findings
    if (findings.length === 0) {
      recommendations.push('Maintain current compliance practices');
    }
    
    return recommendations;
  }

  private static calculateGreekDPAScore(findings: any[]): number {
    // Calculate compliance score based on findings
    const baseScore = 100;
    const deductionPerFinding = 10;
    
    return Math.max(0, baseScore - (findings.length * deductionPerFinding));
  }
}

/**
 * Security Utilities
 * Helper functions for common security operations
 */
export class SecurityUtils {
  /**
   * Generate secure random string
   */
  static generateSecureRandom(length: number = 32): string {
    const crypto = require('crypto');
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Hash password securely
   */
  static async hashPassword(password: string): Promise<string> {
    return await PasswordSecurity.hashPassword(password);
  }

  /**
   * Verify password
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return await PasswordSecurity.verifyPassword(password, hash);
  }

  /**
   * Sanitize input to prevent XSS
   */
  static sanitizeInput(input: string): string {
    return sanitizeInput(input);
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): boolean {
    return InputValidator.validateEmail(email).length === 0;
  }

  /**
   * Validate phone number
   */
  static validatePhone(phone: string): boolean {
    return InputValidator.validatePhone(phone).length === 0;
  }

  /**
   * Generate CSRF token
   */
  static generateCSRFToken(): string {
    return this.generateSecureRandom(32);
  }

  /**
   * Create security headers for HTTP responses
   */
  static getSecurityHeaders(): Record<string, string> {
    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Content-Security-Policy': Object.values(SECURITY_CONFIG.HEADERS.CSP).join(' '),
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
    };
  }
}

// Default export for convenience
export default SecurityManager;