#!/usr/bin/env node

// Comprehensive test suite for menu management functionality
const { createClient } = require('@supabase/supabase-js');

// Configuration
const supabaseUrl = 'https://voiwzwyfnkzvcffuxpwl.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0MDU2MzIsImV4cCI6MjA2NDk4MTYzMn0.RvabQ6EGP1gBb0w8J8_VpaqqXgkpEFR7Nbbkmw11wNA';

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
  global: {
    headers: {
      'x-application-name': 'admin-dashboard-test',
      'x-client-id': `test-${Date.now()}`,
    }
  }
});

// Test data
const testData = {
  category: {
    name: 'Test Category',
    description: 'Test category for automated testing',
    category_type: 'standard',
    display_order: 999,
    is_active: true,
    is_featured: false
  },
  ingredientCategory: {
    name: 'Test Ingredient Category',
    description: 'Test ingredient category for automated testing',
    color_code: '#FF0000',
    display_order: 999,
    is_active: true
  },
  ingredient: {
    name: 'Test Ingredient',
    description: 'Test ingredient for automated testing',
    price: 2.50,
    cost: 1.00,
    stock_quantity: 100,
    min_stock_level: 10,
    is_available: true,
    display_order: 999
  },
  menuItem: {
    name: 'Test Menu Item',
    description: 'Test menu item for automated testing',
    base_price: 12.99,
    cost: 6.50,
    preparation_time: 15,
    calories: 350,
    is_available: true,
    is_featured: false,
    is_customizable: true,
    max_ingredients: 5,
    display_order: 999
  }
};

// Test results tracking
const testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

function logTest(testName, success, error = null) {
  testResults.total++;
  if (success) {
    testResults.passed++;
    console.log(`✅ ${testName}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: ${error?.message || error}`);
    testResults.errors.push({ test: testName, error: error?.message || error });
  }
}

// Test environment and access
async function testEnvironmentAccess() {
  console.log('\n🔧 Testing Environment and Access...');
  
  try {
    // Test if the test_menu_access function is available
    const { data: accessData, error: accessError } = await supabase.rpc('test_menu_access');
    
    if (accessError) {
      logTest('Environment access function', false, accessError);
    } else {
      logTest('Environment access function', true);
      console.log('   Environment info:', accessData?.[0] || 'No data returned');
    }
  } catch (error) {
    logTest('Environment access test', false, error);
  }
}

// Test Categories CRUD operations
async function testCategoriesCRUD() {
  console.log('\n📁 Testing Categories CRUD...');
  let createdCategoryId = null;
  
  try {
    // CREATE
    const { data: createData, error: createError } = await supabase
      .from('menu_categories')
      .insert(testData.category)
      .select()
      .single();
    
    if (createError) {
      logTest('Categories CREATE', false, createError);
      return;
    }
    
    createdCategoryId = createData.id;
    logTest('Categories CREATE', true);
    
    // READ
    const { data: readData, error: readError } = await supabase
      .from('menu_categories')
      .select('*')
      .eq('id', createdCategoryId)
      .single();
    
    if (readError) {
      logTest('Categories READ', false, readError);
    } else {
      logTest('Categories READ', readData.name === testData.category.name);
    }
    
    // UPDATE
    const updateData = { description: 'Updated test description' };
    const { data: updateResult, error: updateError } = await supabase
      .from('menu_categories')
      .update(updateData)
      .eq('id', createdCategoryId)
      .select()
      .single();
    
    if (updateError) {
      logTest('Categories UPDATE', false, updateError);
    } else {
      logTest('Categories UPDATE', updateResult.description === updateData.description);
    }
    
    // DELETE
    const { error: deleteError } = await supabase
      .from('menu_categories')
      .delete()
      .eq('id', createdCategoryId);
    
    if (deleteError) {
      logTest('Categories DELETE', false, deleteError);
    } else {
      logTest('Categories DELETE', true);
      createdCategoryId = null; // Successfully deleted
    }
    
  } catch (error) {
    logTest('Categories CRUD Exception', false, error);
  } finally {
    // Cleanup
    if (createdCategoryId) {
      await supabase.from('menu_categories').delete().eq('id', createdCategoryId);
    }
  }
}

// Test Ingredient Categories CRUD operations
async function testIngredientCategoriesCRUD() {
  console.log('\n🏷️ Testing Ingredient Categories CRUD...');
  let createdCategoryId = null;
  
  try {
    // CREATE
    const { data: createData, error: createError } = await supabase
      .from('ingredient_categories')
      .insert(testData.ingredientCategory)
      .select()
      .single();
    
    if (createError) {
      logTest('Ingredient Categories CREATE', false, createError);
      return;
    }
    
    createdCategoryId = createData.id;
    logTest('Ingredient Categories CREATE', true);
    
    // READ
    const { data: readData, error: readError } = await supabase
      .from('ingredient_categories')
      .select('*')
      .eq('id', createdCategoryId)
      .single();
    
    if (readError) {
      logTest('Ingredient Categories READ', false, readError);
    } else {
      logTest('Ingredient Categories READ', readData.name === testData.ingredientCategory.name);
    }
    
    // UPDATE
    const updateData = { color_code: '#00FF00' };
    const { data: updateResult, error: updateError } = await supabase
      .from('ingredient_categories')
      .update(updateData)
      .eq('id', createdCategoryId)
      .select()
      .single();
    
    if (updateError) {
      logTest('Ingredient Categories UPDATE', false, updateError);
    } else {
      logTest('Ingredient Categories UPDATE', updateResult.color_code === updateData.color_code);
    }
    
    // DELETE
    const { error: deleteError } = await supabase
      .from('ingredient_categories')
      .delete()
      .eq('id', createdCategoryId);
    
    if (deleteError) {
      logTest('Ingredient Categories DELETE', false, deleteError);
    } else {
      logTest('Ingredient Categories DELETE', true);
      createdCategoryId = null;
    }
    
  } catch (error) {
    logTest('Ingredient Categories CRUD Exception', false, error);
  } finally {
    // Cleanup
    if (createdCategoryId) {
      await supabase.from('ingredient_categories').delete().eq('id', createdCategoryId);
    }
  }
}

// Test Ingredients CRUD operations
async function testIngredientsCRUD() {
  console.log('\n🧄 Testing Ingredients CRUD...');
  let createdCategoryId = null;
  let createdIngredientId = null;
  
  try {
    // First create an ingredient category
    const { data: categoryData, error: categoryError } = await supabase
      .from('ingredient_categories')
      .insert({
        name: 'Test Category for Ingredient',
        description: 'Temporary category for ingredient testing',
        color_code: '#0000FF',
        display_order: 998,
        is_active: true
      })
      .select()
      .single();
    
    if (categoryError) {
      logTest('Ingredients CRUD Setup (Category)', false, categoryError);
      return;
    }
    
    createdCategoryId = categoryData.id;
    
    // CREATE ingredient
    const ingredientData = { ...testData.ingredient, category_id: createdCategoryId };
    const { data: createData, error: createError } = await supabase
      .from('ingredients')
      .insert(ingredientData)
      .select()
      .single();
    
    if (createError) {
      logTest('Ingredients CREATE', false, createError);
      return;
    }
    
    createdIngredientId = createData.id;
    logTest('Ingredients CREATE', true);
    
    // READ
    const { data: readData, error: readError } = await supabase
      .from('ingredients')
      .select('*')
      .eq('id', createdIngredientId)
      .single();
    
    if (readError) {
      logTest('Ingredients READ', false, readError);
    } else {
      logTest('Ingredients READ', readData.name === testData.ingredient.name);
    }
    
    // UPDATE
    const updateData = { price: 3.50 };
    const { data: updateResult, error: updateError } = await supabase
      .from('ingredients')
      .update(updateData)
      .eq('id', createdIngredientId)
      .select()
      .single();
    
    if (updateError) {
      logTest('Ingredients UPDATE', false, updateError);
    } else {
      logTest('Ingredients UPDATE', updateResult.price === updateData.price);
    }
    
    // DELETE
    const { error: deleteError } = await supabase
      .from('ingredients')
      .delete()
      .eq('id', createdIngredientId);
    
    if (deleteError) {
      logTest('Ingredients DELETE', false, deleteError);
    } else {
      logTest('Ingredients DELETE', true);
      createdIngredientId = null;
    }
    
  } catch (error) {
    logTest('Ingredients CRUD Exception', false, error);
  } finally {
    // Cleanup
    if (createdIngredientId) {
      await supabase.from('ingredients').delete().eq('id', createdIngredientId);
    }
    if (createdCategoryId) {
      await supabase.from('ingredient_categories').delete().eq('id', createdCategoryId);
    }
  }
}

// Test Menu Items CRUD operations
async function testMenuItemsCRUD() {
  console.log('\n🍽️ Testing Menu Items CRUD...');
  let createdCategoryId = null;
  let createdMenuItemId = null;
  
  try {
    // First create a menu category
    const { data: categoryData, error: categoryError } = await supabase
      .from('menu_categories')
      .insert({
        name: 'Test Category for Menu Item',
        description: 'Temporary category for menu item testing',
        category_type: 'standard',
        display_order: 998,
        is_active: true,
        is_featured: false
      })
      .select()
      .single();
    
    if (categoryError) {
      logTest('Menu Items CRUD Setup (Category)', false, categoryError);
      return;
    }
    
    createdCategoryId = categoryData.id;
    
    // CREATE menu item
    const menuItemData = { ...testData.menuItem, category_id: createdCategoryId };
    const { data: createData, error: createError } = await supabase
      .from('subcategories')
      .insert(menuItemData)
      .select()
      .single();
    
    if (createError) {
      logTest('Menu Items CREATE', false, createError);
      return;
    }
    
    createdMenuItemId = createData.id;
    logTest('Menu Items CREATE', true);
    
    // READ
    const { data: readData, error: readError } = await supabase
      .from('subcategories')
      .select('*')
      .eq('id', createdMenuItemId)
      .single();
    
    if (readError) {
      logTest('Menu Items READ', false, readError);
    } else {
      logTest('Menu Items READ', readData.name === testData.menuItem.name);
    }
    
    // UPDATE
    const updateData = { base_price: 15.99 };
    const { data: updateResult, error: updateError } = await supabase
      .from('subcategories')
      .update(updateData)
      .eq('id', createdMenuItemId)
      .select()
      .single();
    
    if (updateError) {
      logTest('Menu Items UPDATE', false, updateError);
    } else {
      logTest('Menu Items UPDATE', updateResult.base_price === updateData.base_price);
    }
    
    // DELETE
    const { error: deleteError } = await supabase
      .from('subcategories')
      .delete()
      .eq('id', createdMenuItemId);
    
    if (deleteError) {
      logTest('Menu Items DELETE', false, deleteError);
    } else {
      logTest('Menu Items DELETE', true);
      createdMenuItemId = null;
    }
    
  } catch (error) {
    logTest('Menu Items CRUD Exception', false, error);
  } finally {
    // Cleanup
    if (createdMenuItemId) {
      await supabase.from('subcategories').delete().eq('id', createdMenuItemId);
    }
    if (createdCategoryId) {
      await supabase.from('menu_categories').delete().eq('id', createdCategoryId);
    }
  }
}

// Test the specific admin dashboard functions
async function testAdminDashboardFunctions() {
  console.log('\n🖥️ Testing Admin Dashboard Functions...');
  
  try {
    // Test loadCategories function
    const { data: categories, error: categoriesError } = await supabase
      .from('menu_categories')
      .select('*');
    
    if (categoriesError) {
      logTest('loadCategories function', false, categoriesError);
    } else {
      logTest('loadCategories function', true);
      console.log(`   Found ${categories?.length || 0} categories`);
    }
    
    // Test loadIngredients function equivalent
    const [ingredientCategoriesResult, ingredientsResult] = await Promise.all([
      supabase.from('ingredient_categories').select('*'),
      supabase.from('ingredients').select('*')
    ]);
    
    if (ingredientCategoriesResult.error || ingredientsResult.error) {
      logTest('loadIngredients function', false, ingredientCategoriesResult.error || ingredientsResult.error);
    } else {
      logTest('loadIngredients function', true);
      console.log(`   Found ${ingredientCategoriesResult.data?.length || 0} ingredient categories`);
      console.log(`   Found ${ingredientsResult.data?.length || 0} ingredients`);
    }
    
    // Test loadMenuItems function equivalent
    const [menuItemsResult, menuItemIngredientsResult] = await Promise.all([
      supabase.from('subcategories').select('*'),
      supabase.from('menu_item_ingredients').select('*')
    ]);
    
    if (menuItemsResult.error || menuItemIngredientsResult.error) {
      logTest('loadMenuItems function', false, menuItemsResult.error || menuItemIngredientsResult.error);
    } else {
      logTest('loadMenuItems function', true);
      console.log(`   Found ${menuItemsResult.data?.length || 0} menu items`);
      console.log(`   Found ${menuItemIngredientsResult.data?.length || 0} menu item ingredients`);
    }
    
  } catch (error) {
    logTest('Admin Dashboard Functions Exception', false, error);
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Comprehensive Menu Functionality Tests...\n');
  console.log(`📍 Testing against: ${supabaseUrl}`);
  console.log(`🔑 Using anonymous key (no authentication required)\n`);
  
  await testEnvironmentAccess();
  await testCategoriesCRUD();
  await testIngredientCategoriesCRUD();
  await testIngredientsCRUD();
  await testMenuItemsCRUD();
  await testAdminDashboardFunctions();
  
  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log(`   Total tests: ${testResults.total}`);
  console.log(`   Passed: ${testResults.passed} ✅`);
  console.log(`   Failed: ${testResults.failed} ❌`);
  console.log(`   Success rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error.test}: ${error.error}`);
    });
  }
  
  const success = testResults.failed === 0;
  console.log(`\n${success ? '🎉' : '⚠️'} Test suite ${success ? 'PASSED' : 'FAILED'}`);
  
  if (success) {
    console.log('\n✅ All CRUD operations working correctly!');
    console.log('✅ Admin dashboard should now function without errors');
    console.log('✅ Category save operations should work');
  } else {
    console.log('\n❌ Some issues remain to be resolved');
  }
  
  return success;
}

// Run tests
runAllTests().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('💥 Test suite crashed:', error);
  process.exit(1);
});