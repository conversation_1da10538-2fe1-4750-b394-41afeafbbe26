'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useNotificationHelpers } from '@/components/ui/notification-system';

interface FavoriteItem {
  id: string;
  name: string;
  price: number;
  image_url: string;
  category_id: string;
  description?: string;
  addedAt: Date;
}

interface FavoritesContextType {
  favorites: FavoriteItem[];
  addToFavorites: (item: Omit<FavoriteItem, 'addedAt'>) => void;
  removeFromFavorites: (itemId: string) => void;
  isFavorite: (itemId: string) => boolean;
  clearFavorites: () => void;
  favoritesCount: number;
}

const FavoritesContext = createContext<FavoritesContextType | undefined>(undefined);

const FAVORITES_STORAGE_KEY = 'delicious-crepes-favorites';

export function useFavorites() {
  const context = useContext(FavoritesContext);
  if (!context) {
    throw new Error('useFavorites must be used within a FavoritesProvider');
  }
  return context;
}

export function FavoritesProvider({ children }: { children: React.ReactNode }) {
  const [favorites, setFavorites] = useState<FavoriteItem[]>([]);
  const [isHydrated, setIsHydrated] = useState(false);
  const { success, info } = useNotificationHelpers();

  // Load favorites from localStorage on mount
  useEffect(() => {
    setIsHydrated(true);
    try {
      const stored = localStorage.getItem(FAVORITES_STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Convert addedAt strings back to Date objects
        const favoritesWithDates = parsed.map((item: any) => ({
          ...item,
          addedAt: new Date(item.addedAt)
        }));
        setFavorites(favoritesWithDates);
      }
    } catch (error) {
      console.error('Failed to load favorites from localStorage:', error);
    }
  }, []);

  // Save favorites to localStorage whenever favorites change
  useEffect(() => {
    if (!isHydrated) return;
    try {
      localStorage.setItem(FAVORITES_STORAGE_KEY, JSON.stringify(favorites));
    } catch (error) {
      console.error('Failed to save favorites to localStorage:', error);
    }
  }, [favorites, isHydrated]);

  const addToFavorites = useCallback((item: Omit<FavoriteItem, 'addedAt'>) => {
    setFavorites(prev => {
      // Check if item is already in favorites
      if (prev.some(fav => fav.id === item.id)) {
        info('Already in favorites', `${item.name} is already in your favorites.`);
        return prev;
      }

      const newFavorite: FavoriteItem = {
        ...item,
        addedAt: new Date()
      };

      success('Added to favorites', `${item.name} has been added to your favorites.`);
      return [...prev, newFavorite];
    });
  }, [success, info]);

  const removeFromFavorites = useCallback((itemId: string) => {
    setFavorites(prev => {
      const item = prev.find(fav => fav.id === itemId);
      if (item) {
        info('Removed from favorites', `${item.name} has been removed from your favorites.`);
      }
      return prev.filter(fav => fav.id !== itemId);
    });
  }, [info]);

  const isFavorite = useCallback((itemId: string) => {
    return favorites.some(fav => fav.id === itemId);
  }, [favorites]);

  const clearFavorites = useCallback(() => {
    setFavorites([]);
    info('Favorites cleared', 'All favorites have been removed.');
  }, [info]);

  const favoritesCount = favorites.length;

  return (
    <FavoritesContext.Provider value={{
      favorites,
      addToFavorites,
      removeFromFavorites,
      isFavorite,
      clearFavorites,
      favoritesCount
    }}>
      {children}
    </FavoritesContext.Provider>
  );
}