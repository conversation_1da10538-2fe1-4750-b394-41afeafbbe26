import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { GlassCard, GlassButton, GlassInput } from '@/components/ui/glass-components';

export const metadata: Metadata = {
  title: 'Reset Password | Delicious Crepes & Waffles',
  description: 'Create a new password for your account.',
};

export default function ResetPasswordPage() {
  return (
    <main className="min-h-screen py-12 px-4 flex items-center justify-center">
      <div className="max-w-md w-full">
        <div className="text-center mb-8">
          <Link href="/" className="inline-block">
            <Image
              src="/images/logo.svg"
              alt="Delicious Crepes & Waffles"
              width={150}
              height={60}
              className="mx-auto"
            />
          </Link>
          <h1 className="text-3xl font-bold mt-6">Reset Password</h1>
          <p className="text-muted-foreground mt-2">Create a new password for your account</p>
        </div>

        <GlassCard>
          <div className="p-6">
            <form>
              <div className="space-y-4">
                <GlassInput
                  label="New Password"
                  type="password"
                  placeholder="Enter your new password"
                  required
                />

                <GlassInput
                  label="Confirm New Password"
                  type="password"
                  placeholder="Confirm your new password"
                  required
                />

                <div className="text-sm text-muted-foreground">
                  <p>Your password must:</p>
                  <ul className="list-disc pl-5 mt-1 space-y-1">
                    <li>Be at least 8 characters long</li>
                    <li>Include at least one uppercase letter</li>
                    <li>Include at least one number</li>
                    <li>Include at least one special character</li>
                  </ul>
                </div>

                <GlassButton variant="primary" className="w-full">
                  Reset Password
                </GlassButton>
              </div>
            </form>

            <div className="mt-6 text-center">
              <p className="text-sm text-muted-foreground">
                Remembered your password?{' '}
                <Link href="/login" className="text-primary hover:underline">
                  Back to login
                </Link>
              </p>
            </div>
          </div>
        </GlassCard>

        <div className="mt-8 text-center">
          <p className="text-sm text-muted-foreground">
            Need help?{' '}
            <Link href="/contact" className="text-primary hover:underline">
              Contact Support
            </Link>
          </p>
        </div>
      </div>
    </main>
  );
}
