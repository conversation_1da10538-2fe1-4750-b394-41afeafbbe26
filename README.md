# Creperie Management System

A comprehensive digital ecosystem for Creperie management built as a monorepo with multiple interconnected applications.

## 🏗️ Project Structure

```
creperie-management-system/
├── admin-dashboard/          # Next.js SaaS Admin Platform
├── pos-system/              # Electron Windows POS Application
├── customer-web/            # Next.js Customer Website
├── customer-mobile/         # React Native Mobile App
├── shared/                  # Shared Types, Utilities & Constants
├── database/                # Supabase Schemas & Migrations
├── package.json             # Root workspace configuration
└── README.md               # This file
```

## 🚀 Technology Stack

### Frontend Platforms
- **Admin Dashboard**: Next.js 14+ with React, TypeScript, Tailwind CSS, shadcn/ui
- **Customer Website**: Next.js 14+ with React, TypeScript, Tailwind CSS, shadcn/ui
- **POS System**: Electron with TypeScript for Windows desktop
- **Mobile App**: React Native with TypeScript for iOS/Android

### Backend & Database
- **Database**: Supabase (PostgreSQL with real-time subscriptions)
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage
- **API**: Supabase Auto-generated REST & GraphQL APIs

### Shared Technologies
- **Language**: TypeScript across all platforms
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: React Query/TanStack Query
- **Form Handling**: React Hook Form with Zod validation
- **Internationalization**: i18next for multi-language support

## 📦 Workspace Commands

### Root Level Commands
```bash
# Install all dependencies
npm run install-all

# Run all applications in development mode
npm run dev

# Build all applications
npm run build

# Run linting across all workspaces
npm run lint

# Run tests across all workspaces
npm run test
```

### Individual Application Commands
```bash
# Admin Dashboard
npm run admin:dev

# POS System
npm run pos:dev

# Customer Website
npm run web:dev

# Mobile App
npm run mobile:dev

# Database Operations
npm run db:migrate
npm run db:seed
```

## 🛠️ Setup Instructions

### Prerequisites
- Node.js 18+ and npm 9+
- Git
- Supabase CLI
- For mobile development: React Native CLI, Android Studio, Xcode
- For POS system: Electron dependencies

### Initial Setup
1. Clone the repository
2. Install dependencies: `npm run install-all`
3. Copy environment files: `cp .env.example .env.local` in each module
4. Configure Supabase credentials in environment files
5. Run database migrations: `npm run db:migrate`
6. Start development: `npm run dev`

## 🔧 Development Workflow

1. **Shared Code**: All shared types, utilities, and constants go in `/shared`
2. **Database Changes**: Schema changes and migrations in `/database`
3. **Feature Development**: Work in respective module directories
4. **Testing**: Each module has its own test suite
5. **Deployment**: Each application can be deployed independently

## 📱 Applications Overview

### Admin Dashboard (`/admin-dashboard`)
- Restaurant management interface
- Menu management
- Order tracking and analytics
- Staff management
- Financial reporting
- Multi-location support

### POS System (`/pos-system`)
- Windows desktop application
- Order taking and processing
- Payment processing
- Kitchen display integration
- Offline capability
- Receipt printing

### Customer Website (`/customer-web`)
- Public-facing restaurant website
- Online menu browsing
- Table reservations
- Online ordering
- Customer reviews
- Location and contact information

### Mobile App (`/customer-mobile`)
- iOS and Android customer app
- Mobile ordering
- Loyalty program
- Push notifications
- Location-based features
- Order tracking

## 🗄️ Database Schema

The database module contains:
- Supabase migration files
- Database schema definitions
- Seed data for development
- Row Level Security (RLS) policies
- Database functions and triggers

## 🌐 Environment Configuration

Each module requires specific environment variables:
- Supabase URL and API keys
- Authentication providers
- Payment gateway credentials
- Third-party service APIs
- Platform-specific configurations

## 🚀 Deployment

- **Admin Dashboard**: Vercel/Netlify
- **Customer Website**: Vercel/Netlify
- **POS System**: Windows installer package
- **Mobile App**: App Store and Google Play Store
- **Database**: Supabase hosted PostgreSQL

## 📄 License

MIT License - see LICENSE file for details.