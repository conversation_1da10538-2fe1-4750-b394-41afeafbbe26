// Test the Supabase MCP wrapper
const supabase = require('./supabase-mcp-wrapper.js');

async function testWrapper() {
  console.log('🧪 Testing Supabase MCP Wrapper\n');
  
  try {
    // Test connection
    console.log('1. Testing connection...');
    const connected = await supabase.testConnection();
    console.log(`   Connection: ${connected ? '✅ OK' : '❌ Failed'}\n`);
    
    // List projects
    console.log('2. Listing projects...');
    const projects = await supabase.listProjects();
    console.log(`   Found ${projects.length} project(s)\n`);
    
    // List tables
    console.log('3. Listing tables...');
    const tables = await supabase.listTables();
    console.log(`   Found ${tables.length} table(s)`);
    tables.forEach(table => console.log(`   - ${table.name}`));
    console.log('');
    
    // Test delivery zones
    console.log('4. Testing delivery_zones...');
    const deliveryZonesResult = await supabase.executeSQL(null, 'SELECT COUNT(*) as count FROM delivery_zones');
    if (deliveryZonesResult.error) {
      console.log(`   ❌ Error: ${deliveryZonesResult.error}`);
    } else {
      console.log(`   ✅ delivery_zones accessible`);
    }
    console.log('');
    
    // Get project info
    console.log('5. Getting project info...');
    const projectUrl = await supabase.getProjectUrl();
    const anonKey = await supabase.getAnonKey();
    console.log(`   URL: ${projectUrl}`);
    console.log(`   Key: ${anonKey.substring(0, 20)}...`);
    console.log('');
    
    console.log('🎉 All tests passed! MCP wrapper is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testWrapper();