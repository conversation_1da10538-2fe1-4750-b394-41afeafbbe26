import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/theme-context';
import CustomerInfoForm from '../components/CustomerInfoForm';
import { MenuModal } from '../components/modals/MenuModal';
import toast from 'react-hot-toast';

interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
}

interface NewOrderPageProps {
  // Optional props if needed
}

interface CustomerInfo {
  name: string;
  phone: string;
  email?: string;
  address?: {
    street: string;
    city: string;
    postalCode: string;
    coordinates?: { lat: number; lng: number };
  };
}

const NewOrderPage: React.FC<NewOrderPageProps> = () => {
  const navigate = useNavigate();
  const { resolvedTheme } = useTheme();
  
  // Modal states
  const [showPhoneLookupModal, setShowPhoneLookupModal] = useState(false);
  const [showCustomerInfoModal, setShowCustomerInfoModal] = useState(false);
  const [showMenuModal, setShowMenuModal] = useState(false);
  const [selectedOrderType, setSelectedOrderType] = useState<"pickup" | "delivery" | null>(null);
  
  // Customer data states
  const [phoneNumber, setPhoneNumber] = useState('');
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    phone: '',
    email: '',
    address: {
      street: '',
      city: '',
      postalCode: '',
    }
  });
  const [existingCustomer, setExistingCustomer] = useState<Customer | null>(null);
  const [isLookingUp, setIsLookingUp] = useState(false);
  
  // Additional states for customer form
  const [orderType, setOrderType] = useState<"dine-in" | "takeaway" | "delivery">("takeaway");
  const [tableNumber, setTableNumber] = useState('');
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [isValidatingAddress, setIsValidatingAddress] = useState(false);
  const [addressValid, setAddressValid] = useState(false);

  // Handler for selecting order type
  const handleOrderTypeSelect = (type: "pickup" | "delivery") => {
    setSelectedOrderType(type);
    
    if (type === "pickup") {
      // For pickup orders, create a basic customer object and go directly to menu
      const pickupCustomer = {
        id: 'pickup-customer',
        name: 'Walk-in Customer',
        phone_number: '',
        email: '',
        addresses: []
      };
      
      setOrderType("takeaway");
      setShowMenuModal(true);
    } else {
      // For delivery orders, show phone lookup modal
      setOrderType("delivery");
      setShowPhoneLookupModal(true);
    }
  };

  // Handler for phone lookup
  const handlePhoneLookup = async () => {
    if (!phoneNumber.trim()) {
      toast.error("Please enter a phone number");
      return;
    }

    setIsLookingUp(true);
    try {
      // TODO: Replace with actual customer service
      const customer: any = null; // await customerService.lookupByPhone(phoneNumber);
      
      if (customer) {
        // Customer found, populate form with existing data
        setExistingCustomer(customer);
        setCustomerInfo({
          name: customer.name,
          phone: customer.phone || '',
          email: customer.email || '',
          address: {
            street: customer.address || '',
            city: '',
            postalCode: customer.postal_code || '',
          }
        });
        toast.success(`Customer found: ${customer.name}`);
      } else {
        // Customer not found, show empty form for new customer
        setExistingCustomer(null);
        setCustomerInfo({
          name: '',
          phone: phoneNumber,
          email: '',
          address: {
            street: '',
            city: '',
            postalCode: '',
          }
        });
        toast("Customer not found. Please enter customer details.", { 
          icon: 'ℹ️',
          duration: 3000,
        });
      }
      
      setShowPhoneLookupModal(false);
      setShowCustomerInfoModal(true);
      
    } catch (error) {
      console.error('Error looking up customer:', error);
      toast.error("Failed to lookup customer. Please try again.");
    } finally {
      setIsLookingUp(false);
    }
  };

  // Handler for address validation
  const handleValidateAddress = async (address: string) => {
    setIsValidatingAddress(true);
    try {
      // Mock address validation - in real app this would call Google Maps API
      await new Promise(resolve => setTimeout(resolve, 1000));
      setAddressValid(true);
      toast.success("Address validated successfully");
      return true;
    } catch (error) {
      setAddressValid(false);
      toast.error("Failed to validate address");
      return false;
    } finally {
      setIsValidatingAddress(false);
    }
  };

  // Handler for customer info form submission - now opens MenuModal instead of navigating
  const handleCustomerInfoSubmit = () => {
    if (!customerInfo.name || !customerInfo.phone) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (orderType === 'delivery' && (!customerInfo.address?.street || !customerInfo.address?.city)) {
      toast.error("Please provide delivery address");
      return;
    }

    // Close customer info modal and open menu modal
    setShowCustomerInfoModal(false);
    setShowMenuModal(true);
  };

  // Handler for menu modal close
  const handleMenuModalClose = () => {
    setShowMenuModal(false);
    // Navigate back to orders after menu is closed
    navigate('/');
  };

  // Handler for going back to main orders page
  const handleBackToOrders = () => {
    navigate('/');
  };

  // Handle keyboard events for modals
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (showMenuModal) {
          setShowMenuModal(false);
        } else if (showCustomerInfoModal) {
          setShowCustomerInfoModal(false);
        } else if (showPhoneLookupModal) {
          setShowPhoneLookupModal(false);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showPhoneLookupModal, showCustomerInfoModal, showMenuModal]);

  // Create customer object for MenuModal
  const getCustomerForMenu = () => {
    if (selectedOrderType === "pickup") {
      return {
        id: 'pickup-customer',
        name: 'Walk-in Customer',
        phone_number: '',
        email: '',
        addresses: []
      };
    }

    return {
      id: existingCustomer?.id || 'new-customer',
      name: customerInfo.name,
      phone_number: customerInfo.phone,
      email: customerInfo.email || '',
      addresses: customerInfo.address?.street ? [{
        id: 'primary-address',
        street: customerInfo.address.street,
        postal_code: customerInfo.address.postalCode || '',
        floor: '',
        notes: specialInstructions || '',
        delivery_instructions: ''
      }] : []
    };
  };

  // Get selected address for MenuModal
  const getSelectedAddress = () => {
    if (selectedOrderType === "pickup") {
      return null;
    }

    if (customerInfo.address?.street) {
      return {
        id: 'primary-address',
        street: customerInfo.address.street,
        postal_code: customerInfo.address.postalCode || '',
        floor: '',
        notes: specialInstructions || '',
        delivery_instructions: ''
      };
    }

    return null;
  };

  return (
    <div className={`min-h-screen relative ${
      resolvedTheme === 'dark' 
        ? 'bg-gradient-to-br from-gray-900 via-blue-900/20 to-purple-900/20' 
        : 'bg-gradient-to-br from-blue-50 via-purple-50/30 to-pink-50/20'
    }`}>
      {/* Header with glassmorphism */}
      <div className={`backdrop-blur-xl border-b shadow-lg ${
        resolvedTheme === 'dark' 
          ? 'bg-gray-800/30 border-gray-700/50' 
          : 'bg-white/30 border-white/50'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={handleBackToOrders}
                className={`mr-4 flex items-center transition-all duration-300 px-3 py-2 rounded-xl ${
                  resolvedTheme === 'dark'
                    ? 'text-blue-400 hover:text-blue-300 hover:bg-blue-500/20'
                    : 'text-blue-600 hover:text-blue-800 hover:bg-blue-500/10'
                }`}
              >
                <svg
                  className="w-5 h-5 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
                Back to Orders
              </button>
              <h1 className={`text-xl font-semibold ${
                resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                New Order
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content with glassmorphism */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col items-center">
          <div className={`backdrop-blur-xl rounded-3xl shadow-2xl border p-12 w-full max-w-4xl ${
            resolvedTheme === 'dark'
              ? 'bg-gray-800/20 border-gray-700/30'
              : 'bg-white/20 border-white/30'
          }`}>
            <h2 className={`text-3xl font-bold text-center mb-4 ${
              resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              Choose Order Type
            </h2>
            
            <p className={`text-lg mb-12 text-center ${
              resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
            }`}>
              What type of order would you like to create?
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Pickup Option */}
              <button
                onClick={() => handleOrderTypeSelect("pickup")}
                className={`border-2 border-blue-500 rounded-2xl p-10 flex flex-col items-center transition-all duration-300 shadow-xl hover:scale-105 transform active:scale-95 backdrop-blur-sm ${
                  resolvedTheme === 'dark'
                    ? 'bg-gray-800/40 hover:bg-gray-700/50 hover:border-blue-400 hover:shadow-blue-500/25'
                    : 'bg-white/40 hover:bg-blue-50/50 hover:border-blue-600 hover:shadow-blue-500/25'
                }`}
              >
                <div className={`w-24 h-24 rounded-full flex items-center justify-center mb-6 ${
                  resolvedTheme === 'dark' ? 'bg-blue-500/30' : 'bg-blue-100'
                }`}>
                  <svg
                    className={`w-12 h-12 ${
                      resolvedTheme === 'dark' ? 'text-blue-400' : 'text-blue-600'
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
                    />
                  </svg>
                </div>
                <h3 className={`text-2xl font-bold mb-3 ${
                  resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  Pickup Order
                </h3>
                <p className={`text-center leading-relaxed ${
                  resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  Customer will pick up the order from the store
                </p>
              </button>

              {/* Delivery Option */}
              <button
                onClick={() => handleOrderTypeSelect("delivery")}
                className={`border-2 border-emerald-500 rounded-2xl p-10 flex flex-col items-center transition-all duration-300 shadow-xl hover:scale-105 transform active:scale-95 backdrop-blur-sm ${
                  resolvedTheme === 'dark'
                    ? 'bg-gray-800/40 hover:bg-gray-700/50 hover:border-emerald-400 hover:shadow-emerald-500/25'
                    : 'bg-white/40 hover:bg-emerald-50/50 hover:border-emerald-600 hover:shadow-emerald-500/25'
                }`}
              >
                <div className={`w-24 h-24 rounded-full flex items-center justify-center mb-6 ${
                  resolvedTheme === 'dark' ? 'bg-emerald-500/30' : 'bg-emerald-100'
                }`}>
                  <svg
                    className={`w-12 h-12 ${
                      resolvedTheme === 'dark' ? 'text-emerald-400' : 'text-emerald-600'
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                    />
                  </svg>
                </div>
                <h3 className={`text-2xl font-bold mb-3 ${
                  resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  Delivery Order
                </h3>
                <p className={`text-center leading-relaxed ${
                  resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  Order will be delivered to the customer's address
                </p>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Phone Lookup Modal */}
      {showPhoneLookupModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`w-full max-w-md rounded-3xl shadow-2xl border transform transition-all duration-300 ${
            resolvedTheme === 'dark'
              ? 'bg-gray-800/90 border-gray-700/50 backdrop-blur-xl'
              : 'bg-white/90 border-gray-200/50 backdrop-blur-xl'
          }`}>
            <div className="p-8">
              <h3 className={`text-2xl font-bold mb-4 text-center ${
                resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                Customer Phone Number
              </h3>
              
              <p className={`text-center mb-6 ${
                resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
              }`}>
                Enter customer's phone number to lookup existing account or create new one
              </p>

              <div className="space-y-6">
                <div>
                  <input
                    type="tel"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    placeholder="Enter phone number"
                    className={`w-full p-4 rounded-xl border-2 transition-all duration-300 ${
                      resolvedTheme === 'dark'
                        ? 'bg-gray-700/50 border-gray-600/50 text-white placeholder-gray-400 focus:border-blue-500 focus:bg-gray-700/70'
                        : 'bg-white/50 border-gray-300/50 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:bg-white/70'
                    } backdrop-blur-sm`}
                    autoFocus
                    onKeyDown={(e) => e.key === 'Enter' && handlePhoneLookup()}
                  />
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowPhoneLookupModal(false)}
                    className={`flex-1 px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                      resolvedTheme === 'dark'
                        ? 'bg-gray-600/50 text-white hover:bg-gray-600/70'
                        : 'bg-gray-300/50 text-gray-700 hover:bg-gray-300/70'
                    } backdrop-blur-sm`}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handlePhoneLookup}
                    disabled={isLookingUp || !phoneNumber.trim()}
                    className={`flex-1 px-6 py-3 rounded-xl font-medium transition-all duration-300 backdrop-blur-sm ${
                      isLookingUp || !phoneNumber.trim()
                        ? resolvedTheme === 'dark'
                          ? 'bg-gray-600/50 text-gray-400 cursor-not-allowed'
                          : 'bg-gray-300/50 text-gray-500 cursor-not-allowed'
                        : resolvedTheme === 'dark'
                          ? 'bg-blue-600/80 text-white hover:bg-blue-600/90'
                          : 'bg-blue-600/80 text-white hover:bg-blue-600/90'
                    }`}
                  >
                    {isLookingUp ? 'Looking up...' : 'Continue'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Customer Info Modal */}
      {showCustomerInfoModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`w-full max-w-4xl rounded-3xl shadow-2xl border transform transition-all duration-300 max-h-[90vh] overflow-hidden ${
            resolvedTheme === 'dark'
              ? 'bg-gray-800/90 border-gray-700/50 backdrop-blur-xl'
              : 'bg-white/90 border-gray-200/50 backdrop-blur-xl'
          }`}>
            <div className="flex items-center justify-between p-6 border-b border-gray-200/20">
              <h3 className={`text-2xl font-bold ${
                resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                Customer Information
                {existingCustomer && (
                  <span className="text-emerald-500 text-lg ml-2">(Existing Customer)</span>
                )}
              </h3>
              <button
                onClick={() => setShowCustomerInfoModal(false)}
                className={`p-2 rounded-xl transition-all duration-200 ${
                  resolvedTheme === 'dark'
                    ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700/50'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100/50'
                }`}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
              <CustomerInfoForm
                customerInfo={customerInfo}
                setCustomerInfo={setCustomerInfo}
                orderType={orderType}
                setOrderType={setOrderType}
                tableNumber={tableNumber}
                setTableNumber={setTableNumber}
                specialInstructions={specialInstructions}
                setSpecialInstructions={setSpecialInstructions}
                onValidateAddress={handleValidateAddress}
                isValidatingAddress={isValidatingAddress}
                addressValid={addressValid}
              />
              
              <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200/20">
                <button
                  onClick={() => setShowCustomerInfoModal(false)}
                  className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                    resolvedTheme === 'dark'
                      ? 'bg-gray-600/50 text-white hover:bg-gray-600/70'
                      : 'bg-gray-300/50 text-gray-700 hover:bg-gray-300/70'
                  } backdrop-blur-sm`}
                >
                  Cancel
                </button>
                <button
                  onClick={handleCustomerInfoSubmit}
                  className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 backdrop-blur-sm ${
                    resolvedTheme === 'dark'
                      ? 'bg-blue-600/80 text-white hover:bg-blue-600/90'
                      : 'bg-blue-600/80 text-white hover:bg-blue-600/90'
                  }`}
                >
                  Continue to Menu
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Menu Modal */}
      {showMenuModal && (
        <MenuModal
          isOpen={showMenuModal}
          onClose={handleMenuModalClose}
          selectedCustomer={getCustomerForMenu()}
          selectedAddress={getSelectedAddress()}
          orderType={selectedOrderType === "pickup" ? "pickup" : "delivery"}
        />
      )}
    </div>
  );
};

export default NewOrderPage;