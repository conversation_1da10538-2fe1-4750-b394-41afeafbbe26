/**
 * Security Middleware for Creperie Digital Ecosystem
 * 
 * This file provides security middleware functions that can be used
 * across all applications to implement consistent security measures.
 */

import { SECURITY_CONFIG } from './security-config';
import { supabase } from '../auth/config';
import crypto from 'crypto';
import bcrypt from 'bcrypt';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { Request, Response, NextFunction } from 'express';

// Types
interface SecurityRequest extends Request {
  user?: any;
  session?: any;
  rateLimitInfo?: {
    limit: number;
    current: number;
    remaining: number;
    resetTime: Date;
  };
}

interface ValidationError {
  field: string;
  message: string;
  code: string;
}

/**
 * HELMET SECURITY HEADERS MIDDLEWARE
 */
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: SECURITY_CONFIG.HEADERS.CSP['default-src'],
      scriptSrc: SECURITY_CONFIG.HEADERS.CSP['script-src'],
      styleSrc: SECURITY_CONFIG.HEADERS.CSP['style-src'],
      fontSrc: SECURITY_CONFIG.HEADERS.CSP['font-src'],
      imgSrc: SECURITY_CONFIG.HEADERS.CSP['img-src'],
      connectSrc: SECURITY_CONFIG.HEADERS.CSP['connect-src'],
      frameSrc: SECURITY_CONFIG.HEADERS.CSP['frame-src'],
      objectSrc: SECURITY_CONFIG.HEADERS.CSP['object-src'],
      baseUri: SECURITY_CONFIG.HEADERS.CSP['base-uri'],
      formAction: SECURITY_CONFIG.HEADERS.CSP['form-action'],
      frameAncestors: SECURITY_CONFIG.HEADERS.CSP['frame-ancestors'],
      upgradeInsecureRequests: [],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
});

/**
 * RATE LIMITING MIDDLEWARE
 */
export const createRateLimit = (type: keyof typeof SECURITY_CONFIG.RATE_LIMITS) => {
  const config = SECURITY_CONFIG.RATE_LIMITS[type];
  
  return Object.keys(config).reduce((limiters, endpoint) => {
    const endpointConfig = (config as any)[endpoint];
    
    limiters[endpoint] = rateLimit({
      windowMs: endpointConfig.windowMs,
      max: endpointConfig.max,
      message: {
        error: 'Rate limit exceeded',
        message: endpointConfig.message,
        retryAfter: Math.ceil(endpointConfig.windowMs / 1000),
      },
      standardHeaders: true,
      legacyHeaders: false,
      handler: async (req: SecurityRequest, res: Response) => {
        // Log rate limit violation
        await logSecurityEvent({
          type: 'rate_limit_exceeded',
          severity: 'medium',
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          endpoint: req.path,
          method: req.method,
          userId: req.user?.id,
        });
        
        res.status(429).json({
          error: 'Rate limit exceeded',
          message: endpointConfig.message,
          retryAfter: Math.ceil(endpointConfig.windowMs / 1000),
        });
      },
    });
    
    return limiters;
  }, {} as Record<string, any>);
};

// Pre-configured rate limiters
export const authRateLimits = createRateLimit('AUTH');
export const apiRateLimits = createRateLimit('API');
export const paymentRateLimits = createRateLimit('PAYMENT');

/**
 * INPUT VALIDATION MIDDLEWARE
 */
export class InputValidator {
  static validateEmail(email: string): ValidationError[] {
    const errors: ValidationError[] = [];
    const rules = SECURITY_CONFIG.VALIDATION.USER.EMAIL;
    
    if (!email && rules.required) {
      errors.push({
        field: 'email',
        message: 'Email is required',
        code: 'REQUIRED',
      });
      return errors;
    }
    
    if (email.length > rules.maxLength) {
      errors.push({
        field: 'email',
        message: `Email must be less than ${rules.maxLength} characters`,
        code: 'MAX_LENGTH',
      });
    }
    
    if (!rules.pattern.test(email)) {
      errors.push({
        field: 'email',
        message: 'Invalid email format',
        code: 'INVALID_FORMAT',
      });
    }
    
    return errors;
  }
  
  static validatePassword(password: string): ValidationError[] {
    const errors: ValidationError[] = [];
    const rules = SECURITY_CONFIG.VALIDATION.USER.PASSWORD;
    
    if (password.length < rules.minLength) {
      errors.push({
        field: 'password',
        message: `Password must be at least ${rules.minLength} characters`,
        code: 'MIN_LENGTH',
      });
    }
    
    if (password.length > rules.maxLength) {
      errors.push({
        field: 'password',
        message: `Password must be less than ${rules.maxLength} characters`,
        code: 'MAX_LENGTH',
      });
    }
    
    if (!rules.pattern.test(password)) {
      errors.push({
        field: 'password',
        message: 'Password must contain uppercase, lowercase, number, and special character',
        code: 'INVALID_FORMAT',
      });
    }
    
    return errors;
  }
  
  static validatePhone(phone: string): ValidationError[] {
    const errors: ValidationError[] = [];
    const rules = SECURITY_CONFIG.VALIDATION.USER.PHONE;
    
    if (!phone && rules.required) {
      errors.push({
        field: 'phone',
        message: 'Phone number is required',
        code: 'REQUIRED',
      });
      return errors;
    }
    
    if (phone.length > rules.maxLength) {
      errors.push({
        field: 'phone',
        message: `Phone number must be less than ${rules.maxLength} characters`,
        code: 'MAX_LENGTH',
      });
    }
    
    if (!rules.pattern.test(phone)) {
      errors.push({
        field: 'phone',
        message: 'Invalid phone number format (use E.164 format)',
        code: 'INVALID_FORMAT',
      });
    }
    
    return errors;
  }
  
  static sanitizeInput(input: string): string {
    // Remove potentially dangerous characters
    return input
      .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remove script tags
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }
  
  static validateAndSanitize(data: Record<string, any>, rules: Record<string, any>): {
    isValid: boolean;
    errors: ValidationError[];
    sanitizedData: Record<string, any>;
  } {
    const errors: ValidationError[] = [];
    const sanitizedData: Record<string, any> = {};
    
    for (const [field, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        sanitizedData[field] = this.sanitizeInput(value);
      } else {
        sanitizedData[field] = value;
      }
      
      // Apply field-specific validation
      if (rules[field]) {
        const fieldRules = rules[field];
        
        if (fieldRules.required && (!value || value === '')) {
          errors.push({
            field,
            message: `${field} is required`,
            code: 'REQUIRED',
          });
        }
        
        if (fieldRules.pattern && !fieldRules.pattern.test(value)) {
          errors.push({
            field,
            message: `Invalid ${field} format`,
            code: 'INVALID_FORMAT',
          });
        }
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData,
    };
  }
}

/**
 * SQL INJECTION PREVENTION
 */
export const preventSQLInjection = (req: SecurityRequest, res: Response, next: NextFunction) => {
  const suspiciousPatterns = [
    /('|(\-\-)|(;)|(\||\|)|(\*|\*))/i,
    /(union|select|insert|delete|update|drop|create|alter|exec|execute)/i,
    /(script|javascript|vbscript|onload|onerror|onclick)/i,
  ];
  
  const checkValue = (value: any): boolean => {
    if (typeof value === 'string') {
      return suspiciousPatterns.some(pattern => pattern.test(value));
    }
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(checkValue);
    }
    return false;
  };
  
  const requestData = { ...req.query, ...req.body, ...req.params };
  
  if (checkValue(requestData)) {
    logSecurityEvent({
      type: 'sql_injection_attempt',
      severity: 'high',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      method: req.method,
      payload: requestData,
      userId: req.user?.id,
    });
    
    return res.status(400).json({
      error: 'Invalid request',
      message: 'Request contains potentially harmful content',
    });
  }
  
  next();
};

/**
 * XSS PROTECTION MIDDLEWARE
 */
export const xssProtection = (req: SecurityRequest, res: Response, next: NextFunction) => {
  const xssPatterns = [
    /<script[^>]*>.*?<\/script>/gi,
    /<iframe[^>]*>.*?<\/iframe>/gi,
    /<object[^>]*>.*?<\/object>/gi,
    /<embed[^>]*>.*?<\/embed>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
  ];
  
  const sanitizeValue = (value: any): any => {
    if (typeof value === 'string') {
      let sanitized = value;
      xssPatterns.forEach(pattern => {
        sanitized = sanitized.replace(pattern, '');
      });
      return sanitized;
    }
    if (typeof value === 'object' && value !== null) {
      const sanitized: any = Array.isArray(value) ? [] : {};
      for (const [key, val] of Object.entries(value)) {
        sanitized[key] = sanitizeValue(val);
      }
      return sanitized;
    }
    return value;
  };
  
  // Sanitize request data
  req.body = sanitizeValue(req.body);
  req.query = sanitizeValue(req.query);
  req.params = sanitizeValue(req.params);
  
  next();
};

/**
 * ENCRYPTION UTILITIES
 */
export class EncryptionUtils {
  private static readonly algorithm = SECURITY_CONFIG.ENCRYPTION.AES.algorithm;
  private static readonly keyLength = SECURITY_CONFIG.ENCRYPTION.AES.keyLength;
  
  static generateKey(): string {
    return crypto.randomBytes(this.keyLength).toString('hex');
  }
  
  static encrypt(text: string, key: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, key);
    cipher.setAAD(Buffer.from('creperie-system'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    return iv.toString('hex') + ':' + tag.toString('hex') + ':' + encrypted;
  }
  
  static decrypt(encryptedData: string, key: string): string {
    const parts = encryptedData.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const tag = Buffer.from(parts[1], 'hex');
    const encrypted = parts[2];
    
    const decipher = crypto.createDecipher(this.algorithm, key);
    decipher.setAAD(Buffer.from('creperie-system'));
    decipher.setAuthTag(tag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
  
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, SECURITY_CONFIG.ENCRYPTION.BCRYPT.saltRounds);
  }
  
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }
  
  static maskCardNumber(cardNumber: string): string {
    const config = SECURITY_CONFIG.PCI_DSS.CARD_DATA.PAN_MASKING;
    const visibleDigits = cardNumber.slice(-config.visibleDigits);
    const maskedPortion = config.maskCharacter.repeat(cardNumber.length - config.visibleDigits);
    return maskedPortion + visibleDigits;
  }
}

/**
 * AUDIT LOGGING
 */
interface SecurityEvent {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  ip?: string;
  userAgent?: string;
  endpoint?: string;
  method?: string;
  userId?: string;
  payload?: any;
  metadata?: Record<string, any>;
}

// Function overloads to support both calling patterns
export async function logSecurityEvent(eventType: string, severity: 'low' | 'medium' | 'high' | 'critical', details?: Record<string, any>): Promise<void>;
export async function logSecurityEvent(event: SecurityEvent): Promise<void>;
export async function logSecurityEvent(
  eventTypeOrEvent: string | SecurityEvent,
  severity?: 'low' | 'medium' | 'high' | 'critical',
  details?: Record<string, any>
): Promise<void> {
  try {
    let event: SecurityEvent;
    
    if (typeof eventTypeOrEvent === 'string') {
      // Called as logSecurityEvent('event_type', 'severity', { details })
      event = {
        type: eventTypeOrEvent,
        severity: severity!,
        metadata: details,
      };
    } else {
      // Called as logSecurityEvent({ type: '...', severity: '...', ... })
      event = eventTypeOrEvent;
    }

    const { error } = await supabase
      .from('auth_attempts')
      .insert({
        attempt_type: event.type,
        success: false,
        ip_address: event.ip,
        user_agent: event.userAgent,
        user_id: event.userId,
        error_message: JSON.stringify({
          severity: event.severity,
          endpoint: event.endpoint,
          method: event.method,
          payload: event.payload,
          metadata: event.metadata,
        }),
        created_at: new Date().toISOString(),
      });
    
    if (error) {
      console.error('Failed to log security event:', error);
    }
    
    // Send alert for high/critical severity events
    if (['high', 'critical'].includes(event.severity)) {
      await sendSecurityAlert(event);
    }
  } catch (error) {
    console.error('Security logging error:', error);
  }
}

// Function overloads to support both calling patterns
export async function logAuditEvent(action: string, resource: string, resourceId?: string | null, details?: Record<string, any>): Promise<void>;
export async function logAuditEvent(params: {
  action: string;
  resource: string;
  resourceId?: string;
  userId?: string;
  changes?: Record<string, any>;
  metadata?: Record<string, any>;
}): Promise<void>;
export async function logAuditEvent(
  actionOrParams: string | {
    action: string;
    resource: string;
    resourceId?: string;
    userId?: string;
    changes?: Record<string, any>;
    metadata?: Record<string, any>;
  },
  resource?: string,
  resourceId?: string | null,
  details?: Record<string, any>
): Promise<void> {
  try {
    let auditData: {
      action: string;
      resource: string;
      resourceId?: string | null;
      userId?: string;
      changes?: Record<string, any>;
      metadata?: Record<string, any>;
    };

    if (typeof actionOrParams === 'string') {
      // Called as logAuditEvent('action', 'resource', 'id', { details })
      auditData = {
        action: actionOrParams,
        resource: resource!,
        resourceId,
        metadata: details,
      };
    } else {
      // Called as logAuditEvent({ action: '...', resource: '...', ... })
      auditData = actionOrParams;
    }

    // Use auth_attempts table as a fallback for audit logging
    const { error } = await supabase
      .from('auth_attempts')
      .insert({
        attempt_type: `audit_${auditData.action}`,
        success: true,
        user_id: auditData.userId,
        error_message: JSON.stringify({
          resource: auditData.resource,
          resource_id: auditData.resourceId,
          changes: auditData.changes,
          metadata: auditData.metadata,
        }),
        created_at: new Date().toISOString(),
      });
    
    if (error) {
      console.error('Failed to log audit event:', error);
    }
  } catch (error) {
    console.error('Audit logging error:', error);
  }
}

/**
 * SECURITY ALERT SYSTEM
 */
async function sendSecurityAlert(event: SecurityEvent): Promise<void> {
  // Implementation would depend on your notification system
  // This is a placeholder for the alert mechanism
  console.warn('SECURITY ALERT:', {
    type: event.type,
    severity: event.severity,
    timestamp: new Date().toISOString(),
    details: event,
  });
  
  // In a real implementation, you would:
  // 1. Send email to security team
  // 2. Send SMS for critical alerts
  // 3. Create incident in monitoring system
  // 4. Log to external SIEM system
}

/**
 * STANDALONE INPUT SANITIZATION
 */
export const sanitizeInput = (input: string): string => {
  return InputValidator.sanitizeInput(input);
};

/**
 * BRUTE FORCE PROTECTION
 */
export class BruteForceProtection {
  private static attempts = new Map<string, { count: number; lastAttempt: Date; lockedUntil?: Date }>();
  
  static async recordFailedAttempt(identifier: string, type: 'login' | 'pin' | 'otp' = 'login'): Promise<void> {
    const key = `${type}:${identifier}`;
    const now = new Date();
    const existing = this.attempts.get(key) || { count: 0, lastAttempt: now };
    
    // Reset count if last attempt was more than lockout period ago
    const lockoutMinutes = SECURITY_CONFIG.RATE_LIMITS.AUTH.LOGIN.windowMs / (60 * 1000);
    if (now.getTime() - existing.lastAttempt.getTime() > lockoutMinutes * 60 * 1000) {
      existing.count = 0;
    }
    
    existing.count++;
    existing.lastAttempt = now;
    
    // Lock account if too many attempts
    const maxAttempts = SECURITY_CONFIG.RATE_LIMITS.AUTH.LOGIN.max;
    if (existing.count >= maxAttempts) {
      existing.lockedUntil = new Date(now.getTime() + lockoutMinutes * 60 * 1000);
      
      // Log security event
      await logSecurityEvent({
        type: 'account_locked',
        severity: 'high',
        metadata: {
          identifier,
          type,
          attemptCount: existing.count,
          lockedUntil: existing.lockedUntil,
        },
      });
    }
    
    this.attempts.set(key, existing);
  }
  
  static isLocked(identifier: string, type: 'login' | 'pin' | 'otp' = 'login'): boolean {
    const key = `${type}:${identifier}`;
    const attempt = this.attempts.get(key);
    
    if (!attempt || !attempt.lockedUntil) {
      return false;
    }
    
    const now = new Date();
    if (now > attempt.lockedUntil) {
      // Lock has expired, clear it
      attempt.lockedUntil = undefined;
      attempt.count = 0;
      this.attempts.set(key, attempt);
      return false;
    }
    
    return true;
  }
  
  static clearAttempts(identifier: string, type: 'login' | 'pin' | 'otp' = 'login'): void {
    const key = `${type}:${identifier}`;
    this.attempts.delete(key);
  }
  
  static getRemainingLockTime(identifier: string, type: 'login' | 'pin' | 'otp' = 'login'): number {
    const key = `${type}:${identifier}`;
    const attempt = this.attempts.get(key);
    
    if (!attempt || !attempt.lockedUntil) {
      return 0;
    }
    
    const now = new Date();
    return Math.max(0, attempt.lockedUntil.getTime() - now.getTime());
  }
}

// Export all middleware and utilities
export default {
  securityHeaders,
  authRateLimits,
  apiRateLimits,
  paymentRateLimits,
  InputValidator,
  preventSQLInjection,
  xssProtection,
  EncryptionUtils,
  logSecurityEvent,
  logAuditEvent,
  BruteForceProtection,
};