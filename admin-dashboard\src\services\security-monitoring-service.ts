/**
 * Security Monitoring Service
 * 
 * This service provides comprehensive security monitoring including:
 * - Real-time login attempt tracking
 * - Anomaly detection and behavioral analysis
 * - Device fingerprinting and trust scoring
 * - Security incident response and alerting
 * - Threat intelligence and risk assessment
 */

import { riskBasedAuthService, type DeviceFingerprint, type RiskAssessment } from './risk-based-auth-service'

// Types
export interface SecurityEvent {
  id: string
  type: SecurityEventType
  severity: 'low' | 'medium' | 'high' | 'critical'
  timestamp: Date
  userId?: string
  sessionId?: string
  deviceFingerprint?: DeviceFingerprint
  ipAddress: string
  userAgent: string
  location?: {
    country: string
    region: string
    city: string
  }
  details: Record<string, any>
  resolved: boolean
  resolvedAt?: Date
  resolvedBy?: string
  notes?: string
}

export type SecurityEventType =
  | 'login_attempt'
  | 'login_success'
  | 'login_failure'
  | 'suspicious_activity'
  | 'brute_force_attempt'
  | 'account_lockout'
  | 'new_device_detected'
  | 'impossible_travel'
  | 'anomalous_behavior'
  | 'security_breach'
  | 'unauthorized_access'
  | 'data_exfiltration'
  | 'privilege_escalation'
  | 'malware_detected'
  | 'phishing_attempt'

export interface LoginAttempt {
  id: string
  userId?: string
  email: string
  timestamp: Date
  success: boolean
  method: string
  deviceFingerprint: DeviceFingerprint
  ipAddress: string
  userAgent: string
  location?: {
    country: string
    region: string
    city: string
  }
  riskAssessment: RiskAssessment
  failureReason?: string
  sessionId?: string
  duration?: number
}

export interface AnomalyDetection {
  id: string
  type: 'behavioral' | 'temporal' | 'geographical' | 'device' | 'access_pattern'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  userId: string
  timestamp: Date
  baseline: any
  observed: any
  confidence: number
  mitigation: string[]
}

export interface SecurityMetrics {
  totalLoginAttempts: number
  successfulLogins: number
  failedLogins: number
  blockedAttempts: number
  uniqueDevices: number
  uniqueLocations: number
  suspiciousActivities: number
  securityIncidents: number
  averageRiskScore: number
  topThreats: string[]
  timeRange: {
    start: Date
    end: Date
  }
}

export interface ThreatIntelligence {
  ipAddress: string
  threatLevel: 'low' | 'medium' | 'high' | 'critical'
  categories: string[]
  lastSeen: Date
  reputation: number
  sources: string[]
  details: {
    isVpn: boolean
    isTor: boolean
    isProxy: boolean
    isMalware: boolean
    isPhishing: boolean
    isBotnet: boolean
  }
}

class SecurityMonitoringService {
  private events: SecurityEvent[] = []
  private loginAttempts: LoginAttempt[] = []
  private anomalies: AnomalyDetection[] = []
  private threatIntel: Map<string, ThreatIntelligence> = new Map()
  private alertSubscribers: ((event: SecurityEvent) => void)[] = []

  // Real-time monitoring thresholds
  private readonly thresholds = {
    maxFailedAttempts: 5,
    maxAttemptsPerMinute: 10,
    maxDevicesPerUser: 5,
    suspiciousRiskScore: 0.7,
    criticalRiskScore: 0.9,
    anomalyConfidence: 0.8,
  }

  /**
   * Initialize security monitoring
   */
  async initialize(): Promise<void> {
    // Start real-time monitoring
    this.startRealTimeMonitoring()
    
    // Load threat intelligence
    await this.loadThreatIntelligence()
    
    // Initialize anomaly detection baselines
    await this.initializeAnomalyBaselines()
  }

  /**
   * Record a login attempt
   */
  async recordLoginAttempt(attempt: Omit<LoginAttempt, 'id' | 'riskAssessment'>): Promise<LoginAttempt> {
    // Generate risk assessment
    const riskAssessment = await riskBasedAuthService.assessLoginRisk(
      attempt.deviceFingerprint,
      await this.isNewDevice(attempt.deviceFingerprint.id),
      await this.isNewLocation(attempt.ipAddress),
      await this.getRecentFailedAttempts(attempt.email)
    )

    const loginAttempt: LoginAttempt = {
      ...attempt,
      id: this.generateId(),
      riskAssessment,
    }

    this.loginAttempts.push(loginAttempt)

    // Create security event
    const eventType: SecurityEventType = attempt.success ? 'login_success' : 'login_failure'
    await this.createSecurityEvent({
      type: eventType,
      severity: this.calculateEventSeverity(riskAssessment, attempt.success),
      userId: attempt.userId,
      deviceFingerprint: attempt.deviceFingerprint,
      ipAddress: attempt.ipAddress,
      userAgent: attempt.userAgent,
      location: attempt.location,
      details: {
        email: attempt.email,
        method: attempt.method,
        riskScore: riskAssessment.riskScore,
        riskLevel: riskAssessment.riskLevel,
        failureReason: attempt.failureReason,
      },
    })

    // Check for suspicious patterns
    await this.detectSuspiciousPatterns(loginAttempt)

    // Update threat intelligence
    await this.updateThreatIntelligence(attempt.ipAddress, attempt.success)

    return loginAttempt
  }

  /**
   * Create a security event
   */
  async createSecurityEvent(
    eventData: Omit<SecurityEvent, 'id' | 'timestamp' | 'resolved'>
  ): Promise<SecurityEvent> {
    const event: SecurityEvent = {
      ...eventData,
      id: this.generateId(),
      timestamp: new Date(),
      resolved: false,
    }

    this.events.push(event)

    // Trigger real-time alerts for high severity events
    if (event.severity === 'high' || event.severity === 'critical') {
      await this.triggerAlert(event)
    }

    return event
  }

  /**
   * Detect anomalous behavior
   */
  async detectAnomalies(userId: string, currentActivity: any): Promise<AnomalyDetection[]> {
    const detectedAnomalies: AnomalyDetection[] = []

    // Get user's baseline behavior
    const baseline = await this.getUserBaseline(userId)
    if (!baseline) return detectedAnomalies

    // Temporal anomaly detection
    const temporalAnomaly = this.detectTemporalAnomaly(baseline, currentActivity)
    if (temporalAnomaly) {
      detectedAnomalies.push(temporalAnomaly)
    }

    // Behavioral anomaly detection
    const behavioralAnomaly = this.detectBehavioralAnomaly(baseline, currentActivity)
    if (behavioralAnomaly) {
      detectedAnomalies.push(behavioralAnomaly)
    }

    // Geographical anomaly detection
    const geographicalAnomaly = this.detectGeographicalAnomaly(baseline, currentActivity)
    if (geographicalAnomaly) {
      detectedAnomalies.push(geographicalAnomaly)
    }

    // Device anomaly detection
    const deviceAnomaly = this.detectDeviceAnomaly(baseline, currentActivity)
    if (deviceAnomaly) {
      detectedAnomalies.push(deviceAnomaly)
    }

    // Store detected anomalies
    this.anomalies.push(...detectedAnomalies)

    // Create security events for high-confidence anomalies
    for (const anomaly of detectedAnomalies) {
      if (anomaly.confidence >= this.thresholds.anomalyConfidence) {
        await this.createSecurityEvent({
          type: 'anomalous_behavior',
          severity: anomaly.severity,
          userId,
          details: {
            anomalyType: anomaly.type,
            description: anomaly.description,
            confidence: anomaly.confidence,
            baseline: anomaly.baseline,
            observed: anomaly.observed,
          },
          ipAddress: currentActivity.ipAddress || 'unknown',
          userAgent: currentActivity.userAgent || 'unknown',
        })
      }
    }

    return detectedAnomalies
  }

  /**
   * Get security metrics for dashboard
   */
  async getSecurityMetrics(timeRange: { start: Date; end: Date }): Promise<SecurityMetrics> {
    const attemptsInRange = this.loginAttempts.filter(
      attempt => attempt.timestamp >= timeRange.start && attempt.timestamp <= timeRange.end
    )

    const eventsInRange = this.events.filter(
      event => event.timestamp >= timeRange.start && event.timestamp <= timeRange.end
    )

    const successfulLogins = attemptsInRange.filter(attempt => attempt.success).length
    const failedLogins = attemptsInRange.filter(attempt => !attempt.success).length
    const blockedAttempts = attemptsInRange.filter(
      attempt => attempt.riskAssessment.riskLevel === 'critical'
    ).length

    const uniqueDevices = new Set(
      attemptsInRange.map(attempt => attempt.deviceFingerprint.id)
    ).size

    const uniqueLocations = new Set(
      attemptsInRange
        .filter(attempt => attempt.location)
        .map(attempt => `${attempt.location!.country}-${attempt.location!.region}`)
    ).size

    const suspiciousActivities = eventsInRange.filter(
      event => event.type === 'suspicious_activity'
    ).length

    const securityIncidents = eventsInRange.filter(
      event => event.severity === 'high' || event.severity === 'critical'
    ).length

    const averageRiskScore = attemptsInRange.length > 0
      ? attemptsInRange.reduce((sum, attempt) => sum + attempt.riskAssessment.riskScore, 0) / attemptsInRange.length
      : 0

    const threatCounts = new Map<string, number>()
    eventsInRange.forEach(event => {
      const count = threatCounts.get(event.type) || 0
      threatCounts.set(event.type, count + 1)
    })

    const topThreats = Array.from(threatCounts.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([threat]) => threat)

    return {
      totalLoginAttempts: attemptsInRange.length,
      successfulLogins,
      failedLogins,
      blockedAttempts,
      uniqueDevices,
      uniqueLocations,
      suspiciousActivities,
      securityIncidents,
      averageRiskScore,
      topThreats,
      timeRange,
    }
  }

  /**
   * Get real-time security alerts
   */
  getRealtimeAlerts(): SecurityEvent[] {
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)

    return this.events.filter(
      event => 
        event.timestamp >= oneHourAgo && 
        (event.severity === 'high' || event.severity === 'critical') &&
        !event.resolved
    )
  }

  /**
   * Subscribe to security alerts
   */
  subscribeToAlerts(callback: (event: SecurityEvent) => void): () => void {
    this.alertSubscribers.push(callback)
    
    return () => {
      const index = this.alertSubscribers.indexOf(callback)
      if (index > -1) {
        this.alertSubscribers.splice(index, 1)
      }
    }
  }

  /**
   * Resolve a security incident
   */
  async resolveIncident(eventId: string, resolvedBy: string, notes?: string): Promise<boolean> {
    const event = this.events.find(e => e.id === eventId)
    if (!event) return false

    event.resolved = true
    event.resolvedAt = new Date()
    event.resolvedBy = resolvedBy
    event.notes = notes

    return true
  }

  /**
   * Generate security report
   */
  async generateSecurityReport(timeRange: { start: Date; end: Date }): Promise<{
    metrics: SecurityMetrics
    topThreats: SecurityEvent[]
    anomalies: AnomalyDetection[]
    recommendations: string[]
  }> {
    const metrics = await this.getSecurityMetrics(timeRange)
    
    const topThreats = this.events
      .filter(event => 
        event.timestamp >= timeRange.start && 
        event.timestamp <= timeRange.end &&
        (event.severity === 'high' || event.severity === 'critical')
      )
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, 10)

    const anomalies = this.anomalies.filter(
      anomaly => 
        anomaly.timestamp >= timeRange.start && 
        anomaly.timestamp <= timeRange.end
    )

    const recommendations = this.generateSecurityRecommendations(metrics, topThreats, anomalies)

    return {
      metrics,
      topThreats,
      anomalies,
      recommendations,
    }
  }

  // Private helper methods
  private startRealTimeMonitoring(): void {
    // Monitor for brute force attacks
    setInterval(() => {
      this.detectBruteForceAttacks()
    }, 60000) // Check every minute

    // Monitor for impossible travel
    setInterval(() => {
      this.detectImpossibleTravel()
    }, 30000) // Check every 30 seconds

    // Clean up old events
    setInterval(() => {
      this.cleanupOldEvents()
    }, 3600000) // Clean up every hour
  }

  private async loadThreatIntelligence(): Promise<void> {
    // In a real implementation, this would load from threat intelligence feeds
    // For now, we'll use a mock implementation
  }

  private async initializeAnomalyBaselines(): Promise<void> {
    // Initialize machine learning baselines for anomaly detection
    // This would typically involve loading historical data and training models
  }

  private async detectSuspiciousPatterns(attempt: LoginAttempt): Promise<void> {
    // Check for brute force attempts
    const recentFailures = await this.getRecentFailedAttempts(attempt.email, 300000) // 5 minutes
    if (recentFailures >= this.thresholds.maxFailedAttempts) {
      await this.createSecurityEvent({
        type: 'brute_force_attempt',
        severity: 'high',
        userId: attempt.userId,
        deviceFingerprint: attempt.deviceFingerprint,
        ipAddress: attempt.ipAddress,
        userAgent: attempt.userAgent,
        details: {
          email: attempt.email,
          failedAttempts: recentFailures,
        },
      })
    }

    // Check for high-risk logins
    if (attempt.riskAssessment.riskScore >= this.thresholds.suspiciousRiskScore) {
      await this.createSecurityEvent({
        type: 'suspicious_activity',
        severity: attempt.riskAssessment.riskScore >= this.thresholds.criticalRiskScore ? 'critical' : 'high',
        userId: attempt.userId,
        deviceFingerprint: attempt.deviceFingerprint,
        ipAddress: attempt.ipAddress,
        userAgent: attempt.userAgent,
        details: {
          riskScore: attempt.riskAssessment.riskScore,
          riskFactors: attempt.riskAssessment.factors,
        },
      })
    }
  }

  private async triggerAlert(event: SecurityEvent): Promise<void> {
    // Notify all subscribers
    this.alertSubscribers.forEach(callback => {
      try {
        callback(event)
      } catch (error) {
        console.error('Error in alert callback:', error)
      }
    })

    // Send to external monitoring systems
    await this.sendToExternalMonitoring(event)
  }

  private async sendToExternalMonitoring(event: SecurityEvent): Promise<void> {
    try {
      // Send to security information and event management (SIEM) system
      await fetch('/api/security/siem', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(event),
      })
    } catch (error) {
      console.error('Failed to send event to SIEM:', error)
    }
  }

  private calculateEventSeverity(riskAssessment: RiskAssessment, success: boolean): 'low' | 'medium' | 'high' | 'critical' {
    if (!success && riskAssessment.riskLevel === 'critical') return 'critical'
    if (!success && riskAssessment.riskLevel === 'high') return 'high'
    if (success && riskAssessment.riskLevel === 'high') return 'medium'
    return 'low'
  }

  private async isNewDevice(deviceId: string): Promise<boolean> {
    const existingAttempts = this.loginAttempts.filter(
      attempt => attempt.deviceFingerprint.id === deviceId
    )
    return existingAttempts.length === 0
  }

  private async isNewLocation(ipAddress: string): Promise<boolean> {
    const existingAttempts = this.loginAttempts.filter(
      attempt => attempt.ipAddress === ipAddress
    )
    return existingAttempts.length === 0
  }

  private async getRecentFailedAttempts(email: string, timeWindowMs: number = 900000): Promise<number> {
    const cutoff = new Date(Date.now() - timeWindowMs)
    return this.loginAttempts.filter(
      attempt => 
        attempt.email === email && 
        !attempt.success && 
        attempt.timestamp >= cutoff
    ).length
  }

  private async getUserBaseline(userId: string): Promise<any> {
    // In a real implementation, this would load user behavioral baselines
    return null
  }

  private detectTemporalAnomaly(baseline: any, current: any): AnomalyDetection | null {
    // Implement temporal anomaly detection logic
    return null
  }

  private detectBehavioralAnomaly(baseline: any, current: any): AnomalyDetection | null {
    // Implement behavioral anomaly detection logic
    return null
  }

  private detectGeographicalAnomaly(baseline: any, current: any): AnomalyDetection | null {
    // Implement geographical anomaly detection logic
    return null
  }

  private detectDeviceAnomaly(baseline: any, current: any): AnomalyDetection | null {
    // Implement device anomaly detection logic
    return null
  }

  private async detectBruteForceAttacks(): Promise<void> {
    // Implement brute force detection logic
  }

  private async detectImpossibleTravel(): Promise<void> {
    // Implement impossible travel detection logic
  }

  private cleanupOldEvents(): void {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    this.events = this.events.filter(event => event.timestamp >= thirtyDaysAgo)
    this.loginAttempts = this.loginAttempts.filter(attempt => attempt.timestamp >= thirtyDaysAgo)
    this.anomalies = this.anomalies.filter(anomaly => anomaly.timestamp >= thirtyDaysAgo)
  }

  private async updateThreatIntelligence(ipAddress: string, success: boolean): Promise<void> {
    // Update threat intelligence based on login success/failure
  }

  private generateSecurityRecommendations(
    metrics: SecurityMetrics,
    threats: SecurityEvent[],
    anomalies: AnomalyDetection[]
  ): string[] {
    const recommendations: string[] = []

    if (metrics.failedLogins > metrics.successfulLogins * 0.3) {
      recommendations.push('High failure rate detected - consider implementing additional security measures')
    }

    if (metrics.averageRiskScore > 0.6) {
      recommendations.push('Average risk score is elevated - review authentication policies')
    }

    if (threats.length > 10) {
      recommendations.push('Multiple security incidents detected - conduct security review')
    }

    if (anomalies.length > 5) {
      recommendations.push('Unusual user behavior patterns detected - investigate further')
    }

    return recommendations
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9) + Date.now().toString(36)
  }
}

// Export singleton instance
export const securityMonitoringService = new SecurityMonitoringService()
 