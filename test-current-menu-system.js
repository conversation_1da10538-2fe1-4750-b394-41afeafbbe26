// Test Menu System with Current Schema
// This script tests the menu functionality using the current database schema (with menu_items table)

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://voiwzwyfnkzvcffuxpwl.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0MDU2MzIsImV4cCI6MjA2NDk4MTYzMn0.RvabQ6EGP1gBb0w8J8_VpaqqXgkpEFR7Nbbkmw11wNA';
const supabase = createClient(supabaseUrl, supabaseKey);

async function testCurrentMenuSystem() {
    console.log('🧪 Testing Current Menu System...\n');
    
    const tests = [
        {
            name: 'Test menu_categories table access',
            test: async () => {
                const { data, error } = await supabase
                    .from('menu_categories')
                    .select('*')
                    .limit(5);
                
                if (error) throw error;
                console.log(`✅ menu_categories: Found ${data.length} categories`);
                
                // Check if category_type column exists
                if (data.length > 0) {
                    const hasTypeColumn = 'category_type' in data[0];
                    console.log(`   category_type column: ${hasTypeColumn ? '✅ Present' : '❌ Missing'}`);
                }
                
                return data;
            }
        },
        {
            name: 'Test menu_items table access (current schema)',
            test: async () => {
                const { data, error } = await supabase
                    .from('menu_items')
                    .select('*')
                    .limit(5);
                
                if (error) throw error;
                console.log(`✅ menu_items: Found ${data.length} items`);
                return data;
            }
        },
        {
            name: 'Test subcategories table access (new schema)',
            test: async () => {
                const { data, error } = await supabase
                    .from('subcategories')
                    .select('*')
                    .limit(5);
                
                if (error) {
                    console.log(`❌ subcategories: ${error.message}`);
                    return null;
                } else {
                    console.log(`✅ subcategories: Found ${data.length} items`);
                    return data;
                }
            }
        },
        {
            name: 'Test ingredient_categories table access',
            test: async () => {
                const { data, error } = await supabase
                    .from('ingredient_categories')
                    .select('*')
                    .limit(5);
                
                if (error) throw error;
                console.log(`✅ ingredient_categories: Found ${data.length} categories`);
                return data;
            }
        },
        {
            name: 'Test ingredients table access',
            test: async () => {
                const { data, error } = await supabase
                    .from('ingredients')
                    .select('*')
                    .limit(5);
                
                if (error) throw error;
                console.log(`✅ ingredients: Found ${data.length} ingredients`);
                return data;
            }
        },
        {
            name: 'Test menu_item_ingredients table access',
            test: async () => {
                const { data, error } = await supabase
                    .from('menu_item_ingredients')
                    .select('*')
                    .limit(5);
                
                if (error) throw error;
                console.log(`✅ menu_item_ingredients: Found ${data.length} relationships`);
                return data;
            }
        },
        {
            name: 'Test customization_presets table access',
            test: async () => {
                const { data, error } = await supabase
                    .from('customization_presets')
                    .select('*')
                    .limit(5);
                
                if (error) throw error;
                console.log(`✅ customization_presets: Found ${data.length} presets`);
                return data;
            }
        },
        {
            name: 'Test order_items table access',
            test: async () => {
                const { data, error } = await supabase
                    .from('order_items')
                    .select('*')
                    .limit(5);
                
                if (error) throw error;
                console.log(`✅ order_items: Found ${data.length} order items`);
                return data;
            }
        },
        {
            name: 'Test get_menu_categories_with_items function',
            test: async () => {
                const { data, error } = await supabase
                    .rpc('get_menu_categories_with_items');
                
                if (error) {
                    console.log(`❌ get_menu_categories_with_items: ${error.message}`);
                    return null;
                } else {
                    console.log(`✅ get_menu_categories_with_items: Found ${data.length} results`);
                    return data;
                }
            }
        },
        {
            name: 'Test test_menu_table_access function',
            test: async () => {
                const { data, error } = await supabase
                    .rpc('test_menu_table_access');
                
                if (error) {
                    console.log(`❌ test_menu_table_access: ${error.message}`);
                    return null;
                } else {
                    console.log(`✅ test_menu_table_access: Function executed successfully`);
                    if (data && data.length > 0) {
                        data.forEach(row => {
                            console.log(`   ${row.table_name}: SELECT=${row.can_select}, INSERT=${row.can_insert}, UPDATE=${row.can_update}, DELETE=${row.can_delete}`);
                        });
                    }
                    return data;
                }
            }
        }
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    for (const test of tests) {
        try {
            console.log(`\n🔍 ${test.name}:`);
            await test.test();
            passedTests++;
        } catch (error) {
            console.log(`❌ ${test.name}: ${error.message}`);
        }
    }
    
    console.log(`\n📊 Test Summary:`);
    console.log(`   Passed: ${passedTests}/${totalTests}`);
    console.log(`   Failed: ${totalTests - passedTests}/${totalTests}`);
    
    if (passedTests === totalTests) {
        console.log(`\n🎉 All tests passed! Menu system is working correctly.`);
    } else {
        console.log(`\n⚠️  Some tests failed. Check the migration status and database schema.`);
        
        console.log(`\n📋 Next Steps:`);
        console.log(`   1. Apply the migration: 20241230000000_fix_menu_system_complete.sql`);
        console.log(`   2. Ensure all RLS policies are properly configured`);
        console.log(`   3. Verify that all required functions exist`);
        console.log(`   4. Check foreign key constraints are updated`);
    }
    
    return {
        passed: passedTests,
        total: totalTests,
        success: passedTests === totalTests
    };
}

// Run the test
if (require.main === module) {
    testCurrentMenuSystem()
        .then(result => {
            process.exit(result.success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ Test execution failed:', error);
            process.exit(1);
        });
}

module.exports = { testCurrentMenuSystem };