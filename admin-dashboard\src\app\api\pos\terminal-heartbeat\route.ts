import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const heartbeatData = await request.json()

    // Validate required fields
    if (!heartbeatData.terminal_id) {
      return NextResponse.json(
        { error: 'terminal_id is required' },
        { status: 400 }
      )
    }

    // Update terminal status in pos_terminals table
    const { error: terminalError } = await supabase
      .from('pos_terminals')
      .upsert({
        terminal_id: heartbeatData.terminal_id,
        name: heartbeatData.name || `POS Terminal ${heartbeatData.terminal_id.slice(-8)}`,
        location: heartbeatData.location || 'Main Store',
        ip_address: heartbeatData.ip_address || request.ip || 'unknown',
        status: heartbeatData.status || 'online',
        version: heartbeatData.version || '1.0.0',
        last_heartbeat: new Date().toISOString(),
        uptime: heartbeatData.uptime || 0,
        memory_usage: heartbeatData.memory_usage || 0,
        cpu_usage: heartbeatData.cpu_usage || 0,
        disk_usage: heartbeatData.disk_usage || 0,
        settings_hash: heartbeatData.settings_hash || '0',
        sync_status: heartbeatData.sync_status || 'synced',
        pending_updates: heartbeatData.pending_updates || 0,
        network_info: heartbeatData.network_info || {},
        performance_metrics: heartbeatData.performance_metrics || {},
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'terminal_id'
      })

    if (terminalError) {
      console.error('Error updating terminal:', terminalError)
      return NextResponse.json(
        { error: 'Failed to update terminal status' },
        { status: 500 }
      )
    }

    // Record heartbeat in pos_heartbeats table
    const { error: heartbeatError } = await supabase
      .from('pos_heartbeats')
      .insert({
        terminal_id: heartbeatData.terminal_id,
        timestamp: new Date().toISOString(),
        status: heartbeatData.status || 'online',
        uptime: heartbeatData.uptime || 0,
        memory_usage: heartbeatData.memory_usage || 0,
        cpu_usage: heartbeatData.cpu_usage || 0,
        disk_usage: heartbeatData.disk_usage || 0,
        network_latency: heartbeatData.network_info?.latency || 0,
        sync_queue_size: heartbeatData.pending_updates || 0,
        error_count: 0,
        metadata: {
          version: heartbeatData.version,
          settings_hash: heartbeatData.settings_hash,
          network_info: heartbeatData.network_info,
          performance_metrics: heartbeatData.performance_metrics
        }
      })

    if (heartbeatError) {
      console.error('Error recording heartbeat:', heartbeatError)
      // Don't fail the request if heartbeat recording fails
    }

    // Return success response with any pending sync operations
    return NextResponse.json({
      success: true,
      message: 'Heartbeat received',
      timestamp: new Date().toISOString(),
      terminal_id: heartbeatData.terminal_id,
      pending_sync_operations: [], // TODO: Implement pending sync operations
      configuration_updates: [] // TODO: Implement configuration updates
    })

  } catch (error) {
    console.error('Terminal heartbeat error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
