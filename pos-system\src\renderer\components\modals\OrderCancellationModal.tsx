import React, { useState } from 'react';
import toast from 'react-hot-toast';

interface OrderCancellationModalProps {
  isOpen: boolean;
  orderCount: number;
  onConfirmCancel: (reason: string) => void;
  onClose: () => void;
}

export const OrderCancellationModal: React.FC<OrderCancellationModalProps> = ({
  isOpen,
  orderCount,
  onConfirmCancel,
  onClose
}) => {
  const [cancelReason, setCancelReason] = useState('');

  if (!isOpen) return null;

  const handleConfirm = () => {
    if (!cancelReason.trim()) {
      toast.error('Please provide a reason for cancellation');
      return;
    }
    onConfirmCancel(cancelReason);
    setCancelReason(''); // Reset form
  };

  const handleClose = () => {
    setCancelReason(''); // Reset form on close
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-2xl p-6 w-full max-w-md border border-gray-200/50 dark:border-white/10">
        <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
          Cancel Orders
        </h3>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          You are about to cancel {orderCount} order(s). Please provide a reason for the cancellation.
        </p>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Cancellation Reason *
          </label>
          <textarea
            value={cancelReason}
            onChange={(e) => setCancelReason(e.target.value)}
            placeholder="Enter reason for cancellation..."
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-none"
            rows={4}
            maxLength={500}
          />
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {cancelReason.length}/500 characters
          </div>
        </div>
        
        <div className="flex gap-3">
          <button
            onClick={handleClose}
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            disabled={!cancelReason.trim()}
            className={`
              flex-1 px-4 py-2 rounded-lg font-medium transition-colors
              ${cancelReason.trim()
                ? 'bg-red-500 hover:bg-red-600 text-white'
                : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              }
            `}
          >
            Confirm Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default OrderCancellationModal; 