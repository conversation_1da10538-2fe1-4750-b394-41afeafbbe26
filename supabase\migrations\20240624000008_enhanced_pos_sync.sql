-- Enhanced POS Synchronization Tables
-- This migration adds tables for terminal management, heartbeat monitoring, and enhanced settings sync

-- POS Terminals Table
CREATE TABLE IF NOT EXISTS pos_terminals (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    terminal_id text UNIQUE NOT NULL,
    name text NOT NULL,
    location text NOT NULL,
    ip_address inet NOT NULL,
    mac_address text,
    status text NOT NULL DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'syncing', 'error')),
    last_heartbeat timestamp with time zone DEFAULT now(),
    version text NOT NULL DEFAULT '1.0.0',
    uptime bigint DEFAULT 0, -- seconds
    settings_version integer DEFAULT 0,
    branch_id uuid REFERENCES branches(id),
    is_active boolean DEFAULT true,
    error_message text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- POS Heartbeats Table for monitoring
CREATE TABLE IF NOT EXISTS pos_heartbeats (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    terminal_id text NOT NULL REFERENCES pos_terminals(terminal_id) ON DELETE CASCADE,
    timestamp timestamp with time zone NOT NULL,
    status text NOT NULL CHECK (status IN ('online', 'offline', 'error')),
    version text NOT NULL,
    uptime bigint NOT NULL,
    memory_usage numeric(5,2), -- percentage
    cpu_usage numeric(5,2), -- percentage
    settings_hash text, -- hash of current settings for comparison
    sync_status text NOT NULL DEFAULT 'synced' CHECK (sync_status IN ('synced', 'pending', 'failed')),
    pending_updates integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now()
);

-- Enhanced POS Configurations Table
-- This replaces the basic pos_configurations with enhanced structure
DROP TABLE IF EXISTS pos_configurations;
CREATE TABLE pos_configurations (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    terminal_id text NOT NULL REFERENCES pos_terminals(terminal_id) ON DELETE CASCADE,
    setting_category text NOT NULL CHECK (setting_category IN (
        'terminal', 'printer', 'tax', 'discount', 'receipt', 
        'payment', 'inventory', 'staff', 'restaurant'
    )),
    setting_key text NOT NULL,
    setting_value jsonb NOT NULL,
    description text,
    is_active boolean DEFAULT true,
    sync_status text NOT NULL DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'failed')),
    settings_version integer DEFAULT 0,
    last_sync_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    updated_by uuid,
    UNIQUE(terminal_id, setting_category, setting_key)
);

-- Settings Sync History for audit trail
CREATE TABLE IF NOT EXISTS pos_settings_sync_history (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    terminal_id text NOT NULL REFERENCES pos_terminals(terminal_id) ON DELETE CASCADE,
    setting_category text NOT NULL,
    operation text NOT NULL CHECK (operation IN ('create', 'update', 'delete', 'sync')),
    old_value jsonb,
    new_value jsonb,
    sync_result text NOT NULL CHECK (sync_result IN ('success', 'failed', 'pending')),
    error_message text,
    settings_version integer NOT NULL,
    synced_at timestamp with time zone DEFAULT now(),
    synced_by uuid
);

-- Enhanced Payment Settings with POS integration
CREATE TABLE IF NOT EXISTS enhanced_payment_settings (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    terminal_id text REFERENCES pos_terminals(terminal_id) ON DELETE CASCADE,
    branch_id uuid REFERENCES branches(id),
    provider text NOT NULL,
    provider_config jsonb NOT NULL,
    is_enabled boolean DEFAULT true,
    processing_fee_percentage numeric(5,4), -- up to 99.99%
    processing_fee_fixed numeric(10,2), -- fixed fee amount
    daily_limit numeric(10,2),
    transaction_limit numeric(10,2),
    requires_signature_above numeric(10,2),
    auto_settle boolean DEFAULT true,
    settlement_time time,
    tip_enabled boolean DEFAULT true,
    tip_percentages integer[] DEFAULT '{15,18,20,25}',
    contactless_enabled boolean DEFAULT true,
    mobile_payments_enabled boolean DEFAULT true,
    cash_enabled boolean DEFAULT true,
    card_enabled boolean DEFAULT true,
    split_payments boolean DEFAULT true,
    minimum_card_amount numeric(10,2) DEFAULT 0,
    surcharge_enabled boolean DEFAULT false,
    surcharge_percentage numeric(5,4) DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    updated_by uuid
);

-- POS System Logs for debugging
CREATE TABLE IF NOT EXISTS pos_system_logs (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    terminal_id text NOT NULL REFERENCES pos_terminals(terminal_id) ON DELETE CASCADE,
    log_level text NOT NULL CHECK (log_level IN ('debug', 'info', 'warn', 'error', 'fatal')),
    category text NOT NULL, -- settings, sync, payment, etc.
    message text NOT NULL,
    data jsonb,
    timestamp timestamp with time zone DEFAULT now()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_pos_terminals_status ON pos_terminals(status);
CREATE INDEX IF NOT EXISTS idx_pos_terminals_terminal_id ON pos_terminals(terminal_id);
CREATE INDEX IF NOT EXISTS idx_pos_heartbeats_terminal_id ON pos_heartbeats(terminal_id);
CREATE INDEX IF NOT EXISTS idx_pos_heartbeats_timestamp ON pos_heartbeats(timestamp);
CREATE INDEX IF NOT EXISTS idx_pos_configurations_terminal_category ON pos_configurations(terminal_id, setting_category);
CREATE INDEX IF NOT EXISTS idx_pos_configurations_sync_status ON pos_configurations(sync_status);
CREATE INDEX IF NOT EXISTS idx_pos_settings_sync_history_terminal ON pos_settings_sync_history(terminal_id);
CREATE INDEX IF NOT EXISTS idx_pos_system_logs_terminal_timestamp ON pos_system_logs(terminal_id, timestamp);

-- Triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_pos_terminals_updated_at 
    BEFORE UPDATE ON pos_terminals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pos_configurations_updated_at 
    BEFORE UPDATE ON pos_configurations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_enhanced_payment_settings_updated_at 
    BEFORE UPDATE ON enhanced_payment_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean old heartbeats (keep last 7 days)
CREATE OR REPLACE FUNCTION cleanup_old_heartbeats()
RETURNS void AS $$
BEGIN
    DELETE FROM pos_heartbeats 
    WHERE created_at < NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- Function to clean old system logs (keep last 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_logs()
RETURNS void AS $$
BEGIN
    DELETE FROM pos_system_logs 
    WHERE timestamp < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Default terminal registration (update existing hardcoded terminal)
INSERT INTO pos_terminals (
    terminal_id, 
    name, 
    location, 
    ip_address, 
    status, 
    version,
    settings_version
) VALUES (
    'terminal-001',
    'Main Terminal',
    'Front Counter',
    '*************'::inet,
    'offline',
    '1.0.0',
    1
) ON CONFLICT (terminal_id) DO UPDATE SET
    name = EXCLUDED.name,
    location = EXCLUDED.location,
    ip_address = EXCLUDED.ip_address,
    updated_at = now();

-- Insert default POS configurations for terminal-001
INSERT INTO pos_configurations (terminal_id, setting_category, setting_key, setting_value, settings_version) VALUES
-- Terminal Settings
('terminal-001', 'terminal', 'display_brightness', '80', 1),
('terminal-001', 'terminal', 'screen_timeout', '300', 1),
('terminal-001', 'terminal', 'audio_enabled', 'true', 1),
('terminal-001', 'terminal', 'receipt_auto_print', 'true', 1),
('terminal-001', 'terminal', 'cash_drawer_enabled', 'true', 1),
('terminal-001', 'terminal', 'barcode_scanner_enabled', 'false', 1),
('terminal-001', 'terminal', 'customer_display_enabled', 'false', 1),
('terminal-001', 'terminal', 'touch_sensitivity', '"medium"', 1),

-- Printer Settings
('terminal-001', 'printer', 'paper_size', '"80mm"', 1),
('terminal-001', 'printer', 'print_logo', 'true', 1),
('terminal-001', 'printer', 'print_order_number', 'true', 1),
('terminal-001', 'printer', 'print_date_time', 'true', 1),
('terminal-001', 'printer', 'copy_count', '1', 1),

-- Tax Settings
('terminal-001', 'tax', 'default_tax_rate', '8.25', 1),
('terminal-001', 'tax', 'tax_inclusive', 'false', 1),
('terminal-001', 'tax', 'tax_name', '"VAT"', 1),
('terminal-001', 'tax', 'tax_calculation_method', '"percentage"', 1),
('terminal-001', 'tax', 'auto_calculate_tax', 'true', 1),
('terminal-001', 'tax', 'tax_rounding', '"nearest_cent"', 1),

-- Payment Settings
('terminal-001', 'payment', 'cash_enabled', 'true', 1),
('terminal-001', 'payment', 'card_enabled', 'true', 1),
('terminal-001', 'payment', 'contactless_enabled', 'true', 1),
('terminal-001', 'payment', 'mobile_payments', 'true', 1),
('terminal-001', 'payment', 'tip_enabled', 'true', 1),
('terminal-001', 'payment', 'tip_percentages', '[15, 18, 20, 25]', 1),

-- Receipt Settings
('terminal-001', 'receipt', 'header_text', '"Thank you for your order!"', 1),
('terminal-001', 'receipt', 'footer_text', '"Visit us again soon!"', 1),
('terminal-001', 'receipt', 'show_logo', 'true', 1),
('terminal-001', 'receipt', 'receipt_width', '"80mm"', 1),
('terminal-001', 'receipt', 'font_size', '"medium"', 1)

ON CONFLICT (terminal_id, setting_category, setting_key) DO NOTHING;

-- Enable RLS (Row Level Security)
ALTER TABLE pos_terminals ENABLE ROW LEVEL SECURITY;
ALTER TABLE pos_heartbeats ENABLE ROW LEVEL SECURITY;
ALTER TABLE pos_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE pos_settings_sync_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE enhanced_payment_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE pos_system_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies (allow all for now, can be restricted later)
CREATE POLICY "Allow all operations on pos_terminals" ON pos_terminals FOR ALL USING (true);
CREATE POLICY "Allow all operations on pos_heartbeats" ON pos_heartbeats FOR ALL USING (true);
CREATE POLICY "Allow all operations on pos_configurations" ON pos_configurations FOR ALL USING (true);
CREATE POLICY "Allow all operations on pos_settings_sync_history" ON pos_settings_sync_history FOR ALL USING (true);
CREATE POLICY "Allow all operations on enhanced_payment_settings" ON enhanced_payment_settings FOR ALL USING (true);
CREATE POLICY "Allow all operations on pos_system_logs" ON pos_system_logs FOR ALL USING (true);

-- Grant permissions
GRANT ALL ON pos_terminals TO anon, authenticated, service_role;
GRANT ALL ON pos_heartbeats TO anon, authenticated, service_role;
GRANT ALL ON pos_configurations TO anon, authenticated, service_role;
GRANT ALL ON pos_settings_sync_history TO anon, authenticated, service_role;
GRANT ALL ON enhanced_payment_settings TO anon, authenticated, service_role;
GRANT ALL ON pos_system_logs TO anon, authenticated, service_role;