'use client'

import React, { useState } from 'react'
import { useTheme } from '@/contexts/theme-context'

export default function SettingsPage() {
  const { isDarkTheme } = useTheme()
  const [activeTab, setActiveTab] = useState('general')

  const tabs = [
    { id: 'general', name: 'General', icon: '⚙️' },
    { id: 'restaurant', name: 'Restaurant', icon: '🏪' },
    { id: 'payments', name: 'Payments', icon: '💳' },
    { id: 'notifications', name: 'Notifications', icon: '🔔' },
    { id: 'users', name: 'Users', icon: '👥' },
    { id: 'security', name: 'Security', icon: '🔒' }
  ]

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className={`text-4xl font-bold mb-2 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>Settings</h1>
          <p className={`transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>Configure your restaurant management system</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Settings Navigation */}
          <div className="lg:col-span-1">
            <div className={`backdrop-blur-md border rounded-2xl p-6 transition-all duration-1000 ${
              isDarkTheme ? 'bg-white/10 border-white/20' : 'bg-black/10 border-black/20'
            }`}>
              <h3 className={`text-lg font-bold mb-4 transition-colors duration-1000 ${
                isDarkTheme ? 'text-white' : 'text-black'
              }`}>Settings Menu</h3>
              <div className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-4 py-3 rounded-xl transition-all duration-300 ${
                      activeTab === tab.id
                        ? isDarkTheme
                          ? 'bg-white/20 border border-white/30 text-white'
                          : 'bg-black/20 border border-black/30 text-black'
                        : isDarkTheme
                          ? 'text-white/70 hover:bg-white/10 hover:text-white'
                          : 'text-black/70 hover:bg-black/10 hover:text-black'
                    }`}
                  >
                    <span className="mr-3 text-lg">{tab.icon}</span>
                    {tab.name}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Settings Content */}
          <div className="lg:col-span-3">
            <div className={`backdrop-blur-md border rounded-2xl p-8 transition-all duration-1000 ${
              isDarkTheme ? 'bg-white/10 border-white/20' : 'bg-black/10 border-black/20'
            }`}>

              {/* General Settings */}
              {activeTab === 'general' && (
                <div>
                  <h2 className={`text-2xl font-bold mb-6 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white' : 'text-black'
                  }`}>⚙️ General Settings</h2>
                  <div className="space-y-6">
                    <div>
                      <label className={`block font-medium mb-2 transition-colors duration-1000 ${
                        isDarkTheme ? 'text-white' : 'text-black'
                      }`}>System Language</label>
                      <select className={`w-full border rounded-xl px-4 py-3 transition-all duration-1000 ${
                        isDarkTheme ? 'bg-white/10 border-white/20 text-white' : 'bg-black/10 border-black/20 text-black'
                      }`}>
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-white font-medium mb-2">Time Zone</label>
                      <select className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white">
                        <option value="utc">UTC</option>
                        <option value="est">Eastern Time</option>
                        <option value="pst">Pacific Time</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-white font-medium mb-2">Currency</label>
                      <select className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white">
                        <option value="usd">USD ($)</option>
                        <option value="eur">EUR (€)</option>
                        <option value="gbp">GBP (£)</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Restaurant Settings */}
              {activeTab === 'restaurant' && (
                <div>
                  <h2 className="text-2xl font-bold text-white mb-6">🏪 Restaurant Information</h2>
                  <div className="space-y-6">
                    <div>
                      <label className="block text-white font-medium mb-2">Restaurant Name</label>
                      <input 
                        type="text" 
                        defaultValue="Delicious Bites"
                        className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/50"
                      />
                    </div>
                    <div>
                      <label className="block text-white font-medium mb-2">Address</label>
                      <textarea 
                        defaultValue="123 Main Street, City, State 12345"
                        className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/50 h-24"
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-white font-medium mb-2">Phone</label>
                        <input 
                          type="tel" 
                          defaultValue="+****************"
                          className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/50"
                        />
                      </div>
                      <div>
                        <label className="block text-white font-medium mb-2">Email</label>
                        <input 
                          type="email" 
                          defaultValue="<EMAIL>"
                          className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/50"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Payment Settings */}
              {activeTab === 'payments' && (
                <div>
                  <h2 className="text-2xl font-bold text-white mb-6">💳 Payment Settings</h2>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-white/5 border border-white/10 rounded-xl p-4">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-white font-medium">Credit Cards</span>
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        </div>
                        <p className="text-white/70 text-sm">Visa, Mastercard, Amex</p>
                      </div>
                      <div className="bg-white/5 border border-white/10 rounded-xl p-4">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-white font-medium">Digital Wallets</span>
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        </div>
                        <p className="text-white/70 text-sm">Apple Pay, Google Pay</p>
                      </div>
                    </div>
                    <div>
                      <label className="block text-white font-medium mb-2">Tax Rate (%)</label>
                      <input 
                        type="number" 
                        defaultValue="8.25"
                        className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-white/50"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Notification Settings */}
              {activeTab === 'notifications' && (
                <div>
                  <h2 className="text-2xl font-bold text-white mb-6">🔔 Notification Settings</h2>
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-white/5 border border-white/10 rounded-xl">
                        <div>
                          <h4 className="text-white font-medium">New Orders</h4>
                          <p className="text-white/70 text-sm">Get notified when new orders arrive</p>
                        </div>
                        <div className="w-12 h-6 bg-green-500 rounded-full relative">
                          <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-4 bg-white/5 border border-white/10 rounded-xl">
                        <div>
                          <h4 className="text-white font-medium">Low Stock Alerts</h4>
                          <p className="text-white/70 text-sm">Alert when inventory is running low</p>
                        </div>
                        <div className="w-12 h-6 bg-green-500 rounded-full relative">
                          <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-4 bg-white/5 border border-white/10 rounded-xl">
                        <div>
                          <h4 className="text-white font-medium">Daily Reports</h4>
                          <p className="text-white/70 text-sm">Receive daily sales reports</p>
                        </div>
                        <div className="w-12 h-6 bg-gray-500 rounded-full relative">
                          <div className="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* User Management */}
              {activeTab === 'users' && (
                <div>
                  <h2 className="text-2xl font-bold text-white mb-6">👥 User Management</h2>
                  <div className="space-y-6">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium text-white">Team Members</h3>
                      <button className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-xl font-semibold hover:from-blue-600 hover:to-purple-700 transition-all duration-300">
                        + Add User
                      </button>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-4 bg-white/5 border border-white/10 rounded-xl">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                            JD
                          </div>
                          <div>
                            <h4 className="text-white font-medium">John Doe</h4>
                            <p className="text-white/70 text-sm">Administrator</p>
                          </div>
                        </div>
                        <span className="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm">Active</span>
                      </div>
                      <div className="flex items-center justify-between p-4 bg-white/5 border border-white/10 rounded-xl">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center text-white font-bold">
                            SS
                          </div>
                          <div>
                            <h4 className="text-white font-medium">Sarah Smith</h4>
                            <p className="text-white/70 text-sm">Manager</p>
                          </div>
                        </div>
                        <span className="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm">Active</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Security Settings */}
              {activeTab === 'security' && (
                <div>
                  <h2 className="text-2xl font-bold text-white mb-6">🔒 Security Settings</h2>
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-white/5 border border-white/10 rounded-xl">
                        <div>
                          <h4 className="text-white font-medium">Two-Factor Authentication</h4>
                          <p className="text-white/70 text-sm">Add an extra layer of security</p>
                        </div>
                        <button className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-xl font-semibold hover:from-blue-600 hover:to-purple-700 transition-all duration-300">
                          Enable
                        </button>
                      </div>
                      <div className="flex items-center justify-between p-4 bg-white/5 border border-white/10 rounded-xl">
                        <div>
                          <h4 className="text-white font-medium">Session Timeout</h4>
                          <p className="text-white/70 text-sm">Auto logout after inactivity</p>
                        </div>
                        <select className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-sm">
                          <option value="30">30 minutes</option>
                          <option value="60">1 hour</option>
                          <option value="120">2 hours</option>
                        </select>
                      </div>
                      <div className="flex items-center justify-between p-4 bg-white/5 border border-white/10 rounded-xl">
                        <div>
                          <h4 className="text-white font-medium">Login Alerts</h4>
                          <p className="text-white/70 text-sm">Get notified of new logins</p>
                        </div>
                        <div className="w-12 h-6 bg-green-500 rounded-full relative">
                          <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Save Button */}
              <div className="mt-8 pt-6 border-t border-white/20">
                <div className="flex space-x-4">
                  <button className="bg-gradient-to-r from-green-500 to-teal-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-green-600 hover:to-teal-700 transition-all duration-300">
                    Save Changes
                  </button>
                  <button className="bg-white/10 border border-white/20 text-white px-6 py-3 rounded-xl font-semibold hover:bg-white/15 transition-all duration-300">
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}