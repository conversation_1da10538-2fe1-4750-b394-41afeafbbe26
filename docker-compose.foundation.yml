# Docker Compose for Phase 1 Foundation Services
# Run with: docker-compose -f docker-compose.foundation.yml up

version: '3.8'

services:
  # Redis for configuration and caching
  redis:
    image: redis:7-alpine
    container_name: creperie-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - creperie-network

  # Elasticsearch for logging (production)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: creperie-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200/_cluster/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - creperie-network

  # Configuration Service
  config-service:
    build:
      context: ./config-service
      dockerfile: Dockerfile
    container_name: creperie-config-service
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - CONFIG_SERVICE_PORT=5000
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=info
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - creperie-network

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: creperie-api-gateway
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=development
      - GATEWAY_PORT=4000
      - JWT_SECRET=dev-jwt-secret-change-in-production
      - CONFIG_SERVICE_URL=http://config-service:5000
      - ADMIN_SERVICE_URL=http://host.docker.internal:3001
      - POS_SERVICE_URL=http://host.docker.internal:3002
      - CUSTOMER_SERVICE_URL=http://host.docker.internal:3000
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=info
      - CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002
    depends_on:
      config-service:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - creperie-network

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: creperie-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - creperie-network

  # Grafana for dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: creperie-grafana
    ports:
      - "3333:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - creperie-network

volumes:
  redis_data:
  elasticsearch_data:
  prometheus_data:
  grafana_data:

networks:
  creperie-network:
    driver: bridge