'use client'

import React, { ReactNode, useState, useEffect, useRef } from 'react'
import { useTheme } from '@/contexts/theme-context'
import { useAuth } from '@/contexts/auth-context'
import { useRouter, usePathname } from 'next/navigation'
import { Toaster } from 'react-hot-toast'

interface ThemeWrapperProps {
  children: ReactNode
}

export function ThemeWrapper({ children }: ThemeWrapperProps) {
  const { isDarkTheme, toggleTheme } = useTheme()
  const { isAuthenticated, isLoading } = useAuth()
  const [isNavExpanded, setIsNavExpanded] = useState(true)
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false)
  const [isProfileOpen, setIsProfileOpen] = useState(false)
  const router = useRouter()
  const pathname = usePathname()
  const headerRef = useRef<HTMLDivElement>(null)

  // Add/remove dark class to HTML element for glassmorphism CSS
  useEffect(() => {
    const htmlElement = document.documentElement
    if (isDarkTheme) {
      htmlElement.classList.add('dark')
    } else {
      htmlElement.classList.remove('dark')
    }
  }, [isDarkTheme])

  // Check if we're on an authentication route OR user is not authenticated
  const isAuthRoute = pathname?.startsWith('/auth')
  const shouldHideNavigation = isAuthRoute || !isAuthenticated || isLoading

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (headerRef.current && !headerRef.current.contains(event.target as Node)) {
        setIsNotificationsOpen(false)
        setIsProfileOpen(false)
        setIsSearchExpanded(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const leftButtons = [
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      ),
      label: 'Dashboard',
      href: '/dashboard'
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      ),
      label: 'POS',
      href: '/pos'
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="10" strokeWidth={2} />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2 12h20" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
        </svg>
      ),
      label: 'Web',
      href: '/web'
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
      ),
      label: 'App',
      href: '/app'
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      label: 'Users & Staff',
      href: '/users'
    },

  ]

  const rightButtons = [
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
        </svg>
      ),
      label: 'Orders',
      href: '/orders'
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      ),
      label: 'Menu',
      href: '/menu'
    },

    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
      label: 'Customers',
      href: '/customers'
    },


    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      ),
      label: 'System Admin',
      href: '/system'
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      label: 'Delivery Zones',
      href: '/delivery-zones'
    },

  ]

  return (
    <div className={`min-h-screen transition-all duration-1000 ease-out ${
      shouldHideNavigation 
        ? 'p-0' // No padding for auth routes or when not authenticated
        : 'p-16'
    } ${
      isDarkTheme 
        ? 'dark bg-gradient-to-br from-gray-900 to-black' 
        : 'bg-gradient-to-br from-blue-50 to-white'
    }`}>
      <div 
        className={`min-h-[calc(100vh-8rem)] overflow-hidden relative transition-all duration-1000 ease-out ${
          shouldHideNavigation 
            ? 'min-h-screen' // Full height when hiding navigation
            : 'backdrop-blur-sm rounded-3xl shadow-[0_0_100px_rgba(255,255,255,0.9),inset_0_0_30px_rgba(50,50,50,0.8)] border border-gray-700'
        }`}
        style={{
          background: shouldHideNavigation 
            ? 'transparent' // No complex background when hiding navigation
            : isDarkTheme 
              ? 'radial-gradient(ellipse at center, #0f172a 0%, #1e293b 20%, #334155 40%, #64748b 65%, #94a3b8 85%, #e2e8f0 100%)'
              : 'radial-gradient(ellipse at center, #f8fafc 0%, #e2e8f0 20%, #cbd5e1 40%, #94a3b8 65%, #64748b 85%, #334155 100%)',
          boxShadow: shouldHideNavigation 
            ? 'none' // No shadows when hiding navigation
            : isDarkTheme
              ? '0 0 100px rgba(255,255,255,0.9), inset 0 0 30px rgba(50,50,50,0.8)'
              : '0 0 100px rgba(0,0,0,0.3), inset 0 0 30px rgba(200,200,200,0.8)',
          borderColor: shouldHideNavigation ? 'transparent' : (isDarkTheme ? '#374151' : '#9ca3af')
        }}
      >
        {/* Top Right Header Elements - Only show when authenticated and not on auth routes */}
        {!shouldHideNavigation && (
        <div ref={headerRef} className="absolute top-6 right-6 z-20">
          <div className={`backdrop-blur-md border rounded-full shadow-lg transition-all duration-1000 ease-out ${
            isDarkTheme ? 'bg-white/10 border-white/20' : 'bg-black/10 border-black/20'
          }`}>
            <div className="flex items-center p-2 transition-all duration-1000 ease-out space-x-4">
              {/* Search */}
              <div className="relative overflow-hidden">
                <input
                  placeholder="Search dashboard..."
                  className={`bg-transparent border-none outline-none text-sm px-3 py-2 transition-all duration-1000 ease-out ${
                    isSearchExpanded ? 'w-48 opacity-100 px-3' : 'w-0 opacity-0 px-0'
                  } ${isDarkTheme ? 'text-white placeholder-white/60' : 'text-black placeholder-black/60'}`}
                  style={{
                    transform: isSearchExpanded ? 'translateX(0)' : 'translateX(-100%)'
                  }}
                />
              </div>
              
              {/* Search Button */}
              <button 
                onClick={() => setIsSearchExpanded(!isSearchExpanded)}
                className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-500 ease-out flex items-center justify-center ${
                  isDarkTheme 
                    ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white'
                    : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black'
                } ${isSearchExpanded ? 'bg-white/20' : ''}`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>

              {/* Notifications Button */}
              <button 
                onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
                className={`relative w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 flex items-center justify-center ${
                  isDarkTheme 
                    ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white'
                    : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black'
                } ${isNotificationsOpen ? 'bg-white/20' : ''}`}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  3
                </span>
              </button>

              {/* Profile Button */}
              <button 
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-500 ease-out flex items-center justify-center overflow-hidden ${
                  isDarkTheme 
                    ? 'bg-white/10 border-white/20 hover:bg-white/20'
                    : 'bg-black/10 border-black/20 hover:bg-black/20'
                } ${isProfileOpen ? 'bg-white/20' : ''}`}
              >
                <img 
                  src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                  alt="User Profile"
                  className="w-full h-full object-cover"
                />
              </button>

              {/* Theme Toggle Button */}
              <button 
                onClick={toggleTheme}
                className={`group w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-500 ease-out flex items-center justify-center relative overflow-hidden ${
                  isDarkTheme 
                    ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white'
                    : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black'
                }`}
              >
                <div className={`absolute inset-0 flex items-center justify-center transition-all duration-500 ${
                  isDarkTheme ? 'opacity-100 rotate-0' : 'opacity-0 group-hover:opacity-0'
                }`}>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                </div>
                <div className={`absolute inset-0 flex items-center justify-center transition-all duration-500 ${
                  isDarkTheme ? 'opacity-0 rotate-180' : 'opacity-100 rotate-0'
                }`}>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </div>
              </button>
            </div>
          </div>

          {/* Notifications Dropdown */}
          <div className={`backdrop-blur-md border rounded-2xl shadow-lg mt-2 overflow-hidden transition-all duration-1000 ease-out ${
            isNotificationsOpen ? 'max-h-96 opacity-100 transform translate-y-0' : 'max-h-0 opacity-0 transform -translate-y-2'
          } ${isDarkTheme ? 'bg-white/10 border-white/20' : 'bg-black/10 border-black/20'}`}>
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className={`text-sm font-medium ${isDarkTheme ? 'text-white' : 'text-black'}`}>Notifications</h3>
                <button className={`text-xs transition-colors duration-200 ${
                  isDarkTheme ? 'text-white/60 hover:text-white/80' : 'text-black/60 hover:text-black/80'
                }`}>
                  Mark all read
                </button>
              </div>
              <div className="max-h-56 overflow-y-auto space-y-2 pr-2">
                {[
                  { color: 'blue', title: 'New order received', desc: 'Order #ORD-004 - €12.50', time: '2 minutes ago' },
                  { color: 'green', title: 'Payment confirmed', desc: 'Order #ORD-003 processed', time: '5 minutes ago' },
                  { color: 'yellow', title: 'Low stock alert', desc: 'Nutella running low', time: '1 hour ago' },
                  { color: 'purple', title: 'Customer review', desc: '5-star rating received', time: '3 hours ago' },
                  { color: 'red', title: 'System maintenance', desc: 'Scheduled for tonight', time: '4 hours ago' }
                ].map((notification, index) => (
                  <div key={index} className="flex items-start space-x-3 p-2 rounded-lg transition-all duration-200 hover:bg-white/5">
                    <div className={`w-2 h-2 bg-${notification.color}-400 rounded-full mt-2 flex-shrink-0`}></div>
                    <div className="flex-1 min-w-0">
                      <p className={`text-sm ${isDarkTheme ? 'text-white/90' : 'text-black/90'}`}>{notification.title}</p>
                      <p className={`text-xs ${isDarkTheme ? 'text-white/60' : 'text-black/60'}`}>{notification.desc}</p>
                      <p className={`text-xs ${isDarkTheme ? 'text-white/40' : 'text-black/40'}`}>{notification.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Profile Dropdown */}
          <div className={`backdrop-blur-md border rounded-2xl shadow-lg mt-2 overflow-hidden transition-all duration-1000 ease-out ${
            isProfileOpen ? 'max-h-48 opacity-100 transform translate-y-0' : 'max-h-0 opacity-0 transform -translate-y-2'
          } ${isDarkTheme ? 'bg-white/10 border-white/20' : 'bg-black/10 border-black/20'}`}>
            <div className="p-4 space-y-1">
              <button className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-300 flex items-center space-x-3 ${
                isDarkTheme ? 'text-white/80 hover:text-white hover:bg-white/10' : 'text-black/80 hover:text-black hover:bg-black/10'
              }`}>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span>Profile Settings</span>
              </button>
              <button 
                onClick={() => router.push('/settings')}
                className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-300 flex items-center space-x-3 ${
                  isDarkTheme ? 'text-white/80 hover:text-white hover:bg-white/10' : 'text-black/80 hover:text-black hover:bg-black/10'
                }`}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>Preferences</span>
              </button>
              <button className="w-full text-left px-3 py-2 rounded-lg text-sm text-red-300 hover:text-red-200 hover:bg-red-500/20 transition-all duration-300 flex items-center space-x-3">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
        )}

        {/* Top Horizontal Navigation - Only show when authenticated and not on auth routes */}
        {!shouldHideNavigation && (
        <div className="absolute top-6 left-1/2 transform -translate-x-1/2 z-10">
          <div 
            className={`backdrop-blur-md border shadow-lg transition-all duration-500 ease-out ${
              isDarkTheme 
                ? 'bg-white/10 border-white/20' 
                : 'bg-black/10 border-black/20'
            }`}
            style={{
              width: isNavExpanded ? '620px' : '56px',
              height: '56px',
              borderRadius: isNavExpanded ? '28px' : '50%'
            }}
          >
            <div className="relative w-full h-full overflow-hidden">
              {/* Always Present Central Toggle Button */}
              <button 
                onClick={() => setIsNavExpanded(!isNavExpanded)}
                className={`absolute w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center z-10 ${
                  isDarkTheme 
                    ? 'bg-gradient-to-br from-white/25 to-white/15 border-white/35 hover:from-white/35 hover:to-white/25 text-white'
                    : 'bg-gradient-to-br from-black/25 to-black/15 border-black/35 hover:from-black/35 hover:to-black/25 text-black'
                }`}
                style={{
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  boxShadow: isDarkTheme 
                    ? '0 0 20px rgba(255,255,255,0.6), inset 0 1px 0 rgba(255,255,255,0.4)' 
                    : '0 0 20px rgba(0,0,0,0.6), inset 0 1px 0 rgba(0,0,0,0.4)'
                }}
              >
                {/* Glowing dot - same in both states */}
                <div className="w-2 h-2 rounded-full bg-current animate-pulse" 
                     style={{
                       boxShadow: isDarkTheme 
                         ? '0 0 8px rgba(255,255,255,0.8)' 
                         : '0 0 8px rgba(0,0,0,0.8)'
                     }} />
              </button>

              {/* Left Button Group */}
              <div
                className={`absolute top-1/2 transform -translate-y-1/2 flex items-center space-x-3 transition-all duration-400 ease-out ${
                  isNavExpanded ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-8'
                }`}
                style={{ left: '25px', right: 'calc(50% + 35px)' }}
              >
                {leftButtons.map((button, index) => (
                  <button 
                    key={`left-${index}`}
                    onClick={() => router.push(button.href)}
                    className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center ${
                      isDarkTheme 
                        ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white'
                        : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black'
                    } ${
                      isNavExpanded ? 'scale-100' : 'scale-0'
                    }`}
                    style={{
                      transitionDelay: isNavExpanded ? `${index * 50}ms` : `${(3 - index) * 50}ms`
                    }}
                    title={button.label}
                  >
                    {button.icon}
                  </button>
                ))}
              </div>

              {/* Right Button Group */}
              <div
                className={`absolute top-1/2 transform -translate-y-1/2 flex items-center space-x-3 transition-all duration-400 ease-out ${
                  isNavExpanded ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-8'
                }`}
                style={{ right: '25px', left: 'calc(50% + 35px)' }}
              >
                {rightButtons.map((button, index) => (
                  <button 
                    key={`right-${index}`}
                    onClick={() => router.push(button.href)}
                    className={`w-10 h-10 backdrop-blur-md border rounded-full shadow-lg transition-all duration-300 ease-out flex items-center justify-center ${
                      isDarkTheme 
                        ? 'bg-white/10 border-white/20 hover:bg-white/20 text-white/80 hover:text-white'
                        : 'bg-black/10 border-black/20 hover:bg-black/20 text-black/80 hover:text-black'
                    } ${
                      isNavExpanded ? 'scale-100' : 'scale-0'
                    }`}
                    style={{
                      transitionDelay: isNavExpanded ? `${index * 50}ms` : `${(3 - index) * 50}ms`
                    }}
                    title={button.label}
                  >
                    {button.icon}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
        )}
        
        <div className={`min-h-full font-sans antialiased ${shouldHideNavigation ? 'pt-0' : 'pt-20'}`}>
          {children}
        </div>
      </div>
      
      {/* Toast Notifications */}
      <Toaster
        position="top-center"
        gutter={8}
        containerStyle={{
          top: 20,
          left: 20,
          right: 20,
        }}
        toastOptions={{
          duration: 2500,
          style: {
            background: 'rgba(0, 0, 0, 0.1)',
            color: '#00ff41',
            fontSize: '14px',
            padding: '8px 12px',
            borderRadius: '12px',
            border: '1px solid rgba(0, 255, 65, 0.3)',
            backdropFilter: 'blur(20px) saturate(180%)',
            boxShadow: '0 8px 32px rgba(0, 255, 65, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
            maxWidth: '320px',
            minHeight: 'auto',
            textShadow: '0 0 10px rgba(0, 255, 65, 0.5)',
            WebkitBackdropFilter: 'blur(20px) saturate(180%)',
          },
          success: {
            style: {
              background: 'rgba(0, 0, 0, 0.15)',
              color: '#00ff41',
              border: '1px solid rgba(0, 255, 65, 0.4)',
              boxShadow: '0 8px 32px rgba(0, 255, 65, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
              textShadow: '0 0 15px rgba(0, 255, 65, 0.7)',
            },
            iconTheme: {
              primary: '#00ff41',
              secondary: 'rgba(0, 0, 0, 0.15)',
            },
          },
          error: {
            style: {
              background: 'rgba(0, 0, 0, 0.15)',
              color: '#00ff41',
              border: '1px solid rgba(0, 255, 65, 0.4)',
              boxShadow: '0 8px 32px rgba(0, 255, 65, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
              textShadow: '0 0 15px rgba(0, 255, 65, 0.7)',
            },
            iconTheme: {
              primary: '#00ff41',
              secondary: 'rgba(0, 0, 0, 0.15)',
            },
          },
          loading: {
            style: {
              background: 'rgba(0, 0, 0, 0.15)',
              color: '#00ff41',
              border: '1px solid rgba(0, 255, 65, 0.4)',
              boxShadow: '0 8px 32px rgba(0, 255, 65, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
              textShadow: '0 0 15px rgba(0, 255, 65, 0.7)',
            },
          },
        }}
      />
    </div>
  )
}