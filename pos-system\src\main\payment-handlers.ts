import { ipcMain } from 'electron';
import { DatabaseManager } from './database';
import { v4 as uuidv4 } from 'uuid';
import { paymentConfig, getProcessingDelay, isTestMode, isProductionMode } from '../config/payment-config';

// Payment gateway simulation
class PaymentGateway {
  static async processCardPayment(cardDetails: {
    cardNumber: string;
    expiryDate: string;
    cvv: string;
    cardholderName: string;
  }, amount: number): Promise<{
    success: boolean;
    transactionId?: string;
    errorCode?: string;
    errorMessage?: string;
  }> {
    // Simulate processing delay (configurable)
    await new Promise(resolve => setTimeout(resolve, getProcessingDelay()));

    const cleanCardNumber = cardDetails.cardNumber.replace(/\s/g, '');

    // Handle test mode vs production mode
    if (isTestMode()) {
      const testResult = paymentConfig.testCards[cleanCardNumber];
      
      if (testResult) {
        if (testResult.success) {
          return {
            success: true,
            transactionId: `test_txn_${uuidv4().substring(0, 8)}`
          };
        } else {
          return {
            success: false,
            errorCode: testResult.errorCode,
            errorMessage: testResult.errorMessage
          };
        }
      }
      
      // Default test card behavior
      return {
        success: true,
        transactionId: `test_txn_${uuidv4().substring(0, 8)}`
      };
    }

    // Production mode - integrate with real payment gateway
    if (isProductionMode()) {
      // TODO: Integrate with actual payment processor (Stripe, PayPal, etc.)
      throw new Error('Production payment processing not yet implemented');
    }

    // Fallback - should not reach here
    throw new Error('Invalid payment configuration');
  }

  static async processRefund(transactionId: string, amount: number): Promise<{
    success: boolean;
    refundId?: string;
    errorCode?: string;
    errorMessage?: string;
  }> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Simulate 95% success rate
    if (Math.random() < 0.95) {
      return {
        success: true,
        refundId: `ref_${uuidv4().substring(0, 8)}`
      };
    } else {
      return {
        success: false,
        errorCode: 'REFUND_FAILED',
        errorMessage: 'Refund processing failed'
      };
    }
  }
}

export function setupPaymentHandlers(db: DatabaseManager) {
  // Process cash payment
  ipcMain.handle('payment:process-cash', async (event, data: {
    orderId: string;
    amount: number;
    cashReceived: number;
  }) => {
    try {
      const { orderId, amount, cashReceived } = data;
      
      if (cashReceived < amount) {
        throw new Error('Insufficient cash received');
      }

      const transactionId = uuidv4();
      const now = new Date().toISOString();
      
      // Create transaction record
      await db.insertPaymentTransaction({
        id: transactionId,
        order_id: orderId,
        amount,
        payment_method: 'cash',
        status: 'completed',
        processed_at: now
      });

      // Update order payment status
      await db.updateOrderPaymentStatus(orderId, 'completed', 'cash', transactionId);

      return {
        success: true,
        transaction: {
          id: transactionId,
          order_id: orderId,
          amount,
          payment_method: 'cash',
          status: 'completed',
          processed_at: now,
          cash_received: cashReceived,
          change_given: cashReceived - amount
        }
      };
    } catch (error) {
      console.error('Cash payment processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Process card payment
  ipcMain.handle('payment:process-card', async (event, data: {
    orderId: string;
    amount: number;
    cardDetails: {
      cardNumber: string;
      expiryDate: string;
      cvv: string;
      cardholderName: string;
    };
  }) => {
    try {
      const { orderId, amount, cardDetails } = data;
      
      const transactionId = uuidv4();
      const now = new Date().toISOString();
      
      // Process payment through gateway
      const gatewayResult = await PaymentGateway.processCardPayment(cardDetails, amount);
      
      if (!gatewayResult.success) {
        // Create failed transaction record
        await db.insertPaymentTransaction({
          id: transactionId,
          order_id: orderId,
          amount,
          payment_method: 'card',
          status: 'failed',
          gateway_response: JSON.stringify(gatewayResult),
          processed_at: now
        });

        return {
          success: false,
          error: gatewayResult.errorMessage || 'Card payment failed',
          errorCode: gatewayResult.errorCode
        };
      }

      // Create successful transaction record
      await db.insertPaymentTransaction({
        id: transactionId,
        order_id: orderId,
        amount,
        payment_method: 'card',
        status: 'completed',
        gateway_transaction_id: gatewayResult.transactionId,
        gateway_response: JSON.stringify(gatewayResult),
        processed_at: now
      });

      // Update order payment status
      await db.updateOrderPaymentStatus(orderId, 'completed', 'card', transactionId);

      return {
        success: true,
        transaction: {
          id: transactionId,
          order_id: orderId,
          amount,
          payment_method: 'card',
          status: 'completed',
          gateway_transaction_id: gatewayResult.transactionId,
          processed_at: now
        }
      };
    } catch (error) {
      console.error('Card payment processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Save receipt
  ipcMain.handle('payment:save-receipt', async (event, data: {
    id: string;
    transaction_id: string;
    receipt_number: string;
    order_details: any;
    subtotal: number;
    tax: number;
    delivery_fee: number;
    total_amount: number;
    payment_method: string;
    cash_received?: number;
    change_given?: number;
  }) => {
    try {
      await db.insertPaymentReceipt({
        ...data,
        order_details: JSON.stringify(data.order_details)
      });

      return { success: true };
    } catch (error) {
      console.error('Receipt save error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Update receipt status
  ipcMain.handle('payment:update-receipt-status', async (event, data: {
    receiptId: string;
    printed?: boolean;
    emailed?: boolean;
  }) => {
    try {
      await db.updateReceiptStatus(data.receiptId, data.printed, data.emailed);
      return { success: true };
    } catch (error) {
      console.error('Receipt status update error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Process refund
  ipcMain.handle('payment:process-refund', async (event, data: {
    transactionId: string;
    amount: number;
    reason?: string;
  }) => {
    try {
      const { transactionId, amount, reason } = data;
      
      // Get original transaction
      const transaction = await db.getPaymentTransaction(transactionId);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      // Check if refund amount is valid
      const existingRefunds = await db.getPaymentRefundsByTransactionId(transactionId);
      const totalRefunded = existingRefunds.reduce((sum, refund) => 
        refund.status === 'completed' ? sum + refund.amount : sum, 0
      );
      
      if (totalRefunded + amount > transaction.amount) {
        throw new Error('Refund amount exceeds transaction amount');
      }

      const refundId = uuidv4();
      const now = new Date().toISOString();
      
      let refundStatus: 'pending' | 'processing' | 'completed' | 'failed' = 'completed';
      let gatewayRefundId: string | undefined;

      // Process refund through gateway for card payments
      if (transaction.payment_method === 'card' && transaction.gateway_transaction_id) {
        const gatewayResult = await PaymentGateway.processRefund(
          transaction.gateway_transaction_id, 
          amount
        );
        
        if (!gatewayResult.success) {
          refundStatus = 'failed';
        } else {
          gatewayRefundId = gatewayResult.refundId;
        }
      }

      // Create refund record
      await db.insertPaymentRefund({
        id: refundId,
        transaction_id: transactionId,
        amount,
        reason,
        status: refundStatus,
        gateway_refund_id: gatewayRefundId,
        processed_at: now
      });

      return {
        success: refundStatus === 'completed',
        refund: {
          id: refundId,
          transaction_id: transactionId,
          amount,
          reason,
          status: refundStatus,
          gateway_refund_id: gatewayRefundId,
          processed_at: now
        }
      };
    } catch (error) {
      console.error('Refund processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Get payment transactions for order
  ipcMain.handle('payment:get-transactions', async (event, orderId: string) => {
    try {
      const transactions = await db.getPaymentTransactionsByOrderId(orderId);
      return { success: true, transactions };
    } catch (error) {
      console.error('Get transactions error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Get receipt by number
  ipcMain.handle('payment:get-receipt', async (event, receiptNumber: string) => {
    try {
      const receipt = await db.getPaymentReceiptByNumber(receiptNumber);
      if (!receipt) {
        return { success: false, error: 'Receipt not found' };
      }
      
      return { 
        success: true, 
        receipt: {
          ...receipt,
          order_details: JSON.parse(receipt.order_details)
        }
      };
    } catch (error) {
      console.error('Get receipt error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Simulate receipt printing
  ipcMain.handle('payment:print-receipt', async (event, receiptData, type = 'customer') => {
    try {
      
      // In a real implementation, you would:
      // 1. Format the receipt data for the printer
      // 2. Send to thermal printer via USB/network
      // 3. Handle printer errors
      
      // Simulate printing delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return { success: true, message: 'Receipt printed successfully' };
    } catch (error) {
      console.error('Print receipt error:', error);
      throw error;
    }
  });

  // Kitchen ticket printing
  ipcMain.handle('kitchen:print-ticket', async (event, orderData) => {
    try {
      
      // Format kitchen ticket data
      const ticketData = {
        orderNumber: orderData.orderNumber,
        items: orderData.items,
        customerName: orderData.customerName,
        orderType: orderData.orderType,
        tableNumber: orderData.tableNumber,
        notes: orderData.notes,
        createdAt: orderData.createdAt,
        estimatedTime: orderData.estimatedTime,
        printedAt: new Date().toISOString()
      };
      
      // In a real implementation, you would:
      // 1. Format the ticket for kitchen printer
      // 2. Send to kitchen display/printer
      // 3. Handle printer errors
      
      // Simulate printing delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      return { success: true, message: 'Kitchen ticket printed successfully' };
    } catch (error) {
      console.error('Print kitchen ticket error:', error);
      throw error;
    }
  });

  // Email receipt (simulation)
  ipcMain.handle('payment:email-receipt', async (event, data: {
    receiptData: any;
    emailAddress: string;
  }) => {
    try {
      // Simulate email sending delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      
      // In a real implementation, this would send an email via SMTP or email service
      // For now, we'll just log and mark as emailed
      
      return { success: true };
    } catch (error) {
      console.error('Email receipt error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });
}