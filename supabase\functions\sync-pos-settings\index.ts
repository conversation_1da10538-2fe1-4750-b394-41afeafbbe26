// Enhanced POS Settings Sync Edge Function v2.0
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EnhancedSyncRequest {
  terminal_id: string
  sync_types: ('staff_permissions' | 'hardware_config' | 'menu_availability' | 'restaurant_settings')[]
  force_sync?: boolean
  version_check?: boolean
  action?: 'sync' | 'heartbeat' | 'register'
}

interface SyncResponse {
  success: boolean
  synced_items: SyncItem[]
  failed_items: FailedSyncItem[]
  conflicts: ConflictItem[]
  next_sync_at: string
  terminal_status: TerminalStatus
}

interface SyncItem {
  id: string
  sync_type: string
  resource_id: string
  data: any
}

interface FailedSyncItem {
  id: string
  sync_type: string
  error: string
  retry_after: string
}

interface ConflictItem {
  id: string
  conflict_type: string
  local_data: any
  remote_data: any
}

interface TerminalStatus {
  terminal_id: string
  status: string
  last_heartbeat: string
  pending_sync_count: number
  settings_version: number
  menu_version: number
}

interface HeartbeatData {
  terminal_id: string
  status: 'online' | 'offline' | 'error' | 'syncing' | 'maintenance'
  version: string
  uptime: number
  memory_usage: number
  cpu_usage: number
  disk_usage: number
  settings_hash: string
  sync_status: 'synced' | 'pending' | 'failed'
  pending_updates: number
  network_info: {
    ip_address: string
    connection_quality: string
    latency_ms: number
  }
  performance_metrics: {
    avg_response_time_ms: number
    orders_per_hour: number
    error_rate: number
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const body = await req.json()
    const { terminal_id, action = 'sync' } = body

    console.log(`🔄 Enhanced POS Sync Request: ${action} for terminal ${terminal_id}`)

    switch (action) {
      case 'heartbeat':
        return await handleEnhancedHeartbeat(supabase, body)

      case 'register':
        return await handleTerminalRegistration(supabase, body)

      case 'sync':
      default:
        return await handleEnhancedSync(supabase, body)
    }

  } catch (error) {
    console.error('❌ Enhanced POS Sync Error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Unknown error occurred',
        retry_after: new Date(Date.now() + 60000).toISOString()
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})

async function handleEnhancedSync(supabase: any, body: any) {
  const { terminal_id, sync_types, force_sync = false, version_check = true }: EnhancedSyncRequest = body

  console.log(`🔄 Enhanced sync request for terminal ${terminal_id}:`, sync_types)

  const response: SyncResponse = {
    success: true,
    synced_items: [],
    failed_items: [],
    conflicts: [],
    next_sync_at: new Date(Date.now() + 30000).toISOString(), // 30 seconds
    terminal_status: await getTerminalStatus(supabase, terminal_id)
  }

  // Process each sync type
  for (const syncType of sync_types) {
    try {
      const result = await processSyncType(supabase, terminal_id, syncType, force_sync, version_check)
      response.synced_items.push(...result.synced_items)
      response.failed_items.push(...result.failed_items)
      response.conflicts.push(...result.conflicts)
    } catch (error) {
      console.error(`❌ Failed to sync ${syncType}:`, error)
      response.failed_items.push({
        id: `${syncType}-${Date.now()}`,
        sync_type: syncType,
        error: error.message,
        retry_after: new Date(Date.now() + 60000).toISOString()
      })
    }
  }

  // Update terminal status
  await updateTerminalStatus(supabase, terminal_id, {
    last_sync_at: new Date().toISOString(),
    pending_sync_count: response.failed_items.length,
    status: response.failed_items.length > 0 ? 'error' : 'online'
  })

  // Update sync queue status
  await updateSyncQueueStatus(supabase, terminal_id, response.synced_items, response.failed_items)

  console.log(`✅ Enhanced sync completed for ${terminal_id}:`, {
    synced: response.synced_items.length,
    failed: response.failed_items.length,
    conflicts: response.conflicts.length
  })

  return new Response(
    JSON.stringify(response),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handleEnhancedHeartbeat(supabase: any, body: any) {
  const heartbeatData: HeartbeatData = body

  try {
    console.log(`💓 Enhanced heartbeat from terminal ${heartbeatData.terminal_id}`)

    // Update enhanced terminal status
    const { error: statusError } = await supabase
      .from('pos_terminal_status')
      .upsert({
        terminal_id: heartbeatData.terminal_id,
        status: heartbeatData.status,
        last_heartbeat: new Date().toISOString(),
        pending_sync_count: heartbeatData.pending_updates,
        system_info: {
          cpu_usage: heartbeatData.cpu_usage,
          memory_usage: heartbeatData.memory_usage,
          disk_usage: heartbeatData.disk_usage,
          uptime: heartbeatData.uptime
        },
        network_info: heartbeatData.network_info,
        performance_metrics: heartbeatData.performance_metrics,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'terminal_id'
      })

    if (statusError) {
      throw new Error(`Failed to update terminal status: ${statusError.message}`)
    }

    // Update legacy pos_terminals table for backward compatibility
    const { error: terminalError } = await supabase
      .from('pos_terminals')
      .update({
        status: heartbeatData.status,
        last_heartbeat: new Date().toISOString(),
        uptime: heartbeatData.uptime,
        version: heartbeatData.version
      })
      .eq('terminal_id', heartbeatData.terminal_id)

    if (terminalError) {
      console.error('Failed to update legacy terminal record:', terminalError)
    }

    // Check for pending settings updates
    const { data: pendingConfigs, error: configError } = await supabase
      .from('pos_configurations')
      .select('*')
      .eq('terminal_id', heartbeatData.terminal_id)
      .eq('sync_status', 'pending')

    if (configError) {
      throw new Error(`Failed to check pending configs: ${configError.message}`)
    }

    const hasPendingUpdates = pendingConfigs && pendingConfigs.length > 0
    const settingsToSync = hasPendingUpdates ? groupSettingsByCategory(pendingConfigs) : null

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Heartbeat processed',
        has_pending_updates: hasPendingUpdates,
        settings_to_sync: settingsToSync,
        sync_required: heartbeatData.sync_status === 'pending' || hasPendingUpdates
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Heartbeat processing error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
}

async function handleTerminalRegistration(supabase: any, req: Request) {
  const registrationData = await req.json()
  
  try {
    const { error } = await supabase
      .from('pos_terminals')
      .upsert({
        terminal_id: registrationData.terminal_id,
        name: registrationData.name || `Terminal ${registrationData.terminal_id}`,
        location: registrationData.location || 'Unknown',
        ip_address: registrationData.ip_address,
        mac_address: registrationData.mac_address,
        status: 'online',
        last_heartbeat: new Date().toISOString(),
        version: registrationData.version || '1.0.0',
        uptime: 0,
        settings_version: 1,
        is_active: true
      }, {
        onConflict: 'terminal_id'
      })

    if (error) {
      throw new Error(`Registration failed: ${error.message}`)
    }

    // Get initial settings for this terminal
    const { data: configs, error: configError } = await supabase
      .from('pos_configurations')
      .select('*')
      .eq('terminal_id', registrationData.terminal_id)
      .eq('is_active', true)

    if (configError) {
      console.error('Failed to fetch initial configs:', configError)
    }

    const initialSettings = configs ? groupSettingsByCategory(configs) : {}

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Terminal registered successfully',
        terminal_id: registrationData.terminal_id,
        initial_settings: initialSettings
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Registration error:', error)
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
}

async function handleSettingsSync(supabase: any, terminal_id: string, category?: string, version?: number) {
  try {
    let query = supabase
      .from('pos_configurations')
      .select('*')
      .eq('terminal_id', terminal_id)
      .eq('is_active', true)

    if (category) {
      query = query.eq('setting_category', category)
    }

    if (version) {
      query = query.eq('settings_version', version)
    }

    const { data: configurations, error } = await query

    if (error) {
      throw new Error(`Failed to fetch configurations: ${error.message}`)
    }

    if (!configurations || configurations.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No configurations found',
          settings: {}
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Group settings by category
    const groupedSettings = groupSettingsByCategory(configurations)

    // Log sync event
    await supabase
      .from('pos_settings_sync_history')
      .insert({
        terminal_id,
        setting_category: category || 'all',
        operation: 'sync',
        new_value: groupedSettings,
        sync_result: 'success',
        settings_version: version || 0
      })

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Settings synchronized',
        settings: groupedSettings,
        timestamp: new Date().toISOString(),
        version: version || 0
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Settings sync error:', error)
    
    // Log failed sync
    await supabase
      .from('pos_settings_sync_history')
      .insert({
        terminal_id,
        setting_category: category || 'all',
        operation: 'sync',
        sync_result: 'failed',
        error_message: error.message,
        settings_version: version || 0
      })

    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
}

function groupSettingsByCategory(configurations: any[]) {
  const grouped: Record<string, Record<string, any>> = {}
  
  configurations.forEach(config => {
    if (!grouped[config.setting_category]) {
      grouped[config.setting_category] = {}
    }
    
    // Parse JSON values
    let value = config.setting_value
    if (typeof value === 'string') {
      try {
        value = JSON.parse(value)
      } catch {
        // Keep as string if not valid JSON
      }
    }
    
    grouped[config.setting_category][config.setting_key] = value
  })
  
  return grouped
}

// Enhanced sync helper functions
async function processSyncType(
  supabase: any,
  terminalId: string,
  syncType: string,
  forceSync: boolean,
  versionCheck: boolean
) {
  const result = {
    synced_items: [] as SyncItem[],
    failed_items: [] as FailedSyncItem[],
    conflicts: [] as ConflictItem[]
  }

  switch (syncType) {
    case 'staff_permissions':
      return await syncStaffPermissions(supabase, terminalId, forceSync, versionCheck)

    case 'hardware_config':
      return await syncHardwareConfig(supabase, terminalId, forceSync, versionCheck)

    case 'menu_availability':
      return await syncMenuAvailability(supabase, terminalId, forceSync, versionCheck)

    case 'restaurant_settings':
      return await syncRestaurantSettings(supabase, terminalId, forceSync, versionCheck)

    default:
      throw new Error(`Unknown sync type: ${syncType}`)
  }
}

async function syncStaffPermissions(supabase: any, terminalId: string, forceSync: boolean, versionCheck: boolean) {
  // Get pending staff permission syncs for this terminal
  const { data: permissions, error } = await supabase
    .from('staff_permissions_sync')
    .select(`
      *,
      staff:staff_id (
        id,
        staff_code,
        first_name,
        last_name,
        is_active
      )
    `)
    .or(`terminal_id.eq.${terminalId},terminal_id.is.null`)
    .eq('sync_status', 'pending')
    .order('created_at', { ascending: true })

  if (error) throw error

  const result = {
    synced_items: [] as SyncItem[],
    failed_items: [] as FailedSyncItem[],
    conflicts: [] as ConflictItem[]
  }

  for (const permission of permissions || []) {
    try {
      // Validate permission data
      if (!permission.staff || !permission.staff.is_active) {
        throw new Error('Staff member is inactive')
      }

      // Check for conflicts if version checking is enabled
      if (versionCheck) {
        const conflict = await checkPermissionConflict(supabase, permission)
        if (conflict) {
          result.conflicts.push(conflict)
          continue
        }
      }

      // Mark as synced
      await supabase
        .from('staff_permissions_sync')
        .update({
          sync_status: 'synced',
          last_sync_at: new Date().toISOString(),
          sync_attempts: permission.sync_attempts + 1
        })
        .eq('id', permission.id)

      result.synced_items.push({
        id: permission.id,
        sync_type: 'staff_permissions',
        resource_id: permission.staff_id,
        data: {
          staff_code: permission.staff.staff_code,
          permission_key: permission.permission_key,
          permission_value: permission.permission_value
        }
      })

    } catch (error) {
      // Mark as failed and increment attempts
      await supabase
        .from('staff_permissions_sync')
        .update({
          sync_status: 'failed',
          sync_error: error.message,
          sync_attempts: permission.sync_attempts + 1
        })
        .eq('id', permission.id)

      result.failed_items.push({
        id: permission.id,
        sync_type: 'staff_permissions',
        error: error.message,
        retry_after: new Date(Date.now() + (permission.sync_attempts + 1) * 60000).toISOString()
      })
    }
  }

  return result
}

async function syncHardwareConfig(supabase: any, terminalId: string, forceSync: boolean, versionCheck: boolean) {
  // Get pending hardware configuration syncs for this terminal
  const { data: configs, error } = await supabase
    .from('pos_hardware_configurations')
    .select('*')
    .eq('terminal_id', terminalId)
    .eq('sync_status', 'pending')
    .order('created_at', { ascending: true })

  if (error) throw error

  const result = {
    synced_items: [] as SyncItem[],
    failed_items: [] as FailedSyncItem[],
    conflicts: [] as ConflictItem[]
  }

  for (const config of configs || []) {
    try {
      // Validate hardware configuration
      const validationResult = await validateHardwareConfig(config.hardware_config, config.hardware_type)
      if (!validationResult.valid) {
        throw new Error(`Invalid configuration: ${validationResult.errors.join(', ')}`)
      }

      // Mark as synced
      await supabase
        .from('pos_hardware_configurations')
        .update({
          sync_status: 'synced',
          validation_status: 'valid',
          last_sync_at: new Date().toISOString(),
          sync_attempts: config.sync_attempts + 1
        })
        .eq('id', config.id)

      result.synced_items.push({
        id: config.id,
        sync_type: 'hardware_config',
        resource_id: config.id,
        data: {
          hardware_type: config.hardware_type,
          hardware_config: config.hardware_config,
          requires_restart: config.requires_restart
        }
      })

    } catch (error) {
      // Mark as failed
      await supabase
        .from('pos_hardware_configurations')
        .update({
          sync_status: 'failed',
          validation_status: 'invalid',
          sync_error: error.message,
          validation_error: error.message,
          sync_attempts: config.sync_attempts + 1
        })
        .eq('id', config.id)

      result.failed_items.push({
        id: config.id,
        sync_type: 'hardware_config',
        error: error.message,
        retry_after: new Date(Date.now() + (config.sync_attempts + 1) * 60000).toISOString()
      })
    }
  }

  return result
}

async function syncMenuAvailability(supabase: any, terminalId: string, forceSync: boolean, versionCheck: boolean) {
  // Get pending menu availability syncs
  const { data: menuUpdates, error } = await supabase
    .from('pos_sync_queue_enhanced')
    .select('*')
    .eq('sync_type', 'menu_availability')
    .or(`target_terminal_id.eq.${terminalId},target_terminal_id.is.null`)
    .eq('sync_status', 'pending')
    .order('priority', { ascending: true })

  if (error) throw error

  const result = {
    synced_items: [] as SyncItem[],
    failed_items: [] as FailedSyncItem[],
    conflicts: [] as ConflictItem[]
  }

  for (const update of menuUpdates || []) {
    try {
      // Mark as completed
      await supabase
        .from('pos_sync_queue_enhanced')
        .update({
          sync_status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', update.id)

      result.synced_items.push({
        id: update.id,
        sync_type: 'menu_availability',
        resource_id: update.resource_id,
        data: update.data_changes
      })

    } catch (error) {
      result.failed_items.push({
        id: update.id,
        sync_type: 'menu_availability',
        error: error.message,
        retry_after: new Date(Date.now() + 60000).toISOString()
      })
    }
  }

  return result
}

async function syncRestaurantSettings(supabase: any, terminalId: string, forceSync: boolean, versionCheck: boolean) {
  // Get restaurant settings for this terminal
  const { data: settings, error } = await supabase
    .from('restaurant_settings')
    .select('*')
    .eq('is_active', true)

  if (error) throw error

  const result = {
    synced_items: [] as SyncItem[],
    failed_items: [] as FailedSyncItem[],
    conflicts: [] as ConflictItem[]
  }

  if (settings && settings.length > 0) {
    result.synced_items.push({
      id: `restaurant-settings-${terminalId}`,
      sync_type: 'restaurant_settings',
      resource_id: 'restaurant_settings',
      data: settings[0]
    })
  }

  return result
}

async function checkPermissionConflict(supabase: any, permission: any): Promise<ConflictItem | null> {
  // Simple conflict detection - can be enhanced based on business rules
  return null
}

async function validateHardwareConfig(config: any, hardwareType: string): Promise<{valid: boolean, errors: string[]}> {
  const errors: string[] = []

  switch (hardwareType) {
    case 'printer':
      if (!config.type) errors.push('Printer type is required')
      if (config.type === 'network' && !config.ip) errors.push('IP address is required for network printers')
      if (config.port && (config.port < 1 || config.port > 65535)) errors.push('Invalid port number')
      break

    case 'cash_drawer':
      if (!config.type) errors.push('Cash drawer type is required')
      if (config.type === 'rj11' && !config.open_code) errors.push('Open code is required for RJ11 cash drawers')
      break

    case 'barcode_scanner':
      if (!config.type) errors.push('Scanner type is required')
      break

    // Add validation for other hardware types
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

// Helper functions for enhanced sync
async function getTerminalStatus(supabase: any, terminalId: string): Promise<TerminalStatus> {
  const { data: status, error } = await supabase
    .from('pos_terminal_status')
    .select('*')
    .eq('terminal_id', terminalId)
    .single()

  if (error || !status) {
    return {
      terminal_id: terminalId,
      status: 'offline',
      last_heartbeat: new Date().toISOString(),
      pending_sync_count: 0,
      settings_version: 0,
      menu_version: 0
    }
  }

  return {
    terminal_id: status.terminal_id,
    status: status.status,
    last_heartbeat: status.last_heartbeat,
    pending_sync_count: status.pending_sync_count,
    settings_version: status.settings_version,
    menu_version: status.menu_version
  }
}

async function updateTerminalStatus(supabase: any, terminalId: string, updates: any) {
  const { error } = await supabase
    .from('pos_terminal_status')
    .upsert({
      terminal_id: terminalId,
      ...updates,
      updated_at: new Date().toISOString()
    }, {
      onConflict: 'terminal_id'
    })

  if (error) {
    console.error('Failed to update terminal status:', error)
  }
}

async function updateSyncQueueStatus(supabase: any, terminalId: string, syncedItems: SyncItem[], failedItems: FailedSyncItem[]) {
  // Update completed items
  for (const item of syncedItems) {
    await supabase
      .from('pos_sync_queue_enhanced')
      .update({
        sync_status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('resource_id', item.resource_id)
      .eq('sync_type', item.sync_type)
  }

  // Update failed items
  for (const item of failedItems) {
    await supabase
      .from('pos_sync_queue_enhanced')
      .update({
        sync_status: 'failed',
        error_message: item.error,
        sync_attempts: supabase.raw('sync_attempts + 1'),
        next_retry_at: item.retry_after
      })
      .eq('id', item.id)
  }
}

// Utility function to validate settings structure
function validateSettingsStructure(settings: any): boolean {
  const requiredCategories = [
    'terminal', 'printer', 'tax', 'discount', 'receipt',
    'payment', 'inventory', 'staff', 'restaurant'
  ]

  return requiredCategories.some(category => settings[category])
}