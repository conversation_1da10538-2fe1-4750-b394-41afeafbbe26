'use client'

import React, { useState, useEffect } from 'react'
import { supabase, createServerSupabaseClient } from '@/lib/supabase'
import { X, Save, Eye, EyeOff, User, Mail, Phone, Shield, Building, CreditCard, Settings, Lock, RefreshCw, UserPlus, Check } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { useTheme } from '@/contexts/theme-context'
import { GlassModal, GlassCard, GlassButton, GlassInput } from '@/components/ui/glass-components'

interface Role {
  id: string
  name: string
  display_name: string
  color: string
}

interface Branch {
  id: string
  name: string
}

interface AddStaffModalProps {
  isOpen: boolean
  onClose: () => void
  onStaffAdded: () => void
}

interface StaffFormData {
  first_name: string
  last_name: string
  email: string
  phone: string
  role_id: string
  branch_id: string
  department: string
  employment_type: string
  hourly_rate: string
  pin: string
  can_login_pos: boolean
  can_login_admin: boolean
  emergency_contact_name: string
  emergency_contact_phone: string
  notes: string
}

const AddStaffModal = ({ isOpen, onClose, onStaffAdded }: AddStaffModalProps) => {
  const { isDarkTheme } = useTheme()
  const [roles, setRoles] = useState<Role[]>([])
  const [branches, setBranches] = useState<Branch[]>([])
  const [loading, setLoading] = useState(false)
  const [showPin, setShowPin] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  
  const [formData, setFormData] = useState<StaffFormData>({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    role_id: '',
    branch_id: '',
    department: '',
    employment_type: 'full-time',
    hourly_rate: '',
    pin: '',
    can_login_pos: true,
    can_login_admin: false,
    emergency_contact_name: '',
    emergency_contact_phone: '',
    notes: ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (isOpen) {
      loadRoles()
      loadBranches()
      generateRandomPin()
    }
  }, [isOpen])

  const loadRoles = async () => {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('id, name, display_name, color')
        .eq('is_active', true)
        .order('level')

      if (error) {
        // If roles table doesn't exist, set empty array
        if (error.message?.includes('relation') || error.message?.includes('does not exist')) {
          console.log('Roles table not found - staff management system not yet set up')
          setRoles([])
          return
        }
        throw error
      }
      setRoles(data || [])
    } catch (error) {
      console.error('Error loading roles:', error)
      setRoles([]) // Set empty array as fallback
    }
  }

  const loadBranches = async () => {
    try {
      const { data, error } = await supabase
        .from('branches')
        .select('id, name')
        .eq('is_active', true)
        .order('name')

      if (error) {
        // If branches table doesn't exist, set empty array
        if (error.message?.includes('relation') || error.message?.includes('does not exist')) {
          console.log('Branches table not found - branch management system not yet set up')
          setBranches([])
          return
        }
        throw error
      }
      setBranches(data || [])
    } catch (error) {
      console.error('Error loading branches:', error)
      setBranches([]) // Set empty array as fallback
    }
  }

  const generateRandomPin = () => {
    const pin = Math.floor(1000 + Math.random() * 9000).toString()
    setFormData(prev => ({ ...prev, pin }))
  }

  const generatePin = () => {
    generateRandomPin()
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Step 1 validation
    if (!formData.first_name.trim()) newErrors.first_name = 'First name is required'
    if (!formData.last_name.trim()) newErrors.last_name = 'Last name is required'
    if (!formData.email.trim()) newErrors.email = 'Email is required'
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) newErrors.email = 'Invalid email format'
    
    // Step 2 validation
    if (!formData.role_id) newErrors.role_id = 'Role is required'
    if (!formData.department.trim()) newErrors.department = 'Department is required'
    
    // Step 3 validation
    if (formData.can_login_pos && !formData.pin.trim()) newErrors.pin = 'PIN is required for POS access'
    else if (formData.pin && !/^\d{4,6}$/.test(formData.pin)) newErrors.pin = 'PIN must be 4-6 digits'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error('Please fix the errors before continuing')
      return
    }

    setLoading(true)
    
    try {
      // Call the server-side API to create staff
      const response = await fetch('/api/staff/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: formData.first_name,
          lastName: formData.last_name,
          email: formData.email,
          phone: formData.phone,
          roleId: formData.role_id,
          branchId: formData.branch_id,
          department: formData.department,
          employmentType: formData.employment_type,
          hourlyRate: formData.hourly_rate,
          notes: formData.notes,
          canLoginPos: formData.can_login_pos,
          canLoginAdmin: formData.can_login_admin,
          pin: formData.pin,
          emergencyContactName: formData.emergency_contact_name,
          emergencyContactPhone: formData.emergency_contact_phone
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create staff member')
      }

      toast.success('Staff member added successfully')
      onStaffAdded()
      onClose()
      
      // Reset form
      setFormData({
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        role_id: '',
        branch_id: '',
        department: '',
        employment_type: 'full-time',
        hourly_rate: '',
        pin: '',
        can_login_pos: true,
        can_login_admin: false,
        emergency_contact_name: '',
        emergency_contact_phone: '',
        notes: ''
      })
      setCurrentStep(1)
      
    } catch (error) {
      console.error('Error creating staff:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to create staff member')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof StaffFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const nextStep = () => {
    if (currentStep === 1) {
      // Validate step 1
      const step1Errors: Record<string, string> = {}
      if (!formData.first_name.trim()) step1Errors.first_name = 'First name is required'
      if (!formData.last_name.trim()) step1Errors.last_name = 'Last name is required'
      if (!formData.email.trim()) step1Errors.email = 'Email is required'
      else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) step1Errors.email = 'Invalid email format'
      
      if (Object.keys(step1Errors).length > 0) {
        setErrors(step1Errors)
        return
      }
    }
    
    if (currentStep === 2) {
      // Validate step 2
      const step2Errors: Record<string, string> = {}
      if (!formData.role_id) step2Errors.role_id = 'Role is required'
      if (!formData.department.trim()) step2Errors.department = 'Department is required'
      
      if (Object.keys(step2Errors).length > 0) {
        setErrors(step2Errors)
        return
      }
    }
    
    setCurrentStep(prev => Math.min(prev + 1, 3))
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  if (!isOpen) return null

  return (
    <GlassModal
      isOpen={isOpen}
      onClose={onClose}
      title="Add New Staff Member"
      className="max-w-2xl w-full max-h-[90vh] flex flex-col"
    >
      <form onSubmit={handleSubmit} className="flex flex-col h-full">
        {/* Progress Steps */}
        <div className="flex items-center justify-center space-x-4 mb-8">
          {[1, 2, 3].map((step) => (
            <div key={step} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                step === currentStep
                  ? isDarkTheme 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-blue-600 text-white'
                  : step < currentStep
                    ? isDarkTheme 
                      ? 'bg-green-500 text-white' 
                      : 'bg-green-600 text-white'
                    : isDarkTheme 
                      ? 'bg-white/20 text-white/60' 
                      : 'bg-gray-200 text-gray-500'
              }`}>
                {step < currentStep ? <Check className="w-4 h-4" /> : step}
              </div>
              {step < 3 && (
                <div className={`w-12 h-0.5 mx-2 transition-colors duration-300 ${
                  step < currentStep
                    ? isDarkTheme ? 'bg-green-500' : 'bg-green-600'
                    : isDarkTheme ? 'bg-white/20' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step Labels */}
        <div className="flex justify-between text-sm mb-6">
          <span className={`transition-colors duration-300 ${
            currentStep === 1 
              ? isDarkTheme ? 'text-blue-400' : 'text-blue-600'
              : isDarkTheme ? 'text-white/60' : 'text-gray-500'
          }`}>
            Personal Info
          </span>
          <span className={`transition-colors duration-300 ${
            currentStep === 2 
              ? isDarkTheme ? 'text-blue-400' : 'text-blue-600'
              : isDarkTheme ? 'text-white/60' : 'text-gray-500'
          }`}>
            Role & Department
          </span>
          <span className={`transition-colors duration-300 ${
            currentStep === 3 
              ? isDarkTheme ? 'text-blue-400' : 'text-blue-600'
              : isDarkTheme ? 'text-white/60' : 'text-gray-500'
          }`}>
            Access & Security
          </span>
        </div>

        {/* Scrollable Form Content */}
        <div className="flex-1 min-h-0">
          <div className="space-y-6">
            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      <User className="w-4 h-4 inline mr-1" />
                      First Name *
                    </label>
                    <input
                      type="text"
                      value={formData.first_name}
                      onChange={(e) => handleInputChange('first_name', e.target.value)}
                      className={`glass-input ${
                        errors.first_name ? 'border-red-500' : ''
                      }`}
                      placeholder="John"
                    />
                    {errors.first_name && (
                      <p className="mt-1 text-sm text-red-400">{errors.first_name}</p>
                    )}
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      Last Name *
                    </label>
                    <input
                      type="text"
                      value={formData.last_name}
                      onChange={(e) => handleInputChange('last_name', e.target.value)}
                      className={`glass-input ${
                        errors.last_name ? 'border-red-500' : ''
                      }`}
                      placeholder="Doe"
                    />
                    {errors.last_name && (
                      <p className="mt-1 text-sm text-red-400">{errors.last_name}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/90' : 'text-gray-700'
                  }`}>
                    <Mail className="w-4 h-4 inline mr-1" />
                    Email Address *
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={`glass-input ${
                      errors.email ? 'border-red-500' : ''
                    }`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-400">{errors.email}</p>
                  )}
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/90' : 'text-gray-700'
                  }`}>
                    <Phone className="w-4 h-4 inline mr-1" />
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="glass-input"
                    placeholder="+****************"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      Emergency Contact Name
                    </label>
                    <input
                      type="text"
                      value={formData.emergency_contact_name}
                      onChange={(e) => handleInputChange('emergency_contact_name', e.target.value)}
                      className="glass-input"
                      placeholder="Jane Doe"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      Emergency Contact Phone
                    </label>
                    <input
                      type="tel"
                      value={formData.emergency_contact_phone}
                      onChange={(e) => handleInputChange('emergency_contact_phone', e.target.value)}
                      className="glass-input"
                      placeholder="+****************"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Role & Department */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/90' : 'text-gray-700'
                  }`}>
                    <Shield className="w-4 h-4 inline mr-1" />
                    Role *
                  </label>
                  <select
                    value={formData.role_id}
                    onChange={(e) => handleInputChange('role_id', e.target.value)}
                    className={`glass-select ${
                      errors.role_id ? 'border-red-500' : ''
                    }`}
                  >
                    <option value="">Select a role</option>
                    {roles.map(role => (
                      <option key={role.id} value={role.id}>
                        {role.display_name}
                      </option>
                    ))}
                  </select>
                  {errors.role_id && (
                    <p className="mt-1 text-sm text-red-400">{errors.role_id}</p>
                  )}
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/90' : 'text-gray-700'
                  }`}>
                    <Building className="w-4 h-4 inline mr-1" />
                    Branch
                  </label>
                  <select
                    value={formData.branch_id}
                    onChange={(e) => handleInputChange('branch_id', e.target.value)}
                    className="glass-select"
                  >
                    <option value="">Select a branch</option>
                    {branches.map(branch => (
                      <option key={branch.id} value={branch.id}>
                        {branch.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      Department *
                    </label>
                    <select
                      value={formData.department}
                      onChange={(e) => handleInputChange('department', e.target.value)}
                      className={`glass-select ${
                        errors.department ? 'border-red-500' : ''
                      }`}
                    >
                      <option value="">Select department</option>
                      <option value="kitchen">Kitchen</option>
                      <option value="front">Front of House</option>
                      <option value="management">Management</option>
                      <option value="delivery">Delivery</option>
                    </select>
                    {errors.department && (
                      <p className="mt-1 text-sm text-red-400">{errors.department}</p>
                    )}
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      Employment Type
                    </label>
                    <select
                      value={formData.employment_type}
                      onChange={(e) => handleInputChange('employment_type', e.target.value)}
                      className="glass-select"
                    >
                      <option value="full-time">Full Time</option>
                      <option value="part-time">Part Time</option>
                      <option value="contract">Contract</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/90' : 'text-gray-700'
                  }`}>
                    Hourly Rate ($)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={formData.hourly_rate}
                    onChange={(e) => handleInputChange('hourly_rate', e.target.value)}
                    className="glass-input"
                    placeholder="15.00"
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                    isDarkTheme ? 'text-white/90' : 'text-gray-700'
                  }`}>
                    Notes
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    rows={3}
                    className="glass-input resize-none"
                    placeholder="Additional notes about this staff member..."
                  />
                </div>
              </div>
            )}

            {/* Step 3: Access & Security */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="can_login_pos"
                      checked={formData.can_login_pos}
                      onChange={(e) => handleInputChange('can_login_pos', e.target.checked)}
                      className="glass-checkbox"
                    />
                    <label 
                      htmlFor="can_login_pos" 
                      className={`text-sm font-medium transition-colors duration-1000 ${
                        isDarkTheme ? 'text-white/90' : 'text-gray-700'
                      }`}
                    >
                      <CreditCard className="w-4 h-4 inline mr-1" />
                      Allow POS System Access
                    </label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="can_login_admin"
                      checked={formData.can_login_admin}
                      onChange={(e) => handleInputChange('can_login_admin', e.target.checked)}
                      className="glass-checkbox"
                    />
                    <label 
                      htmlFor="can_login_admin" 
                      className={`text-sm font-medium transition-colors duration-1000 ${
                        isDarkTheme ? 'text-white/90' : 'text-gray-700'
                      }`}
                    >
                      <Settings className="w-4 h-4 inline mr-1" />
                      Allow Admin Dashboard Access
                    </label>
                  </div>
                </div>

                {formData.can_login_pos && (
                  <div>
                    <label className={`block text-sm font-medium mb-2 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/90' : 'text-gray-700'
                    }`}>
                      <Lock className="w-4 h-4 inline mr-1" />
                      POS PIN
                    </label>
                    <div className="flex space-x-2">
                      <div className="relative flex-1">
                        <input
                          type={showPin ? "text" : "password"}
                          value={formData.pin}
                          onChange={(e) => handleInputChange('pin', e.target.value)}
                          className={`glass-input pr-10 ${
                            errors.pin ? 'border-red-500' : ''
                          }`}
                          placeholder="Enter 4-digit PIN"
                          maxLength={4}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPin(!showPin)}
                          className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded transition-colors duration-200 ${
                            isDarkTheme 
                              ? 'text-white/60 hover:text-white/80 hover:bg-white/10' 
                              : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
                          }`}
                        >
                          {showPin ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                      <GlassButton
                        type="button"
                        onClick={generatePin}
                        variant="secondary"
                        size="small"
                      >
                        <RefreshCw className="w-4 h-4" />
                      </GlassButton>
                    </div>
                    {errors.pin && (
                      <p className="mt-1 text-sm text-red-400">{errors.pin}</p>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Form Footer with Buttons */}
        <div className="glass-modal-footer">
          <div className="flex items-center justify-between">
            <div>
              {currentStep > 1 && (
                <GlassButton
                  type="button"
                  onClick={prevStep}
                  variant="secondary"
                >
                  Previous
                </GlassButton>
              )}
            </div>
            
            <div className="flex space-x-3">
              <GlassButton
                type="button"
                onClick={onClose}
                variant="secondary"
              >
                Cancel
              </GlassButton>
              
              {currentStep < 3 ? (
                <GlassButton
                  type="button"
                  onClick={nextStep}
                  variant="primary"
                >
                  Next
                </GlassButton>
              ) : (
                <GlassButton
                  type="submit"
                  disabled={loading}
                  variant="primary"
                  className="flex items-center"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating...
                    </>
                  ) : (
                    <>
                      <UserPlus className="w-4 h-4 mr-2" />
                      Create Staff Member
                    </>
                  )}
                </GlassButton>
              )}
            </div>
          </div>
        </div>
      </form>
    </GlassModal>
  )
}

export default AddStaffModal