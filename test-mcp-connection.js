// Test script to verify Supabase connection and MCP functionality
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration from the shared config
const SUPABASE_CONFIG = {
  url: 'https://voiwzwyfnkzvcffuxpwl.supabase.co',
  anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0MDU2MzIsImV4cCI6MjA2NDk4MTYzMn0.RvabQ6EGP1gBb0w8J8_VpaqqXgkpEFR7Nbbkmw11wNA',
  serviceRoleKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMTg0NjgzMywiZXhwIjoyMDQ3NDIyODMzfQ.qPWQJLa-C8vPOZBq9FjvC0yKjG6kN8N8jQZ9XGZ8Z0k'
};

async function testSupabaseConnection() {
  console.log('🔍 Testing Supabase connection...');
  
  try {
    // Test basic connection
    const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.anonKey);
    
    console.log('✅ Supabase client created successfully');
    
    // Test database connection by listing tables
    console.log('📋 Fetching table information...');
    
    const { data: tables, error: tablesError } = await supabase
      .rpc('exec_sql', {
        sql: "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' LIMIT 10"
      });
    
    if (tablesError) {
      console.error('❌ Error fetching tables:', tablesError);
      return false;
    }
    
    console.log('✅ Successfully connected to database');
    console.log('📊 Available tables:', tables?.map(t => t.table_name).join(', '));
    
    // Test if delivery_zones table exists
    console.log('🔍 Checking for delivery_zones table...');
    
    const { data: deliveryZones, error: dzError } = await supabase
      .from('delivery_zones')
      .select('id, name')
      .limit(1);
    
    if (dzError) {
      console.log('⚠️  delivery_zones table not found or accessible:', dzError.message);
      
      // Try to create the table using service role key
      console.log('🔧 Attempting to create delivery_zones table...');
      
      const adminSupabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.serviceRoleKey);
      
      const { error: createError } = await adminSupabase.rpc('exec_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS public.delivery_zones (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name VARCHAR(255) NOT NULL,
            description TEXT,
            coordinates JSONB NOT NULL,
            delivery_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            minimum_order_amount DECIMAL(10,2) DEFAULT 0.00,
            estimated_delivery_time_min INTEGER DEFAULT 30,
            estimated_delivery_time_max INTEGER DEFAULT 60,
            is_active BOOLEAN DEFAULT true,
            priority INTEGER DEFAULT 0,
            color VARCHAR(7) DEFAULT '#3B82F6',
            branch_id UUID,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW(),
            created_by UUID,
            updated_by UUID
          );
          
          ALTER TABLE public.delivery_zones ENABLE ROW LEVEL SECURITY;
          
          CREATE POLICY IF NOT EXISTS "Enable read access for all users" ON public.delivery_zones
            FOR SELECT USING (true);
        `
      });
      
      if (createError) {
        console.error('❌ Failed to create delivery_zones table:', createError);
      } else {
        console.log('✅ delivery_zones table created successfully');
      }
    } else {
      console.log('✅ delivery_zones table exists and is accessible');
      console.log('📊 Current delivery zones count:', deliveryZones?.length || 0);
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Connection test failed:', error);
    return false;
  }
}

// Test MCP-like functionality
async function testMCPFunctionality() {
  console.log('\n🔧 Testing MCP-like functionality...');
  
  try {
    // Simulate what MCP tools would do
    const supabase = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.anonKey);
    
    // List projects (simulate mcp_supabase-mcp_list_projects)
    console.log('📋 Simulating project listing...');
    const projectInfo = {
      id: SUPABASE_CONFIG.url.match(/https:\/\/(.+)\.supabase\.co/)?.[1],
      name: 'The Small',
      region: 'eu-central-1',
      status: 'active'
    };
    console.log('✅ Project info:', projectInfo);
    
    // List tables (simulate mcp_supabase-mcp_list_tables)
    console.log('📋 Simulating table listing...');
    const { data: tableList, error: tableError } = await supabase
      .rpc('exec_sql', {
        sql: "SELECT table_name, table_schema FROM information_schema.tables WHERE table_schema = 'public'"
      });
    
    if (tableError) {
      console.error('❌ Error listing tables:', tableError);
    } else {
      console.log('✅ Tables found:', tableList?.length || 0);
      console.log('📊 Table names:', tableList?.map(t => t.table_name).slice(0, 10).join(', '));
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ MCP functionality test failed:', error);
    return false;
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting Supabase and MCP connection tests...\n');
  
  const connectionTest = await testSupabaseConnection();
  const mcpTest = await testMCPFunctionality();
  
  console.log('\n📊 Test Results:');
  console.log(`Connection Test: ${connectionTest ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`MCP Functionality Test: ${mcpTest ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (connectionTest && mcpTest) {
    console.log('\n🎉 All tests passed! MCP tools should work correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. MCP tools may have issues.');
  }
}

// Check if @supabase/supabase-js is available
try {
  require('@supabase/supabase-js');
  runTests();
} catch (error) {
  console.error('❌ @supabase/supabase-js not found. Please install it first:');
  console.log('npm install @supabase/supabase-js');
}