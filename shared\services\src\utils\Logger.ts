/**
 * Centralized Logging Utility
 */

import winston from 'winston';

export interface LogContext {
  [key: string]: any;
}

export class Logger {
  private winston: winston.Logger;
  private serviceName: string;

  constructor(serviceName: string, level: string = 'info') {
    this.serviceName = serviceName;
    
    this.winston = winston.createLogger({
      level,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json(),
        winston.format.printf((info) => {
          const { timestamp, level, message, service, ...meta } = info;
          return JSON.stringify({
            timestamp,
            level,
            service: service || this.serviceName,
            message,
            ...meta
          });
        })
      ),
      defaultMeta: { 
        service: this.serviceName 
      },
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          )
        })
      ]
    });

    // Add file transport in production
    if (process.env.NODE_ENV === 'production') {
      this.winston.add(new winston.transports.File({
        filename: `logs/${serviceName}-error.log`,
        level: 'error'
      }));
      
      this.winston.add(new winston.transports.File({
        filename: `logs/${serviceName}.log`
      }));
    }
  }

  info(message: string, context?: LogContext): void {
    this.winston.info(message, context);
  }

  error(message: string, context?: LogContext): void {
    this.winston.error(message, context);
  }

  warn(message: string, context?: LogContext): void {
    this.winston.warn(message, context);
  }

  debug(message: string, context?: LogContext): void {
    this.winston.debug(message, context);
  }

  child(context: LogContext): Logger {
    const childLogger = new Logger(this.serviceName);
    childLogger.winston = this.winston.child(context);
    return childLogger;
  }
}