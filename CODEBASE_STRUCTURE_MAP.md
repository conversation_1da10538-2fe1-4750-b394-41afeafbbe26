# Codebase Structure Map

## Overview
This is a comprehensive Creperie Management System built as a monorepo containing multiple interconnected applications for restaurant management. The system includes an admin dashboard, POS system, customer website, mobile app, and shared components.

## Technology Stack
- **Frontend**: Next.js 14+ with React, TypeScript, Tailwind CSS, shadcn/ui
- **Desktop**: Electron with TypeScript for Windows POS system
- **Mobile**: React Native with TypeScript
- **Backend**: Supabase (PostgreSQL with real-time subscriptions)
- **Authentication**: Supabase Auth
- **Testing**: Jest, Cypress for E2E testing

---

## Root Directory Structure

```
/
├── 📁 admin-dashboard/           # Next.js SaaS Admin Platform
├── 📁 customer-mobile/          # React Native Mobile App
├── 📁 customer-web/             # Next.js Customer Website
├── 📁 database/                 # Database schemas and configurations
├── 📁 docs/                     # Documentation
├── 📁 node_modules/             # Root dependencies
├── 📁 pos-system/               # Electron Windows POS Application
├── 📁 scripts/                  # Build and deployment scripts
├── 📁 shared/                   # Shared Types, Utilities & Constants
├── 📁 supabase/                 # Supabase migrations and functions
├── 📁 tests/                    # Cross-application test utilities
├── 📄 package.json              # Root workspace configuration
├── 📄 package-lock.json         # Lock file
├── 📄 README.md                 # Project documentation
├── 📄 IMPLEMENTATION_COMPLETE_SUMMARY.md
├── 📄 PROGRESS_SUMMARY.md
└── 📄 TESTING_STRATEGY.md
```

---

## Admin Dashboard (`/admin-dashboard/`)

**Purpose**: Restaurant management interface for administrators

### Structure
```
admin-dashboard/
├── 📁 cypress/                  # E2E testing
│   ├── e2e/                     # Test scenarios
│   ├── fixtures/                # Test data
│   └── support/                 # Test utilities
├── 📁 public/                   # Static assets
├── 📁 src/
│   ├── app/                     # Next.js App Router
│   │   ├── api/                 # API routes
│   │   │   ├── customers/       # Customer management APIs
│   │   │   ├── delivery-zones/  # Delivery zone APIs
│   │   │   ├── google-maps/     # Maps integration APIs
│   │   │   ├── ingredients/     # Inventory APIs
│   │   │   ├── menu_categories/ # Menu category APIs
│   │   │   └── subcategories/      # Menu item APIs
│   │   ├── auth/                # Authentication pages
│   │   ├── branches/            # Branch management
│   │   ├── customers/           # Customer management
│   │   ├── dashboard/           # Main dashboard
│   │   ├── delivery-zones/      # Delivery zone management
│   │   ├── menu/                # Menu management
│   │   ├── orders/              # Order management
│   │   ├── pos/                 # POS system integration
│   │   ├── settings/            # System settings
│   │   ├── staff/               # Staff management
│   │   └── system/              # System administration
│   ├── components/              # React components
│   │   ├── branches/            # Branch-related components
│   │   ├── delivery-zones/      # Map and zone components
│   │   ├── menu/                # Menu management components
│   │   ├── notifications/       # Notification system
│   │   ├── staff/               # Staff management components
│   │   └── ui/                  # Reusable UI components
│   ├── contexts/                # React contexts
│   ├── hooks/                   # Custom React hooks
│   ├── lib/                     # Utility libraries
│   ├── locales/                 # i18n translations
│   ├── services/                # Business logic services
│   ├── styles/                  # CSS and styling
│   └── types/                   # TypeScript type definitions
├── 📁 tests/                    # Unit and integration tests
├── 📄 components.json           # shadcn/ui configuration
├── 📄 next.config.js            # Next.js configuration
├── 📄 package.json              # Dependencies and scripts
└── 📄 tailwind.config.js        # Tailwind CSS configuration
```

### Key Features
- Branch management with admin setup
- Menu and inventory management
- Order tracking and analytics
- Staff management with role-based access
- Google Maps integration for delivery zones
- Real-time notifications
- Multi-language support (English/Greek)

---

## Customer Web (`/customer-web/`)

**Purpose**: Public-facing restaurant website for customers

### Structure
```
customer-web/
├── 📁 cypress/                  # E2E testing
├── 📁 public/                   # Static assets & PWA files
├── 📁 src/
│   ├── app/                     # Next.js App Router
│   │   ├── cart/                # Shopping cart
│   │   ├── checkout/            # Checkout process
│   │   ├── login/               # Customer authentication
│   │   ├── menu/                # Menu browsing
│   │   ├── orders/              # Order history
│   │   ├── profile/             # Customer profile
│   │   │   ├── addresses/       # Address management
│   │   │   ├── favorites/       # Favorite items
│   │   │   ├── payment-methods/ # Payment management
│   │   │   └── settings/        # Profile settings
│   │   └── register/            # Customer registration
│   ├── components/              # React components
│   │   ├── cart/                # Cart-related components
│   │   ├── menu/                # Menu display components
│   │   ├── order-tracking/      # Real-time order tracking
│   │   └── ui/                  # Reusable UI components
│   ├── hooks/                   # Custom React hooks
│   ├── lib/                     # Utility libraries
│   ├── locales/                 # i18n translations
│   ├── providers/               # React context providers
│   └── types/                   # TypeScript type definitions
├── 📁 tests/                    # Unit and integration tests
└── 📄 Configuration files (Next.js, Tailwind, etc.)
```

### Key Features
- Progressive Web App (PWA) capabilities
- Menu browsing with categories
- Shopping cart and checkout
- Customer authentication and profiles
- Real-time order tracking
- Address and payment management
- Multi-language support

---

## POS System (`/pos-system/`)

**Purpose**: Electron-based Windows desktop application for point-of-sale operations

### Structure
```
pos-system/
├── 📁 dist/                     # Build output
├── 📁 public/                   # Static assets
├── 📁 src/
│   ├── main/                    # Electron main process
│   │   ├── auth-service.ts      # Authentication service
│   │   ├── database.ts          # Database operations
│   │   ├── main.ts              # Main Electron process
│   │   ├── payment-handlers.ts  # Payment processing
│   │   ├── preload.ts           # Preload script
│   │   ├── settings-service.ts  # Settings management
│   │   ├── staff-auth-service.ts # Staff authentication
│   │   └── sync-service.ts      # Data synchronization
│   ├── renderer/                # Electron renderer process (React)
│   │   ├── components/          # React components
│   │   │   ├── forms/           # Form components
│   │   │   ├── modals/          # Modal dialogs
│   │   │   ├── order/           # Order-related components
│   │   │   └── ui/              # UI components
│   │   ├── contexts/            # React contexts
│   │   ├── hooks/               # Custom hooks
│   │   ├── pages/               # Page components
│   │   ├── services/            # Business logic
│   │   ├── styles/              # CSS and styling
│   │   ├── types/               # TypeScript definitions
│   │   └── utils/               # Utility functions
│   ├── shared/                  # Shared between main/renderer
│   └── types/                   # Global type definitions
├── 📄 webpack.main.config.js    # Webpack config for main
├── 📄 webpack.renderer.config.js # Webpack config for renderer
└── 📄 Configuration files (package.json, tsconfig, etc.)
```

### Key Features
- Order taking and processing
- Customer search and management
- Real-time synchronization with cloud database
- Offline capability
- Staff authentication
- Payment processing integration
- Receipt printing
- Kitchen display integration

---

## Customer Mobile (`/customer-mobile/`)

**Purpose**: React Native mobile application for iOS and Android

### Structure
```
customer-mobile/
├── 📁 src/
│   ├── components/              # Reusable components
│   ├── contexts/                # React contexts
│   │   ├── AuthContext.tsx      # Authentication state
│   │   ├── CartContext.tsx      # Shopping cart state
│   │   ├── FavoritesContext.tsx # Favorites management
│   │   ├── LocationContext.tsx  # Location services
│   │   ├── NotificationContext.tsx # Push notifications
│   │   ├── OrderContext.tsx     # Order management
│   │   └── ThemeContext.tsx     # Theme management
│   ├── hooks/                   # Custom hooks
│   ├── navigation/              # Navigation configuration
│   ├── providers/               # Context providers
│   ├── screens/                 # Screen components
│   │   ├── AuthScreen.tsx       # Authentication
│   │   ├── CartScreen.tsx       # Shopping cart
│   │   ├── CheckoutScreen.tsx   # Checkout process
│   │   ├── HomeScreen.tsx       # Home screen
│   │   ├── MenuScreen.tsx       # Menu browsing
│   │   ├── OrdersScreen.tsx     # Order history
│   │   └── ProfileScreen.tsx    # User profile
│   ├── services/                # Business services
│   ├── types/                   # TypeScript definitions
│   └── utils/                   # Utility functions
├── 📄 App.tsx                   # Root component
└── 📄 package.json              # Dependencies
```

### Key Features
- Mobile ordering
- Loyalty program integration
- Push notifications
- Location-based features
- Order tracking
- User authentication
- Favorites management

---

## Shared (`/shared/`)

**Purpose**: Common utilities, types, and configurations shared across all applications

### Structure
```
shared/
├── 📁 auth/                     # Authentication utilities
│   ├── auth-utils.ts           # Auth helper functions
│   ├── config.ts               # Auth configuration
│   ├── database.types.ts       # Database type definitions
│   ├── otp-utils.ts            # OTP utilities
│   ├── password-utils.ts       # Password utilities
│   ├── pin-utils.ts            # PIN utilities
│   ├── role-utils.ts           # Role management
│   ├── session-utils.ts        # Session management
│   ├── two-fa-utils.ts         # Two-factor authentication
│   └── types.ts                # Auth types
├── 📁 security/                 # Security utilities
│   ├── audit-logging.ts        # Audit logging
│   ├── auth-security.ts        # Security middleware
│   ├── data-protection.ts      # Data protection
│   ├── greek-data-protection.ts # GDPR compliance
│   ├── pci-compliance.ts       # PCI DSS compliance
│   ├── security-config.ts      # Security configuration
│   └── security-middleware.ts  # Security middleware
├── 📁 types/                    # Shared type definitions
└── 📄 package.json              # Shared dependencies
```

### Key Features
- Centralized authentication utilities
- Security and compliance tools
- Shared TypeScript types
- Common utilities and helpers

---

## Database (`/database/`)

**Purpose**: Database schemas and configuration (currently minimal structure)

---

## Supabase (`/supabase/`)

**Purpose**: Supabase configuration, migrations, and cloud functions

### Structure
```
supabase/
├── 📁 functions/                # Edge functions
│   ├── order-status-trigger/   # Order status notifications
│   ├── send-email-notification/ # Email notifications
│   ├── send-push-notification/ # Push notifications
│   ├── send-sms-notification/  # SMS notifications
│   └── sync-settings/          # Settings synchronization
└── 📁 migrations/              # Database migrations
    ├── 20241201000000_realtime_notifications.sql
    ├── 20241202000000_security_audit_system.sql
    ├── 20241203000000_admin_control_system.sql
    ├── 20241204000000_staff_management_system.sql
    ├── 20241205000000_branches_management_system.sql
    ├── 20241210000000_dev_admin_user.sql
    ├── 20241210000001_dev_rls_bypass.sql
    ├── 20241211000000_enhanced_menu_system.sql
    └── 20241225000000_customer_addresses_system.sql
```

### Key Features
- Real-time notifications system
- Security and audit logging
- Staff and branch management
- Menu management system
- Customer address management

---

## Documentation (`/docs/`)

### Structure
```
docs/
├── 📄 README.md                 # Documentation overview
├── 📄 REALTIME_NOTIFICATIONS.md # Notification system docs
├── 📁 guides/                   # User guides
├── 📁 setup/                    # Setup instructions
├── 📁 support/                  # Support documentation
└── 📁 training/                 # Training materials
```

---

## Tests (`/tests/`)

**Purpose**: Cross-application test utilities and integration tests

### Structure
```
tests/
├── 📁 integration/              # Integration tests
│   └── synchronization/         # Sync testing
├── 📁 synchronization/          # Sync test framework
│   ├── data-consistency-monitor.ts
│   ├── performance-monitor.ts
│   ├── sync-test-framework.ts
│   └── test-scenarios.ts
└── 📄 test-runner.ts            # Test runner
```

---

## Scripts (`/scripts/`)

**Purpose**: Build and deployment automation scripts

```
scripts/
└── 📄 setup-realtime-notifications.ps1 # PowerShell setup script
```

---

## Development Workflow

### Workspace Commands (from root)
- `npm run dev` - Start all applications in development mode
- `npm run build` - Build all applications
- `npm run lint` - Run linting across all workspaces
- `npm run test` - Run tests across all workspaces

### Individual Application Commands
- `npm run admin:dev` - Admin Dashboard
- `npm run pos:dev` - POS System
- `npm run web:dev` - Customer Website
- `npm run mobile:dev` - Mobile App
- `npm run db:migrate` - Database migrations
- `npm run db:seed` - Database seeding

---

## Key Technologies per Module

| Module | Primary Tech | Secondary Tech | Testing |
|--------|-------------|----------------|---------|
| Admin Dashboard | Next.js 14, React, TypeScript | Tailwind, shadcn/ui, Google Maps | Jest, Cypress |
| Customer Web | Next.js 14, React, TypeScript | PWA, Tailwind, shadcn/ui | Jest, Cypress |
| POS System | Electron, React, TypeScript | Webpack, Tailwind | Jest |
| Mobile App | React Native, TypeScript | Context API, AsyncStorage | Jest |
| Shared | TypeScript | Security utilities, Auth utils | Jest |
| Database | Supabase/PostgreSQL | Edge functions, RLS | Migration tests |

---

## Architecture Highlights

1. **Monorepo Structure**: All applications share common dependencies and types
2. **Real-time Synchronization**: Supabase provides real-time data sync across all platforms
3. **Multi-platform Support**: Web, mobile, and desktop applications
4. **Security-first Design**: Comprehensive security utilities and compliance tools
5. **Internationalization**: Multi-language support across platforms
6. **Progressive Web App**: Customer web application supports offline functionality
7. **Role-based Access Control**: Different access levels for staff, managers, and admins
8. **Real-time Notifications**: Push notifications, email, and SMS integration

---

*Generated on: 2025-06-20*
*Structure based on current codebase state*