/**
 * Logger Factory - Production-ready logging infrastructure
 * Replaces console.log statements with structured logging
 */

import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import { ElasticsearchTransport } from 'winston-elasticsearch';

export interface LogContext {
  [key: string]: any;
  requestId?: string;
  userId?: string;
  correlationId?: string;
  traceId?: string;
  spanId?: string;
}

export interface LoggerConfig {
  serviceName: string;
  environment: string;
  logLevel: string;
  enableConsole: boolean;
  enableFile: boolean;
  enableElasticsearch: boolean;
  elasticsearchConfig?: {
    node: string;
    index: string;
  };
}

export class LoggerFactory {
  private static instances: Map<string, winston.Logger> = new Map();

  static createLogger(config: LoggerConfig): winston.Logger {
    const key = `${config.serviceName}-${config.environment}`;
    
    if (this.instances.has(key)) {
      return this.instances.get(key)!;
    }

    const logger = winston.createLogger({
      level: config.logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json(),
        winston.format.printf(({ timestamp, level, message, service, environment, ...meta }) => {
          return JSON.stringify({
            '@timestamp': timestamp,
            level,
            service: service || config.serviceName,
            environment: environment || config.environment,
            message,
            ...meta
          });
        })
      ),
      defaultMeta: { 
        service: config.serviceName,
        environment: config.environment,
        version: process.env.npm_package_version || '1.0.0',
        hostname: process.env.HOSTNAME || 'unknown',
        pid: process.pid
      },
      transports: this.createTransports(config)
    });

    this.instances.set(key, logger);
    return logger;
  }

  private static createTransports(config: LoggerConfig): winston.transport[] {
    const transports: winston.transport[] = [];

    // Console transport for development
    if (config.enableConsole) {
      transports.push(new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.printf(({ timestamp, level, service, message, ...meta }) => {
            const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
            return `${timestamp} [${service}] ${level}: ${message}${metaStr}`;
          })
        )
      }));
    }

    // File transport with rotation
    if (config.enableFile) {
      transports.push(new DailyRotateFile({
        filename: `logs/${config.serviceName}-%DATE%.log`,
        datePattern: 'YYYY-MM-DD',
        maxSize: '20m',
        maxFiles: '14d',
        level: 'info'
      }));

      transports.push(new DailyRotateFile({
        filename: `logs/${config.serviceName}-error-%DATE%.log`,
        datePattern: 'YYYY-MM-DD',
        maxSize: '20m',
        maxFiles: '30d',
        level: 'error'
      }));
    }

    // Elasticsearch transport for production
    if (config.enableElasticsearch && config.elasticsearchConfig) {
      transports.push(new ElasticsearchTransport({
        level: 'info',
        clientOpts: {
          node: config.elasticsearchConfig.node
        },
        index: config.elasticsearchConfig.index
      }));
    }

    return transports;
  }

  static getDefaultConfig(serviceName: string): LoggerConfig {
    const environment = process.env.NODE_ENV || 'development';
    
    return {
      serviceName,
      environment,
      logLevel: process.env.LOG_LEVEL || (environment === 'production' ? 'warn' : 'debug'),
      enableConsole: environment !== 'production',
      enableFile: true,
      enableElasticsearch: environment === 'production',
      elasticsearchConfig: environment === 'production' ? {
        node: process.env.ELASTICSEARCH_URL || 'http://localhost:9200',
        index: `creperie-logs-${environment}`
      } : undefined
    };
  }
}