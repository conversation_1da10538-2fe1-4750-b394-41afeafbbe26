'use client';

import * as React from 'react';
import { Moon, Sun, Monitor, ChevronDown } from 'lucide-react';
import { useTheme } from '@/providers/theme-provider';
import { GlassButton } from '@/components/ui/glass-components';
import { cn } from '@/lib/utils';

export function ThemeToggle() {
  const { setTheme, theme } = useTheme();
  const [isOpen, setIsOpen] = React.useState(false);

  const themes = [
    { value: 'light', label: 'Light', icon: Sun },
    { value: 'dark', label: 'Dark', icon: Moon },
    { value: 'system', label: 'System', icon: Monitor },
  ];

  const currentTheme = themes.find(t => t.value === theme);

  return (
    <div className="relative">
      <GlassButton
        variant="ghost"
        size="sm"
        className="h-8 px-2 gap-1"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Toggle theme"
      >
        {currentTheme && (
          <currentTheme.icon className="h-4 w-4" />
        )}
        <ChevronDown className="h-3 w-3" />
      </GlassButton>
      
      {isOpen && (
        <>
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute right-0 top-full mt-1 z-50 min-w-[120px] rounded-md border bg-white/80 backdrop-blur-md shadow-lg dark:bg-gray-900/80">
            {themes.map((themeOption) => {
              const Icon = themeOption.icon;
              return (
                <button
                  key={themeOption.value}
                  onClick={() => {
                    setTheme(themeOption.value as any);
                    setIsOpen(false);
                  }}
                  className={cn(
                    "flex w-full items-center gap-2 px-3 py-2 text-sm hover:bg-gray-100/50 dark:hover:bg-gray-800/50 first:rounded-t-md last:rounded-b-md",
                    theme === themeOption.value && "bg-gray-100/70 dark:bg-gray-800/70"
                  )}
                >
                  <Icon className="h-4 w-4" />
                  <span>{themeOption.label}</span>
                </button>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
}

// Simple toggle button version (without dropdown)
export function SimpleThemeToggle() {
  const { setTheme, resolvedTheme } = useTheme();

  return (
    <Button
      variant="ghost"
      size="sm"
      className="h-8 w-8 px-0"
      onClick={() => setTheme(resolvedTheme === 'dark' ? 'light' : 'dark')}
      aria-label={`Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} theme`}
    >
      <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">Toggle theme</span>
    </Button>
  );
}