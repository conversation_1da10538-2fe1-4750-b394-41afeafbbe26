'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Slot } from '@radix-ui/react-slot';

// Import glassmorphism styles
import '@/styles/glassmorphism.css';

export interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'danger'
    | 'warning'
    | 'info'
    | 'crepe'
    | 'chocolate'
    | 'strawberry'
    | 'blueberry';
  size?: 'sm' | 'md' | 'lg';
  asChild?: boolean;
}

const GlassCard = React.forwardRef<HTMLDivElement, GlassCardProps>(
  ({ className, variant = 'default', size = 'md', asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'div';
    return (
      <Comp
        className={cn(
          'glass-card',
          {
            'glass-primary': variant === 'primary',
            'glass-secondary': variant === 'secondary',
            'glass-success': variant === 'success',
            'glass-danger': variant === 'danger',
            'glass-warning': variant === 'warning',
            'glass-info': variant === 'info',
            'glass-crepe': variant === 'crepe',
            'glass-chocolate': variant === 'chocolate',
            'glass-strawberry': variant === 'strawberry',
            'glass-blueberry': variant === 'blueberry',
            'glass-card-sm': size === 'sm',
            'glass-card-md': size === 'md',
            'glass-card-lg': size === 'lg',
          },
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
GlassCard.displayName = 'GlassCard';

export interface GlassNavbarProps extends React.HTMLAttributes<HTMLDivElement> {
  asChild?: boolean;
}

const GlassNavbar = React.forwardRef<HTMLDivElement, GlassNavbarProps>(
  ({ className, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'div';
    return <Comp className={cn('glass-navbar', className)} ref={ref} {...props} />;
  }
);
GlassNavbar.displayName = 'GlassNavbar';

export interface GlassButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'danger'
    | 'warning'
    | 'info'
    | 'crepe'
    | 'chocolate'
    | 'strawberry'
    | 'blueberry'
    | 'outline';
  size?: 'sm' | 'md' | 'lg';
  asChild?: boolean;
}

const GlassButton = React.forwardRef<HTMLButtonElement, GlassButtonProps>(
  ({ className, variant = 'default', size = 'md', asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(
          'glass-button',
          {
            'glass-primary': variant === 'primary',
            'glass-secondary': variant === 'secondary',
            'glass-success': variant === 'success',
            'glass-danger': variant === 'danger',
            'glass-warning': variant === 'warning',
            'glass-info': variant === 'info',
            'glass-crepe': variant === 'crepe',
            'glass-chocolate': variant === 'chocolate',
            'glass-strawberry': variant === 'strawberry',
            'glass-blueberry': variant === 'blueberry',
            'glass-outline': variant === 'outline',
            'glass-button-sm': size === 'sm',
            'glass-button-md': size === 'md',
            'glass-button-lg': size === 'lg',
          },
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
GlassButton.displayName = 'GlassButton';

export interface GlassInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  multiline?: boolean;
  rows?: number;
  asChild?: boolean;
}

const GlassInput = React.forwardRef<HTMLInputElement | HTMLTextAreaElement, GlassInputProps>(
  ({ className, label, multiline = false, rows = 3, asChild = false, ...props }, ref) => {
    // Use a type assertion to handle the component type
    const Comp = asChild ? Slot : multiline ? 'textarea' : 'input';

    return (
      <div className="flex flex-col space-y-2">
        {label && <label className="text-sm font-medium">{label}</label>}
        {multiline ? (
          <textarea
            className={cn('glass-input', className)}
            rows={rows}
            ref={ref as React.Ref<HTMLTextAreaElement>}
            {...(props as React.TextareaHTMLAttributes<HTMLTextAreaElement>)}
          />
        ) : (
          <input
            className={cn('glass-input', className)}
            ref={ref as React.Ref<HTMLInputElement>}
            {...(props as React.InputHTMLAttributes<HTMLInputElement>)}
          />
        )}
      </div>
    );
  }
);
GlassInput.displayName = 'GlassInput';

export interface GlassDividerProps extends React.HTMLAttributes<HTMLHRElement> {}

const GlassDivider = React.forwardRef<HTMLHRElement, GlassDividerProps>(
  ({ className, ...props }, ref) => {
    return <hr className={cn('glass-divider', className)} ref={ref} {...props} />;
  }
);
GlassDivider.displayName = 'GlassDivider';

export interface GlassBadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'danger'
    | 'warning'
    | 'info'
    | 'crepe'
    | 'chocolate'
    | 'strawberry'
    | 'blueberry';
}

const GlassBadge = React.forwardRef<HTMLDivElement, GlassBadgeProps>(
  ({ className, variant = 'default', ...props }, ref) => {
    return (
      <div
        className={cn(
          'glass-badge',
          {
            'glass-primary': variant === 'primary',
            'glass-secondary': variant === 'secondary',
            'glass-success': variant === 'success',
            'glass-danger': variant === 'danger',
            'glass-warning': variant === 'warning',
            'glass-info': variant === 'info',
            'glass-crepe': variant === 'crepe',
            'glass-chocolate': variant === 'chocolate',
            'glass-strawberry': variant === 'strawberry',
            'glass-blueberry': variant === 'blueberry',
          },
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
GlassBadge.displayName = 'GlassBadge';

export interface GlassModalProps extends React.HTMLAttributes<HTMLDivElement> {}

const GlassModal = React.forwardRef<HTMLDivElement, GlassModalProps>(
  ({ className, ...props }, ref) => {
    return <div className={cn('glass-modal', className)} ref={ref} {...props} />;
  }
);
GlassModal.displayName = 'GlassModal';

export { GlassCard, GlassNavbar, GlassButton, GlassInput, GlassDivider, GlassBadge, GlassModal };
