'use client';

import React from 'react';
import { AlertTriangle, RefreshCw, Home, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { GlassCard, GlassButton } from './glass-components';
import { cn } from '@/lib/utils';

interface ErrorStateProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  showHomeButton?: boolean;
  showBackButton?: boolean;
  className?: string;
}

// Generic error state component
export function ErrorState({
  title = 'Something went wrong',
  message = 'We encountered an error while loading this content.',
  onRetry,
  showHomeButton = true,
  showBackButton = false,
  className,
}: ErrorStateProps) {
  return (
    <div className={cn('flex items-center justify-center min-h-[400px]', className)}>
      <GlassCard className="p-8 text-center max-w-md">
        <AlertTriangle className="w-16 h-16 text-amber-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold mb-2">{title}</h3>
        <p className="text-muted-foreground mb-6">{message}</p>
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {onRetry && (
            <GlassButton onClick={onRetry} variant="primary" className="flex items-center space-x-2">
              <RefreshCw className="w-4 h-4" />
              <span>Try Again</span>
            </GlassButton>
          )}
          
          {showBackButton && (
            <GlassButton onClick={() => window.history.back()} variant="secondary" className="flex items-center space-x-2">
              <ArrowLeft className="w-4 h-4" />
              <span>Go Back</span>
            </GlassButton>
          )}
          
          {showHomeButton && (
            <Link href="/">
              <GlassButton variant="secondary" className="flex items-center space-x-2">
                <Home className="w-4 h-4" />
                <span>Go Home</span>
              </GlassButton>
            </Link>
          )}
        </div>
      </GlassCard>
    </div>
  );
}

// Network error state
export function NetworkError({ onRetry }: { onRetry?: () => void }) {
  return (
    <ErrorState
      title="Connection Error"
      message="Unable to connect to our servers. Please check your internet connection and try again."
      onRetry={onRetry}
    />
  );
}

// Data loading error
export function DataLoadError({ onRetry, itemType = 'data' }: { onRetry?: () => void; itemType?: string }) {
  return (
    <ErrorState
      title={`Failed to load ${itemType}`}
      message={`We couldn't load the ${itemType} you requested. This might be a temporary issue.`}
      onRetry={onRetry}
    />
  );
}

// Menu category error
export function MenuCategoryError({ category, onRetry }: { category: string; onRetry?: () => void }) {
  return (
    <ErrorState
      title="Menu Unavailable"
      message={`We're having trouble loading the ${category} menu. Please try again or browse other categories.`}
      onRetry={onRetry}
      showHomeButton={true}
    />
  );
}

// Cart error
export function CartError({ onRetry }: { onRetry?: () => void }) {
  return (
    <ErrorState
      title="Cart Error"
      message="We couldn't load your cart items. Your items are safe and will be restored when the connection is back."
      onRetry={onRetry}
    />
  );
}

// 404 Not Found
export function NotFoundError({ itemType = 'page' }: { itemType?: string }) {
  return (
    <ErrorState
      title="Not Found"
      message={`The ${itemType} you're looking for doesn't exist or has been moved.`}
      showHomeButton={true}
      showBackButton={true}
    />
  );
}

// Inline error for smaller components
export function InlineError({ message, onRetry, className }: { message: string; onRetry?: () => void; className?: string }) {
  return (
    <div className={cn('flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg', className)}>
      <div className="flex items-center space-x-2">
        <AlertTriangle className="w-5 h-5 text-red-500" />
        <span className="text-red-700 text-sm">{message}</span>
      </div>
      {onRetry && (
        <button
          onClick={onRetry}
          className="text-red-600 hover:text-red-800 text-sm font-medium flex items-center space-x-1"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Retry</span>
        </button>
      )}
    </div>
  );
}

// Empty state (when no data is available but no error occurred)
export function EmptyState({
  title = 'No items found',
  message = 'There are no items to display at the moment.',
  actionLabel,
  onAction,
  className,
}: {
  title?: string;
  message?: string;
  actionLabel?: string;
  onAction?: () => void;
  className?: string;
}) {
  return (
    <div className={cn('flex items-center justify-center min-h-[300px]', className)}>
      <div className="text-center">
        <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
          <AlertTriangle className="w-8 h-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        <p className="text-muted-foreground mb-4">{message}</p>
        
        {actionLabel && onAction && (
          <GlassButton onClick={onAction} variant="primary">
            {actionLabel}
          </GlassButton>
        )}
      </div>
    </div>
  );
}