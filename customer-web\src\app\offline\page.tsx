import Link from 'next/link';
import { Glass<PERSON>ard } from '@/components/ui/glass-components';
import { RefreshButton } from '@/components/ui/refresh-button';

export const metadata = {
  title: 'Offline - Creperie',
  description: 'You are currently offline',
};

export default function OfflinePage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
      <GlassCard className="max-w-md p-8 space-y-6">
        <h1 className="text-3xl font-bold">You're Offline</h1>

        <div className="p-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-24 h-24 mx-auto text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M18.364 5.636a9 9 0 010 12.728m-3.536-3.536a5 5 0 010-7.072m-3.183 1.757a3 3 0 010 3.558M6.75 17.25h10.5a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25H6.75A2.25 2.25 0 004.5 6v9a2.25 2.25 0 002.25 2.25z"
            />
          </svg>
        </div>

        <p className="text-lg">
          It looks like you're currently offline. Some features may be unavailable until you
          reconnect.
        </p>

        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            You can still browse previously loaded content and view your cart.
          </p>

          <div className="flex flex-col space-y-2">
            <Link
              href="/"
              className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
            >
              Go to Homepage
            </Link>

            <RefreshButton className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2" />
          </div>
        </div>
      </GlassCard>
    </div>
  );
}
