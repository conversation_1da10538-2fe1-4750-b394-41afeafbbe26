const puppeteer = require('puppeteer');

async function testMenuPage() {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  console.log('🧪 Starting Menu Page Testing...\n');
  
  const errors = [];
  const warnings = [];
  
  // Capture console errors
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.push(`Console Error: ${msg.text()}`);
    } else if (msg.type() === 'warning') {
      warnings.push(`Console Warning: ${msg.text()}`);
    }
  });
  
  // Capture page errors
  page.on('pageerror', error => {
    errors.push(`Page Error: ${error.message}`);
  });
  
  try {
    console.log('1. Testing Page Load...');
    await page.goto('http://localhost:3001/menu', { waitUntil: 'networkidle2' });
    
    // Check page title
    const title = await page.title();
    console.log(`   ✓ Page Title: ${title}`);
    
    // Check if main heading exists
    const heading = await page.$eval('h1', el => el.textContent).catch(() => null);
    console.log(`   ✓ Main Heading: ${heading || 'NOT FOUND'}`);
    
    // Check for essential elements
    console.log('\n2. Testing Essential Elements...');
    
    const elements = {
      'Search Input': 'input[placeholder="Search..."]',
      'Add New Button': 'button:has-text("Add New")',
      'Refresh Button': 'button:has-text("Refresh")',
      'Categories Tab': 'button:has-text("Categories")',
      'Ingredients Tab': 'button:has-text("Ingredients")',
      'Menu Items Tab': 'button:has-text("Menu Items")',
      'Categories Stats': '.backdrop-blur-md:has-text("Categories")',
      'Empty State': 'div:has-text("No categories found")'
    };
    
    for (const [name, selector] of Object.entries(elements)) {
      try {
        await page.waitForSelector(selector, { timeout: 5000 });
        console.log(`   ✓ ${name}: Found`);
      } catch (e) {
        console.log(`   ❌ ${name}: Not Found`);
        errors.push(`Element not found: ${name} (${selector})`);
      }
    }
    
    // Test tab navigation
    console.log('\n3. Testing Tab Navigation...');
    
    const tabs = [
      { name: 'Categories', selector: 'button:has-text("Categories")' },
      { name: 'Ingredients', selector: 'button:has-text("Ingredients")' },
      { name: 'Menu Items', selector: 'button:has-text("Menu Items")' }
    ];
    
    for (const tab of tabs) {
      try {
        await page.click(tab.selector);
        await page.waitForTimeout(1000);
        console.log(`   ✓ ${tab.name} Tab: Clickable`);
      } catch (e) {
        console.log(`   ❌ ${tab.name} Tab: Not Clickable`);
        errors.push(`Tab not clickable: ${tab.name}`);
      }
    }
    
    // Test search functionality
    console.log('\n4. Testing Search Functionality...');
    
    try {
      await page.type('input[placeholder="Search..."]', 'test');
      await page.waitForTimeout(500);
      await page.keyboard.press('Backspace');
      await page.keyboard.press('Backspace');
      await page.keyboard.press('Backspace');
      await page.keyboard.press('Backspace');
      console.log('   ✓ Search Input: Functional');
    } catch (e) {
      console.log('   ❌ Search Input: Not Functional');
      errors.push('Search input not functional');
    }
    
    // Test responsive design
    console.log('\n5. Testing Responsive Design...');
    
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewport({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(500);
      
      try {
        await page.waitForSelector('h1', { timeout: 3000 });
        console.log(`   ✓ ${viewport.name} (${viewport.width}x${viewport.height}): Responsive`);
      } catch (e) {
        console.log(`   ❌ ${viewport.name}: Not Responsive`);
        errors.push(`Responsive design issue: ${viewport.name}`);
      }
    }
    
    // Test performance
    console.log('\n6. Testing Performance...');
    
    const metrics = await page.metrics();
    console.log(`   ✓ DOM Nodes: ${metrics.Nodes}`);
    console.log(`   ✓ JS Heap Used: ${(metrics.JSHeapUsedSize / 1024 / 1024).toFixed(2)} MB`);
    
    if (metrics.Nodes > 1000) {
      warnings.push('High DOM node count detected');
    }
    
    if (metrics.JSHeapUsedSize > 50 * 1024 * 1024) {
      warnings.push('High memory usage detected');
    }
    
    // Test accessibility
    console.log('\n7. Testing Basic Accessibility...');
    
    const accessibilityItems = [
      { name: 'Page has h1', selector: 'h1' },
      { name: 'Images have alt text', selector: 'img:not([alt])' },
      { name: 'Forms have labels', selector: 'input:not([aria-label]):not([placeholder])' },
      { name: 'Buttons have text', selector: 'button:empty' }
    ];
    
    for (const item of accessibilityItems) {
      try {
        if (item.name.includes('not')) {
          // For negative checks (things that shouldn't exist)
          const elements = await page.$$(item.selector);
          if (elements.length === 0) {
            console.log(`   ✓ ${item.name}: Pass`);
          } else {
            console.log(`   ❌ ${item.name}: Fail`);
            warnings.push(`Accessibility issue: ${item.name}`);
          }
        } else {
          await page.waitForSelector(item.selector, { timeout: 3000 });
          console.log(`   ✓ ${item.name}: Pass`);
        }
      } catch (e) {
        console.log(`   ❌ ${item.name}: Fail`);
        warnings.push(`Accessibility issue: ${item.name}`);
      }
    }
    
    // Test error handling
    console.log('\n8. Testing Error Handling...');
    
    // Test refresh button
    try {
      await page.click('button:has-text("Refresh")');
      await page.waitForTimeout(2000);
      console.log('   ✓ Refresh Button: Functional');
    } catch (e) {
      console.log('   ❌ Refresh Button: Not Functional');
      errors.push('Refresh button not functional');
    }
    
    // Test add new button
    try {
      await page.click('button:has-text("Add New")');
      await page.waitForTimeout(1000);
      console.log('   ✓ Add New Button: Functional');
    } catch (e) {
      console.log('   ❌ Add New Button: Not Functional');
      errors.push('Add new button not functional');
    }
    
  } catch (error) {
    errors.push(`Test execution error: ${error.message}`);
  } finally {
    await browser.close();
  }
  
  // Generate report
  console.log('\n' + '='.repeat(60));
  console.log('📋 MENU PAGE TEST REPORT');
  console.log('='.repeat(60));
  
  console.log(`\n🔍 Total Errors Found: ${errors.length}`);
  if (errors.length > 0) {
    console.log('\n❌ ERRORS:');
    errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }
  
  console.log(`\n⚠️  Total Warnings: ${warnings.length}`);
  if (warnings.length > 0) {
    console.log('\n⚠️  WARNINGS:');
    warnings.forEach((warning, index) => {
      console.log(`${index + 1}. ${warning}`);
    });
  }
  
  if (errors.length === 0 && warnings.length === 0) {
    console.log('\n✅ All tests passed! No critical issues found.');
  }
  
  return { errors, warnings };
}

if (require.main === module) {
  testMenuPage().catch(console.error);
}

module.exports = testMenuPage;