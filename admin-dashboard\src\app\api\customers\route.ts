import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// Add CORS headers for POS system access
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
}

export async function OPTIONS(_request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: corsHeaders,
  })
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    // Extract query parameters
    const search = searchParams.get('search')
    const status = searchParams.get('status') // 'active', 'inactive', 'all'
    const sort_by = searchParams.get('sort_by') || 'created_at'
    const sort_order = searchParams.get('sort_order') || 'desc'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    
    let query = supabase
      .from('customers')
      .select('*', { count: 'exact' })
    
    // Apply filters
    // Note: customers table doesn't have is_active field, so we skip status filtering
    
    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%,phone.ilike.%${search}%`)
    }
    
    // Apply sorting
    query = query.order(sort_by, { ascending: sort_order === 'asc' })
    
    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)
    
    const { data, error, count } = await query
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch customers' },
        { status: 500, headers: corsHeaders }
      )
    }
    
    // Enhance customer data with order statistics
    const enhancedCustomers = await Promise.all(
      (data || []).map(async (customer) => {
        // Use existing data from customers table and calculate additional fields
        const totalOrders = customer.total_orders || 0
        const totalSpent = totalOrders * 15 // Estimate average order value of €15
        const avgOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0
        const lastOrderDate = customer.updated_at // Use updated_at as proxy for last order
        
        // Determine loyalty tier
        const loyaltyTier = getLoyaltyTier(customer.loyalty_points || 0, totalSpent)
        
        return {
          ...customer,
          total_orders: totalOrders,
          total_spent: totalSpent,
          avg_order_value: avgOrderValue,
          last_order_date: lastOrderDate,
          loyalty_tier: loyaltyTier
        }
      })
    )
    
    return NextResponse.json({
      data: enhancedCustomers,
      pagination: {
        page,
        limit,
        total: count || 0,
        pages: Math.ceil((count || 0) / limit)
      }
    }, { headers: corsHeaders })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500, headers: corsHeaders }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const {
      phone,
      name,
      email,
      address,
      postal_code,
      notes,
      name_on_ringer
    } = await request.json();

    if (!phone || !name || !address) {
      return NextResponse.json(
        { success: false, error: 'Phone, name, and address are required' },
        { status: 400 }
      );
    }


    try {
      // Use secure Supabase client with parameterized queries
      const supabase = createServerSupabaseClient()
      
      const { data: newCustomer, error: insertError } = await supabase
        .from('customers')
        .insert({
          phone: phone,
          name: name,
          email: email || null,
          address: address || null,
          postal_code: postal_code || null,
          notes: notes || null,
          ringer_name: name_on_ringer || null,
        })
        .select()
        .single()

      if (insertError) {
        return NextResponse.json(
          { success: false, error: 'Failed to create customer: ' + insertError.message },
          { status: 500, headers: corsHeaders }
        );
      }

      return NextResponse.json({
        success: true,
        customer: {
          id: newCustomer.id,
          phone: newCustomer.phone,
          name: newCustomer.name,
          email: newCustomer.email,
          address: newCustomer.address,
          postal_code: newCustomer.postal_code,
          notes: newCustomer.notes,
          name_on_ringer: newCustomer.ringer_name,
        }
      }, { headers: corsHeaders });

    } catch (dbError) {
      return NextResponse.json(
        { success: false, error: 'Unexpected error occurred while creating customer' },
        { status: 500, headers: corsHeaders }
      );
    }

  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Failed to create customer' },
      { status: 500 }
    );
  }
}

function getLoyaltyTier(points: number, totalSpent: number): string {
  if (points >= 10000 || totalSpent >= 1000) return 'Platinum'
  if (points >= 5000 || totalSpent >= 500) return 'Gold'
  if (points >= 1000 || totalSpent >= 100) return 'Silver'
  return 'Bronze'
} 