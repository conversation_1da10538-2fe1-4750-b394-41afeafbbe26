'use client'

import { CreditC<PERSON>, DollarSign, Percent, Calculator } from 'lucide-react'
import { GlassCard } from '@/components/ui/glass-components'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useTheme } from '@/contexts/theme-context'

interface TaxSettings {
  default_tax_rate: number
  tax_inclusive: boolean
  tax_name: string
  tax_calculation_method: string
  multiple_tax_rates: boolean
  tax_exempt_enabled: boolean
  auto_calculate_tax: boolean
  tax_rounding: string
}

interface DiscountSettings {
  enable_discounts: boolean
  enable_coupons: boolean
  max_discount_percentage: number
  require_manager_approval: boolean
  discount_on_total: boolean
  discount_on_items: boolean
  loyalty_discounts: boolean
  employee_discount_rate: number
  senior_discount_rate: number
  military_discount_rate: number
}

interface PaymentSettingsProps {
  taxSettings: TaxSettings
  discountSettings: DiscountSettings
  onTaxSettingsChange: (settings: TaxSettings) => void
  onDiscountSettingsChange: (settings: DiscountSettings) => void
}

export function PaymentSettings({
  taxSettings,
  discountSettings,
  onTaxSettingsChange,
  onDiscountSettingsChange
}: PaymentSettingsProps) {
  const { isDarkTheme } = useTheme()
  return (
    <div className="space-y-6">
      {/* Tax Configuration */}
      <GlassCard className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Calculator className="h-5 w-5 text-green-400" />
          <h3 className={`text-xl font-semibold transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>Tax Configuration</h3>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Tax Name</label>
              <Input
                value={taxSettings.tax_name}
                onChange={(e) => onTaxSettingsChange({ ...taxSettings, tax_name: e.target.value })}
                className={`transition-all duration-1000 ${
                  isDarkTheme ? 'bg-white/5 border-white/10 text-white' : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="Sales Tax"
              />
            </div>

            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Default Tax Rate (%)</label>
              <Input
                type="number"
                step="0.01"
                value={taxSettings.default_tax_rate}
                onChange={(e) => onTaxSettingsChange({ ...taxSettings, default_tax_rate: parseFloat(e.target.value) || 0 })}
                className={`transition-all duration-1000 ${
                  isDarkTheme ? 'bg-white/5 border-white/10 text-white' : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="8.25"
              />
            </div>
            
            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Tax Calculation Method</label>
              <Select
                value={taxSettings.tax_calculation_method}
                onValueChange={(value: string) => onTaxSettingsChange({ ...taxSettings, tax_calculation_method: value })}
              >
                <SelectTrigger className={`transition-all duration-1000 ${
                  isDarkTheme ? 'bg-white/5 border-white/10 text-white' : 'bg-black/5 border-black/10 text-black'
                }`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">Percentage</SelectItem>
                  <SelectItem value="fixed">Fixed Amount</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Tax Rounding</label>
              <Select
                value={taxSettings.tax_rounding}
                onValueChange={(value: string) => onTaxSettingsChange({ ...taxSettings, tax_rounding: value })}
              >
                <SelectTrigger className={`transition-all duration-1000 ${
                  isDarkTheme ? 'bg-white/5 border-white/10 text-white' : 'bg-black/5 border-black/10 text-black'
                }`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="nearest_cent">Nearest Cent</SelectItem>
                  <SelectItem value="round_up">Round Up</SelectItem>
                  <SelectItem value="round_down">Round Down</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Tax Inclusive Pricing</span>
              <Switch
                checked={taxSettings.tax_inclusive}
                onCheckedChange={(checked: boolean) => onTaxSettingsChange({ ...taxSettings, tax_inclusive: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Multiple Tax Rates</span>
              <Switch
                checked={taxSettings.multiple_tax_rates}
                onCheckedChange={(checked: boolean) => onTaxSettingsChange({ ...taxSettings, multiple_tax_rates: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Tax Exempt Enabled</span>
              <Switch
                checked={taxSettings.tax_exempt_enabled}
                onCheckedChange={(checked: boolean) => onTaxSettingsChange({ ...taxSettings, tax_exempt_enabled: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Auto Calculate Tax</span>
              <Switch
                checked={taxSettings.auto_calculate_tax}
                onCheckedChange={(checked: boolean) => onTaxSettingsChange({ ...taxSettings, auto_calculate_tax: checked })}
              />
            </div>
          </div>
        </div>
      </GlassCard>

      {/* Discount Configuration */}
      <GlassCard className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Percent className="h-5 w-5 text-orange-400" />
          <h3 className={`text-xl font-semibold transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>Discount Configuration</h3>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Maximum Discount Percentage</label>
              <Input
                type="number"
                max="100"
                value={discountSettings.max_discount_percentage}
                onChange={(e) => onDiscountSettingsChange({ ...discountSettings, max_discount_percentage: parseInt(e.target.value) || 0 })}
                className={`transition-all duration-1000 ${
                  isDarkTheme ? 'bg-white/5 border-white/10 text-white' : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="50"
              />
            </div>

            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Employee Discount Rate (%)</label>
              <Input
                type="number"
                value={discountSettings.employee_discount_rate}
                onChange={(e) => onDiscountSettingsChange({ ...discountSettings, employee_discount_rate: parseInt(e.target.value) || 0 })}
                className={`transition-all duration-1000 ${
                  isDarkTheme ? 'bg-white/5 border-white/10 text-white' : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="10"
              />
            </div>

            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Senior Discount Rate (%)</label>
              <Input
                type="number"
                value={discountSettings.senior_discount_rate}
                onChange={(e) => onDiscountSettingsChange({ ...discountSettings, senior_discount_rate: parseInt(e.target.value) || 0 })}
                className={`transition-all duration-1000 ${
                  isDarkTheme ? 'bg-white/5 border-white/10 text-white' : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="5"
              />
            </div>

            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Military Discount Rate (%)</label>
              <Input
                type="number"
                value={discountSettings.military_discount_rate}
                onChange={(e) => onDiscountSettingsChange({ ...discountSettings, military_discount_rate: parseInt(e.target.value) || 0 })}
                className={`transition-all duration-1000 ${
                  isDarkTheme ? 'bg-white/5 border-white/10 text-white' : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="10"
              />
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Enable Discounts</span>
              <Switch
                checked={discountSettings.enable_discounts}
                onCheckedChange={(checked: boolean) => onDiscountSettingsChange({ ...discountSettings, enable_discounts: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Enable Coupons</span>
              <Switch
                checked={discountSettings.enable_coupons}
                onCheckedChange={(checked: boolean) => onDiscountSettingsChange({ ...discountSettings, enable_coupons: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Require Manager Approval</span>
              <Switch
                checked={discountSettings.require_manager_approval}
                onCheckedChange={(checked: boolean) => onDiscountSettingsChange({ ...discountSettings, require_manager_approval: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Discount on Total</span>
              <Switch
                checked={discountSettings.discount_on_total}
                onCheckedChange={(checked: boolean) => onDiscountSettingsChange({ ...discountSettings, discount_on_total: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Discount on Items</span>
              <Switch
                checked={discountSettings.discount_on_items}
                onCheckedChange={(checked: boolean) => onDiscountSettingsChange({ ...discountSettings, discount_on_items: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Loyalty Discounts</span>
              <Switch
                checked={discountSettings.loyalty_discounts}
                onCheckedChange={(checked: boolean) => onDiscountSettingsChange({ ...discountSettings, loyalty_discounts: checked })}
              />
            </div>
          </div>
        </div>
      </GlassCard>
    </div>
  )
}