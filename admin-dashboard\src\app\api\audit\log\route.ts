import { NextRequest, NextResponse } from 'next/server'
import { auditLoggingService, AuditEventType } from '@/services/audit-logging-service'

export async function POST(request: NextRequest) {
  try {
    const { eventType, details, userId, sessionId, riskLevel, dataClassification, complianceFlags } = await request.json()

    if (!eventType) {
      return NextResponse.json(
        { error: 'Event type is required' },
        { status: 400 }
      )
    }

    const eventId = await auditLoggingService.logEvent(
      eventType as AuditEventType,
      details || {},
      {
        userId,
        sessionId,
        riskLevel,
        dataClassification,
        complianceFlags
      }
    )

    return NextResponse.json({ 
      success: true, 
      eventId,
      message: 'Audit event logged successfully'
    })

  } catch (error) {
    console.error('Failed to log audit event:', error)
    return NextResponse.json(
      { error: 'Failed to log audit event' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const query = {
      startDate: searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined,
      endDate: searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined,
      eventTypes: searchParams.get('eventTypes')?.split(',') as AuditEventType[] | undefined,
      userIds: searchParams.get('userIds')?.split(','),
      riskLevels: searchParams.get('riskLevels')?.split(','),
      complianceFlags: searchParams.get('complianceFlags')?.split(',') as any,
      ipAddresses: searchParams.get('ipAddresses')?.split(','),
      searchText: searchParams.get('searchText') || undefined,
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50,
      offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0
    }

    const result = await auditLoggingService.queryEvents(query)

    return NextResponse.json(result)

  } catch (error) {
    console.error('Failed to query audit events:', error)
    return NextResponse.json(
      { error: 'Failed to query audit events' },
      { status: 500 }
    )
  }
} 