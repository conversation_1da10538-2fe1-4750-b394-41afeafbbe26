import { Metadata } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { GlassCard, GlassButton, GlassInput } from '@/components/ui/glass-components';

export const metadata: Metadata = {
  title: 'Payment Methods | Delicious Crepes & Waffles',
  description: 'Manage your payment methods for faster checkout.',
};

export default function PaymentMethodsPage() {
  // Mock user data - in a real app, this would come from the AuthProvider
  const user = {
    id: 'user123',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/images/avatar.jpg',
    paymentMethods: [
      {
        id: 'card1',
        type: 'visa',
        lastFour: '4242',
        expiryMonth: 12,
        expiryYear: 2025,
        cardholderName: '<PERSON>',
        isDefault: true,
      },
      {
        id: 'card2',
        type: 'mastercard',
        lastFour: '5555',
        expiryMonth: 10,
        expiryYear: 2024,
        cardholderName: '<PERSON>',
        isDefault: false,
      },
    ],
  };

  // Helper function to get card logo
  const getCardLogo = (type: string) => {
    switch (type.toLowerCase()) {
      case 'visa':
        return '/images/visa-logo.svg';
      case 'mastercard':
        return '/images/mastercard-logo.svg';
      case 'amex':
        return '/images/amex-logo.svg';
      default:
        return '/images/generic-card-logo.svg';
    }
  };

  return (
    <main className="min-h-screen py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold">Payment Methods</h1>
          <GlassButton variant="primary">Add Payment Method</GlassButton>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <GlassCard>
              <div className="p-6">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="relative h-16 w-16 rounded-full overflow-hidden">
                    <Image src={user.avatar} alt={user.name} fill className="object-cover" />
                  </div>
                  <div>
                    <h2 className="font-semibold">{user.name}</h2>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                  </div>
                </div>

                <nav className="space-y-2">
                  <Link
                    href="/profile"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Personal Information
                  </Link>
                  <Link
                    href="/profile/addresses"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Addresses
                  </Link>
                  <Link
                    href="/profile/payment-methods"
                    className="block p-2 rounded-md bg-primary/10 text-primary font-medium"
                  >
                    Payment Methods
                  </Link>
                  <Link
                    href="/profile/favorites"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Favorites
                  </Link>
                  <Link
                    href="/profile/settings"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Settings
                  </Link>
                  <Link
                    href="/orders"
                    className="block p-2 rounded-md hover:bg-muted transition-colors"
                  >
                    Order History
                  </Link>
                </nav>
              </div>
            </GlassCard>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {/* Payment Methods List */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {user.paymentMethods.map(method => (
                <GlassCard key={method.id}>
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="h-10 w-16 relative">
                          <Image
                            src={getCardLogo(method.type)}
                            alt={method.type}
                            fill
                            className="object-contain"
                          />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg capitalize">{method.type}</h3>
                          {method.isDefault && (
                            <span className="inline-block text-xs bg-primary/10 text-primary px-2 py-1 rounded-full mt-1">
                              Default
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button className="text-muted-foreground hover:text-red-500">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>

                    <div className="text-muted-foreground">
                      <p>•••• •••• •••• {method.lastFour}</p>
                      <p>
                        Expires {method.expiryMonth}/{method.expiryYear}
                      </p>
                      <p className="mt-1">{method.cardholderName}</p>
                    </div>

                    <div className="mt-4 flex justify-between">
                      {!method.isDefault && (
                        <GlassButton variant="secondary" size="sm">
                          Set as Default
                        </GlassButton>
                      )}
                      {method.isDefault && (
                        <div className="text-sm text-muted-foreground">
                          Used for payments by default
                        </div>
                      )}
                    </div>
                  </div>
                </GlassCard>
              ))}

              {/* Add New Payment Method Card */}
              <GlassCard className="border border-dashed border-muted-foreground/50">
                <button className="h-full w-full flex flex-col items-center justify-center p-6 text-muted-foreground hover:text-foreground transition-colors">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-12 w-12 mb-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  <span className="font-medium">Add New Payment Method</span>
                </button>
              </GlassCard>
            </div>

            {/* Add Payment Method Form - This would be shown in a modal in a real app */}
            <GlassCard className="mt-8">
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-6">Add New Payment Method</h2>

                <div className="grid grid-cols-1 gap-6">
                  <GlassInput label="Card Number" placeholder="1234 5678 9012 3456" />

                  <div className="grid grid-cols-2 gap-6">
                    <GlassInput label="Expiry Date" placeholder="MM/YY" />

                    <GlassInput label="CVC" placeholder="123" />
                  </div>

                  <GlassInput label="Cardholder Name" placeholder="Name as it appears on card" />

                  <div>
                    <label className="flex items-center space-x-2 cursor-pointer">
                      <input type="checkbox" className="h-4 w-4 text-primary rounded" />

                      <span>Set as default payment method</span>
                    </label>
                  </div>

                  <div className="text-sm text-muted-foreground">
                    <p>
                      Your card information is encrypted and securely stored. We never store your
                      full card details on our servers.
                    </p>
                  </div>
                </div>

                <div className="mt-6 flex justify-end space-x-4">
                  <GlassButton variant="secondary">Cancel</GlassButton>
                  <GlassButton variant="primary">Save Payment Method</GlassButton>
                </div>
              </div>
            </GlassCard>

            {/* Other Payment Options */}
            <GlassCard>
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-6">Other Payment Options</h2>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border border-input rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="h-10 w-10 bg-muted rounded-full flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-6 w-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
                          />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-medium">Cash on Delivery</h3>
                        <p className="text-sm text-muted-foreground">
                          Pay with cash when your order arrives
                        </p>
                      </div>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">Always available</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 border border-input rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="h-10 w-10 bg-muted rounded-full flex items-center justify-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-6 w-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                          />
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-medium">Pay at Store</h3>
                        <p className="text-sm text-muted-foreground">
                          Pay when you pick up your order
                        </p>
                      </div>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">Pickup orders only</span>
                    </div>
                  </div>
                </div>
              </div>
            </GlassCard>
          </div>
        </div>
      </div>
    </main>
  );
}
