/**
 * Structured Logger - High-performance logging with context
 */

import winston from 'winston';
import { LoggerFactory, LogContext, LoggerConfig } from './LoggerFactory';

export class StructuredLogger {
  private winston: winston.Logger;
  private serviceName: string;
  private defaultContext: LogContext;

  constructor(serviceName: string, config?: Partial<LoggerConfig>) {
    this.serviceName = serviceName;
    const fullConfig = { ...LoggerFactory.getDefaultConfig(serviceName), ...config };
    this.winston = LoggerFactory.createLogger(fullConfig);
    this.defaultContext = {};
  }

  /**
   * Set default context that will be included in all log entries
   */
  setDefaultContext(context: LogContext): void {
    this.defaultContext = { ...this.defaultContext, ...context };
  }

  /**
   * Create a child logger with additional context
   */
  child(context: LogContext): StructuredLogger {
    const child = new StructuredLogger(this.serviceName);
    child.winston = this.winston.child(context);
    child.defaultContext = { ...this.defaultContext, ...context };
    return child;
  }

  /**
   * Info level logging
   */
  info(message: string, context?: LogContext): void {
    this.winston.info(message, { ...this.defaultContext, ...context });
  }

  /**
   * Error level logging with automatic error handling
   */
  error(message: string, error?: Error | unknown, context?: LogContext): void {
    const errorContext: LogContext = { ...this.defaultContext, ...context };
    
    if (error) {
      if (error instanceof Error) {
        errorContext.error = {
          name: error.name,
          message: error.message,
          stack: error.stack,
          cause: (error as any).cause
        };
      } else {
        errorContext.error = String(error);
      }
    }

    this.winston.error(message, errorContext);
  }

  /**
   * Warning level logging
   */
  warn(message: string, context?: LogContext): void {
    this.winston.warn(message, { ...this.defaultContext, ...context });
  }

  /**
   * Debug level logging
   */
  debug(message: string, context?: LogContext): void {
    this.winston.debug(message, { ...this.defaultContext, ...context });
  }

  /**
   * Trace level logging for detailed debugging
   */
  trace(message: string, context?: LogContext): void {
    this.winston.silly(message, { ...this.defaultContext, ...context, level: 'trace' });
  }

  /**
   * Performance logging for measuring operation duration
   */
  performance(operation: string, startTime: number, context?: LogContext): void {
    const duration = Date.now() - startTime;
    this.info(`Performance: ${operation}`, {
      ...this.defaultContext,
      ...context,
      operation,
      duration_ms: duration,
      performance: true
    });
  }

  /**
   * Audit logging for security and compliance
   */
  audit(action: string, actor: string, resource?: string, context?: LogContext): void {
    this.info(`Audit: ${action}`, {
      ...this.defaultContext,
      ...context,
      audit: true,
      action,
      actor,
      resource,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Business event logging
   */
  event(eventName: string, data?: any, context?: LogContext): void {
    this.info(`Event: ${eventName}`, {
      ...this.defaultContext,
      ...context,
      event: true,
      event_name: eventName,
      event_data: data
    });
  }

  /**
   * API request/response logging
   */
  api(method: string, path: string, statusCode: number, duration: number, context?: LogContext): void {
    const level = statusCode >= 400 ? 'error' : statusCode >= 300 ? 'warn' : 'info';
    
    this[level](`API: ${method} ${path}`, {
      ...this.defaultContext,
      ...context,
      api: true,
      http_method: method,
      http_path: path,
      http_status: statusCode,
      duration_ms: duration
    });
  }

  /**
   * Database operation logging
   */
  database(operation: string, table: string, duration: number, rowCount?: number, context?: LogContext): void {
    this.debug(`Database: ${operation} on ${table}`, {
      ...this.defaultContext,
      ...context,
      database: true,
      db_operation: operation,
      db_table: table,
      duration_ms: duration,
      row_count: rowCount
    });
  }

  /**
   * Security event logging
   */
  security(event: string, severity: 'low' | 'medium' | 'high' | 'critical', context?: LogContext): void {
    const level = severity === 'critical' || severity === 'high' ? 'error' : 'warn';
    
    this[level](`Security: ${event}`, {
      ...this.defaultContext,
      ...context,
      security: true,
      security_event: event,
      severity
    });
  }

  /**
   * Business metric logging
   */
  metric(name: string, value: number, unit?: string, tags?: Record<string, string>): void {
    this.info(`Metric: ${name}`, {
      ...this.defaultContext,
      metric: true,
      metric_name: name,
      metric_value: value,
      metric_unit: unit,
      metric_tags: tags
    });
  }

  /**
   * Log an operation with automatic timing
   */
  async timed<T>(operation: string, fn: () => Promise<T>, context?: LogContext): Promise<T> {
    const startTime = Date.now();
    try {
      this.debug(`Starting: ${operation}`, { ...context, operation });
      const result = await fn();
      this.performance(operation, startTime, { ...context, success: true });
      return result;
    } catch (error) {
      this.performance(operation, startTime, { ...context, success: false });
      this.error(`Failed: ${operation}`, error, context);
      throw error;
    }
  }

  /**
   * Log correlation for distributed tracing
   */
  withCorrelation(correlationId: string, traceId?: string, spanId?: string): StructuredLogger {
    return this.child({
      correlationId,
      traceId,
      spanId
    });
  }

  /**
   * Flush all log transports
   */
  async flush(): Promise<void> {
    return new Promise((resolve) => {
      this.winston.end(() => resolve());
    });
  }
}