{"currentOrder": {"id": "order-123", "customerId": "customer-123", "status": "pending", "orderType": "delivery", "items": [{"id": "order-item-1", "menuItemId": "item-1", "name": "Margherita Pizza", "price": 14.99, "quantity": 1, "customizations": [{"id": "size", "name": "Size", "selectedOption": {"id": "medium", "name": "Medium (12\")", "price": 3}}, {"id": "toppings", "name": "Extra Toppings", "selectedOptions": [{"id": "mushrooms", "name": "Mushrooms", "price": 1.5}]}], "itemTotal": 19.49, "specialInstructions": "Extra cheese please"}, {"id": "order-item-2", "menuItemId": "item-4", "name": "Coca Cola", "price": 2.99, "quantity": 2, "customizations": [{"id": "size", "name": "Size", "selectedOption": {"id": "large", "name": "Large (20oz)", "price": 1}}], "itemTotal": 7.98, "specialInstructions": null}], "subtotal": 27.47, "tax": 2.47, "deliveryFee": 3.99, "tip": 5.0, "total": 38.93, "paymentMethod": {"type": "card", "last4": "4242", "brand": "visa"}, "deliveryAddress": {"street": "123 Main St", "city": "New York", "state": "NY", "zipCode": "10001", "country": "USA", "instructions": "Ring doorbell twice"}, "customerInfo": {"name": "<PERSON>", "email": "<EMAIL>", "phone": "+1234567890"}, "estimatedDeliveryTime": "2024-01-15T12:30:00Z", "actualDeliveryTime": null, "createdAt": "2024-01-15T11:45:00Z", "updatedAt": "2024-01-15T11:45:00Z", "specialInstructions": "Please call when you arrive", "orderNumber": "ORD-2024-001"}, "orderHistory": [{"id": "order-456", "customerId": "customer-123", "status": "delivered", "orderType": "delivery", "items": [{"id": "order-item-3", "menuItemId": "item-2", "name": "Classic Cheeseburger", "price": 12.99, "quantity": 1, "customizations": [{"id": "extras", "name": "Add Extras", "selectedOptions": [{"id": "bacon", "name": "<PERSON>", "price": 2}]}], "itemTotal": 14.99, "specialInstructions": null}], "subtotal": 14.99, "tax": 1.35, "deliveryFee": 3.99, "tip": 3.0, "total": 23.33, "paymentMethod": {"type": "card", "last4": "4242", "brand": "visa"}, "deliveryAddress": {"street": "123 Main St", "city": "New York", "state": "NY", "zipCode": "10001", "country": "USA", "instructions": "Ring doorbell twice"}, "customerInfo": {"name": "<PERSON>", "email": "<EMAIL>", "phone": "+1234567890"}, "estimatedDeliveryTime": "2024-01-10T13:00:00Z", "actualDeliveryTime": "2024-01-10T12:55:00Z", "createdAt": "2024-01-10T12:15:00Z", "updatedAt": "2024-01-10T12:55:00Z", "specialInstructions": null, "orderNumber": "ORD-2024-002", "rating": 5, "review": "Great food and fast delivery!"}, {"id": "order-789", "customerId": "customer-123", "status": "cancelled", "orderType": "pickup", "items": [{"id": "order-item-4", "menuItemId": "item-3", "name": "<PERSON>", "price": 9.99, "quantity": 1, "customizations": [{"id": "protein", "name": "<PERSON><PERSON>", "selectedOption": {"id": "chicken", "name": "Grilled Chicken", "price": 4}}], "itemTotal": 13.99, "specialInstructions": "Dressing on the side"}], "subtotal": 13.99, "tax": 1.26, "deliveryFee": 0, "tip": 0, "total": 15.25, "paymentMethod": {"type": "cash"}, "deliveryAddress": null, "customerInfo": {"name": "<PERSON>", "email": "<EMAIL>", "phone": "+1234567890"}, "estimatedPickupTime": "2024-01-08T14:30:00Z", "actualPickupTime": null, "createdAt": "2024-01-08T14:00:00Z", "updatedAt": "2024-01-08T14:05:00Z", "specialInstructions": null, "orderNumber": "ORD-2024-003", "cancellationReason": "Customer requested cancellation"}], "orderStatuses": [{"id": "pending", "name": "Order Placed", "description": "Your order has been received and is being prepared", "color": "#FFA500", "icon": "clock"}, {"id": "confirmed", "name": "Confirmed", "description": "Your order has been confirmed by the restaurant", "color": "#4CAF50", "icon": "check"}, {"id": "preparing", "name": "Preparing", "description": "Your order is being prepared", "color": "#2196F3", "icon": "cooking"}, {"id": "ready", "name": "Ready", "description": "Your order is ready for pickup/delivery", "color": "#FF9800", "icon": "ready"}, {"id": "out-for-delivery", "name": "Out for Delivery", "description": "Your order is on its way", "color": "#9C27B0", "icon": "truck"}, {"id": "delivered", "name": "Delivered", "description": "Your order has been delivered", "color": "#4CAF50", "icon": "delivered"}, {"id": "cancelled", "name": "Cancelled", "description": "Your order has been cancelled", "color": "#F44336", "icon": "cancel"}], "trackingInfo": {"orderId": "order-123", "currentStatus": "preparing", "estimatedTime": "25 minutes", "timeline": [{"status": "pending", "timestamp": "2024-01-15T11:45:00Z", "completed": true, "message": "Order placed successfully"}, {"status": "confirmed", "timestamp": "2024-01-15T11:47:00Z", "completed": true, "message": "Order confirmed by restaurant"}, {"status": "preparing", "timestamp": "2024-01-15T11:50:00Z", "completed": true, "message": "Kitchen started preparing your order"}, {"status": "ready", "timestamp": null, "completed": false, "message": "Order will be ready for delivery"}, {"status": "out-for-delivery", "timestamp": null, "completed": false, "message": "Driver will pick up your order"}, {"status": "delivered", "timestamp": null, "completed": false, "message": "Order will be delivered to your address"}], "driverInfo": null, "restaurantInfo": {"name": "Delicious Bites", "phone": "+1234567890", "address": "456 Restaurant St, New York, NY 10002"}}, "cartData": {"items": [{"id": "cart-item-1", "menuItemId": "item-1", "name": "Margherita Pizza", "price": 14.99, "quantity": 1, "image": "/images/menu/margherita-pizza.jpg", "customizations": [{"id": "size", "name": "Size", "selectedOption": {"id": "medium", "name": "Medium (12\")", "price": 3}}], "itemTotal": 17.99, "specialInstructions": ""}, {"id": "cart-item-2", "menuItemId": "item-4", "name": "Coca Cola", "price": 2.99, "quantity": 1, "image": "/images/menu/coca-cola.jpg", "customizations": [{"id": "size", "name": "Size", "selectedOption": {"id": "large", "name": "Large (20oz)", "price": 1}}], "itemTotal": 3.99, "specialInstructions": ""}], "subtotal": 21.98, "tax": 1.98, "deliveryFee": 3.99, "total": 27.95, "itemCount": 2, "estimatedDeliveryTime": 30}, "checkoutData": {"customerInfo": {"email": "<EMAIL>", "phone": "+1234567890", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "deliveryAddress": {"street": "123 Main St", "apartment": "Apt 4B", "city": "New York", "state": "NY", "zipCode": "10001", "country": "USA", "instructions": "Ring doorbell twice"}, "orderType": "delivery", "paymentMethod": "card", "specialInstructions": "Please call when you arrive", "tip": {"type": "percentage", "value": 18, "amount": 5.0}, "scheduledDelivery": {"isScheduled": false, "date": null, "time": null}}, "paymentData": {"methods": [{"id": "card", "name": "Credit/Debit Card", "icon": "credit-card", "enabled": true}, {"id": "cash", "name": "Cash on Delivery", "icon": "cash", "enabled": true}, {"id": "paypal", "name": "PayPal", "icon": "paypal", "enabled": false}], "cardInfo": {"number": "****************", "expiryMonth": "12", "expiryYear": "2025", "cvv": "123", "cardholderName": "<PERSON>", "billingAddress": {"street": "123 Main St", "city": "New York", "state": "NY", "zipCode": "10001", "country": "USA"}}, "stripeClientSecret": "pi_test_1234567890_secret_abcdef", "paymentIntent": {"id": "pi_test_1234567890", "status": "requires_payment_method", "amount": 3893, "currency": "usd"}}, "deliveryOptions": [{"id": "standard", "name": "Standard Delivery", "description": "30-45 minutes", "fee": 3.99, "estimatedTime": 35, "available": true}, {"id": "express", "name": "Express Delivery", "description": "15-25 minutes", "fee": 6.99, "estimatedTime": 20, "available": true}, {"id": "pickup", "name": "Pickup", "description": "Ready in 15-20 minutes", "fee": 0, "estimatedTime": 18, "available": true}], "promoCode": {"code": "SAVE10", "description": "10% off your order", "discountType": "percentage", "discountValue": 10, "minimumOrder": 20, "maxDiscount": 5, "validUntil": "2024-12-31T23:59:59Z", "isValid": true}, "orderConfirmation": {"orderId": "order-123", "orderNumber": "ORD-2024-001", "estimatedDeliveryTime": "2024-01-15T12:30:00Z", "total": 38.93, "paymentStatus": "paid", "confirmationMessage": "Thank you for your order! We'll send you updates as your order is prepared and delivered."}}