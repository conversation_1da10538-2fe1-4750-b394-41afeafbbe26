/**
 * Custom React hooks for the customer-web project
 */

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/supabase/database.types';
import {
  ThemeMode,
  Language,
  Cart,
  CartItem,
  MenuItem,
  UserPreferences,
  Order,
} from '@/types/types';
import {
  getLocalStorage,
  setLocalStorage,
  safeJsonParse,
  isDarkMode,
  isPwa,
  isMobile,
} from '@/lib/utils';

/**
 * Hook to manage theme (light/dark/system)
 */
export function useTheme() {
  const [theme, setTheme] = useState<ThemeMode>('system');

  useEffect(() => {
    // Initialize theme from localStorage or default to system
    const savedTheme = getLocalStorage<ThemeMode>('theme', 'system');
    if (savedTheme) {
      setTheme(savedTheme);
    }
  }, []);

  useEffect(() => {
    // Apply theme to document
    const root = window.document.documentElement;
    root.classList.remove('light', 'dark');

    if (theme === 'system') {
      const systemTheme = isDarkMode() ? 'dark' : 'light';
      root.classList.add(systemTheme);
    } else {
      root.classList.add(theme);
    }

    // Save theme preference
    setLocalStorage('theme', theme);
  }, [theme]);

  const toggleTheme = useCallback(() => {
    setTheme(prev => {
      if (prev === 'light') return 'dark';
      if (prev === 'dark') return 'system';
      return 'light';
    });
  }, []);

  return {
    theme,
    setTheme,
    toggleTheme,
    isDarkMode: theme === 'dark' || (theme === 'system' && isDarkMode()),
  };
}

/**
 * Hook to manage shopping cart
 */
export function useCart() {
  const [cart, setCart] = useState<Cart>({
    items: [],
    subtotal: 0,
    tax: 0,
    deliveryFee: 0,
    total: 0,
  });
  const [isCartOpen, setIsCartOpen] = useState(false);

  // Load cart from localStorage on mount
  useEffect(() => {
    const savedCart = safeJsonParse<Cart>(getLocalStorage('cart', '{}'));
    if (savedCart) {
      setCart(savedCart);
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    setLocalStorage('cart', JSON.stringify(cart));
  }, [cart]);

  // Calculate totals whenever items change
  useEffect(() => {
    const subtotal = cart.items.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const tax = subtotal * 0.24; // 24% VAT
    const total = subtotal + tax + cart.deliveryFee + (cart.tip || 0);

    setCart(prev => ({
      ...prev,
      subtotal,
      tax,
      total,
    }));
  }, [cart.items, cart.deliveryFee, cart.tip]);

  const addItem = useCallback(
    (
      menuItem: MenuItem,
      quantity: number = 1,
      variantId?: string,
      specialInstructions?: string
    ) => {
      setCart(prev => {
        // Find selected variant if any
        const variant =
          variantId && menuItem.variants
            ? menuItem.variants.find(v => v.id === variantId)
            : undefined;

        // Calculate item price including variant if selected
        const itemPrice = variant ? variant.price : menuItem.price;

        // Check if item already exists with same variant and special instructions
        const existingItemIndex = prev.items.findIndex(
          item =>
            item.menuItem.id === menuItem.id &&
            item.variant?.id === variantId &&
            item.specialInstructions === specialInstructions
        );

        let newItems;
        if (existingItemIndex >= 0) {
          // Update quantity of existing item
          newItems = [...prev.items];
          newItems[existingItemIndex] = {
            ...newItems[existingItemIndex],
            quantity: newItems[existingItemIndex].quantity + quantity,
          };
        } else {
          // Add new item
          const newItem: CartItem = {
            id: `${menuItem.id}-${Date.now()}`,
            menuItem,
            quantity,
            variant: variant,
            specialInstructions,
            price: itemPrice,
          };
          newItems = [...prev.items, newItem];
        }

        return {
          ...prev,
          items: newItems,
        };
      });
    },
    []
  );

  const updateItem = useCallback(
    (itemId: string, quantity: number, specialInstructions?: string) => {
      setCart(prev => {
        const itemIndex = prev.items.findIndex(item => item.id === itemId);
        if (itemIndex === -1) return prev;

        const newItems = [...prev.items];
        if (quantity <= 0) {
          // Remove item if quantity is 0 or negative
          newItems.splice(itemIndex, 1);
        } else {
          // Update item quantity and special instructions
          newItems[itemIndex] = {
            ...newItems[itemIndex],
            quantity,
            specialInstructions:
              specialInstructions !== undefined
                ? specialInstructions
                : newItems[itemIndex].specialInstructions,
          };
        }

        return {
          ...prev,
          items: newItems,
        };
      });
    },
    []
  );

  const removeItem = useCallback((itemId: string) => {
    setCart(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId),
    }));
  }, []);

  const clearCart = useCallback(() => {
    setCart({
      items: [],
      subtotal: 0,
      tax: 0,
      deliveryFee: 0,
      total: 0,
    });
  }, []);

  const setDeliveryFee = useCallback((fee: number) => {
    setCart(prev => ({
      ...prev,
      deliveryFee: fee,
    }));
  }, []);

  const setTip = useCallback((tip: number) => {
    setCart(prev => ({
      ...prev,
      tip,
    }));
  }, []);

  const toggleCart = useCallback(() => {
    setIsCartOpen(prev => !prev);
  }, []);

  return {
    cart,
    isCartOpen,
    setIsCartOpen,
    toggleCart,
    addItem,
    updateItem,
    removeItem,
    clearCart,
    setDeliveryFee,
    setTip,
    itemCount: cart.items.reduce((sum, item) => sum + item.quantity, 0),
  };
}

/**
 * Hook to manage user favorites
 */
export function useFavorites() {
  const [favorites, setFavorites] = useState<MenuItem[]>([]);
  const supabase = createClientComponentClient<Database>();

  // Load favorites from localStorage or database if logged in
  useEffect(() => {
    const loadFavorites = async () => {
      // Check if user is logged in
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (session?.user) {
        // Load favorites from database
        const { data, error } = await supabase
          .from('favorites')
          .select('subcategory_id, subcategories(*)')
          .eq('user_id', session.user.id);

        if (!error && data) {
          // Extract menu items and ensure they match the MenuItem type
          const menuItems = data.map(item => item.subcategories as unknown as MenuItem);
          setFavorites(menuItems);
        }
      } else {
        // Load favorites from localStorage
        const savedFavorites = safeJsonParse<MenuItem[]>(getLocalStorage('favorites', '[]'));
        if (savedFavorites) {
          setFavorites(savedFavorites);
        }
      }
    };

    loadFavorites();
  }, [supabase]);

  // Save favorites to localStorage if not logged in
  useEffect(() => {
    const saveFavorites = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session?.user) {
        setLocalStorage('favorites', JSON.stringify(favorites));
      }
    };

    saveFavorites();
  }, [favorites, supabase]);

  const addFavorite = useCallback(
    async (menuItem: MenuItem) => {
      // Check if already in favorites
      if (favorites.some(item => item.id === menuItem.id)) return;

      // Add to local state
      setFavorites(prev => [...prev, menuItem]);

      // If logged in, add to database
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (session?.user) {
        await supabase.from('favorites').insert({
          user_id: session.user.id,
          subcategory_id: menuItem.id,
        });
      }
    },
    [favorites, supabase]
  );

  const removeFavorite = useCallback(
    async (menuItemId: string) => {
      // Remove from local state
      setFavorites(prev => prev.filter(item => item.id !== menuItemId));

      // If logged in, remove from database
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (session?.user) {
        await supabase.from('favorites').delete().match({
          user_id: session.user.id,
          subcategory_id: menuItemId,
        });
      }
    },
    [supabase]
  );

  const isFavorite = useCallback(
    (menuItemId: string) => {
      return favorites.some(item => item.id === menuItemId);
    },
    [favorites]
  );

  const toggleFavorite = useCallback(
    (menuItem: MenuItem) => {
      if (isFavorite(menuItem.id)) {
        removeFavorite(menuItem.id);
      } else {
        addFavorite(menuItem);
      }
    },
    [isFavorite, removeFavorite, addFavorite]
  );

  return {
    favorites,
    addFavorite,
    removeFavorite,
    isFavorite,
    toggleFavorite,
  };
}

/**
 * Hook to manage user preferences
 */
export function useUserPreferences() {
  const [preferences, setPreferences] = useState<UserPreferences>({
    language: 'en',
    theme: 'system',
    notifications: {
      push: true,
      email: true,
      sms: false,
      orderUpdates: true,
      promotions: false,
      newsletter: false,
    },
  });
  const supabase = createClientComponentClient<Database>();

  // Load preferences from localStorage or database if logged in
  useEffect(() => {
    const loadPreferences = async () => {
      // Check if user is logged in
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (session?.user) {
        // Load preferences from database
        const { data, error } = await supabase
          .from('profiles')
          .select('preferences')
          .eq('id', session.user.id)
          .single();

        if (!error && data?.preferences) {
          setPreferences(data.preferences as UserPreferences);
        }
      } else {
        // Load preferences from localStorage
        const savedPreferences = safeJsonParse<UserPreferences>(
          getLocalStorage('preferences', '{}')
        );
        if (savedPreferences) {
          setPreferences(savedPreferences);
        }
      }
    };

    loadPreferences();
  }, [supabase]);

  // Save preferences to localStorage if not logged in
  useEffect(() => {
    const savePreferences = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session?.user) {
        setLocalStorage('preferences', JSON.stringify(preferences));
      }
    };

    savePreferences();
  }, [preferences, supabase]);

  const updatePreferences = useCallback(
    async (newPreferences: Partial<UserPreferences>) => {
      // Update local state
      setPreferences(prev => ({
        ...prev,
        ...newPreferences,
        notifications: {
          ...prev.notifications,
          ...newPreferences.notifications,
        },
      }));

      // If logged in, update database
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (session?.user) {
        await supabase
          .from('profiles')
          .update({
            preferences: {
              ...preferences,
              ...newPreferences,
              notifications: {
                ...preferences.notifications,
                ...newPreferences.notifications,
              },
            },
          })
          .eq('id', session.user.id);
      }
    },
    [preferences, supabase]
  );

  return {
    preferences,
    updatePreferences,
  };
}

/**
 * Hook to manage language
 */
export function useLanguage() {
  const { i18n } = useTranslation();
  const [language, setLanguage] = useState<Language>('en');

  useEffect(() => {
    // Initialize language from localStorage, browser, or default to English
    const savedLanguage = getLocalStorage('language', 'en') as Language;
    if (savedLanguage && ['en', 'el'].includes(savedLanguage)) {
      setLanguage(savedLanguage);
      i18n.changeLanguage(savedLanguage);
    } else {
      // Try to detect from browser
      const browserLang = navigator.language.split('-')[0];
      if (browserLang === 'el') {
        setLanguage('el');
        i18n.changeLanguage('el');
      }
    }
  }, [i18n]);

  const changeLanguage = useCallback(
    (lang: Language) => {
      setLanguage(lang);
      i18n.changeLanguage(lang);
      setLocalStorage('language', lang);
    },
    [i18n]
  );

  return { language, changeLanguage };
}

/**
 * Hook to manage orders
 */
export function useOrders() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();

  const fetchOrders = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session?.user) {
        setOrders([]);
        setLoading(false);
        return;
      }

      const { data, error: fetchError } = await supabase
        .from('orders')
        .select(
          `
          *,
          order_items(*,
            subcategories(*)
          )
        `
        )
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false });

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      setOrders(data as unknown as Order[]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch orders');
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  const getOrderById = useCallback(
    (orderId: string) => {
      return orders.find(order => order.id === orderId) || null;
    },
    [orders]
  );

  const cancelOrder = useCallback(
    async (orderId: string) => {
      try {
        const { error: updateError } = await supabase
          .from('orders')
          .update({ status: 'cancelled' })
          .eq('id', orderId);

        if (updateError) {
          throw new Error(updateError.message);
        }

        // Update local state
        setOrders(prev =>
          prev.map(order =>
            order.id === orderId ? { ...order, status: 'cancelled' as const } : order
          )
        );

        return { success: true };
      } catch (err) {
        return {
          success: false,
          error: err instanceof Error ? err.message : 'Failed to cancel order',
        };
      }
    },
    [supabase]
  );

  return {
    orders,
    loading,
    error,
    fetchOrders,
    getOrderById,
    cancelOrder,
  };
}

/**
 * Hook to detect if app is installed as PWA
 */
export function usePwa() {
  const [isInstalled, setIsInstalled] = useState(false);
  const [canInstall, setCanInstall] = useState(false);
  const [installPrompt, setInstallPrompt] = useState<any>(null);

  useEffect(() => {
    // Check if already installed
    setIsInstalled(isPwa());

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault();
      // Stash the event so it can be triggered later
      setInstallPrompt(e);
      setCanInstall(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setCanInstall(false);
      setInstallPrompt(null);
    };

    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const promptInstall = useCallback(async () => {
    if (!installPrompt) return false;

    // Show the install prompt
    installPrompt.prompt();

    // Wait for the user to respond to the prompt
    const choiceResult = await installPrompt.userChoice;

    // Clear the saved prompt regardless of user choice
    setInstallPrompt(null);
    setCanInstall(false);

    return choiceResult.outcome === 'accepted';
  }, [installPrompt]);

  return { isInstalled, canInstall, promptInstall };
}

/**
 * Hook to manage scroll position
 */
export function useScrollPosition() {
  const [scrollPosition, setScrollPosition] = useState(0);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isScrollingUp, setIsScrollingUp] = useState(false);
  const prevScrollY = useRef(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollPosition(currentScrollY);
      setIsScrolled(currentScrollY > 50);
      setIsScrollingUp(currentScrollY < prevScrollY.current);
      prevScrollY.current = currentScrollY;
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return { scrollPosition, isScrolled, isScrollingUp };
}

/**
 * Hook to detect if device is mobile
 */
export function useDevice() {
  const [isMobileDevice, setIsMobileDevice] = useState(false);

  useEffect(() => {
    setIsMobileDevice(isMobile());

    const handleResize = () => {
      setIsMobileDevice(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return { isMobile: isMobileDevice };
}

/**
 * Hook to manage query parameters
 */
export function useQueryParams<T extends Record<string, string>>() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const params = useMemo(() => {
    const params: Record<string, string> = {};
    if (searchParams) {
      searchParams.forEach((value, key) => {
        params[key] = value;
      });
    }
    return params as T;
  }, [searchParams]);

  const setParams = useCallback(
    (newParams: Partial<T>) => {
      const urlSearchParams = new URLSearchParams(searchParams?.toString() || '');

      // Update or add new params
      Object.entries(newParams).forEach(([key, value]) => {
        if (value === undefined || value === null) {
          urlSearchParams.delete(key);
        } else {
          urlSearchParams.set(key, String(value));
        }
      });

      const search = urlSearchParams.toString();
      const query = search ? `?${search}` : '';
      router.push(`${pathname}${query}`);
    },
    [router, pathname, searchParams]
  );

  return { params, setParams };
}

/**
 * Hook for intersection observer
 */
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
      if (entry.isIntersecting && !hasIntersected) {
        setHasIntersected(true);
      }
    }, options);

    observer.observe(element);
    return () => observer.disconnect();
  }, [elementRef, options, hasIntersected]);

  return { isIntersecting, hasIntersected };
}

/**
 * Hook for local storage with state sync
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.error(error);
    }
  };

  return [storedValue, setValue];
}
