import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServerSupabaseClient()
    const customerId = params.id
    
    // Get customer preferences
    const { data: customer, error: customerError } = await supabase
      .from('user_profiles')
      .select('preferences')
      .eq('id', customerId)
      .single()
    
    if (customerError || !customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }
    
    // Default preferences structure
    const defaultPreferences = {
      communication: {
        email_notifications: true,
        sms_notifications: false,
        push_notifications: true,
        marketing_emails: false,
        order_updates: true,
        loyalty_updates: true,
        promotional_offers: false
      },
      ordering: {
        save_payment_methods: true,
        save_delivery_addresses: true,
        auto_reorder_favorites: false,
        dietary_restrictions: [],
        allergies: [],
        preferred_language: 'en'
      },
      privacy: {
        share_analytics: false,
        personalized_recommendations: true,
        location_tracking: false
      }
    }
    
    // Merge with existing preferences
    const preferences = {
      ...defaultPreferences,
      ...customer.preferences,
      communication: {
        ...defaultPreferences.communication,
        ...customer.preferences?.communication
      },
      ordering: {
        ...defaultPreferences.ordering,
        ...customer.preferences?.ordering
      },
      privacy: {
        ...defaultPreferences.privacy,
        ...customer.preferences?.privacy
      }
    }
    
    return NextResponse.json({
      data: preferences
    })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServerSupabaseClient()
    const customerId = params.id
    const body = await request.json()
    
    const { preferences } = body
    
    if (!preferences) {
      return NextResponse.json(
        { error: 'Preferences data is required' },
        { status: 400 }
      )
    }
    
    // Get current customer data
    const { data: currentCustomer, error: fetchError } = await supabase
      .from('user_profiles')
      .select('preferences')
      .eq('id', customerId)
      .single()
    
    if (fetchError || !currentCustomer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }
    
    // Merge preferences
    const updatedPreferences = {
      ...currentCustomer.preferences,
      ...preferences,
      communication: {
        ...currentCustomer.preferences?.communication,
        ...preferences.communication
      },
      ordering: {
        ...currentCustomer.preferences?.ordering,
        ...preferences.ordering
      },
      privacy: {
        ...currentCustomer.preferences?.privacy,
        ...preferences.privacy
      }
    }
    
    // Update customer preferences
    const { data: updatedCustomer, error: updateError } = await supabase
      .from('user_profiles')
      .update({
        preferences: updatedPreferences,
        updated_at: new Date().toISOString()
      })
      .eq('id', customerId)
      .select('preferences')
      .single()
    
    if (updateError) {
      return NextResponse.json(
        { error: 'Failed to update preferences' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      data: updatedCustomer.preferences,
      message: 'Customer preferences updated successfully'
    })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServerSupabaseClient()
    const customerId = params.id
    const body = await request.json()
    
    const { action, data } = body
    
    // Handle specific preference actions
    switch (action) {
      case 'add_dietary_restriction':
        return handleAddDietaryRestriction(supabase, customerId, data)
      
      case 'remove_dietary_restriction':
        return handleRemoveDietaryRestriction(supabase, customerId, data)
      
      case 'add_allergy':
        return handleAddAllergy(supabase, customerId, data)
      
      case 'remove_allergy':
        return handleRemoveAllergy(supabase, customerId, data)
      
      case 'update_language':
        return handleUpdateLanguage(supabase, customerId, data)
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function handleAddDietaryRestriction(supabase: any, customerId: string, restriction: string) {
  const { data: customer, error: fetchError } = await supabase
    .from('user_profiles')
    .select('preferences')
    .eq('id', customerId)
    .single()
  
  if (fetchError || !customer) {
    return NextResponse.json(
      { error: 'Customer not found' },
      { status: 404 }
    )
  }
  
  const currentRestrictions = customer.preferences?.ordering?.dietary_restrictions || []
  
  if (!currentRestrictions.includes(restriction)) {
    const updatedPreferences = {
      ...customer.preferences,
      ordering: {
        ...customer.preferences?.ordering,
        dietary_restrictions: [...currentRestrictions, restriction]
      }
    }
    
    const { error: updateError } = await supabase
      .from('user_profiles')
      .update({
        preferences: updatedPreferences,
        updated_at: new Date().toISOString()
      })
      .eq('id', customerId)
    
    if (updateError) {
      return NextResponse.json(
        { error: 'Failed to add dietary restriction' },
        { status: 500 }
      )
    }
  }
  
  return NextResponse.json({
    message: 'Dietary restriction added successfully'
  })
}

async function handleRemoveDietaryRestriction(supabase: any, customerId: string, restriction: string) {
  const { data: customer, error: fetchError } = await supabase
    .from('user_profiles')
    .select('preferences')
    .eq('id', customerId)
    .single()
  
  if (fetchError || !customer) {
    return NextResponse.json(
      { error: 'Customer not found' },
      { status: 404 }
    )
  }
  
  const currentRestrictions = customer.preferences?.ordering?.dietary_restrictions || []
  const updatedRestrictions = currentRestrictions.filter((r: string) => r !== restriction)
  
  const updatedPreferences = {
    ...customer.preferences,
    ordering: {
      ...customer.preferences?.ordering,
      dietary_restrictions: updatedRestrictions
    }
  }
  
  const { error: updateError } = await supabase
    .from('user_profiles')
    .update({
      preferences: updatedPreferences,
      updated_at: new Date().toISOString()
    })
    .eq('id', customerId)
  
  if (updateError) {
    return NextResponse.json(
      { error: 'Failed to remove dietary restriction' },
      { status: 500 }
    )
  }
  
  return NextResponse.json({
    message: 'Dietary restriction removed successfully'
  })
}

async function handleAddAllergy(supabase: any, customerId: string, allergy: string) {
  const { data: customer, error: fetchError } = await supabase
    .from('user_profiles')
    .select('preferences')
    .eq('id', customerId)
    .single()
  
  if (fetchError || !customer) {
    return NextResponse.json(
      { error: 'Customer not found' },
      { status: 404 }
    )
  }
  
  const currentAllergies = customer.preferences?.ordering?.allergies || []
  
  if (!currentAllergies.includes(allergy)) {
    const updatedPreferences = {
      ...customer.preferences,
      ordering: {
        ...customer.preferences?.ordering,
        allergies: [...currentAllergies, allergy]
      }
    }
    
    const { error: updateError } = await supabase
      .from('user_profiles')
      .update({
        preferences: updatedPreferences,
        updated_at: new Date().toISOString()
      })
      .eq('id', customerId)
    
    if (updateError) {
      return NextResponse.json(
        { error: 'Failed to add allergy' },
        { status: 500 }
      )
    }
  }
  
  return NextResponse.json({
    message: 'Allergy added successfully'
  })
}

async function handleRemoveAllergy(supabase: any, customerId: string, allergy: string) {
  const { data: customer, error: fetchError } = await supabase
    .from('user_profiles')
    .select('preferences')
    .eq('id', customerId)
    .single()
  
  if (fetchError || !customer) {
    return NextResponse.json(
      { error: 'Customer not found' },
      { status: 404 }
    )
  }
  
  const currentAllergies = customer.preferences?.ordering?.allergies || []
  const updatedAllergies = currentAllergies.filter((a: string) => a !== allergy)
  
  const updatedPreferences = {
    ...customer.preferences,
    ordering: {
      ...customer.preferences?.ordering,
      allergies: updatedAllergies
    }
  }
  
  const { error: updateError } = await supabase
    .from('user_profiles')
    .update({
      preferences: updatedPreferences,
      updated_at: new Date().toISOString()
    })
    .eq('id', customerId)
  
  if (updateError) {
    return NextResponse.json(
      { error: 'Failed to remove allergy' },
      { status: 500 }
    )
  }
  
  return NextResponse.json({
    message: 'Allergy removed successfully'
  })
}

async function handleUpdateLanguage(supabase: any, customerId: string, language: string) {
  const { data: customer, error: fetchError } = await supabase
    .from('user_profiles')
    .select('preferences')
    .eq('id', customerId)
    .single()
  
  if (fetchError || !customer) {
    return NextResponse.json(
      { error: 'Customer not found' },
      { status: 404 }
    )
  }
  
  const updatedPreferences = {
    ...customer.preferences,
    ordering: {
      ...customer.preferences?.ordering,
      preferred_language: language
    }
  }
  
  const { error: updateError } = await supabase
    .from('user_profiles')
    .update({
      preferences: updatedPreferences,
      updated_at: new Date().toISOString()
    })
    .eq('id', customerId)
  
  if (updateError) {
    return NextResponse.json(
      { error: 'Failed to update language preference' },
      { status: 500 }
    )
  }
  
  return NextResponse.json({
    message: 'Language preference updated successfully'
  })
} 