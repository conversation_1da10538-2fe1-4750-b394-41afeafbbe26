'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Save, X, Package, ShoppingCart, DollarSign } from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface MenuCategory {
  id: string;
  name: string;
  description: string;
  parent_id?: string;
  category_type: 'standard' | 'customizable';
  display_order: number;
  is_active: boolean;
  is_featured: boolean;
}

interface Subcategory {
  id: string;
  name_en: string;
  name_el?: string;
  description_en?: string;
  description_el?: string;
  price: number;
  category_id: string;
  category_name?: string;
  image_url?: string;
  allergens?: string[];
  preparation_time: number;
  is_available: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

interface Ingredient {
  id: string;
  category_id: string;
  name: string;
  description: string;
  price: number;
  cost: number;
  image_url?: string;
  stock_quantity: number;
  min_stock_level: number;
  is_available: boolean;
  allergens: string[];
  display_order: number;
  category_name?: string;
}

interface IngredientCategory {
  id: string;
  name: string;
  description: string;
  color_code: string;
  display_order: number;
  is_active: boolean;
}

const CustomizableMenuManager = () => {
  const [categories, setCategories] = useState<MenuCategory[]>([]);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [ingredientCategories, setIngredientCategories] = useState<IngredientCategory[]>([]);
  const [activeTab, setActiveTab] = useState<'categories' | 'subcategories' | 'ingredients'>('categories');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);


  useEffect(() => {
    loadAllData();
  }, []);

  const loadAllData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load all data in parallel
      const [categoriesRes, subcategoriesRes, ingredientsRes, ingredientCategoriesRes] = await Promise.all([
        fetch('/api/menu_categories'),
        fetch('/api/subcategories'),
        fetch('/api/ingredients'),
        fetch('/api/ingredient_categories')
      ]);

      if (!categoriesRes.ok || !subcategoriesRes.ok || !ingredientsRes.ok || !ingredientCategoriesRes.ok) {
        throw new Error('Failed to load data');
      }

      const [categoriesData, subcategoriesData, ingredientsData, ingredientCategoriesData] = await Promise.all([
        categoriesRes.json(),
        subcategoriesRes.json(),
        ingredientsRes.json(),
        ingredientCategoriesRes.json()
      ]);

      setCategories(categoriesData.data || []);
      setSubcategories(subcategoriesData.data || []);
      setIngredients(ingredientsData.data || []);
      setIngredientCategories(ingredientCategoriesData.data || []);

    } catch (err) {
      console.error('Error loading data:', err);
      setError('Failed to load menu data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveCategory = async (categoryData: Partial<MenuCategory>) => {
    try {
      if (editingItem?.id) {
        const { error } = await supabase
          .from('menu_categories')
          .update(categoryData)
          .eq('id', editingItem.id);
        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('menu_categories')
          .insert([categoryData]);
        if (error) throw error;
      }
      
      setShowModal(false);
      setEditingItem(null);
      loadAllData();
    } catch (err) {
      console.error('Error saving category:', err);
      setError('Failed to save category. Please try again.');
    }
  };

  const handleSave = async (table: string, data: any) => {
    try {
      const method = data.id ? 'PUT' : 'POST';
      const url = data.id ? `/api/${table}/${data.id}` : `/api/${table}`;
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to save item');
      }

      loadAllData();
      setShowModal(false);
      setEditingItem(null);
    } catch (err) {
      console.error('Error saving item:', err);
      setError('Failed to save item. Please try again.');
    }
  };

  const handleSaveIngredient = async (ingredientData: Partial<Ingredient>) => {
    try {
      if (editingItem?.id) {
        const { error } = await supabase
          .from('ingredients')
          .update(ingredientData)
          .eq('id', editingItem.id);
        if (error) throw error;
      } else {
        const { error } = await supabase
          .from('ingredients')
          .insert([ingredientData]);
        if (error) throw error;
      }
      
      setShowModal(false);
      setEditingItem(null);
      loadAllData();
    } catch (err) {
      console.error('Error saving ingredient:', err);
      setError('Failed to save ingredient. Please try again.');
    }
  };

  const handleDelete = async (table: string, id: string) => {
    if (!confirm('Are you sure you want to delete this item?')) return;

    try {
      const response = await fetch(`/api/${table}/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete item');
      }
      
      loadAllData();
    } catch (err) {
      console.error('Error deleting item:', err);
      setError('Failed to delete item. Please try again.');
    }
  };

  const renderCategoriesTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Menu Categories</h2>
        <button
          onClick={() => {
            setEditingItem({});
            setShowModal(true);
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <Plus className="h-5 w-5" />
          <span>Add Category</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.map(category => (
          <div key={category.id} className="bg-white border border-gray-200 rounded-xl p-6">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{category.name}</h3>
                <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                  category.category_type === 'customizable' 
                    ? 'bg-purple-100 text-purple-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {category.category_type}
                </span>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    setEditingItem(category);
                    setShowModal(true);
                  }}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete('menu_categories', category.id)}
                  className="text-red-600 hover:text-red-800"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
            
            <p className="text-gray-600 text-sm mb-3">{category.description}</p>
            
            <div className="flex justify-between items-center text-sm text-gray-500">
              <span>Order: {category.display_order}</span>
              <div className="flex space-x-2">
                {category.is_featured && (
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">
                    Featured
                  </span>
                )}
                <span className={`px-2 py-1 rounded-full text-xs ${
                  category.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {category.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderSubcategoriesTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Subcategories</h2>
        <button
          onClick={() => {
            setEditingItem({});
            setShowModal(true);
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <Plus className="h-5 w-5" />
          <span>Add Subcategory</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {subcategories.map(item => (
          <div key={item.id} className="bg-white border border-gray-200 rounded-xl p-6">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{item.name_en}</h3>
                <span className="text-sm text-gray-600">{item.category_name}</span>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    setEditingItem(item);
                    setShowModal(true);
                  }}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete('subcategories', item.id)}
                  className="text-red-600 hover:text-red-800"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
            
            <p className="text-gray-600 text-sm mb-3">{item.description_en}</p>
            
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Price:</span>
                <span className="font-semibold text-green-600">${item.price.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Prep Time:</span>
                <span className="text-sm">{item.preparation_time} min</span>
              </div>
              {item.allergens && item.allergens.length > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Allergens:</span>
                  <span className="text-sm">{item.allergens.join(', ')}</span>
                </div>
              )}
            </div>
            
            <div className="flex justify-between items-center mt-4 text-sm">
              <span className="text-xs text-gray-500">Order: {item.display_order}</span>
              <span className={`px-2 py-1 rounded-full text-xs ${
                item.is_available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {item.is_available ? 'Available' : 'Unavailable'}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderIngredientsTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Ingredients</h2>
        <button
          onClick={() => {
            setEditingItem({});
            setShowModal(true);
          }}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <Plus className="h-5 w-5" />
          <span>Add Ingredient</span>
        </button>
      </div>

      {/* Ingredient Categories */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">Categories</h3>
        <div className="flex space-x-4">
          {ingredientCategories.map(category => (
            <div key={category.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <div
                  className="w-4 h-4 rounded-full"
                  style={{ backgroundColor: category.color_code }}
                ></div>
                <span className="font-medium">{category.name}</span>
              </div>
              <p className="text-sm text-gray-600 mt-1">{category.description}</p>
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {ingredients.map(ingredient => (
          <div key={ingredient.id} className="bg-white border border-gray-200 rounded-xl p-6">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{ingredient.name}</h3>
                <span className="text-sm text-gray-600">{ingredient.category_name}</span>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    setEditingItem(ingredient);
                    setShowModal(true);
                  }}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete('ingredients', ingredient.id)}
                  className="text-red-600 hover:text-red-800"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
            
            <p className="text-gray-600 text-sm mb-3">{ingredient.description}</p>
            
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Price:</span>
                <span className="font-semibold text-green-600">+${ingredient.price.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Cost:</span>
                <span className="text-sm">${ingredient.cost.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Stock:</span>
                <span className={`text-sm font-medium ${
                  ingredient.stock_quantity <= ingredient.min_stock_level 
                    ? 'text-red-600' 
                    : 'text-green-600'
                }`}>
                  {ingredient.stock_quantity} units
                </span>
              </div>
            </div>
            
            <div className="flex justify-between items-center mt-4">
              <span className="text-xs text-gray-500">Order: {ingredient.display_order}</span>
              <span className={`px-2 py-1 rounded-full text-xs ${
                ingredient.is_available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {ingredient.is_available ? 'Available' : 'Unavailable'}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <p className="text-red-600">{error}</p>
        <button
          onClick={loadAllData}
          className="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Customizable Menu Manager</h1>
        <p className="text-gray-600">
          Manage your menu categories, items, and ingredients for the customizable ordering system
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('categories')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'categories'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Package className="h-5 w-5 inline mr-2" />
            Categories
          </button>
          <button
            onClick={() => setActiveTab('subcategories')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'subcategories'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <ShoppingCart className="h-5 w-5 inline mr-2" />
            Subcategories
          </button>
          <button
            onClick={() => setActiveTab('ingredients')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'ingredients'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <DollarSign className="h-5 w-5 inline mr-2" />
            Ingredients
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'categories' && renderCategoriesTab()}
      {activeTab === 'subcategories' && renderSubcategoriesTab()}
      {activeTab === 'ingredients' && renderIngredientsTab()}

      {/* Modal placeholder - would need separate modal components for each type */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">
                {editingItem?.id ? 'Edit' : 'Add'} {activeTab.slice(0, -1)}
              </h3>
              <button
                onClick={() => {
                  setShowModal(false);
                  setEditingItem(null);
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <p className="text-gray-600">Form implementation needed for {activeTab}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomizableMenuManager;