import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { z } from 'zod'

// Validation schema for updates
const updateSubcategorySchema = z.object({
  name_en: z.string().min(1).optional(),
  name_el: z.string().min(1).optional(),
  description_en: z.string().optional(),
  description_el: z.string().optional(),
  category_id: z.string().uuid().optional(),
  image_url: z.string().url().optional(),
  is_available: z.boolean().optional(),
  display_order: z.number().int().min(0).optional(),
  ingredient_ids: z.array(z.string().uuid()).optional()
})

interface RouteParams {
  params: {
    id: string
  }
}

// GET /api/subcategories/[id] - Fetch a specific subcategory
export async function GET(
  _request: NextRequest,
  { params }: RouteParams
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params
    
    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json(
        { error: 'Invalid subcategory ID format' },
        { status: 400 }
      )
    }
    
    const { data, error } = await supabase
      .from('subcategories')
      .select(`
        *,
        menu_categories(
          id,
          name_en,
          name_el
        ),
        subcategory_ingredients(
          id,
          quantity,
          ingredients(
            id,
            name_en,
            name_el,
            unit,
            stock_quantity,
            is_active
          )
        )
      `)
      .eq('id', id)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Subcategory not found' },
          { status: 404 }
        )
      }
      
      return NextResponse.json(
        { error: 'Failed to fetch subcategory' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({ data })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/subcategories/[id] - Update a specific subcategory
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params
    const body = await request.json()
    
    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json(
        { error: 'Invalid subcategory ID format' },
        { status: 400 }
      )
    }
    
    // Validate request body
    const validationResult = updateSubcategorySchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }
    
    const updateData = validationResult.data
    
    // If category_id is being updated, verify it exists
    if (updateData.category_id) {
      const { data: category, error: categoryError } = await supabase
        .from('menu_categories')
        .select('id')
        .eq('id', updateData.category_id)
        .single()
      
      if (categoryError || !category) {
        return NextResponse.json(
          { error: 'Invalid category ID' },
          { status: 400 }
        )
      }
    }
    
    // Update subcategory
    const { data, error } = await supabase
      .from('subcategories')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        menu_categories(
          id,
          name_en,
          name_el
        ),
        subcategory_ingredients(
          id,
          quantity,
          ingredients(
            id,
            name_en,
            name_el,
            unit,
            stock_quantity,
            is_active
          )
        )
      `)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Subcategory not found' },
          { status: 404 }
        )
      }
      
      return NextResponse.json(
        { error: 'Failed to update subcategory' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({ data })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/subcategories/[id] - Delete a specific subcategory
export async function DELETE(
  _request: NextRequest,
  { params }: RouteParams
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params
    
    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json(
        { error: 'Invalid subcategory ID format' },
        { status: 400 }
      )
    }
    
    // Check if subcategory exists and get its details
    const { data: existingItem, error: fetchError } = await supabase
      .from('subcategories')
      .select('id, name_en')
      .eq('id', id)
      .single()
    
    if (fetchError || !existingItem) {
      return NextResponse.json(
        { error: 'Subcategory not found' },
        { status: 404 }
      )
    }
    
    // Delete subcategory (this will cascade to subcategory_ingredients due to foreign key)
    const { error } = await supabase
      .from('subcategories')
      .delete()
      .eq('id', id)
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to delete subcategory' },
        { status: 500 }
      )
    }
    
    return NextResponse.json(
      { 
        message: 'Subcategory deleted successfully',
        deleted_item: existingItem
      },
      { status: 200 }
    )
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}