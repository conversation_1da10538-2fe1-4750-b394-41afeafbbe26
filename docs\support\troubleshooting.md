# Troubleshooting Guide

## Overview

This guide helps resolve common issues with The Small Creperie Admin Dashboard system. Issues are organized by category with step-by-step solutions.

## Quick Diagnostics

Before troubleshooting specific issues, run these quick checks:

### System Health Check
1. **Admin Dashboard**: Visit http://localhost:3001/system
2. **Check Status**: Verify all services show "Online"
3. **Database**: Confirm Supabase connection is active
4. **Real-time Sync**: Look for "Connected" status

### Network Connectivity
```bash
# Test internet connection
ping google.com

# Test Supabase connectivity
curl -I https://your-project.supabase.co/rest/v1/
```

### Browser Issues
1. **Clear Cache**: Ctrl+Shift+R (Chrome/Firefox)
2. **Check Console**: F12 → Console for JavaScript errors
3. **Try Incognito**: Test in private browsing mode

## Authentication & Login Issues

### Cannot Login to Admin Dashboard

**Symptoms**: Login page shows "Invalid credentials" or page won't load

**Solutions**:

1. **Verify Credentials**
   - Check email address format
   - Verify password (case sensitive)
   - Try password reset if available

2. **Check Environment Configuration**
   ```bash
   # Verify .env.local file exists
   cd admin-dashboard
   cat .env.local | grep SUPABASE
   ```

3. **Database Connection**
   - Verify Supabase project is active
   - Check API keys are correct
   - Confirm database migrations have run

4. **Browser Issues**
   - Clear cookies and cache
   - Disable browser extensions
   - Try different browser

**Advanced Troubleshooting**:
```bash
# Check application logs
cd admin-dashboard
npm run dev
# Look for authentication errors in console
```

### Session Expires Quickly

**Symptoms**: Forced to login repeatedly

**Solutions**:

1. **Check Session Configuration**
   ```env
   # In .env.local
   NEXTAUTH_SECRET=your-secret-key
   NEXTAUTH_URL=http://localhost:3001
   ```

2. **Browser Settings**
   - Allow cookies for localhost
   - Disable "Clear cookies on exit"
   - Check if third-party cookies are blocked

## POS System Issues

### POS System Not Receiving Updates

**Symptoms**: Changes in admin dashboard don't appear on POS terminals

**Immediate Actions**:
1. **Check Sync Status**: Look for sync indicator on POS
2. **Force Refresh**: Restart POS application
3. **Verify Network**: Ensure POS has internet connection

**Detailed Troubleshooting**:

1. **Admin Dashboard Check**
   ```bash
   # Navigate to POS settings
   http://localhost:3001/pos
   # Verify changes are saved (check for success message)
   ```

2. **POS System Check**
   ```bash
   # Check POS application logs
   cd pos-system
   npm run dev
   # Look for Supabase connection errors
   ```

3. **Network Connectivity**
   ```bash
   # From POS terminal, test connection
   ping your-supabase-project.supabase.co
   ```

4. **Database Verification**
   - Log into Supabase dashboard
   - Check `pos_configurations` table for recent updates
   - Verify row-level security policies

**Resolution Steps**:
1. Restart POS application
2. Check internet connection on POS terminal
3. Verify terminal ID matches configuration
4. Review Supabase real-time subscriptions

### POS Terminal Not Responding

**Symptoms**: POS screen frozen, touch not working, or application crashes

**Immediate Actions**:
1. **Restart Application**: Close and reopen POS software
2. **Check Hardware**: Verify touch screen and peripherals
3. **Force Restart**: If completely frozen, restart the terminal

**Hardware Troubleshooting**:

1. **Touch Screen Issues**
   - Clean screen surface
   - Calibrate touch sensitivity
   - Check USB/power connections

2. **Printer Problems**
   - Verify printer power and paper
   - Check network/USB connections
   - Test print from system settings

3. **Cash Drawer Issues**
   - Verify connection to printer or POS
   - Test manual drawer opening
   - Check cash drawer settings

**Software Troubleshooting**:
```bash
# Check application logs
cd pos-system
# Look for error messages
npm run dev

# Clear application cache
rm -rf node_modules/.cache
npm install
```

## Real-time Synchronization Issues

### Changes Not Syncing Between Apps

**Symptoms**: Updates in one system don't appear in others

**Diagnostic Steps**:

1. **Check Admin Dashboard**
   - Navigate to System → Health Monitoring
   - Verify all platforms show "Connected"
   - Check real-time subscription status

2. **Test Sync Manually**
   ```bash
   # Make a test change
   1. Update menu item price in admin dashboard
   2. Check if change appears in POS within 30 seconds
   3. Verify customer web app reflects change
   ```

3. **Check Database Activity**
   - Open Supabase dashboard
   - Monitor real-time logs
   - Look for subscription errors

**Common Causes & Solutions**:

1. **Network Connectivity**
   - Intermittent internet connection
   - Firewall blocking WebSocket connections
   - Solution: Check network stability, configure firewall

2. **Database Subscriptions**
   - Supabase real-time quotas exceeded
   - Row-level security blocking updates
   - Solution: Check Supabase project limits, review RLS policies

3. **Application State**
   - Browser tab inactive (mobile devices)
   - Application not focused
   - Solution: Keep applications active, implement background sync

### Sync Conflicts

**Symptoms**: Different values showing in different systems

**Resolution Process**:

1. **Identify Conflict Source**
   - Check which system has the most recent change
   - Review audit logs in System → Security Audit

2. **Manual Resolution**
   - Choose correct value
   - Update from admin dashboard (source of truth)
   - Verify propagation to all systems

3. **Prevent Future Conflicts**
   - Train staff to make changes only in admin dashboard
   - Implement change approval workflows
   - Regular sync status monitoring

## Menu Management Issues

### Menu Items Not Appearing

**Symptoms**: Items added in admin dashboard don't show on customer apps or POS

**Checklist**:
- ✅ Item marked as "Available"
- ✅ Category is visible
- ✅ Current time within availability schedule
- ✅ Platform-specific settings enabled

**Troubleshooting Steps**:

1. **Verify Item Settings**
   ```bash
   # Check in admin dashboard
   Menu → Items → [Item Name]
   - Status: Available
   - Visibility: All platforms
   - Schedule: Current time included
   ```

2. **Check Category Settings**
   ```bash
   # Verify category is active
   Menu → Categories → [Category Name]
   - Status: Active
   - Display order: Set appropriately
   ```

3. **Platform-Specific Issues**
   - POS: Check if terminal is configured for category
   - Web: Verify web app settings include category
   - Mobile: Confirm mobile app sync is active

### Image Upload Issues

**Symptoms**: Menu item images won't upload or appear broken

**Common Causes**:
1. File size too large (max 5MB recommended)
2. Unsupported format (use JPG, PNG, WebP)
3. Network timeout during upload

**Solutions**:
1. **Optimize Images**
   - Resize to 800x600 pixels
   - Compress file size
   - Convert to JPG or PNG

2. **Check Upload Process**
   ```bash
   # Verify upload completes
   1. Select image file
   2. Wait for progress bar completion
   3. Confirm image preview appears
   ```

## Staff Management Issues

### Staff Cannot Access POS

**Symptoms**: Staff member cannot login to POS terminal

**Verification Steps**:

1. **Check Account Status**
   ```bash
   # In admin dashboard
   Staff → Manage Staff → [Employee Name]
   - Status: Active
   - Role: Appropriate for POS access
   - POS PIN: Generated and active
   ```

2. **Verify Permissions**
   ```bash
   # Check role permissions
   Staff → Roles & Permissions
   - Confirm role includes POS access
   - Verify specific permissions granted
   ```

3. **Test Login Process**
   ```bash
   # On POS terminal
   1. Enter employee PIN
   2. Check for error messages
   3. Verify terminal accepts PIN format
   ```

**Common Solutions**:
- Regenerate POS PIN
- Update staff role/permissions
- Restart POS application
- Check staff account expiration date

### Permission Changes Not Taking Effect

**Symptoms**: Staff permission updates don't apply immediately

**Resolution**:
1. **Force Sync**: Restart POS terminals
2. **Check Cache**: Clear application cache
3. **Verify Update**: Confirm changes saved in admin dashboard

## Analytics and Reporting Issues

### Dashboard Shows No Data

**Symptoms**: Analytics dashboard empty or shows zero values

**Troubleshooting**:

1. **Check Date Range**
   - Verify selected time period has data
   - Try different date ranges
   - Confirm timezone settings

2. **Verify Data Source**
   ```bash
   # Check database directly
   # Log into Supabase dashboard
   # Query orders table for recent data
   SELECT COUNT(*) FROM orders WHERE created_at >= NOW() - INTERVAL '24 hours';
   ```

3. **Test Data Collection**
   - Process a test order
   - Check if it appears in analytics
   - Verify real-time updates work

### Reports Not Generating

**Symptoms**: Export or scheduled reports fail to generate

**Common Causes**:
1. Large date range causing timeout
2. Insufficient permissions
3. Database connectivity issues

**Solutions**:
1. **Reduce Scope**: Try smaller date ranges
2. **Check Permissions**: Verify report access rights
3. **Manual Generation**: Test individual report sections

## System Administration Issues

### Backup Failures

**Symptoms**: Automated backups not running or failing

**Diagnosis**:
1. **Check Backup Schedule**
   ```bash
   # In admin dashboard
   System → Backups
   - Verify schedule is active
   - Check last successful backup date
   ```

2. **Review Error Logs**
   ```bash
   # Check system logs
   System → Health Monitoring → Error Logs
   - Look for backup-related errors
   - Note specific error messages
   ```

**Resolution**:
1. **Storage Space**: Ensure adequate storage available
2. **Permissions**: Verify backup service permissions
3. **Manual Backup**: Try running backup manually
4. **Contact Support**: If issues persist

### Feature Flags Not Working

**Symptoms**: Enabled features don't appear or disabled features still show

**Troubleshooting**:
1. **Verify Platform Selection**: Check if feature is enabled for specific platform
2. **Clear Cache**: Force refresh applications
3. **Check Feature Dependencies**: Some features require others to be enabled

## Hardware Issues

### Receipt Printer Problems

**Common Issues & Solutions**:

1. **No Printing**
   - Check power and paper
   - Verify USB/network connection
   - Test from POS settings

2. **Poor Print Quality**
   - Clean print head
   - Check paper quality
   - Adjust print density settings

3. **Paper Jams**
   - Clear jam carefully
   - Check paper loading
   - Verify paper width settings

### Cash Drawer Issues

**Troubleshooting Steps**:
1. **Manual Test**: Try opening with key
2. **Connection Check**: Verify cable to printer/POS
3. **Settings Verification**: Check cash drawer configuration
4. **Replace Cable**: Try different connection cable

## Performance Issues

### Slow Application Performance

**Symptoms**: Pages load slowly, operations take too long

**Optimization Steps**:

1. **Browser Optimization**
   - Close unnecessary tabs
   - Clear browser cache
   - Disable unused extensions

2. **Network Check**
   ```bash
   # Test connection speed
   speedtest-cli
   
   # Check latency to Supabase
   ping your-project.supabase.co
   ```

3. **System Resources**
   - Check CPU and memory usage
   - Close unnecessary applications
   - Restart if needed

### Database Query Timeouts

**Symptoms**: Operations fail with timeout errors

**Solutions**:
1. **Reduce Query Scope**: Use smaller date ranges
2. **Check Database Load**: Monitor Supabase usage
3. **Optimize Queries**: Review slow query reports
4. **Upgrade Plan**: Consider higher Supabase tier if needed

## Emergency Procedures

### Complete System Down

**Immediate Actions**:
1. **Check System Status**: Visit status.supabase.com
2. **Verify Internet**: Test general connectivity
3. **Fallback Mode**: Use offline POS capabilities
4. **Contact Support**: Report system-wide issues

### Data Corruption Detected

**Critical Steps**:
1. **Stop Operations**: Prevent further data changes
2. **Document Issue**: Note what was corrupted
3. **Contact Support**: Immediate technical assistance
4. **Restore Backup**: Prepare for potential restoration

## Getting Additional Help

### Before Contacting Support

1. **Gather Information**:
   - Error messages (exact text)
   - Steps to reproduce issue
   - Browser/system information
   - Screenshots of problem

2. **Check Resources**:
   - [FAQ](./faq.md)
   - [Common Issues](./common-issues.md)
   - System logs and error messages

### Contact Information

- **Technical Support**: <EMAIL>
- **Emergency Hotline**: 1-800-XXX-XXXX
- **Online Chat**: Available in admin dashboard
- **Knowledge Base**: help.yourcompany.com

### Support Ticket Information

Include in your support request:
- **System Version**: Check in System → About
- **Error Messages**: Copy exact text
- **Reproduction Steps**: How to recreate the issue
- **Impact Level**: How it affects your operations
- **Browser/Device**: What you're using to access the system

## Preventive Maintenance

### Daily Checks
- ✅ System health status
- ✅ Backup completion
- ✅ POS sync status
- ✅ Critical error review

### Weekly Tasks
- ✅ Performance review
- ✅ User account audit
- ✅ Menu accuracy verification
- ✅ Staff permission review

### Monthly Activities
- ✅ Full system backup test
- ✅ Security audit review
- ✅ Performance optimization
- ✅ Staff training update 