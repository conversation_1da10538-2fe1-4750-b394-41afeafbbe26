const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Remove any problematic serializer options
if (config.serializer) {
  delete config.serializer.isThirdPartyModule;
}

// Ensure clean serializer configuration
config.serializer = {
  ...config.serializer,
  // Remove the problematic isThirdPartyModule option
};

// Limit watching to only the customer-mobile directory
config.watchFolders = [__dirname];

// Exclude problematic directories
config.resolver = {
  ...config.resolver,
  blockList: [
    /admin-dashboard\/\.next\/.*/,
    /pos-system\/dist\/.*/,
    /customer-web\/\.next\/.*/,
  ],
};

module.exports = config;
