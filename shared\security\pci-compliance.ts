/**
 * PCI DSS Compliance Module
 * Implements Payment Card Industry Data Security Standard requirements
 * for secure payment processing and card data handling
 */

import { createClient } from '@supabase/supabase-js';
import { EncryptionManager } from './data-protection';
import { SecurityLogger } from './audit-logging';
import { SECURITY_CONFIG } from './security-config';
import crypto from 'crypto';

// Types
interface CardData {
  number: string;
  expiryMonth: string;
  expiryYear: string;
  cvv: string;
  holderName: string;
}

interface TokenizedCard {
  token: string;
  lastFourDigits: string;
  expiryMonth: string;
  expiryYear: string;
  cardType: string;
  holderName: string;
}

interface PaymentTransaction {
  id: string;
  amount: number;
  currency: string;
  cardToken: string;
  merchantId: string;
  status: 'pending' | 'approved' | 'declined' | 'failed';
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

interface PCIAuditLog {
  eventType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  details: Record<string, any>;
  userId?: string;
  ipAddress?: string;
  timestamp: Date;
}

interface SecurityAssessment {
  assessmentId: string;
  assessmentType: 'vulnerability_scan' | 'penetration_test' | 'code_review';
  scope: string[];
  findings: SecurityFinding[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  status: 'in_progress' | 'completed' | 'remediated';
  assessedBy: string;
  assessmentDate: Date;
}

interface SecurityFinding {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  cveId?: string;
  remediation: string;
  status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk';
}

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * PCI DSS Card Data Handler
 * Requirement 3: Protect stored cardholder data
 */
export class PCICardDataHandler {
  /**
   * Tokenizes card data for secure storage
   * PCI DSS Requirement 3.4: Render PAN unreadable
   */
  static async tokenizeCard(cardData: CardData, userId: string): Promise<TokenizedCard> {
    try {
      // Validate card data
      this.validateCardData(cardData);

      // Generate secure token
      const token = this.generateCardToken();

      // Encrypt sensitive data
      const encryptedData = await EncryptionManager.encryptData(JSON.stringify({
        number: cardData.number,
        cvv: cardData.cvv
      }), 'pci_card_data');

      // Store tokenized card data
      const tokenizedCard: TokenizedCard = {
        token,
        lastFourDigits: cardData.number.slice(-4),
        expiryMonth: cardData.expiryMonth,
        expiryYear: cardData.expiryYear,
        cardType: this.detectCardType(cardData.number),
        holderName: cardData.holderName
      };

      // Store in secure vault
      await supabase
        .from('card_tokens')
        .insert({
          token,
          encrypted_data: encryptedData,
          last_four_digits: tokenizedCard.lastFourDigits,
          expiry_month: tokenizedCard.expiryMonth,
          expiry_year: tokenizedCard.expiryYear,
          card_type: tokenizedCard.cardType,
          holder_name: tokenizedCard.holderName,
          user_id: userId,
          created_at: new Date()
        });

      // Log PCI compliance event
      await this.logPCIEvent({
        eventType: 'card_tokenization',
        severity: 'medium',
        details: {
          token,
          cardType: tokenizedCard.cardType,
          lastFourDigits: tokenizedCard.lastFourDigits
        },
        userId,
        timestamp: new Date()
      });

      return tokenizedCard;
    } catch (error) {
      await this.logPCIEvent({
        eventType: 'card_tokenization_failed',
        severity: 'high',
        details: { error: error instanceof Error ? error.message : String(error) },
        userId,
        timestamp: new Date()
      });
      throw error;
    }
  }

  /**
   * Retrieves card data using token
   * PCI DSS Requirement 3.4: Access to cryptographic keys
   */
  static async detokenizeCard(token: string, userId: string): Promise<CardData | null> {
    try {
      // Verify token ownership
      const { data: tokenData, error } = await supabase
        .from('card_tokens')
        .select('*')
        .eq('token', token)
        .eq('user_id', userId)
        .single();

      if (error || !tokenData) {
        await this.logPCIEvent({
          eventType: 'unauthorized_card_access',
          severity: 'critical',
          details: { token, userId },
          userId,
          timestamp: new Date()
        });
        return null;
      }

      // Decrypt card data
      const decryptedData = await EncryptionManager.decryptData(tokenData.encrypted_data, 'pci_card_data');
      const cardData = JSON.parse(decryptedData);

      // Log access
      await this.logPCIEvent({
        eventType: 'card_data_accessed',
        severity: 'medium',
        details: {
          token,
          lastFourDigits: tokenData.last_four_digits
        },
        userId,
        timestamp: new Date()
      });

      return {
        number: cardData.number,
        expiryMonth: tokenData.expiry_month,
        expiryYear: tokenData.expiry_year,
        cvv: cardData.cvv,
        holderName: tokenData.holder_name
      };
    } catch (error) {
      await this.logPCIEvent({
        eventType: 'card_detokenization_failed',
        severity: 'high',
        details: { error: error instanceof Error ? error.message : String(error), token },
        userId,
        timestamp: new Date()
      });
      return null;
    }
  }

  /**
   * Securely deletes card data
   * PCI DSS Requirement 3.1: Keep cardholder data storage to minimum
   */
  static async deleteCardData(token: string, userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('card_tokens')
        .delete()
        .eq('token', token)
        .eq('user_id', userId);

      if (error) {
        throw error;
      }

      await this.logPCIEvent({
        eventType: 'card_data_deleted',
        severity: 'medium',
        details: { token },
        userId,
        timestamp: new Date()
      });

      return true;
    } catch (error) {
      await this.logPCIEvent({
        eventType: 'card_deletion_failed',
        severity: 'high',
        details: { error: error instanceof Error ? error.message : String(error), token },
        userId,
        timestamp: new Date()
      });
      return false;
    }
  }

  private static validateCardData(cardData: CardData): void {
    // Validate card number using Luhn algorithm
    if (!this.isValidCardNumber(cardData.number)) {
      throw new Error('Invalid card number');
    }

    // Validate expiry date
    const currentDate = new Date();
    const expiryDate = new Date(
      parseInt(cardData.expiryYear),
      parseInt(cardData.expiryMonth) - 1
    );
    
    if (expiryDate <= currentDate) {
      throw new Error('Card has expired');
    }

    // Validate CVV
    if (!/^\d{3,4}$/.test(cardData.cvv)) {
      throw new Error('Invalid CVV');
    }

    // Validate holder name
    if (!cardData.holderName || cardData.holderName.length < 2) {
      throw new Error('Invalid cardholder name');
    }
  }

  private static isValidCardNumber(cardNumber: string): boolean {
    // Remove spaces and non-digits
    const cleanNumber = cardNumber.replace(/\D/g, '');
    
    // Check length
    if (cleanNumber.length < 13 || cleanNumber.length > 19) {
      return false;
    }

    // Luhn algorithm
    let sum = 0;
    let isEven = false;
    
    for (let i = cleanNumber.length - 1; i >= 0; i--) {
      let digit = parseInt(cleanNumber[i]);
      
      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }
      
      sum += digit;
      isEven = !isEven;
    }
    
    return sum % 10 === 0;
  }

  private static detectCardType(cardNumber: string): string {
    const cleanNumber = cardNumber.replace(/\D/g, '');
    
    if (/^4/.test(cleanNumber)) return 'visa';
    if (/^5[1-5]/.test(cleanNumber)) return 'mastercard';
    if (/^3[47]/.test(cleanNumber)) return 'amex';
    if (/^6(?:011|5)/.test(cleanNumber)) return 'discover';
    
    return 'unknown';
  }

  private static generateCardToken(): string {
    return `card_${crypto.randomUUID().replace(/-/g, '')}`;
  }

  private static async logPCIEvent(event: PCIAuditLog): Promise<void> {
    await supabase
      .from('pci_compliance_logs')
      .insert({
        event_type: event.eventType,
        severity: event.severity,
        details: event.details,
        user_id: event.userId,
        ip_address: event.ipAddress,
        created_at: event.timestamp
      });

    // Also log to security system
    await SecurityLogger.logEvent({
      eventType: `pci_${event.eventType}`,
      severity: event.severity,
      userId: event.userId,
      payload: event.details,
      ipAddress: event.ipAddress
    });
  }
}

/**
 * PCI DSS Network Security
 * Requirement 1: Install and maintain firewall configuration
 * Requirement 2: Do not use vendor-supplied defaults
 */
export class PCINetworkSecurity {
  /**
   * Validates network security configuration
   */
  static async validateNetworkSecurity(): Promise<{
    compliant: boolean;
    issues: string[];
  }> {
    const issues: string[] = [];

    // Check HTTPS enforcement
    if (!process.env.FORCE_HTTPS) {
      issues.push('HTTPS not enforced');
    }

    // Check TLS version
    const tlsVersion = process.env.MIN_TLS_VERSION;
    if (!tlsVersion || parseFloat(tlsVersion) < 1.2) {
      issues.push('TLS version below 1.2');
    }

    // Check firewall rules (would integrate with actual firewall)
    const firewallRules = await this.checkFirewallRules();
    if (!firewallRules.compliant) {
      issues.push(...firewallRules.issues);
    }

    // Check for default passwords
    const defaultPasswords = await this.checkDefaultPasswords();
    if (defaultPasswords.length > 0) {
      issues.push('Default passwords detected');
    }

    return {
      compliant: issues.length === 0,
      issues
    };
  }

  /**
   * Monitors network traffic for suspicious activity
   */
  static async monitorNetworkTraffic(
    ipAddress: string,
    endpoint: string,
    method: string,
    payload?: any
  ): Promise<void> {
    // Check for suspicious patterns
    const suspiciousPatterns = [
      /admin/i,
      /sql/i,
      /script/i,
      /union/i,
      /select/i,
      /drop/i,
      /delete/i
    ];

    const isSuspicious = suspiciousPatterns.some(pattern => 
      pattern.test(endpoint) || 
      (payload && pattern.test(JSON.stringify(payload)))
    );

    if (isSuspicious) {
      await SecurityLogger.logSuspiciousActivity(
        'suspicious_network_traffic',
        {
          endpoint,
          method,
          payload: payload ? JSON.stringify(payload).substring(0, 1000) : null,
          patterns: suspiciousPatterns.filter(p => 
            p.test(endpoint) || (payload && p.test(JSON.stringify(payload)))
          ).map(p => p.toString())
        },
        ipAddress
      );
    }

    // Log to PCI compliance
    await supabase
      .from('pci_compliance_logs')
      .insert({
        event_type: 'network_access',
        severity: isSuspicious ? 'high' : 'low',
        details: {
          endpoint,
          method,
          suspicious: isSuspicious
        },
        ip_address: ipAddress,
        created_at: new Date()
      });
  }

  private static async checkFirewallRules(): Promise<{
    compliant: boolean;
    issues: string[];
  }> {
    // This would integrate with actual firewall management
    // For now, return mock compliance check
    return {
      compliant: true,
      issues: []
    };
  }

  private static async checkDefaultPasswords(): Promise<string[]> {
    // Check for common default passwords in configuration
    const defaultPasswords = [
      'admin',
      'password',
      '123456',
      'default',
      'root'
    ];

    // This would check actual system passwords
    // For now, return empty array
    return [];
  }
}

/**
 * PCI DSS Access Control
 * Requirement 7: Restrict access to cardholder data by business need-to-know
 * Requirement 8: Identify and authenticate access to system components
 */
export class PCIAccessControl {
  /**
   * Validates user access to cardholder data
   */
  static async validateCardDataAccess(
    userId: string,
    operation: 'read' | 'write' | 'delete',
    resourceId?: string
  ): Promise<boolean> {
    try {
      // Get user permissions
      const { data: userProfile } = await supabase
        .from('user_profiles')
        .select('role, permissions')
        .eq('id', userId)
        .single();

      if (!userProfile) {
        await this.logAccessViolation(userId, operation, 'user_not_found');
        return false;
      }

      // Check role-based access
      const allowedRoles = ['admin', 'payment_processor', 'finance_manager'];
      if (!allowedRoles.includes(userProfile.role)) {
        await this.logAccessViolation(userId, operation, 'insufficient_role');
        return false;
      }

      // Check specific permissions
      const requiredPermission = `card_data_${operation}`;
      if (!userProfile.permissions?.includes(requiredPermission)) {
        await this.logAccessViolation(userId, operation, 'missing_permission');
        return false;
      }

      // Log successful access
      await supabase
        .from('pci_compliance_logs')
        .insert({
          event_type: 'card_data_access_granted',
          severity: 'medium',
          details: {
            operation,
            resourceId,
            role: userProfile.role
          },
          user_id: userId,
          created_at: new Date()
        });

      return true;
    } catch (error) {
      await this.logAccessViolation(userId, operation, 'system_error');
      return false;
    }
  }

  /**
   * Implements principle of least privilege
   */
  static async enforceMinimalAccess(
    userId: string,
    requestedPermissions: string[]
  ): Promise<string[]> {
    // Get user's current role and responsibilities
    const { data: userProfile } = await supabase
      .from('user_profiles')
      .select('role, department, job_function')
      .eq('id', userId)
      .single();

    if (!userProfile) {
      return [];
    }

    // Define minimal permissions by role
    const rolePermissions: Record<string, string[]> = {
      'cashier': ['order_create', 'payment_process'],
      'manager': ['order_create', 'order_view', 'payment_process', 'payment_refund'],
      'admin': ['*'], // Full access
      'finance_manager': ['payment_view', 'payment_refund', 'financial_reports'],
      'payment_processor': ['card_data_read', 'payment_process']
    };

    const allowedPermissions = rolePermissions[userProfile.role] || [];
    
    // Filter requested permissions based on role
    const grantedPermissions = allowedPermissions.includes('*') 
      ? requestedPermissions
      : requestedPermissions.filter(perm => allowedPermissions.includes(perm));

    // Log permission grant
    await supabase
      .from('pci_compliance_logs')
      .insert({
        event_type: 'permissions_granted',
        severity: 'low',
        details: {
          requestedPermissions,
          grantedPermissions,
          role: userProfile.role
        },
        user_id: userId,
        created_at: new Date()
      });

    return grantedPermissions;
  }

  private static async logAccessViolation(
    userId: string,
    operation: string,
    reason: string
  ): Promise<void> {
    await supabase
      .from('pci_compliance_logs')
      .insert({
        event_type: 'card_data_access_denied',
        severity: 'high',
        details: {
          operation,
          reason,
          timestamp: new Date()
        },
        user_id: userId,
        created_at: new Date()
      });

    await SecurityLogger.logSuspiciousActivity(
      'unauthorized_card_access',
      {
        operation,
        reason,
        userId
      }
    );
  }
}

/**
 * PCI DSS Vulnerability Management
 * Requirement 6: Develop and maintain secure systems and applications
 * Requirement 11: Regularly test security systems and processes
 */
export class PCIVulnerabilityManagement {
  /**
   * Performs security assessment
   */
  static async performSecurityAssessment(
    assessmentType: 'vulnerability_scan' | 'penetration_test' | 'code_review',
    scope: string[],
    assessedBy: string
  ): Promise<SecurityAssessment> {
    const assessmentId = crypto.randomUUID();
    
    // Perform assessment based on type
    let findings: SecurityFinding[] = [];
    
    switch (assessmentType) {
      case 'vulnerability_scan':
        findings = await this.performVulnerabilityScan(scope);
        break;
      case 'penetration_test':
        findings = await this.performPenetrationTest(scope);
        break;
      case 'code_review':
        findings = await this.performCodeReview(scope);
        break;
    }

    // Calculate overall risk level
    const riskLevel = this.calculateRiskLevel(findings);

    const assessment: SecurityAssessment = {
      assessmentId,
      assessmentType,
      scope,
      findings,
      riskLevel,
      status: 'completed',
      assessedBy,
      assessmentDate: new Date()
    };

    // Store assessment
    await supabase
      .from('vulnerability_assessments')
      .insert({
        assessment_id: assessmentId,
        assessment_type: assessmentType,
        scope,
        findings,
        risk_level: riskLevel,
        status: 'completed',
        assessed_by: assessedBy,
        assessment_date: new Date()
      });

    // Log PCI compliance event
    await supabase
      .from('pci_compliance_logs')
      .insert({
        event_type: 'security_assessment_completed',
        severity: riskLevel === 'critical' ? 'critical' : 'medium',
        details: {
          assessmentId,
          assessmentType,
          findingsCount: findings.length,
          riskLevel
        },
        user_id: assessedBy,
        created_at: new Date()
      });

    return assessment;
  }

  /**
   * Tracks remediation of security findings
   */
  static async trackRemediation(
    findingId: string,
    status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk',
    remediatedBy: string,
    notes?: string
  ): Promise<void> {
    await supabase
      .from('security_findings')
      .update({
        status,
        remediated_by: remediatedBy,
        remediation_notes: notes,
        remediation_date: status === 'resolved' ? new Date() : null
      })
      .eq('id', findingId);

    await supabase
      .from('pci_compliance_logs')
      .insert({
        event_type: 'finding_remediation',
        severity: 'medium',
        details: {
          findingId,
          status,
          notes
        },
        user_id: remediatedBy,
        created_at: new Date()
      });
  }

  private static async performVulnerabilityScan(scope: string[]): Promise<SecurityFinding[]> {
    // Mock vulnerability scan results
    // In real implementation, this would integrate with vulnerability scanners
    return [
      {
        id: crypto.randomUUID(),
        title: 'Outdated SSL/TLS Configuration',
        description: 'Server supports deprecated TLS versions',
        severity: 'medium',
        category: 'network_security',
        remediation: 'Update TLS configuration to support only TLS 1.2+',
        status: 'open'
      }
    ];
  }

  private static async performPenetrationTest(scope: string[]): Promise<SecurityFinding[]> {
    // Mock penetration test results
    return [
      {
        id: crypto.randomUUID(),
        title: 'SQL Injection Vulnerability',
        description: 'Input validation bypass in payment endpoint',
        severity: 'high',
        category: 'application_security',
        remediation: 'Implement parameterized queries and input validation',
        status: 'open'
      }
    ];
  }

  private static async performCodeReview(scope: string[]): Promise<SecurityFinding[]> {
    // Mock code review results
    return [
      {
        id: crypto.randomUUID(),
        title: 'Hardcoded Credentials',
        description: 'API keys found in source code',
        severity: 'critical',
        category: 'code_security',
        remediation: 'Move credentials to environment variables',
        status: 'open'
      }
    ];
  }

  private static calculateRiskLevel(findings: SecurityFinding[]): 'low' | 'medium' | 'high' | 'critical' {
    const criticalCount = findings.filter(f => f.severity === 'critical').length;
    const highCount = findings.filter(f => f.severity === 'high').length;
    const mediumCount = findings.filter(f => f.severity === 'medium').length;

    if (criticalCount > 0) return 'critical';
    if (highCount > 2) return 'high';
    if (highCount > 0 || mediumCount > 5) return 'medium';
    return 'low';
  }
}

/**
 * PCI DSS Compliance Reporter
 * Generates compliance reports and tracks compliance status
 */
export class PCIComplianceReporter {
  /**
   * Generates PCI DSS compliance report
   */
  static async generateComplianceReport(
    startDate: Date,
    endDate: Date,
    generatedBy: string
  ): Promise<any> {
    const [logs, assessments, incidents] = await Promise.all([
      this.getPCILogs(startDate, endDate),
      this.getSecurityAssessments(startDate, endDate),
      this.getSecurityIncidents(startDate, endDate)
    ]);

    const report = {
      reportType: 'pci_dss_compliance',
      period: { startDate, endDate },
      summary: {
        totalEvents: logs.length,
        securityIncidents: incidents.length,
        vulnerabilityAssessments: assessments.length,
        complianceScore: this.calculateComplianceScore(logs, assessments, incidents)
      },
      requirements: {
        requirement1: await this.checkRequirement1(), // Firewall
        requirement2: await this.checkRequirement2(), // Default passwords
        requirement3: await this.checkRequirement3(), // Cardholder data protection
        requirement4: await this.checkRequirement4(), // Encryption in transit
        requirement6: await this.checkRequirement6(), // Secure systems
        requirement7: await this.checkRequirement7(), // Access control
        requirement8: await this.checkRequirement8(), // Authentication
        requirement10: await this.checkRequirement10(), // Logging
        requirement11: await this.checkRequirement11()  // Security testing
      },
      logs,
      assessments,
      incidents,
      generatedBy,
      generatedAt: new Date()
    };

    // Store report
    await supabase
      .from('compliance_reports')
      .insert({
        report_type: 'pci_dss',
        period_start: startDate,
        period_end: endDate,
        report_data: report,
        generated_by: generatedBy,
        created_at: new Date()
      });

    return report;
  }

  private static async getPCILogs(startDate: Date, endDate: Date): Promise<any[]> {
    const { data } = await supabase
      .from('pci_compliance_logs')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .order('created_at', { ascending: false });
    return data || [];
  }

  private static async getSecurityAssessments(startDate: Date, endDate: Date): Promise<any[]> {
    const { data } = await supabase
      .from('vulnerability_assessments')
      .select('*')
      .gte('assessment_date', startDate.toISOString())
      .lte('assessment_date', endDate.toISOString());
    return data || [];
  }

  private static async getSecurityIncidents(startDate: Date, endDate: Date): Promise<any[]> {
    const { data } = await supabase
      .from('security_incidents')
      .select('*')
      .gte('detection_time', startDate.toISOString())
      .lte('detection_time', endDate.toISOString());
    return data || [];
  }

  private static calculateComplianceScore(
    logs: any[],
    assessments: any[],
    incidents: any[]
  ): number {
    let score = 100;

    // Deduct points for incidents
    score -= incidents.length * 5;

    // Deduct points for high-severity findings
    const criticalFindings = assessments.reduce((count, assessment) => {
      return count + (assessment.findings?.filter((f: any) => f.severity === 'critical').length || 0);
    }, 0);
    score -= criticalFindings * 10;

    // Deduct points for compliance violations
    const violations = logs.filter(log => log.severity === 'high' || log.severity === 'critical');
    score -= violations.length * 2;

    return Math.max(score, 0);
  }

  // Mock compliance checks for each requirement
  private static async checkRequirement1(): Promise<{ compliant: boolean; details: string }> {
    return { compliant: true, details: 'Firewall configuration validated' };
  }

  private static async checkRequirement2(): Promise<{ compliant: boolean; details: string }> {
    return { compliant: true, details: 'No default passwords detected' };
  }

  private static async checkRequirement3(): Promise<{ compliant: boolean; details: string }> {
    return { compliant: true, details: 'Cardholder data properly encrypted' };
  }

  private static async checkRequirement4(): Promise<{ compliant: boolean; details: string }> {
    return { compliant: true, details: 'Strong encryption in transit enforced' };
  }

  private static async checkRequirement6(): Promise<{ compliant: boolean; details: string }> {
    return { compliant: true, details: 'Secure development practices implemented' };
  }

  private static async checkRequirement7(): Promise<{ compliant: boolean; details: string }> {
    return { compliant: true, details: 'Access control by business need-to-know enforced' };
  }

  private static async checkRequirement8(): Promise<{ compliant: boolean; details: string }> {
    return { compliant: true, details: 'Strong authentication mechanisms in place' };
  }

  private static async checkRequirement10(): Promise<{ compliant: boolean; details: string }> {
    return { compliant: true, details: 'Comprehensive logging and monitoring active' };
  }

  private static async checkRequirement11(): Promise<{ compliant: boolean; details: string }> {
    return { compliant: true, details: 'Regular security testing performed' };
  }
}

// Export all PCI compliance utilities
