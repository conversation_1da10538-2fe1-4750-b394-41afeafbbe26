{"permissions": {"allow": ["Bash(node:*)", "Bash(npm --version)", "<PERSON><PERSON>(claude auth status)", "<PERSON><PERSON>(claude auth login)", "Bash(npx tsc:*)", "Bash(npm run build:renderer:*)", "Bash(npm run build:*)", "Bash(npm install:*)", "Bash(timeout 90 npm run build)", "Bash(npm run dev:main:*)", "Bash(rm:*)", "Bash(find /mnt/d/trae/The-Small-002/admin-dashboard -name \".swcrc\" -o -name \"swc.config.js\" -o -name \"swc.config.json\" 2>/dev/null)", "Bash(grep:*)", "Bash(rg:*)", "Bash(npx next build:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(timeout 5s npm run dev:renderer)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(apt:*)", "Bash(apt install:*)", "Bash(PGPASSWORD=\"98741235\" psql -h voiwzwyfnkzvcffuxpwl.supabase.co -p 6543 -U postgres -d postgres -f supabase/migrations/20240624000008_enhanced_pos_sync.sql)", "Bash(PGPASSWORD=\"98741235\" psql \"postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require\" -f supabase/migrations/20240624000008_enhanced_pos_sync.sql)", "Bash(./supabase-cli login)", "Bash(SUPABASE_ACCESS_TOKEN=\"********************************************\" ./supabase-cli projects list)", "Bash(PGPASSWORD=\"98741235\" psql \"postgresql://postgres:<EMAIL>:6543/postgres?sslmode=require\" -c \"SELECT version();\")", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "<PERSON><PERSON>(npx playwright install:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(pkill:*)", "Bash(git checkout:*)", "Bash(cp:*)", "Bash(npm run lint:*)", "Bash(npm ls:*)", "Bash(npm search:*)", "Bash(git add:*)", "Bash(git config:*)", "Bash(git push:*)", "Bash(git remote set-url:*)", "Bash(npm outdated)", "Bash(npm update:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(npx jest:*)", "Bash(/mnt/d/The-Small-002/node_modules/electron/dist/electron.exe /mnt/d/The-Small-002/pos-system/dist/main/main.js --remote-debugging-port=9222 --disable-web-security --no-sandbox)", "Bash(/mnt/d/The-Small-002/node_modules/electron/dist/electron.exe ./dist/main/main.js --remote-debugging-port=9222 --disable-web-security --no-sandbox)", "<PERSON><PERSON>(npx playwright test:*)", "Bash(npm uninstall:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(npx cypress run:*)", "Bash(npm run type-check:*)", "Bash(npx @upstash/context7-mcp:*)", "Bash(npx @modelcontextprotocol/server-sequential-thinking:*)"], "deny": []}}