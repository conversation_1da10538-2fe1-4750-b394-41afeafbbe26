/**
 * Audit Logging System
 * Implements comprehensive audit logging for:
 * - All user actions and interactions
 * - Data changes and modifications
 * - Login attempts and authentication events
 * - System events and security incidents
 * - Compliance and regulatory reporting
 */

import { createClient } from '@supabase/supabase-js';
import { SECURITY_CONFIG } from './security-config';
import crypto from 'crypto';

// Types
interface AuditLogEntry {
  id?: string;
  action: string;
  resource: string;
  resourceId?: string;
  userId?: string;
  changes?: Record<string, any>;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  timestamp?: Date;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  category?: 'user_action' | 'data_change' | 'system_event' | 'security_event';
}

interface SecurityLogEntry {
  id?: string;
  eventType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  ipAddress?: string;
  userAgent?: string;
  endpoint?: string;
  httpMethod?: string;
  userId?: string;
  payload?: Record<string, any>;
  metadata?: Record<string, any>;
  timestamp?: Date;
}

interface UserActionLog {
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: Record<string, any>;
  platform: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
}

interface DataChangeLog {
  tableName: string;
  recordId: string;
  operation: 'INSERT' | 'UPDATE' | 'DELETE';
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  changes?: Record<string, any>;
  userId?: string;
  reason?: string;
}

interface SystemEventLog {
  eventType: string;
  component: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  details?: Record<string, any>;
  stackTrace?: string;
}

interface ComplianceReport {
  reportType: string;
  startDate: Date;
  endDate: Date;
  filters?: Record<string, any>;
  data: any[];
  generatedBy: string;
  generatedAt: Date;
}

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * Main Audit Logger
 */
export class AuditLogger {
  /**
   * Logs a general audit event
   */
  static async logEvent(entry: AuditLogEntry): Promise<string | null> {
    try {
      const logEntry = {
        action: entry.action,
        resource: entry.resource,
        resource_id: entry.resourceId,
        user_id: entry.userId,
        changes: entry.changes || {},
        old_values: entry.oldValues || {},
        new_values: entry.newValues || {},
        metadata: {
          ...entry.metadata,
          severity: entry.severity || 'low',
          category: entry.category || 'user_action',
          ip_address: entry.ipAddress,
          user_agent: entry.userAgent,
          session_id: entry.sessionId,
          timestamp: entry.timestamp || new Date()
        },
        ip_address: entry.ipAddress,
        user_agent: entry.userAgent
      };

      const { data, error } = await supabase
        .from('audit_logs')
        .insert(logEntry)
        .select('id')
        .single();

      if (error) {
        console.error('Failed to log audit event:', error);
        return null;
      }

      // If this is a high-severity event, also log as security event
      if (entry.severity === 'high' || entry.severity === 'critical') {
        await SecurityLogger.logEvent({
          eventType: `audit_${entry.action}`,
          severity: entry.severity,
          userId: entry.userId,
          payload: { resource: entry.resource, resourceId: entry.resourceId },
          metadata: entry.metadata,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent
        });
      }

      return data.id;
    } catch (error) {
      console.error('Error logging audit event:', error);
      return null;
    }
  }

  /**
   * Logs user actions
   */
  static async logUserAction(action: UserActionLog): Promise<void> {
    await this.logEvent({
      action: action.action,
      resource: action.resource,
      resourceId: action.resourceId,
      userId: action.userId,
      metadata: {
        platform: action.platform,
        details: action.details
      },
      ipAddress: action.ipAddress,
      userAgent: action.userAgent,
      sessionId: action.sessionId,
      category: 'user_action',
      severity: 'low'
    });
  }

  /**
   * Logs data changes
   */
  static async logDataChange(change: DataChangeLog): Promise<void> {
    const changes = this.calculateChanges(change.oldValues, change.newValues);
    
    await this.logEvent({
      action: change.operation,
      resource: change.tableName,
      resourceId: change.recordId,
      userId: change.userId,
      oldValues: change.oldValues,
      newValues: change.newValues,
      changes,
      metadata: {
        reason: change.reason,
        changeCount: Object.keys(changes).length
      },
      category: 'data_change',
      severity: this.getDataChangeSeverity(change.tableName, change.operation)
    });
  }

  /**
   * Logs system events
   */
  static async logSystemEvent(event: SystemEventLog): Promise<void> {
    await this.logEvent({
      action: event.eventType,
      resource: event.component,
      metadata: {
        message: event.message,
        details: event.details,
        stackTrace: event.stackTrace,
        systemEvent: true
      },
      category: 'system_event',
      severity: this.mapSystemSeverity(event.severity)
    });
  }

  /**
   * Retrieves audit logs with filtering
   */
  static async getAuditLogs(
    filters: {
      userId?: string;
      resource?: string;
      action?: string;
      startDate?: Date;
      endDate?: Date;
      severity?: string;
      category?: string;
    },
    limit: number = 100,
    offset: number = 0
  ): Promise<AuditLogEntry[]> {
    try {
      let query = supabase
        .from('audit_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (filters.userId) {
        query = query.eq('user_id', filters.userId);
      }

      if (filters.resource) {
        query = query.eq('resource', filters.resource);
      }

      if (filters.action) {
        query = query.eq('action', filters.action);
      }

      if (filters.startDate) {
        query = query.gte('created_at', filters.startDate.toISOString());
      }

      if (filters.endDate) {
        query = query.lte('created_at', filters.endDate.toISOString());
      }

      if (filters.severity) {
        query = query.eq('metadata->>severity', filters.severity);
      }

      if (filters.category) {
        query = query.eq('metadata->>category', filters.category);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error retrieving audit logs:', error);
      return [];
    }
  }

  private static calculateChanges(
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>
  ): Record<string, any> {
    const changes: Record<string, any> = {};

    if (!oldValues && newValues) {
      // New record
      return { created: newValues };
    }

    if (oldValues && !newValues) {
      // Deleted record
      return { deleted: oldValues };
    }

    if (oldValues && newValues) {
      // Updated record
      for (const key in newValues) {
        if (oldValues[key] !== newValues[key]) {
          changes[key] = {
            from: oldValues[key],
            to: newValues[key]
          };
        }
      }

      // Check for removed fields
      for (const key in oldValues) {
        if (!(key in newValues)) {
          changes[key] = {
            from: oldValues[key],
            to: null
          };
        }
      }
    }

    return changes;
  }

  private static getDataChangeSeverity(
    tableName: string,
    operation: string
  ): 'low' | 'medium' | 'high' | 'critical' {
    // Critical tables
    const criticalTables = ['payments', 'user_profiles', 'encryption_keys'];
    if (criticalTables.includes(tableName)) {
      return operation === 'DELETE' ? 'critical' : 'high';
    }

    // High-importance tables
    const highTables = ['orders', 'products', 'roles', 'permissions'];
    if (highTables.includes(tableName)) {
      return operation === 'DELETE' ? 'high' : 'medium';
    }

    return 'low';
  }

  private static mapSystemSeverity(
    severity: 'info' | 'warning' | 'error' | 'critical'
  ): 'low' | 'medium' | 'high' | 'critical' {
    const mapping = {
      info: 'low' as const,
      warning: 'medium' as const,
      error: 'high' as const,
      critical: 'critical' as const
    };
    return mapping[severity];
  }
}

/**
 * Security Event Logger
 */
export class SecurityLogger {
  /**
   * Logs security events
   */
  static async logEvent(entry: SecurityLogEntry): Promise<string | null> {
    try {
      const logEntry = {
        event_type: entry.eventType,
        severity: entry.severity,
        ip_address: entry.ipAddress,
        user_agent: entry.userAgent,
        endpoint: entry.endpoint,
        http_method: entry.httpMethod,
        user_id: entry.userId,
        payload: entry.payload || {},
        metadata: {
          ...entry.metadata,
          timestamp: entry.timestamp || new Date(),
          event_id: crypto.randomUUID()
        }
      };

      const { data, error } = await supabase
        .from('security_logs')
        .insert(logEntry)
        .select('id')
        .single();

      if (error) {
        console.error('Failed to log security event:', error);
        return null;
      }

      // Trigger alerts for high-severity events
      if (entry.severity === 'high' || entry.severity === 'critical') {
        await this.triggerSecurityAlert(entry);
      }

      return data.id;
    } catch (error) {
      console.error('Error logging security event:', error);
      return null;
    }
  }

  /**
   * Logs authentication attempts
   */
  static async logAuthAttempt(
    identifier: string,
    attemptType: string,
    platform: string,
    success: boolean,
    failureReason?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await this.logEvent({
      eventType: success ? 'auth_success' : 'auth_failure',
      severity: success ? 'low' : 'medium',
      ipAddress,
      userAgent,
      payload: {
        identifier,
        attemptType,
        platform,
        failureReason
      }
    });
  }

  /**
   * Logs suspicious activities
   */
  static async logSuspiciousActivity(
    activityType: string,
    details: Record<string, any>,
    ipAddress?: string,
    userId?: string
  ): Promise<void> {
    await this.logEvent({
      eventType: 'suspicious_activity',
      severity: 'high',
      ipAddress,
      userId,
      payload: {
        activityType,
        details,
        riskScore: this.calculateRiskScore(activityType, details)
      }
    });
  }

  /**
   * Retrieves security logs
   */
  static async getSecurityLogs(
    filters: {
      eventType?: string;
      severity?: string;
      userId?: string;
      ipAddress?: string;
      startDate?: Date;
      endDate?: Date;
    },
    limit: number = 100
  ): Promise<SecurityLogEntry[]> {
    try {
      let query = supabase
        .from('security_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (filters.eventType) {
        query = query.eq('event_type', filters.eventType);
      }

      if (filters.severity) {
        query = query.eq('severity', filters.severity);
      }

      if (filters.userId) {
        query = query.eq('user_id', filters.userId);
      }

      if (filters.ipAddress) {
        query = query.eq('ip_address', filters.ipAddress);
      }

      if (filters.startDate) {
        query = query.gte('created_at', filters.startDate.toISOString());
      }

      if (filters.endDate) {
        query = query.lte('created_at', filters.endDate.toISOString());
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error retrieving security logs:', error);
      return [];
    }
  }

  private static async triggerSecurityAlert(entry: SecurityLogEntry): Promise<void> {
    // Implementation would integrate with alerting system
    // For now, we'll log to console and create a notification
    console.warn('SECURITY ALERT:', {
      eventType: entry.eventType,
      severity: entry.severity,
      timestamp: new Date(),
      details: entry.payload
    });

    // Create notification for security team
    await supabase
      .from('staff_notifications')
      .insert({
        type: 'security_alert',
        title: `Security Alert: ${entry.eventType}`,
        message: `Severity: ${entry.severity}`,
        data: entry.payload,
        priority: entry.severity === 'critical' ? 'urgent' : 'high'
      });
  }

  private static calculateRiskScore(
    activityType: string,
    details: Record<string, any>
  ): number {
    let score = 0;

    // Base scores by activity type
    const baseScores: Record<string, number> = {
      'multiple_failed_logins': 30,
      'unusual_location': 20,
      'suspicious_user_agent': 15,
      'rapid_requests': 25,
      'privilege_escalation': 40,
      'data_exfiltration': 50
    };

    score += baseScores[activityType] || 10;

    // Additional factors
    if (details.attemptCount > 10) score += 20;
    if (details.fromTor) score += 30;
    if (details.newDevice) score += 10;
    if (details.offHours) score += 15;

    return Math.min(score, 100); // Cap at 100
  }
}

/**
 * Login Attempt Logger
 */
export class LoginLogger {
  /**
   * Logs login attempts with detailed information
   */
  static async logLoginAttempt(
    identifier: string,
    platform: string,
    success: boolean,
    metadata: {
      ipAddress?: string;
      userAgent?: string;
      deviceFingerprint?: string;
      location?: string;
      failureReason?: string;
      sessionId?: string;
      userId?: string;
    }
  ): Promise<void> {
    // Log to audit system
    await AuditLogger.logEvent({
      action: success ? 'login_success' : 'login_failure',
      resource: 'authentication',
      resourceId: identifier,
      userId: metadata.userId,
      metadata: {
        platform,
        deviceFingerprint: metadata.deviceFingerprint,
        location: metadata.location,
        failureReason: metadata.failureReason
      },
      ipAddress: metadata.ipAddress,
      userAgent: metadata.userAgent,
      sessionId: metadata.sessionId,
      category: 'security_event',
      severity: success ? 'low' : 'medium'
    });

    // Log to security system
    await SecurityLogger.logAuthAttempt(
      identifier,
      'login',
      platform,
      success,
      metadata.failureReason,
      metadata.ipAddress,
      metadata.userAgent
    );

    // Store in auth_attempts table for rate limiting
    await supabase.rpc('log_auth_attempt', {
      p_identifier: identifier,
      p_attempt_type: 'login',
      p_platform: platform,
      p_success: success,
      p_failure_reason: metadata.failureReason,
      p_ip_address: metadata.ipAddress,
      p_user_agent: metadata.userAgent
    });
  }

  /**
   * Logs logout events
   */
  static async logLogout(
    userId: string,
    platform: string,
    sessionId: string,
    reason: 'user_initiated' | 'timeout' | 'forced' | 'security',
    ipAddress?: string
  ): Promise<void> {
    await AuditLogger.logEvent({
      action: 'logout',
      resource: 'authentication',
      userId,
      metadata: {
        platform,
        reason,
        sessionDuration: await this.calculateSessionDuration(sessionId)
      },
      ipAddress,
      sessionId,
      category: 'user_action',
      severity: reason === 'security' ? 'medium' : 'low'
    });
  }

  /**
   * Logs password changes
   */
  static async logPasswordChange(
    userId: string,
    reason: 'user_requested' | 'admin_reset' | 'security_policy' | 'expired',
    ipAddress?: string,
    adminUserId?: string
  ): Promise<void> {
    await AuditLogger.logEvent({
      action: 'password_change',
      resource: 'user_credentials',
      resourceId: userId,
      userId: adminUserId || userId,
      metadata: {
        reason,
        targetUserId: userId,
        isAdminAction: !!adminUserId
      },
      ipAddress,
      category: 'security_event',
      severity: 'medium'
    });
  }

  private static async calculateSessionDuration(sessionId: string): Promise<number> {
    try {
      const { data } = await supabase
        .from('user_sessions')
        .select('created_at')
        .eq('id', sessionId)
        .single();

      if (data) {
        const startTime = new Date(data.created_at);
        const endTime = new Date();
        return endTime.getTime() - startTime.getTime();
      }
    } catch (error) {
      console.error('Error calculating session duration:', error);
    }
    return 0;
  }
}

/**
 * Compliance Reporter
 */
export class ComplianceReporter {
  /**
   * Generates GDPR compliance report
   */
  static async generateGDPRReport(
    startDate: Date,
    endDate: Date,
    generatedBy: string
  ): Promise<ComplianceReport> {
    const data = await Promise.all([
      this.getGDPRRequests(startDate, endDate),
      this.getDataProcessingActivities(startDate, endDate),
      this.getDataBreaches(startDate, endDate),
      this.getConsentRecords(startDate, endDate)
    ]);

    return {
      reportType: 'gdpr_compliance',
      startDate,
      endDate,
      data: [
        data[0], // gdprRequests
        data[1], // dataProcessing  
        data[2], // dataBreaches
        data[3]  // consentRecords
      ],
      generatedBy,
      generatedAt: new Date()
    };
  }

  /**
   * Generates security audit report
   */
  static async generateSecurityReport(
    startDate: Date,
    endDate: Date,
    generatedBy: string
  ): Promise<ComplianceReport> {
    const data = await Promise.all([
      SecurityLogger.getSecurityLogs({ startDate, endDate }, 1000),
      this.getSecurityIncidents(startDate, endDate),
      this.getVulnerabilityAssessments(startDate, endDate),
      this.getAccessControlEvents(startDate, endDate)
    ]);

    return {
      reportType: 'security_audit',
      startDate,
      endDate,
      data: [
        data[0], // securityLogs
        data[1], // incidents
        data[2], // vulnerabilities
        data[3]  // accessControl
      ],
      generatedBy,
      generatedAt: new Date()
    };
  }

  /**
   * Generates user activity report
   */
  static async generateUserActivityReport(
    userId: string,
    startDate: Date,
    endDate: Date,
    generatedBy: string
  ): Promise<ComplianceReport> {
    const data = await AuditLogger.getAuditLogs(
      { userId, startDate, endDate },
      1000
    );

    return {
      reportType: 'user_activity',
      startDate,
      endDate,
      filters: { userId },
      data,
      generatedBy,
      generatedAt: new Date()
    };
  }

  private static async getGDPRRequests(startDate: Date, endDate: Date): Promise<any[]> {
    const { data } = await supabase
      .from('gdpr_requests')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());
    return data || [];
  }

  private static async getDataProcessingActivities(startDate: Date, endDate: Date): Promise<any[]> {
    const { data } = await supabase
      .from('audit_logs')
      .select('*')
      .in('action', ['INSERT', 'UPDATE', 'DELETE'])
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());
    return data || [];
  }

  private static async getDataBreaches(startDate: Date, endDate: Date): Promise<any[]> {
    const { data } = await supabase
      .from('security_incidents')
      .select('*')
      .eq('incident_type', 'data_breach')
      .gte('detection_time', startDate.toISOString())
      .lte('detection_time', endDate.toISOString());
    return data || [];
  }

  private static async getConsentRecords(startDate: Date, endDate: Date): Promise<any[]> {
    // Implementation would depend on consent management system
    return [];
  }

  private static async getSecurityIncidents(startDate: Date, endDate: Date): Promise<any[]> {
    const { data } = await supabase
      .from('security_incidents')
      .select('*')
      .gte('detection_time', startDate.toISOString())
      .lte('detection_time', endDate.toISOString());
    return data || [];
  }

  private static async getVulnerabilityAssessments(startDate: Date, endDate: Date): Promise<any[]> {
    const { data } = await supabase
      .from('vulnerability_assessments')
      .select('*')
      .gte('discovered_date', startDate.toISOString())
      .lte('discovered_date', endDate.toISOString());
    return data || [];
  }

  private static async getAccessControlEvents(startDate: Date, endDate: Date): Promise<any[]> {
    const { data } = await supabase
      .from('audit_logs')
      .select('*')
      .in('action', ['login_success', 'login_failure', 'permission_granted', 'permission_denied'])
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());
    return data || [];
  }
}

