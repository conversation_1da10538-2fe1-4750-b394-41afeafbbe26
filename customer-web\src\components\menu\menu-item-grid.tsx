'use client';

import { MenuItem } from '@/types/menu';
import Image from 'next/image';

interface MenuItemGridProps {
  items: MenuItem[];
  onItemSelect?: (item: MenuItem) => void;
}

export function MenuItemGrid({ items, onItemSelect }: MenuItemGridProps) {
  const handleItemClick = (item: MenuItem) => {
    if (onItemSelect) {
      onItemSelect(item);
    }
  };

  const formatPrice = (price: number) => {
    return `€${price.toFixed(2)}`;
  };

  if (items.length === 0) {
    return (
      <div className="text-center text-gray-500 py-8">
        <p>No items available at the moment.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {items.map((item) => (
        <div
          key={item.id}
          onClick={() => handleItemClick(item)}
          className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 cursor-pointer group"
        >
          {/* Item Image */}
          <div className="relative h-48 bg-gray-200">
            {item.image_url ? (
              <Image
                src={item.image_url}
                alt={item.name}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
            ) : (
              <div className="flex items-center justify-center h-full bg-gradient-to-br from-orange-100 to-orange-200">
                <svg
                  className="w-16 h-16 text-orange-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
            )}
            
            {/* Overlay on hover */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300" />
            
            {/* Availability Badge */}
            {!item.available && (
              <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                Unavailable
              </div>
            )}
          </div>

          {/* Item Info */}
          <div className="p-6">
            <div className="flex justify-between items-start mb-2">
              <h3 className="text-lg font-semibold text-gray-900 group-hover:text-orange-600 transition-colors flex-1">
                {item.name}
              </h3>
              <span className="text-lg font-bold text-orange-600 ml-2">
                {formatPrice(item.price)}
              </span>
            </div>
            
            {item.description && (
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                {item.description}
              </p>
            )}
            
            {/* Category Badge */}
            {item.category && (
              <div className="mb-3">
                <span className="inline-block bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                  {item.category.name}
                </span>
              </div>
            )}
            
            {/* Allergens */}
            {item.allergens && item.allergens.length > 0 && (
              <div className="mb-3">
                <p className="text-xs text-gray-500 mb-1">Contains:</p>
                <div className="flex flex-wrap gap-1">
                  {item.allergens.map((allergen, index) => (
                    <span
                      key={index}
                      className="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded"
                    >
                      {allergen}
                    </span>
                  ))}
                </div>
              </div>
            )}
            
            {/* Add to Cart Button */}
            <button
              className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                item.available
                  ? 'bg-orange-600 text-white hover:bg-orange-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
              disabled={!item.available}
            >
              {item.available ? 'Add to Cart' : 'Unavailable'}
            </button>
          </div>
        </div>
      ))}
    </div>
  );
}