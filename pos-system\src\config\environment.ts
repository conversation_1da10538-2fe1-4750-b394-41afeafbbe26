// Environment configuration service for POS system
import { getSupabaseClient } from '../shared/supabase-config';

// Window interface for electron API
interface WindowWithElectronAPI {
  electronAPI?: any;
}

export interface EnvironmentConfig {
  NODE_ENV: string;
  ADMIN_DASHBOARD_URL: string;
  ADMIN_API_BASE_URL: string;
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;
  PAYMENT_MODE: 'test' | 'production';
  PAYMENT_TEST_CARDS_ENABLED: boolean;
  DEBUG_LOGGING: boolean;
}

// Get environment variable with fallback
function getEnvVar(key: string, fallback: string = ''): string {
  // Check process.env first (main process)
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key] || fallback;
  }
  
  // Check window environment (renderer process)
  if (typeof window !== 'undefined') {
    const windowWithElectron = window as WindowWithElectronAPI & Window;
    if (windowWithElectron.electronAPI && windowWithElectron.electronAPI.getEnv) {
      return windowWithElectron.electronAPI.getEnv(key) || fallback;
    }
  }
  
  return fallback;
}

// Environment configuration
export const environment: EnvironmentConfig = {
  NODE_ENV: getEnvVar('NODE_ENV', 'development'),
  ADMIN_DASHBOARD_URL: getEnvVar('ADMIN_DASHBOARD_URL', 'http://localhost:3001'),
  ADMIN_API_BASE_URL: getEnvVar('ADMIN_API_BASE_URL', 'http://localhost:3001/api'),
  SUPABASE_URL: getEnvVar('SUPABASE_URL', 'https://localhost:54321'),
  SUPABASE_ANON_KEY: getEnvVar('SUPABASE_ANON_KEY', 'your-anon-key'),
  PAYMENT_MODE: getEnvVar('PAYMENT_MODE', 'test') as 'test' | 'production',
  PAYMENT_TEST_CARDS_ENABLED: getEnvVar('PAYMENT_TEST_CARDS_ENABLED', 'true') === 'true',
  DEBUG_LOGGING: getEnvVar('DEBUG_LOGGING', 'true') === 'true'
};

// Utility functions
export const isDevelopment = () => environment.NODE_ENV === 'development';
export const isProduction = () => environment.NODE_ENV === 'production';

// API endpoint builders
export const getApiUrl = (endpoint: string) => {
  const baseUrl = environment.ADMIN_API_BASE_URL.replace(/\/+$/, '');
  const cleanEndpoint = endpoint.replace(/^\/+/, '');
  return `${baseUrl}/${cleanEndpoint}`;
};

export const getDashboardUrl = (path: string = '') => {
  const baseUrl = environment.ADMIN_DASHBOARD_URL.replace(/\/+$/, '');
  const cleanPath = path.replace(/^\/+/, '');
  return cleanPath ? `${baseUrl}/${cleanPath}` : baseUrl;
};

// Export for debugging
if (isDevelopment()) {
  // Environment configuration logging removed
}