'use client'

import React, { useEffect, useRef } from 'react'
import { useTheme } from '@/contexts/theme-context'

export function GlobalScrollbar() {
  const { isDarkTheme } = useTheme()
  const styleRef = useRef<HTMLStyleElement | null>(null)

  useEffect(() => {
    // Safe cleanup of existing style
    if (styleRef.current && styleRef.current.parentNode) {
      styleRef.current.parentNode.removeChild(styleRef.current)
      styleRef.current = null
    }

    // Create new style element with safety checks
    if (typeof document !== 'undefined' && document.head) {
      const style = document.createElement('style')
      style.id = 'global-scrollbar-styles'
      styleRef.current = style
    
    const scrollbarStyles = isDarkTheme ? `
      /* Dark theme global scrollbar */
      * {
        scrollbar-width: thin;
        scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
      }

      *::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      *::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      *::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, 
          rgba(255, 255, 255, 0.3) 0%, 
          rgba(255, 255, 255, 0.2) 50%, 
          rgba(255, 255, 255, 0.1) 100%);
        border-radius: 10px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        box-shadow: 
          0 2px 8px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.4);
        transition: all 0.3s ease;
      }

      *::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, 
          rgba(255, 255, 255, 0.4) 0%, 
          rgba(255, 255, 255, 0.3) 50%, 
          rgba(255, 255, 255, 0.2) 100%);
        box-shadow: 
          0 4px 12px rgba(0, 0, 0, 0.15),
          inset 0 1px 0 rgba(255, 255, 255, 0.5);
        transform: scale(1.05);
      }

      *::-webkit-scrollbar-thumb:active {
        background: linear-gradient(135deg, 
          rgba(255, 255, 255, 0.5) 0%, 
          rgba(255, 255, 255, 0.4) 50%, 
          rgba(255, 255, 255, 0.3) 100%);
      }

      *::-webkit-scrollbar-corner {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
      }
    ` : `
      /* Light theme global scrollbar */
      * {
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
      }

      *::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      *::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 10px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(0, 0, 0, 0.1);
      }

      *::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, 
          rgba(0, 0, 0, 0.3) 0%, 
          rgba(0, 0, 0, 0.2) 50%, 
          rgba(0, 0, 0, 0.1) 100%);
        border-radius: 10px;
        border: 1px solid rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(10px);
        box-shadow: 
          0 2px 8px rgba(255, 255, 255, 0.1),
          inset 0 1px 0 rgba(0, 0, 0, 0.4);
        transition: all 0.3s ease;
      }

      *::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, 
          rgba(0, 0, 0, 0.4) 0%, 
          rgba(0, 0, 0, 0.3) 50%, 
          rgba(0, 0, 0, 0.2) 100%);
        box-shadow: 
          0 4px 12px rgba(255, 255, 255, 0.15),
          inset 0 1px 0 rgba(0, 0, 0, 0.5);
        transform: scale(1.05);
      }

      *::-webkit-scrollbar-thumb:active {
        background: linear-gradient(135deg, 
          rgba(0, 0, 0, 0.5) 0%, 
          rgba(0, 0, 0, 0.4) 50%, 
          rgba(0, 0, 0, 0.3) 100%);
      }

      *::-webkit-scrollbar-corner {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 10px;
      }
    `

      style.textContent = scrollbarStyles
      
      // Safe append with error handling
      try {
        document.head.appendChild(style)
      } catch (error) {
        console.warn('Failed to append scrollbar styles:', error)
        styleRef.current = null
      }
    }

    // Cleanup function with safety checks
    return () => {
      if (styleRef.current && styleRef.current.parentNode) {
        try {
          styleRef.current.parentNode.removeChild(styleRef.current)
        } catch (error) {
          console.warn('Failed to remove scrollbar styles:', error)
        }
        styleRef.current = null
      }
    }
  }, [isDarkTheme])

  return null // This component doesn't render anything
}