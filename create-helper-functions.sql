-- Create only the essential helper functions for the existing database
-- This script works with the current multilingual table structure

-- Function to check current environment mode
CREATE OR REPLACE FUNCTION get_menu_environment_mode()
RETURNS text AS $$
BEGIN
  RETURN COALESCE(current_setting('app.environment', true), 'development');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to toggle development mode
CREATE OR REPLACE FUNCTION toggle_menu_dev_mode(enable boolean)
RETURNS void AS $$
BEGIN
  IF enable THEN
    PERFORM set_config('app.environment', 'development', false);
    RAISE NOTICE 'Menu development mode enabled - RLS policies are permissive';
  ELSE
    PERFORM set_config('app.environment', 'production', false);
    RAISE NOTICE 'Menu production mode enabled - RLS policies are restrictive';
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get menu categories with items (adapted for multilingual schema)
CREATE OR REPLACE FUNCTION get_menu_categories_with_items(lang text DEFAULT 'en')
RETURNS TABLE (
  category_id uuid,
  category_name text,
  category_description text,
  display_order integer,
  is_active boolean,
  item_count bigint
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    mc.id,
    CASE 
      WHEN lang = 'el' THEN mc.name_el 
      ELSE mc.name_en 
    END as category_name,
    CASE 
      WHEN lang = 'el' THEN mc.description_el 
      ELSE mc.description_en 
    END as category_description,
    mc.display_order,
    mc.is_active,
    COUNT(mi.id) as item_count
  FROM menu_categories mc
  LEFT JOIN subcategories mi ON mc.id = mi.category_id
  GROUP BY mc.id, mc.name_en, mc.name_el, mc.description_en, mc.description_el, mc.display_order, mc.is_active
  ORDER BY mc.display_order;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get ingredients by category (adapted for multilingual schema)
CREATE OR REPLACE FUNCTION get_ingredients_by_category(lang text DEFAULT 'en')
RETURNS TABLE (
  ingredient_id uuid,
  ingredient_name text,
  category_name text,
  stock_quantity numeric,
  unit text,
  is_active boolean
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    i.id,
    CASE 
      WHEN lang = 'el' THEN i.name_el 
      ELSE i.name_en 
    END as ingredient_name,
    ic.name as category_name,
    i.stock_quantity,
    i.unit,
    i.is_active
  FROM ingredients i
  LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
  WHERE i.is_active = true
  ORDER BY ic.name, i.name_en;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to all roles
GRANT EXECUTE ON FUNCTION get_menu_environment_mode() TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION toggle_menu_dev_mode(boolean) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION get_menu_categories_with_items(text) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION get_ingredients_by_category(text) TO authenticated, anon, service_role;

-- Set development environment by default
SELECT set_config('app.environment', 'development', false);