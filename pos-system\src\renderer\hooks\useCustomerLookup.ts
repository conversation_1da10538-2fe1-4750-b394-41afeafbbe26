import { useState, useCallback } from 'react';
import toast from 'react-hot-toast';
import { Customer, CustomerSearchHistory } from '../types/customer';

interface CustomerLookupState {
  phoneNumber: string;
  isLookingUp: boolean;
  foundCustomer: Customer | null;
  lookupHistory: Customer[];
  recentSearches: string[];
}

const initialState: CustomerLookupState = {
  phoneNumber: '',
  isLookingUp: false,
  foundCustomer: null,
  lookupHistory: [],
  recentSearches: [],
};

export const useCustomerLookup = () => {
  const [state, setState] = useState<CustomerLookupState>(initialState);

  // Update phone number
  const updatePhoneNumber = useCallback((phone: string) => {
    setState(prev => ({ ...prev, phoneNumber: phone }));
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setState(prev => ({ 
      ...prev, 
      phoneNumber: '', 
      foundCustomer: null 
    }));
  }, []);

  // Add to recent searches
  const addToRecentSearches = useCallback((phone: string) => {
    setState(prev => ({
      ...prev,
      recentSearches: [
        phone,
        ...prev.recentSearches.filter(p => p !== phone)
      ].slice(0, 5) // Keep only last 5 searches
    }));
  }, []);

  // Lookup typedCustomer by phone
  const lookupCustomer = useCallback(async (phone?: string) => {
    const searchPhone = phone || state.phoneNumber;
    
    if (!searchPhone.trim()) {
      toast.error("Please enter a phone number");
      return null;
    }

    setState(prev => ({ ...prev, isLookingUp: true }));
    
    try {
      // TODO: Replace with actual customer service
      const customer: Customer | null = null; // await customerService.lookupByPhone(searchPhone);
      
      // Type guard to prevent never type issues
      const typedCustomer = customer as Customer | null;
      
      setState(prev => {
        const baseState = {
          ...prev,
          isLookingUp: false,
          foundCustomer: typedCustomer,
        };

        if (typedCustomer && typedCustomer.id) {
          return {
            ...baseState,
            lookupHistory: [
              typedCustomer,
              ...prev.lookupHistory.filter(c => c.id !== typedCustomer.id)
            ].slice(0, 10) // Keep only last 10 typedCustomers
          };
        }

        return baseState;
      });

      if (typedCustomer && typedCustomer.name) {
        addToRecentSearches(searchPhone);
        toast.success(`Found typedCustomer: ${typedCustomer.name}`);
        return typedCustomer;
      } else {
        addToRecentSearches(searchPhone);
        toast.success("Customer not found - creating new");
        return null;
      }
    } catch (error) {
      console.error('Customer lookup failed:', error);
      setState(prev => ({
        ...prev,
        isLookingUp: false,
        foundCustomer: null
      }));
      
      toast.error("Customer lookup failed");
      return null;
    }
  }, [state.phoneNumber, addToRecentSearches]);

  // Quick lookup from recent searches
  const quickLookup = useCallback(async (phone: string) => {
    setState(prev => ({ ...prev, phoneNumber: phone }));
    return await lookupCustomer(phone);
  }, [lookupCustomer]);

  // Get typedCustomer from history
  const getFromHistory = useCallback((typedCustomerId: string) => {
    const typedCustomer = state.lookupHistory.find(c => c.id === typedCustomerId);
    if (typedCustomer) {
      setState(prev => ({ 
        ...prev, 
        foundCustomer: typedCustomer,
        phoneNumber: typedCustomer.phone || ''
      }));
      return typedCustomer;
    }
    return null;
  }, [state.lookupHistory]);

  // Reset state
  const reset = useCallback(() => {
    setState(initialState);
  }, []);

  return {
    // State
    phoneNumber: state.phoneNumber,
    isLookingUp: state.isLookingUp,
    foundCustomer: state.foundCustomer,
    lookupHistory: state.lookupHistory,
    recentSearches: state.recentSearches,
    
    // Actions
    updatePhoneNumber,
    lookupCustomer,
    quickLookup,
    clearSearch,
    getFromHistory,
    reset,
    
    // Computed
    hasFoundCustomer: !!state.foundCustomer,
    hasHistory: state.lookupHistory.length > 0,
    hasRecentSearches: state.recentSearches.length > 0,
  };
};