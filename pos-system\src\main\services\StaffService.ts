import Database from 'better-sqlite3';
import { BaseService } from './BaseService';
import * as crypto from 'crypto';

// Database row interfaces
interface StaffSessionRow {
  id: string;
  staff_id: string;
  pin_hash: string;
  role: 'admin' | 'staff';
  login_time: string;
  logout_time?: string;
  is_active: boolean;
}

interface StaffFilter {
  staffId?: string;
  role?: string;
  active?: boolean;
}

export interface StaffSession {
  id: string;
  staff_id: string;
  pin_hash: string;
  role: 'admin' | 'staff';
  login_time: string;
  logout_time?: string;
  is_active: boolean;
}

export interface StaffInfo {
  id: string;
  name: string;
  role: string;
  permissions: string[];
}

export class StaffService extends BaseService {
  constructor(database: Database.Database) {
    super(database);
  }

  createSession(staffId: string, pin: string, role: 'admin' | 'staff'): StaffSession {
    return this.executeTransaction(() => {
      this.validateRequired({ staffId, pin, role }, ['staffId', 'pin', 'role']);

      // Hash the PIN for security
      const pinHash = this.hashPin(pin);

      // End any existing active sessions for this staff member
      this.endActiveSession(staffId);

      const session: StaffSession = {
        id: this.generateId(),
        staff_id: staffId,
        pin_hash: pinHash,
        role,
        login_time: this.getCurrentTimestamp(),
        is_active: true
      };

      const stmt = this.db.prepare(`
        INSERT INTO staff_sessions (
          id, staff_id, pin_hash, role, login_time, is_active
        ) VALUES (?, ?, ?, ?, ?, ?)
      `);

      stmt.run(
        session.id, session.staff_id, session.pin_hash,
        session.role, session.login_time, session.is_active
      );

      return session;
    });
  }

  validatePin(staffId: string, pin: string): StaffSession | null {
    const pinHash = this.hashPin(pin);
    
    const stmt = this.db.prepare(`
      SELECT * FROM staff_sessions 
      WHERE staff_id = ? AND pin_hash = ? AND is_active = 1
      ORDER BY login_time DESC
      LIMIT 1
    `);

    const row = stmt.get(staffId, pinHash) as StaffSessionRow | undefined;
    
    if (!row) return null;
    
    return this.mapRowToSession(row);
  }

  getActiveSession(staffId: string): StaffSession | null {
    const stmt = this.db.prepare(`
      SELECT * FROM staff_sessions 
      WHERE staff_id = ? AND is_active = 1
      ORDER BY login_time DESC
      LIMIT 1
    `);

    const row = stmt.get(staffId) as StaffSessionRow | undefined;
    
    if (!row) return null;
    
    return this.mapRowToSession(row);
  }

  getAllActiveSessions(): StaffSession[] {
    const stmt = this.db.prepare(`
      SELECT * FROM staff_sessions 
      WHERE is_active = 1
      ORDER BY login_time DESC
    `);

    const rows = stmt.all() as StaffSessionRow[];
    
    return rows.map(row => this.mapRowToSession(row));
  }

  endSession(sessionId: string): boolean {
    return this.executeTransaction(() => {
      const stmt = this.db.prepare(`
        UPDATE staff_sessions SET 
          is_active = 0, 
          logout_time = ?
        WHERE id = ?
      `);
      
      const result = stmt.run(this.getCurrentTimestamp(), sessionId);
      return result.changes > 0;
    });
  }

  endActiveSession(staffId: string): boolean {
    return this.executeTransaction(() => {
      const stmt = this.db.prepare(`
        UPDATE staff_sessions SET 
          is_active = 0, 
          logout_time = ?
        WHERE staff_id = ? AND is_active = 1
      `);
      
      const result = stmt.run(this.getCurrentTimestamp(), staffId);
      return result.changes > 0;
    });
  }

  endAllActiveSessions(): number {
    return this.executeTransaction(() => {
      const stmt = this.db.prepare(`
        UPDATE staff_sessions SET 
          is_active = 0, 
          logout_time = ?
        WHERE is_active = 1
      `);
      
      const result = stmt.run(this.getCurrentTimestamp());
      return result.changes;
    });
  }

  getSessionHistory(staffId?: string, limit: number = 50): StaffSession[] {
    let query = 'SELECT * FROM staff_sessions';
    const params: (string | number | boolean)[] = [];

    if (staffId) {
      query += ' WHERE staff_id = ?';
      params.push(staffId);
    }

    query += ' ORDER BY login_time DESC LIMIT ?';
    params.push(limit);

    const stmt = this.db.prepare(query);
    const rows = stmt.all(...params) as StaffSessionRow[];
    
    return rows.map(row => this.mapRowToSession(row));
  }

  isStaffLoggedIn(staffId: string): boolean {
    const session = this.getActiveSession(staffId);
    return session !== null;
  }

  getStaffRole(staffId: string): 'admin' | 'staff' | null {
    const session = this.getActiveSession(staffId);
    return session?.role || null;
  }

  hasPermission(staffId: string, permission: string): boolean {
    const role = this.getStaffRole(staffId);
    
    if (role === 'admin') {
      return true; // Admins have all permissions
    }

    // For staff members, check specific permissions
    // This could be extended to check against a permissions table
    const staffPermissions = this.getStaffPermissions(staffId);
    return staffPermissions.includes(permission);
  }

  private getStaffPermissions(staffId: string): string[] {
    // This is a simplified implementation
    // In a real application, you would query a permissions table
    const role = this.getStaffRole(staffId);
    
    if (role === 'admin') {
      return [
        'process_orders',
        'refund_orders',
        'void_transactions',
        'access_reports',
        'manage_staff',
        'modify_prices',
        'open_cash_drawer'
      ];
    }

    return [
      'process_orders',
      'open_cash_drawer'
    ];
  }

  private hashPin(pin: string): string {
    return crypto.createHash('sha256').update(pin).digest('hex');
  }

  private mapRowToSession(row: StaffSessionRow): StaffSession {
    return {
      id: row.id,
      staff_id: row.staff_id,
      pin_hash: row.pin_hash,
      role: row.role,
      login_time: row.login_time,
      logout_time: row.logout_time,
      is_active: Boolean(row.is_active)
    };
  }

  // Cleanup old sessions (older than 30 days)
  cleanupOldSessions(): number {
    return this.executeTransaction(() => {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const cutoffDate = thirtyDaysAgo.toISOString();

      const stmt = this.db.prepare(`
        DELETE FROM staff_sessions 
        WHERE is_active = 0 AND login_time < ?
      `);
      
      const result = stmt.run(cutoffDate);
      return result.changes;
    });
  }
}