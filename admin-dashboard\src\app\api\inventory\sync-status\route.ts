import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    const terminalId = searchParams.get('terminal_id')
    const category = searchParams.get('category')
    const syncStatus = searchParams.get('sync_status')
    const search = searchParams.get('search')
    const limit = parseInt(searchParams.get('limit') || '100')

    // Get ingredients with their sync status
    let query = supabase
      .from('ingredients')
      .select(`
        id,
        name,
        stock_quantity,
        min_stock_level,
        is_available,
        category_id,
        updated_at,
        created_at
      `)
      .order('name')
      .limit(limit)

    if (category && category !== 'all') {
      query = query.eq('category', category)
    }

    if (search) {
      query = query.ilike('name', `%${search}%`)
    }

    const { data: ingredients, error: ingredientsError } = await query

    if (ingredientsError) {
      console.error('Error fetching ingredients:', ingredientsError)
      return NextResponse.json(
        { error: 'Failed to fetch ingredients' },
        { status: 500 }
      )
    }

    // Get sync history for each ingredient
    const enrichedItems = await Promise.all(
      (ingredients || []).map(async (ingredient) => {
        // Get latest sync status for this ingredient
        let syncHistory = null
        if (terminalId && terminalId !== 'all') {
          const { data: history } = await supabase
            .from('pos_settings_sync_history')
            .select('*')
            .eq('terminal_id', terminalId)
            .eq('sync_type', 'inventory')
            .contains('metadata', { ingredient_id: ingredient.id })
            .order('created_at', { ascending: false })
            .limit(1)
            .single()
          
          syncHistory = history
        }

        // Calculate stock status
        const isLowStock = ingredient.stock_quantity > 0 && 
                          ingredient.min_stock_level && 
                          ingredient.stock_quantity <= ingredient.min_stock_level
        const isOutOfStock = ingredient.stock_quantity === 0

        // Determine sync status
        let itemSyncStatus = 'synced'
        let lastSyncAt = null
        let syncError = null

        if (syncHistory) {
          itemSyncStatus = syncHistory.sync_status
          lastSyncAt = syncHistory.synced_at || syncHistory.created_at
          syncError = syncHistory.error_message
        } else {
          // If no sync history, consider it as needing sync
          itemSyncStatus = 'pending'
        }

        // Get terminal-specific status if specified
        let terminalStatus = null
        if (terminalId && terminalId !== 'all') {
          const { data: terminal } = await supabase
            .from('pos_terminals')
            .select('name, status, last_heartbeat')
            .eq('terminal_id', terminalId)
            .single()
          
          terminalStatus = terminal
        }

        return {
          id: ingredient.id,
          name: ingredient.name,
          category: ingredient.category_id || 'uncategorized',
          stock_level: ingredient.stock_quantity,
          min_stock_level: ingredient.min_stock_level || 0,
          is_available: ingredient.is_available,
          stock_status: {
            is_low_stock: isLowStock,
            is_out_of_stock: isOutOfStock,
            status: isOutOfStock ? 'out_of_stock' : isLowStock ? 'low_stock' : 'in_stock'
          },
          sync_status: itemSyncStatus,
          last_sync_at: lastSyncAt,
          sync_error: syncError,
          terminal_status: terminalStatus,
          updated_at: ingredient.updated_at,
          needs_sync: itemSyncStatus === 'pending' || itemSyncStatus === 'failed' || 
                     (ingredient.updated_at > (lastSyncAt || '1970-01-01'))
        }
      })
    )

    // Apply sync status filter
    let filteredItems = enrichedItems
    if (syncStatus && syncStatus !== 'all') {
      filteredItems = enrichedItems.filter(item => {
        switch (syncStatus) {
          case 'synced':
            return item.sync_status === 'success' && !item.needs_sync
          case 'pending':
            return item.sync_status === 'pending' || item.needs_sync
          case 'failed':
            return item.sync_status === 'failed'
          case 'conflicts':
            return item.sync_status === 'conflict'
          default:
            return true
        }
      })
    }

    // Calculate summary statistics
    const totalItems = filteredItems.length
    const syncedItems = filteredItems.filter(item => item.sync_status === 'success' && !item.needs_sync).length
    const pendingItems = filteredItems.filter(item => item.sync_status === 'pending' || item.needs_sync).length
    const failedItems = filteredItems.filter(item => item.sync_status === 'failed').length
    const conflictItems = filteredItems.filter(item => item.sync_status === 'conflict').length
    const lowStockItems = filteredItems.filter(item => item.stock_status.is_low_stock).length
    const outOfStockItems = filteredItems.filter(item => item.stock_status.is_out_of_stock).length

    return NextResponse.json({
      items: filteredItems,
      summary: {
        total_items: totalItems,
        synced: syncedItems,
        pending: pendingItems,
        failed: failedItems,
        conflicts: conflictItems,
        low_stock: lowStockItems,
        out_of_stock: outOfStockItems,
        sync_success_rate: totalItems > 0 ? (syncedItems / totalItems) * 100 : 0
      },
      filters: {
        terminal_id: terminalId,
        category,
        sync_status: syncStatus,
        search,
        limit
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Inventory sync status error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
