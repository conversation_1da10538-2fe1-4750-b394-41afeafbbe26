'use client'

import React, { useState, useEffect } from 'react'
import { 
  Plus, 
  Search, 
  MapPin, 
  Phone, 
  Mail, 
  Clock, 
  Settings,
  Users,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Building2,
  Filter,
  Download
} from 'lucide-react'
import { supabase } from '@/lib/supabase'
import toast from 'react-hot-toast'
import AddBranchModal from '@/components/branches/AddBranchModal'
import BranchDetailsModal from '@/components/branches/BranchDetailsModal'
import EditBranchModal from '@/components/branches/EditBranchModal'

interface Branch {
  id: string
  name: string
  code: string
  display_name: string
  description: string
  address_line1: string
  address_line2?: string
  city: string
  state: string
  postal_code: string
  country: string
  phone?: string
  email?: string
  website?: string
  manager_id?: string
  status: 'active' | 'inactive' | 'temporarily_closed' | 'coming_soon' | 'permanently_closed'
  is_active: boolean
  has_delivery: boolean
  has_pickup: boolean
  has_dine_in: boolean
  has_drive_through: boolean
  seating_capacity: number
  parking_spaces: number
  delivery_radius: number
  delivery_fee: number
  minimum_order_amount: number
  timezone: string
  created_at: string
  updated_at: string
}

const statusColors = {
  active: 'bg-green-100 text-green-800',
  inactive: 'bg-gray-100 text-gray-800',
  temporarily_closed: 'bg-yellow-100 text-yellow-800',
  coming_soon: 'bg-blue-100 text-blue-800',
  permanently_closed: 'bg-red-100 text-red-800'
}

const statusLabels = {
  active: 'Active',
  inactive: 'Inactive',
  temporarily_closed: 'Temporarily Closed',
  coming_soon: 'Coming Soon',
  permanently_closed: 'Permanently Closed'
}

export default function BranchesPage() {
  const [branches, setBranches] = useState<Branch[]>([])
  const [filteredBranches, setFilteredBranches] = useState<Branch[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [showDetailsModal, setShowDetailsModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)

  useEffect(() => {
    loadBranches()
  }, [])

  useEffect(() => {
    filterBranches()
  }, [branches, searchTerm, statusFilter])

  const loadBranches = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('branches')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setBranches(data || [])
    } catch (error) {
      console.error('Error loading branches:', error)
      toast.error('Failed to load branches')
    } finally {
      setLoading(false)
    }
  }

  const filterBranches = () => {
    let filtered = branches

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(branch =>
        branch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.state.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(branch => branch.status === statusFilter)
    }

    setFilteredBranches(filtered)
  }

  const handleViewDetails = (branch: Branch) => {
    setSelectedBranch(branch)
    setShowDetailsModal(true)
    setActiveDropdown(null)
  }

  const handleEditBranch = (branch: Branch) => {
    setSelectedBranch(branch)
    setShowEditModal(true)
    setActiveDropdown(null)
  }

  const handleDeleteBranch = async (branchId: string) => {
    if (!confirm('Are you sure you want to delete this branch? This action cannot be undone.')) {
      return
    }

    try {
      const { error } = await supabase
        .from('branches')
        .delete()
        .eq('id', branchId)

      if (error) throw error

      toast.success('Branch deleted successfully')
      loadBranches()
    } catch (error) {
      console.error('Error deleting branch:', error)
      toast.error('Failed to delete branch')
    }
    setActiveDropdown(null)
  }

  const handleBranchAdded = () => {
    loadBranches()
    setShowAddModal(false)
  }

  const handleBranchUpdated = () => {
    loadBranches()
    setShowEditModal(false)
  }

  const getServiceIcons = (branch: Branch) => {
    const services = []
    if (branch.has_delivery) services.push({ icon: '🚚', label: 'Delivery' })
    if (branch.has_pickup) services.push({ icon: '🛍️', label: 'Pickup' })
    if (branch.has_dine_in) services.push({ icon: '🍽️', label: 'Dine-in' })
    return services
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading branches...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Building2 className="h-8 w-8 text-blue-600" />
                Branch Management
              </h1>
              <p className="mt-2 text-gray-600">
                Manage restaurant locations, operating hours, and configurations
              </p>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <Plus className="h-5 w-5" />
              Add Branch
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search branches by name, code, or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Status Filter */}
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="temporarily_closed">Temporarily Closed</option>
                <option value="coming_soon">Coming Soon</option>
                <option value="permanently_closed">Permanently Closed</option>
              </select>
            </div>

            {/* Export Button */}
            <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center gap-2 transition-colors">
              <Download className="h-5 w-5 text-gray-500" />
              Export
            </button>
          </div>
        </div>        {/* Results Summary */}
        <div className="flex justify-between items-center mb-6">
          <p className="text-gray-600">
            Showing {filteredBranches.length} of {branches.length} branches
          </p>
        </div>

        {/* Branches Grid */}
        {filteredBranches.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border p-12 text-center">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No branches found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your search or filter criteria.'
                : 'Get started by adding your first branch location.'}
            </p>
            {!searchTerm && statusFilter === 'all' && (
              <button
                onClick={() => setShowAddModal(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg inline-flex items-center gap-2 transition-colors"
              >
                <Plus className="h-5 w-5" />
                Add First Branch
              </button>
            )}
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredBranches.map((branch) => (
              <div key={branch.id} className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
                <div className="p-6">
                  {/* Header */}
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {branch.display_name || branch.name}
                      </h3>
                      <p className="text-sm text-gray-500 font-mono">#{branch.code}</p>
                    </div>
                    <div className="relative">
                      <button
                        onClick={() => setActiveDropdown(activeDropdown === branch.id ? null : branch.id)}
                        className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                      >
                        <MoreVertical className="h-5 w-5 text-gray-500" />
                      </button>
                      
                      {activeDropdown === branch.id && (
                        <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-10">
                          <button
                            onClick={() => handleViewDetails(branch)}
                            className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center gap-2 text-sm"
                          >
                            <Eye className="h-4 w-4" />
                            View Details
                          </button>
                          <button
                            onClick={() => handleEditBranch(branch)}
                            className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center gap-2 text-sm"
                          >
                            <Edit className="h-4 w-4" />
                            Edit Branch
                          </button>
                          <button
                            onClick={() => handleDeleteBranch(branch.id)}
                            className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center gap-2 text-sm text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                            Delete Branch
                          </button>
                        </div>
                      )}
                    </div>
                  </div>                  {/* Status */}
                  <div className="mb-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[branch.status]}`}>
                      {statusLabels[branch.status]}
                    </span>
                  </div>

                  {/* Location */}
                  <div className="mb-4">
                    <div className="flex items-start gap-2 text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mt-0.5 flex-shrink-0" />
                      <div>
                        <p>{branch.address_line1}</p>
                        {branch.address_line2 && <p>{branch.address_line2}</p>}
                        <p>{branch.city}, {branch.state} {branch.postal_code}</p>
                      </div>
                    </div>
                  </div>

                  {/* Contact */}
                  <div className="mb-4 space-y-2">
                    {branch.phone && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Phone className="h-4 w-4" />
                        <span>{branch.phone}</span>
                      </div>
                    )}
                    {branch.email && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Mail className="h-4 w-4" />
                        <span>{branch.email}</span>
                      </div>
                    )}
                  </div>

                  {/* Services */}
                  <div className="mb-4">
                    <div className="flex items-center gap-2 flex-wrap">
                      {getServiceIcons(branch).map((service, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 rounded text-xs"
                          title={service.label}
                        >
                          <span>{service.icon}</span>
                          <span className="text-gray-600">{service.label}</span>
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="border-t pt-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Capacity:</span>
                        <span className="ml-1 font-medium">{branch.seating_capacity} seats</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Delivery:</span>
                        <span className="ml-1 font-medium">${branch.delivery_fee}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>      {/* Modals */}
      {showAddModal && (
        <AddBranchModal
          onClose={() => setShowAddModal(false)}
          onBranchAdded={handleBranchAdded}
        />
      )}

      {showDetailsModal && selectedBranch && (
        <BranchDetailsModal
          branch={selectedBranch}
          onClose={() => setShowDetailsModal(false)}
          onEdit={() => {
            setShowDetailsModal(false)
            setShowEditModal(true)
          }}
        />
      )}

      {showEditModal && selectedBranch && (
        <EditBranchModal
          branch={selectedBranch}
          onClose={() => setShowEditModal(false)}
          onBranchUpdated={handleBranchUpdated}
        />
      )}

      {/* Click outside to close dropdown */}
      {activeDropdown && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setActiveDropdown(null)}
        />
      )}
    </div>
  )
}