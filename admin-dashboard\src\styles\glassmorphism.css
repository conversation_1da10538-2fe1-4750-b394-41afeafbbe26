/* ===== GLASSMORPHISM DESIGN SYSTEM ===== */

/* CSS Custom Properties for Consistent Theming */
:root {
  /* Glass Effect Variables */
  --glass-bg-primary: rgba(255, 255, 255, 0.15);
  --glass-bg-secondary: rgba(255, 255, 255, 0.1);
  --glass-bg-interactive: rgba(255, 255, 255, 0.12);
  --glass-bg-hover: rgba(255, 255, 255, 0.2);
  
  /* Border Variables */
  --glass-border-primary: rgba(255, 255, 255, 0.2);
  --glass-border-secondary: rgba(255, 255, 255, 0.15);
  --glass-border-interactive: rgba(255, 255, 255, 0.18);
  --glass-border-hover: rgba(255, 255, 255, 0.3);
  
  /* Shadow Variables */
  --glass-shadow-primary: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-shadow-secondary: 0 4px 16px 0 rgba(31, 38, 135, 0.25);
  --glass-shadow-interactive: 0 6px 20px 0 rgba(31, 38, 135, 0.4);
  
  /* Blur Variables */
  --glass-blur-primary: blur(10px);
  --glass-blur-secondary: blur(8px);
  --glass-blur-interactive: blur(6px);
  
  /* Border Radius Variables */
  --glass-radius-small: 6px;
  --glass-radius-medium: 8px;
  --glass-radius-large: 12px;
  --glass-radius-xl: 16px;
  
  /* Transition Variables */
  --glass-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --glass-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Text Color Variables */
  --glass-text-primary: rgba(0, 0, 0, 0.9);
  --glass-text-secondary: rgba(0, 0, 0, 0.7);
  --glass-text-muted: rgba(0, 0, 0, 0.5);
}

/* Dark mode adjustments */
.dark {
  --glass-bg-primary: rgba(0, 0, 0, 0.25);
  --glass-bg-secondary: rgba(0, 0, 0, 0.15);
  --glass-bg-interactive: rgba(0, 0, 0, 0.18);
  --glass-bg-hover: rgba(0, 0, 0, 0.3);
  
  --glass-border-primary: rgba(255, 255, 255, 0.1);
  --glass-border-secondary: rgba(255, 255, 255, 0.08);
  --glass-border-interactive: rgba(255, 255, 255, 0.12);
  --glass-border-hover: rgba(255, 255, 255, 0.2);
  
  --glass-text-primary: rgba(255, 255, 255, 0.9);
  --glass-text-secondary: rgba(255, 255, 255, 0.7);
  --glass-text-muted: rgba(255, 255, 255, 0.5);
}

/* Base Glass Container Classes */
.glass-container {
  position: relative;
  backdrop-filter: var(--glass-blur-primary);
  -webkit-backdrop-filter: var(--glass-blur-primary);
  border-radius: var(--glass-radius-large);
  transition: var(--glass-transition);
}

.glass-primary {
  background: var(--glass-bg-primary);
  border: 1px solid var(--glass-border-primary);
  box-shadow: var(--glass-shadow-primary);
}

.glass-secondary {
  background: var(--glass-bg-secondary);
  border: 1px solid var(--glass-border-secondary);
  box-shadow: var(--glass-shadow-secondary);
  backdrop-filter: var(--glass-blur-secondary);
  -webkit-backdrop-filter: var(--glass-blur-secondary);
  border-radius: var(--glass-radius-medium);
}

.glass-interactive {
  background: var(--glass-bg-interactive);
  border: 1px solid var(--glass-border-interactive);
  backdrop-filter: var(--glass-blur-interactive);
  -webkit-backdrop-filter: var(--glass-blur-interactive);
  border-radius: var(--glass-radius-small);
  cursor: pointer;
  transition: var(--glass-transition);
}

.glass-interactive:hover {
  background: var(--glass-bg-hover);
  border: 1px solid var(--glass-border-hover);
  box-shadow: var(--glass-shadow-interactive);
  transform: translateY(-2px);
}

.glass-interactive:active {
  transform: translateY(0);
  transition: var(--glass-transition-fast);
}

/* Specialized Glass Components */
.glass-card {
  @apply glass-container glass-primary;
  padding: 1.5rem;
  margin: 1rem 0;
}

.glass-button {
  @apply glass-interactive;
  padding: 0.75rem 1.5rem;
  border: none;
  font-weight: 600;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 44px; /* Accessibility: minimum touch target */
  color: var(--glass-text-primary);
  transition: var(--glass-transition);
}

.glass-input {
  @apply glass-interactive;
  padding: 0.75rem 1rem;
  border: 1px solid var(--glass-border-interactive);
  background: var(--glass-bg-interactive);
  color: var(--glass-text-primary);
  font-size: 1rem;
  width: 100%;
  transition: var(--glass-transition);
}

.glass-input:focus {
  outline: none;
  border: 2px solid var(--glass-border-hover);
  background: var(--glass-bg-hover);
  box-shadow: var(--glass-shadow-interactive);
}

.glass-input::placeholder {
  color: var(--glass-text-muted);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-select {
  @apply glass-interactive;
  padding: 0.75rem 1rem;
  border: 1px solid var(--glass-border-interactive);
  background: var(--glass-bg-interactive);
  color: var(--glass-text-primary);
  font-size: 1rem;
  width: 100%;
  transition: var(--glass-transition);
  cursor: pointer;
}

.glass-select:focus {
  outline: none;
  border: 2px solid var(--glass-border-hover);
  background: var(--glass-bg-hover);
  box-shadow: var(--glass-shadow-interactive);
}

.glass-select option {
  background: var(--glass-bg-primary);
  color: var(--glass-text-primary);
  padding: 0.5rem;
}

.glass-checkbox {
  appearance: none;
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--glass-border-interactive);
  border-radius: var(--glass-radius-small);
  background: var(--glass-bg-interactive);
  backdrop-filter: var(--glass-blur-interactive);
  -webkit-backdrop-filter: var(--glass-blur-interactive);
  cursor: pointer;
  transition: var(--glass-transition);
  position: relative;
  flex-shrink: 0;
}

.glass-checkbox:checked {
  background: rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.5);
}

.glass-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--glass-text-primary);
  font-size: 0.875rem;
  font-weight: bold;
}

.glass-checkbox:focus {
  outline: none;
  border-color: var(--glass-border-hover);
  box-shadow: var(--glass-shadow-interactive);
}

.glass-checkbox:hover {
  border-color: var(--glass-border-hover);
  background: var(--glass-bg-hover);
}

/* Scrollbar styling for glassmorphism */
.scrollbar-glassmorphism {
  scrollbar-width: thin;
  scrollbar-color: var(--glass-border-interactive) transparent;
}

.scrollbar-glassmorphism::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-glassmorphism::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-glassmorphism::-webkit-scrollbar-thumb {
  background: var(--glass-bg-interactive);
  border-radius: var(--glass-radius-small);
  border: 1px solid var(--glass-border-interactive);
}

.scrollbar-glassmorphism::-webkit-scrollbar-thumb:hover {
  background: var(--glass-bg-hover);
}

.glass-modal {
  @apply glass-container glass-primary;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 90vw;
  max-height: 90vh;
  padding: 0;
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.glass-modal-header {
  padding: 2rem 2rem 1rem 2rem;
  flex-shrink: 0;
  border-bottom: 1px solid var(--glass-border-secondary);
}

.glass-modal-content {
  padding: 1rem 2rem;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  @apply scrollbar-glassmorphism;
  min-height: 0; /* Important for flex child to be scrollable */
}

.glass-modal-footer {
  @apply px-6 py-4 border-t border-white/10 bg-white/5 backdrop-blur-sm;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
  flex-shrink: 0;
}

.glass-modal-footer.light {
  @apply border-gray-200 bg-gray-50/50;
  border-top: 1px solid rgb(229, 231, 235);
}

.glass-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: 999;
}

/* Status-based Glass Variants */
.glass-success {
  border: 1px solid rgba(34, 197, 94, 0.3);
  background: rgba(34, 197, 94, 0.1);
}

.glass-warning {
  border: 1px solid rgba(251, 191, 36, 0.3);
  background: rgba(251, 191, 36, 0.1);
}

.glass-error {
  border: 1px solid rgba(239, 68, 68, 0.3);
  background: rgba(239, 68, 68, 0.1);
}

.glass-info {
  border: 1px solid rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.1);
}

/* Responsive Glass Effects */
@media (max-width: 768px) {
  :root {
    --glass-blur-primary: blur(6px);
    --glass-blur-secondary: blur(4px);
    --glass-blur-interactive: blur(3px);
  }
  
  .glass-card {
    padding: 1rem;
    margin: 0.5rem 0;
  }
  
  .glass-modal {
    padding: 1rem;
    max-width: 95vw;
    max-height: 95vh;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .glass-interactive {
    transition: none;
  }
  
  .glass-interactive:hover {
    transform: none;
  }
  
  /* Disable all floating animations */
  .floating-shape,
  .glass-login-container,
  .glass-float {
    animation: none !important;
  }
}

/* Text Color Utility Classes */
.glass-text-primary {
  color: var(--glass-text-primary);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-text-secondary {
  color: var(--glass-text-secondary);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-text-muted {
  color: var(--glass-text-muted);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --glass-border-primary: rgba(255, 255, 255, 0.8);
    --glass-border-secondary: rgba(255, 255, 255, 0.6);
    --glass-border-interactive: rgba(255, 255, 255, 0.7);
  }
}

/* Advanced Glass Effects */
.glass-glow {
  position: relative;
  overflow: hidden;
}

.glass-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.5s ease-in-out;
}

.glass-glow:hover::before {
  left: 100%;
}

/* Floating Animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-6px); }
}

.glass-float {
  animation: float 3s ease-in-out infinite;
}

.glass-float:nth-child(2n) {
  animation-delay: -1s;
}

.glass-float:nth-child(3n) {
  animation-delay: -2s;
}

/* Enhanced Hover States */
.glass-hover-lift {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.glass-hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    var(--glass-shadow-interactive),
    0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Pulse Effect */
@keyframes glass-pulse {
  0% { box-shadow: var(--glass-shadow-primary); }
  50% { box-shadow: var(--glass-shadow-interactive); }
  100% { box-shadow: var(--glass-shadow-primary); }
}

.glass-pulse {
  animation: glass-pulse 2s ease-in-out infinite;
}

/* Shimmer Effect */
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.glass-shimmer {
  background: linear-gradient(
    90deg,
    var(--glass-bg-primary) 25%,
    var(--glass-bg-hover) 50%,
    var(--glass-bg-primary) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Login Page Specific Glass Enhancements */
.glass-login-enhanced {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-input-enhanced {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-input-enhanced:focus {
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
  outline: none;
}

.glass-input-enhanced::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.glass-button-enhanced {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-button-enhanced:hover {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.glass-error-enhanced {
  background: rgba(239, 68, 68, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Mobile-specific optimizations for login */
@media (max-width: 768px) {
  .glass-login-enhanced {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }
  
  .glass-input-enhanced {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    padding: 1rem;
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .glass-button-enhanced {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    min-height: 48px; /* Better touch target */
  }
}

/* High contrast mode support for login */
@media (prefers-contrast: high) {
  .glass-login-enhanced,
  .glass-input-enhanced,
  .glass-button-enhanced {
    border-width: 2px;
    background: rgba(255, 255, 255, 0.2);
  }
  
  .glass-input-enhanced::placeholder {
    color: rgba(255, 255, 255, 0.8);
  }
}