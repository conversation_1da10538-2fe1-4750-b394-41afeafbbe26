/**
 * Authentication Security Module
 * Implements comprehensive authentication security measures including:
 * - Password requirements and validation
 * - Session management and security
 * - JWT token security
 * - Brute force protection
 * - Multi-factor authentication support
 */

import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { encode as base32Encode, decode as base32Decode } from 'hi-base32';
import { createClient } from '@supabase/supabase-js';
import { SECURITY_CONFIG } from './security-config';
import { logSecurityEvent, logAuditEvent } from './security-middleware';

// Types
interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong' | 'very_strong';
}

interface SessionData {
  userId: string;
  email: string;
  role: string;
  platform: string;
  permissions: string[];
  createdAt: Date;
  lastActivity: Date;
  deviceFingerprint?: string;
  ipAddress?: string;
}

interface JWTPayload {
  sub: string; // user ID
  email: string;
  role: string;
  platform: string;
  permissions: string[];
  iat: number;
  exp: number;
  jti: string; // JWT ID for revocation
  deviceFingerprint?: string;
}

interface AuthAttempt {
  identifier: string;
  attemptType: 'login' | 'pin' | 'otp' | '2fa';
  platform: string;
  success: boolean;
  failureReason?: string;
  ipAddress?: string;
  userAgent?: string;
  deviceFingerprint?: string;
}

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * Password Security Manager
 */
export class PasswordSecurity {
  private static readonly SALT_ROUNDS = 12;
  private static readonly PASSWORD_HISTORY_COUNT = 5;

  /**
   * Validates password against security requirements
   */
  static validatePassword(password: string, userEmail?: string): PasswordValidationResult {
    const errors: string[] = [];
    const policy = SECURITY_CONFIG.VALIDATION.USER.PASSWORD;

    // Length check
    if (password.length < policy.minLength) {
      errors.push(`Password must be at least ${policy.minLength} characters long`);
    }

    if (password.length > policy.maxLength) {
      errors.push(`Password must not exceed ${policy.maxLength} characters`);
    }

    // Character requirements
    if (policy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (policy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (policy.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (policy.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    // Common password checks
    if (this.isCommonPassword(password)) {
      errors.push('Password is too common. Please choose a more unique password');
    }

    // Email similarity check
    if (userEmail && this.isSimilarToEmail(password, userEmail)) {
      errors.push('Password cannot be similar to your email address');
    }

    // Calculate strength
    const strength = this.calculatePasswordStrength(password);

    return {
      isValid: errors.length === 0,
      errors,
      strength
    };
  }

  /**
   * Hashes password securely
   */
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.SALT_ROUNDS);
  }

  /**
   * Verifies password against hash
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * Checks if password was used recently
   */
  static async checkPasswordHistory(userId: string, newPasswordHash: string): Promise<boolean> {
    try {
      const { data: history } = await supabase
        .from('password_history')
        .select('password_hash')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(this.PASSWORD_HISTORY_COUNT);

      if (!history) return false;

      for (const entry of history) {
        if (await bcrypt.compare(newPasswordHash, entry.password_hash)) {
          return true; // Password was used before
        }
      }

      return false;
    } catch (error) {
      console.error('Error checking password history:', error);
      return false;
    }
  }

  /**
   * Stores password in history
   */
  static async storePasswordHistory(userId: string, passwordHash: string): Promise<void> {
    try {
      // Add new password to history
      await supabase
        .from('password_history')
        .insert({
          user_id: userId,
          password_hash: passwordHash
        });

      // Clean up old entries
      const { data: oldEntries } = await supabase
        .from('password_history')
        .select('id')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(this.PASSWORD_HISTORY_COUNT, 100);

      if (oldEntries && oldEntries.length > 0) {
        const idsToDelete = oldEntries.map(entry => entry.id);
        await supabase
          .from('password_history')
          .delete()
          .in('id', idsToDelete);
      }
    } catch (error) {
      console.error('Error storing password history:', error);
    }
  }

  private static isCommonPassword(password: string): boolean {
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey',
      'dragon', 'master', 'shadow', 'superman', 'michael'
    ];
    return commonPasswords.includes(password.toLowerCase());
  }

  private static isSimilarToEmail(password: string, email: string): boolean {
    const emailParts = email.toLowerCase().split('@')[0];
    const passwordLower = password.toLowerCase();
    return passwordLower.includes(emailParts) || emailParts.includes(passwordLower);
  }

  private static calculatePasswordStrength(password: string): 'weak' | 'medium' | 'strong' | 'very_strong' {
    let score = 0;

    // Length scoring
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    if (password.length >= 16) score += 1;

    // Character variety
    if (/[a-z]/.test(password)) score += 1;
    if (/[A-Z]/.test(password)) score += 1;
    if (/\d/.test(password)) score += 1;
    if (/[^\w\s]/.test(password)) score += 1;

    // Patterns
    if (!/(..).*\1/.test(password)) score += 1; // No repeated pairs
    if (!/012|123|234|345|456|567|678|789|890|abc|bcd|cde/.test(password.toLowerCase())) score += 1;

    if (score <= 3) return 'weak';
    if (score <= 5) return 'medium';
    if (score <= 7) return 'strong';
    return 'very_strong';
  }
}

/**
 * Session Security Manager
 */
export class SessionSecurity {
  private static readonly SESSION_COOKIE_NAME = 'creperie_session';
  private static readonly REFRESH_TOKEN_NAME = 'creperie_refresh';

  /**
   * Creates a secure session
   */
  static async createSession(
    userId: string,
    email: string,
    role: string,
    platform: string,
    permissions: string[],
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ accessToken: string; refreshToken: string; sessionId: string }> {
    const sessionId = crypto.randomUUID();
    const deviceFingerprint = this.generateDeviceFingerprint(userAgent, ipAddress);

    // Create session data
    const sessionData: SessionData = {
      userId,
      email,
      role,
      platform,
      permissions,
      createdAt: new Date(),
      lastActivity: new Date(),
      deviceFingerprint,
      ipAddress
    };

    // Store session in database
    await supabase
      .from('user_sessions')
      .insert({
        id: sessionId,
        user_id: userId,
        platform,
        device_fingerprint: deviceFingerprint,
        ip_address: ipAddress,
        user_agent: userAgent,
        expires_at: new Date(Date.now() + this.getSessionDuration(platform))
      });

    // Generate tokens
    const accessToken = this.generateAccessToken({
      sub: userId,
      email,
      role,
      platform,
      permissions,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor((Date.now() + this.getAccessTokenDuration()) / 1000),
      jti: sessionId,
      deviceFingerprint
    });

    const refreshToken = this.generateRefreshToken(sessionId, userId);

    // Log session creation
    await logSecurityEvent('session_created', 'low', {
      userId,
      sessionId,
      platform,
      ipAddress,
      userAgent
    });

    return { accessToken, refreshToken, sessionId };
  }

  /**
   * Validates and refreshes session
   */
  static async validateSession(token: string): Promise<SessionData | null> {
    try {
      const payload = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload;
      
      // Check if session exists and is valid
      const { data: session } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('id', payload.jti)
        .eq('user_id', payload.sub)
        .single();

      if (!session || new Date(session.expires_at) < new Date()) {
        return null;
      }

      // Update last activity
      await supabase
        .from('user_sessions')
        .update({ last_activity: new Date().toISOString() })
        .eq('id', payload.jti);

      return {
        userId: payload.sub,
        email: payload.email,
        role: payload.role,
        platform: payload.platform,
        permissions: payload.permissions,
        createdAt: new Date(session.created_at),
        lastActivity: new Date(),
        deviceFingerprint: payload.deviceFingerprint,
        ipAddress: session.ip_address
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Revokes a session
   */
  static async revokeSession(sessionId: string, userId: string): Promise<void> {
    await supabase
      .from('user_sessions')
      .delete()
      .eq('id', sessionId)
      .eq('user_id', userId);

    await logSecurityEvent('session_revoked', 'low', {
      sessionId,
      userId
    });
  }

  /**
   * Revokes all sessions for a user
   */
  static async revokeAllSessions(userId: string): Promise<void> {
    await supabase
      .from('user_sessions')
      .delete()
      .eq('user_id', userId);

    await logSecurityEvent('all_sessions_revoked', 'medium', {
      userId
    });
  }

  /**
   * Cleans up expired sessions
   */
  static async cleanupExpiredSessions(): Promise<void> {
    await supabase
      .from('user_sessions')
      .delete()
      .lt('expires_at', new Date().toISOString());
  }

  private static generateAccessToken(payload: JWTPayload): string {
    return jwt.sign(payload, process.env.JWT_SECRET!, {
      algorithm: 'HS256',
      issuer: 'creperie-system',
      audience: payload.platform
    });
  }

  private static generateRefreshToken(sessionId: string, userId: string): string {
    return jwt.sign(
      { sessionId, userId, type: 'refresh' },
      process.env.JWT_REFRESH_SECRET!,
      { expiresIn: '30d' }
    );
  }

  private static generateDeviceFingerprint(userAgent?: string, ipAddress?: string): string {
    const data = `${userAgent || 'unknown'}-${ipAddress || 'unknown'}`;
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  private static getSessionDuration(platform: string): number {
    const durations = {
      'admin-dashboard': 8 * 60 * 60 * 1000, // 8 hours
      'pos-system': 12 * 60 * 60 * 1000, // 12 hours
      'customer-web': 24 * 60 * 60 * 1000, // 24 hours
      'customer-mobile': 30 * 24 * 60 * 60 * 1000 // 30 days
    };
    return durations[platform as keyof typeof durations] || 24 * 60 * 60 * 1000;
  }

  private static getAccessTokenDuration(): number {
    return 15 * 60 * 1000; // 15 minutes
  }
}

/**
 * Brute Force Protection Manager
 */
export class BruteForceProtection {
  /**
   * Records an authentication attempt
   */
  static async recordAttempt(attempt: AuthAttempt): Promise<void> {
    try {
      // Log to database
      await supabase.rpc('log_auth_attempt', {
        p_identifier: attempt.identifier,
        p_attempt_type: attempt.attemptType,
        p_platform: attempt.platform,
        p_success: attempt.success,
        p_failure_reason: attempt.failureReason,
        p_ip_address: attempt.ipAddress,
        p_user_agent: attempt.userAgent
      });

      // Log security event for failed attempts
      if (!attempt.success) {
        await logSecurityEvent('auth_failure', 'medium', {
          identifier: attempt.identifier,
          attemptType: attempt.attemptType,
          platform: attempt.platform,
          reason: attempt.failureReason,
          ipAddress: attempt.ipAddress
        });
      }
    } catch (error) {
      console.error('Error recording auth attempt:', error);
    }
  }

  /**
   * Checks if identifier is locked due to too many failed attempts
   */
  static async isLocked(identifier: string, attemptType: string): Promise<{ locked: boolean; lockoutEnd?: Date }> {
    try {
      const config = SECURITY_CONFIG.RATE_LIMITS.AUTH.LOGIN;
      const windowStart = new Date(Date.now() - config.windowMs);

      // Count failed attempts in the window
      const { data: attempts } = await supabase
        .from('auth_attempts')
        .select('created_at')
        .eq('identifier', identifier)
        .eq('attempt_type', attemptType)
        .eq('success', false)
        .gte('created_at', windowStart.toISOString())
        .order('created_at', { ascending: false });

      if (!attempts || attempts.length < config.max) {
        return { locked: false };
      }

      // Calculate lockout end time
      const lastAttempt = new Date(attempts[0].created_at);
      const lockoutEnd = new Date(lastAttempt.getTime() + config.windowMs);

      if (new Date() < lockoutEnd) {
        return { locked: true, lockoutEnd };
      }

      return { locked: false };
    } catch (error) {
      console.error('Error checking lockout status:', error);
      return { locked: false };
    }
  }

  /**
   * Checks for suspicious patterns in authentication attempts
   */
  static async detectSuspiciousActivity(ipAddress: string): Promise<boolean> {
    try {
      const windowStart = new Date(Date.now() - 60 * 60 * 1000); // 1 hour

      // Check for multiple failed attempts from same IP
      const { data: ipAttempts } = await supabase
        .from('auth_attempts')
        .select('identifier')
        .eq('ip_address', ipAddress)
        .eq('success', false)
        .gte('created_at', windowStart.toISOString());

      if (ipAttempts && ipAttempts.length > 20) {
        await logSecurityEvent('suspicious_ip_activity', 'high', {
          ipAddress,
          attemptCount: ipAttempts.length,
          timeWindow: '1 hour'
        });
        return true;
      }

      // Check for attempts on multiple accounts from same IP
      const uniqueIdentifiers = new Set(ipAttempts?.map(a => a.identifier) || []);
      if (uniqueIdentifiers.size > 10) {
        await logSecurityEvent('ip_targeting_multiple_accounts', 'high', {
          ipAddress,
          targetCount: uniqueIdentifiers.size,
          timeWindow: '1 hour'
        });
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error detecting suspicious activity:', error);
      return false;
    }
  }
}

/**
 * Two-Factor Authentication Manager
 */
export class TwoFactorAuth {
  /**
   * Generates TOTP secret for user
   */
  static generateTOTPSecret(): { secret: string; qrCode: string } {
    const secret = base32Encode(crypto.randomBytes(20));
    const qrCode = `otpauth://totp/Creperie:user?secret=${secret}&issuer=Creperie`;
    return { secret, qrCode };
  }

  /**
   * Verifies TOTP token
   */
  static verifyTOTP(token: string, secret: string): boolean {
    // Implementation would use a TOTP library like 'otplib'
    // This is a simplified version
    const window = 30; // 30 second window
    const currentTime = Math.floor(Date.now() / 1000 / window);
    
    // Check current window and adjacent windows for clock drift
    for (let i = -1; i <= 1; i++) {
      const timeStep = currentTime + i;
      const expectedToken = this.generateTOTP(secret, timeStep);
      if (token === expectedToken) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * Generates backup codes for 2FA
   */
  static generateBackupCodes(count: number = 10): string[] {
    const codes: string[] = [];
    for (let i = 0; i < count; i++) {
      codes.push(crypto.randomBytes(4).toString('hex').toUpperCase());
    }
    return codes;
  }

  private static generateTOTP(secret: string, timeStep: number): string {
    // Simplified TOTP generation - use proper library in production
    const hmac = crypto.createHmac('sha1', Buffer.from(base32Decode.asBytes(secret)));
    const timeBuffer = Buffer.alloc(8);
    timeBuffer.writeUInt32BE(timeStep, 4);
    const hash = hmac.update(timeBuffer).digest();
    const offset = hash[hash.length - 1] & 0xf;
    const code = ((hash[offset] & 0x7f) << 24) |
                 ((hash[offset + 1] & 0xff) << 16) |
                 ((hash[offset + 2] & 0xff) << 8) |
                 (hash[offset + 3] & 0xff);
    return (code % 1000000).toString().padStart(6, '0');
  }
}

/**
 * JWT Security Utilities
 */
export class JWTSecurity {
  private static blacklistedTokens = new Set<string>();

  /**
   * Validates JWT token security
   */
  static validateTokenSecurity(token: string): boolean {
    try {
      // Check if token is blacklisted
      if (this.blacklistedTokens.has(token)) {
        return false;
      }

      // Verify token signature and expiration
      const payload = jwt.verify(token, process.env.JWT_SECRET!) as JWTPayload;
      
      // Additional security checks
      if (!payload.jti || !payload.sub || !payload.exp) {
        return false;
      }

      // Check if token is expired
      if (payload.exp < Math.floor(Date.now() / 1000)) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Blacklists a JWT token
   */
  static blacklistToken(token: string): void {
    this.blacklistedTokens.add(token);
    
    // In production, store in Redis or database
    // For now, we'll use in-memory storage
  }

  /**
   * Extracts payload from JWT without verification (for logging)
   */
  static extractPayload(token: string): Partial<JWTPayload> | null {
    try {
      return jwt.decode(token) as JWTPayload;
    } catch (error) {
      return null;
    }
  }
}

