'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

export type OrderType = 'pickup' | 'delivery';

interface OrderTypeContextType {
  orderType: OrderType;
  setOrderType: (type: OrderType) => void;
}

const OrderTypeContext = createContext<OrderTypeContextType | undefined>(undefined);

interface OrderTypeProviderProps {
  children: ReactNode;
}

export function OrderTypeProvider({ children }: OrderTypeProviderProps) {
  const [orderType, setOrderType] = useState<OrderType>('pickup');

  return (
    <OrderTypeContext.Provider value={{ orderType, setOrderType }}>
      {children}
    </OrderTypeContext.Provider>
  );
}

export function useOrderType() {
  const context = useContext(OrderTypeContext);
  if (context === undefined) {
    throw new Error('useOrderType must be used within an OrderTypeProvider');
  }
  return context;
}