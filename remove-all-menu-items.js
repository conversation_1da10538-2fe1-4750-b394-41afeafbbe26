const { createClient } = require('@supabase/supabase-js');

// Load environment variables from admin-dashboard
require('dotenv').config({ path: './admin-dashboard/.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.log('Required variables:');
  console.log('- NEXT_PUBLIC_SUPABASE_URL');
  console.log('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function removeAllMenuItems() {
  try {
    console.log('🔍 Checking for existing menu items...\n');

    // First, check how many menu items exist
    const { data: existingItems, error: countError } = await supabase
      .from('menu_items')
      .select('id, name_en, name_el, category_id')
      .order('created_at', { ascending: true });

    if (countError) {
      console.error('❌ Error checking menu items:', countError);
      return;
    }

    if (!existingItems || existingItems.length === 0) {
      console.log('✅ No menu items found in the database. Nothing to delete.');
      return;
    }

    console.log(`📋 Found ${existingItems.length} menu items to delete:\n`);
    
    existingItems.forEach((item, index) => {
      console.log(`${index + 1}. ${item.name_en || item.name_el || 'Unnamed'} (ID: ${item.id})`);
    });

    console.log('\n🗑️  Starting deletion process...\n');

    // Delete all menu items
    const { error: deleteError } = await supabase
      .from('menu_items')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // This condition will match all rows

    if (deleteError) {
      console.error('❌ Error deleting menu items:', deleteError);
      return;
    }

    console.log('✅ Successfully deleted all menu items from the database!');
    console.log('\n📝 Summary:');
    console.log(`   - Deleted: ${existingItems.length} menu items`);
    console.log('   - Status: Complete');
    
    console.log('\n💡 What this means:');
    console.log('   ✓ You can now delete any category without foreign key constraints');
    console.log('   ✓ The "Breakfast" category can be deleted successfully');
    console.log('   ✓ All categories, subcategories, and ingredients remain intact');
    
    console.log('\n🔄 Next steps:');
    console.log('   1. Refresh your admin dashboard');
    console.log('   2. Try deleting the "Breakfast" category again');
    console.log('   3. The deletion should now work without any errors');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the function
removeAllMenuItems();