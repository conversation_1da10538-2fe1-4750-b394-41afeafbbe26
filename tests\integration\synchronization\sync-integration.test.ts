/**
 * Integration Tests for Synchronization Features
 * Tests real synchronization across all platforms
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { SynchronizationTestFramework } from '../../synchronization/sync-test-framework';
import { SynchronizationTestScenarios } from '../../synchronization/test-scenarios';
import { DataConsistencyMonitor } from '../../synchronization/data-consistency-monitor';
import { PerformanceMonitor } from '../../synchronization/performance-monitor';

// Test configuration
const TEST_CONFIG = {
  supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://test.supabase.co',
  supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'test-key',
  platforms: [
    { id: 'admin', name: 'Admin Dashboard', type: 'admin' as const },
    { id: 'pos', name: 'POS System', type: 'pos' as const },
    { id: 'web', name: 'Customer Web', type: 'web' as const },
    { id: 'mobile', name: 'Customer Mobile', type: 'mobile' as const }
  ],
  testTimeout: 30000,
  syncTimeout: 10000
};

describe('Synchronization Integration Tests', () => {
  let framework: SynchronizationTestFramework;
  let scenarios: SynchronizationTestScenarios;
  let consistencyMonitor: DataConsistencyMonitor;
  let performanceMonitor: PerformanceMonitor;

  beforeAll(async () => {
    framework = new SynchronizationTestFramework(TEST_CONFIG);
    scenarios = new SynchronizationTestScenarios(TEST_CONFIG);
    consistencyMonitor = new DataConsistencyMonitor(TEST_CONFIG.supabaseUrl, TEST_CONFIG.supabaseKey);
    performanceMonitor = new PerformanceMonitor();

    await framework.initialize();
    await consistencyMonitor.startMonitoring();
    performanceMonitor.startMonitoring();
  });

  afterAll(async () => {
    await framework.cleanup();
    await consistencyMonitor.stopMonitoring();
    performanceMonitor.stopMonitoring();
  });

  beforeEach(() => {
    performanceMonitor.clearMetrics();
    consistencyMonitor.clearViolations();
  });

  describe('Settings Synchronization', () => {
    test('should sync tax rate changes from admin to POS', async () => {
      const endTiming = performanceMonitor.startTiming('tax-rate-sync');
      
      const testData = {
        id: 'test-tax-config',
        sales_tax_rate: 8.75,
        service_tax_rate: 5.0,
        updated_at: new Date().toISOString()
      };

      const result = await framework.testSettingsPropagation(
        'pos_configurations',
        testData,
        ['admin', 'pos']
      );

      endTiming('sync', 'pos', 'pos_configurations', 1, result.success);

      expect(result.success).toBe(true);
      expect(result.platforms).toHaveLength(2);
      expect(result.platforms.every(p => p.success)).toBe(true);
      expect(result.errors).toHaveLength(0);
    }, TEST_CONFIG.testTimeout);

    test('should sync payment settings across all platforms', async () => {
      const endTiming = performanceMonitor.startTiming('payment-settings-sync');
      
      const testData = {
        id: 'test-payment-config',
        accept_cash: true,
        accept_card: true,
        accept_mobile: true,
        minimum_card_amount: 5.00,
        updated_at: new Date().toISOString()
      };

      const result = await framework.testSettingsPropagation(
        'payment_settings',
        testData,
        ['admin', 'pos', 'web', 'mobile']
      );

      endTiming('sync', 'all', 'payment_settings', 1, result.success);

      expect(result.success).toBe(true);
      expect(result.platforms).toHaveLength(4);
      expect(result.dataConsistency.every(d => d.consistent)).toBe(true);
    }, TEST_CONFIG.testTimeout);

    test('should sync operating hours to customer platforms', async () => {
      const testData = {
        id: 'test-hours',
        monday_open: '08:00',
        monday_close: '22:00',
        tuesday_open: '08:00',
        tuesday_close: '22:00',
        updated_at: new Date().toISOString()
      };

      const result = await framework.testSettingsPropagation(
        'restaurant_settings',
        testData,
        ['admin', 'web', 'mobile']
      );

      expect(result.success).toBe(true);
      expect(result.platforms.filter(p => p.platformId !== 'pos')).toHaveLength(3);
    }, TEST_CONFIG.testTimeout);
  });

  describe('Menu Synchronization', () => {
    test('should sync menu item price changes across all platforms', async () => {
      const testData = {
        id: 'test-menu-item',
        name: 'Test Crepe',
        price: 12.99,
        is_available: true,
        updated_at: new Date().toISOString()
      };

      const result = await framework.testSettingsPropagation(
        'subcategories',
        testData,
        ['admin', 'pos', 'web', 'mobile']
      );

      expect(result.success).toBe(true);
      expect(result.platforms).toHaveLength(4);
      
      // Check that price is consistent across all platforms
      const priceConsistency = result.dataConsistency.find(d => d.field === 'price');
      expect(priceConsistency?.consistent).toBe(true);
    }, TEST_CONFIG.testTimeout);

    test('should sync menu item availability immediately', async () => {
      const testData = {
        id: 'test-seasonal-item',
        name: 'Seasonal Crepe',
        is_available: false,
        updated_at: new Date().toISOString()
      };

      const result = await framework.testSettingsPropagation(
        'subcategories',
        testData,
        ['admin', 'pos', 'web', 'mobile']
      );

      expect(result.success).toBe(true);
      
      // Availability should be synced within 3 seconds
      const avgSyncTime = result.platforms.reduce((sum, p) => sum + p.syncTime, 0) / result.platforms.length;
      expect(avgSyncTime).toBeLessThan(3000);
    }, TEST_CONFIG.testTimeout);
  });

  describe('Staff Management Synchronization', () => {
    test('should sync staff permission changes to POS immediately', async () => {
      const testData = {
        id: 'test-staff-member',
        can_process_refunds: false,
        can_modify_prices: false,
        can_access_reports: true,
        updated_at: new Date().toISOString()
      };

      const result = await framework.testSettingsPropagation(
        'staff_members',
        testData,
        ['admin', 'pos']
      );

      expect(result.success).toBe(true);
      expect(result.platforms.every(p => p.syncTime < 2000)).toBe(true);
    }, TEST_CONFIG.testTimeout);

    test('should sync staff PIN reset to POS system', async () => {
      const testData = {
        id: 'test-staff-pin',
        pin_hash: 'new-hashed-pin-value',
        updated_at: new Date().toISOString()
      };

      const result = await framework.testSettingsPropagation(
        'staff_members',
        testData,
        ['admin', 'pos']
      );

      expect(result.success).toBe(true);
      expect(result.errors).toHaveLength(0);
    }, TEST_CONFIG.testTimeout);
  });

  describe('System Configuration Synchronization', () => {
    test('should sync feature flag changes across platforms', async () => {
      const testData = {
        id: 'test-feature',
        name: 'loyalty_program',
        enabled: true,
        platforms: ['web', 'mobile'],
        updated_at: new Date().toISOString()
      };

      const result = await framework.testSettingsPropagation(
        'feature_flags',
        testData,
        ['admin', 'web', 'mobile']
      );

      expect(result.success).toBe(true);
      expect(result.platforms.filter(p => ['web', 'mobile'].includes(p.platformId))).toHaveLength(2);
    }, TEST_CONFIG.testTimeout);

    test('should sync maintenance mode activation', async () => {
      const testData = {
        id: 'test-maintenance',
        platform: 'web',
        is_active: true,
        message: 'System maintenance in progress',
        scheduled_start: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const result = await framework.testSettingsPropagation(
        'maintenance_modes',
        testData,
        ['admin', 'web']
      );

      expect(result.success).toBe(true);
      
      // Maintenance mode should be activated quickly
      const webPlatform = result.platforms.find(p => p.platformId === 'web');
      expect(webPlatform?.syncTime).toBeLessThan(1500);
    }, TEST_CONFIG.testTimeout);
  });

  describe('Error Handling and Rollback', () => {
    test('should handle invalid data gracefully', async () => {
      const invalidData = {
        id: 'test-invalid',
        sales_tax_rate: -5, // Invalid negative tax rate
        updated_at: new Date().toISOString()
      };

      const originalData = {
        id: 'test-invalid',
        sales_tax_rate: 8.0,
        updated_at: new Date().toISOString()
      };

      const result = await framework.testRollbackCapability(
        'pos_configurations',
        originalData,
        invalidData
      );

      // Should either reject the invalid data or rollback
      expect(result.success).toBe(true);
    }, TEST_CONFIG.testTimeout);

    test('should recover from temporary network failures', async () => {
      // This test would simulate network failures and recovery
      // For now, we'll test the framework's resilience
      
      const testData = {
        id: 'test-network-recovery',
        value: 'test-value',
        updated_at: new Date().toISOString()
      };

      const result = await framework.testSettingsPropagation(
        'system_configurations',
        testData,
        ['admin', 'pos']
      );

      // Should handle network issues gracefully
      expect(result).toBeDefined();
      expect(result.errors.length).toBeLessThanOrEqual(2); // Allow some tolerance
    }, TEST_CONFIG.testTimeout);
  });

  describe('Performance Testing', () => {
    test('should sync settings within performance thresholds', async () => {
      const performanceReport = await framework.runPerformanceTests();
      
      expect(performanceReport.averageSyncTime).toBeLessThan(3000);
      expect(performanceReport.bottlenecks.length).toBeLessThan(2);
      
      if (performanceReport.recommendations.length > 0) {
        console.log('Performance Recommendations:', performanceReport.recommendations);
      }
    }, TEST_CONFIG.testTimeout);

    test('should handle bulk operations efficiently', async () => {
      const bulkTestData = Array.from({ length: 10 }, (_, i) => ({
        id: `bulk-test-${i}`,
        name: `Bulk Item ${i}`,
        price: 10 + i,
        updated_at: new Date().toISOString()
      }));

      const startTime = Date.now();
      
      // Test bulk update simulation
      for (const item of bulkTestData) {
        await framework.testSettingsPropagation(
          'subcategories',
          item,
          ['admin', 'pos']
        );
      }
      
      const totalTime = Date.now() - startTime;
      const avgTimePerItem = totalTime / bulkTestData.length;
      
      // Should process bulk operations efficiently
      expect(avgTimePerItem).toBeLessThan(2000);
    }, TEST_CONFIG.testTimeout * 2);
  });

  describe('Data Consistency Monitoring', () => {
    test('should detect data inconsistencies', async () => {
      // Add a consistency rule
      consistencyMonitor.addRule({
        id: 'test-consistency-rule',
        name: 'Test Consistency Rule',
        description: 'Test rule for consistency monitoring',
        table: 'subcategories',
        fields: ['price', 'is_available'],
        platforms: ['admin', 'pos', 'web', 'mobile'],
        criticalLevel: 'high',
        enabled: true
      });

      // Perform consistency check
      await consistencyMonitor.performConsistencyCheck();

      const violations = consistencyMonitor.getViolations();
      
      // Should be able to detect and report violations
      expect(violations).toBeDefined();
      expect(Array.isArray(violations)).toBe(true);
    }, TEST_CONFIG.testTimeout);

    test('should generate comprehensive consistency report', async () => {
      const report = await consistencyMonitor.generateReport();
      
      expect(report).toBeDefined();
      expect(report.generatedAt).toBeInstanceOf(Date);
      expect(report.totalRules).toBeGreaterThanOrEqual(0);
      expect(report.platformStatus).toBeDefined();
      expect(Array.isArray(report.violations)).toBe(true);
      expect(Array.isArray(report.recommendations)).toBe(true);
    }, TEST_CONFIG.testTimeout);
  });

  describe('Real-time Performance Monitoring', () => {
    test('should track performance metrics in real-time', async () => {
      // Record some test metrics
      performanceMonitor.recordMetric('sync', 'pos', 'subcategories', 1500, 1, true);
      performanceMonitor.recordMetric('sync', 'web', 'subcategories', 2000, 1, true);
      performanceMonitor.recordMetric('sync', 'mobile', 'subcategories', 2500, 1, false, 'Network timeout');

      const stats = performanceMonitor.getRealTimeStats();
      
      expect(stats.recentOperations).toBeGreaterThan(0);
      expect(stats.averageResponseTime).toBeGreaterThan(0);
      expect(stats.successRate).toBeLessThanOrEqual(100);
    });

    test('should generate performance report with recommendations', async () => {
      // Generate some test data
      for (let i = 0; i < 10; i++) {
        performanceMonitor.recordMetric(
          'sync',
          'pos',
          'test_table',
          Math.random() * 3000 + 1000,
          1,
          Math.random() > 0.1 // 90% success rate
        );
      }

      const report = performanceMonitor.generateReport();
      
      expect(report.totalOperations).toBeGreaterThan(0);
      expect(report.averageResponseTime).toBeGreaterThan(0);
      expect(report.successRate).toBeGreaterThanOrEqual(0);
      expect(Array.isArray(report.benchmarks)).toBe(true);
      expect(Array.isArray(report.recommendations)).toBe(true);
    });
  });

  describe('End-to-End Scenario Testing', () => {
    test('should execute high priority scenarios successfully', async () => {
      const results = await scenarios.executeHighPriorityScenarios();
      
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeGreaterThan(0);
      
      // At least 80% of high priority scenarios should pass
      const successRate = results.filter(r => r.success).length / results.length;
      expect(successRate).toBeGreaterThanOrEqual(0.8);
    }, TEST_CONFIG.testTimeout * 3);

    test('should handle concurrent updates correctly', async () => {
      // Simulate concurrent updates from different platforms
      const promises = [
        framework.testSettingsPropagation(
          'pos_configurations',
          { id: 'concurrent-test', value: 'value1', updated_at: new Date().toISOString() },
          ['admin', 'pos']
        ),
        framework.testSettingsPropagation(
          'pos_configurations',
          { id: 'concurrent-test', value: 'value2', updated_at: new Date().toISOString() },
          ['admin', 'pos']
        )
      ];

      const results = await Promise.allSettled(promises);
      
      // Should handle concurrent updates gracefully
      expect(results.length).toBe(2);
      
      // At least one should succeed, or both should handle conflict properly
      const successfulResults = results.filter(r => r.status === 'fulfilled' && r.value.success);
      expect(successfulResults.length).toBeGreaterThanOrEqual(1);
    }, TEST_CONFIG.testTimeout);
  });
});