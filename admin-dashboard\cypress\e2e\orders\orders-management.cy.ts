describe('Orders Management', () => {
  beforeEach(() => {
    cy.loginAsAdmin();
    cy.visit('/orders');
    cy.waitForPageLoad();
  });

  describe('Orders List Page', () => {
    it('should display orders list with correct columns', () => {
      cy.get('[data-testid="orders-table"]').should('be.visible');
      
      // Check table headers
      const expectedHeaders = ['Order ID', 'Customer', 'Items', 'Total', 'Status', 'Date', 'Actions'];
      expectedHeaders.forEach((header) => {
        cy.get('[data-testid="orders-table"]')
          .find('thead')
          .should('contain.text', header);
      });
      
      // Check that orders are displayed
      cy.get('[data-testid="order-row"]').should('have.length.at.least', 1);
    });

    it('should display order details correctly', () => {
      cy.get('[data-testid="order-row"]').first().within(() => {
        // Check order ID format
        cy.get('[data-testid="order-id"]')
          .should('be.visible')
          .and('match', /^ORD-\d+$/);
        
        // Check customer name
        cy.get('[data-testid="customer-name"]')
          .should('be.visible')
          .and('not.be.empty');
        
        // Check total amount format
        cy.get('[data-testid="order-total"]')
          .should('be.visible')
          .and('match', /^\$\d+\.\d{2}$/);
        
        // Check status badge
        cy.get('[data-testid="order-status"]')
          .should('be.visible')
          .and('have.class', 'status-badge');
        
        // Check date format
        cy.get('[data-testid="order-date"]')
          .should('be.visible')
          .and('not.be.empty');
      });
    });

    it('should filter orders by status', () => {
      // Test pending filter
      cy.get('[data-testid="status-filter"]').select('pending');
      cy.waitForApi('@getOrders');
      
      cy.get('[data-testid="order-row"]').each(($row) => {
        cy.wrap($row)
          .find('[data-testid="order-status"]')
          .should('contain.text', 'Pending');
      });
      
      // Test completed filter
      cy.get('[data-testid="status-filter"]').select('completed');
      cy.waitForApi('@getOrders');
      
      cy.get('[data-testid="order-row"]').each(($row) => {
        cy.wrap($row)
          .find('[data-testid="order-status"]')
          .should('contain.text', 'Completed');
      });
      
      // Reset filter
      cy.get('[data-testid="status-filter"]').select('all');
      cy.waitForApi('@getOrders');
    });

    it('should search orders by customer name', () => {
      const searchTerm = 'John';
      
      cy.get('[data-testid="search-input"]')
        .type(searchTerm);
      
      cy.get('[data-testid="search-button"]').click();
      cy.waitForApi('@getOrders');
      
      // Check that results contain search term
      cy.get('[data-testid="order-row"]').each(($row) => {
        cy.wrap($row)
          .find('[data-testid="customer-name"]')
          .should('contain.text', searchTerm);
      });
      
      // Clear search
      cy.get('[data-testid="search-clear"]').click();
      cy.waitForApi('@getOrders');
    });

    it('should handle pagination correctly', () => {
      // Mock orders with pagination
      cy.intercept('GET', '/api/orders*', {
        statusCode: 200,
        body: {
          data: Array.from({ length: 10 }, (_, i) => ({
            id: `order-${i + 1}`,
            customer_name: `Customer ${i + 1}`,
            total: (Math.random() * 50 + 10).toFixed(2),
            status: ['pending', 'completed', 'cancelled'][i % 3],
            created_at: new Date().toISOString()
          })),
          pagination: {
            page: 1,
            limit: 10,
            total: 25,
            totalPages: 3
          }
        }
      }).as('getOrdersWithPagination');
      
      cy.reload();
      cy.wait('@getOrdersWithPagination');
      
      // Test pagination
      cy.testPagination(3);
    });

    it('should sort orders by different columns', () => {
      // Sort by date (newest first)
      cy.get('[data-testid="sort-date"]').click();
      cy.waitForApi('@getOrders');
      
      // Verify sorting (check that first order is newer than last)
      cy.get('[data-testid="order-row"]').first()
        .find('[data-testid="order-date"]')
        .invoke('text')
        .then((firstDate) => {
          cy.get('[data-testid="order-row"]').last()
            .find('[data-testid="order-date"]')
            .invoke('text')
            .then((lastDate) => {
              expect(new Date(firstDate).getTime())
                .to.be.greaterThan(new Date(lastDate).getTime());
            });
        });
      
      // Sort by total (highest first)
      cy.get('[data-testid="sort-total"]').click();
      cy.waitForApi('@getOrders');
    });
  });

  describe('Order Details Modal', () => {
    it('should open order details modal', () => {
      cy.get('[data-testid="order-row"]').first()
        .find('[data-testid="view-order-button"]')
        .click();
      
      cy.get('[data-testid="order-details-modal"]').should('be.visible');
      cy.get('[data-testid="modal-backdrop"]').should('be.visible');
    });

    it('should display complete order information', () => {
      cy.get('[data-testid="order-row"]').first()
        .find('[data-testid="view-order-button"]')
        .click();
      
      cy.get('[data-testid="order-details-modal"]').within(() => {
        // Check order header
        cy.get('[data-testid="order-id"]').should('be.visible');
        cy.get('[data-testid="order-status"]').should('be.visible');
        cy.get('[data-testid="order-date"]').should('be.visible');
        
        // Check customer information
        cy.get('[data-testid="customer-info"]').should('be.visible');
        cy.get('[data-testid="customer-name"]').should('be.visible');
        cy.get('[data-testid="customer-email"]').should('be.visible');
        cy.get('[data-testid="customer-phone"]').should('be.visible');
        
        // Check order items
        cy.get('[data-testid="order-items"]').should('be.visible');
        cy.get('[data-testid="order-item"]').should('have.length.at.least', 1);
        
        // Check order summary
        cy.get('[data-testid="order-subtotal"]').should('be.visible');
        cy.get('[data-testid="order-tax"]').should('be.visible');
        cy.get('[data-testid="order-total"]').should('be.visible');
        
        // Check payment information
        cy.get('[data-testid="payment-method"]').should('be.visible');
        cy.get('[data-testid="payment-status"]').should('be.visible');
      });
    });

    it('should close modal with close button', () => {
      cy.openModal('[data-testid="view-order-button"]');
      cy.closeModal();
    });

    it('should close modal with backdrop click', () => {
      cy.openModal('[data-testid="view-order-button"]');
      cy.get('[data-testid="modal-backdrop"]').click({ force: true });
      cy.get('[data-testid="order-details-modal"]').should('not.exist');
    });

    it('should close modal with escape key', () => {
      cy.openModal('[data-testid="view-order-button"]');
      cy.get('body').type('{esc}');
      cy.get('[data-testid="order-details-modal"]').should('not.exist');
    });
  });

  describe('Order Status Management', () => {
    it('should update order status', () => {
      // Mock successful status update
      cy.intercept('PUT', '/api/orders/*', {
        statusCode: 200,
        body: {
          id: 'order-1',
          status: 'completed',
          updated_at: new Date().toISOString()
        }
      }).as('updateOrderStatus');
      
      cy.get('[data-testid="order-row"]').first().within(() => {
        cy.get('[data-testid="status-dropdown"]').select('completed');
      });
      
      cy.wait('@updateOrderStatus');
      
      // Check success notification
      cy.checkNotification('Order status updated successfully');
      
      // Verify status change in UI
      cy.get('[data-testid="order-row"]').first()
        .find('[data-testid="order-status"]')
        .should('contain.text', 'Completed');
    });

    it('should handle status update errors', () => {
      // Mock failed status update
      cy.intercept('PUT', '/api/orders/*', {
        statusCode: 500,
        body: { error: 'Failed to update order status' }
      }).as('failedStatusUpdate');
      
      cy.get('[data-testid="order-row"]').first().within(() => {
        cy.get('[data-testid="status-dropdown"]').select('cancelled');
      });
      
      cy.wait('@failedStatusUpdate');
      
      // Check error notification
      cy.checkNotification('Failed to update order status', 'error');
    });

    it('should confirm status change for critical actions', () => {
      cy.get('[data-testid="order-row"]').first().within(() => {
        cy.get('[data-testid="status-dropdown"]').select('cancelled');
      });
      
      // Check confirmation dialog
      cy.get('[data-testid="confirmation-dialog"]').should('be.visible');
      cy.get('[data-testid="confirmation-message"]')
        .should('contain.text', 'Are you sure you want to cancel this order?');
      
      // Confirm action
      cy.get('[data-testid="confirm-button"]').click();
      
      // Check that status was updated
      cy.checkNotification('Order cancelled successfully');
    });
  });

  describe('Bulk Operations', () => {
    it('should select multiple orders', () => {
      // Select first three orders
      cy.get('[data-testid="order-checkbox"]')
        .slice(0, 3)
        .check();
      
      // Check that bulk actions are enabled
      cy.get('[data-testid="bulk-actions"]').should('be.visible');
      cy.get('[data-testid="selected-count"]')
        .should('contain.text', '3 orders selected');
    });

    it('should select all orders', () => {
      cy.get('[data-testid="select-all-checkbox"]').check();
      
      // Check that all visible orders are selected
      cy.get('[data-testid="order-checkbox"]').should('be.checked');
      cy.get('[data-testid="bulk-actions"]').should('be.visible');
    });

    it('should perform bulk status update', () => {
      // Mock bulk update
      cy.intercept('PUT', '/api/orders/bulk', {
        statusCode: 200,
        body: { updated: 3, message: 'Orders updated successfully' }
      }).as('bulkUpdate');
      
      // Select orders
      cy.get('[data-testid="order-checkbox"]')
        .slice(0, 3)
        .check();
      
      // Perform bulk action
      cy.get('[data-testid="bulk-status-select"]').select('completed');
      cy.get('[data-testid="bulk-update-button"]').click();
      
      // Confirm action
      cy.get('[data-testid="confirm-button"]').click();
      
      cy.wait('@bulkUpdate');
      
      // Check success notification
      cy.checkNotification('3 orders updated successfully');
    });
  });

  describe('Export Functionality', () => {
    it('should export orders to CSV', () => {
      cy.get('[data-testid="export-button"]').click();
      cy.get('[data-testid="export-csv"]').click();
      
      // Check that download was triggered
      cy.readFile('cypress/downloads/orders.csv').should('exist');
    });

    it('should export filtered orders', () => {
      // Apply filter
      cy.get('[data-testid="status-filter"]').select('pending');
      cy.waitForApi('@getOrders');
      
      // Export filtered data
      cy.get('[data-testid="export-button"]').click();
      cy.get('[data-testid="export-csv"]').click();
      
      // Verify export includes filter parameters
      cy.readFile('cypress/downloads/orders-pending.csv').should('exist');
    });
  });

  describe('Real-time Updates', () => {
    it('should update orders list when new order is received', () => {
      // Simulate real-time update
      cy.window().then((win) => {
        win.dispatchEvent(new CustomEvent('orderUpdate', {
          detail: {
            type: 'new',
            order: {
              id: 'order-new',
              customer_name: 'New Customer',
              total: 25.99,
              status: 'pending',
              created_at: new Date().toISOString()
            }
          }
        }));
      });
      
      // Check that new order appears in list
      cy.get('[data-testid="order-row"]')
        .should('contain.text', 'New Customer');
      
      // Check notification
      cy.checkNotification('New order received');
    });

    it('should update order status in real-time', () => {
      // Simulate status update from another source
      cy.window().then((win) => {
        win.dispatchEvent(new CustomEvent('orderUpdate', {
          detail: {
            type: 'update',
            order: {
              id: 'order-1',
              status: 'completed'
            }
          }
        }));
      });
      
      // Check that status is updated in UI
      cy.get('[data-testid="order-row"]')
        .contains('order-1')
        .parent()
        .find('[data-testid="order-status"]')
        .should('contain.text', 'Completed');
    });
  });

  describe('Performance and Loading States', () => {
    it('should show loading state while fetching orders', () => {
      // Mock delayed response
      cy.intercept('GET', '/api/orders*', (req) => {
        req.reply((res) => {
          res.delay(2000);
          res.send({ fixture: 'orders/orders.json' });
        });
      }).as('delayedOrders');
      
      cy.reload();
      
      // Check loading state
      cy.get('[data-testid="orders-loading"]').should('be.visible');
      cy.get('[data-testid="loading-spinner"]').should('be.visible');
      
      cy.wait('@delayedOrders');
      
      // Check that loading state is removed
      cy.get('[data-testid="orders-loading"]').should('not.exist');
      cy.get('[data-testid="orders-table"]').should('be.visible');
    });

    it('should handle empty state', () => {
      // Mock empty response
      cy.intercept('GET', '/api/orders*', {
        statusCode: 200,
        body: {
          data: [],
          pagination: { page: 1, limit: 10, total: 0, totalPages: 0 }
        }
      }).as('emptyOrders');
      
      cy.reload();
      cy.wait('@emptyOrders');
      
      // Check empty state
      cy.get('[data-testid="empty-state"]').should('be.visible');
      cy.get('[data-testid="empty-message"]')
        .should('contain.text', 'No orders found');
      cy.get('[data-testid="empty-illustration"]').should('be.visible');
    });

    it('should handle error state', () => {
      // Mock error response
      cy.intercept('GET', '/api/orders*', {
        statusCode: 500,
        body: { error: 'Internal server error' }
      }).as('ordersError');
      
      cy.reload();
      cy.wait('@ordersError');
      
      // Check error state
      cy.get('[data-testid="error-state"]').should('be.visible');
      cy.get('[data-testid="error-message"]')
        .should('contain.text', 'Failed to load orders');
      cy.get('[data-testid="retry-button"]').should('be.visible');
    });
  });

  describe('Accessibility', () => {
    it('should be accessible', () => {
      cy.checkA11y();
    });

    it('should support keyboard navigation', () => {
      // Test table navigation
      cy.get('[data-testid="orders-table"]').focus();
      
      // Navigate through table rows
      cy.get('body').type('{downarrow}');
      cy.focused().should('have.attr', 'data-testid', 'order-row');
      
      // Test action buttons
      cy.get('body').type('{tab}');
      cy.focused().should('have.attr', 'data-testid', 'view-order-button');
      
      // Test modal keyboard navigation
      cy.get('body').type('{enter}');
      cy.get('[data-testid="order-details-modal"]').should('be.visible');
      
      // Close modal with escape
      cy.get('body').type('{esc}');
      cy.get('[data-testid="order-details-modal"]').should('not.exist');
    });
  });

  describe('Responsive Design', () => {
    it('should be responsive across different screen sizes', () => {
      cy.testResponsive();
    });

    it('should adapt table layout for mobile', () => {
      cy.viewport(375, 667);
      
      // Check that table switches to card layout on mobile
      cy.get('[data-testid="orders-mobile-view"]').should('be.visible');
      cy.get('[data-testid="orders-table"]').should('not.be.visible');
      
      // Check that cards contain essential information
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="order-id"]').should('be.visible');
        cy.get('[data-testid="customer-name"]').should('be.visible');
        cy.get('[data-testid="order-total"]').should('be.visible');
        cy.get('[data-testid="order-status"]').should('be.visible');
      });
    });
  });
});