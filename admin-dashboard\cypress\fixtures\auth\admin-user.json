{"user": {"id": "admin-001", "email": "<EMAIL>", "password": "AdminTest123!", "role": "admin", "permissions": ["orders:read", "orders:write", "orders:delete", "menu:read", "menu:write", "menu:delete", "staff:read", "staff:write", "staff:delete", "analytics:read", "inventory:read", "inventory:write", "settings:read", "settings:write"], "profile": {"first_name": "Admin", "last_name": "User", "phone": "******-0001", "avatar_url": null, "created_at": "2024-01-01T00:00:00.000Z", "updated_at": "2024-01-15T10:00:00.000Z", "last_login": "2024-01-15T09:00:00.000Z", "is_active": true, "email_verified": true, "two_factor_enabled": false}, "session": {"access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************.test-token", "refresh_token": "refresh-token-admin-001", "expires_at": "2024-01-16T09:00:00.000Z", "token_type": "Bearer"}}, "loginCredentials": {"valid": {"email": "<EMAIL>", "password": "AdminTest123!"}, "invalid": {"wrongPassword": {"email": "<EMAIL>", "password": "wrongpassword"}, "wrongEmail": {"email": "<EMAIL>", "password": "AdminTest123!"}, "nonAdmin": {"email": "<EMAIL>", "password": "CustomerPass123!"}, "empty": {"email": "", "password": ""}, "invalidEmail": {"email": "invalid-email", "password": "AdminTest123!"}}}, "authResponses": {"success": {"user": {"id": "admin-001", "email": "<EMAIL>", "role": "admin", "permissions": ["orders:read", "orders:write", "orders:delete", "menu:read", "menu:write", "menu:delete", "staff:read", "staff:write", "staff:delete", "analytics:read", "inventory:read", "inventory:write", "settings:read", "settings:write"]}, "session": {"access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************.test-token", "refresh_token": "refresh-token-admin-001", "expires_at": "2024-01-16T09:00:00.000Z", "token_type": "Bearer"}}, "invalidCredentials": {"error": {"message": "Invalid email or password", "code": "INVALID_CREDENTIALS", "status": 401}}, "nonAdminUser": {"error": {"message": "Access denied. Admin privileges required.", "code": "INSUFFICIENT_PERMISSIONS", "status": 403}}, "accountLocked": {"error": {"message": "Account temporarily locked due to multiple failed login attempts", "code": "ACCOUNT_LOCKED", "status": 423, "retry_after": 900}}, "serverError": {"error": {"message": "Internal server error", "code": "INTERNAL_ERROR", "status": 500}}}}