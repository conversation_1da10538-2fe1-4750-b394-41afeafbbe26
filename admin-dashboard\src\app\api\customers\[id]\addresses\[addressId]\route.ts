import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';

// PUT endpoint to update a specific address
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; addressId: string }> }
) {
  try {
    const { id: customerId, addressId } = await params;
    const body = await request.json();
    
    
    const {
      address,
      postal_code,
      floor_number,
      notes,
      address_type = 'delivery',
      is_default = false
    } = body;

    if (!address) {
      return NextResponse.json(
        { success: false, error: 'Address is required' },
        { status: 400 }
      );
    }

    // Parse the address to extract street and city
    const addressParts = address.trim().split(',');
    const streetAddress = addressParts[0]?.trim() || address.trim();
    
    // Extract city more intelligently
    let city = 'Athens'; // default
    if (addressParts.length > 1) {
      const cityPart = addressParts[addressParts.length - 1]?.trim() || '';
      const cityWithoutPostal = cityPart.replace(/\d+/g, '').trim();
      city = cityWithoutPostal || 'Athens';
    }


    const supabase = createServerSupabaseClient();

    // Update the address
    const { data: updatedAddress, error: updateError } = await supabase
      .from('customer_addresses')
      .update({
        street_address: streetAddress,
        city: city,
        postal_code: postal_code?.trim() || null,
        floor_number: floor_number?.trim() || null,
        notes: notes?.trim() || null,
        address_type,
        is_default,
        updated_at: new Date().toISOString()
      })
      .eq('id', addressId)
      .eq('customer_id', customerId) // Security: ensure address belongs to customer
      .select()
      .single();

    if (updateError) {
      return NextResponse.json(
        { success: false, error: `Failed to update address: ${updateError.message}` },
        { status: 500 }
      );
    }


    // If this is set as default, update other addresses to not be default
    if (is_default) {
      await supabase
        .from('customer_addresses')
        .update({ is_default: false })
        .eq('customer_id', customerId)
        .neq('id', addressId);
    }

    return NextResponse.json({
      success: true,
      address: updatedAddress,
      message: 'Address updated successfully'
    });

  } catch (error) {
    return NextResponse.json(
      { success: false, error: `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}

// DELETE endpoint to remove a specific address
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string; addressId: string }> }
) {
  try {
    const { id: customerId, addressId } = await params;
    

    const supabase = createServerSupabaseClient();

    // First, check if this address exists and belongs to the customer
    const { data: existingAddress, error: fetchError } = await supabase
      .from('customer_addresses')
      .select('*')
      .eq('id', addressId)
      .eq('customer_id', customerId)
      .single();

    if (fetchError || !existingAddress) {
      return NextResponse.json(
        { success: false, error: 'Address not found or access denied' },
        { status: 404 }
      );
    }

    // Check if this is the default address and if there are other addresses
    if (existingAddress.is_default) {
      const { data: otherAddresses, error: countError } = await supabase
        .from('customer_addresses')
        .select('id')
        .eq('customer_id', customerId)
        .neq('id', addressId);

      if (countError) {
        return NextResponse.json(
          { success: false, error: 'Error checking address dependencies' },
          { status: 500 }
        );
      }

      // If there are other addresses, set one of them as default
      if (otherAddresses && otherAddresses.length > 0) {
        await supabase
          .from('customer_addresses')
          .update({ is_default: true })
          .eq('id', otherAddresses[0].id);
      }
    }

    // Delete the address
    const { error: deleteError } = await supabase
      .from('customer_addresses')
      .delete()
      .eq('id', addressId)
      .eq('customer_id', customerId); // Security: ensure address belongs to customer

    if (deleteError) {
      return NextResponse.json(
        { success: false, error: `Failed to delete address: ${deleteError.message}` },
        { status: 500 }
      );
    }


    return NextResponse.json({
      success: true,
      message: 'Address deleted successfully'
    });

  } catch (error) {
    return NextResponse.json(
      { success: false, error: `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
} 