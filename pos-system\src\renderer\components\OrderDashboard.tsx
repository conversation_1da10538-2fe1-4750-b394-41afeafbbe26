import React, { memo, useState, useEffect } from 'react';
import { useOrderStore } from '../hooks/useOrderStore';
import type { OrderItem } from '../types/orders';
import type { Customer, CustomerInfo } from '../types/customer';
import OrderGrid from './OrderGrid';
import OrderTabsBar from './OrderTabsBar';
import BulkActionsBar from './BulkActionsBar';
import DriverAssignmentModal from './modals/DriverAssignmentModal';
import OrderCancellationModal from './modals/OrderCancellationModal';
import EditOptionsModal from './modals/EditOptionsModal';
import EditCustomerInfoModal from './modals/EditCustomerInfoModal';
import EditOrderItemsModal from './modals/EditOrderItemsModal';
import { MenuModal } from './modals/MenuModal';
import CustomerInfoForm from './CustomerInfoForm';
import { Plus } from 'lucide-react';
import { useTheme } from '../contexts/theme-context';
import toast from 'react-hot-toast';

// Import the Order type from the hook
type Order = {
  id: string;
  orderNumber: string;
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled';
  items: OrderItem[];
  totalAmount: number;
  customerName?: string;
  customerPhone?: string;
  orderType: 'dine-in' | 'takeaway' | 'delivery';
  tableNumber?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  estimatedTime?: number;
  paymentStatus?: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
  paymentMethod?: 'cash' | 'card' | 'digital';
  paymentTransactionId?: string;
};

interface OrderDashboardProps {
  className?: string;
}

export const OrderDashboard = memo<OrderDashboardProps>(({ className = '' }) => {
  const { resolvedTheme } = useTheme();
  const { 
    orders, 
    filter, 
    setFilter, 
    isLoading,
    updateOrderStatus 
  } = useOrderStore();

  // State for computed values
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [orderCounts, setOrderCounts] = useState({
    orders: 0,
    delivered: 0,
    canceled: 0,
  });

  // State for selected orders and active tab
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<'orders' | 'delivered' | 'canceled'>('orders');
  
  // State for modals
  const [showDriverModal, setShowDriverModal] = useState(false);
  const [pendingDeliveryOrders, setPendingDeliveryOrders] = useState<string[]>([]);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [pendingCancelOrders, setPendingCancelOrders] = useState<string[]>([]);
  
  // State for edit modals
  const [showEditOptionsModal, setShowEditOptionsModal] = useState(false);
  const [showEditCustomerModal, setShowEditCustomerModal] = useState(false);
  const [showEditOrderModal, setShowEditOrderModal] = useState(false);
  const [pendingEditOrders, setPendingEditOrders] = useState<string[]>([]);
  const [editingSingleOrder, setEditingSingleOrder] = useState<string | null>(null);

  // State for new order flow
  const [showOrderTypeModal, setShowOrderTypeModal] = useState(false);
  const [showMenuModal, setShowMenuModal] = useState(false);
  const [selectedOrderType, setSelectedOrderType] = useState<'pickup' | 'delivery' | null>(null);

  // State for delivery flow
  const [showPhoneLookupModal, setShowPhoneLookupModal] = useState(false);
  const [showCustomerInfoModal, setShowCustomerInfoModal] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isLookingUp, setIsLookingUp] = useState(false);
  const [existingCustomer, setExistingCustomer] = useState<Customer | null>(null);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo | null>(null);
  const [orderType, setOrderType] = useState<'pickup' | 'delivery'>('pickup');
  const [tableNumber, setTableNumber] = useState('');
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [isValidatingAddress, setIsValidatingAddress] = useState(false);
  const [addressValid, setAddressValid] = useState(false);

  // Update computed values when dependencies change
  useEffect(() => {
    if (!orders) return;

    // Filter orders based on active tab and global filters
    let filtered = orders;

    // Apply global filters first
    if (filter.status && filter.status !== 'all') {
      filtered = filtered.filter(order => order.status === filter.status);
    }

    if (filter.orderType && filter.orderType !== 'all') {
      filtered = filtered.filter(order => order.orderType === filter.orderType);
    }

    if (filter.searchTerm) {
      const searchTerm = filter.searchTerm.toLowerCase();
      filtered = filtered.filter(order => 
        order.orderNumber.toLowerCase().includes(searchTerm) ||
        order.customerName?.toLowerCase().includes(searchTerm) ||
        order.customerPhone?.includes(searchTerm)
      );
    }

    // Apply tab-specific filters
    switch (activeTab) {
      case 'orders':
        filtered = filtered.filter(order => 
          order.status === 'pending' || 
          order.status === 'preparing' || 
          order.status === 'ready'
        );
        break;
      case 'delivered':
        filtered = filtered.filter(order => order.status === 'completed');
        break;
      case 'canceled':
        filtered = filtered.filter(order => order.status === 'cancelled');
        break;
    }

    setFilteredOrders(filtered);

    // Calculate order counts for tabs
    const counts = {
      orders: 0,
      delivered: 0,
      canceled: 0,
    };

    orders.forEach(order => {
      switch (order.status) {
        case 'pending':
        case 'preparing':
        case 'ready':
          counts.orders++;
          break;
        case 'completed':
          counts.delivered++;
          break;
        case 'cancelled':
          counts.canceled++;
          break;
      }
    });

    setOrderCounts(counts);
  }, [orders, filter, activeTab]);

  // Handle tab change
  const handleTabChange = (tab: 'orders' | 'delivered' | 'canceled') => {
    setActiveTab(tab);
    setSelectedOrders([]); // Clear selection when changing tabs
  };

  // Handle order selection
  const handleToggleOrderSelection = (orderId: string) => {
    setSelectedOrders(prev => 
      prev.includes(orderId) 
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  // Handle order double-click for editing
  const handleOrderDoubleClick = (orderId: string) => {
    setPendingEditOrders([orderId]);
    setEditingSingleOrder(orderId);
    setShowEditOptionsModal(true);
  };

  // Handle new order FAB click
  const handleNewOrderClick = () => {
    setShowOrderTypeModal(true);
  };

  // Handle order type selection
  const handleOrderTypeSelect = (type: 'pickup' | 'delivery') => {
    setSelectedOrderType(type);
    setOrderType(type);
    setShowOrderTypeModal(false);
    
    if (type === 'pickup') {
      // For pickup orders, go directly to menu with basic customer info
      setCustomerInfo({
        name: 'Walk-in Customer',
        phone: '',
        email: '',
        address: {
          street: '',
          city: '',
          postalCode: ''
        },
        notes: ''
      });
      setShowMenuModal(true);
    } else {
      // For delivery orders, start with phone lookup
      setShowPhoneLookupModal(true);
    }
  };

  // Handle menu modal close
  const handleMenuModalClose = () => {
    setShowMenuModal(false);
    setSelectedOrderType(null);
    // Reset all state
    setPhoneNumber('');
    setCustomerInfo(null);
    setExistingCustomer(null);
    setSpecialInstructions('');
    setTableNumber('');
    setAddressValid(false);
    setShowPhoneLookupModal(false);
    setShowCustomerInfoModal(false);
  };

  // Phone lookup handler
  const handlePhoneLookup = async () => {
    if (!phoneNumber.trim()) {
      toast.error('Please enter a phone number');
      return;
    }

    setIsLookingUp(true);
    try {
      // Simulate API call to lookup customer
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock response - in real app, this would come from API
      const mockCustomer = {
        id: '1',
        name: 'John Doe',
        phone: phoneNumber,
        email: '<EMAIL>',
        addresses: [{
          id: '1',
          street: '123 Main St',
          city: 'Athens',
          postal_code: '12345',
          country: 'Greece',
          is_default: true
        }]
      };
      
      if (phoneNumber === '1234567890') {
        setExistingCustomer(mockCustomer);
        toast.success('Customer found!');
        setShowPhoneLookupModal(false);
        setShowMenuModal(true);
      } else {
        setExistingCustomer(null);
        // Initialize customerInfo for new customer
        setCustomerInfo({
          name: '',
          phone: phoneNumber,
          email: '',
          address: {
            street: '',
            city: '',
            postalCode: ''
          },
          notes: ''
        });
        toast.success('New customer - please fill in details');
        setShowPhoneLookupModal(false);
        setShowCustomerInfoModal(true);
      }
    } catch (error) {
      console.error('Phone lookup error:', error);
      toast.error('Failed to lookup customer');
    } finally {
      setIsLookingUp(false);
    }
  };

  // Handle customer info submission
  const handleCustomerInfoSubmit = () => {
    // Validate required fields
    if (!customerInfo?.name.trim()) {
      toast.error('Customer name is required');
      return;
    }
    
    if (!customerInfo?.phone.trim()) {
      toast.error('Phone number is required');
      return;
    }
    
    // For delivery orders, validate address
    if (orderType === 'delivery') {
      if (!customerInfo?.address?.street.trim()) {
        toast.error('Address is required for delivery orders');
        return;
      }
      if (!customerInfo?.address?.city.trim()) {
        toast.error('City is required for delivery orders');
        return;
      }
      if (!customerInfo?.address?.postalCode.trim()) {
        toast.error('Postal code is required for delivery orders');
        return;
      }
    }
    
    setShowCustomerInfoModal(false);
    setShowMenuModal(true);
  };

  // Handle address validation
  const handleValidateAddress = async (): Promise<boolean> => {
    setIsValidatingAddress(true);
    
    try {
      // Simulate address validation API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock validation - in real app, this would validate against a real service
      const isValid = customerInfo?.address?.street.trim() && 
                     customerInfo?.address?.city.trim() && 
                     customerInfo?.address?.postalCode.trim();
      setAddressValid(!!isValid);
      
      if (isValid) {
        toast.success('Address validated successfully');
      } else {
        toast.error('Address validation failed');
      }
      return !!isValid;
    } catch (error) {
      console.error('Address validation failed:', error);
      toast.error('Failed to validate address');
      setAddressValid(false);
      return false;
    } finally {
      setIsValidatingAddress(false);
    }
  };

  // Helper functions for menu modal
  const getCustomerForMenu = () => {
    if (existingCustomer) {
      return {
        id: existingCustomer.id,
        name: existingCustomer.name,
        phone: existingCustomer.phone,
        email: existingCustomer.email
      };
    } else if (customerInfo) {
      return {
        id: 'new',
        name: customerInfo.name,
        phone: customerInfo.phone,
        email: customerInfo.email
      };
    }
    return null;
  };

  const getSelectedAddress = () => {
    if (existingCustomer?.addresses && existingCustomer.addresses.length > 0) {
      const defaultAddress = existingCustomer.addresses.find(addr => addr.is_default) || existingCustomer.addresses[0];
      return {
        street: defaultAddress.street,
        city: defaultAddress.city,
        postalCode: defaultAddress.postal_code,
        notes: defaultAddress.delivery_notes
      };
    } else if (customerInfo?.address) {
      return {
        street: customerInfo.address.street,
        city: customerInfo.address.city,
        postalCode: customerInfo.address.postalCode,
        notes: customerInfo.notes
      };
    }
    return null;
  };

  // Handle order completion from menu modal
  const handleOrderComplete = (orderData: any) => {
    // Handle the completed order
    toast.success('Order created successfully!');
    setShowMenuModal(false);
    setSelectedOrderType(null);
    // Refresh orders if needed
  };

  // Handle bulk actions
  const handleBulkAction = async (action: string) => {
    
    if (action === 'delivered') {
      // Get the selected order objects
      const selectedOrderObjects = orders.filter(order => selectedOrders.includes(order.id));
      
      // Separate delivery orders from pickup orders
      const deliveryOrders = selectedOrderObjects.filter(order => order.orderType === 'delivery');
      const pickupOrders = selectedOrderObjects.filter(order => order.orderType !== 'delivery');
      
      // Handle pickup orders immediately (mark as delivered)
      if (pickupOrders.length > 0) {
        for (const order of pickupOrders) {
          const success = await updateOrderStatus(order.id, 'completed');
          if (!success) {
            toast.error(`Failed to mark order ${order.orderNumber} as delivered`);
            return;
          }
        }
        toast.success(`${pickupOrders.length} pickup order(s) marked as delivered`);
      }
      
      // Handle delivery orders - ask for driver assignment first
      if (deliveryOrders.length > 0) {
        setPendingDeliveryOrders(deliveryOrders.map(order => order.id));
        setShowDriverModal(true);
      } else if (pickupOrders.length > 0) {
        // If only pickup orders, clear selection
        setSelectedOrders([]);
      }
         } else if (action === 'cancel') {
       // Handle cancel action - show cancellation modal
       if (selectedOrders.length > 0) {
         setPendingCancelOrders(selectedOrders);
         setShowCancelModal(true);
       } else {
         toast.error('No orders selected for cancellation');
       }
     } else if (action === 'edit') {
       // Handle edit action - show edit options modal
       if (selectedOrders.length > 0) {
         setPendingEditOrders(selectedOrders);
         setEditingSingleOrder(null);
         setShowEditOptionsModal(true);
       } else {
         toast.error('No orders selected for editing');
       }
     }
     
     // Handle other bulk actions here
     // TODO: Implement other bulk actions (map, etc.)
   };

  // Handle clearing selection
  const handleClearSelection = () => {
    setSelectedOrders([]);
  };

  // Handle driver assignment
  const handleDriverAssignment = async (driverId: string) => {
    try {
      // Mark all pending delivery orders as completed (with driver assigned)
      for (const orderId of pendingDeliveryOrders) {
        const success = await updateOrderStatus(orderId, 'completed');
        if (!success) {
          const order = orders.find(o => o.id === orderId);
          toast.error(`Failed to mark order ${order?.orderNumber} as delivered`);
          return;
        }
      }
      
      toast.success(`${pendingDeliveryOrders.length} delivery order(s) assigned to driver and marked as delivered`);
      
      // Close modal and clear selections
      setShowDriverModal(false);
      setPendingDeliveryOrders([]);
      setSelectedOrders([]);
    } catch (error) {
      console.error('Failed to assign driver:', error);
      toast.error('Failed to assign driver to orders');
    }
  };

  // Handle driver modal close
  const handleDriverModalClose = () => {
    setShowDriverModal(false);
    setPendingDeliveryOrders([]);
  };

  // Handle order cancellation
  const handleOrderCancellation = async (reason: string) => {
    try {
      // Cancel all pending orders
      for (const orderId of pendingCancelOrders) {
        const success = await updateOrderStatus(orderId, 'cancelled');
        if (!success) {
          const order = orders.find(o => o.id === orderId);
          toast.error(`Failed to cancel order ${order?.orderNumber}`);
          return;
        }
      }
      
      toast.success(`${pendingCancelOrders.length} order(s) cancelled successfully`);
      
      // Close modal and clear selections
      setShowCancelModal(false);
      setPendingCancelOrders([]);
      setSelectedOrders([]);
    } catch (error) {
      console.error('Failed to cancel orders:', error);
      toast.error('Failed to cancel orders');
    }
  };

  // Handle cancel modal close
  const handleCancelModalClose = () => {
    setShowCancelModal(false);
    setPendingCancelOrders([]);
  };

  // Handle edit options
  const handleEditInfo = () => {
    setShowEditOptionsModal(false);
    setShowEditCustomerModal(true);
  };

  const handleEditOrder = () => {
    setShowEditOptionsModal(false);
    setShowEditOrderModal(true);
  };

  const handleEditOptionsClose = () => {
    setShowEditOptionsModal(false);
    setPendingEditOrders([]);
    setEditingSingleOrder(null);
  };

  // Handle customer info edit
  const handleCustomerInfoSave = async (customerInfo: { name: string; phone: string; email?: string; address?: string }) => {
    try {
      // Update customer info for all pending edit orders
      for (const orderId of pendingEditOrders) {
        // Here you would typically call an API to update the order
        // await updateOrderCustomerInfo(orderId, customerInfo);
      }
      
      toast.success(`Customer information updated for ${pendingEditOrders.length} order(s)`);
      
      // Close modal and clear state
      setShowEditCustomerModal(false);
      setPendingEditOrders([]);
      setEditingSingleOrder(null);
      setSelectedOrders([]);
    } catch (error) {
      console.error('Failed to update customer info:', error);
      toast.error('Failed to update customer information');
    }
  };

  const handleEditCustomerClose = () => {
    setShowEditCustomerModal(false);
    setPendingEditOrders([]);
    setEditingSingleOrder(null);
  };

  // Handle order items edit
  const handleOrderItemsSave = async (items: OrderItem[], orderNotes?: string) => {
    try {
      // Update order items for all pending edit orders
      for (const orderId of pendingEditOrders) {
        // Here you would typically call an API to update the order
        // await updateOrderItems(orderId, { items, orderNotes });
      }
      
      toast.success(`Order items updated for ${pendingEditOrders.length} order(s)`);
      
      // Close modal and clear state
      setShowEditOrderModal(false);
      setPendingEditOrders([]);
      setEditingSingleOrder(null);
      setSelectedOrders([]);
    } catch (error) {
      console.error('Failed to update order items:', error);
      toast.error('Failed to update order items');
    }
  };

  const handleEditOrderClose = () => {
    setShowEditOrderModal(false);
    setPendingEditOrders([]);
    setEditingSingleOrder(null);
  };

  // Get customer info for the first selected order (for editing)
  const getSelectedOrderCustomerInfo = () => {
    if (pendingEditOrders.length === 0) return { name: '', phone: '', address: '', notes: '' };
    
    const firstOrder = orders.find(order => order.id === pendingEditOrders[0]);
    return {
      name: firstOrder?.customerName || '',
      phone: firstOrder?.customerPhone || '',
      address: '', // Add address field to order type if needed
      notes: firstOrder?.notes || ''
    };
  };

  // Get order items for the first selected order (for editing)
  const getSelectedOrderItems = () => {
    if (pendingEditOrders.length === 0) return [];
    
    const firstOrder = orders.find(order => order.id === pendingEditOrders[0]);
    return firstOrder?.items || [];
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-white/70">Loading orders...</div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 relative ${className}`}>
      {/* Order Tabs */}
      <OrderTabsBar 
        activeTab={activeTab}
        onTabChange={handleTabChange}
        orderCounts={orderCounts}
      />

      {/* Bulk Actions */}
      <BulkActionsBar 
        selectedCount={selectedOrders.length}
        activeTab={activeTab}
        onBulkAction={handleBulkAction}
        onClearSelection={handleClearSelection}
      />

      {/* Orders Grid */}
      <OrderGrid
        selectedOrders={selectedOrders}
        onToggleOrderSelection={handleToggleOrderSelection}
        onOrderDoubleClick={handleOrderDoubleClick}
        activeTab={activeTab}
      />

      {/* Floating Action Button for New Order */}
      <button
        onClick={handleNewOrderClick}
        className={`fixed bottom-6 right-6 w-16 h-16 rounded-full shadow-lg transition-all duration-300 hover:scale-110 active:scale-95 z-50 ${
          resolvedTheme === 'light'
            ? 'bg-blue-600 hover:bg-blue-700 text-white'
            : 'bg-blue-500 hover:bg-blue-600 text-white'
        }`}
        title="New Order"
      >
        <Plus size={24} className="mx-auto" />
      </button>

      {/* Order Type Selection Modal */}
      {showOrderTypeModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className={`rounded-lg p-6 max-w-md w-full mx-4 ${
            resolvedTheme === 'light' ? 'bg-white' : 'bg-gray-800'
          }`}>
            <h2 className={`text-xl font-semibold mb-4 ${
              resolvedTheme === 'light' ? 'text-gray-900' : 'text-white'
            }`}>
              Select Order Type
            </h2>
            <div className="space-y-3">
              <button
                onClick={() => handleOrderTypeSelect('pickup')}
                className={`w-full p-4 rounded-lg border-2 transition-colors ${
                  resolvedTheme === 'light'
                    ? 'border-gray-200 hover:border-blue-500 hover:bg-blue-50'
                    : 'border-gray-600 hover:border-blue-400 hover:bg-blue-900/20'
                }`}
              >
                <div className="text-left">
                  <div className={`font-medium ${resolvedTheme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                    🥡 Pickup
                  </div>
                  <div className={`text-sm ${resolvedTheme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
                    Customer picks up the order
                  </div>
                </div>
              </button>
              <button
                onClick={() => handleOrderTypeSelect('delivery')}
                className={`w-full p-4 rounded-lg border-2 transition-colors ${
                  resolvedTheme === 'light'
                    ? 'border-gray-200 hover:border-blue-500 hover:bg-blue-50'
                    : 'border-gray-600 hover:border-blue-400 hover:bg-blue-900/20'
                }`}
              >
                <div className="text-left">
                  <div className={`font-medium ${resolvedTheme === 'light' ? 'text-gray-900' : 'text-white'}`}>
                    🚚 Delivery
                  </div>
                  <div className={`text-sm ${resolvedTheme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}>
                    Deliver to customer address
                  </div>
                </div>
              </button>
            </div>
            <button
              onClick={() => setShowOrderTypeModal(false)}
              className={`mt-4 w-full p-2 rounded-lg ${
                resolvedTheme === 'light'
                  ? 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                  : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
              }`}
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Phone Lookup Modal */}
      {showPhoneLookupModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`w-full max-w-md rounded-3xl shadow-2xl border transform transition-all duration-300 ${
            resolvedTheme === 'dark'
              ? 'bg-gray-800/90 border-gray-700/50 backdrop-blur-xl'
              : 'bg-white/90 border-gray-200/50 backdrop-blur-xl'
          }`}>
            <div className="p-8">
              <h3 className={`text-2xl font-bold mb-4 text-center ${
                resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                Customer Phone Number
              </h3>
              
              <p className={`text-center mb-6 ${
                resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
              }`}>
                Enter customer's phone number to lookup existing account or create new one
              </p>

              <div className="space-y-6">
                <div>
                  <input
                    type="tel"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    placeholder="Enter phone number"
                    className={`w-full p-4 rounded-xl border-2 transition-all duration-300 ${
                      resolvedTheme === 'dark'
                        ? 'bg-gray-700/50 border-gray-600/50 text-white placeholder-gray-400 focus:border-blue-500 focus:bg-gray-700/70'
                        : 'bg-white/50 border-gray-300/50 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:bg-white/70'
                    } backdrop-blur-sm`}
                    autoFocus
                    onKeyDown={(e) => e.key === 'Enter' && handlePhoneLookup()}
                  />
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowPhoneLookupModal(false)}
                    className={`flex-1 px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                      resolvedTheme === 'dark'
                        ? 'bg-gray-600/50 text-white hover:bg-gray-600/70'
                        : 'bg-gray-300/50 text-gray-700 hover:bg-gray-300/70'
                    } backdrop-blur-sm`}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handlePhoneLookup}
                    disabled={isLookingUp || !phoneNumber.trim()}
                    className={`flex-1 px-6 py-3 rounded-xl font-medium transition-all duration-300 backdrop-blur-sm ${
                      isLookingUp || !phoneNumber.trim()
                        ? resolvedTheme === 'dark'
                          ? 'bg-gray-600/50 text-gray-400 cursor-not-allowed'
                          : 'bg-gray-300/50 text-gray-500 cursor-not-allowed'
                        : resolvedTheme === 'dark'
                          ? 'bg-blue-600/80 text-white hover:bg-blue-600/90'
                          : 'bg-blue-600/80 text-white hover:bg-blue-600/90'
                    }`}
                  >
                    {isLookingUp ? 'Looking up...' : 'Continue'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Customer Info Modal */}
      {showCustomerInfoModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className={`w-full max-w-4xl rounded-3xl shadow-2xl border transform transition-all duration-300 max-h-[90vh] overflow-hidden ${
            resolvedTheme === 'dark'
              ? 'bg-gray-800/90 border-gray-700/50 backdrop-blur-xl'
              : 'bg-white/90 border-gray-200/50 backdrop-blur-xl'
          }`}>
            <div className="flex items-center justify-between p-6 border-b border-gray-200/20">
              <h3 className={`text-2xl font-bold ${
                resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                Customer Information
                {existingCustomer && (
                  <span className="text-emerald-500 text-lg ml-2">(Existing Customer)</span>
                )}
              </h3>
              <button
                onClick={() => setShowCustomerInfoModal(false)}
                className={`p-2 rounded-xl transition-all duration-200 ${
                  resolvedTheme === 'dark'
                    ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700/50'
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100/50'
                }`}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
              {customerInfo && (
                <CustomerInfoForm
                  customerInfo={customerInfo}
                  setCustomerInfo={(value) => {
                    if (typeof value === 'function') {
                      setCustomerInfo(prev => prev ? value(prev) : null);
                    } else {
                      setCustomerInfo(value);
                    }
                  }}
                  orderType={orderType === 'pickup' ? 'takeaway' : orderType as 'dine-in' | 'takeaway' | 'delivery'}
                  setOrderType={(type) => {
                    const mappedType = type === 'takeaway' ? 'pickup' : type;
                    setOrderType(mappedType as 'pickup' | 'delivery');
                  }}
                  tableNumber={tableNumber}
                  setTableNumber={setTableNumber}
                  specialInstructions={specialInstructions}
                  setSpecialInstructions={setSpecialInstructions}
                  onValidateAddress={handleValidateAddress}
                  isValidatingAddress={isValidatingAddress}
                  addressValid={addressValid}
                />
              )}
              
              <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200/20">
                <button
                  onClick={() => setShowCustomerInfoModal(false)}
                  className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                    resolvedTheme === 'dark'
                      ? 'bg-gray-600/50 text-white hover:bg-gray-600/70'
                      : 'bg-gray-300/50 text-gray-700 hover:bg-gray-300/70'
                  } backdrop-blur-sm`}
                >
                  Cancel
                </button>
                <button
                  onClick={handleCustomerInfoSubmit}
                  className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 backdrop-blur-sm ${
                    resolvedTheme === 'dark'
                      ? 'bg-blue-600/80 text-white hover:bg-blue-600/90'
                      : 'bg-blue-600/80 text-white hover:bg-blue-600/90'
                  }`}
                >
                  Continue to Menu
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Menu Modal */}
      <MenuModal
        isOpen={showMenuModal}
        onClose={handleMenuModalClose}
        selectedCustomer={getCustomerForMenu()}
        selectedAddress={getSelectedAddress()}
        orderType={selectedOrderType || 'pickup'}
        onOrderComplete={handleOrderComplete}
      />

      {/* Existing Modals */}
      <DriverAssignmentModal
        isOpen={showDriverModal}
        orderCount={pendingDeliveryOrders.length}
        onDriverAssign={handleDriverAssignment}
        onClose={handleDriverModalClose}
      />

      <OrderCancellationModal
        isOpen={showCancelModal}
        orderCount={pendingCancelOrders.length}
        onConfirmCancel={handleOrderCancellation}
        onClose={handleCancelModalClose}
      />

      <EditOptionsModal
        isOpen={showEditOptionsModal}
        orderCount={pendingEditOrders.length}
        onEditInfo={handleEditInfo}
        onEditOrder={handleEditOrder}
        onClose={handleEditOptionsClose}
      />

      <EditCustomerInfoModal
        isOpen={showEditCustomerModal}
        orderCount={pendingEditOrders.length}
        initialCustomerInfo={getSelectedOrderCustomerInfo()}
        onSave={handleCustomerInfoSave}
        onClose={handleEditCustomerClose}
      />

      <EditOrderItemsModal
        isOpen={showEditOrderModal}
        orderCount={pendingEditOrders.length}
        initialItems={getSelectedOrderItems()}
        onSave={handleOrderItemsSave}
        onClose={handleEditOrderClose}
      />
    </div>
  );
});

OrderDashboard.displayName = 'OrderDashboard';

export default OrderDashboard;