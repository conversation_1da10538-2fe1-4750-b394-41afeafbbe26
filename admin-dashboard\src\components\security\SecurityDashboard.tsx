'use client'

import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Shield,
  AlertTriangle,
  Activity,
  Users,
  MapPin,
  Clock,
  TrendingUp,
  TrendingDown,
  Eye,
  Lock,
  Unlock,
  Wifi,
  WifiOff,
  CheckCircle,
  XCircle,
  BarChart3,
  PieChart,
  LineChart,
  RefreshCw,
  Download,
  Filter,
  Search,
  Bell,
  BellOff,
} from 'lucide-react'

import {
  securityMonitoringService,
  type SecurityEvent,
  type SecurityMetrics,
  type AnomalyDetection,
} from '@/services/security-monitoring-service'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface SecurityDashboardProps {
  className?: string
}

interface DashboardState {
  metrics: SecurityMetrics | null
  realtimeAlerts: SecurityEvent[]
  anomalies: AnomalyDetection[]
  isLoading: boolean
  error: string | null
  autoRefresh: boolean
  timeRange: '1h' | '24h' | '7d' | '30d'
  alertsEnabled: boolean
}

export default function SecurityDashboard({ className }: SecurityDashboardProps) {
  const { t } = useTranslation()
  const [state, setState] = useState<DashboardState>({
    metrics: null,
    realtimeAlerts: [],
    anomalies: [],
    isLoading: true,
    error: null,
    autoRefresh: true,
    timeRange: '24h',
    alertsEnabled: true,
  })

  // Load dashboard data
  useEffect(() => {
    loadDashboardData()
    
    // Set up auto-refresh
    if (state.autoRefresh) {
      const interval = setInterval(loadDashboardData, 30000) // Refresh every 30 seconds
      return () => clearInterval(interval)
    }
    return undefined
  }, [state.autoRefresh, state.timeRange])

  // Set up real-time alerts
  useEffect(() => {
    if (!state.alertsEnabled) return

    const unsubscribe = securityMonitoringService.subscribeToAlerts((event) => {
      setState(prev => ({
        ...prev,
        realtimeAlerts: [event, ...prev.realtimeAlerts].slice(0, 10), // Keep latest 10 alerts
      }))
    })

    return unsubscribe
  }, [state.alertsEnabled])

  const loadDashboardData = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      // Calculate time range
      const now = new Date()
      const timeRanges = {
        '1h': new Date(now.getTime() - 60 * 60 * 1000),
        '24h': new Date(now.getTime() - 24 * 60 * 60 * 1000),
        '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
        '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      }

      const timeRange = {
        start: timeRanges[state.timeRange],
        end: now,
      }

      // Load security metrics
      const metrics = await securityMonitoringService.getSecurityMetrics(timeRange)
      
      // Load real-time alerts
      const realtimeAlerts = securityMonitoringService.getRealtimeAlerts()

      setState(prev => ({
        ...prev,
        metrics,
        realtimeAlerts,
        isLoading: false,
      }))

    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load dashboard data',
        isLoading: false,
      }))
    }
  }

  const handleTimeRangeChange = (newTimeRange: '1h' | '24h' | '7d' | '30d') => {
    setState(prev => ({ ...prev, timeRange: newTimeRange }))
  }

  const toggleAutoRefresh = () => {
    setState(prev => ({ ...prev, autoRefresh: !prev.autoRefresh }))
  }

  const toggleAlerts = () => {
    setState(prev => ({ ...prev, alertsEnabled: !prev.alertsEnabled }))
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'bg-green-500/10 text-green-400 border-green-500/20'
      case 'medium':
        return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20'
      case 'high':
        return 'bg-orange-500/10 text-orange-400 border-orange-500/20'
      case 'critical':
        return 'bg-red-500/10 text-red-400 border-red-500/20'
      default:
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20'
    }
  }

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const getRiskScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-red-400'
    if (score >= 0.6) return 'text-orange-400'
    if (score >= 0.4) return 'text-yellow-400'
    return 'text-green-400'
  }

  if (state.isLoading && !state.metrics) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center p-12">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-400" />
          <span className="ml-2 text-gray-400">Loading security dashboard...</span>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">Security Dashboard</h1>
          <p className="text-gray-400">Real-time security monitoring and threat detection</p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Time Range Selector */}
          <div className="flex items-center space-x-1 bg-gray-800/50 rounded-lg p-1">
            {(['1h', '24h', '7d', '30d'] as const).map((range) => (
              <Button
                key={range}
                onClick={() => handleTimeRangeChange(range)}
                variant={state.timeRange === range ? 'default' : 'ghost'}
                className="px-3 py-1 text-sm"
              >
                {range}
              </Button>
            ))}
          </div>

          {/* Controls */}
          <Button
            onClick={toggleAutoRefresh}
            variant={state.autoRefresh ? 'default' : 'ghost'}
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 ${state.autoRefresh ? 'animate-spin' : ''}`} />
          </Button>

          <Button
            onClick={toggleAlerts}
            variant={state.alertsEnabled ? 'default' : 'ghost'}
            size="sm"
          >
            {state.alertsEnabled ? <Bell className="h-4 w-4" /> : <BellOff className="h-4 w-4" />}
          </Button>

          <Button variant="ghost" size="sm">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {state.error && (
        <Alert className="border-red-500/20 bg-red-500/10">
          <AlertTriangle className="h-4 w-4 text-red-400" />
          <AlertDescription className="text-red-400">
            {state.error}
          </AlertDescription>
        </Alert>
      )}

      {/* Real-time Alerts */}
      {state.realtimeAlerts.length > 0 && (
        <Card className="border-orange-500/20 bg-orange-500/5">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-400" />
              <span>Active Security Alerts</span>
              <Badge className="bg-red-500/10 text-red-400 border-red-500/20">
                {state.realtimeAlerts.length}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {state.realtimeAlerts.slice(0, 5).map((alert) => (
                <div
                  key={alert.id}
                  className="flex items-center justify-between p-3 rounded-lg bg-gray-800/30 border border-gray-700/50"
                >
                  <div className="flex items-center space-x-3">
                    <Badge className={getSeverityColor(alert.severity)}>
                      {alert.severity.toUpperCase()}
                    </Badge>
                    <div>
                      <p className="text-sm font-medium text-white">
                        {alert.type.replace(/_/g, ' ').toUpperCase()}
                      </p>
                      <p className="text-xs text-gray-400">
                        {alert.timestamp.toLocaleTimeString()} • {alert.ipAddress}
                      </p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    Investigate
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Security Metrics Grid */}
      {state.metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Login Attempts */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">Total Attempts</p>
                  <p className="text-2xl font-bold text-white">
                    {formatNumber(state.metrics.totalLoginAttempts)}
                  </p>
                </div>
                <div className="p-3 rounded-full bg-blue-500/10 border border-blue-500/20">
                  <Activity className="h-6 w-6 text-blue-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-green-400" />
                <span className="text-sm text-green-400">+12% from last period</span>
              </div>
            </CardContent>
          </Card>

          {/* Success Rate */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">Success Rate</p>
                  <p className="text-2xl font-bold text-white">
                    {state.metrics.totalLoginAttempts > 0
                      ? Math.round((state.metrics.successfulLogins / state.metrics.totalLoginAttempts) * 100)
                      : 0}%
                  </p>
                </div>
                <div className="p-3 rounded-full bg-green-500/10 border border-green-500/20">
                  <CheckCircle className="h-6 w-6 text-green-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center space-x-2">
                <span className="text-sm text-gray-400">
                  {formatNumber(state.metrics.successfulLogins)} successful
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Blocked Attempts */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">Blocked</p>
                  <p className="text-2xl font-bold text-white">
                    {formatNumber(state.metrics.blockedAttempts)}
                  </p>
                </div>
                <div className="p-3 rounded-full bg-red-500/10 border border-red-500/20">
                  <Lock className="h-6 w-6 text-red-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center space-x-2">
                <Shield className="h-4 w-4 text-blue-400" />
                <span className="text-sm text-blue-400">Security protected</span>
              </div>
            </CardContent>
          </Card>

          {/* Risk Score */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">Avg Risk Score</p>
                  <p className={`text-2xl font-bold ${getRiskScoreColor(state.metrics.averageRiskScore)}`}>
                    {Math.round(state.metrics.averageRiskScore * 100)}%
                  </p>
                </div>
                <div className="p-3 rounded-full bg-orange-500/10 border border-orange-500/20">
                  <Eye className="h-6 w-6 text-orange-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center space-x-2">
                {state.metrics.averageRiskScore > 0.6 ? (
                  <TrendingUp className="h-4 w-4 text-red-400" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-green-400" />
                )}
                <span className={`text-sm ${state.metrics.averageRiskScore > 0.6 ? 'text-red-400' : 'text-green-400'}`}>
                  {state.metrics.averageRiskScore > 0.6 ? 'Elevated' : 'Normal'}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Detailed Analytics */}
      {state.metrics && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Device Analytics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-blue-400" />
                <span>Device Analytics</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-400">Unique Devices</span>
                  <span className="text-lg font-semibold text-white">
                    {formatNumber(state.metrics.uniqueDevices)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-400">Unique Locations</span>
                  <span className="text-lg font-semibold text-white">
                    {formatNumber(state.metrics.uniqueLocations)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-400">Suspicious Activities</span>
                  <span className="text-lg font-semibold text-orange-400">
                    {formatNumber(state.metrics.suspiciousActivities)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Top Threats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-400" />
                <span>Top Threats</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {state.metrics.topThreats.length > 0 ? (
                  state.metrics.topThreats.map((threat, index) => (
                    <div key={threat} className="flex items-center justify-between">
                      <span className="text-sm text-gray-300">
                        {threat.replace(/_/g, ' ').toUpperCase()}
                      </span>
                      <Badge className={getSeverityColor(index === 0 ? 'high' : index === 1 ? 'medium' : 'low')}>
                        #{index + 1}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-400 text-center py-4">
                    No threats detected in this time period
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Security Incidents */}
      {state.metrics && state.metrics.securityIncidents > 0 && (
        <Card className="border-red-500/20 bg-red-500/5">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-red-400" />
                <span>Security Incidents</span>
                <Badge className="bg-red-500/10 text-red-400 border-red-500/20">
                  {state.metrics.securityIncidents}
                </Badge>
              </div>
              <Button variant="ghost" size="sm">
                View All
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Alert className="border-red-500/20 bg-red-500/10">
              <AlertTriangle className="h-4 w-4 text-red-400" />
              <AlertDescription className="text-red-400">
                {state.metrics.securityIncidents} security incidents detected in the last {state.timeRange}.
                Immediate investigation recommended.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 