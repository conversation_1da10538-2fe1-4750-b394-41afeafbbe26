'use client'

import { useState, useEffect } from 'react'
import { enhancedPOSSettings, POSTerminal, SyncResult } from '@/lib/enhanced-pos-settings'
import {
  POSSettingCategory,
  DefaultPOSSettings,
  TerminalSettings,
  PrinterSettings,
  TaxSettings,
  PaymentSettings,
  ReceiptSettings
} from '@/lib/pos-settings-validation'
import { toast } from 'react-hot-toast'
// import { useRealTimeSync } from '@/hooks/useRealTimeSync'
import StaffPermissionsManager from './StaffPermissionsManager'
import HardwareConfigurationPanel from './HardwareConfigurationPanel'
import SyncStatusMonitor from './SyncStatusMonitor'
import InventorySyncManager from './InventorySyncManager'
import RemotePOSControl from './RemotePOSControl'
import LiveOrderMonitor from './LiveOrderMonitor'
import DeveloperTools from './DeveloperTools'
import AdvancedAnalytics from './AdvancedAnalytics'
import {
  Settings,
  Monitor,
  Printer,
  CreditCard,
  Receipt,
  Calculator,
  Wifi,
  WifiOff,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  AlertTriangle,
  Save,
  Eye,
  Edit,
  Trash2,
  Users,
  HardDrive,
  Activity,
  Package,
  Zap,
  BarChart3,
  Bug,
  ShoppingCart
} from 'lucide-react'

interface EnhancedPOSManagerProps {
  onSyncStatusChange?: (status: string) => void
  branchId?: string
}

interface TerminalStatus {
  terminal_id: string
  name: string
  status: 'online' | 'offline' | 'syncing' | 'error' | 'maintenance'
  last_heartbeat: string
  settings_version: number
  menu_version: number
  pending_sync_count: number
  system_info: {
    cpu_usage: number
    memory_usage: number
    disk_usage: number
    uptime: number
  }
  network_info: {
    ip_address: string
    connection_quality: 'excellent' | 'good' | 'poor' | 'disconnected'
    latency_ms: number
  }
  performance_metrics: {
    avg_response_time_ms: number
    orders_per_hour: number
    error_rate: number
  }
}

interface SyncStatus {
  queue: any[]
  conflicts: any[]
  successRate: number
  pendingCount: number
  avgWaitTime: number
  terminalHealth: number
}

export default function EnhancedPOSManager({ onSyncStatusChange, branchId }: EnhancedPOSManagerProps) {
  const [terminals, setTerminals] = useState<POSTerminal[]>([])
  const [terminalStatuses, setTerminalStatuses] = useState<TerminalStatus[]>([])
  const [selectedTerminal, setSelectedTerminal] = useState<string>('')
  const [activeTab, setActiveTab] = useState<'overview' | 'terminals' | 'staff' | 'hardware' | 'inventory' | 'sync' | 'remote' | 'orders' | 'dev' | 'analytics'>('overview')
  const [activeCategory, setActiveCategory] = useState<POSSettingCategory>('terminal')
  const [settings, setSettings] = useState<any>(DefaultPOSSettings)
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    queue: [],
    conflicts: [],
    successRate: 0,
    pendingCount: 0,
    avgWaitTime: 0,
    terminalHealth: 0
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [syncing, setSyncing] = useState(false)

  // Real-time sync hook for live updates (TODO: Implement)
  // const { isConnected, lastUpdate, errors } = useRealTimeSync({
  //   tables: ['pos_terminal_status', 'pos_sync_queue_enhanced', 'staff_permissions_sync'],
  //   onUpdate: handleRealTimeUpdate,
  //   filters: branchId ? { pos_terminal_status: `branch_id.eq.${branchId}` } : undefined
  // })
  const isConnected = true
  const lastUpdate = new Date().toISOString()
  const errors: any[] = []

  // Tab configurations
  const tabs = [
    { id: 'overview', name: 'Overview', icon: Activity, description: 'System overview and health' },
    { id: 'terminals', name: 'Terminals', icon: Monitor, description: 'Terminal management and settings' },
    { id: 'staff', name: 'Staff', icon: Users, description: 'Staff permissions and access control' },
    { id: 'hardware', name: 'Hardware', icon: HardDrive, description: 'Hardware configuration and setup' },
    { id: 'inventory', name: 'Inventory', icon: Package, description: 'Inventory synchronization and management' },
    { id: 'sync', name: 'Sync', icon: RefreshCw, description: 'Synchronization monitoring and control' },
    { id: 'remote', name: 'Remote Control', icon: Zap, description: 'Remote POS terminal control' },
    { id: 'orders', name: 'Live Orders', icon: ShoppingCart, description: 'Real-time order monitoring' },
    { id: 'dev', name: 'Developer', icon: Bug, description: 'Developer tools and debugging' },
    { id: 'analytics', name: 'Analytics', icon: BarChart3, description: 'Advanced analytics and insights' },
  ]

  // Category configurations
  const categories = [
    { id: 'terminal' as POSSettingCategory, name: 'Terminal', icon: Monitor, description: 'Display and hardware settings' },
    { id: 'printer' as POSSettingCategory, name: 'Printer', icon: Printer, description: 'Receipt and kitchen printer setup' },
    { id: 'payment' as POSSettingCategory, name: 'Payment', icon: CreditCard, description: 'Payment processing options' },
    { id: 'tax' as POSSettingCategory, name: 'Tax', icon: Calculator, description: 'Tax calculation rules' },
    { id: 'receipt' as POSSettingCategory, name: 'Receipt', icon: Receipt, description: 'Receipt formatting and content' },
    { id: 'restaurant' as POSSettingCategory, name: 'Restaurant', icon: Settings, description: 'Restaurant information and branding' },
  ]

  useEffect(() => {
    loadTerminals()
    loadTerminalStatuses()
    loadSyncStatus()
  }, [branchId])

  useEffect(() => {
    if (selectedTerminal) {
      loadTerminalSettings(selectedTerminal)
    }
  }, [selectedTerminal, activeCategory])

  // Real-time update handler
  const handleRealTimeUpdate = (table: string, payload: any) => {
    switch (table) {
      case 'pos_terminal_status':
        updateTerminalStatus(payload)
        break
      case 'pos_sync_queue_enhanced':
        updateSyncStatus(payload)
        break
      case 'staff_permissions_sync':
        updateStaffPermissions(payload)
        break
    }
  }

  const updateTerminalStatus = (payload: any) => {
    if (payload.eventType === 'UPDATE' || payload.eventType === 'INSERT') {
      setTerminalStatuses(prev => {
        const updated = prev.filter(t => t.terminal_id !== payload.new.terminal_id)
        return [...updated, payload.new]
      })
    }
  }

  const updateSyncStatus = (payload: any) => {
    // Update sync queue status
    setSyncStatus(prev => ({
      ...prev,
      queue: prev.queue.map(item =>
        item.id === payload.new.id ? payload.new : item
      )
    }))
  }

  const updateStaffPermissions = (payload: any) => {
    // Handle staff permission updates
    console.log('Staff permission updated:', payload)
  }

  const loadTerminals = async () => {
    try {
      const terminalList = await enhancedPOSSettings.getTerminals()
      setTerminals(terminalList)

      // Convert terminals to terminal statuses for the counter
      const statusList: TerminalStatus[] = terminalList.map(terminal => ({
        terminal_id: terminal.terminal_id,
        name: terminal.name,
        status: terminal.status as 'online' | 'offline' | 'syncing' | 'error' | 'maintenance',
        last_heartbeat: terminal.last_heartbeat,
        settings_version: terminal.settings_version || 1,
        menu_version: 1,
        pending_sync_count: 0,
        system_info: {
          cpu_usage: 0,
          memory_usage: 0,
          disk_usage: 0,
          uptime: terminal.uptime || 0
        },
        network_info: {
          ip_address: terminal.ip_address,
          connection_quality: 'good' as const,
          latency_ms: 50
        },
        performance_metrics: {
          avg_response_time_ms: 100,
          orders_per_hour: 0,
          error_rate: 0
        }
      }))
      setTerminalStatuses(statusList)

      if (terminalList.length > 0 && !selectedTerminal) {
        setSelectedTerminal(terminalList[0].terminal_id)
      }
    } catch (error) {
      console.error('Failed to load terminals:', error)
      toast.error('Failed to load terminals')
    } finally {
      setLoading(false)
    }
  }

  const loadTerminalStatuses = async () => {
    try {
      const response = await fetch(`/api/pos/terminal-status${branchId ? `?branchId=${branchId}` : ''}`)
      if (response.ok) {
        const data = await response.json()
        setTerminalStatuses(data.terminals || [])
      }
    } catch (error) {
      console.error('Failed to load terminal statuses:', error)
    }
  }

  const loadSyncStatus = async () => {
    try {
      const [queueResponse, metricsResponse] = await Promise.all([
        fetch('/api/sync/queue'),
        fetch('/api/sync/metrics')
      ])

      if (queueResponse.ok && metricsResponse.ok) {
        const queueData = await queueResponse.json()
        const metricsData = await metricsResponse.json()

        setSyncStatus({
          queue: queueData.queue || [],
          conflicts: queueData.conflicts || [],
          successRate: metricsData.successRate || 0,
          pendingCount: metricsData.pendingCount || 0,
          avgWaitTime: metricsData.avgWaitTime || 0,
          terminalHealth: metricsData.terminalHealth || 0
        })
      }
    } catch (error) {
      console.error('Failed to load sync status:', error)
    }
  }

  const loadTerminalSettings = async (terminalId: string) => {
    try {
      const configurations = await enhancedPOSSettings.getPOSConfiguration(terminalId, activeCategory)
      
      // Convert to settings object
      const categorySettings: any = {}
      configurations.forEach(config => {
        categorySettings[config.setting_key] = config.setting_value
      })

      setSettings((prev: any) => ({
        ...prev,
        [activeCategory]: {
          ...DefaultPOSSettings[activeCategory],
          ...categorySettings
        }
      }))

    } catch (error) {
      console.error('Failed to load terminal settings:', error)
      toast.error('Failed to load settings')
    }
  }

  const handleSettingChange = (key: string, value: any) => {
    setSettings((prev: any) => ({
      ...prev,
      [activeCategory]: {
        ...prev[activeCategory],
        [key]: value
      }
    }))
  }

  const saveSettings = async () => {
    if (!selectedTerminal) {
      toast.error('No terminal selected')
      return
    }

    setSaving(true)
    try {
      const result = await enhancedPOSSettings.updatePOSConfiguration(
        selectedTerminal,
        activeCategory,
        settings[activeCategory]
      )

      if (result.success) {
        toast.success(`${activeCategory} settings saved successfully`)
        onSyncStatusChange?.('syncing')
      } else {
        toast.error(`Failed to save settings: ${result.errors.join(', ')}`)
      }
    } catch (error) {
      console.error('Failed to save settings:', error)
      toast.error('Failed to save settings')
    } finally {
      setSaving(false)
    }
  }

  const syncAllSettings = async () => {
    if (!selectedTerminal) {
      toast.error('No terminal selected')
      return
    }

    setSyncing(true)
    try {
      const results = await enhancedPOSSettings.syncAllSettings(selectedTerminal)
      
      const failedCategories = Object.entries(results)
        .filter(([_, result]) => !result.success)
        .map(([category, _]) => category)

      if (failedCategories.length === 0) {
        toast.success('All settings synchronized successfully')
        onSyncStatusChange?.('synced')
      } else {
        toast.error(`Failed to sync: ${failedCategories.join(', ')}`)
        onSyncStatusChange?.('failed')
      }

      // Refresh terminal list to update sync status
      await loadTerminals()

    } catch (error) {
      console.error('Failed to sync settings:', error)
      toast.error('Failed to sync settings')
      onSyncStatusChange?.('failed')
    } finally {
      setSyncing(false)
    }
  }

  const getTerminalStatusIcon = (status: POSTerminal['status']) => {
    switch (status) {
      case 'online':
        return <Wifi className="w-4 h-4 text-green-400" />
      case 'offline':
        return <WifiOff className="w-4 h-4 text-gray-400" />
      case 'syncing':
        return <RefreshCw className="w-4 h-4 text-blue-400 animate-spin" />
      case 'error':
        return <AlertTriangle className="w-4 h-4 text-red-400" />
      default:
        return <WifiOff className="w-4 h-4 text-gray-400" />
    }
  }

  const renderTerminalSettings = () => {
    const categorySettings = settings[activeCategory] || {}

    switch (activeCategory) {
      case 'terminal':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Display Brightness (%)
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={categorySettings.display_brightness || 80}
                  onChange={(e) => handleSettingChange('display_brightness', parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
                <span className="text-sm text-gray-500">{categorySettings.display_brightness || 80}%</span>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Screen Timeout (seconds)
                </label>
                <input
                  type="number"
                  min="30"
                  max="3600"
                  value={categorySettings.screen_timeout || 300}
                  onChange={(e) => handleSettingChange('screen_timeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Touch Sensitivity
                </label>
                <select
                  value={categorySettings.touch_sensitivity || 'medium'}
                  onChange={(e) => handleSettingChange('touch_sensitivity', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </select>
              </div>

              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.audio_enabled ?? true}
                    onChange={(e) => handleSettingChange('audio_enabled', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Audio Enabled</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.receipt_auto_print ?? true}
                    onChange={(e) => handleSettingChange('receipt_auto_print', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Auto Print Receipts</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.cash_drawer_enabled ?? true}
                    onChange={(e) => handleSettingChange('cash_drawer_enabled', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Cash Drawer Enabled</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.barcode_scanner_enabled ?? false}
                    onChange={(e) => handleSettingChange('barcode_scanner_enabled', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Barcode Scanner</span>
                </label>
              </div>
            </div>
          </div>
        )

      case 'printer':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Receipt Printer IP
                </label>
                <input
                  type="text"
                  placeholder="*************"
                  value={categorySettings.receipt_printer_ip || ''}
                  onChange={(e) => handleSettingChange('receipt_printer_ip', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Printer Port
                </label>
                <input
                  type="number"
                  min="1"
                  max="65535"
                  value={categorySettings.receipt_printer_port || 9100}
                  onChange={(e) => handleSettingChange('receipt_printer_port', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Paper Size
                </label>
                <select
                  value={categorySettings.paper_size || '80mm'}
                  onChange={(e) => handleSettingChange('paper_size', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="58mm">58mm</option>
                  <option value="80mm">80mm</option>
                  <option value="112mm">112mm</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Copy Count
                </label>
                <input
                  type="number"
                  min="1"
                  max="5"
                  value={categorySettings.copy_count || 1}
                  onChange={(e) => handleSettingChange('copy_count', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.print_logo ?? true}
                  onChange={(e) => handleSettingChange('print_logo', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Print Logo</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.print_order_number ?? true}
                  onChange={(e) => handleSettingChange('print_order_number', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Print Order Number</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.print_date_time ?? true}
                  onChange={(e) => handleSettingChange('print_date_time', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Print Date & Time</span>
              </label>
            </div>
          </div>
        )

      case 'payment':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.cash_enabled ?? true}
                    onChange={(e) => handleSettingChange('cash_enabled', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Cash Payments</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.card_enabled ?? true}
                    onChange={(e) => handleSettingChange('card_enabled', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Card Payments</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.contactless_enabled ?? true}
                    onChange={(e) => handleSettingChange('contactless_enabled', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Contactless Payments</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={categorySettings.mobile_payments ?? true}
                    onChange={(e) => handleSettingChange('mobile_payments', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Mobile Payments</span>
                </label>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Minimum Card Amount (€)
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={categorySettings.minimum_card_amount || 0}
                    onChange={(e) => handleSettingChange('minimum_card_amount', parseFloat(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="flex items-center mb-2">
                    <input
                      type="checkbox"
                      checked={categorySettings.tip_enabled ?? true}
                      onChange={(e) => handleSettingChange('tip_enabled', e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300">Enable Tips</span>
                  </label>
                  
                  {categorySettings.tip_enabled && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Tip Percentages (comma-separated)
                      </label>
                      <input
                        type="text"
                        placeholder="15,18,20,25"
                        value={(categorySettings.tip_percentages || [15, 18, 20, 25]).join(',')}
                        onChange={(e) => {
                          const percentages = e.target.value.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p))
                          handleSettingChange('tip_percentages', percentages)
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )

      case 'tax':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Default Tax Rate (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  value={categorySettings.default_tax_rate || 8.25}
                  onChange={(e) => handleSettingChange('default_tax_rate', parseFloat(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tax Name
                </label>
                <input
                  type="text"
                  value={categorySettings.tax_name || 'VAT'}
                  onChange={(e) => handleSettingChange('tax_name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Calculation Method
                </label>
                <select
                  value={categorySettings.tax_calculation_method || 'percentage'}
                  onChange={(e) => handleSettingChange('tax_calculation_method', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="percentage">Percentage</option>
                  <option value="fixed">Fixed Amount</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tax Rounding
                </label>
                <select
                  value={categorySettings.tax_rounding || 'nearest_cent'}
                  onChange={(e) => handleSettingChange('tax_rounding', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="nearest_cent">Nearest Cent</option>
                  <option value="round_up">Round Up</option>
                  <option value="round_down">Round Down</option>
                </select>
              </div>
            </div>

            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.tax_inclusive ?? false}
                  onChange={(e) => handleSettingChange('tax_inclusive', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Tax Inclusive Pricing</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.auto_calculate_tax ?? true}
                  onChange={(e) => handleSettingChange('auto_calculate_tax', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Auto Calculate Tax</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.tax_exempt_enabled ?? true}
                  onChange={(e) => handleSettingChange('tax_exempt_enabled', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Allow Tax Exempt</span>
              </label>
            </div>
          </div>
        )

      case 'receipt':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Header Text
                </label>
                <textarea
                  rows={3}
                  value={categorySettings.header_text || 'Thank you for your order!'}
                  onChange={(e) => handleSettingChange('header_text', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Footer Text
                </label>
                <textarea
                  rows={3}
                  value={categorySettings.footer_text || 'Visit us again soon!'}
                  onChange={(e) => handleSettingChange('footer_text', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Receipt Width
                </label>
                <select
                  value={categorySettings.receipt_width || '80mm'}
                  onChange={(e) => handleSettingChange('receipt_width', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="58mm">58mm</option>
                  <option value="80mm">80mm</option>
                  <option value="112mm">112mm</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Font Size
                </label>
                <select
                  value={categorySettings.font_size || 'medium'}
                  onChange={(e) => handleSettingChange('font_size', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="small">Small</option>
                  <option value="medium">Medium</option>
                  <option value="large">Large</option>
                </select>
              </div>
            </div>

            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.show_logo ?? true}
                  onChange={(e) => handleSettingChange('show_logo', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Show Logo</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.show_qr_code ?? false}
                  onChange={(e) => handleSettingChange('show_qr_code', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Show QR Code</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.print_customer_copy ?? true}
                  onChange={(e) => handleSettingChange('print_customer_copy', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Print Customer Copy</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.email_receipts ?? false}
                  onChange={(e) => handleSettingChange('email_receipts', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Email Receipts</span>
              </label>
            </div>
          </div>
        )

      case 'restaurant':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Restaurant Name
                </label>
                <input
                  type="text"
                  value={categorySettings.restaurant_name || 'La Petite Crêperie'}
                  onChange={(e) => handleSettingChange('restaurant_name', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Phone Number
                </label>
                <input
                  type="text"
                  value={categorySettings.phone_number || '+33 1 23 45 67 89'}
                  onChange={(e) => handleSettingChange('phone_number', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Address
                </label>
                <textarea
                  rows={3}
                  value={categorySettings.address || '123 Rue de la Paix, 75001 Paris, France'}
                  onChange={(e) => handleSettingChange('address', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  value={categorySettings.email || '<EMAIL>'}
                  onChange={(e) => handleSettingChange('email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Website
                </label>
                <input
                  type="url"
                  value={categorySettings.website || 'https://lapetitecreperie.fr'}
                  onChange={(e) => handleSettingChange('website', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Cuisine Type
                </label>
                <select
                  value={categorySettings.cuisine_type || 'french'}
                  onChange={(e) => handleSettingChange('cuisine_type', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                >
                  <option value="french">French</option>
                  <option value="italian">Italian</option>
                  <option value="american">American</option>
                  <option value="asian">Asian</option>
                  <option value="mediterranean">Mediterranean</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Operating Hours
                </label>
                <input
                  type="text"
                  value={categorySettings.operating_hours || 'Mon-Sun: 8:00 AM - 10:00 PM'}
                  onChange={(e) => handleSettingChange('operating_hours', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.delivery_enabled ?? true}
                  onChange={(e) => handleSettingChange('delivery_enabled', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Delivery Service</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.takeout_enabled ?? true}
                  onChange={(e) => handleSettingChange('takeout_enabled', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Takeout Service</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.reservations_enabled ?? false}
                  onChange={(e) => handleSettingChange('reservations_enabled', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Table Reservations</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={categorySettings.loyalty_program_enabled ?? false}
                  onChange={(e) => handleSettingChange('loyalty_program_enabled', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Loyalty Program</span>
              </label>
            </div>
          </div>
        )

      default:
        return <div>Select a category to configure settings</div>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Real-time Connection Status */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {isConnected ? 'Live Sync Active' : 'Sync Disconnected'}
            </span>
            <span className="text-xs text-gray-500">
              Last update: {new Date(lastUpdate).toLocaleTimeString()}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-500">
              {terminalStatuses.filter(t => t.status === 'online').length}/{terminalStatuses.length} terminals online
            </span>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">System Overview</h3>

              {/* Overview Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Terminals</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {terminalStatuses.filter(t => t.status === 'online').length}
                      </p>
                    </div>
                    <Monitor className="w-8 h-8 text-blue-500" />
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Sync Success Rate</p>
                      <p className="text-2xl font-bold text-green-600">
                        {(syncStatus.successRate * 100).toFixed(1)}%
                      </p>
                    </div>
                    <CheckCircle className="w-8 h-8 text-green-500" />
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Syncs</p>
                      <p className="text-2xl font-bold text-yellow-600">
                        {syncStatus.pendingCount}
                      </p>
                    </div>
                    <Clock className="w-8 h-8 text-yellow-500" />
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">System Health</p>
                      <p className="text-2xl font-bold text-blue-600">
                        {(syncStatus.terminalHealth * 100).toFixed(0)}%
                      </p>
                    </div>
                    <Activity className="w-8 h-8 text-blue-500" />
                  </div>
                </div>
              </div>

              {/* Terminal Status Grid */}
              <div>
                <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">Terminal Status</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {terminalStatuses.map((terminal) => (
                    <div key={terminal.terminal_id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h5 className="font-medium text-gray-900 dark:text-white">{terminal.name}</h5>
                        <div className={`w-3 h-3 rounded-full ${
                          terminal.status === 'online' ? 'bg-green-500' :
                          terminal.status === 'offline' ? 'bg-red-500' :
                          terminal.status === 'syncing' ? 'bg-yellow-500' : 'bg-gray-500'
                        }`} />
                      </div>
                      <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex justify-between">
                          <span>CPU:</span>
                          <span>{terminal.system_info?.cpu_usage || 0}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Memory:</span>
                          <span>{terminal.system_info?.memory_usage || 0}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Pending:</span>
                          <span>{terminal.pending_sync_count || 0}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'terminals' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Terminal Management</h3>

              {/* Terminal Selection */}
              <div>
                <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">Select Terminal</h4>
                <div className="grid gap-4">
                  {terminals.map((terminal) => (
                    <div
                      key={terminal.terminal_id}
                      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                        selectedTerminal === terminal.terminal_id
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                      }`}
                      onClick={() => setSelectedTerminal(terminal.terminal_id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          {getTerminalStatusIcon(terminal.status)}
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-white">{terminal.name}</h4>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {terminal.terminal_id} • {terminal.ip_address} • {terminal.location}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            Last seen: {new Date(terminal.last_heartbeat).toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            Version: {terminal.version}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Settings Categories */}
              {selectedTerminal && (
                <div>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
                    <h4 className="text-md font-medium text-gray-900 dark:text-white">Terminal Settings</h4>
                    <div className="flex space-x-2 mt-4 sm:mt-0">
                      <button
                        onClick={saveSettings}
                        disabled={saving || !selectedTerminal}
                        className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {saving ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Save className="w-4 h-4" />}
                        <span>{saving ? 'Saving...' : 'Save Settings'}</span>
                      </button>

                      <button
                        onClick={syncAllSettings}
                        disabled={syncing || !selectedTerminal}
                        className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {syncing ? <RefreshCw className="w-4 h-4 animate-spin" /> : <RefreshCw className="w-4 h-4" />}
                        <span>{syncing ? 'Syncing...' : 'Sync All'}</span>
                      </button>
                    </div>
                  </div>

                  {/* Category Tabs */}
                  <div className="flex flex-wrap gap-2 mb-6">
                    {categories.map((category) => {
                      const Icon = category.icon
                      return (
                        <button
                          key={category.id}
                          onClick={() => setActiveCategory(category.id)}
                          className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                            activeCategory === category.id
                              ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                              : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
                          }`}
                        >
                          <Icon className="w-4 h-4" />
                          <span className="text-sm font-medium">{category.name}</span>
                        </button>
                      )
                    })}
                  </div>

                  {/* Settings Form */}
                  <div className="border-t border-gray-200 dark:border-gray-600 pt-6">
                    {renderTerminalSettings()}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'staff' && (
            <StaffPermissionsManager />
          )}

          {activeTab === 'hardware' && (
            <HardwareConfigurationPanel />
          )}

          {activeTab === 'inventory' && (
            <InventorySyncManager />
          )}

          {activeTab === 'sync' && (
            <SyncStatusMonitor />
          )}

          {activeTab === 'remote' && (
            <RemotePOSControl />
          )}

          {activeTab === 'orders' && (
            <LiveOrderMonitor />
          )}

          {activeTab === 'dev' && (
            <DeveloperTools />
          )}

          {activeTab === 'analytics' && (
            <AdvancedAnalytics />
          )}
        </div>
      </div>
    </div>
  )
}