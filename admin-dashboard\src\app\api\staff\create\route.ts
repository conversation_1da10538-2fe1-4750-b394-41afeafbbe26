import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      firstName, 
      lastName, 
      email, 
      phone, 
      roleId, 
      department, 
      canLoginPos, 
      canLoginAdmin, 
      pin,
      emergencyContactName,
      emergencyContactPhone 
    } = body

    // Validate required fields
    if (!firstName || !email || !roleId) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Create server-side Supabase client with service role
    const supabase = createServerSupabaseClient()
    
    // Log the current configuration for debugging
    console.log('Service role key available:', !!process.env.NEXT_PUBLIC_SUPABASE_SECRET)

    // Create staff member using the database function
    const { data: result, error: staffError } = await supabase
      .rpc('create_staff_member', {
        p_first_name: firstName,
        p_email: email,
        p_role_id: roleId,
        p_last_name: lastName || null,
        p_phone: phone || null,
        p_emergency_contact_name: emergencyContactName || null,
        p_emergency_contact_phone: emergencyContactPhone || null,
        p_department: department || null,
        p_can_login_pos: canLoginPos !== false,
        p_can_login_admin: canLoginAdmin === true,
        p_pin: pin || null
      })

    if (staffError) {
      console.error('Staff creation error:', staffError)
      return NextResponse.json(
        { error: 'Failed to create staff record', details: staffError.message },
        { status: 400 }
      )
    }

    // Check if the function returned an error
    if (result && !result.success) {
      console.error('Staff creation function error:', result.error)
      return NextResponse.json(
        { error: result.message || 'Failed to create staff record', details: result.error },
        { status: 400 }
      )
    }

    // Get the created staff record
    const { data: staffRecord, error: fetchError } = await supabase
      .from('staff')
      .select('*')
      .eq('id', result.staff_id)
      .single()

    if (fetchError) {
      console.error('Staff fetch error:', fetchError)
      return NextResponse.json(
        { error: 'Staff created but failed to fetch record', details: fetchError.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      staff: staffRecord,
      staff_code: result.staff_code,
      message: 'Staff member created successfully'
    })

  } catch (error) {
    console.error('Staff creation API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}