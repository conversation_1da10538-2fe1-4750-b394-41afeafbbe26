import React from 'react';
import toast from 'react-hot-toast';

interface Driver {
  id: string;
  name: string;
  phone: string;
  status: 'available' | 'busy' | 'offline';
}

interface DriverAssignmentModalProps {
  isOpen: boolean;
  orderCount: number;
  onDriverAssign: (driverId: string) => void;
  onClose: () => void;
}

// Mock drivers data
const mockDrivers: Driver[] = [
  { id: "1", name: "<PERSON>", phone: "555-0101", status: "available" },
  { id: "2", name: "<PERSON>", phone: "555-0102", status: "busy" },
  { id: "3", name: "<PERSON>", phone: "555-0103", status: "available" },
  { id: "4", name: "<PERSON>", phone: "555-0104", status: "offline" }
];

export const DriverAssignmentModal: React.FC<DriverAssignmentModalProps> = ({
  isOpen,
  orderCount,
  onDriverAssign,
  onClose
}) => {
  if (!isOpen) return null;

  const handleDriverSelect = (driverId: string) => {
    const driver = mockDrivers.find(d => d.id === driverId);
    if (driver && driver.status === 'available') {
      onDriverAssign(driverId);
    } else {
      toast.error('Selected driver is not available');
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-2xl p-6 w-full max-w-md border border-gray-200/50 dark:border-white/10">
        <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
          Assign Driver
        </h3>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          {orderCount} delivery order(s) need to be assigned to a driver before marking as delivered.
        </p>
        
        <div className="space-y-3 mb-6">
          {mockDrivers.map((driver) => (
            <button
              key={driver.id}
              onClick={() => handleDriverSelect(driver.id)}
              disabled={driver.status !== 'available'}
              className={`
                w-full p-3 rounded-lg border text-left transition-all duration-200
                ${driver.status === 'available'
                  ? 'border-blue-200 bg-blue-50 hover:bg-blue-100 text-blue-900 dark:border-blue-400/30 dark:bg-blue-500/10 dark:hover:bg-blue-500/20 dark:text-blue-200'
                  : 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed dark:border-gray-600 dark:bg-gray-800 dark:text-gray-500'
                }
              `}
            >
              <div className="font-medium">{driver.name}</div>
              <div className="text-sm opacity-75">{driver.phone} • {driver.status}</div>
            </button>
          ))}
        </div>
        
        <div className="flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default DriverAssignmentModal; 