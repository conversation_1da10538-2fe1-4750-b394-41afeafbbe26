/**
 * Notification Types and Interfaces
 * Shared types for real-time notifications across all applications
 */

export type NotificationType = 
  | 'new_order'
  | 'order_status_change'
  | 'order_ready'
  | 'order_cancelled'
  | 'delivery_assigned'
  | 'delivery_update'
  | 'kitchen_alert'
  | 'payment_received'
  | 'urgent'
  | 'system';

export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';

export type UserRole = 'customer' | 'admin' | 'kitchen' | 'delivery' | 'manager';

export interface BaseNotification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  priority: NotificationPriority;
  userId?: string;
  orderId?: string;
  actionRequired?: boolean;
  expiresAt?: string;
  metadata?: Record<string, any>;
}

export interface CustomerNotification extends BaseNotification {
  deliveryMethod?: 'push' | 'email' | 'sms';
  channels: ('push' | 'email' | 'sms')[];
  orderStatus?: string;
  estimatedTime?: string;
  driverInfo?: {
    name: string;
    phone: string;
    location?: { lat: number; lng: number };
  };
}

export interface StaffNotification extends BaseNotification {
  targetRoles: UserRole[];
  department?: 'kitchen' | 'delivery' | 'management' | 'all';
  soundAlert?: boolean;
  autoExpire?: boolean;
  orderDetails?: {
    orderNumber: string;
    customerName: string;
    items: string[];
    total: number;
    deliveryMethod: 'pickup' | 'delivery';
    specialInstructions?: string;
  };
}

export interface NotificationPreferences {
  userId: string;
  pushNotifications: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  orderUpdates: boolean;
  promotions: boolean;
  newsletters: boolean;
  soundAlerts: boolean;
  quietHours?: {
    enabled: boolean;
    startTime: string; // HH:MM format
    endTime: string;   // HH:MM format
  };
  deliveryUpdates: boolean;
  kitchenAlerts: boolean;
  newOrderAlerts: boolean;
}

export interface NotificationTemplate {
  id: string;
  type: NotificationType;
  name: string;
  title: string;
  message: string;
  channels: ('push' | 'email' | 'sms')[];
  variables: string[]; // Template variables like {orderNumber}, {customerName}
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PushSubscription {
  id: string;
  userId: string;
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
  userAgent?: string;
  createdAt: string;
  lastUsed?: string;
  isActive: boolean;
}

export interface NotificationLog {
  id: string;
  notificationId: string;
  userId: string;
  type: NotificationType;
  channel: 'push' | 'email' | 'sms';
  status: 'pending' | 'sent' | 'delivered' | 'failed' | 'read';
  sentAt?: string;
  deliveredAt?: string;
  readAt?: string;
  errorMessage?: string;
  retryCount: number;
  metadata?: Record<string, any>;
}

export interface NotificationStats {
  totalSent: number;
  totalDelivered: number;
  totalRead: number;
  totalFailed: number;
  deliveryRate: number;
  readRate: number;
  averageDeliveryTime: number;
  channelBreakdown: {
    push: { sent: number; delivered: number; read: number };
    email: { sent: number; delivered: number; read: number };
    sms: { sent: number; delivered: number; read: number };
  };
  typeBreakdown: Record<NotificationType, {
    sent: number;
    delivered: number;
    read: number;
  }>;
}

// Real-time subscription types
export interface RealtimeSubscription {
  id: string;
  userId: string;
  channel: string;
  event: string;
  filter?: Record<string, any>;
  isActive: boolean;
  createdAt: string;
  lastActivity?: string;
}

export interface RealtimeEvent {
  id: string;
  type: 'INSERT' | 'UPDATE' | 'DELETE';
  table: string;
  schema: string;
  old_record?: Record<string, any>;
  record?: Record<string, any>;
  eventTs: string;
}

// Notification service configuration
export interface NotificationServiceConfig {
  vapidKeys: {
    publicKey: string;
    privateKey: string;
  };
  emailConfig: {
    provider: 'sendgrid' | 'resend' | 'smtp';
    apiKey?: string;
    fromEmail: string;
    fromName: string;
  };
  smsConfig: {
    provider: 'twilio' | 'vonage';
    apiKey?: string;
    apiSecret?: string;
    fromNumber: string;
  };
  pushConfig: {
    fcmServerKey?: string;
    apnsConfig?: {
      keyId: string;
      teamId: string;
      bundleId: string;
      privateKey: string;
    };
  };
  retryConfig: {
    maxRetries: number;
    retryDelay: number;
    exponentialBackoff: boolean;
  };
}

// API request/response types
export interface SendNotificationRequest {
  userId?: string;
  userIds?: string[];
  type: NotificationType;
  title: string;
  message: string;
  channels: ('push' | 'email' | 'sms')[];
  priority?: NotificationPriority;
  orderId?: string;
  metadata?: Record<string, any>;
  scheduleAt?: string;
  expiresAt?: string;
}

export interface SendNotificationResponse {
  success: boolean;
  notificationId: string;
  message: string;
  failedChannels?: string[];
  errors?: Record<string, string>;
}

export interface GetNotificationsRequest {
  userId: string;
  limit?: number;
  offset?: number;
  type?: NotificationType;
  read?: boolean;
  priority?: NotificationPriority;
  startDate?: string;
  endDate?: string;
}

export interface GetNotificationsResponse {
  notifications: (CustomerNotification | StaffNotification)[];
  total: number;
  unreadCount: number;
  hasMore: boolean;
}

export interface UpdateNotificationRequest {
  notificationId: string;
  read?: boolean;
  metadata?: Record<string, any>;
}

export interface BulkUpdateNotificationsRequest {
  userId: string;
  notificationIds?: string[];
  markAllAsRead?: boolean;
  deleteAll?: boolean;
  type?: NotificationType;
}

// Webhook types for external integrations
export interface NotificationWebhook {
  id: string;
  url: string;
  events: NotificationType[];
  isActive: boolean;
  secret: string;
  retryConfig: {
    maxRetries: number;
    retryDelay: number;
  };
  headers?: Record<string, string>;
  createdAt: string;
  lastTriggered?: string;
}

export interface WebhookPayload {
  event: NotificationType;
  notification: CustomerNotification | StaffNotification;
  timestamp: string;
  signature: string;
}

// Error types
export class NotificationError extends Error {
  constructor(
    message: string,
    public code: string,
    public channel?: string,
    public userId?: string
  ) {
    super(message);
    this.name = 'NotificationError';
  }
}

export class RateLimitError extends NotificationError {
  constructor(channel: string, retryAfter: number) {
    super(`Rate limit exceeded for ${channel}. Retry after ${retryAfter}s`, 'RATE_LIMIT', channel);
    this.retryAfter = retryAfter;
  }
  
  retryAfter: number;
}

export class InvalidChannelError extends NotificationError {
  constructor(channel: string) {
    super(`Invalid notification channel: ${channel}`, 'INVALID_CHANNEL', channel);
  }
}

export class UserNotFoundError extends NotificationError {
  constructor(userId: string) {
    super(`User not found: ${userId}`, 'USER_NOT_FOUND', undefined, userId);
  }
}