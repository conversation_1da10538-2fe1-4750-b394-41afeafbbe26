export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          role: 'admin' | 'manager' | 'staff'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'admin' | 'manager' | 'staff'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'admin' | 'manager' | 'staff'
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          description: string | null
          image_url: string | null
          sort_order: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          image_url?: string | null
          sort_order?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          image_url?: string | null
          sort_order?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      subcategories: {
        Row: {
          id: string
          category_id: string
          name_en: string
          name_el: string | null
          description_en: string | null
          description_el: string | null
          price: number
          image_url: string | null
          allergens: string[] | null
          preparation_time: number
          is_available: boolean
          display_order: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          category_id: string
          name_en: string
          name_el?: string | null
          description_en?: string | null
          description_el?: string | null
          price: number
          image_url?: string | null
          allergens?: string[] | null
          preparation_time?: number
          is_available?: boolean
          display_order?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          category_id?: string
          name_en?: string
          name_el?: string | null
          description_en?: string | null
          description_el?: string | null
          price?: number
          image_url?: string | null
          allergens?: string[] | null
          preparation_time?: number
          is_available?: boolean
          display_order?: number
          created_at?: string
          updated_at?: string
        }
      }
      orders: {
        Row: {
          id: string
          customer_name: string | null
          customer_email: string | null
          customer_phone: string | null
          order_type: 'dine_in' | 'takeaway' | 'delivery'
          status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'completed' | 'cancelled'
          total_amount: number
          tax_amount: number
          discount_amount: number | null
          payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
          payment_method: string | null
          notes: string | null
          table_number: number | null
          estimated_ready_time: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          customer_name?: string | null
          customer_email?: string | null
          customer_phone?: string | null
          order_type: 'dine_in' | 'takeaway' | 'delivery'
          status?: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'completed' | 'cancelled'
          total_amount: number
          tax_amount: number
          discount_amount?: number | null
          payment_status?: 'pending' | 'paid' | 'failed' | 'refunded'
          payment_method?: string | null
          notes?: string | null
          table_number?: number | null
          estimated_ready_time?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          customer_name?: string | null
          customer_email?: string | null
          customer_phone?: string | null
          order_type?: 'dine_in' | 'takeaway' | 'delivery'
          status?: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'completed' | 'cancelled'
          total_amount?: number
          tax_amount?: number
          discount_amount?: number | null
          payment_status?: 'pending' | 'paid' | 'failed' | 'refunded'
          payment_method?: string | null
          notes?: string | null
          table_number?: number | null
          estimated_ready_time?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      order_items: {
        Row: {
          id: string
          order_id: string
          subcategory_id: string
          quantity: number
          unit_price: number
          total_price: number
          customizations: Json | null
          notes: string | null
          created_at: string
        }
        Insert: {
          id?: string
          order_id: string
          subcategory_id: string
          quantity: number
          unit_price: number
          total_price: number
          customizations?: Json | null
          notes?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          order_id?: string
          subcategory_id?: string
          quantity?: number
          unit_price?: number
          total_price?: number
          customizations?: Json | null
          notes?: string | null
          created_at?: string
        }
      }
      ingredients: {
        Row: {
          id: string
          category_id: string
          name: string
          description: string | null
          price: number
          cost: number
          image_url: string | null
          stock_quantity: number
          min_stock_level: number
          is_available: boolean
          allergens: string[] | null
          nutritional_info: Json | null
          display_order: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          category_id: string
          name: string
          description?: string | null
          price?: number
          cost?: number
          image_url?: string | null
          stock_quantity?: number
          min_stock_level?: number
          is_available?: boolean
          allergens?: string[] | null
          nutritional_info?: Json | null
          display_order?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          category_id?: string
          name?: string
          description?: string | null
          price?: number
          cost?: number
          image_url?: string | null
          stock_quantity?: number
          min_stock_level?: number
          is_available?: boolean
          allergens?: string[] | null
          nutritional_info?: Json | null
          display_order?: number
          created_at?: string
          updated_at?: string
        }
      }
      ingredient_categories: {
        Row: {
          id: string
          name: string
          description: string | null
          color_code: string
          display_order: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          color_code?: string
          display_order?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          color_code?: string
          display_order?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      order_status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'completed' | 'cancelled'
      order_type: 'dine_in' | 'takeaway' | 'delivery'
      payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
      user_role: 'admin' | 'manager' | 'staff'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}