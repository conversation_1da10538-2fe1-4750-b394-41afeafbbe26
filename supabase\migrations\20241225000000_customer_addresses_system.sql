-- Customer Addresses System Migration
-- Creates the customer_addresses table for storing multiple addresses per customer

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- CUSTOMER ADDRESSES TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS customer_addresses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID NOT NULL,
  
  -- Address information
  street_address VARCHAR(500) NOT NULL,
  city VARCHAR(100) NOT NULL DEFAULT 'Athens',
  postal_code VARCHAR(20),
  floor_number VARCHAR(50),
  notes TEXT,
  
  -- Address metadata
  address_type VARCHAR(20) NOT NULL DEFAULT 'delivery' CHECK (address_type IN ('delivery', 'home', 'work', 'other')),
  is_default BOOLEAN NOT NULL DEFAULT FALSE,
  
  -- System metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for customer_addresses
CREATE INDEX IF NOT EXISTS idx_customer_addresses_customer_id ON customer_addresses (customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_addresses_type ON customer_addresses (address_type);
CREATE INDEX IF NOT EXISTS idx_customer_addresses_default ON customer_addresses (is_default);
CREATE INDEX IF NOT EXISTS idx_customer_addresses_created_at ON customer_addresses (created_at);

-- Add foreign key constraint to customers table (if it exists)
-- Note: Using conditional constraint creation since customers table structure may vary
DO $$
BEGIN
  -- Check if customers table exists and add foreign key if it does
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'customers') THEN
    -- Try to add foreign key constraint
    BEGIN
      ALTER TABLE customer_addresses 
      ADD CONSTRAINT fk_customer_addresses_customer_id 
      FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE;
    EXCEPTION WHEN OTHERS THEN
      -- If foreign key fails, just add a comment
      COMMENT ON COLUMN customer_addresses.customer_id IS 'References customers.id - foreign key constraint may need to be added manually';
    END;
  END IF;
END $$;

-- =============================================
-- TRIGGERS AND FUNCTIONS
-- =============================================

-- Function to ensure only one default address per customer
CREATE OR REPLACE FUNCTION ensure_single_default_address()
RETURNS TRIGGER AS $$
BEGIN
  -- If this address is being set as default
  IF NEW.is_default = TRUE THEN
    -- Set all other addresses for this customer to non-default
    UPDATE customer_addresses 
    SET is_default = FALSE, updated_at = NOW()
    WHERE customer_id = NEW.customer_id AND id != NEW.id;
  END IF;
  
  -- Update the updated_at timestamp
  NEW.updated_at = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for the ensure_single_default_address function
CREATE TRIGGER trigger_ensure_single_default_address
  BEFORE INSERT OR UPDATE ON customer_addresses
  FOR EACH ROW
  EXECUTE FUNCTION ensure_single_default_address();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_customer_addresses_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updating updated_at
CREATE TRIGGER trigger_update_customer_addresses_updated_at
  BEFORE UPDATE ON customer_addresses
  FOR EACH ROW
  EXECUTE FUNCTION update_customer_addresses_updated_at();

-- =============================================
-- SAMPLE DATA (Optional - for development)
-- =============================================

-- Add some sample addresses if customers exist
-- This is safe to run as it uses INSERT ... ON CONFLICT DO NOTHING
INSERT INTO customer_addresses (customer_id, street_address, city, postal_code, address_type, is_default)
SELECT 
  'daecd92c-a7cc-45fc-b53c-b3938edd8f29'::uuid as customer_id,
  'Κωνσταντινουπόλεως 62' as street_address,
  'Thessaloniki' as city,
  '54642' as postal_code,
  'home' as address_type,
  true as is_default
WHERE EXISTS (
  SELECT 1 FROM information_schema.tables WHERE table_name = 'customers'
)
ON CONFLICT DO NOTHING;

-- =============================================
-- COMMENTS AND DOCUMENTATION
-- =============================================

COMMENT ON TABLE customer_addresses IS 'Stores multiple delivery addresses for customers';
COMMENT ON COLUMN customer_addresses.customer_id IS 'Reference to the customer who owns this address';
COMMENT ON COLUMN customer_addresses.street_address IS 'The street address line';
COMMENT ON COLUMN customer_addresses.city IS 'City name, defaults to Athens';
COMMENT ON COLUMN customer_addresses.postal_code IS 'Postal/ZIP code';
COMMENT ON COLUMN customer_addresses.floor_number IS 'Floor number or apartment details';
COMMENT ON COLUMN customer_addresses.notes IS 'Additional delivery notes or instructions';
COMMENT ON COLUMN customer_addresses.address_type IS 'Type of address: delivery, home, work, or other';
COMMENT ON COLUMN customer_addresses.is_default IS 'Whether this is the default address for the customer'; 