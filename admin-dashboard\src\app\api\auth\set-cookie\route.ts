import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const { sessionToken, rememberMe } = await request.json()

    if (!sessionToken) {
      return NextResponse.json(
        { error: 'Session token is required' },
        { status: 400 }
      )
    }

    // Create response
    const response = NextResponse.json({ success: true })

    // Set secure cookie
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      path: '/',
      maxAge: rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60, // 30 days or 24 hours
    }

    response.cookies.set('auth_session', sessionToken, cookieOptions)

    return response
  } catch (error) {
    console.error('Cookie setting error:', error)
    return NextResponse.json(
      { error: 'Failed to set authentication cookie' },
      { status: 500 }
    )
  }
}

export async function DELETE() {
  try {
    const response = NextResponse.json({ success: true })
    
    // Clear the auth cookie
    response.cookies.delete('auth_session')
    
    return response
  } catch (error) {
    console.error('Cookie deletion error:', error)
    return NextResponse.json(
      { error: 'Failed to clear authentication cookie' },
      { status: 500 }
    )
  }
}