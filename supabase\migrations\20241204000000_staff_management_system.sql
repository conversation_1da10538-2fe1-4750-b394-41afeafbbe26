-- Staff Management System Migration
-- Creates comprehensive staff management, roles, permissions, and activity tracking

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- ROLES TABLE (Enhanced)
-- =============================================
CREATE TABLE IF NOT EXISTS roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(50) NOT NULL UNIQUE,
  display_name VARCHAR(100) NOT NULL,
  description TEXT,
  level INTEGER NOT NULL DEFAULT 5, -- 1=highest (admin), 5=lowest (customer)
  color VARCHAR(7) DEFAULT '#6B7280', -- hex color for UI
  is_system_role BOOLEAN NOT NULL DEFAULT FALSE, -- can't be deleted
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- PERMISSIONS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL UNIQUE,
  display_name VARCHAR(150) NOT NULL,
  description TEXT,
  category VARCHAR(50) NOT NULL, -- 'admin', 'manager', 'staff', 'pos', 'reports', etc.
  resource VARCHAR(50), -- 'users', 'products', 'orders', 'inventory', etc.
  action VARCHAR(50), -- 'read', 'write', 'delete', 'access', etc.
  is_system_permission BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- ROLE PERMISSIONS JUNCTION TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS role_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
  permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
  granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  granted_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  UNIQUE(role_id, permission_id)
);

-- =============================================
-- STAFF TABLE (Enhanced)
-- =============================================
CREATE TABLE IF NOT EXISTS staff (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  staff_code VARCHAR(20) UNIQUE, -- auto-generated staff code
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE,
  phone VARCHAR(20),
  role_id UUID REFERENCES roles(id) ON DELETE SET NULL,
  branch_id UUID REFERENCES branches(id) ON DELETE SET NULL,
  department VARCHAR(50), -- 'kitchen', 'front', 'management', 'delivery'
  employment_type VARCHAR(30) DEFAULT 'full-time', -- 'full-time', 'part-time', 'contract'
  hire_date DATE,
  termination_date DATE,
  hourly_rate DECIMAL(8,2),
  pin_hash VARCHAR(255), -- encrypted PIN for POS access
  pin_attempts INTEGER DEFAULT 0,
  pin_locked_until TIMESTAMP WITH TIME ZONE,
  emergency_contact_name VARCHAR(200),
  emergency_contact_phone VARCHAR(20),
  notes TEXT,
  avatar_url TEXT,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  can_login_pos BOOLEAN NOT NULL DEFAULT TRUE,
  can_login_admin BOOLEAN NOT NULL DEFAULT FALSE,
  last_login_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- =============================================
-- STAFF PERMISSIONS (Individual Overrides)
-- =============================================
CREATE TABLE IF NOT EXISTS staff_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  staff_id UUID REFERENCES staff(id) ON DELETE CASCADE,
  permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
  granted BOOLEAN NOT NULL, -- true=grant, false=revoke (override role permission)
  granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  granted_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  expires_at TIMESTAMP WITH TIME ZONE, -- optional expiration
  reason TEXT,
  UNIQUE(staff_id, permission_id)
);

-- =============================================
-- STAFF SESSIONS (POS Login Sessions)
-- =============================================
CREATE TABLE IF NOT EXISTS staff_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  staff_id UUID REFERENCES staff(id) ON DELETE CASCADE,
  branch_id UUID REFERENCES branches(id) ON DELETE SET NULL,
  terminal_id VARCHAR(100),
  session_token VARCHAR(255) NOT NULL UNIQUE,
  platform VARCHAR(50) NOT NULL, -- 'pos-system', 'admin-dashboard'
  ip_address INET,
  user_agent TEXT,
  login_method VARCHAR(30) DEFAULT 'pin', -- 'pin', 'email_password', 'biometric'
  login_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  logout_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  forced_logout BOOLEAN NOT NULL DEFAULT FALSE,
  logout_reason VARCHAR(100), -- 'manual', 'timeout', 'forced', 'system'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- STAFF ACTIVITY LOGS
-- =============================================
CREATE TABLE IF NOT EXISTS staff_activity_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  staff_id UUID REFERENCES staff(id) ON DELETE SET NULL,
  session_id UUID REFERENCES staff_sessions(id) ON DELETE SET NULL,
  activity_type VARCHAR(50) NOT NULL, -- 'login', 'logout', 'order_create', 'refund', 'discount', etc.
  resource_type VARCHAR(50), -- 'order', 'product', 'customer', 'payment', etc.
  resource_id UUID, -- ID of the affected resource
  action VARCHAR(50) NOT NULL, -- 'create', 'update', 'delete', 'view', 'approve', etc.
  details JSONB DEFAULT '{}', -- additional activity details
  result VARCHAR(20) DEFAULT 'success', -- 'success', 'failed', 'denied'
  ip_address INET,
  user_agent TEXT,
  branch_id UUID REFERENCES branches(id) ON DELETE SET NULL,
  terminal_id VARCHAR(100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- STAFF SCHEDULES (Optional for future expansion)
-- =============================================
CREATE TABLE IF NOT EXISTS staff_schedules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  staff_id UUID REFERENCES staff(id) ON DELETE CASCADE,
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6), -- 0=Sunday
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  break_start TIME,
  break_end TIME,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  effective_from DATE DEFAULT CURRENT_DATE,
  effective_until DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES
-- =============================================

-- Roles indexes
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles (name);
CREATE INDEX IF NOT EXISTS idx_roles_level ON roles (level);
CREATE INDEX IF NOT EXISTS idx_roles_active ON roles (is_active);

-- Permissions indexes
CREATE INDEX IF NOT EXISTS idx_permissions_name ON permissions (name);
CREATE INDEX IF NOT EXISTS idx_permissions_category_resource ON permissions (category, resource);
CREATE INDEX IF NOT EXISTS idx_permissions_action ON permissions (action);

-- Role permissions indexes
CREATE INDEX IF NOT EXISTS idx_role_permissions_role ON role_permissions (role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission ON role_permissions (permission_id);

-- Staff indexes
CREATE INDEX IF NOT EXISTS idx_staff_user_id ON staff (user_id);
CREATE INDEX IF NOT EXISTS idx_staff_staff_code ON staff (staff_code);
CREATE INDEX IF NOT EXISTS idx_staff_email ON staff (email);
CREATE INDEX IF NOT EXISTS idx_staff_role_branch ON staff (role_id, branch_id);
CREATE INDEX IF NOT EXISTS idx_staff_active ON staff (is_active);
CREATE INDEX IF NOT EXISTS idx_staff_department ON staff (department);

-- Staff permissions indexes
CREATE INDEX IF NOT EXISTS idx_staff_permissions_staff ON staff_permissions (staff_id);
CREATE INDEX IF NOT EXISTS idx_staff_permissions_permission ON staff_permissions (permission_id);
CREATE INDEX IF NOT EXISTS idx_staff_permissions_granted ON staff_permissions (granted);

-- Staff sessions indexes
CREATE INDEX IF NOT EXISTS idx_staff_sessions_staff ON staff_sessions (staff_id);
CREATE INDEX IF NOT EXISTS idx_staff_sessions_active ON staff_sessions (is_active);
CREATE INDEX IF NOT EXISTS idx_staff_sessions_platform ON staff_sessions (platform);
CREATE INDEX IF NOT EXISTS idx_staff_sessions_terminal ON staff_sessions (terminal_id);
CREATE INDEX IF NOT EXISTS idx_staff_sessions_login_at ON staff_sessions (login_at);

-- Activity logs indexes
CREATE INDEX IF NOT EXISTS idx_staff_activity_staff ON staff_activity_logs (staff_id);
CREATE INDEX IF NOT EXISTS idx_staff_activity_session ON staff_activity_logs (session_id);
CREATE INDEX IF NOT EXISTS idx_staff_activity_type_action ON staff_activity_logs (activity_type, action);
CREATE INDEX IF NOT EXISTS idx_staff_activity_created_at ON staff_activity_logs (created_at);
CREATE INDEX IF NOT EXISTS idx_staff_activity_resource ON staff_activity_logs (resource_type, resource_id);

-- Staff schedules indexes
CREATE INDEX IF NOT EXISTS idx_staff_schedules_staff_branch ON staff_schedules (staff_id, branch_id);
CREATE INDEX IF NOT EXISTS idx_staff_schedules_day ON staff_schedules (day_of_week);
CREATE INDEX IF NOT EXISTS idx_staff_schedules_active ON staff_schedules (is_active);

-- =============================================
-- INITIAL DATA SEEDING
-- =============================================

-- Insert system roles
INSERT INTO roles (id, name, display_name, description, level, color, is_system_role) VALUES
  ('4a6c9d1d-ca7d-4849-be08-2e4cd2475ec8', 'admin', 'Administrator', 'Full system access and management', 1, '#DC2626', true),
  ('5b7d0e2e-db8e-4959-cf19-3f5de3586fc9', 'manager', 'Manager', 'Branch and staff management', 2, '#D97706', true),
  ('6c8e1f3f-ec9f-4a6a-d02a-4061e4697fda', 'supervisor', 'Supervisor', 'Shift supervision and limited management', 3, '#059669', true),
  ('7d9f2040-fd07-4b7b-e13b-5172f5708feb', 'staff', 'Staff Member', 'Standard staff access', 4, '#3B82F6', true),
  ('8ea03151-0e18-4c8c-f24c-6283061910fc', 'customer', 'Customer', 'Customer-facing apps access', 5, '#6B7280', true)
ON CONFLICT (id) DO NOTHING;

-- Insert system permissions
INSERT INTO permissions (name, display_name, description, category, resource, action, is_system_permission) VALUES
  -- Admin permissions
  ('admin.full_access', 'Full System Access', 'Complete access to all system features', 'admin', 'system', 'access', true),
  ('admin.user_management', 'User Management', 'Manage all users and staff accounts', 'admin', 'users', 'manage', true),
  ('admin.system_settings', 'System Settings', 'Configure system-wide settings', 'admin', 'settings', 'manage', true),
  ('admin.reports', 'Admin Reports', 'Access all reports and analytics', 'admin', 'reports', 'access', true),
  ('admin.access', 'Admin Dashboard Access', 'Login to admin dashboard', 'admin', 'dashboard', 'access', true),
  
  -- Manager permissions
  ('manager.branch_management', 'Branch Management', 'Manage branch operations and settings', 'manager', 'branches', 'manage', true),
  ('manager.staff_management', 'Staff Management', 'Manage staff within assigned branches', 'manager', 'staff', 'manage', true),
  ('manager.inventory', 'Inventory Management', 'Full inventory control', 'manager', 'inventory', 'manage', true),
  ('manager.reports', 'Manager Reports', 'Access branch and staff reports', 'manager', 'reports', 'read', true),
  
  -- Staff permissions
  ('staff.pos_access', 'POS System Access', 'Access POS system functions', 'staff', 'pos', 'access', true),
  ('staff.order_management', 'Order Management', 'Create and manage customer orders', 'staff', 'orders', 'manage', true),
  ('staff.inventory_view', 'View Inventory', 'View inventory levels and products', 'staff', 'inventory', 'read', true),
  
  -- POS specific permissions
  ('pos.access', 'POS Access', 'Basic POS system access', 'pos', 'pos', 'access', true),
  ('pos.refunds', 'Process Refunds', 'Process customer refunds', 'pos', 'payments', 'refund', true),
  ('pos.discounts', 'Apply Discounts', 'Apply discounts to orders', 'pos', 'orders', 'discount', true),
  ('pos.void_transactions', 'Void Transactions', 'Void completed transactions', 'pos', 'transactions', 'void', true),
  ('pos.cash_drawer', 'Cash Drawer Access', 'Open cash drawer', 'pos', 'cash', 'access', true),
  ('pos.reports', 'POS Reports', 'Access POS reports and summaries', 'pos', 'reports', 'read', true),
  ('pos.price_override', 'Price Override', 'Override product prices', 'pos', 'products', 'price_override', true),
  
  -- CRUD permissions
  ('users.read', 'Read Users', 'View user information', 'users', 'users', 'read', true),
  ('users.write', 'Write Users', 'Create and edit users', 'users', 'users', 'write', true),
  ('users.delete', 'Delete Users', 'Delete user accounts', 'users', 'users', 'delete', true),
  ('products.read', 'Read Products', 'View products and menu', 'products', 'products', 'read', true),
  ('products.write', 'Write Products', 'Create and edit products', 'products', 'products', 'write', true),
  ('products.delete', 'Delete Products', 'Delete products', 'products', 'products', 'delete', true),
  ('orders.read', 'Read Orders', 'View customer orders', 'orders', 'orders', 'read', true),
  ('orders.write', 'Write Orders', 'Create and edit orders', 'orders', 'orders', 'write', true),
  ('orders.delete', 'Delete Orders', 'Cancel or delete orders', 'orders', 'orders', 'delete', true),
  ('inventory.read', 'Read Inventory', 'View inventory information', 'inventory', 'inventory', 'read', true),
  ('inventory.write', 'Write Inventory', 'Update inventory levels', 'inventory', 'inventory', 'write', true),
  ('reports.read', 'Read Reports', 'View reports and analytics', 'reports', 'reports', 'read', true),
  ('reports.write', 'Write Reports', 'Generate and export reports', 'reports', 'reports', 'write', true),
  ('settings.read', 'Read Settings', 'View system settings', 'settings', 'settings', 'read', true),
  ('settings.write', 'Write Settings', 'Modify system settings', 'settings', 'settings', 'write', true),
  
  -- Customer permissions
  ('customer.order_placement', 'Place Orders', 'Place orders through customer apps', 'customer', 'orders', 'create', true),
  ('customer.order_history', 'Order History', 'View order history', 'customer', 'orders', 'history', true),
  ('customer.loyalty_points', 'Loyalty Points', 'View and use loyalty points', 'customer', 'loyalty', 'access', true)
ON CONFLICT (name) DO NOTHING;

-- Assign default permissions to roles
INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'admin' AND p.category = 'admin'
ON CONFLICT DO NOTHING;

INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'admin' AND p.name IN ('users.read', 'users.write', 'users.delete', 'products.read', 'products.write', 'products.delete', 'orders.read', 'orders.write', 'orders.delete', 'inventory.read', 'inventory.write', 'reports.read', 'reports.write', 'settings.read', 'settings.write', 'pos.access')
ON CONFLICT DO NOTHING;

INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'manager' AND p.category IN ('manager', 'pos')
ON CONFLICT DO NOTHING;

INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'manager' AND p.name IN ('users.read', 'users.write', 'products.read', 'products.write', 'orders.read', 'orders.write', 'inventory.read', 'inventory.write', 'reports.read', 'settings.read')
ON CONFLICT DO NOTHING;

INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'staff' AND p.category = 'staff'
ON CONFLICT DO NOTHING;

INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'staff' AND p.name IN ('products.read', 'orders.read', 'orders.write', 'inventory.read', 'pos.access')
ON CONFLICT DO NOTHING;

INSERT INTO role_permissions (role_id, permission_id) 
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'customer' AND p.category = 'customer'
ON CONFLICT DO NOTHING;

-- =============================================
-- FUNCTIONS
-- =============================================

-- Function to generate staff code
CREATE OR REPLACE FUNCTION generate_staff_code()
RETURNS TEXT AS $$
DECLARE
  code TEXT;
  counter INTEGER := 1;
BEGIN
  LOOP
    code := 'STF' || LPAD(counter::TEXT, 4, '0');
    EXIT WHEN NOT EXISTS (SELECT 1 FROM staff WHERE staff_code = code);
    counter := counter + 1;
  END LOOP;
  RETURN code;
END;
$$ LANGUAGE plpgsql;

-- Function to verify staff PIN
CREATE OR REPLACE FUNCTION verify_staff_pin(staff_pin TEXT)
RETURNS TABLE(success BOOLEAN, staff_id UUID, role_name TEXT, branch_id UUID) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    CASE WHEN s.pin_hash IS NOT NULL AND crypt(staff_pin, s.pin_hash) = s.pin_hash THEN TRUE ELSE FALSE END as success,
    s.id as staff_id,
    r.name as role_name,
    s.branch_id
  FROM staff s
  LEFT JOIN roles r ON s.role_id = r.id
  WHERE s.is_active = TRUE 
    AND s.can_login_pos = TRUE
    AND (s.pin_locked_until IS NULL OR s.pin_locked_until < NOW())
    AND s.pin_hash IS NOT NULL
    AND crypt(staff_pin, s.pin_hash) = s.pin_hash
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to set staff PIN
CREATE OR REPLACE FUNCTION set_staff_pin(staff_uuid UUID, new_pin TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE staff 
  SET pin_hash = crypt(new_pin, gen_salt('bf')),
      pin_attempts = 0,
      pin_locked_until = NULL,
      updated_at = NOW()
  WHERE id = staff_uuid;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check staff permissions
CREATE OR REPLACE FUNCTION staff_has_permission(staff_uuid UUID, permission_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  has_perm BOOLEAN := FALSE;
BEGIN
  -- Check role permissions first
  SELECT EXISTS (
    SELECT 1 FROM staff s
    JOIN role_permissions rp ON s.role_id = rp.role_id
    JOIN permissions p ON rp.permission_id = p.id
    WHERE s.id = staff_uuid AND p.name = permission_name
  ) INTO has_perm;
  
  -- Check individual permission overrides
  IF EXISTS (
    SELECT 1 FROM staff_permissions sp
    JOIN permissions p ON sp.permission_id = p.id
    WHERE sp.staff_id = staff_uuid 
      AND p.name = permission_name
      AND (sp.expires_at IS NULL OR sp.expires_at > NOW())
  ) THEN
    -- Get the individual permission setting (grant or revoke)
    SELECT sp.granted INTO has_perm
    FROM staff_permissions sp
    JOIN permissions p ON sp.permission_id = p.id
    WHERE sp.staff_id = staff_uuid 
      AND p.name = permission_name
      AND (sp.expires_at IS NULL OR sp.expires_at > NOW())
    ORDER BY sp.granted_at DESC
    LIMIT 1;
  END IF;
  
  RETURN has_perm;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log staff activity
CREATE OR REPLACE FUNCTION log_staff_activity(
  staff_uuid UUID,
  session_uuid UUID,
  activity_type_param VARCHAR(50),
  resource_type_param VARCHAR(50),
  resource_id_param UUID,
  action_param VARCHAR(50),
  details_param JSONB DEFAULT '{}',
  result_param VARCHAR(20) DEFAULT 'success'
)
RETURNS UUID AS $$
DECLARE
  activity_id UUID;
  staff_branch_id UUID;
  staff_terminal VARCHAR(100);
BEGIN
  -- Get staff branch and terminal from session if available
  IF session_uuid IS NOT NULL THEN
    SELECT branch_id, terminal_id INTO staff_branch_id, staff_terminal
    FROM staff_sessions 
    WHERE id = session_uuid;
  END IF;
  
  -- If no session, get branch from staff record
  IF staff_branch_id IS NULL THEN
    SELECT branch_id INTO staff_branch_id
    FROM staff 
    WHERE id = staff_uuid;
  END IF;
  
  INSERT INTO staff_activity_logs (
    staff_id, session_id, activity_type, resource_type, resource_id, 
    action, details, result, branch_id, terminal_id
  ) VALUES (
    staff_uuid, session_uuid, activity_type_param, resource_type_param, resource_id_param,
    action_param, details_param, result_param, staff_branch_id, staff_terminal
  ) RETURNING id INTO activity_id;
  
  RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to auto-generate staff code
CREATE OR REPLACE FUNCTION trigger_generate_staff_code()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.staff_code IS NULL OR NEW.staff_code = '' THEN
    NEW.staff_code := generate_staff_code();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER staff_code_generation_trigger
  BEFORE INSERT ON staff
  FOR EACH ROW EXECUTE FUNCTION trigger_generate_staff_code();

-- =============================================
-- ROW LEVEL SECURITY (RLS)
-- =============================================

-- Enable RLS on all tables
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_schedules ENABLE ROW LEVEL SECURITY;

-- RLS Policies for roles table
CREATE POLICY "Anyone can view roles" ON roles FOR SELECT USING (TRUE);
CREATE POLICY "Admins can manage roles" ON roles FOR ALL USING (
  EXISTS (
    SELECT 1 FROM staff s 
    JOIN role_permissions rp ON s.role_id = rp.role_id
    JOIN permissions p ON rp.permission_id = p.id
    WHERE s.user_id = auth.uid() AND p.name = 'admin.user_management'
  )
);

-- RLS Policies for permissions table
CREATE POLICY "Anyone can view permissions" ON permissions FOR SELECT USING (TRUE);
CREATE POLICY "Admins can manage permissions" ON permissions FOR ALL USING (
  EXISTS (
    SELECT 1 FROM staff s 
    JOIN role_permissions rp ON s.role_id = rp.role_id
    JOIN permissions p ON rp.permission_id = p.id
    WHERE s.user_id = auth.uid() AND p.name = 'admin.user_management'
  )
);

-- RLS Policies for staff table
CREATE POLICY "Staff can view their own record" ON staff FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Managers can view staff in their branches" ON staff FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM staff s 
    WHERE s.user_id = auth.uid() 
      AND (
        -- Same branch manager
        (s.branch_id = staff.branch_id AND EXISTS (
          SELECT 1 FROM role_permissions rp 
          JOIN permissions p ON rp.permission_id = p.id
          WHERE rp.role_id = s.role_id AND p.name = 'manager.staff_management'
        ))
        -- Or admin
        OR EXISTS (
          SELECT 1 FROM role_permissions rp 
          JOIN permissions p ON rp.permission_id = p.id
          WHERE rp.role_id = s.role_id AND p.name = 'admin.user_management'
        )
      )
  )
);

-- Service role policies (for system access)
CREATE POLICY "Service role full access - roles" ON roles FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access - permissions" ON permissions FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access - role_permissions" ON role_permissions FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access - staff" ON staff FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access - staff_permissions" ON staff_permissions FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access - staff_sessions" ON staff_sessions FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access - staff_activity_logs" ON staff_activity_logs FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access - staff_schedules" ON staff_schedules FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- =============================================
-- REALTIME SUBSCRIPTIONS
-- =============================================

-- Enable realtime for staff management tables
ALTER PUBLICATION supabase_realtime ADD TABLE roles;
ALTER PUBLICATION supabase_realtime ADD TABLE permissions;
ALTER PUBLICATION supabase_realtime ADD TABLE role_permissions;
ALTER PUBLICATION supabase_realtime ADD TABLE staff;
ALTER PUBLICATION supabase_realtime ADD TABLE staff_permissions;
ALTER PUBLICATION supabase_realtime ADD TABLE staff_sessions;
ALTER PUBLICATION supabase_realtime ADD TABLE staff_activity_logs;