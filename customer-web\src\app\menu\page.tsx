'use client';

import { useState, useEffect } from 'react';
import { CategoryGrid } from '@/components/menu/category-grid';
import { MenuItemGrid } from '@/components/menu/menu-item-grid';
import { useCategories, useSubcategories } from '@/hooks/api-hooks';
import { Category, MenuItem } from '@/types/menu';

export default function MenuPage() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const { categories, loading: categoriesLoading, error: categoriesError } = useCategories();
  const { 
    subcategories, 
    loading: subcategoriesLoading, 
    error: subcategoriesError,
    fetchMostFrequentedSubcategories 
  } = useSubcategories();

  // Fetch most frequented subcategories when component mounts
  useEffect(() => {
    if (!selectedCategory) {
      fetchMostFrequentedSubcategories(6);
    }
  }, [selectedCategory, fetchMostFrequentedSubcategories]);

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
  };

  const handleBackToCategories = () => {
    setSelectedCategory(null);
    // Fetch most frequented subcategories again
    fetchMostFrequentedSubcategories(6);
  };

  // Show loading state
  if (categoriesLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
        </div>
      </div>
    );
  }

  // Show error state
  if (categoriesError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center text-red-600">
          <h2 className="text-xl font-semibold mb-2">Error Loading Menu</h2>
          <p>{categoriesError}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {!selectedCategory ? (
        <div>
          {/* Categories Section */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-6">Our Menu</h1>
            <CategoryGrid 
              categories={categories} 
              onCategorySelect={handleCategorySelect} 
            />
          </div>

          {/* Most Frequented Subcategories Section */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Popular Items</h2>
            {subcategoriesLoading ? (
              <div className="flex items-center justify-center min-h-[200px]">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
              </div>
            ) : subcategoriesError ? (
              <div className="text-center text-red-600">
                <p>Error loading popular items: {subcategoriesError}</p>
              </div>
            ) : subcategories.length > 0 ? (
              <MenuItemGrid 
                items={subcategories}
                onItemSelect={(item) => {
                  // Handle item selection - could navigate to item detail or add to cart
                  console.log('Selected item:', item);
                }}
              />
            ) : (
              <div className="text-center text-gray-500 py-8">
                <p>No popular items available at the moment.</p>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div>
          {/* Category-specific items view */}
          <div className="mb-6">
            <button
              onClick={handleBackToCategories}
              className="flex items-center text-orange-600 hover:text-orange-700 mb-4"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Categories
            </button>
            <h1 className="text-3xl font-bold text-gray-900">
              {categories.find(cat => cat.id === selectedCategory)?.name || 'Category'}
            </h1>
          </div>
          
          {/* This would show items for the selected category */}
          <div className="text-center text-gray-500 py-8">
            <p>Category items will be displayed here.</p>
            <p className="text-sm mt-2">Selected category ID: {selectedCategory}</p>
          </div>
        </div>
      )}
    </div>
  );
}
