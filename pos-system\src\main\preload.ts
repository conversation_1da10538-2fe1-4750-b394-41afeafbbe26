import { contextB<PERSON>, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define types for electron API
interface ElectronAPI {
  // Window controls
  minimize: () => Promise<void>;
  maximize: () => Promise<void>;
  close: () => Promise<void>;

  // Settings
  getSettings: () => Promise<any>;
  updateSettings: (settings: Record<string, any>) => Promise<void>;

  // Database
  executeQuery: (query: string, params?: any[]) => Promise<any>;

  // Auth
  login: (credentials: LoginCredentials) => Promise<any>;
  logout: () => Promise<void>;

  // System
  getSystemInfo: () => Promise<SystemInfo>;

  // Development helpers
  openDevTools: () => Promise<void>;
  reload: () => Promise<void>;

  // Sync Event Listeners
  onSettingsUpdate: (callback: (data: any) => void) => void;
  onStaffPermissionUpdate: (callback: (data: any) => void) => void;
  onHardwareConfigUpdate: (callback: (data: any) => void) => void;
  onRestartRequired: (callback: (data: any) => void) => void;
  onSyncError: (callback: (error: any) => void) => void;
  onSyncComplete: (callback: (data: any) => void) => void;

  // Sync Status Methods
  getSyncStatus: () => Promise<any>;
  onSyncStatus: (callback: (status: any) => void) => void;
  onNetworkStatus: (callback: (status: any) => void) => void;

  // Sync Action Methods
  requestRestart: () => Promise<void>;
  forceSync: () => Promise<void>;
  openSyncLogs: () => Promise<void>;

  // Event Listener Cleanup Methods
  removeSettingsUpdateListener: () => void;
  removeStaffPermissionUpdateListener: () => void;
  removeHardwareConfigUpdateListener: () => void;
  removeRestartRequiredListener: () => void;
  removeSyncErrorListener: () => void;
  removeSyncCompleteListener: () => void;
  removeSyncStatusListener: () => void;
  removeNetworkStatusListener: () => void;
}

interface LoginCredentials {
  username?: string;
  password?: string;
  pin?: string;
  staffId?: string;
}

interface SystemInfo {
  platform: string;
  version: string;
  arch: string;
  appVersion: string;
}

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Window controls
  minimize: () => ipcRenderer.invoke('window-minimize'),
  maximize: () => ipcRenderer.invoke('window-maximize'),
  close: () => ipcRenderer.invoke('window-close'),

  // Settings
  getSettings: () => ipcRenderer.invoke('get-settings'),
  updateSettings: (settings: Record<string, any>) => ipcRenderer.invoke('update-settings', settings),

  // Database
  executeQuery: (query: string, params?: any[]) => ipcRenderer.invoke('execute-query', query, params),

  // Auth
  login: (credentials: LoginCredentials) => ipcRenderer.invoke('auth-login', credentials),
  logout: () => ipcRenderer.invoke('auth-logout'),

  // System
  getSystemInfo: () => ipcRenderer.invoke('get-system-info'),

  // Development helpers
  openDevTools: () => ipcRenderer.invoke('open-dev-tools'),
  reload: () => ipcRenderer.invoke('reload'),

  // Sync Event Listeners
  onSettingsUpdate: (callback: (data: any) => void) => {
    ipcRenderer.on('settings-update', (_, data) => callback(data));
  },
  onStaffPermissionUpdate: (callback: (data: any) => void) => {
    ipcRenderer.on('staff-permission-update', (_, data) => callback(data));
  },
  onHardwareConfigUpdate: (callback: (data: any) => void) => {
    ipcRenderer.on('hardware-config-update', (_, data) => callback(data));
  },
  onRestartRequired: (callback: (data: any) => void) => {
    ipcRenderer.on('restart-required', (_, data) => callback(data));
  },
  onSyncError: (callback: (error: any) => void) => {
    ipcRenderer.on('sync-error', (_, error) => callback(error));
  },
  onSyncComplete: (callback: (data: any) => void) => {
    ipcRenderer.on('sync-complete', (_, data) => callback(data));
  },

  // Sync Status Methods
  getSyncStatus: () => ipcRenderer.invoke('get-sync-status'),
  onSyncStatus: (callback: (status: any) => void) => {
    ipcRenderer.on('sync-status', (_, status) => callback(status));
  },
  onNetworkStatus: (callback: (status: any) => void) => {
    ipcRenderer.on('network-status', (_, status) => callback(status));
  },

  // Sync Action Methods
  requestRestart: () => ipcRenderer.invoke('request-restart'),
  forceSync: () => ipcRenderer.invoke('force-sync'),
  openSyncLogs: () => ipcRenderer.invoke('open-sync-logs'),

  // Event Listener Cleanup Methods
  removeSettingsUpdateListener: () => {
    ipcRenderer.removeAllListeners('settings-update');
  },
  removeStaffPermissionUpdateListener: () => {
    ipcRenderer.removeAllListeners('staff-permission-update');
  },
  removeHardwareConfigUpdateListener: () => {
    ipcRenderer.removeAllListeners('hardware-config-update');
  },
  removeRestartRequiredListener: () => {
    ipcRenderer.removeAllListeners('restart-required');
  },
  removeSyncErrorListener: () => {
    ipcRenderer.removeAllListeners('sync-error');
  },
  removeSyncCompleteListener: () => {
    ipcRenderer.removeAllListeners('sync-complete');
  },
  removeSyncStatusListener: () => {
    ipcRenderer.removeAllListeners('sync-status');
  },
  removeNetworkStatusListener: () => {
    ipcRenderer.removeAllListeners('network-status');
  },
} as ElectronAPI);