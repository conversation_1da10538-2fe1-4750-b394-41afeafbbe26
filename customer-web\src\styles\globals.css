@tailwind base;
@tailwind components;
@tailwind utilities;

@import './glassmorphism.css';

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 42 100% 50%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 42 100% 50%;
    --radius: 0.5rem;

    /* Creperie Theme Colors */
    --crepe-brown: 30 70% 45%;
    --crepe-light: 40 100% 97%;
    --chocolate: 25 75% 30%;
    --strawberry: 350 100% 60%;
    --blueberry: 240 100% 60%;
    --vanilla: 48 100% 93%;
    --caramel: 30 90% 60%;
  }

  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 0 0% 95%;
    --card: 24 9.8% 10%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 95%;
    --primary: 42 100% 50%;
    --primary-foreground: 144.9 80.4% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 12 6.5% 15.1%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 42 100% 50%;

    /* Creperie Theme Colors - Dark Mode */
    --crepe-brown: 30 60% 35%;
    --crepe-light: 40 30% 87%;
    --chocolate: 25 65% 25%;
    --strawberry: 350 70% 50%;
    --blueberry: 240 70% 50%;
    --vanilla: 48 30% 83%;
    --caramel: 30 70% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}

/* Custom styles for food items */
.food-item-card {
  transition:
    transform 0.2s ease-in-out,
    box-shadow 0.2s ease-in-out;
}

.food-item-card:hover {
  transform: translateY(-4px);
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

/* Animation for page transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-transition {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Hero section gradient overlay */
.hero-gradient {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.7));
}

/* Custom styles for allergen tags */
.allergen-tag {
  @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  background-color: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
}

/* Custom styles for ingredient lists */
.ingredients-list li {
  @apply flex items-center space-x-2 text-sm;
}

.ingredients-list li::before {
  content: '•';
  @apply text-primary;
}

/* Custom styles for order status indicators */
.status-pending {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

.status-preparing {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
}

.status-ready {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.status-delivered {
  @apply bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200;
}

.status-cancelled {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

/* Responsive font sizes */
@media (max-width: 640px) {
  h1 {
    @apply text-3xl;
  }
  h2 {
    @apply text-2xl;
  }
  h3 {
    @apply text-xl;
  }
}
