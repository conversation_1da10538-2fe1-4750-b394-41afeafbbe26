import { create } from 'zustand';
import { toast } from 'react-hot-toast';
import { Order, OrderItem, OrderPricing, OrderStatus, OrderType, PaymentStatus, PaymentMethod } from '../types/orders';
import { 
  PricingCalculationRequest, 
  PricingCalculationResponse,
  DeliveryValidationRequest,
  DeliveryValidationResponse,
  PricingBreakdown,
  ORDER_TYPES 
} from '../../../../shared/types/pricing';

// Define payment data interface
interface PaymentData {
  method: Order['paymentMethod'];
  amount: number;
  cardNumber?: string;
  expiryDate?: string;
  cvv?: string;
  holderName?: string;
  reference?: string;
  [key: string]: any;
}

// Define the order store interface
interface OrderStore {
  orders: Order[];
  selectedOrder: Order | null;
  isLoading: boolean;
  filter: {
    status: string;
    orderType: string;
    searchTerm: string;
  };
  
  // Cached/computed values - removed to prevent setState during render
  // _filteredOrders: Order[] | null;
  // _orderCounts: { pending: number; preparing: number; ready: number; completed: number; cancelled: number } | null;
  
  // Actions
  initializeOrders: () => Promise<void>;
  loadOrders: () => Promise<void>;
  getOrderById: (orderId: string) => Promise<Order | null>;
  updateOrderStatus: (orderId: string, status: Order['status']) => Promise<boolean>;
  createOrder: (orderData: Partial<Order>) => Promise<{ success: boolean; orderId?: string }>;
  setSelectedOrder: (order: Order | null) => void;
  setFilter: (filter: Partial<OrderStore['filter']>) => void;
  getFilteredOrders: () => Order[];
  getOrderCounts: () => { pending: number; preparing: number; ready: number; completed: number; cancelled: number };
  refreshOrders: () => Promise<void>;
  updatePaymentStatus: (orderId: string, paymentStatus: NonNullable<Order['paymentStatus']>, paymentMethod?: Order['paymentMethod'], transactionId?: string) => Promise<boolean>;
  processPayment: (orderId: string, paymentData: PaymentData) => Promise<{ success: boolean; transactionId?: string; error?: string }>;
  
  // Kitchen operations
  updatePreparationStatus: (orderId: string, status: 'preparing' | 'ready' | 'completed') => Promise<boolean>;
  printKitchenTicket: (orderId: string) => Promise<{ success: boolean; error?: string }>;
  getKitchenOrders: () => Order[];
  updateEstimatedTime: (orderId: string, estimatedTime: number) => Promise<boolean>;
  
  // Pricing methods
  calculateOrderPricing: (orderData: {
    items: OrderItem[];
    orderType: OrderType;
    address?: { lat: number; lng: number };
    branchId?: string;
  }) => Promise<PricingCalculationResponse>;
  validateDeliveryAddress: (request: DeliveryValidationRequest) => Promise<DeliveryValidationResponse>;
  updateOrderPricing: (orderId: string, pricing: PricingBreakdown) => Promise<boolean>;
  recalculateOrderTotal: (orderId: string) => Promise<boolean>;
  
  // Internal methods
  _invalidateCache: () => void;
  _cleanup: () => void;
}

// Event listener cleanup registry
let eventListeners: Array<() => void> = [];

// Create the order store with performance optimizations
export const useOrderStore = create<OrderStore>()((set, get) => ({
    orders: [],
    selectedOrder: null,
    isLoading: false,
    filter: {
      status: 'all',
      orderType: 'all',
      searchTerm: ''
    },

    // Cached values - removed to prevent setState during render
    // _filteredOrders: null,
    // _orderCounts: null,

    _invalidateCache: () => {
      // Cache invalidation removed to prevent setState during render
      // Components should use direct state access instead of cached methods
    },

    _cleanup: () => {
      // Clean up all event listeners
      eventListeners.forEach(cleanup => cleanup());
      eventListeners = [];
    },

    initializeOrders: async () => {
      // Check if already initialized to prevent multiple calls
      const state = get();
      if (state.isLoading) {
        return; // Already initializing
      }
      
      await get().loadOrders();
      // Note: Real-time updates removed as they depend on electron API features not currently available
    },

    loadOrders: async () => {
      set({ isLoading: true });
      
      try {
        const { OrderService } = await import('../../services/OrderService');
        const orderService = OrderService.getInstance();
        const orders = await orderService.fetchOrders();
        
        set({ orders, isLoading: false });
        get()._invalidateCache();
      } catch (error) {
        console.error('Failed to load orders:', error);
        set({ isLoading: false });
        toast.error('Failed to load orders');
      }
    },

    // Optimized filtered orders without caching to prevent setState during render
    getFilteredOrders: () => {
      const state = get();
      
      let filtered = state.orders;
      
      // Apply status filter
      if (state.filter.status !== 'all') {
        filtered = filtered.filter(order => order.status === state.filter.status);
      }
      
      // Apply order type filter
      if (state.filter.orderType !== 'all') {
        filtered = filtered.filter(order => order.orderType === state.filter.orderType);
      }
      
      // Apply search filter
      if (state.filter.searchTerm) {
        const searchTerm = state.filter.searchTerm.toLowerCase();
        filtered = filtered.filter(order => 
          order.orderNumber.toLowerCase().includes(searchTerm) ||
          order.customerName?.toLowerCase().includes(searchTerm) ||
          order.customerPhone?.includes(searchTerm)
        );
      }

      return filtered;
    },

    // Optimized order counts without caching to prevent setState during render
    getOrderCounts: () => {
      const state = get();

      const counts = state.orders.reduce((acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        pending: counts.pending || 0,
        preparing: counts.preparing || 0,
        ready: counts.ready || 0,
        completed: counts.completed || 0,
        cancelled: counts.cancelled || 0
      };
    },

    setFilter: (newFilter: Partial<OrderStore['filter']>) => {
      set((state) => ({
        filter: { ...state.filter, ...newFilter }
      }));
      // Cache invalidation removed - no longer needed
    },

    getOrderById: async (orderId: string) => {
      try {
        // Use local state since electron API is simplified
        const state = get();
        return state.orders.find(order => order.id === orderId) || null;
      } catch (error) {
        console.error('Failed to get order by ID:', error);
        return null;
      }
    },

    updateOrderStatus: async (orderId: string, status: Order['status']) => {
      try {
        const { OrderService } = await import('../../services/OrderService');
        const orderService = OrderService.getInstance();
        
        await orderService.updateOrderStatus(orderId, status);
        
        // Update local state after successful API call
        set((state) => ({
          orders: state.orders.map(order => 
            order.id === orderId 
              ? { ...order, status, updatedAt: new Date().toISOString() }
              : order
          )
        }));
        // Cache invalidation removed - no longer needed
        return true;
      } catch (error) {
        console.error('Failed to update order status:', error);
        toast.error('Failed to update order status');
        return false;
      }
    },

    createOrder: async (orderData: Partial<Order>) => {
      try {
        const { OrderService } = await import('../../services/OrderService');
        const orderService = OrderService.getInstance();
        
        const newOrder = await orderService.createOrder(orderData);
        
        // Add to local state after successful API call
        set((state) => ({ 
          orders: [newOrder, ...state.orders]
        }));
        // Cache invalidation removed - no longer needed
        return { success: true, orderId: newOrder.id };
      } catch (error) {
        console.error('Failed to create order:', error);
        toast.error('Failed to create order');
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
      }
    },

    setSelectedOrder: (order) => set({ selectedOrder: order }),

    refreshOrders: async () => {
      await get().loadOrders();
    },

         updatePaymentStatus: async (orderId: string, paymentStatus: NonNullable<Order['paymentStatus']>, paymentMethod?: Order['paymentMethod'], transactionId?: string) => {
       try {
         // Update local state optimistically
         set((state) => ({
           orders: state.orders.map(order => 
             order.id === orderId 
               ? { 
                   ...order, 
                   paymentStatus, 
                   paymentMethod: paymentMethod || order.paymentMethod,
                   paymentTransactionId: transactionId || order.paymentTransactionId,
                   updatedAt: new Date().toISOString()
                 }
               : order
           )
         }));
         // Cache invalidation removed - no longer needed
         
         // TODO: Sync with backend when payment API is available
         return true;
       } catch (error) {
         console.error('Failed to update payment status:', error);
         return false;
       }
     },

     processPayment: async (orderId: string, paymentData: PaymentData) => {
       try {
         // Simulate payment processing for now
         const transactionId = `txn_${Date.now()}`;
         await get().updatePaymentStatus(orderId, 'completed', paymentData.method, transactionId);
         return { success: true, transactionId };
       } catch (error) {
         console.error('Failed to process payment:', error);
         return { success: false, error: 'Payment processing failed' };
       }
     },

    updatePreparationStatus: async (orderId: string, status: 'preparing' | 'ready' | 'completed') => {
      return await get().updateOrderStatus(orderId, status);
    },

         printKitchenTicket: async (orderId: string) => {
       try {
         // Simulate printing for now - TODO: implement kitchen printing API
         // Kitchen ticket printing would be implemented here
         return { success: true };
       } catch (error) {
         console.error('Failed to print kitchen ticket:', error);
         return { success: false, error: 'Printing failed' };
       }
     },

    getKitchenOrders: () => {
      const state = get();
      return state.orders.filter(order => 
        ['pending', 'preparing', 'ready'].includes(order.status)
      );
    },

         updateEstimatedTime: async (orderId: string, estimatedTime: number) => {
       try {
         // Update local state optimistically
         set((state) => ({
           orders: state.orders.map(order => 
             order.id === orderId 
               ? { ...order, estimatedTime, updatedAt: new Date().toISOString() }
               : order
           )
         }));
         // Cache invalidation removed - no longer needed
         
         // TODO: Sync with backend when estimated time API is available
         return true;
       } catch (error) {
         console.error('Failed to update estimated time:', error);
         return false;
       }
     },

    // Pricing methods implementation
    calculateOrderPricing: async (orderData) => {
      try {
        const { MenuService } = await import('../services/MenuService');
        const menuService = MenuService.getInstance();
        
        // Calculate subtotal from items
        const subtotal = orderData.items.reduce((total, item) => total + (item.price * item.quantity), 0);
        
        const request: PricingCalculationRequest = {
          orderType: orderData.orderType === 'takeaway' ? ORDER_TYPES.TAKEAWAY : 
                     orderData.orderType === 'delivery' ? ORDER_TYPES.DELIVERY : ORDER_TYPES.TAKEAWAY,
          subtotal,
          address: orderData.address,
          branchId: orderData.branchId
        };
        
        const response = await menuService.calculateOrderPricing(request);
        return response;
      } catch (error) {
        console.error('Failed to calculate order pricing:', error);
        return {
          success: false,
          orderType: orderData.orderType === 'takeaway' ? ORDER_TYPES.TAKEAWAY : ORDER_TYPES.DELIVERY,
          pricing: {
            subtotal: orderData.items.reduce((total, item) => total + (item.price * item.quantity), 0),
            deliveryFee: 0,
            pickupDiscount: 0,
            serviceFee: 0,
            taxAmount: 0,
            totalAmount: orderData.items.reduce((total, item) => total + (item.price * item.quantity), 0)
          },
          error: 'Pricing calculation failed'
        };
      }
    },

    validateDeliveryAddress: async (request) => {
      try {
        const { MenuService } = await import('../services/MenuService');
        const menuService = MenuService.getInstance();
        
        const response = await menuService.validateDeliveryAddress(request);
        return response;
      } catch (error) {
        console.error('Failed to validate delivery address:', error);
        return {
          success: false,
          deliveryAvailable: false,
          reason: 'VALIDATION_ERROR',
          message: 'Unable to validate delivery address. Please try again or contact support.',
          suggestion: 'Please verify your address or try pickup instead'
        };
      }
    },

    updateOrderPricing: async (orderId, pricing) => {
      try {
        const state = get();
        const order = state.orders.find(o => o.id === orderId);
        
        if (!order) {
          console.error('Order not found:', orderId);
          return false;
        }

        // Update local state with new pricing
        set((state) => ({
          orders: state.orders.map(order => 
            order.id === orderId 
              ? { 
                  ...order, 
                  totalAmount: pricing.totalAmount,
                  subtotal: pricing.subtotal,
                  deliveryFee: pricing.deliveryFee,
                  pickupDiscount: pricing.pickupDiscount,
                  serviceFee: pricing.serviceFee,
                  taxAmount: pricing.taxAmount,
                  pricingCalculatedAt: new Date().toISOString(),
                  pricingVersion: '1.0',
                  updatedAt: new Date().toISOString(),
                  pricing: {
                    subtotal: pricing.subtotal,
                    deliveryFee: pricing.deliveryFee,
                    pickupDiscount: pricing.pickupDiscount,
                    serviceFee: pricing.serviceFee,
                    taxAmount: pricing.taxAmount,
                    totalAmount: pricing.totalAmount,
                    deliveryZoneId: pricing.deliveryZone?.id || null,
                    deliveryZoneName: pricing.deliveryZone?.name || null,
                    pricingCalculatedAt: new Date().toISOString(),
                    pricingVersion: '1.0',
                    estimatedTime: pricing.estimatedTime
                  }
                }
              : order
          )
        }));

        // TODO: Sync with backend when order pricing update API is available
        // For now, just update local state
        
        return true;
      } catch (error) {
        console.error('Failed to update order pricing:', error);
        return false;
      }
    },

    recalculateOrderTotal: async (orderId) => {
      try {
        const state = get();
        const order = state.orders.find(o => o.id === orderId);
        
        if (!order) {
          console.error('Order not found:', orderId);
          return false;
        }

        // Get order items and calculate pricing
        const orderData = {
          items: order.items,
          orderType: order.orderType,
          address: order.address ? { lat: 0, lng: 0 } : undefined, // TODO: Parse address to coordinates
          branchId: undefined // TODO: Get branch ID from context
        };

        const pricingResponse = await get().calculateOrderPricing(orderData);
        
        if (pricingResponse.success) {
          await get().updateOrderPricing(orderId, pricingResponse.pricing);
          toast.success('Order total recalculated');
          return true;
        } else {
          toast.error('Failed to recalculate order total');
          return false;
        }
      } catch (error) {
        console.error('Failed to recalculate order total:', error);
        toast.error('Failed to recalculate order total');
        return false;
      }
    }
  }));

// Export cleanup function for component unmounting
export const cleanupOrderStore = () => {
  const store = useOrderStore.getState();
  store._cleanup();
};