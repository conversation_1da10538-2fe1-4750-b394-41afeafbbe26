import React from 'react';
import { cn } from '../../utils/cn';
import { Plus } from 'lucide-react';

interface FloatingActionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  className?: string;
  icon?: React.ReactNode;
}

const FloatingActionButton = React.forwardRef<HTMLButtonElement, FloatingActionButtonProps>(
  ({ className, icon, ...props }, ref) => {
    return (
      <button
        ref={ref}
        className={cn(
          'fixed bottom-6 right-6 z-50',
          'w-16 h-16 rounded-full',
          'bg-pos-primary text-white',
          'flex items-center justify-center',
          'shadow-lg hover:shadow-xl',
          'transform transition-all duration-300 ease-in-out',
          'hover:scale-110 hover:bg-blue-600',
          'active:scale-95',
          'focus:outline-none focus:ring-4 focus:ring-blue-300',
          className
        )}
        {...props}
      >
        {icon || <Plus size={32} />}
      </button>
    );
  }
);

FloatingActionButton.displayName = 'FloatingActionButton';

export { FloatingActionButton }; 