// Supabase MCP Tool Alternative
// This script provides the same functionality as the MCP Supabase tools
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_CONFIG = {
  url: 'https://voiwzwyfnkzvcffuxpwl.supabase.co',
  anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0MDU2MzIsImV4cCI6MjA2NDk4MTYzMn0.RvabQ6EGP1gBb0w8J8_VpaqqXgkpEFR7Nbbkmw11wNA',
  serviceRoleKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMTg0NjgzMywiZXhwIjoyMDQ3NDIyODMzfQ.qPWQJLa-C8vPOZBq9FjvC0yKjG6kN8N8jQZ9XGZ8Z0k'
};

class SupabaseMCPAlternative {
  constructor() {
    this.client = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.anonKey);
    this.adminClient = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.serviceRoleKey);
  }

  // Equivalent to mcp_supabase-mcp_list_projects
  async listProjects() {
    console.log('📋 Listing Supabase projects...');
    const projectInfo = {
      id: SUPABASE_CONFIG.url.match(/https:\/\/(.+)\.supabase\.co/)?.[1],
      name: 'The Small',
      region: 'eu-central-1',
      status: 'active',
      url: SUPABASE_CONFIG.url
    };
    console.log('✅ Project found:', JSON.stringify(projectInfo, null, 2));
    return [projectInfo];
  }

  // Equivalent to mcp_supabase-mcp_list_tables
  async listTables(projectId = null) {
    console.log('📋 Listing tables...');
    try {
      // Get table information using REST API
      const response = await fetch(`${SUPABASE_CONFIG.url}/rest/v1/`, {
        method: 'OPTIONS',
        headers: {
          'apikey': SUPABASE_CONFIG.anonKey,
          'Authorization': `Bearer ${SUPABASE_CONFIG.anonKey}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Test known tables
      const knownTables = [
        'branches', 'orders', 'subcategories', 'customers', 'order_items',
        'delivery_zones', 'menu_categories', 'ingredients', 'staff',
        'user_profiles', 'customer_loyalty_points', 'gdpr_requests',
        'branch_special_hours'
      ];

      const accessibleTables = [];

      for (const tableName of knownTables) {
        try {
          const { data, error } = await this.client
            .from(tableName)
            .select('*')
            .limit(1);

          if (!error) {
            // Get table schema
            const { data: schemaData } = await this.client
              .from(tableName)
              .select('*')
              .limit(0);

            accessibleTables.push({
              name: tableName,
              schema: 'public',
              accessible: true,
              row_count_estimate: data?.length || 0
            });
          }
        } catch (err) {
          // Table not accessible
        }
      }

      console.log(`✅ Found ${accessibleTables.length} accessible tables`);
      accessibleTables.forEach(table => {
        console.log(`   - ${table.name}`);
      });

      return accessibleTables;
    } catch (error) {
      console.error('❌ Error listing tables:', error.message);
      return [];
    }
  }

  // Equivalent to mcp_supabase-mcp_execute_sql
  async executeSQL(projectId, query) {
    console.log('🔧 Executing SQL query...');
    console.log('Query:', query);
    
    try {
      const { data, error } = await this.adminClient.rpc('exec_sql', {
        sql: query
      });

      if (error) {
        console.error('❌ SQL execution failed:', error.message);
        return { error };
      }

      console.log('✅ SQL executed successfully');
      console.log('Result:', data);
      return { data };
    } catch (err) {
      console.error('❌ SQL execution error:', err.message);
      return { error: err.message };
    }
  }

  // Equivalent to mcp_supabase-mcp_apply_migration
  async applyMigration(projectId, name, query) {
    console.log(`🔧 Applying migration: ${name}`);
    console.log('Migration SQL:', query);
    
    try {
      // Execute the migration
      const result = await this.executeSQL(projectId, query);
      
      if (result.error) {
        console.error('❌ Migration failed:', result.error);
        return result;
      }

      console.log('✅ Migration applied successfully');
      return { success: true, data: result.data };
    } catch (err) {
      console.error('❌ Migration error:', err.message);
      return { error: err.message };
    }
  }

  // Get project URL
  async getProjectUrl(projectId) {
    return SUPABASE_CONFIG.url;
  }

  // Get anon key
  async getAnonKey(projectId) {
    return SUPABASE_CONFIG.anonKey;
  }

  // Test delivery zones functionality
  async testDeliveryZones() {
    console.log('🔍 Testing delivery_zones table...');
    
    try {
      // Check if table exists and is accessible
      const { data, error } = await this.client
        .from('delivery_zones')
        .select('*')
        .limit(5);

      if (error) {
        console.error('❌ delivery_zones table error:', error.message);
        
        // Try to create the table
        console.log('🔧 Attempting to create delivery_zones table...');
        const createTableSQL = `
          CREATE TABLE IF NOT EXISTS public.delivery_zones (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name VARCHAR(255) NOT NULL,
            description TEXT,
            coordinates JSONB NOT NULL,
            delivery_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            minimum_order_amount DECIMAL(10,2) DEFAULT 0.00,
            estimated_delivery_time_min INTEGER DEFAULT 30,
            estimated_delivery_time_max INTEGER DEFAULT 60,
            is_active BOOLEAN DEFAULT true,
            priority INTEGER DEFAULT 0,
            color VARCHAR(7) DEFAULT '#3B82F6',
            branch_id UUID,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW(),
            created_by UUID,
            updated_by UUID
          );
          
          ALTER TABLE public.delivery_zones ENABLE ROW LEVEL SECURITY;
          
          DROP POLICY IF EXISTS "Enable read access for all users" ON public.delivery_zones;
          CREATE POLICY "Enable read access for all users" ON public.delivery_zones
            FOR SELECT USING (true);
            
          DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.delivery_zones;
          CREATE POLICY "Enable insert for authenticated users" ON public.delivery_zones
            FOR INSERT WITH CHECK (auth.role() = 'authenticated');
        `;
        
        const createResult = await this.applyMigration(null, 'create_delivery_zones', createTableSQL);
        
        if (createResult.error) {
          return { error: createResult.error };
        }
        
        // Test again
        const { data: newData, error: newError } = await this.client
          .from('delivery_zones')
          .select('*')
          .limit(5);
          
        if (newError) {
          return { error: newError.message };
        }
        
        data = newData;
      }

      console.log('✅ delivery_zones table is accessible');
      console.log(`📊 Current records: ${data?.length || 0}`);
      
      if (data && data.length > 0) {
        console.log('Sample data:', JSON.stringify(data[0], null, 2));
      }

      return { success: true, data };
    } catch (err) {
      console.error('❌ delivery_zones test failed:', err.message);
      return { error: err.message };
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  const mcp = new SupabaseMCPAlternative();
  
  console.log('🚀 Supabase MCP Alternative Tool\n');
  
  switch (command) {
    case 'list-projects':
      await mcp.listProjects();
      break;
      
    case 'list-tables':
      await mcp.listTables();
      break;
      
    case 'test-delivery-zones':
      await mcp.testDeliveryZones();
      break;
      
    case 'execute-sql':
      const query = args[1];
      if (!query) {
        console.error('❌ Please provide SQL query as second argument');
        process.exit(1);
      }
      await mcp.executeSQL(null, query);
      break;
      
    case 'get-project-url':
      const url = await mcp.getProjectUrl();
      console.log('Project URL:', url);
      break;
      
    case 'get-anon-key':
      const key = await mcp.getAnonKey();
      console.log('Anon Key:', key);
      break;
      
    default:
      console.log('Available commands:');
      console.log('  list-projects        - List Supabase projects');
      console.log('  list-tables          - List database tables');
      console.log('  test-delivery-zones  - Test delivery zones table');
      console.log('  execute-sql "query"  - Execute SQL query');
      console.log('  get-project-url      - Get project URL');
      console.log('  get-anon-key         - Get anonymous key');
      console.log('\nExample:');
      console.log('  node supabase-mcp-alternative.js list-tables');
      console.log('  node supabase-mcp-alternative.js execute-sql "SELECT * FROM delivery_zones LIMIT 5"');
      break;
  }
}

// Export for use as module
module.exports = SupabaseMCPAlternative;

// Run CLI if called directly
if (require.main === module) {
  main().catch(console.error);
}