#!/usr/bin/env node

// Supabase MCP Wrapper
// Use this script instead of the broken MCP tools

const SupabaseMCPFix = require('./supabase-mcp-fix.js');

const mcp = new SupabaseMCPFix();

// Export all MCP functions for easy use
module.exports = {
  // Core MCP functions
  listProjects: () => mcp.listProjects(),
  getProject: (id) => mcp.getProject(id),
  listTables: (projectId, schemas) => mcp.listTables(projectId, schemas),
  executeSQL: (projectId, query) => mcp.executeSQL(projectId, query),
  applyMigration: (projectId, name, query) => mcp.applyMigration(projectId, name, query),
  listMigrations: (projectId) => mcp.listMigrations(projectId),
  getProjectUrl: (projectId) => mcp.getProjectUrl(projectId),
  getAnonKey: (projectId) => mcp.getAnonKey(projectId),
  
  // Helper functions
  testConnection: () => mcp.testConnection(),
  fixDeliveryZones: () => mcp.fixDeliveryZones(),
  
  // Direct client access
  client: mcp.client,
  adminClient: mcp.adminClient,
  projectId: mcp.projectId
};

// If called directly, show usage
if (require.main === module) {
  console.log('🔧 Supabase MCP Wrapper');
  console.log('');
  console.log('This module provides working alternatives to the broken MCP Supabase tools.');
  console.log('');
  console.log('Usage in your code:');
  console.log('  const supabase = require("./supabase-mcp-wrapper.js");');
  console.log('  const projects = await supabase.listProjects();');
  console.log('  const tables = await supabase.listTables();');
  console.log('  const result = await supabase.executeSQL(null, "SELECT * FROM delivery_zones");');
  console.log('');
  console.log('Available functions:');
  console.log('  - listProjects()');
  console.log('  - getProject(id)');
  console.log('  - listTables(projectId, schemas)');
  console.log('  - executeSQL(projectId, query)');
  console.log('  - applyMigration(projectId, name, query)');
  console.log('  - listMigrations(projectId)');
  console.log('  - getProjectUrl(projectId)');
  console.log('  - getAnonKey(projectId)');
  console.log('  - testConnection()');
  console.log('  - fixDeliveryZones()');
  console.log('');
  console.log('For CLI usage, use: node supabase-mcp-fix.js [command]');
}