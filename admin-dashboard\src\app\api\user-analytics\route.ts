import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30d'
    
    // Calculate date range
    const now = new Date()
    const startDate = new Date()
    
    switch (timeRange) {
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        startDate.setDate(now.getDate() - 30)
    }

    // Get basic user counts
    const [totalUsersResult, activeUsersResult, newUsersResult] = await Promise.all([
      supabase
        .from('user_profiles')
        .select('id', { count: 'exact' }),
      
      supabase
        .from('user_profiles')
        .select('id', { count: 'exact' })
        .eq('is_active', true),
      
      supabase
        .from('user_profiles')
        .select('id', { count: 'exact' })
        .gte('created_at', now.toISOString().split('T')[0])
    ])

    // Get order analytics for average order value
    const { data: ordersData } = await supabase
      .from('orders')
      .select('total_amount')
      .gte('created_at', startDate.toISOString())

    const avgOrderValue = ordersData && ordersData.length > 0
      ? ordersData.reduce((sum: number, order: any) => sum + (order.total_amount || 0), 0) / ordersData.length
      : 0

    // Get top customers
    const { data: topCustomers } = await supabase
      .from('user_profiles')
      .select(`
        id,
        full_name,
        email,
        total_orders,
        loyalty_points,
        avatar_url
      `)
      .order('total_orders', { ascending: false })
      .limit(10)

    // Get user growth data
    const userGrowthData = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(now.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]
      
      const { count } = await supabase
        .from('user_profiles')
        .select('id', { count: 'exact' })
        .eq('created_at', dateStr)
      
      userGrowthData.push({
        date: dateStr,
        count: count || 0
      })
    }

    // Get loyalty points distribution
    const loyaltyDistribution = [
      { range: '0-100', count: 0 },
      { range: '101-500', count: 0 },
      { range: '501-1000', count: 0 },
      { range: '1000+', count: 0 }
    ]

    const { data: allUsers } = await supabase
      .from('user_profiles')
      .select('loyalty_points')

    if (allUsers) {
      allUsers.forEach((user: any) => {
        const points = user.loyalty_points || 0
        if (points <= 100) loyaltyDistribution[0].count++
        else if (points <= 500) loyaltyDistribution[1].count++
        else if (points <= 1000) loyaltyDistribution[2].count++
        else loyaltyDistribution[3].count++
      })
    }

    // Get user activity by platform
    const { data: userActivity } = await supabase
      .from('user_analytics')
      .select('platform, session_count')
      .gte('created_at', startDate.toISOString())

    const platformActivity = userActivity?.reduce((acc: Record<string, number>, activity: any) => {
      acc[activity.platform] = (acc[activity.platform] || 0) + activity.session_count
      return acc
    }, {} as Record<string, number>) || {}

    // Compile analytics response
    const analytics = {
      total_users: totalUsersResult.count || 0,
      active_users: activeUsersResult.count || 0,
      new_users_today: newUsersResult.count || 0,
      avg_order_value: avgOrderValue,
      top_customers: topCustomers || [],
      user_growth: userGrowthData,
      loyalty_distribution: loyaltyDistribution,
      platform_activity: platformActivity,
      time_range: timeRange
    }

    return NextResponse.json(analytics)

  } catch (error) {
    console.error('Error fetching user analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch user analytics' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { user_id, platform, session_duration, actions_performed, page_views } = body

    // Record user activity
    const { data, error } = await supabase
      .from('user_analytics')
      .insert([
        {
          user_id,
          platform,
          session_duration,
          actions_performed: actions_performed || 0,
          page_views: page_views || 0,
          session_count: 1,
          created_at: new Date().toISOString()
        }
      ])
      .select()

    if (error) throw error

    return NextResponse.json(data[0])

  } catch (error) {
    console.error('Error recording user analytics:', error)
    return NextResponse.json(
      { error: 'Failed to record user analytics' },
      { status: 500 }
    )
  }
}