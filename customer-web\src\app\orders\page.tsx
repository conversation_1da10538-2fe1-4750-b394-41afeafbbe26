import { Metadata } from 'next';
import Link from 'next/link';
import { GlassCard, GlassButton, GlassBadge } from '@/components/ui/glass-components';

export const metadata: Metadata = {
  title: 'My Orders | Delicious Crepes & Waffles',
  description: 'View your order history and track current orders.',
};

export default function OrdersPage() {
  // Mock orders data - in a real app, this would come from the API or Supabase
  const orders = [
    {
      id: 'ORD-1234',
      date: '2023-11-15T18:30:00Z',
      status: 'delivered',
      total: 26.5,
      items: [
        { name: 'Nutella & Strawberry', quantity: 2 },
        { name: 'Ham & Cheese', quantity: 1 },
        { name: 'Coffee', quantity: 2 },
      ],
    },
    {
      id: 'ORD-1235',
      date: '2023-11-10T12:15:00Z',
      status: 'delivered',
      total: 18.0,
      items: [
        { name: 'Spinach & Feta', quantity: 1 },
        { name: 'Classic Waffle', quantity: 1 },
        { name: 'Orange Juice', quantity: 1 },
      ],
    },
    {
      id: 'ORD-1236',
      date: '2023-11-05T19:45:00Z',
      status: 'delivered',
      total: 22.5,
      items: [
        { name: 'Banana Caramel', quantity: 1 },
        { name: 'Chocolate Waffle', quantity: 1 },
        { name: 'Hot Chocolate', quantity: 2 },
      ],
    },
  ];

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Function to get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'processing':
        return 'info';
      case 'delivering':
        return 'primary';
      case 'delivered':
        return 'success';
      case 'cancelled':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  // Check if orders list is empty
  const hasOrders = orders.length > 0;

  return (
    <main className="min-h-screen py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">My Orders</h1>

        {!hasOrders ? (
          <GlassCard className="text-center py-12">
            <h2 className="text-2xl font-semibold mb-4">No orders yet</h2>
            <p className="text-muted-foreground mb-8">
              You haven't placed any orders yet. Start ordering delicious crepes and waffles!
            </p>
            <Link href="/menu">
              <GlassButton variant="primary" size="lg">
                Browse Menu
              </GlassButton>
            </Link>
          </GlassCard>
        ) : (
          <div className="space-y-6">
            {orders.map(order => (
              <GlassCard key={order.id}>
                <div className="p-6">
                  <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                    <div>
                      <h2 className="text-xl font-semibold">{order.id}</h2>
                      <p className="text-muted-foreground">{formatDate(order.date)}</p>
                    </div>

                    <div className="flex items-center space-x-4 mt-4 md:mt-0">
                      <GlassBadge variant={getStatusVariant(order.status)}>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </GlassBadge>
                      <span className="font-bold">${order.total.toFixed(2)}</span>
                    </div>
                  </div>

                  <div className="mt-4">
                    <h3 className="font-medium mb-2">Order Items:</h3>
                    <ul className="list-disc list-inside text-muted-foreground">
                      {order.items.map((item, index) => (
                        <li key={index}>
                          {item.quantity}x {item.name}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="mt-6 flex flex-col sm:flex-row gap-3">
                    <Link href={`/orders/${order.id}`}>
                      <GlassButton variant="primary" size="sm">
                        View Details
                      </GlassButton>
                    </Link>

                    <GlassButton variant="secondary" size="sm">
                      Reorder
                    </GlassButton>
                  </div>
                </div>
              </GlassCard>
            ))}
          </div>
        )}
      </div>
    </main>
  );
}
