// Quick verification script to test if the database fixes worked
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://voiwzwyfnkzvcffuxpwl.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0MDU2MzIsImV4cCI6MjA2NDk4MTYzMn0.RvabQ6EGP1gBb0w8J8_VpaqqXgkpEFR7Nbbkmw11wNA';

const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyFix() {
  console.log('🔍 Verifying database fixes...\n');
  
  try {
    // Test 1: Check if new columns exist by trying to create a category
    console.log('📋 Testing category creation...');
    const { data: categoryData, error: categoryError } = await supabase
      .from('menu_categories')
      .insert([{
        name: 'Test Category',
        category_type: 'standard',
        description: 'Test description',
        display_order: 999,
        is_active: true,
        is_featured: false
      }])
      .select();

    if (categoryError) {
      console.log('❌ Category creation failed:', categoryError.message);
      if (categoryError.message.includes('category_type')) {
        console.log('   → Database schema not yet updated. Please apply apply-menu-fixes.sql');
      }
      if (categoryError.message.includes('row-level security')) {
        console.log('   → RLS policies need updating. Please apply apply-menu-fixes.sql');
      }
    } else {
      console.log('✅ Category creation successful!');
      
      // Clean up test data
      const testId = categoryData[0].id;
      await supabase.from('menu_categories').delete().eq('id', testId);
      console.log('🧹 Test data cleaned up');
    }
    
    // Test 2: Check ingredient categories
    console.log('\n🥬 Testing ingredient categories...');
    const { data: ingredientCats, error: ingredientCatError } = await supabase
      .from('ingredient_categories')
      .select('*')
      .limit(1);
      
    if (ingredientCatError) {
      console.log('❌ Ingredient categories access failed:', ingredientCatError.message);
    } else {
      console.log('✅ Ingredient categories accessible');
    }
    
    // Test 3: Check ingredients
    console.log('\n🧄 Testing ingredients...');
    const { data: ingredients, error: ingredientsError } = await supabase
      .from('ingredients')
      .select('*')
      .limit(1);
      
    if (ingredientsError) {
      console.log('❌ Ingredients access failed:', ingredientsError.message);
    } else {
      console.log('✅ Ingredients accessible');
    }
    
    console.log('\n📊 Verification Summary:');
    console.log('========================');
    
    if (!categoryError && !ingredientCatError && !ingredientsError) {
      console.log('🎉 All tests passed! Admin dashboard should work correctly now.');
      console.log('👉 You can now safely use the admin dashboard to manage categories.');
    } else {
      console.log('⚠️  Some issues detected. Please apply the database fixes:');
      console.log('   1. Open Supabase Dashboard SQL Editor');
      console.log('   2. Copy contents of apply-menu-fixes.sql');
      console.log('   3. Run the script');
      console.log('   4. Re-run this verification: node verify-fix.js');
    }
    
  } catch (error) {
    console.log('❌ Verification failed:', error.message);
  }
}

verifyFix();