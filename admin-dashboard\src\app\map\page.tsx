'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { toast } from 'react-hot-toast'
import { 
  Map, 
  Plus, 
  Edit3, 
  Trash2, 
  Eye,
  MapPin,
  Clock,
  DollarSign,
  TrendingUp,
  Package,
  Users,
  BarChart3,
  Navigation,
  Zap,
  RefreshCw,
  Download,
  Search,
  Filter,
  Settings
} from 'lucide-react'

// Enhanced interfaces for delivery zone management
interface DeliveryZone {
  id: string
  name: string
  description?: string
  coordinates: Array<{ lat: number; lng: number }>
  delivery_fee: number
  min_order_amount: number
  max_delivery_time: number
  is_active: boolean
  priority: number
  color_code: string
  created_at: string
  updated_at: string
}

interface ZoneAnalytics {
  zone_id: string
  zone_name: string
  total_orders: number
  total_revenue: number
  avg_delivery_time: number
  customer_satisfaction: number
  peak_hours: Array<{ hour: number; order_count: number }>
  popular_items: Array<{ item_name: string; quantity: number }>
}

interface MapAnalytics {
  total_zones: number
  active_zones: number
  total_deliveries: number
  avg_delivery_time: number
  total_revenue: number
  zone_performance: ZoneAnalytics[]
  delivery_heat_map: Array<{ lat: number; lng: number; intensity: number }>
  time_analytics: Array<{ hour: string; orders: number; avg_time: number }>
}

export default function MapPage() {
  const [zones, setZones] = useState<DeliveryZone[]>([])
  const [analytics, setAnalytics] = useState<MapAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all')
  const [selectedZone, setSelectedZone] = useState<DeliveryZone | null>(null)
  const [showZoneModal, setShowZoneModal] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [mapView, setMapView] = useState<'zones' | 'analytics' | 'heatmap'>('zones')

  useEffect(() => {
    loadZonesAndAnalytics()
  }, [])

  const loadZonesAndAnalytics = async () => {
    setLoading(true)
    try {
      await Promise.all([
        loadDeliveryZones(),
        loadMapAnalytics()
      ])
    } catch (error) {
      console.error('Error loading map data:', error)
      toast.error('Failed to load map data')
    } finally {
      setLoading(false)
    }
  }

  const loadDeliveryZones = async () => {
    try {
      const { data, error } = await supabase
        .from('delivery_zones')
        .select('*')
        .order('priority', { ascending: true })

      if (error) throw error
      setZones(data || [])
    } catch (error) {
      console.error('Error loading delivery zones:', error)
      throw error
    }
  }

  const loadMapAnalytics = async () => {
    try {
      // Try to get analytics from RPC function, fallback to basic queries
      const { data: analyticsData, error: analyticsError } = await supabase
        .rpc('get_map_analytics')

      if (analyticsError) {
        console.warn('Analytics function not available, using basic queries')
        
        // Fallback to basic queries
        const [zonesCount, activeZones, deliveriesCount, avgTime] = await Promise.all([
          supabase.from('delivery_zones').select('id', { count: 'exact' }),
          supabase.from('delivery_zones').select('id', { count: 'exact' }).eq('is_active', true),
          supabase.from('orders').select('id', { count: 'exact' }).eq('order_type', 'delivery'),
          supabase.from('orders').select('delivery_time').eq('order_type', 'delivery').then((res: any) => {
            const times = res.data?.filter((o: any) => o.delivery_time).map((o: any) => o.delivery_time) || []
            return times.length > 0 ? times.reduce((a: number, b: number) => a + b, 0) / times.length : 0
          })
        ])

        setAnalytics({
          total_zones: zonesCount.count || 0,
          active_zones: activeZones.count || 0,
          total_deliveries: deliveriesCount.count || 0,
          avg_delivery_time: avgTime || 0,
          total_revenue: 0,
          zone_performance: [],
          delivery_heat_map: [],
          time_analytics: []
        })
      } else {
        setAnalytics(analyticsData)
      }
    } catch (error) {
      console.error('Error loading map analytics:', error)
      // Set fallback analytics
      setAnalytics({
        total_zones: zones.length,
        active_zones: zones.filter(z => z.is_active).length,
        total_deliveries: 0,
        avg_delivery_time: 0,
        total_revenue: 0,
        zone_performance: [],
        delivery_heat_map: [],
        time_analytics: []
      })
    }
  }

  const handleCreateZone = () => {
    setSelectedZone(null)
    setIsCreating(true)
    setShowZoneModal(true)
  }

  const handleEditZone = (zone: DeliveryZone) => {
    setSelectedZone(zone)
    setIsCreating(false)
    setShowZoneModal(true)
  }

  const handleDeleteZone = async (zoneId: string) => {
    if (!confirm('Are you sure you want to delete this delivery zone?')) return

    try {
      const { error } = await supabase
        .from('delivery_zones')
        .delete()
        .eq('id', zoneId)

      if (error) throw error

      toast.success('Delivery zone deleted successfully')
      loadDeliveryZones()
    } catch (error) {
      console.error('Error deleting delivery zone:', error)
      toast.error('Failed to delete delivery zone')
    }
  }

  const handleToggleZoneStatus = async (zoneId: string, isActive: boolean) => {
    try {
      const { error } = await supabase
        .from('delivery_zones')
        .update({ is_active: !isActive })
        .eq('id', zoneId)

      if (error) throw error

      toast.success(`Zone ${!isActive ? 'activated' : 'deactivated'} successfully`)
      loadDeliveryZones()
    } catch (error) {
      console.error('Error updating zone status:', error)
      toast.error('Failed to update zone status')
    }
  }

  const filteredZones = zones.filter(zone => {
    const matchesSearch = !searchTerm || 
      zone.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      zone.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterStatus === 'all' || 
      (filterStatus === 'active' && zone.is_active) ||
      (filterStatus === 'inactive' && !zone.is_active)
    
    return matchesSearch && matchesFilter
  })

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount)
  }

  const getStatusBadge = (isActive: boolean) => {
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        isActive 
          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
          : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
      }`}>
        {isActive ? 'Active' : 'Inactive'}
      </span>
    )
  }

  const getPriorityBadge = (priority: number) => {
    const color = priority <= 3 ? 'red' : priority <= 6 ? 'yellow' : 'green'
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        color === 'red' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
        color === 'yellow' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
        'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
      }`}>
        Priority {priority}
      </span>
    )
  }

  if (loading) {
    return (
      <>
        <div className="p-8">
          <div className="space-y-6">
            {/* Header skeleton */}
            <div className="flex justify-between items-center">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-48 animate-pulse" />
              <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse" />
            </div>
            
            {/* Analytics cards skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse" />
              ))}
            </div>
            
            {/* Content skeleton */}
            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
              <div className="p-6 space-y-4">
                {[1, 2, 3, 4, 5].map(i => (
                  <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                ))}
              </div>
            </div>
          </div>
        </div>
      </>
    )
  }

  return (
      <div className="p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Map & Delivery Zones</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage delivery zones and analyze delivery performance with interactive maps
            </p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={loadZonesAndAnalytics}
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </button>
            <button
              onClick={handleCreateZone}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Zone
            </button>
          </div>
        </div>

        {/* Analytics Cards */}
        {analytics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Zones</p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white">{analytics.total_zones}</p>
                </div>
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <Map className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                {analytics.active_zones} active zones
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Deliveries</p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white">{analytics.total_deliveries.toLocaleString()}</p>
                </div>
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Delivery Time</p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white">{analytics.avg_delivery_time.toFixed(0)} min</p>
                </div>
                <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                  <Clock className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Delivery Revenue</p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white">{formatCurrency(analytics.total_revenue)}</p>
                </div>
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Map View Toggle */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Interactive Map</h2>
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setMapView('zones')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  mapView === 'zones'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                Zones
              </button>
              <button
                onClick={() => setMapView('analytics')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  mapView === 'analytics'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                Analytics
              </button>
              <button
                onClick={() => setMapView('heatmap')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  mapView === 'heatmap'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                Heatmap
              </button>
            </div>
          </div>
          
          {/* Map Placeholder */}
          <div className="h-96 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <Map className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Interactive Map View</h3>
              <p className="text-gray-600 dark:text-gray-400">
                {mapView === 'zones' && 'Delivery zones with boundaries and settings'}
                {mapView === 'analytics' && 'Performance analytics overlay on map'}
                {mapView === 'heatmap' && 'Order density and delivery time heatmap'}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
                Google Maps integration will be implemented here
              </p>
            </div>
          </div>
        </div>

        {/* Zones Management */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
          {/* Filters and Search */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="text"
                    placeholder="Search zones by name or description..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <div className="flex gap-3">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value as 'all' | 'active' | 'inactive')}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Zones</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
                
                <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </button>
              </div>
            </div>
          </div>

          {/* Zones Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700/50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Zone
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Delivery Info
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Performance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {filteredZones.map((zone) => {
                  const zoneAnalytics = analytics?.zone_performance.find(zp => zp.zone_id === zone.id)
                  
                  return (
                    <tr key={zone.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div 
                            className="h-4 w-4 rounded-full mr-3"
                            style={{ backgroundColor: zone.color_code }}
                          />
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {zone.name}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {zone.description || 'No description'}
                            </div>
                          </div>
                        </div>
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-1">
                          <div className="flex items-center text-sm text-gray-900 dark:text-white">
                            <DollarSign className="w-4 h-4 mr-2 text-gray-400" />
                            {formatCurrency(zone.delivery_fee)} fee
                          </div>
                          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            <Package className="w-4 h-4 mr-2 text-gray-400" />
                            {formatCurrency(zone.min_order_amount)} min order
                          </div>
                          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            <Clock className="w-4 h-4 mr-2 text-gray-400" />
                            {zone.max_delivery_time} min max
                          </div>
                        </div>
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap">
                        {zoneAnalytics ? (
                          <div className="space-y-1">
                            <div className="text-sm text-gray-900 dark:text-white">
                              {zoneAnalytics.total_orders} orders
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {formatCurrency(zoneAnalytics.total_revenue)} revenue
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {zoneAnalytics.avg_delivery_time.toFixed(0)} min avg
                            </div>
                          </div>
                        ) : (
                          <div className="text-sm text-gray-400">No data</div>
                        )}
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="space-y-2">
                          {getStatusBadge(zone.is_active)}
                          {getPriorityBadge(zone.priority)}
                        </div>
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleEditZone(zone)}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleEditZone(zone)}
                            className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300 transition-colors"
                          >
                            <Edit3 className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleToggleZoneStatus(zone.id, zone.is_active)}
                            className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300 transition-colors"
                          >
                            <Zap className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteZone(zone.id)}
                            className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
          
          {filteredZones.length === 0 && (
            <div className="text-center py-12">
              <Map className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No delivery zones found</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {searchTerm ? 'Try adjusting your search criteria.' : 'Get started by creating your first delivery zone.'}
              </p>
            </div>
          )}
        </div>

        {/* Zone Performance Analytics */}
        {analytics && analytics.zone_performance.length > 0 && (
          <div className="grid gap-6 md:grid-cols-2">
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Top Performing Zones</h3>
              <div className="space-y-4">
                {analytics.zone_performance
                  .sort((a, b) => b.total_revenue - a.total_revenue)
                  .slice(0, 5)
                  .map((zone, index) => (
                    <div key={zone.zone_id} className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="font-medium text-gray-900 dark:text-white">{zone.zone_name}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{zone.total_orders} orders</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900 dark:text-white">
                          {formatCurrency(zone.total_revenue)}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {zone.avg_delivery_time.toFixed(0)} min avg
                        </p>
                      </div>
                    </div>
                  ))}
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Delivery Time Analytics</h3>
              <div className="space-y-4">
                {analytics.time_analytics.slice(0, 6).map((timeData, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex-1">
                      <p className="font-medium text-gray-900 dark:text-white">{timeData.hour}:00</p>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${(timeData.orders / Math.max(...analytics.time_analytics.map(t => t.orders))) * 100}%` }}
                        />
                      </div>
                    </div>
                    <div className="text-right ml-4">
                      <p className="font-medium text-gray-900 dark:text-white">{timeData.orders}</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{timeData.avg_time.toFixed(0)} min</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
  )
}