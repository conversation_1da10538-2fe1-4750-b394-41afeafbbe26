// Role-Based Access Control (RBAC) Utilities

import { supabase } from './config';
import type { Role, Permission, UserRole } from './types';

/**
 * Get user roles and permissions
 */
export async function getUserRolePermissions(userId: string): Promise<{
  roles: Role[];
  permissions: Permission[];
  success: boolean;
  error?: string;
}> {
  try {
    // Get user profile with role information
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profiles')
      .select(`
        role_id,
        roles (
          id,
          name,
          description
        )
      `)
      .eq('user_id', userId)
      .single();

    if (profileError) {
      console.error('Get user profile error:', profileError);
      return {
        roles: [],
        permissions: [],
        success: false,
        error: profileError.message,
      };
    }

    if (!userProfile || !userProfile.roles) {
      return {
        roles: [],
        permissions: [],
        success: true,
      };
    }

    const role = userProfile.roles as any;
    const roleData: Role = {
      id: role.id,
      name: role.name,
      description: role.description || undefined,
      created_at: role.created_at || new Date().toISOString(),
      updated_at: role.updated_at || new Date().toISOString(),
    };

    // Get permissions based on role
    const permissions = getDefaultRolePermissions(role.name);

    return {
      roles: [roleData],
      permissions,
      success: true,
    };
  } catch (error) {
    console.error('Get user role permissions error:', error);
    return {
      roles: [],
      permissions: [],
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user roles and permissions',
    };
  }
}

/**
 * Check if user has specific permission
 */
export async function hasPermission(userId: string, permission: Permission): Promise<boolean> {
  try {
    const { permissions } = await getUserRolePermissions(userId);
    return permissions.includes(permission);
  } catch (error) {
    console.error('Check permission error:', error);
    return false;
  }
}

/**
 * Check if user has any of the specified permissions
 */
export async function hasAnyPermission(userId: string, permissions: Permission[]): Promise<boolean> {
  try {
    const { permissions: userPermissions } = await getUserRolePermissions(userId);
    return permissions.some(permission => userPermissions.includes(permission));
  } catch (error) {
    console.error('Check any permission error:', error);
    return false;
  }
}

/**
 * Check if user has all specified permissions
 */
export async function hasAllPermissions(userId: string, permissions: Permission[]): Promise<boolean> {
  try {
    const { permissions: userPermissions } = await getUserRolePermissions(userId);
    return permissions.every(permission => userPermissions.includes(permission));
  } catch (error) {
    console.error('Check all permissions error:', error);
    return false;
  }
}

/**
 * Check if user has specific role
 */
export async function hasRole(userId: string, roleName: UserRole): Promise<boolean> {
  try {
    const { roles } = await getUserRolePermissions(userId);
    return roles.some(role => role.name === roleName);
  } catch (error) {
    console.error('Check role error:', error);
    return false;
  }
}

/**
 * Get all available roles
 */
export async function getAllRoles(): Promise<{
  roles: Role[];
  success: boolean;
  error?: string;
}> {
  try {
    const { data: roles, error } = await supabase
      .from('roles')
      .select('*')
      .order('name');

    if (error) {
      console.error('Get all roles error:', error);
      return {
        roles: [],
        success: false,
        error: error.message,
      };
    }

    return {
      roles: (roles || []).map(role => ({
        id: role.id,
        name: role.name || '',
        description: role.description || undefined,
        created_at: role.created_at || new Date().toISOString(),
        updated_at: role.updated_at || new Date().toISOString(),
      })),
      success: true,
    };
  } catch (error) {
    console.error('Get all roles error:', error);
    return {
      roles: [],
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get roles',
    };
  }
}

/**
 * Get all available permissions
 */
export async function getAllPermissions(): Promise<{
  permissions: Permission[];
  success: boolean;
  error?: string;
}> {
  try {
    // Return all available permissions as defined in the Permission type
    const allPermissions: Permission[] = [
      'admin.full_access',
      'admin.user_management',
      'admin.system_settings',
      'admin.reports',
      'admin.access',
      'manager.branch_management',
      'manager.staff_management',
      'manager.inventory',
      'manager.reports',
      'staff.pos_access',
      'staff.order_management',
      'staff.inventory_view',
      'pos.access',
      'users.read',
      'users.write',
      'users.delete',
      'products.read',
      'products.write',
      'products.delete',
      'orders.read',
      'orders.write',
      'orders.delete',
      'inventory.read',
      'inventory.write',
      'reports.read',
      'reports.write',
      'settings.read',
      'settings.write',
      'customer.order_placement',
      'customer.order_history',
      'customer.loyalty_points'
    ];

    return {
      permissions: allPermissions,
      success: true,
    };
  } catch (error) {
    console.error('Get all permissions error:', error);
    return {
      permissions: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Assign role to user
 */
export async function assignUserRole(userId: string, roleId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Check current user role
    const { data: userProfile, error: checkError } = await supabase
      .from('user_profiles')
      .select('role_id')
      .eq('user_id', userId)
      .single();

    if (checkError) {
      console.error('Check user profile error:', checkError);
      return {
        success: false,
        error: checkError.message,
      };
    }

    if (userProfile?.role_id === roleId) {
      return {
        success: true, // Already has role
      };
    }

    // Assign the role by updating user profile
    const { error } = await supabase
      .from('user_profiles')
      .update({
        role_id: roleId,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId);

    if (error) {
      console.error('Assign user role error:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error('Assign user role error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to assign role',
    };
  }
}

/**
 * Remove role from user
 */
export async function removeUserRole(userId: string, roleId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Remove role by setting role_id to null in user profile
    const { error } = await supabase
      .from('user_profiles')
      .update({
        role_id: null,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId)
      .eq('role_id', roleId);

    if (error) {
      console.error('Remove user role error:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error('Remove user role error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to remove role',
    };
  }
}

/**
 * Get role by name
 */
export async function getRoleByName(roleName: UserRole): Promise<{
  role?: Role;
  success: boolean;
  error?: string;
}> {
  try {
    const { data: roleData, error } = await supabase
      .from('roles')
      .select('*')
      .eq('name', roleName)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return {
          success: true, // No role found, but not an error
        };
      }
      console.error('Get role by name error:', error);
      return {
        success: false,
        error: error.message,
      };
    }

    // Convert null values to undefined to match Role interface
    const role: Role = {
      id: roleData.id,
      name: roleData.name,
      description: roleData.description || undefined,
      created_at: roleData.created_at || new Date().toISOString(),
      updated_at: roleData.updated_at || new Date().toISOString(),
    };

    return {
      role,
      success: true,
    };
  } catch (error) {
    console.error('Get role by name error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get role',
    };
  }
}

/**
 * Get permissions for a specific role
 */
export async function getRolePermissions(roleId: string): Promise<{
  permissions: Permission[];
  success: boolean;
  error?: string;
}> {
  try {
    // Get role name first
    const { data: roleData, error: roleError } = await supabase
      .from('roles')
      .select('name')
      .eq('id', roleId)
      .single();

    if (roleError || !roleData) {
      return {
        permissions: [],
        success: false,
        error: 'Role not found',
      };
    }

    // Return hardcoded permissions based on role name
    const permissions = getDefaultRolePermissions(roleData.name as UserRole);

    return {
      permissions,
      success: true,
    };
  } catch (error) {
    console.error('Get role permissions error:', error);
    return {
      permissions: [],
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get role permissions',
    };
  }
}

/**
 * Check if user is admin
 */
export async function isAdmin(userId: string): Promise<boolean> {
  return await hasRole(userId, 'admin');
}

/**
 * Check if user is manager
 */
export async function isManager(userId: string): Promise<boolean> {
  return await hasRole(userId, 'manager');
}

/**
 * Check if user is staff
 */
export async function isStaff(userId: string): Promise<boolean> {
  return await hasRole(userId, 'staff');
}

/**
 * Check if user is customer
 */
export async function isCustomer(userId: string): Promise<boolean> {
  return await hasRole(userId, 'customer');
}

/**
 * Check if user has admin or manager role
 */
export async function isAdminOrManager(userId: string): Promise<boolean> {
  const [adminRole, managerRole] = await Promise.all([
    hasRole(userId, 'admin'),
    hasRole(userId, 'manager'),
  ]);
  return adminRole || managerRole;
}

/**
 * Check if user has staff-level access (admin, manager, or staff)
 */
export async function hasStaffAccess(userId: string): Promise<boolean> {
  const [adminRole, managerRole, staffRole] = await Promise.all([
    hasRole(userId, 'admin'),
    hasRole(userId, 'manager'),
    hasRole(userId, 'staff'),
  ]);
  return adminRole || managerRole || staffRole;
}

/**
 * Get user's highest role (based on hierarchy: admin > manager > staff > customer)
 */
export async function getUserHighestRole(userId: string): Promise<{
  role?: UserRole;
  success: boolean;
  error?: string;
}> {
  try {
    const { roles } = await getUserRolePermissions(userId);
    
    if (roles.length === 0) {
      return {
        success: true,
      };
    }

    // Role hierarchy (highest to lowest)
    const roleHierarchy: UserRole[] = ['admin', 'manager', 'staff', 'customer'];
    
    for (const hierarchyRole of roleHierarchy) {
      if (roles.some(role => role.name === hierarchyRole)) {
        return {
          role: hierarchyRole,
          success: true,
        };
      }
    }

    // If no recognized role found, return the first one
    return {
      role: roles[0].name as UserRole,
      success: true,
    };
  } catch (error) {
    console.error('Get user highest role error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user highest role',
    };
  }
}

/**
 * Validate permission name
 */
export function isValidPermission(permission: string): permission is Permission {
  const validPermissions: Permission[] = [
    'users.read',
    'users.write',
    'users.delete',
    'products.read',
    'products.write',
    'products.delete',
    'orders.read',
    'orders.write',
    'orders.delete',
    'inventory.read',
    'inventory.write',
    'reports.read',
    'reports.write',
    'settings.read',
    'settings.write',
    'pos.access',
    'admin.access',
  ];
  
  return validPermissions.includes(permission as Permission);
}

/**
 * Validate role name
 */
export function isValidRole(role: string): role is UserRole {
  const validRoles: UserRole[] = ['admin', 'manager', 'staff', 'customer'];
  return validRoles.includes(role as UserRole);
}

/**
 * Get role hierarchy level (lower number = higher privilege)
 */
export function getRoleLevel(role: UserRole): number {
  const roleLevels: Record<UserRole, number> = {
    admin: 1,
    manager: 2,
    staff: 3,
    customer: 4,
  };
  
  return roleLevels[role] || 999;
}

/**
 * Check if role A has higher or equal privilege than role B
 */
export function hasHigherOrEqualRole(roleA: UserRole, roleB: UserRole): boolean {
  return getRoleLevel(roleA) <= getRoleLevel(roleB);
}

/**
 * Get default permissions for a role
 */
export function getDefaultRolePermissions(role: UserRole): Permission[] {
  const defaultPermissions: Record<UserRole, Permission[]> = {
    admin: [
      'users.read', 'users.write', 'users.delete',
      'products.read', 'products.write', 'products.delete',
      'orders.read', 'orders.write', 'orders.delete',
      'inventory.read', 'inventory.write',
      'reports.read', 'reports.write',
      'settings.read', 'settings.write',
      'pos.access', 'admin.access',
    ],
    manager: [
      'users.read', 'users.write',
      'products.read', 'products.write',
      'orders.read', 'orders.write',
      'inventory.read', 'inventory.write',
      'reports.read',
      'settings.read',
      'pos.access',
    ],
    staff: [
      'products.read',
      'orders.read', 'orders.write',
      'inventory.read',
      'pos.access',
    ],
    customer: [
      'orders.read',
    ],
  };
  
  return defaultPermissions[role] || [];
}