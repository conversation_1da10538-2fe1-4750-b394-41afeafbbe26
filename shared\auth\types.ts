// Authentication Types for Creperie System

export interface User {
  id: string;
  email?: string;
  phone?: string;
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  id: string;
  user_id: string;
  staff_id?: string;
  customer_id?: string;
  role_id?: string;
  branch_id?: string;
  full_name?: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  phone?: string;
  pin_hash?: string;
  phone_verified?: boolean;
  two_fa_enabled?: boolean;
  two_fa_secret?: string;
  last_login?: string;
  last_login_at?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface AuthSession {
  id: string;
  user_id: string;
  session_token: string;
  platform: Platform;
  ip_address?: string;
  user_agent?: string;
  expires_at: string;
  created_at: string;
}

export interface AuthAttempt {
  id: string;
  user_id?: string;
  email?: string;
  phone?: string;
  attempt_type: 'email_password' | 'phone_otp' | 'pin' | 'two_fa';
  success: boolean;
  ip_address?: string;
  user_agent?: string;
  error_message?: string;
  created_at: string;
}

export interface PhoneVerification {
  id: string;
  phone: string;
  otp_code: string;
  verified: boolean;
  expires_at: string;
  attempts: number;
  created_at: string;
}

export interface TwoFABackupCode {
  id: string;
  user_id: string;
  code: string;
  used: boolean;
  used_at?: string;
  created_at: string;
}

export interface Branch {
  id: string;
  name: string;
  address: string;
  phone?: string;
  manager_id?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Authentication Request/Response Types
export interface LoginRequest {
  email?: string;
  phone?: string;
  password?: string;
  pin?: string;
  otp?: string;
  platform: Platform;
}

export interface LoginResponse {
  success: boolean;
  user?: User;
  profile?: UserProfile;
  role?: Role;
  session?: AuthSession;
  requires_2fa?: boolean;
  access_token?: string;
  refresh_token?: string;
  error?: string;
}

export interface OTPRequest {
  phone: string;
  platform: 'customer-web' | 'customer-mobile';
}

export interface OTPResponse {
  success: boolean;
  message: string;
  expires_in?: number;
  error?: string;
}

export interface VerifyOTPRequest {
  phone: string;
  otp: string;
  platform: 'customer-web' | 'customer-mobile';
}

export interface PINVerificationRequest {
  pin: string;
  staff_id?: string;
}

export interface TwoFASetupResponse {
  success: boolean;
  qr_code?: string;
  secret?: string;
  backup_codes?: string[];
  error?: string;
}

export interface TwoFAVerifyRequest {
  user_id: string;
  code: string;
  is_backup_code?: boolean;
}

// Permission Types
export type Permission = 
  | 'admin.full_access'
  | 'admin.user_management'
  | 'admin.system_settings'
  | 'admin.reports'
  | 'admin.access'
  | 'manager.branch_management'
  | 'manager.staff_management'
  | 'manager.inventory'
  | 'manager.reports'
  | 'staff.pos_access'
  | 'staff.order_management'
  | 'staff.inventory_view'
  | 'pos.access'
  | 'users.read'
  | 'users.write'
  | 'users.delete'
  | 'products.read'
  | 'products.write'
  | 'products.delete'
  | 'orders.read'
  | 'orders.write'
  | 'orders.delete'
  | 'inventory.read'
  | 'inventory.write'
  | 'reports.read'
  | 'reports.write'
  | 'settings.read'
  | 'settings.write'
  | 'customer.order_placement'
  | 'customer.order_history'
  | 'customer.loyalty_points';

export type UserRole = 'admin' | 'manager' | 'staff' | 'customer';

export type Platform = 'admin-dashboard' | 'pos-system' | 'customer-web' | 'customer-mobile';

export type AuthMethod = 'email_password' | 'phone_otp' | 'pin' | 'two_fa';