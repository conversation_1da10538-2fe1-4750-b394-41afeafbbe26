import { supabase } from '@/lib/supabase';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface MenuSyncEvent {
  id: string;
  sync_type: 'subcategory' | 'ingredient' | 'category' | 'availability' | 'delivery_pricing' | 'pickup_pricing' | 'pricing_config';
  resource_id: string;
  operation: 'insert' | 'update' | 'delete';
  data_changes: any;
  target_platforms: string[];
  timestamp: string;
}

export interface IngredientAvailabilityUpdate {
  ingredient_id: string;
  is_available: boolean;
  stock_quantity: number;
  affected_subcategories?: string[];
}

export interface SubcategoryAvailabilityUpdate {
  subcategory_id: string;
  is_available: boolean;
  reason?: string;
  auto_disabled?: boolean;
}

export interface DeliveryZonePricingUpdate {
  zone_id: string;
  delivery_fee: number;
  minimum_order_amount: number;
  is_active: boolean;
  affected_orders?: string[];
}

export interface PickupPricingUpdate {
  setting_key: string;
  setting_value: string;
  previous_value?: string;
  affected_platforms: string[];
}

export interface PricingConfigUpdate {
  config_type: 'pickup' | 'delivery' | 'general';
  setting_key: string;
  setting_value: string;
  branch_id?: string;
  requires_recalculation: boolean;
}

export class MenuSyncService {
  private supabase;
  private channels: Map<string, RealtimeChannel> = new Map();
  private listeners: Map<string, Set<Function>> = new Map();
  private isInitialized: boolean = false;

  constructor() {
    this.supabase = supabase;
  }

  /**
   * Initialize real-time subscriptions for menu synchronization
   */
  async initializeSync() {
    // Prevent duplicate initialization
    if (this.isInitialized) {
      console.warn('MenuSyncService already initialized');
      return;
    }

    try {
      // Clean up any existing channels first
      await this.cleanup();

      // Subscribe to subcategories changes
      const subcategoriesChannel = this.supabase
        .channel('subcategories_changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'subcategories' },
          (payload: any) => this.handleSubcategoryChange(payload)
        )
        .subscribe();

      // Subscribe to ingredients changes
      const ingredientsChannel = this.supabase
        .channel('ingredients_changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'ingredients' },
          (payload: any) => this.handleIngredientChange(payload)
        )
        .subscribe();

      // Subscribe to menu categories changes
      const categoriesChannel = this.supabase
        .channel('categories_changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'menu_categories' },
          (payload: any) => this.handleCategoryChange(payload)
        )
        .subscribe();

      // Subscribe to sync queue for processing
      const syncQueueChannel = this.supabase
        .channel('sync_queue_changes')
        .on(
          'postgres_changes',
          { event: 'INSERT', schema: 'public', table: 'menu_sync_queue' },
          (payload: any) => this.processSyncQueueItem(payload)
        )
        .subscribe();

      // Subscribe to delivery zones changes for pricing sync
      const deliveryZonesChannel = this.supabase
        .channel('delivery_zones_changes')
        .on(
          'postgres_changes',
          { event: '*', schema: 'public', table: 'delivery_zones' },
          (payload: any) => this.handleDeliveryZoneChange(payload)
        )
        .subscribe();

      // Subscribe to restaurant settings changes for pricing config
      const settingsChannel = this.supabase
        .channel('settings_changes')
        .on(
          'postgres_changes',
          { 
            event: '*', 
            schema: 'public', 
            table: 'restaurant_settings',
            filter: 'setting_key=like.*pickup*,setting_key=like.*delivery*,setting_key=like.*tax*,setting_key=like.*fee*'
          },
          (payload: any) => this.handlePricingSettingChange(payload)
        )
        .subscribe();

      this.channels.set('subcategories', subcategoriesChannel);
      this.channels.set('ingredients', ingredientsChannel);
      this.channels.set('categories', categoriesChannel);
      this.channels.set('sync_queue', syncQueueChannel);
      this.channels.set('delivery_zones', deliveryZonesChannel);
      this.channels.set('settings', settingsChannel);
      
      this.isInitialized = true;
      console.log('MenuSyncService initialized successfully');
    } catch (error) {
      console.error('Error initializing MenuSyncService:', error);
      throw error;
    }
  }

  /**
   * Clean up subscriptions
   */
  async cleanup() {
    try {
      for (const [name, channel] of this.channels) {
        try {
          await this.supabase.removeChannel(channel);
          console.log(`Removed channel: ${name}`);
        } catch (error) {
          console.warn(`Error removing channel ${name}:`, error);
        }
      }
      this.channels.clear();
      this.listeners.clear();
      this.isInitialized = false;
      console.log('MenuSyncService cleanup completed');
    } catch (error) {
      console.error('Error during MenuSyncService cleanup:', error);
    }
  }

  /**
   * Add a listener for specific sync events
   */
  addListener(eventType: string, callback: Function) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType)?.add(callback);
  }

  /**
   * Remove a listener
   */
  removeListener(eventType: string, callback: Function) {
    this.listeners.get(eventType)?.delete(callback);
  }

  /**
   * Emit event to all listeners
   */
  private emit(eventType: string, data: any) {
    this.listeners.get(eventType)?.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in sync listener:', error);
      }
    });
  }

  /**
   * Handle subcategory changes
   */
  private handleSubcategoryChange(payload: any) {
    console.log('Subcategory changed:', payload);
    
    const event: MenuSyncEvent = {
      id: crypto.randomUUID(),
      sync_type: 'subcategory',
      resource_id: payload.new?.id || payload.old?.id,
      operation: payload.eventType.toLowerCase() as any,
      data_changes: payload.new || payload.old,
      target_platforms: ['pos', 'web', 'mobile'],
      timestamp: new Date().toISOString()
    };

    // Special handling for availability changes
    if (payload.eventType === 'UPDATE' && 
        payload.old?.is_available !== payload.new?.is_available) {
      this.handleSubcategoryAvailabilityChange({
        subcategory_id: payload.new.id,
        is_available: payload.new.is_available,
        reason: payload.new.is_available ? 'manual_enable' : 'manual_disable'
      });
    }

    this.emit('subcategory_change', event);
    this.emit('sync_event', event);
  }

  /**
   * Handle ingredient changes
   */
  private handleIngredientChange(payload: any) {
    console.log('Ingredient changed:', payload);

    const event: MenuSyncEvent = {
      id: crypto.randomUUID(),
      sync_type: 'ingredient',
      resource_id: payload.new?.id || payload.old?.id,
      operation: payload.eventType.toLowerCase() as any,
      data_changes: payload.new || payload.old,
      target_platforms: ['admin', 'pos'],
      timestamp: new Date().toISOString()
    };

    // Special handling for availability changes
    if (payload.eventType === 'UPDATE' && 
        payload.old?.is_available !== payload.new?.is_available) {
      this.handleIngredientAvailabilityChange({
        ingredient_id: payload.new.id,
        is_available: payload.new.is_available,
        stock_quantity: payload.new.stock_quantity
      });
    }

    // Handle stock level changes
    if (payload.eventType === 'UPDATE' && 
        payload.old?.stock_quantity !== payload.new?.stock_quantity) {
      this.handleStockLevelChange(payload.new);
    }

    this.emit('ingredient_change', event);
    this.emit('sync_event', event);
  }

  /**
   * Handle category changes
   */
  private handleCategoryChange(payload: any) {
    console.log('Category changed:', payload);

    const event: MenuSyncEvent = {
      id: crypto.randomUUID(),
      sync_type: 'category',
      resource_id: payload.new?.id || payload.old?.id,
      operation: payload.eventType.toLowerCase() as any,
      data_changes: payload.new || payload.old,
      target_platforms: ['admin', 'pos', 'web', 'mobile'],
      timestamp: new Date().toISOString()
    };

    this.emit('category_change', event);
    this.emit('sync_event', event);
  }

  /**
   * Process sync queue items
   */
  private async processSyncQueueItem(payload: any) {
    const syncItem = payload.new;
    console.log('Processing sync queue item:', syncItem);

    // Mark as processing
    await this.supabase
      .from('menu_sync_queue')
      .update({ 
        sync_status: 'processing',
        last_sync_attempt: new Date().toISOString(),
        sync_attempts: syncItem.sync_attempts + 1
      })
      .eq('id', syncItem.id);

    try {
      // Process based on sync type
      switch (syncItem.sync_type) {
        case 'availability':
          await this.processAvailabilitySync(syncItem);
          break;
        case 'subcategory':
          await this.processSubcategorySync(syncItem);
          break;
        case 'ingredient':
          await this.processIngredientSync(syncItem);
          break;
        case 'delivery_pricing':
          await this.processDeliveryPricingSync(syncItem);
          break;
        case 'pickup_pricing':
          await this.processPickupPricingSync(syncItem);
          break;
        case 'pricing_config':
          await this.processPricingConfigSync(syncItem);
          break;
        default:
          console.log('Unknown sync type:', syncItem.sync_type);
      }

      // Mark as completed
      await this.supabase
        .from('menu_sync_queue')
        .update({ sync_status: 'completed' })
        .eq('id', syncItem.id);

    } catch (error) {
      console.error('Error processing sync item:', error);
      
      // Mark as failed
      await this.supabase
        .from('menu_sync_queue')
        .update({ 
          sync_status: 'failed',
          error_message: error instanceof Error ? error.message : 'Unknown error'
        })
        .eq('id', syncItem.id);
    }
  }

  /**
   * Handle ingredient availability changes
   */
  private async handleIngredientAvailabilityChange(update: IngredientAvailabilityUpdate) {
    console.log('Ingredient availability changed:', update);

    if (!update.is_available) {
      // Find subcategories that use this ingredient as required
      const { data: affectedItems } = await this.supabase
        .from('subcategory_ingredients')
        .select(`
          subcategory_id,
          subcategories(id, name_en, is_available)
        `)
        .eq('ingredient_id', update.ingredient_id)
        .eq('is_optional', false);

      if (affectedItems && affectedItems.length > 0) {
        // Auto-disable affected subcategories
        const itemIds = affectedItems
          .filter((item: any) => Array.isArray(item.subcategories) && item.subcategories.length > 0 && item.subcategories[0].is_available)
          .map((item: any) => item.subcategory_id);

        if (itemIds.length > 0) {
          await this.supabase
            .from('subcategories')
            .update({ is_available: false })
            .in('id', itemIds);

          update.affected_subcategories = itemIds;

          // Queue sync events for each affected item
          for (const itemId of itemIds) {
            await this.queueSync('availability', itemId, 'update', {
              auto_disabled: true,
              reason: 'ingredient_unavailable',
              ingredient_id: update.ingredient_id
            }, 1);
          }
        }
      }
    }

    this.emit('ingredient_availability_change', update);
  }

  /**
   * Handle subcategory availability changes
   */
  private async handleSubcategoryAvailabilityChange(update: SubcategoryAvailabilityUpdate) {
    console.log('Subcategory availability changed:', update);
    this.emit('subcategory_availability_change', update);
  }

  /**
   * Handle stock level changes
   */
  private async handleStockLevelChange(ingredient: any) {
    if (ingredient.stock_quantity <= ingredient.min_stock_level) {
      console.log('Low stock alert for ingredient:', ingredient.name);
      
      this.emit('low_stock_alert', {
        ingredient_id: ingredient.id,
        ingredient_name: ingredient.name,
        current_stock: ingredient.stock_quantity,
        min_level: ingredient.min_stock_level
      });

      // If stock is zero, mark as unavailable
      if (ingredient.stock_quantity === 0 && ingredient.is_available) {
        await this.supabase
          .from('ingredients')
          .update({ is_available: false })
          .eq('id', ingredient.id);
      }
    }
  }

  /**
   * Handle delivery zone changes
   */
  private handleDeliveryZoneChange(payload: any) {
    console.log('Delivery zone changed:', payload);
    
    const event: MenuSyncEvent = {
      id: crypto.randomUUID(),
      sync_type: 'delivery_pricing',
      resource_id: payload.new?.id || payload.old?.id,
      operation: payload.eventType.toLowerCase() as any,
      data_changes: payload.new || payload.old,
      target_platforms: ['pos', 'web', 'mobile'],
      timestamp: new Date().toISOString()
    };

    // Special handling for pricing changes
    if (payload.eventType === 'UPDATE' && 
        (payload.old?.delivery_fee !== payload.new?.delivery_fee ||
         payload.old?.minimum_order_amount !== payload.new?.minimum_order_amount ||
         payload.old?.is_active !== payload.new?.is_active)) {
      this.handleDeliveryZonePricingChange({
        zone_id: payload.new.id,
        delivery_fee: payload.new.delivery_fee,
        minimum_order_amount: payload.new.minimum_order_amount,
        is_active: payload.new.is_active
      });
    }

    this.emit('delivery_zone_change', event);
    this.emit('sync_event', event);
  }

  /**
   * Handle pricing setting changes
   */
  private handlePricingSettingChange(payload: any) {
    console.log('Pricing setting changed:', payload);
    
    const settingKey = payload.new?.setting_key || payload.old?.setting_key;
    const isPickupSetting = settingKey.includes('pickup');
    const isDeliverySetting = settingKey.includes('delivery');
    
    let syncType: 'pickup_pricing' | 'pricing_config' = 'pricing_config';
    if (isPickupSetting) {
      syncType = 'pickup_pricing';
    }

    const event: MenuSyncEvent = {
      id: crypto.randomUUID(),
      sync_type: syncType,
      resource_id: settingKey,
      operation: payload.eventType.toLowerCase() as any,
      data_changes: payload.new || payload.old,
      target_platforms: ['pos', 'web', 'mobile'],
      timestamp: new Date().toISOString()
    };

    // Handle specific pricing config changes
    if (payload.eventType === 'UPDATE' && 
        payload.old?.setting_value !== payload.new?.setting_value) {
      
      if (isPickupSetting) {
        this.handlePickupPricingChange({
          setting_key: settingKey,
          setting_value: payload.new.setting_value,
          previous_value: payload.old.setting_value,
          affected_platforms: ['pos', 'web', 'mobile']
        });
      } else {
        this.handlePricingConfigChange({
          config_type: isDeliverySetting ? 'delivery' : 'general',
          setting_key: settingKey,
          setting_value: payload.new.setting_value,
          requires_recalculation: true
        });
      }
    }

    this.emit('pricing_setting_change', event);
    this.emit('sync_event', event);
  }

  /**
   * Handle delivery zone pricing changes
   */
  private async handleDeliveryZonePricingChange(update: DeliveryZonePricingUpdate) {
    console.log('Delivery zone pricing changed:', update);

    // Find orders that might be affected by this pricing change
    const { data: affectedOrders } = await this.supabase
      .from('orders')
      .select('id, order_type, status, total_amount')
      .eq('delivery_zone_id', update.zone_id)
      .in('status', ['pending', 'confirmed', 'preparing'])
      .eq('order_type', 'delivery');

    if (affectedOrders && affectedOrders.length > 0) {
      update.affected_orders = affectedOrders.map((order: any) => order.id);
      
      // Queue pricing recalculation for affected orders
      for (const order of affectedOrders) {
        await this.queueSync('delivery_pricing', order.id, 'update', {
          recalculation_required: true,
          zone_change: true,
          new_delivery_fee: update.delivery_fee,
          new_minimum_order: update.minimum_order_amount
        }, 2); // High priority for pricing updates
      }
    }

    this.emit('delivery_zone_pricing_change', update);
  }

  /**
   * Handle pickup pricing changes
   */
  private async handlePickupPricingChange(update: PickupPricingUpdate) {
    console.log('Pickup pricing changed:', update);

    // For pickup pricing changes, we might need to recalculate pending pickup orders
    if (update.setting_key === 'pickup_discount_percentage') {
      const { data: affectedOrders } = await this.supabase
        .from('orders')
        .select('id, total_amount')
        .eq('order_type', 'takeaway') // 'pickup' is mapped to 'takeaway' in database
        .in('status', ['pending', 'confirmed']);

      if (affectedOrders && affectedOrders.length > 0) {
        // Queue pricing recalculation for affected pickup orders
        for (const order of affectedOrders) {
          await this.queueSync('pickup_pricing', order.id, 'update', {
            recalculation_required: true,
            new_discount_rate: update.setting_value,
            previous_discount_rate: update.previous_value
          }, 2); // High priority
        }
      }
    }

    this.emit('pickup_pricing_change', update);
  }

  /**
   * Handle general pricing config changes
   */
  private async handlePricingConfigChange(update: PricingConfigUpdate) {
    console.log('Pricing config changed:', update);

    // For general config changes (tax rate, service fee), might affect all pending orders
    if (update.requires_recalculation && 
        ['tax_rate', 'service_fee_rate'].includes(update.setting_key)) {
      
      const { data: affectedOrders } = await this.supabase
        .from('orders')
        .select('id, order_type')
        .in('status', ['pending', 'confirmed']);

      if (affectedOrders && affectedOrders.length > 0) {
        // Queue recalculation for all pending orders
        for (const order of affectedOrders) {
          await this.queueSync('pricing_config', order.id, 'update', {
            recalculation_required: true,
            config_change: update.setting_key,
            new_value: update.setting_value
          }, 3); // Medium priority
        }
      }
    }

    this.emit('pricing_config_change', update);
  }

  /**
   * Queue a sync operation
   */
  async queueSync(
    syncType: 'subcategory' | 'ingredient' | 'category' | 'availability' | 'delivery_pricing' | 'pickup_pricing' | 'pricing_config',
    resourceId: string,
    operation: 'insert' | 'update' | 'delete',
    dataChanges: any,
    priority: number = 5
  ) {
    const { error } = await this.supabase
      .from('menu_sync_queue')
      .insert({
        sync_type: syncType,
        resource_id: resourceId,
        operation,
        data_changes: dataChanges,
        priority
      });

    if (error) {
      console.error('Error queuing sync:', error);
      throw error;
    }
  }

  /**
   * Process availability sync
   */
  private async processAvailabilitySync(syncItem: any) {
    console.log('Processing availability sync:', syncItem);
    // Implementation would depend on target platforms
    // For now, just emit the event
    this.emit('availability_sync', syncItem);
  }

  /**
   * Process subcategory sync
   */
  private async processSubcategorySync(syncItem: any) {
    console.log('Processing subcategory sync:', syncItem);
    this.emit('subcategory_sync', syncItem);
  }

  /**
   * Process ingredient sync
   */
  private async processIngredientSync(syncItem: any) {
    console.log('Processing ingredient sync:', syncItem);
    this.emit('ingredient_sync', syncItem);
  }

  /**
   * Update ingredient stock
   */
  async updateIngredientStock(ingredientId: string, newQuantity: number) {
    const { data, error } = await this.supabase
      .rpc('update_ingredient_stock_and_sync', {
        ingredient_id_param: ingredientId,
        new_quantity: newQuantity
      });

    if (error) {
      console.error('Error updating ingredient stock:', error);
      throw error;
    }

    return data;
  }

  /**
   * Get sync queue status
   */
  async getSyncQueueStatus() {
    const { data, error } = await this.supabase
      .from('menu_sync_queue')
      .select('sync_status');

    if (error) {
      console.error('Error getting sync queue status:', error);
      return {};
    }

    // Group by sync_status and count manually
    return (data || []).reduce((acc: Record<string, number>, item: any) => {
      acc[item.sync_status] = (acc[item.sync_status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Get pending sync items
   */
  async getPendingSyncItems(limit: number = 50) {
    const { data, error } = await this.supabase
      .from('menu_sync_queue')
      .select('*')
      .in('sync_status', ['pending', 'failed'])
      .order('priority')
      .order('created_at')
      .limit(limit);

    if (error) {
      console.error('Error getting pending sync items:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Process delivery pricing sync
   */
  private async processDeliveryPricingSync(syncItem: any) {
    console.log('Processing delivery pricing sync:', syncItem);
    
    // For delivery pricing sync, we might need to:
    // 1. Update cached delivery zone data in POS systems
    // 2. Recalculate affected order totals
    // 3. Notify users about pricing changes
    
    if (syncItem.data_changes.recalculation_required) {
      console.log('Delivery pricing recalculation required for order:', syncItem.resource_id);
      
      // Emit event for POS systems to update pricing
      this.emit('delivery_pricing_sync', {
        order_id: syncItem.resource_id,
        zone_change: syncItem.data_changes.zone_change,
        new_delivery_fee: syncItem.data_changes.new_delivery_fee,
        new_minimum_order: syncItem.data_changes.new_minimum_order
      });
    }
    
    this.emit('delivery_pricing_sync', syncItem);
  }

  /**
   * Process pickup pricing sync
   */
  private async processPickupPricingSync(syncItem: any) {
    console.log('Processing pickup pricing sync:', syncItem);
    
    if (syncItem.data_changes.recalculation_required) {
      console.log('Pickup pricing recalculation required for order:', syncItem.resource_id);
      
      // Emit event for POS systems to update pickup pricing
      this.emit('pickup_pricing_sync', {
        order_id: syncItem.resource_id,
        new_discount_rate: syncItem.data_changes.new_discount_rate,
        previous_discount_rate: syncItem.data_changes.previous_discount_rate
      });
    }
    
    this.emit('pickup_pricing_sync', syncItem);
  }

  /**
   * Process pricing config sync
   */
  private async processPricingConfigSync(syncItem: any) {
    console.log('Processing pricing config sync:', syncItem);
    
    if (syncItem.data_changes.recalculation_required) {
      console.log('Pricing config recalculation required for order:', syncItem.resource_id);
      
      // Emit event for POS systems to update general pricing config
      this.emit('pricing_config_sync', {
        order_id: syncItem.resource_id,
        config_change: syncItem.data_changes.config_change,
        new_value: syncItem.data_changes.new_value
      });
    }
    
    this.emit('pricing_config_sync', syncItem);
  }

  /**
   * Recalculate order pricing
   */
  async recalculateOrderPricing(orderId: string, orderType: 'pickup' | 'delivery', coordinates?: { lat: number; lng: number }) {
    try {
      // Get current order details
      const { data: order, error: orderError } = await this.supabase
        .from('orders')
        .select('*')
        .eq('id', orderId)
        .single();

      if (orderError || !order) {
        throw new Error(`Order not found: ${orderId}`);
      }

      // Calculate new pricing using the database function
      const { data: pricingData, error: pricingError } = await this.supabase
        .rpc('calculate_order_pricing', {
          p_order_type: orderType,
          p_subtotal: order.subtotal || 0,
          p_delivery_address_lat: coordinates?.lat || null,
          p_delivery_address_lng: coordinates?.lng || null,
          p_branch_id: order.branch_id || null
        });

      if (pricingError) {
        throw new Error(`Pricing calculation failed: ${pricingError.message}`);
      }

      if (pricingData && pricingData.length > 0) {
        const pricing = pricingData[0];
        
        // Update order with new pricing
        const { error: updateError } = await this.supabase
          .from('orders')
          .update({
            delivery_fee: pricing.delivery_fee,
            pickup_discount: pricing.pickup_discount,
            service_fee: pricing.service_fee,
            tax_amount: pricing.tax_amount,
            total_amount: pricing.total_amount,
            delivery_zone_id: pricing.delivery_zone_id,
            pricing_calculated_at: new Date().toISOString(),
            pricing_version: '1.0'
          })
          .eq('id', orderId);

        if (updateError) {
          throw new Error(`Order update failed: ${updateError.message}`);
        }

        console.log('Order pricing recalculated successfully:', orderId);
        return pricing;
      }
    } catch (error) {
      console.error('Error recalculating order pricing:', error);
      throw error;
    }
  }

  /**
   * Retry failed sync items
   */
  async retryFailedSyncItems() {
    const { error } = await this.supabase
      .from('menu_sync_queue')
      .update({ 
        sync_status: 'pending',
        error_message: null,
        last_sync_attempt: null
      })
      .eq('sync_status', 'failed')
      .lt('sync_attempts', 3); // Only retry items that haven't been attempted 3 times

    if (error) {
      console.error('Error retrying failed sync items:', error);
      throw error;
    }
  }
}

// Singleton instance with proper lifecycle management
let menuSyncServiceInstance: MenuSyncService | null = null;

export const getMenuSyncService = (): MenuSyncService => {
  if (!menuSyncServiceInstance) {
    menuSyncServiceInstance = new MenuSyncService();
  }
  return menuSyncServiceInstance;
};

// Clean up singleton on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', async () => {
    if (menuSyncServiceInstance) {
      await menuSyncServiceInstance.cleanup();
      menuSyncServiceInstance = null;
    }
  });
}

// Export singleton instance (backward compatibility)
export const menuSyncService = getMenuSyncService();