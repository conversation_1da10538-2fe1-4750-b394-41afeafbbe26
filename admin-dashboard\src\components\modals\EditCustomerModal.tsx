'use client'

import { useState } from 'react'
import { Modal } from './ModalSystem'
import { CustomerForm } from '../forms/CustomerForm'
import toast from 'react-hot-toast'

interface Customer {
  id: string
  name: string
  ringer_name?: string
  phone: string
  email?: string
  address?: string
  postal_code?: string
  notes?: string
}

interface CustomerFormData {
  name: string
  ringer_name: string
  phone: string
  email: string
  address: string
  postal_code: string
  notes: string
}

interface EditCustomerModalProps {
  isOpen: boolean
  onClose: () => void
  customer: Customer | null
  onCustomerUpdated: () => void
}

export function EditCustomerModal({ 
  isOpen, 
  onClose, 
  customer, 
  onCustomerUpdated 
}: EditCustomerModalProps) {
  const [isLoading, setIsLoading] = useState(false)

  if (!customer) return null

  const handleSubmit = async (formData: CustomerFormData) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/customers/${customer.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Customer updated successfully!')
        onCustomerUpdated()
        onClose()
      } else {
        toast.error(result.error || 'Failed to update customer')
      }
    } catch (error) {
      console.error('Error updating customer:', error)
      toast.error('Failed to update customer')
    } finally {
      setIsLoading(false)
    }
  }

  const initialData = {
    name: customer.name || '',
    ringer_name: customer.ringer_name || '',
    phone: customer.phone || '',
    email: customer.email || '',
    address: customer.address || '',
    postal_code: customer.postal_code || '',
    notes: customer.notes || ''
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Edit Customer"
      size="lg"
    >
      <CustomerForm
        initialData={initialData}
        onSubmit={handleSubmit}
        onCancel={onClose}
        submitText="Update Customer"
        isLoading={isLoading}
      />
    </Modal>
  )
}