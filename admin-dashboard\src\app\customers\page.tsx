'use client'

import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useTheme } from '@/contexts/theme-context'
import { GlassCard } from '@/components/ui/glass-components'
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Users } from 'lucide-react'
import toast from 'react-hot-toast'

// Import our new modular components
import { CustomerFilters } from '@/components/customers/CustomerFilters'
import { CustomerList } from '@/components/customers/CustomerList'
import { AddCustomerModal } from '@/components/modals/AddCustomerModal'
import { EditCustomerModal } from '@/components/modals/EditCustomerModal'
import { ConfirmModal } from '@/components/modals/ModalSystem'

// Type definitions
interface CustomerAddress {
  id: string
  customer_id: string
  address_type: 'home' | 'work' | 'other'
  street_address: string
  postal_code?: string
  city?: string
  is_default: boolean
  created_at: string
  updated_at: string
}

interface Customer {
  id: string
  name: string
  ringer_name?: string
  phone: string
  email?: string
  address?: string
  postal_code?: string
  notes?: string
  loyalty_points?: number
  total_orders?: number
  created_at: string
  updated_at: string
  addresses?: CustomerAddress[]
  is_vip?: boolean
}

export default function CustomersPage() {
  const { t } = useTranslation()
  const { isDarkTheme } = useTheme()
  
  // State management
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // Modal states
  const [showAddCustomer, setShowAddCustomer] = useState(false)
  const [showEditCustomer, setShowEditCustomer] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null)

  // Load customers with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadCustomers()
    }, searchTerm ? 500 : 0)
    
    return () => clearTimeout(timeoutId)
  }, [searchTerm, filterStatus, sortBy, sortOrder])

  const loadCustomers = async () => {
    try {
      setLoading(true)
      
      const params = new URLSearchParams()
      if (searchTerm) params.append('search', searchTerm)
      if (filterStatus !== 'all') params.append('filter', filterStatus)
      params.append('sortBy', sortBy)
      params.append('sortOrder', sortOrder)

      const response = await fetch(`/api/customers?${params}`)
      const data = await response.json()

      if (Array.isArray(data)) {
        setCustomers(data)
      } else {
        console.error('Unexpected API response:', data)
        setCustomers([])
      }
    } catch (error) {
      console.error('Error loading customers:', error)
      toast.error('Failed to load customers')
      setCustomers([])
    } finally {
      setLoading(false)
    }
  }

  const handleViewCustomer = (customer: Customer) => {
    setEditingCustomer(customer)
    setShowEditCustomer(true)
  }

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer)
    setShowEditCustomer(true)
  }

  const handleDeleteCustomer = (customer: Customer) => {
    setCustomerToDelete(customer)
    setShowDeleteConfirm(true)
  }

  const confirmDeleteCustomer = async () => {
    if (!customerToDelete) return

    try {
      const response = await fetch(`/api/customers/${customerToDelete.id}`, {
        method: 'DELETE',
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Customer deleted successfully!')
        loadCustomers()
      } else {
        toast.error(result.error || 'Failed to delete customer')
      }
    } catch (error) {
      console.error('Error deleting customer:', error)
      toast.error('Failed to delete customer')
    } finally {
      setShowDeleteConfirm(false)
      setCustomerToDelete(null)
    }
  }

  const handleExportCustomers = () => {
    if (!customers.length) {
      toast.error('No customers to export')
      return
    }
    
    const csvData = customers.map(customer => ({
      Name: customer.name,
      Phone: customer.phone,
      Email: customer.email || '',
      'Total Orders': customer.total_orders || 0,
      'Loyalty Points': customer.loyalty_points || 0,
      'Created At': new Date(customer.created_at).toLocaleDateString()
    }))
    
    const csvString = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).join(','))
    ].join('\n')
    
    const blob = new Blob([csvString], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `customers-${new Date().toISOString().split('T')[0]}.csv`
    link.click()
    URL.revokeObjectURL(url)
    
    toast.success('Customer data exported successfully!')
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-3">
        <Users className="h-8 w-8 text-blue-400" />
        <h1 className={`text-3xl font-bold transition-colors duration-1000 ${
          isDarkTheme ? 'text-white' : 'text-black'
        }`}>
          {t('customers.title', 'Customer Management')}
        </h1>
      </div>

      <Tabs defaultValue="all" className="space-y-6">
        <TabsList className="bg-white/5 border-white/10">
          <TabsTrigger value="all" className="data-[state=active]:bg-white/10">
            All Customers
          </TabsTrigger>
          <TabsTrigger value="vip" className="data-[state=active]:bg-white/10">
            VIP Customers
          </TabsTrigger>
          <TabsTrigger value="recent" className="data-[state=active]:bg-white/10">
            Recent
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          {/* Filters and Search */}
          <CustomerFilters
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            filterStatus={filterStatus}
            onFilterStatusChange={setFilterStatus}
            sortBy={sortBy}
            onSortByChange={setSortBy}
            sortOrder={sortOrder}
            onSortOrderChange={setSortOrder}
            onAddCustomer={() => setShowAddCustomer(true)}
            onRefresh={loadCustomers}
            onExport={handleExportCustomers}
            totalCustomers={customers.length}
            loading={loading}
          />

          {/* Customer List */}
          <CustomerList
            customers={customers}
            loading={loading}
            onViewCustomer={handleViewCustomer}
            onEditCustomer={handleEditCustomer}
            onDeleteCustomer={handleDeleteCustomer}
          />
        </TabsContent>

        {/* Other tab contents would be similar */}
        <TabsContent value="vip">
          <GlassCard className="p-6">
            <p className={`transition-colors duration-1000 ${
              isDarkTheme ? 'text-white/60' : 'text-black/60'
            }`}>VIP customers view coming soon...</p>
          </GlassCard>
        </TabsContent>

        <TabsContent value="recent">
          <GlassCard className="p-6">
            <p className={`transition-colors duration-1000 ${
              isDarkTheme ? 'text-white/60' : 'text-black/60'
            }`}>Recent customers view coming soon...</p>
          </GlassCard>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      <AddCustomerModal
        isOpen={showAddCustomer}
        onClose={() => setShowAddCustomer(false)}
        onCustomerAdded={loadCustomers}
      />

      <EditCustomerModal
        isOpen={showEditCustomer}
        onClose={() => {
          setShowEditCustomer(false)
          setEditingCustomer(null)
        }}
        customer={editingCustomer}
        onCustomerUpdated={loadCustomers}
      />

      <ConfirmModal
        isOpen={showDeleteConfirm}
        onClose={() => {
          setShowDeleteConfirm(false)
          setCustomerToDelete(null)
        }}
        onConfirm={confirmDeleteCustomer}
        title="Delete Customer"
        message={`Are you sure you want to delete ${customerToDelete?.name}? This action cannot be undone.`}
        confirmText="Delete"
        variant="danger"
      />
    </div>
  )
}