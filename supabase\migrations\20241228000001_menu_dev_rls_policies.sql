-- Development-Friendly RLS Policies for Menu Tables
-- Adds permissive policies for development environment while keeping production policies

-- =============================================
-- DEVELOPMENT ENVIRONMENT DETECTION
-- =============================================
-- Set development environment (can be overridden in production)
SELECT set_config('app.environment', 'development', false);

-- =============================================
-- DEVELOPMENT RLS POLICIES FOR MENU TABLES
-- =============================================

-- Menu Categories - Development Access
CREATE POLICY "Dev: Any authenticated user can manage menu categories" ON menu_categories
  FOR ALL USING (
    current_setting('app.environment', true) = 'development' AND
    auth.role() = 'authenticated'
  );

-- Menu Categories - Service Role Access
CREATE POLICY "Service role full access to menu categories" ON menu_categories
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Ingredient Categories - Development Access  
CREATE POLICY "Dev: Any authenticated user can manage ingredient categories" ON ingredient_categories
  FOR ALL USING (
    current_setting('app.environment', true) = 'development' AND
    auth.role() = 'authenticated'
  );

-- Ingredient Categories - Service Role Access
CREATE POLICY "Service role full access to ingredient categories" ON ingredient_categories
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Ingredients - Development Access
CREATE POLICY "Dev: Any authenticated user can manage ingredients" ON ingredients
  FOR ALL USING (
    current_setting('app.environment', true) = 'development' AND
    auth.role() = 'authenticated'
  );

-- Ingredients - Service Role Access  
CREATE POLICY "Service role full access to ingredients" ON ingredients
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Menu Items - Development Access
CREATE POLICY "Dev: Any authenticated user can manage menu items" ON subcategories
  FOR ALL USING (
    current_setting('app.environment', true) = 'development' AND
    auth.role() = 'authenticated'
  );

-- Menu Items - Service Role Access
CREATE POLICY "Service role full access to menu items" ON subcategories
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Menu Item Ingredients - Development Access
CREATE POLICY "Dev: Any authenticated user can manage menu item ingredients" ON menu_item_ingredients
  FOR ALL USING (
    current_setting('app.environment', true) = 'development' AND
    auth.role() = 'authenticated'
  );

-- Menu Item Ingredients - Service Role Access
CREATE POLICY "Service role full access to menu item ingredients" ON menu_item_ingredients
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Customization Presets - Development Access
CREATE POLICY "Dev: Any authenticated user can manage customization presets" ON customization_presets
  FOR ALL USING (
    current_setting('app.environment', true) = 'development' AND
    auth.role() = 'authenticated'
  );

-- Customization Presets - Service Role Access
CREATE POLICY "Service role full access to customization presets" ON customization_presets
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Menu Sync Queue - Development Access
CREATE POLICY "Dev: Any authenticated user can read sync queue" ON menu_sync_queue
  FOR SELECT USING (
    current_setting('app.environment', true) = 'development' AND
    auth.role() = 'authenticated'
  );

-- =============================================
-- ENHANCED PUBLIC READ POLICIES
-- =============================================
-- These allow unauthenticated users to read active/available items
-- Useful for customer-facing applications

-- Enhanced public read for menu categories
DROP POLICY IF EXISTS "Public read access for menu categories" ON menu_categories;
CREATE POLICY "Enhanced public read access for menu categories" ON menu_categories 
  FOR SELECT USING (
    is_active = true OR 
    current_setting('app.environment', true) = 'development'
  );

-- Enhanced public read for ingredient categories  
DROP POLICY IF EXISTS "Public read access for ingredient categories" ON ingredient_categories;
CREATE POLICY "Enhanced public read access for ingredient categories" ON ingredient_categories 
  FOR SELECT USING (
    is_active = true OR 
    current_setting('app.environment', true) = 'development'
  );

-- Enhanced public read for ingredients
DROP POLICY IF EXISTS "Public read access for ingredients" ON ingredients;
CREATE POLICY "Enhanced public read access for ingredients" ON ingredients 
  FOR SELECT USING (
    is_available = true OR 
    current_setting('app.environment', true) = 'development'
  );

-- Enhanced public read for menu items
DROP POLICY IF EXISTS "Public read access for menu items" ON subcategories;
CREATE POLICY "Enhanced public read access for menu items" ON subcategories 
  FOR SELECT USING (
    is_available = true OR 
    current_setting('app.environment', true) = 'development'
  );

-- Enhanced public read for menu item ingredients
DROP POLICY IF EXISTS "Public read access for menu item ingredients" ON menu_item_ingredients;
CREATE POLICY "Enhanced public read access for menu item ingredients" ON menu_item_ingredients 
  FOR SELECT USING (true);

-- Enhanced public read for customization presets
DROP POLICY IF EXISTS "Public read access for customization presets" ON customization_presets;
CREATE POLICY "Enhanced public read access for customization presets" ON customization_presets 
  FOR SELECT USING (true);

-- =============================================
-- FUNCTIONS FOR ENVIRONMENT MANAGEMENT
-- =============================================

-- Function to toggle between development and production modes
CREATE OR REPLACE FUNCTION toggle_menu_dev_mode(enable boolean)
RETURNS void AS $$
BEGIN
  IF enable THEN
    PERFORM set_config('app.environment', 'development', false);
    RAISE NOTICE 'Menu development mode enabled - RLS policies are permissive';
  ELSE
    PERFORM set_config('app.environment', 'production', false);
    RAISE NOTICE 'Menu production mode enabled - RLS policies are restrictive';
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check current environment mode
CREATE OR REPLACE FUNCTION get_menu_environment_mode()
RETURNS text AS $$
BEGIN
  RETURN current_setting('app.environment', true);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION toggle_menu_dev_mode(boolean) TO authenticated;
GRANT EXECUTE ON FUNCTION get_menu_environment_mode() TO authenticated;
GRANT EXECUTE ON FUNCTION get_menu_environment_mode() TO anon;

-- =============================================
-- UPDATE EXISTING ADMIN POLICIES
-- =============================================
-- Update the existing admin policies to be more robust

-- Update menu categories admin policy
DROP POLICY IF EXISTS "Admin full access to menu categories" ON menu_categories;
CREATE POLICY "Admin full access to menu categories" ON menu_categories
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles up
      JOIN roles r ON up.role_id = r.id
      WHERE up.user_id = auth.uid() 
        AND r.name IN ('admin', 'manager')
        AND up.is_active = true
        AND r.is_active = true
    )
  );

-- Update ingredients admin policy  
DROP POLICY IF EXISTS "Admin full access to ingredients" ON ingredients;
CREATE POLICY "Admin full access to ingredients" ON ingredients
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles up
      JOIN roles r ON up.role_id = r.id
      WHERE up.user_id = auth.uid() 
        AND r.name IN ('admin', 'manager')
        AND up.is_active = true
        AND r.is_active = true
    )
  );

-- Update menu items admin policy
DROP POLICY IF EXISTS "Admin full access to menu items" ON subcategories;
CREATE POLICY "Admin full access to menu items" ON subcategories
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles up
      JOIN roles r ON up.role_id = r.id
      WHERE up.user_id = auth.uid() 
        AND r.name IN ('admin', 'manager')
        AND up.is_active = true
        AND r.is_active = true
    )
  );

-- =============================================
-- TESTING AND VALIDATION
-- =============================================

-- Function to test menu table access
CREATE OR REPLACE FUNCTION test_menu_table_access()
RETURNS TABLE(
  table_name text,
  can_select boolean,
  can_insert boolean,
  can_update boolean,
  can_delete boolean,
  error_message text
) AS $$
DECLARE
  tables text[] := ARRAY['menu_categories', 'ingredient_categories', 'ingredients', 'subcategories'];
  tbl text;
  test_result record;
BEGIN
  FOREACH tbl IN ARRAY tables
  LOOP
    BEGIN
      -- Test SELECT
      EXECUTE format('SELECT 1 FROM %I LIMIT 1', tbl);
      test_result.can_select := true;
    EXCEPTION WHEN OTHERS THEN
      test_result.can_select := false;
    END;
    
    test_result.table_name := tbl;
    test_result.can_insert := false; -- Would need actual test data
    test_result.can_update := false; -- Would need actual test data  
    test_result.can_delete := false; -- Would need actual test data
    test_result.error_message := CASE 
      WHEN test_result.can_select THEN 'OK'
      ELSE 'SELECT permission denied'
    END;
    
    RETURN NEXT test_result;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION test_menu_table_access() TO authenticated;
GRANT EXECUTE ON FUNCTION test_menu_table_access() TO anon;

-- =============================================
-- COMMENTS AND DOCUMENTATION
-- =============================================
COMMENT ON FUNCTION toggle_menu_dev_mode(boolean) IS 
'Toggle between development and production modes for menu table access. In development mode, any authenticated user can manage menu data.';

COMMENT ON FUNCTION get_menu_environment_mode() IS 
'Returns current environment mode (development/production) for menu table access policies.';

COMMENT ON FUNCTION test_menu_table_access() IS 
'Test function to check if current user can access menu tables. Useful for debugging RLS issues.';

-- Log the current state
DO $$
BEGIN
  RAISE NOTICE 'Menu development RLS policies installed. Current environment: %', current_setting('app.environment', true);
  RAISE NOTICE 'Use SELECT toggle_menu_dev_mode(true) to enable development mode';
  RAISE NOTICE 'Use SELECT toggle_menu_dev_mode(false) to enable production mode';
  RAISE NOTICE 'Use SELECT * FROM test_menu_table_access() to test access';
END $$;