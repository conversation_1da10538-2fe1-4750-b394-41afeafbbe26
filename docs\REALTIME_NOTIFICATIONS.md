# Real-time Notifications System

This document describes the implementation of real-time notifications for The Small Crêperie using Supabase Realtime, Edge Functions, and various notification channels.

## Overview

The real-time notification system provides:

- **Order Status Updates**: Real-time status changes, estimated delivery times, preparation progress
- **Customer Notifications**: Push notifications, email confirmations, SMS updates
- **Staff Notifications**: New order alerts, kitchen notifications, delivery assignments
- **Multi-channel Support**: Push, Email, SMS with user preferences
- **Real-time Dashboard**: Live updates for staff and customers

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Order Update  │───▶│  Database Trigger │───▶│  Edge Function  │
│   (Status Change)│    │                  │    │  (Notification) │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Push Service  │◀───│  Notification    │───▶│  Email Service  │
│   (Web Push)    │    │  Orchestrator    │    │  (Resend)       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   SMS Service   │
                       │   (Twilio)      │
                       └─────────────────┘
```

## Components

### 1. Database Schema

#### Tables Created

- `notification_logs`: Tracks all notification attempts and delivery status
- `staff_notifications`: Real-time notifications for staff dashboard
- `push_subscriptions`: Web push notification subscriptions
- `notification_attempts`: Batch notification tracking

#### Enhanced Tables

- `user_profiles.preferences`: User notification preferences and settings
- `orders`: Triggers for status changes

### 2. Edge Functions

#### `send-email-notification`
- Handles email notifications via Resend API
- Order status templates with HTML/text versions
- Delivery tracking and logging

#### `send-sms-notification`
- SMS notifications via Twilio API
- Phone number formatting and validation
- Quiet hours and preference checking

#### `send-push-notification`
- Web push notifications using VAPID
- Subscription management
- Cross-browser compatibility

#### `order-status-trigger`
- Main orchestrator for order status changes
- Multi-channel notification dispatch
- Staff notification creation
- Comprehensive logging

### 3. Frontend Components

#### Customer Components
- `useRealtimeOrder`: Real-time order tracking hook
- `RealTimeOrderTracker`: Order status display component
- `notification-service.ts`: Push notification management

#### Staff Components
- `useStaffNotifications`: Staff notification management
- `StaffNotificationCenter`: Admin dashboard component
- Real-time order alerts and sound notifications

## Setup Instructions

### 1. Prerequisites

```bash
# Install Supabase CLI
npm install -g supabase

# Generate VAPID keys for push notifications
npx web-push generate-vapid-keys
```

### 2. Environment Variables

Set the following environment variables:

```env
# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Email (Resend)
RESEND_API_KEY=your_resend_api_key

# SMS (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=your_twilio_phone

# Push Notifications (VAPID)
VAPID_PUBLIC_KEY=your_vapid_public_key
VAPID_PRIVATE_KEY=your_vapid_private_key

# Frontend
FRONTEND_URL=https://your-app-domain.com
```

### 3. Automated Setup

Run the setup script:

```powershell
# Basic setup
.\scripts\setup-realtime-notifications.ps1

# With project credentials
.\scripts\setup-realtime-notifications.ps1 -SupabaseProjectRef "your-ref" -SupabaseAccessToken "your-token"

# Skip migration or functions
.\scripts\setup-realtime-notifications.ps1 -SkipMigration -SkipFunctions
```

### 4. Manual Setup

#### Deploy Database Migration

```bash
supabase db push
```

#### Deploy Edge Functions

```bash
supabase functions deploy send-email-notification
supabase functions deploy send-sms-notification
supabase functions deploy send-push-notification
supabase functions deploy order-status-trigger
```

#### Configure Database Webhook

1. Go to Supabase Dashboard → Database → Webhooks
2. Create new webhook:
   - **Name**: Order Status Notifications
   - **Table**: orders
   - **Events**: Update
   - **URL**: `https://[project-ref].supabase.co/functions/v1/order-status-trigger`
   - **HTTP Headers**: `Authorization: Bearer [service-role-key]`

### 5. Enable Realtime

In Supabase Dashboard → Settings → API:
- Enable Realtime for `orders` table
- Enable Realtime for `staff_notifications` table
- Enable Realtime for `notification_logs` table

## Usage

### Customer Notifications

#### Real-time Order Tracking

```typescript
import { useRealtimeOrder } from '@/hooks/use-realtime-order';

function OrderTrackingPage({ orderId }: { orderId: string }) {
  const { order, isLoading, error } = useRealtimeOrder(orderId);
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  
  return (
    <div>
      <h2>Order #{order.id}</h2>
      <p>Status: {order.status}</p>
      <p>Estimated Delivery: {order.estimatedDeliveryTime}</p>
      {order.preparationProgress && (
        <div>Progress: {order.preparationProgress}%</div>
      )}
    </div>
  );
}
```

#### Push Notification Setup

```typescript
import { notificationService } from '@/lib/notifications/notification-service';

// Initialize service worker and request permissions
await notificationService.initialize();

// Subscribe to push notifications
const subscription = await notificationService.subscribeToPushNotifications();
```

### Staff Notifications

#### Staff Dashboard Integration

```typescript
import { useStaffNotifications } from '@/hooks/use-staff-notifications';
import { StaffNotificationCenter } from '@/components/notifications/staff-notification-center';

function AdminDashboard() {
  const {
    notifications,
    unreadCount,
    markAsRead,
    deleteNotification,
    playSound
  } = useStaffNotifications(['admin', 'manager']);
  
  return (
    <div>
      <h1>Dashboard ({unreadCount} new)</h1>
      <StaffNotificationCenter 
        notifications={notifications}
        onMarkAsRead={markAsRead}
        onDelete={deleteNotification}
      />
    </div>
  );
}
```

### Manual Notification Sending

#### Send Email Notification

```typescript
const response = await fetch('/api/notifications/email', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId: 'user-uuid',
    type: 'order_status_update',
    orderId: 'order-uuid',
    to: '<EMAIL>',
    subject: 'Order Update',
    orderData: {
      orderNumber: '12345',
      status: 'preparing',
      // ... other order data
    }
  })
});
```

#### Send SMS Notification

```typescript
const response = await fetch('/api/notifications/sms', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId: 'user-uuid',
    type: 'order_status_update',
    orderId: 'order-uuid',
    to: '+1234567890',
    message: 'Your order #12345 is now being prepared!',
    orderData: { /* order data */ }
  })
});
```

## Notification Types

### Order Status Updates

- `pending → confirmed`: Order confirmation
- `confirmed → preparing`: Kitchen started preparation
- `preparing → ready`: Order ready for pickup/delivery
- `preparing → out_for_delivery`: Order out for delivery
- `ready → completed`: Order completed (pickup)
- `out_for_delivery → delivered`: Order delivered
- `delivered → completed`: Order finalized
- `* → cancelled`: Order cancelled

### Staff Notifications

- `new_order`: New order received
- `order_status_change`: Order status updated
- `delivery_assigned`: Order ready for delivery
- `kitchen_alert`: Kitchen-specific notifications
- `order_cancelled`: Order cancellation alerts

## User Preferences

Users can configure notification preferences in their profile:

```json
{
  "pushNotifications": true,
  "emailNotifications": true,
  "smsNotifications": false,
  "orderUpdates": true,
  "promotions": true,
  "newsletters": false,
  "quietHours": {
    "enabled": false,
    "start": "22:00",
    "end": "08:00",
    "timezone": "UTC"
  }
}
```

## Monitoring and Analytics

### Notification Logs

All notifications are logged in the `notification_logs` table:

```sql
SELECT 
  notification_type,
  channel,
  status,
  COUNT(*) as count,
  AVG(EXTRACT(EPOCH FROM (sent_at - created_at))) as avg_delivery_time
FROM notification_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY notification_type, channel, status;
```

### User Statistics

```sql
SELECT * FROM get_user_notification_stats('user-uuid');
```

### Staff Notification Metrics

```sql
SELECT 
  type,
  priority,
  COUNT(*) as total,
  COUNT(*) FILTER (WHERE is_read = true) as read_count,
  AVG(EXTRACT(EPOCH FROM (NOW() - created_at))) as avg_age_seconds
FROM staff_notifications 
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY type, priority;
```

## Troubleshooting

### Common Issues

#### 1. Notifications Not Sending

- Check environment variables are set correctly
- Verify Edge Functions are deployed
- Check Supabase logs for errors
- Ensure webhook is configured properly

#### 2. Push Notifications Not Working

- Verify VAPID keys are set
- Check browser permissions
- Ensure service worker is registered
- Test with different browsers

#### 3. Email/SMS Failures

- Verify API credentials (Resend/Twilio)
- Check recipient addresses/phone numbers
- Review rate limits and quotas
- Check notification logs for error details

### Debug Commands

```bash
# Check Edge Function logs
supabase functions logs order-status-trigger

# Test Edge Function locally
supabase functions serve order-status-trigger

# Check database triggers
supabase db diff
```

### Testing

#### Test Order Status Change

```sql
-- Update order status to trigger notifications
UPDATE orders 
SET status = 'preparing' 
WHERE id = 'your-order-uuid';
```

#### Test Push Notification

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/send-push-notification' \
  -H 'Authorization: Bearer your-service-role-key' \
  -H 'Content-Type: application/json' \
  -d '{
    "userId": "user-uuid",
    "title": "Test Notification",
    "body": "This is a test push notification",
    "type": "test"
  }'
```

## Performance Considerations

- **Rate Limiting**: Implement rate limiting for notification APIs
- **Batch Processing**: Group notifications when possible
- **Cleanup**: Regular cleanup of old logs and expired notifications
- **Caching**: Cache user preferences and subscription data
- **Error Handling**: Implement retry logic for failed notifications

## Security

- **Row Level Security**: All tables have RLS policies
- **API Keys**: Store sensitive keys as environment variables
- **CORS**: Proper CORS configuration for Edge Functions
- **Validation**: Input validation for all notification payloads
- **Audit Trail**: Complete logging of all notification attempts

## Future Enhancements

- **Rich Push Notifications**: Images and action buttons
- **In-app Notifications**: Real-time in-app notification center
- **Notification Templates**: Visual template editor
- **A/B Testing**: Test different notification strategies
- **Analytics Dashboard**: Real-time notification analytics
- **Webhook Integrations**: Third-party service integrations
- **Multi-language Support**: Localized notification content

## Support

For issues or questions:

1. Check the troubleshooting section above
2. Review Supabase logs and Edge Function logs
3. Test individual components in isolation
4. Verify all environment variables and configurations

---

*Last updated: December 2024*