import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    const platform = searchParams.get('platform')
    const type = searchParams.get('type')
    const branchId = searchParams.get('branchId')

    let query = supabase
      .from('push_notification_settings')
      .select('*')
      .order('notification_type, platform')

    if (platform) {
      query = query.in('platform', [platform, 'both'])
    }

    if (type) {
      query = query.eq('notification_type', type)
    }

    if (branchId) {
      query = query.eq('branch_id', branchId)
    } else {
      query = query.is('branch_id', null)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching push notification settings:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const body = await request.json()
    const {
      notification_type,
      platform,
      title_template,
      body_template,
      icon_url,
      sound = 'default',
      deep_link_url,
      custom_data = {},
      schedule_type = 'immediate',
      schedule_config = {},
      targeting_rules = {},
      priority = 5,
      branch_id,
      is_enabled = true
    } = body

    const { data, error } = await supabase
      .from('push_notification_settings')
      .insert({
        notification_type,
        platform,
        title_template,
        body_template,
        icon_url,
        sound,
        deep_link_url,
        custom_data,
        schedule_type,
        schedule_config,
        targeting_rules,
        priority,
        branch_id,
        is_enabled
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating push notification setting:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const body = await request.json()
    const {
      id,
      title_template,
      body_template,
      icon_url,
      sound,
      deep_link_url,
      custom_data,
      schedule_type,
      schedule_config,
      targeting_rules,
      priority,
      is_enabled
    } = body

    if (!id) {
      return NextResponse.json({ error: 'ID is required' }, { status: 400 })
    }

    const { data, error } = await supabase
      .from('push_notification_settings')
      .update({
        title_template,
        body_template,
        icon_url,
        sound,
        deep_link_url,
        custom_data,
        schedule_type,
        schedule_config,
        targeting_rules,
        priority,
        is_enabled,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating push notification setting:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}