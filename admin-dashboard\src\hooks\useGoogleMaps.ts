'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { loadGoogleMaps } from '@/lib/google-maps';

interface UseGoogleMapsReturn {
  isLoaded: boolean;
  loadError: string | null;
  google: any | null;
}

/**
 * Custom hook for loading and managing Google Maps API
 * @returns Object containing loading state, error state, and google object
 */
export const useGoogleMaps = (): UseGoogleMapsReturn => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [google, setGoogle] = useState<any | null>(null);

  useEffect(() => {
    let isMounted = true;

    const initializeGoogleMaps = async () => {
      try {
        setLoadError(null);
        const googleInstance = await loadGoogleMaps();
        
        if (isMounted) {
          setGoogle(googleInstance);
          setIsLoaded(true);
        }
      } catch (error) {
        if (isMounted) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to load Google Maps';
          setLoadError(errorMessage);
          setIsLoaded(false);
        }
      }
    };

    initializeGoogleMaps();

    return () => {
      isMounted = false;
    };
  }, []);

  return {
    isLoaded,
    loadError,
    google,
  };
};

interface UseMapReturn {
  mapRef: React.RefObject<HTMLDivElement | null>;
  map: google.maps.Map | null;
  initializeMap: (options?: google.maps.MapOptions) => void;
}

/**
 * Custom hook for creating and managing a Google Map instance
 * @param options - Optional map configuration options
 * @returns Object containing map reference, map instance, and initialization function
 */
export const useMap = (options?: google.maps.MapOptions): UseMapReturn => {
  const { isLoaded, google } = useGoogleMaps();
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const mapRef = React.useRef<HTMLDivElement>(null);

  const initializeMap = useCallback((mapOptions?: google.maps.MapOptions) => {
    if (!isLoaded || !google || !mapRef.current) return;

    const defaultOptions: google.maps.MapOptions = {
      zoom: 12,
      center: { lat: 37.7749, lng: -122.4194 },
      mapTypeId: google.maps.MapTypeId.ROADMAP,
      ...options,
      ...mapOptions,
    };

    const mapInstance = new google.maps.Map(mapRef.current, defaultOptions);
    setMap(mapInstance);
  }, [isLoaded, google, options]);

  useEffect(() => {
    if (isLoaded && !map) {
      initializeMap();
    }
  }, [isLoaded, map, initializeMap]);

  return {
    mapRef,
    map,
    initializeMap,
  };
};

interface UseDrawingManagerReturn {
  drawingManager: google.maps.drawing.DrawingManager | null;
  initializeDrawingManager: (map: google.maps.Map, options?: google.maps.drawing.DrawingManagerOptions) => void;
  clearDrawing: () => void;
}

/**
 * Custom hook for managing Google Maps Drawing Manager
 * @returns Object containing drawing manager instance and control functions
 */
export const useDrawingManager = (): UseDrawingManagerReturn => {
  const { isLoaded, google } = useGoogleMaps();
  const [drawingManager, setDrawingManager] = useState<google.maps.drawing.DrawingManager | null>(null);

  const initializeDrawingManager = useCallback(
    (map: google.maps.Map, options?: google.maps.drawing.DrawingManagerOptions) => {
      if (!isLoaded || !google || !map) return;

      const defaultOptions: google.maps.drawing.DrawingManagerOptions = {
        drawingMode: null,
        drawingControl: true,
        drawingControlOptions: {
          position: google.maps.ControlPosition.TOP_CENTER,
          drawingModes: [google.maps.drawing.OverlayType.POLYGON],
        },
        polygonOptions: {
          fillColor: '#2563eb',
          fillOpacity: 0.3,
          strokeWeight: 2,
          strokeColor: '#1d4ed8',
          clickable: true,
          editable: true,
          zIndex: 1,
        },
        ...options,
      };

      const manager = new google.maps.drawing.DrawingManager(defaultOptions);
      manager.setMap(map);
      setDrawingManager(manager);
    },
    [isLoaded, google]
  );

  const clearDrawing = useCallback(() => {
    if (drawingManager) {
      drawingManager.setMap(null);
      setDrawingManager(null);
    }
  }, [drawingManager]);

  return {
    drawingManager,
    initializeDrawingManager,
    clearDrawing,
  };
};