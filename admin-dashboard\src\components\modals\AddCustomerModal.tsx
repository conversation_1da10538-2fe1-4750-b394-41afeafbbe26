'use client'

import { useState } from 'react'
import { Modal } from './ModalSystem'
import { CustomerForm } from '../forms/CustomerForm'
import toast from 'react-hot-toast'

interface CustomerFormData {
  name: string
  ringer_name: string
  phone: string
  email: string
  address: string
  postal_code: string
  notes: string
}

interface AddCustomerModalProps {
  isOpen: boolean
  onClose: () => void
  onCustomerAdded: () => void
}

export function AddCustomerModal({ isOpen, onClose, onCustomerAdded }: AddCustomerModalProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (formData: CustomerFormData) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Customer added successfully!')
        onCustomerAdded()
        onClose()
      } else {
        toast.error(result.error || 'Failed to add customer')
      }
    } catch (error) {
      console.error('Error adding customer:', error)
      toast.error('Failed to add customer')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Add New Customer"
      size="lg"
    >
      <CustomerForm
        onSubmit={handleSubmit}
        onCancel={onClose}
        submitText="Add Customer"
        isLoading={isLoading}
      />
    </Modal>
  )
}