import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Security headers for enhanced protection
const securityHeaders = {
  // Content Security Policy - Relaxed for development
  'Content-Security-Policy': 
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://localhost:* http://localhost:*; " +
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
    "font-src 'self' https://fonts.gstatic.com; " +
    "img-src 'self' data: https: blob:; " +
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co ws://localhost:* http://localhost:* https://localhost:*; " +
    "frame-src 'none'; " +
    "object-src 'none'; " +
    "base-uri 'self'; " +
    "form-action 'self'; " +
    "frame-ancestors 'none';",
  
  // Additional security headers
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 
    'camera=(), microphone=(), geolocation=(), payment=(), usb=(), ' +
    'magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=()',
  
  // HSTS for HTTPS enforcement (disabled for localhost)
  // 'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  
  // Cache control for auth pages
  'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
  'Pragma': 'no-cache',
  'Expires': '0',
  'Surrogate-Control': 'no-store',
}

// Enhanced CSP for auth pages (even stricter)
const authSecurityHeaders = {
  ...securityHeaders,
  'Content-Security-Policy': 
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://localhost:* http://localhost:*; " +
    "style-src 'self' 'unsafe-inline'; " +
    "font-src 'self' https://fonts.gstatic.com; " +
    "img-src 'self' data:; " +
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co ws://localhost:* http://localhost:* https://localhost:*; " +
    "frame-src 'none'; " +
    "object-src 'none'; " +
    "base-uri 'self'; " +
    "form-action 'self'; " +
    "frame-ancestors 'none';",
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Block Vite client requests (common in development tools/extensions)
  if (pathname === '/@vite/client' || pathname.startsWith('/@vite/')) {
    return new NextResponse('Vite client not available in Next.js', { 
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    })
  }
  
  // Protected routes that require authentication
  const protectedRoutes = ['/dashboard', '/orders', '/menu', '/customers', '/settings', '/system', '/pos', '/web', '/app']
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route))
  const isAuthRoute = pathname.startsWith('/auth/')
  
  // Check for authentication token
  const authToken = request.cookies.get('auth_session')?.value || 
                   request.cookies.get('session')?.value ||
                   request.headers.get('authorization')?.replace('Bearer ', '')
  
  // Redirect unauthenticated users from protected routes to login
  if (isProtectedRoute && !authToken) {
    const loginUrl = new URL('/auth/login', request.url)
    loginUrl.searchParams.set('redirect', pathname)
    return NextResponse.redirect(loginUrl)
  }
  
  // Redirect authenticated users from auth routes to dashboard
  if (isAuthRoute && authToken && pathname !== '/auth/logout') {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }
  
  const response = NextResponse.next()

  // Apply enhanced security headers for auth pages
  if (pathname.startsWith('/auth/')) {
    // Apply strict security headers for authentication pages
    Object.entries(authSecurityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value)
    })
    
    // Additional auth-specific headers
    response.headers.set('X-Robots-Tag', 'noindex, nofollow, noarchive, nosnippet')
    response.headers.set('X-Auth-Page', 'true')
    
    // Prevent caching of auth pages
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
    
    return response
  }

  // Apply standard security headers for other pages
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })

  // Admin dashboard protection
  if (pathname.startsWith('/dashboard') || pathname.startsWith('/admin')) {
    response.headers.set('X-Robots-Tag', 'noindex, nofollow')
    response.headers.set('X-Admin-Page', 'true')
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
}