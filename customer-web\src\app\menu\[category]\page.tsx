'use client';

import Image from 'next/image';
import { notFound } from 'next/navigation';
import { useEffect, useState, use } from 'react';
import { CartSummary } from '@/components/cart/cart-summary';
import { useMenuItems, useCategories } from '@/hooks/api-hooks';
import { Breadcrumb } from '@/components/navigation/breadcrumb';
import { PageLoading, MenuGridLoading } from '@/components/ui/loading-states';
import { MenuCategoryError, EmptyState } from '@/components/ui/error-states';
import { OrderTypeSelector } from '@/components/ui/OrderTypeSelector';
import type { MenuItem } from '@/types/types';

type Props = {
  params: Promise<{ category: string }>;
};

// Category variant mapping for styling
const getCategoryVariant = (categoryId: string) => {
  const variantMap: { [key: string]: string } = {
    'sweet-crepes': 'sweet',
    'savory-crepes': 'chocolate',
    'waffles': 'strawberry',
    'beverages': 'blueberry',
    'sides': 'secondary',
    'specials': 'primary',
  };
  return variantMap[categoryId] || 'primary';
};

export default function CategoryPage({ params }: Props) {
  const { category } = use(params);
  const { categories, loading: categoriesLoading, getCategoryById } = useCategories();
  const { getMenuItemsByCategory, loading: menuLoading } = useMenuItems();
  const [categoryData, setCategoryData] = useState<any>(null);
  const [menuItems, setMenuItems] = useState<any[]>([]);

  useEffect(() => {
    if (!categoriesLoading && categories.length > 0) {
      const foundCategory = getCategoryById(category);
      if (foundCategory) {
        setCategoryData({
          ...foundCategory,
          variant: getCategoryVariant(category),
        });
      } else {
        // Fallback for categories that might not be in database yet
        setCategoryData({
          id: category,
          name: category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' '),
          description: `Delicious ${category.replace('-', ' ')} items made fresh daily`,
          image_url: `/images/categories/${category}.jpg`,
          variant: getCategoryVariant(category),
        });
      }
    }
  }, [category, categories, categoriesLoading, getCategoryById]);

  useEffect(() => {
    if (!menuLoading) {
      const items = getMenuItemsByCategory(category);
      setMenuItems(items.map(item => ({
        id: item.id,
        name: item.name,
        description: item.description || '',
        price: item.price,
        image: item.image_url || `/images/menu/${category}-default.jpg`,
        popular: Math.random() > 0.7, // Random for now
        vegetarian: item.isVegetarian || false,
      })));
    }
  }, [category, menuLoading, getMenuItemsByCategory]);

  if (categoriesLoading || menuLoading) {
    return <PageLoading message="Loading menu..." />;
  }

  if (!categoryData) {
    notFound();
  }

  const handleRetry = () => {
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50">
      {/* Breadcrumb Navigation */}
      <div className="container mx-auto px-4 pt-4">
        <Breadcrumb />
      </div>

      {/* Order Type Selector */}
      <div className="container mx-auto px-4 py-4">
        <OrderTypeSelector />
      </div>

      {/* Category Banner */}
      <div className="relative h-64 overflow-hidden">
        <Image 
          src={categoryData.image_url || `/images/categories/${category}.jpg`} 
          alt={categoryData.name} 
          fill 
          className="object-cover" 
        />
        <div className="absolute inset-0 bg-black/40" />
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">{categoryData.name}</h1>
            <p className="text-lg md:text-xl opacity-90">{categoryData.description}</p>
          </div>
        </div>
      </div>

      {/* Main Content with Sidebar */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Menu Items - Main Content */}
          <div className="flex-1">
            {menuItems.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {menuItems.map(item => (
                  <MenuItemCard
                    key={item.id}
                    item={item}
                    categoryId={category}
                    categoryVariant={categoryData.variant}
                  />
                ))}
              </div>
            ) : (
              <EmptyState
                title="No items available"
                message={`We don't have any ${categoryData.name.toLowerCase()} items available right now. Please check back later or browse other categories.`}
                actionLabel="Browse Menu"
                onAction={() => window.location.href = '/menu'}
              />
            )}
          </div>

          {/* Cart Summary - Sidebar */}
          <div className="lg:w-80">
            <div className="sticky top-4">
              <CartSummary />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
