/**
 * Authentication-related React hooks for the customer-web project
 */

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/supabase/database.types';
import { User, UserProfile } from '@/types/types';

/**
 * Hook to manage authentication state
 */
export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();
  const router = useRouter();

  // Fetch user and profile on mount
  useEffect(() => {
    const fetchUserAndProfile = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get current session
        const {
          data: { session },
          error: sessionError,
        } = await supabase.auth.getSession();

        if (sessionError) {
          throw new Error(sessionError.message);
        }

        if (!session) {
          setUser(null);
          setProfile(null);
          return;
        }

        // Set user from session
        setUser(session.user as unknown as User);

        // Fetch user profile
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();

        if (profileError && profileError.code !== 'PGRST116') {
          // PGRST116 is "not found"
          throw new Error(profileError.message);
        }

        if (profileData) {
          setProfile(profileData as UserProfile);
        }
      } catch (err) {
        console.error('Error fetching user:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchUserAndProfile();

    // Subscribe to auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (session) {
        setUser(session.user as unknown as User);

        // Fetch profile when auth state changes
        const { data: profileData } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();

        if (profileData) {
          setProfile(profileData as UserProfile);
        }
      } else {
        setUser(null);
        setProfile(null);
      }
      setLoading(false);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase]);

  const signIn = useCallback(
    async (email: string, password: string) => {
      try {
        setLoading(true);
        setError(null);

        const { data, error: signInError } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (signInError) {
          throw new Error(signInError.message);
        }

        return { success: true, data };
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to sign in');
        return { success: false, error: err instanceof Error ? err.message : 'Failed to sign in' };
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  const signUp = useCallback(
    async (
      email: string,
      password: string,
      metadata?: { first_name?: string; last_name?: string; phone?: string }
    ) => {
      try {
        setLoading(true);
        setError(null);

        const { data, error: signUpError } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: metadata,
          },
        });

        if (signUpError) {
          throw new Error(signUpError.message);
        }

        // Create profile record
        if (data.user) {
          const { error: profileError } = await supabase.from('profiles').insert([
            {
              id: data.user.id,
              first_name: metadata?.first_name || '',
              last_name: metadata?.last_name || '',
              email: email,
              phone: metadata?.phone || '',
              created_at: new Date().toISOString(),
            },
          ]);

          if (profileError) {
            console.error('Error creating profile:', profileError);
          }
        }

        return { success: true, data };
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to sign up');
        return { success: false, error: err instanceof Error ? err.message : 'Failed to sign up' };
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  const signOut = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { error: signOutError } = await supabase.auth.signOut();

      if (signOutError) {
        throw new Error(signOutError.message);
      }

      // Redirect to home page after sign out
      router.push('/');
      return { success: true };
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sign out');
      return { success: false, error: err instanceof Error ? err.message : 'Failed to sign out' };
    } finally {
      setLoading(false);
    }
  }, [supabase, router]);

  const resetPassword = useCallback(
    async (email: string) => {
      try {
        setLoading(true);
        setError(null);

        const { error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
          redirectTo: `${window.location.origin}/reset-password`,
        });

        if (resetError) {
          throw new Error(resetError.message);
        }

        return { success: true };
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to send password reset email');
        return {
          success: false,
          error: err instanceof Error ? err.message : 'Failed to send password reset email',
        };
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  const updatePassword = useCallback(
    async (password: string) => {
      try {
        setLoading(true);
        setError(null);

        const { error: updateError } = await supabase.auth.updateUser({
          password,
        });

        if (updateError) {
          throw new Error(updateError.message);
        }

        return { success: true };
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to update password');
        return {
          success: false,
          error: err instanceof Error ? err.message : 'Failed to update password',
        };
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  const updateProfile = useCallback(
    async (updates: Partial<UserProfile>) => {
      try {
        setLoading(true);
        setError(null);

        if (!user) {
          throw new Error('User not authenticated');
        }

        // Update profile in database
        const { error: updateError } = await supabase
          .from('profiles')
          .update(updates)
          .eq('id', user.id);

        if (updateError) {
          throw new Error(updateError.message);
        }

        // Update local profile state
        setProfile(prev => (prev ? { ...prev, ...updates } : null));

        // If email is being updated, update auth email as well
        if (updates.email && updates.email !== user.email) {
          const { error: emailUpdateError } = await supabase.auth.updateUser({
            email: updates.email,
          });

          if (emailUpdateError) {
            throw new Error(emailUpdateError.message);
          }
        }

        return { success: true };
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to update profile');
        return {
          success: false,
          error: err instanceof Error ? err.message : 'Failed to update profile',
        };
      } finally {
        setLoading(false);
      }
    },
    [supabase, user]
  );

  const deleteAccount = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Delete profile first (due to foreign key constraints)
      const { error: profileDeleteError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', user.id);

      if (profileDeleteError) {
        throw new Error(profileDeleteError.message);
      }

      // Delete user from auth
      const { error: userDeleteError } = await supabase.auth.admin.deleteUser(user.id);

      if (userDeleteError) {
        throw new Error(userDeleteError.message);
      }

      // Sign out and redirect to home
      await signOut();
      return { success: true };
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete account');
      return {
        success: false,
        error: err instanceof Error ? err.message : 'Failed to delete account',
      };
    } finally {
      setLoading(false);
    }
  }, [supabase, user, signOut]);

  const signInWithGoogle = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: signInError } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (signInError) {
        throw new Error(signInError.message);
      }

      return { success: true, data };
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sign in with Google');
      return {
        success: false,
        error: err instanceof Error ? err.message : 'Failed to sign in with Google',
      };
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  return {
    user,
    profile,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile,
    deleteAccount,
    signInWithGoogle,
    isAuthenticated: !!user,
  };
}

/**
 * Hook to protect routes that require authentication
 */
export function useAuthGuard(redirectTo: string = '/login') {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push(redirectTo);
    }
  }, [user, loading, router, redirectTo]);

  return { user, loading };
}

/**
 * Hook to redirect authenticated users away from auth pages
 */
export function useAuthRedirect(redirectTo: string = '/') {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && user) {
      router.push(redirectTo);
    }
  }, [user, loading, router, redirectTo]);

  return { user, loading };
}

/**
 * Hook to check if user has admin role
 */
export function useIsAdmin() {
  const { user, profile } = useAuth();

  const isAdmin = user && profile?.role === 'admin';

  return { isAdmin };
}

/**
 * Hook to manage email verification
 */
export function useEmailVerification() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();

  const sendVerificationEmail = useCallback(
    async (email: string) => {
      try {
        setLoading(true);
        setError(null);

        const { error: verificationError } = await supabase.auth.resend({
          type: 'signup',
          email,
          options: {
            emailRedirectTo: `${window.location.origin}/auth/callback`,
          },
        });

        if (verificationError) {
          throw new Error(verificationError.message);
        }

        return { success: true };
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to send verification email');
        return {
          success: false,
          error: err instanceof Error ? err.message : 'Failed to send verification email',
        };
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  const verifyOtp = useCallback(
    async (email: string, token: string) => {
      try {
        setLoading(true);
        setError(null);

        const { error: verificationError } = await supabase.auth.verifyOtp({
          email,
          token,
          type: 'email',
        });

        if (verificationError) {
          throw new Error(verificationError.message);
        }

        return { success: true };
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to verify email');
        return {
          success: false,
          error: err instanceof Error ? err.message : 'Failed to verify email',
        };
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  return {
    loading,
    error,
    sendVerificationEmail,
    verifyOtp,
  };
}
