'use client'

import React, { useState } from 'react'
import { useTheme } from '@/contexts/theme-context'

export default function OrdersPage() {
  const { isDarkTheme } = useTheme()
  const [activeFilter, setActiveFilter] = useState('all')

  const filters = [
    { id: 'all', name: 'All Orders', count: 45 },
    { id: 'pending', name: 'Pending', count: 12 },
    { id: 'preparing', name: 'Preparing', count: 8 },
    { id: 'ready', name: 'Ready', count: 5 },
    { id: 'delivered', name: 'Delivered', count: 20 }
  ]

  const orders = [
    {
      id: '#ORD-001',
      customer: '<PERSON>',
      items: ['2x Burger', '1x Fries', '1x Coke'],
      total: 28.97,
      status: 'pending',
      time: '2 min ago',
      type: 'delivery'
    },
    {
      id: '#ORD-002',
      customer: '<PERSON>',
      items: ['1x Pizza Margherita', '2x Garlic Bread'],
      total: 24.50,
      status: 'preparing',
      time: '5 min ago',
      type: 'pickup'
    },
    {
      id: '#ORD-003',
      customer: '<PERSON>',
      items: ['3x Tacos', '1x Nachos', '2x Beer'],
      total: 35.75,
      status: 'ready',
      time: '8 min ago',
      type: 'dine-in'
    },
    {
      id: '#ORD-004',
      customer: 'Emily Davis',
      items: ['1x Caesar Salad', '1x Grilled Chicken'],
      total: 18.99,
      status: 'delivered',
      time: '15 min ago',
      type: 'delivery'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-500/20 text-yellow-300'
      case 'preparing': return 'bg-blue-500/20 text-blue-300'
      case 'ready': return 'bg-green-500/20 text-green-300'
      case 'delivered': return 'bg-gray-500/20 text-gray-300'
      default: return 'bg-gray-500/20 text-gray-300'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'delivery': return '🚚'
      case 'pickup': return '🏪'
      case 'dine-in': return '🍽️'
      default: return '📦'
    }
  }

  const filteredOrders = activeFilter === 'all' 
    ? orders 
    : orders.filter(order => order.status === activeFilter)

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className={`text-4xl font-bold mb-2 transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>Order Management</h1>
          <p className={`transition-colors duration-1000 ${
            isDarkTheme ? 'text-white/70' : 'text-black/70'
          }`}>Track and manage all restaurant orders</p>
        </div>

        {/* Order Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
          {filters.map((filter) => (
            <button
              key={filter.id}
              onClick={() => setActiveFilter(filter.id)}
              className={`p-4 rounded-2xl backdrop-blur-md border transition-all duration-300 ${
                activeFilter === filter.id
                  ? isDarkTheme
                    ? 'bg-white/20 border-white/30 text-white'
                    : 'bg-black/20 border-black/30 text-black'
                  : isDarkTheme
                    ? 'bg-white/10 border-white/20 text-white/70 hover:bg-white/15'
                    : 'bg-black/10 border-black/20 text-black/70 hover:bg-black/15'
              }`}
            >
              <div className="text-2xl font-bold mb-1">{filter.count}</div>
              <div className="text-sm">{filter.name}</div>
            </button>
          ))}
        </div>

        {/* Orders List */}
        <div className={`backdrop-blur-md border rounded-2xl overflow-hidden transition-all duration-1000 ${
          isDarkTheme
            ? 'bg-white/10 border-white/20'
            : 'bg-black/10 border-black/20'
        }`}>
          <div className={`p-6 border-b transition-colors duration-1000 ${
            isDarkTheme ? 'border-white/20' : 'border-black/20'
          }`}>
            <h2 className={`text-2xl font-bold transition-colors duration-1000 ${
              isDarkTheme ? 'text-white' : 'text-black'
            }`}>Recent Orders</h2>
          </div>
          
          <div className={`divide-y transition-colors duration-1000 ${
            isDarkTheme ? 'divide-white/10' : 'divide-black/10'
          }`}>
            {filteredOrders.map((order) => (
              <div key={order.id} className={`p-6 transition-all duration-300 ${
                isDarkTheme ? 'hover:bg-white/5' : 'hover:bg-black/5'
              }`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="text-2xl">{getTypeIcon(order.type)}</div>
                    <div>
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className={`text-lg font-semibold transition-colors duration-1000 ${
                          isDarkTheme ? 'text-white' : 'text-black'
                        }`}>{order.id}</h3>
                        <span className={`px-3 py-1 rounded-full text-sm ${getStatusColor(order.status)}`}>
                          {order.status}
                        </span>
                      </div>
                      <p className={`mb-1 transition-colors duration-1000 ${
                        isDarkTheme ? 'text-white/70' : 'text-black/70'
                      }`}>{order.customer}</p>
                      <p className={`text-sm transition-colors duration-1000 ${
                        isDarkTheme ? 'text-white/50' : 'text-black/50'
                      }`}>{order.items.join(', ')}</p>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className={`text-2xl font-bold mb-1 transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white' : 'text-black'
                    }`}>${order.total}</div>
                    <div className={`text-sm transition-colors duration-1000 ${
                      isDarkTheme ? 'text-white/50' : 'text-black/50'
                    }`}>{order.time}</div>
                    <div className="mt-2 space-x-2">
                      {order.status === 'pending' && (
                        <button className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-lg text-sm hover:bg-blue-500/30 transition-colors">
                          Accept
                        </button>
                      )}
                      {order.status === 'preparing' && (
                        <button className="px-3 py-1 bg-green-500/20 text-green-300 rounded-lg text-sm hover:bg-green-500/30 transition-colors">
                          Ready
                        </button>
                      )}
                      {order.status === 'ready' && (
                        <button className="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-lg text-sm hover:bg-purple-500/30 transition-colors">
                          Complete
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className={`backdrop-blur-md border rounded-2xl p-6 text-center transition-all duration-1000 ${
            isDarkTheme
              ? 'bg-white/10 border-white/20'
              : 'bg-black/10 border-black/20'
          }`}>
            <div className="text-4xl mb-4">📊</div>
            <h3 className={`text-xl font-bold mb-2 transition-colors duration-1000 ${
              isDarkTheme ? 'text-white' : 'text-black'
            }`}>Order Analytics</h3>
            <p className={`mb-4 transition-colors duration-1000 ${
              isDarkTheme ? 'text-white/70' : 'text-black/70'
            }`}>View detailed order statistics</p>
            <button className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-xl font-semibold hover:from-blue-600 hover:to-purple-700 transition-all duration-300">
              View Analytics
            </button>
          </div>

          <div className={`backdrop-blur-md border rounded-2xl p-6 text-center transition-all duration-1000 ${
            isDarkTheme
              ? 'bg-white/10 border-white/20'
              : 'bg-black/10 border-black/20'
          }`}>
            <div className="text-4xl mb-4">🔔</div>
            <h3 className={`text-xl font-bold mb-2 transition-colors duration-1000 ${
              isDarkTheme ? 'text-white' : 'text-black'
            }`}>Notifications</h3>
            <p className={`mb-4 transition-colors duration-1000 ${
              isDarkTheme ? 'text-white/70' : 'text-black/70'
            }`}>Manage order notifications</p>
            <button className="bg-gradient-to-r from-green-500 to-teal-600 text-white px-6 py-2 rounded-xl font-semibold hover:from-green-600 hover:to-teal-700 transition-all duration-300">
              Settings
            </button>
          </div>

          <div className={`backdrop-blur-md border rounded-2xl p-6 text-center transition-all duration-1000 ${
            isDarkTheme
              ? 'bg-white/10 border-white/20'
              : 'bg-black/10 border-black/20'
          }`}>
            <div className="text-4xl mb-4">📋</div>
            <h3 className={`text-xl font-bold mb-2 transition-colors duration-1000 ${
              isDarkTheme ? 'text-white' : 'text-black'
            }`}>Export Orders</h3>
            <p className={`mb-4 transition-colors duration-1000 ${
              isDarkTheme ? 'text-white/70' : 'text-black/70'
            }`}>Download order reports</p>
            <button className="bg-gradient-to-r from-orange-500 to-red-600 text-white px-6 py-2 rounded-xl font-semibold hover:from-orange-600 hover:to-red-700 transition-all duration-300">
              Export
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}