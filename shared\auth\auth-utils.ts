// Core Authentication Utilities for Creperie System

import { supabase, AUTH_CONFIG, AUTH_ERRORS, AUTH_SUCCESS } from './config';
import type {
  User,
  UserProfile,
  Role,
  LoginResponse,
  OTPRequest,
  OTPResponse,
  VerifyOTPRequest,
  PINVerificationRequest,
  Platform,
  Permission,
} from './types';
import { logAuthAttempt, createSession, validateSession, revokeSession } from './session-utils';
import { generateOTP, verifyOTP } from './otp-utils';
// import { hashPIN, verifyPIN } from './pin-utils'; // Commented out - unused

// Re-export password utilities
export { requestPasswordReset, resetPassword } from './password-utils';

// Re-export two-factor authentication utilities
export { generateTwoFASecret, verifyTwoFASetup } from './two-fa-utils';

/**
 * Email/Password Authentication (Admin/Staff)
 */
export async function loginWithEmail({
  email,
  password,
  platform,
}: {
  email: string;
  password: string;
  platform: Platform;
}): Promise<LoginResponse> {
  try {
    // Validate platform
    if (!['admin-dashboard', 'pos-system'].includes(platform)) {
      throw new Error(AUTH_ERRORS.PLATFORM_NOT_ALLOWED);
    }

    // Attempt authentication with Supabase
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (authError || !authData.user) {
      await logAuthAttempt({
        email,
        attempt_type: 'email_password',
        success: false,
        error_message: authError?.message || AUTH_ERRORS.INVALID_CREDENTIALS,
      });
      return {
        success: false,
        error: AUTH_ERRORS.INVALID_CREDENTIALS,
      };
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();

    if (profileError || !profile) {
      await logAuthAttempt({
        user_id: authData.user.id,
        email,
        attempt_type: 'email_password',
        success: false,
        error_message: AUTH_ERRORS.USER_NOT_FOUND,
      });
      return {
        success: false,
        error: AUTH_ERRORS.USER_NOT_FOUND,
      };
    }

    // Get staff information if user has staff_id
    let staff = null;
    let role = null;
    
    // If staff_id exists, get the staff record and role
    if (profile.staff_id) {
      // First get the staff record
      const { data: staffData, error: staffError } = await supabase
        .from('staff')
        .select('*')
        .eq('id', profile.staff_id)
        .single();
      
      if (staffData && !staffError) {
        staff = staffData;
        
        // Get the role for this staff member
        if (staff.role_id) {
          const { data: roleData, error: roleError } = await supabase
            .from('roles')
            .select('*')
            .eq('id', staff.role_id)
            .single();
            
          if (roleData && !roleError) {
            role = roleData;
          } else {
            console.error('Role query error:', roleError);
          }
        }
      } else {
        console.error('Staff query error:', staffError);
      }
    }

    // IMPORTANT FIX: For admin-dashboard login without staff role, get staff by email
    if (!staff && platform === 'admin-dashboard' && authData.user.email) {
      const { data: staffByEmail, error: staffEmailError } = await supabase
        .from('staff')
        .select('*')
        .eq('email', authData.user.email)
        .single();
        
      if (staffByEmail && !staffEmailError) {
        staff = staffByEmail;
        
        // Also update the user_profile with this staff_id
        await supabase
          .from('user_profiles')
          .update({ staff_id: staff.id })
          .eq('id', profile.id);
          
        // Get the role for this staff member
        if (staff.role_id) {
          const { data: roleData, error: roleError } = await supabase
            .from('roles')
            .select('*')
            .eq('id', staff.role_id)
            .single();
            
          if (roleData && !roleError) {
            role = roleData;
          } else {
            console.error('Role query error:', roleError);
          }
        }
      }
    }

    // Create a default role if none exists
    if (!role) {
      role = {
        id: '00000000-0000-0000-0000-000000000000',
        name: 'user',
        description: undefined,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      } as Role;
    }

    // Create a default staff record if this is admin/manager but no staff record
    if (!staff && platform === 'admin-dashboard') {
      // For admin-dashboard, we need to create a new staff record
      const { data: newStaff, error: newStaffError } = await supabase
        .from('staff')
        .insert({
          name: profile.full_name || (authData.user.email || '').split('@')[0],
          email: authData.user.email || '',
          role_id: '4a6c9d1d-ca7d-4849-be08-2e4cd2475ec8', // admin role id
          is_active: true
        })
        .select()
        .single();
        
      if (newStaff && !newStaffError) {
        staff = newStaff;
        
        // Update the user profile with the new staff_id
        await supabase
          .from('user_profiles')
          .update({ staff_id: staff.id })
          .eq('id', profile.id);
          
        // Get the admin role
        const { data: adminRole } = await supabase
          .from('roles')
          .select('*')
          .eq('id', '4a6c9d1d-ca7d-4849-be08-2e4cd2475ec8')
          .single();
          
        if (adminRole) {
          role = adminRole;
        }
      } else {
        console.error('Failed to create staff record:', newStaffError);
      }
    }

    // Check if user is active
    if (!profile.is_active) {
      await logAuthAttempt({
        user_id: authData.user.id,
        email,
        attempt_type: 'email_password',
        success: false,
        error_message: AUTH_ERRORS.USER_INACTIVE,
      });
      return {
        success: false,
        error: AUTH_ERRORS.USER_INACTIVE,
      };
    }

    // For debugging
    console.log('Platform access check:', {
      platform,
      roleName: role?.name,
      staffId: profile.staff_id,
      email: authData.user.email,
      allowAccess: (role && ['admin', 'manager'].includes(role.name.toLowerCase())) || platform !== 'admin-dashboard'
    });

    // MODIFIED: Platform access check - allow 'admin' or 'manager' roles only for admin-dashboard
    // But only if we're logging into the admin-dashboard
    if (platform === 'admin-dashboard' && role && !['admin', 'manager'].includes(role.name.toLowerCase())) {
      await logAuthAttempt({
        user_id: authData.user.id,
        email,
        attempt_type: 'email_password',
        success: false,
        error_message: AUTH_ERRORS.PLATFORM_NOT_ALLOWED,
      });
      return {
        success: false,
        error: AUTH_ERRORS.PLATFORM_NOT_ALLOWED,
      };
    }

    // Check if 2FA is required for admin
    const twoFAEnabled = profile.two_fa_enabled || false;
    if (platform === 'admin-dashboard' && AUTH_CONFIG.platforms.admin.requires2FA && !twoFAEnabled) {
      return {
        success: false,
        error: AUTH_ERRORS.TWO_FA_REQUIRED,
        requires_2fa: true,
      };
    }

    // Create session
    const session = await createSession({
      user_id: authData.user.id,
      platform,
    });

    // Log successful attempt
    await logAuthAttempt({
      user_id: authData.user.id,
      email,
      attempt_type: 'email_password',
      success: true,
    });

    // Update last_login_at in user_profiles
    await supabase
      .from('user_profiles')
      .update({ last_login_at: new Date().toISOString() })
      .eq('id', profile.id);

    // Return successful login with default objects if needed
    return {
      success: true,
      user: {
        id: authData.user.id,
        email: authData.user.email,
        phone: authData.user.phone,
        created_at: authData.user.created_at,
        updated_at: authData.user.updated_at,
      } as User,
      profile: {
        id: profile.id,
        user_id: profile.id, // user_profiles.id is the user_id
        staff_id: profile.staff_id,
        role_id: role?.id,
        branch_id: undefined, // Field doesn't exist in current schema
        full_name: profile.full_name,
        avatar_url: undefined,
        phone: profile.phone,
        phone_verified: false, // Default value since not in schema
        two_fa_enabled: profile.two_fa_enabled || false,
        last_login: profile.last_login_at,
        is_active: profile.is_active || false,
        created_at: profile.created_at || new Date().toISOString(),
        updated_at: profile.updated_at || new Date().toISOString(),
        // Additional properties for backward compatibility
        role: role,
        staff: staff,
      } as UserProfile & { role: any; staff: any },
      session,
      role: role as Role,
      access_token: authData.session?.access_token,
      refresh_token: authData.session?.refresh_token,
    };
  } catch (error) {
    console.error('Email login error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : AUTH_ERRORS.INVALID_CREDENTIALS,
    };
  }
}

/**
 * Phone OTP Authentication (Customers)
 */
export async function requestPhoneOTP({ phone, platform }: OTPRequest): Promise<OTPResponse> {
  try {
    // Validate platform
    if (!['customer-web', 'customer-mobile'].includes(platform)) {
      throw new Error(AUTH_ERRORS.PLATFORM_NOT_ALLOWED);
    }

    // Generate and send OTP
    const result = await generateOTP(phone);
    
    if (!result.success) {
      return {
        success: false,
        message: result.message,
        error: result.message,
      };
    }

    return {
      success: true,
      message: AUTH_SUCCESS.OTP_SENT,
      expires_in: AUTH_CONFIG.otp.expiryMinutes * 60,
    };
  } catch (error) {
    console.error('OTP request error:', error);
    return {
      success: false,
      message: AUTH_ERRORS.RATE_LIMIT_EXCEEDED,
      error: error instanceof Error ? error.message : AUTH_ERRORS.RATE_LIMIT_EXCEEDED,
    };
  }
}

export async function loginWithPhoneOTP({
  phone,
  otp,
  platform,
}: VerifyOTPRequest): Promise<LoginResponse> {
  try {
    // Validate platform
    if (!['customer-web', 'customer-mobile'].includes(platform)) {
      throw new Error(AUTH_ERRORS.PLATFORM_NOT_ALLOWED);
    }

    // Verify OTP
    const otpResult = await verifyOTP(phone, otp);
    
    if (!otpResult.success) {
      await logAuthAttempt({
        phone,
        attempt_type: 'phone_otp',
        success: false,
        error_message: AUTH_ERRORS.INVALID_OTP,
      });
      return {
        success: false,
        error: AUTH_ERRORS.INVALID_OTP,
      };
    }

    // Find or create user
    const { data, error } = await supabase.auth.signInWithOtp({
      phone,
      options: {
        shouldCreateUser: true,
      },
    });
    
    let authUser = data;
    const authError = error;

    if (authError) {
      // Try to get existing user by phone
      const { data: existingProfile } = await supabase
        .from('user_profiles')
        .select('id, role_id')
        .eq('phone_verified', true)
        .single();

      if (existingProfile) {
        // Get the auth user
        const { data: user } = await supabase.auth.admin.getUserById(existingProfile.id);
        if (user && user.user) {
          authUser = { 
            user: {
              id: user.user.id,
              email: user.user.email,
              phone: user.user.phone,
              created_at: user.user.created_at,
              updated_at: user.user.updated_at,
            } as User, 
            session: null 
          } as any;
        }
      }
    }

    if (!authUser?.user) {
      await logAuthAttempt({
        phone,
        attempt_type: 'phone_otp',
        success: false,
        error_message: AUTH_ERRORS.USER_NOT_FOUND,
      });
      return {
        success: false,
        error: AUTH_ERRORS.USER_NOT_FOUND,
      };
    }

    // Get or create user profile
    const { data: profileData, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', (authUser as any).user.id)
      .single();

    let profile = profileData;
    if (profileError || !profile) {
      // Create customer profile
      const { data: customerRole } = await supabase
        .from('roles')
        .select('id')
        .eq('name', 'customer')
        .single();

      if (customerRole) {
        const { data: newProfile, error: createError } = await supabase
          .from('user_profiles')
          .insert({
            id: (authUser as any).user.id,
            user_id: (authUser as any).user.id,
            role_id: customerRole.id,
            is_active: true,
          })
          .select('*')
          .single();

        if (createError) {
          return {
            success: false,
            error: AUTH_ERRORS.USER_NOT_FOUND,
          };
        }
        profile = newProfile;
      }
    }

    if (!profile) {
      return {
        success: false,
        error: AUTH_ERRORS.USER_NOT_FOUND,
      };
    }

    // Get role from role_id if it exists
    let role = null;
    if (profile.role_id) {
      const { data: roleData } = await supabase
        .from('roles')
        .select('id, name, description')
        .eq('id', profile.role_id)
        .single();
      role = roleData;
    }

    // Staff info is now part of the user profile
    let staff = null;
    if (profile.role_id) {
      // For backward compatibility, create a staff-like object from profile
      staff = {
        id: profile.id,
        name: `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || profile.email,
        email: profile.email
      };
    }

    if (!profile.is_active) {
      await logAuthAttempt({
        user_id: (authUser as any).user.id,
        phone,
        attempt_type: 'phone_otp',
        success: false,
        error_message: AUTH_ERRORS.USER_INACTIVE,
      });
      return {
        success: false,
        error: AUTH_ERRORS.USER_INACTIVE,
      };
    }

    // Create session
    const session = await createSession({
      user_id: (authUser as any).user.id,
      platform,
    });

    // Log successful attempt
    await logAuthAttempt({
      user_id: (authUser as any).user.id,
      phone,
      attempt_type: 'phone_otp',
      success: true,
    });

    // Note: last_login field doesn't exist in current schema, skipping update
    // await supabase
    //   .from('user_profiles')
    //   .update({ last_login: new Date().toISOString() })
    //   .eq('id', (authUser as any).user.id);

    return {
      success: true,
      user: {
        id: (authUser as any).user.id,
        email: (authUser as any).user.email,
        phone: (authUser as any).user.phone,
        created_at: (authUser as any).user.created_at,
        updated_at: (authUser as any).user.updated_at,
      } as User,
      profile: {
        id: profile.id,
        user_id: profile.user_id,
        role_id: profile.role_id,
        branch_id: undefined, // Field doesn't exist in current schema
        full_name: `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || undefined,
        avatar_url: undefined,
        phone_verified: false, // Default value since not in schema
        two_fa_enabled: (profile as any).two_fa_enabled || false,
        last_login: undefined, // Field doesn't exist in schema
        is_active: profile.is_active || false,
        created_at: profile.created_at || new Date().toISOString(),
        updated_at: profile.updated_at || new Date().toISOString(),
        // Additional properties for backward compatibility
        role: role,
        staff: staff,
      } as UserProfile & { role: any; staff: any },
      session,
    };
  } catch (error) {
    console.error('Phone OTP login error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : AUTH_ERRORS.INVALID_OTP,
    };
  }
}

/**
 * PIN Authentication (POS Staff)
 */
export async function loginWithPIN({ pin, staff_id: _staff_id }: PINVerificationRequest): Promise<LoginResponse> {
  try {
    // Verify PIN using database function
    const { data: pinResult, error: pinError } = await (supabase as any)
      .rpc('verify_staff_pin', { staff_pin: pin });

    if (pinError || !pinResult?.success) {
      await logAuthAttempt({
        attempt_type: 'pin',
        success: false,
        error_message: AUTH_ERRORS.INVALID_PIN,
      });
      return {
        success: false,
        error: AUTH_ERRORS.INVALID_PIN,
      };
    }

    // Get staff user profile
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', pinResult.staff_id)
      .eq('is_active', true)
      .single();

    if (profileError || !profile) {
      await logAuthAttempt({
        attempt_type: 'pin',
        success: false,
        error_message: AUTH_ERRORS.USER_NOT_FOUND,
      });
      return {
        success: false,
        error: AUTH_ERRORS.USER_NOT_FOUND,
      };
    }

    // Get role from role_id if it exists
    let role = null;
    if (profile.role_id) {
      const { data: roleData } = await supabase
        .from('roles')
        .select('id, name, description')
        .eq('id', profile.role_id)
        .single();
      role = roleData;
    }

    // Staff info is now part of the user profile
    let staff = null;
    if (profile.role_id) {
      // For backward compatibility, create a staff-like object from profile
      staff = {
        id: profile.id,
        name: `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || profile.email,
        email: profile.email
      };
    }

    // Get auth user
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(profile.id);
    
    if (authError || !authUser.user) {
      return {
        success: false,
        error: AUTH_ERRORS.USER_NOT_FOUND,
      };
    }

    // Create session
    const session = await createSession({
      user_id: profile.id,
      platform: 'pos-system',
    });

    // Log successful attempt
    await logAuthAttempt({
      user_id: profile.id,
      attempt_type: 'pin',
      success: true,
    });

    // Note: last_login field doesn't exist in current schema, skipping update
    // await supabase
    //   .from('user_profiles')
    //   .update({ last_login: new Date().toISOString() })
    //   .eq('id', profile.id);

    return {
      success: true,
      user: {
        id: (authUser as any).user.id,
        email: (authUser as any).user.email,
        phone: (authUser as any).user.phone,
        created_at: (authUser as any).user.created_at,
        updated_at: (authUser as any).user.updated_at,
      } as User,
      profile: {
        id: profile.id,
        user_id: profile.user_id,
        role_id: profile.role_id,
        branch_id: undefined, // Field doesn't exist in current schema
        full_name: `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || undefined,
        avatar_url: undefined,
        phone_verified: false, // Default value since not in schema
        two_fa_enabled: (profile as any).two_fa_enabled || false,
        last_login: undefined, // Field doesn't exist in schema
        is_active: profile.is_active || false,
        created_at: profile.created_at || new Date().toISOString(),
        updated_at: profile.updated_at || new Date().toISOString(),
        // Additional properties for backward compatibility
        role: role,
        staff: staff,
      } as UserProfile & { role: any; staff: any },
      session,
    };
  } catch (error) {
    console.error('PIN login error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : AUTH_ERRORS.INVALID_PIN,
    };
  }
}

/**
 * Logout Function
 */
export async function logout(sessionToken?: string): Promise<{ success: boolean; message: string }> {
  try {
    // Revoke session if provided
    if (sessionToken) {
      await revokeSession(sessionToken);
    }

    // Sign out from Supabase
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      console.error('Logout error:', error);
    }

    return {
      success: true,
      message: AUTH_SUCCESS.LOGOUT_SUCCESS,
    };
  } catch (error) {
    console.error('Logout error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Logout failed',
    };
  }
}

/**
 * Get User Profile by ID
 */
export async function getUserProfile(userId: string): Promise<{
  success: boolean;
  data?: UserProfile;
  error?: string;
}> {
  try {
    const { data: profile, error } = await supabase
      .from('user_profiles')
      .select('id, *') // Explicitly selecting 'id' first
      .eq('id', userId)
      .eq('is_active', true)
      .single();

    if (error || !profile) {
      return {
        success: false,
        error: 'User profile not found'
      };
    }

    // Get role from role_id if it exists
    let role = null;
    if (profile.role_id) {
      const { data: roleData } = await supabase
        .from('roles')
        .select('id, name, description')
        .eq('id', profile.role_id)
        .single();
      role = roleData;
    }

    // Staff info is now part of the user profile
    let staff = null;
    if (profile.role_id) {
      // For backward compatibility, create a staff-like object from profile
      staff = {
        id: profile.id,
        name: `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || profile.email,
        email: profile.email
      };
    }

    return {
      success: true,
      data: {
        ...profile,
        role: role,
        staff: staff,
      } as any
    };
  } catch (error) {
    console.error('Get user profile error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get user profile'
    };
  }
}

/**
 * Get Current User
 */
export async function getCurrentUser(): Promise<{
  user: User | null;
  profile: UserProfile | null;
  role: Role | null;
}> {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      return { user: null, profile: null, role: null };
    }

    const { data: profile } = await supabase
      .from('user_profiles')
      .select('id, *') // Explicitly selecting 'id' first
      .eq('id', user.id)
      .eq('is_active', true)
      .single();

    if (!profile) {
      return { user: user as any, profile: null, role: null };
    }

    // Get role from role_id if it exists
    let role = null;
    if (profile.role_id) {
      const { data: roleData } = await supabase
        .from('roles')
        .select('id, name, description')
        .eq('id', profile.role_id)
        .single();
      role = roleData;
    }

    // Staff info is now part of the user profile
    let staff = null;
    if (profile.role_id) {
      // For backward compatibility, create a staff-like object from profile
      staff = {
        id: profile.id,
        name: `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || profile.email,
        email: profile.email
      };
    }

    return {
      user: user as any,
      profile: {
        id: profile.id,
        user_id: profile.user_id,
        role_id: profile.role_id,
        branch_id: undefined, // Field doesn't exist in current schema
        full_name: `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || undefined,
        avatar_url: undefined,
        phone_verified: false, // Default value since not in schema
        two_fa_enabled: (profile as any).two_fa_enabled || false,
        last_login: undefined, // Field doesn't exist in schema
        is_active: profile.is_active || false,
        created_at: profile.created_at || new Date().toISOString(),
        updated_at: profile.updated_at || new Date().toISOString(),
        // Additional properties for backward compatibility
        role: role,
        staff: staff,
      } as UserProfile & { role: any; staff: any },
      role: role as Role,
    };
  } catch (error) {
    console.error('Get current user error:', error);
    return { user: null, profile: null, role: null };
  }
}

/**
 * Check User Permissions
 */
export async function checkUserPermissions(userId: string): Promise<Permission[]> {
  try {
    const { data: result, error } = await (supabase as any)
      .rpc('get_user_role_permissions', { user_uuid: userId });

    if (error || !result) {
      return [];
    }

    return result.permissions || [];
  } catch (error) {
    console.error('Check permissions error:', error);
    return [];
  }
}

/**
 * Validate User Session
 */
export async function validateUserSession(sessionToken: string): Promise<{
  valid: boolean;
  user?: User;
  profile?: UserProfile;
  role?: Role;
}> {
  try {
    const sessionResult = await validateSession(sessionToken);
    
    if (!sessionResult.valid || !sessionResult.session) {
      return { valid: false };
    }

    const { user, profile, role } = await getCurrentUser();
    
    return {
      valid: true,
      user: user || undefined,
      profile: profile || undefined,
      role: role || undefined,
    };
  } catch (error) {
    console.error('Validate session error:', error);
    return { valid: false };
  }
}