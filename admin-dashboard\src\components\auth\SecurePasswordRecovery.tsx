﻿'use client'

import React, { useState } from 'react'
import { passwordSecurityService, SecurityQuestion } from '@/services/password-security-service'
import PasswordStrengthIndicator from './PasswordStrengthIndicator'

interface SecurePasswordRecoveryProps {
  onRecoveryComplete?: () => void
  className?: string
}

type RecoveryStep = 'email' | 'verification' | 'security-questions' | 'new-password' | 'complete'

export default function SecurePasswordRecovery({
  onRecoveryComplete,
  className = ''
}: SecurePasswordRecoveryProps) {
  const [currentStep, setCurrentStep] = useState<RecoveryStep>('email')
  const [email, setEmail] = useState('')
  const [verificationCode, setVerificationCode] = useState('')
  const [recoveryToken, setRecoveryToken] = useState('')
  const [securityQuestions, setSecurityQuestions] = useState<SecurityQuestion[]>([])
  const [securityAnswers, setSecurityAnswers] = useState<Record<string, string>>({})
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [successMessage, setSuccessMessage] = useState('')

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const result = await passwordSecurityService.initiatePasswordRecovery(email, 'email')
      
      if (result.success) {
        setRecoveryToken(result.token || '')
        setSuccessMessage(result.message)
        setCurrentStep('verification')
        
        // Load security questions for additional verification
        const questions = passwordSecurityService.getSecurityQuestions()
        setSecurityQuestions(questions.slice(0, 2)) // Use first 2 questions
      } else {
        setError(result.message)
      }
    } catch (error) {
      setError('Failed to initiate password recovery. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const result = await passwordSecurityService.verifyRecoveryToken(verificationCode)
      
      if (result.isValid) {
        setSuccessMessage('Email verified successfully')
        setCurrentStep('security-questions')
      } else {
        setError(result.message)
      }
    } catch (error) {
      setError('Failed to verify code. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSecurityQuestionsSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    // Validate that all questions are answered
    const unansweredQuestions = securityQuestions.filter(q => !securityAnswers[q.id])
    if (unansweredQuestions.length > 0) {
      setError('Please answer all security questions')
      setIsLoading(false)
      return
    }

    try {
      // In a real implementation, this would verify answers against stored hashes
      // For now, we'll simulate successful verification
      setSuccessMessage('Security questions verified successfully')
      setCurrentStep('new-password')
    } catch (error) {
      setError('Security question verification failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    // Validate password match
    if (newPassword !== confirmPassword) {
      setError('Passwords do not match')
      setIsLoading(false)
      return
    }

    // Validate password strength
    try {
      const validation = await passwordSecurityService.validatePassword(newPassword)
      if (!validation.isValid) {
        const errorMessages = validation.violations
          .filter(v => v.severity === 'error')
          .map(v => v.message)
        setError(errorMessages.join('. '))
        setIsLoading(false)
        return
      }

      // In a real implementation, this would update the password in the database
      setSuccessMessage('Password reset successfully!')
      setCurrentStep('complete')
      
      // Auto-complete after 3 seconds
      setTimeout(() => {
        onRecoveryComplete?.()
      }, 3000)
      
    } catch (error) {
      setError('Failed to reset password. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const renderEmailStep = () => (
    <form onSubmit={handleEmailSubmit} className="space-y-4">
      <div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Reset Your Password</h2>
        <p className="text-sm text-gray-600 mb-4">
          Enter your email address and we'll send you a verification code to reset your password.
        </p>
      </div>

      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
          Email Address
        </label>
        <input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter your email address"
        />
      </div>

      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
      >
        {isLoading ? 'Sending...' : 'Send Verification Code'}
      </button>
          </form>
    )

  const renderVerificationStep = () => (
    <form onSubmit={handleVerificationSubmit} className="space-y-4">
      <div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Verify Your Email</h2>
        <p className="text-sm text-gray-600 mb-4">
          We've sent a verification code to {email}. Please enter the code below.
        </p>
      </div>

      <div>
        <label htmlFor="verification-code" className="block text-sm font-medium text-gray-700 mb-2">
          Verification Code
        </label>
        <input
          id="verification-code"
          type="text"
          value={verificationCode}
          onChange={(e) => setVerificationCode(e.target.value)}
          required
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center text-lg tracking-widest"
          placeholder="Enter 6-digit code"
          maxLength={6}
        />
      </div>

      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
      >
        {isLoading ? 'Verifying...' : 'Verify Code'}
      </button>

      <button
        type="button"
        onClick={() => setCurrentStep('email')}
        className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors"
      >
        Back to Email
      </button>
    </form>
  )

  const renderSecurityQuestionsStep = () => (
    <form onSubmit={handleSecurityQuestionsSubmit} className="space-y-4">
      <div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Security Questions</h2>
        <p className="text-sm text-gray-600 mb-4">
          Please answer these security questions to verify your identity.
        </p>
      </div>

      {securityQuestions.map((question) => (
        <div key={question.id}>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {question.question}
          </label>
          <input
            type="text"
            value={securityAnswers[question.id] || ''}
            onChange={(e) => setSecurityAnswers(prev => ({
              ...prev,
              [question.id]: e.target.value
            }))}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter your answer"
          />
        </div>
      ))}

      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
      >
        {isLoading ? 'Verifying...' : 'Verify Answers'}
      </button>

      <button
        type="button"
        onClick={() => setCurrentStep('verification')}
        className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors"
      >
        Back to Verification
      </button>
    </form>
  )

  const renderNewPasswordStep = () => (
    <form onSubmit={handlePasswordReset} className="space-y-4">
      <div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Create New Password</h2>
        <p className="text-sm text-gray-600 mb-4">
          Choose a strong password that you haven't used before.
        </p>
      </div>

      <div>
        <label htmlFor="new-password" className="block text-sm font-medium text-gray-700 mb-2">
          New Password
        </label>
        <input
          id="new-password"
          type="password"
          value={newPassword}
          onChange={(e) => setNewPassword(e.target.value)}
          required
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter new password"
        />
      </div>

      {newPassword && (
        <PasswordStrengthIndicator
          password={newPassword}
          showDetails={true}
        />
      )}

      <div>
        <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700 mb-2">
          Confirm New Password
        </label>
        <input
          id="confirm-password"
          type="password"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          required
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Confirm new password"
        />
      </div>

      {confirmPassword && newPassword !== confirmPassword && (
        <p className="text-sm text-red-600">Passwords do not match</p>
      )}

      <button
        type="submit"
        disabled={isLoading || !newPassword || !confirmPassword || newPassword !== confirmPassword}
        className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
      >
        {isLoading ? 'Resetting...' : 'Reset Password'}
      </button>

      <button
        type="button"
        onClick={() => setCurrentStep('security-questions')}
        className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors"
      >
        Back to Security Questions
      </button>
    </form>
  )
  
    const renderCompleteStep = () => (
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
          <span className="text-2xl">✅</span>
        </div>
      <h2 className="text-xl font-semibold text-gray-800">Password Reset Complete</h2>
      <p className="text-sm text-gray-600">
        Your password has been successfully reset. You can now log in with your new password.
      </p>
      <button
        onClick={onRecoveryComplete}
        className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors"
      >
        Return to Login
      </button>
    </div>
  )

  return (
    <div className={`max-w-md mx-auto ${className}`}>
      <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
        {/* Progress Indicator */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            {['email', 'verification', 'security-questions', 'new-password', 'complete'].map((step, index) => (
              <div
                key={step}
                className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                  ['email', 'verification', 'security-questions', 'new-password', 'complete'].indexOf(currentStep) >= index
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}
              >
                {index + 1}
              </div>
            ))}
          </div>
          <div className="w-full bg-gray-200 rounded-full h-1">
            <div
              className="bg-blue-600 h-1 rounded-full transition-all duration-300"
              style={{
                width: `${((['email', 'verification', 'security-questions', 'new-password', 'complete'].indexOf(currentStep) + 1) / 5) * 100}%`
              }}
            ></div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {/* Success Message */}
        {successMessage && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-800">{successMessage}</p>
          </div>
        )}

                  {/* Step Content */}
          {currentStep === 'email' && renderEmailStep()}
          {currentStep === 'verification' && renderVerificationStep()}
          {currentStep === 'security-questions' && renderSecurityQuestionsStep()}
          {currentStep === 'new-password' && renderNewPasswordStep()}
          {currentStep === 'complete' && renderCompleteStep()}
      </div>
    </div>
  )
}
