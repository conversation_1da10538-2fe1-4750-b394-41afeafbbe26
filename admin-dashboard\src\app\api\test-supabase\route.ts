import { NextRequest, NextResponse } from 'next/server'
// import { testSupabaseConnection, getSupabaseConfig } from '@/shared/config/supabase-config'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    console.log('Testing Supabase connection...')
    
    // Test Supabase connection using createServerSupabaseClient
    const supabase = createServerSupabaseClient()
    console.log('Testing Supabase connection...')
    
    // Test connection with a simple query
    const { data, error } = await supabase
      .from('restaurant_settings')
      .select('id')
      .limit(1)
    
    if (error) {
      console.error('Supabase connection error:', error)
      return NextResponse.json({
        success: false,
        message: 'Supabase connection failed',
        error: error.message,
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Supabase connection successful',
      timestamp: new Date().toISOString(),
      recordCount: data?.length || 0
    })
  } catch (error) {
    console.error('Supabase test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
