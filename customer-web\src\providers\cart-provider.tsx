'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Cart, CartItem, MenuItem } from '@/types/types';
import { getLocalStorage, setLocalStorage, safeJsonParse } from '@/lib/utils';

interface CartContextType {
  cart: Cart;
  itemCount: number;
  isCartOpen: boolean;
  setIsCartOpen: (open: boolean) => void;
  addItem: (
    menuItem: MenuItem,
    quantity?: number,
    variantId?: string,
    specialInstructions?: string
  ) => void;
  updateItem: (itemId: string, quantity: number, specialInstructions?: string) => void;
  removeItem: (itemId: string) => void;
  clearCart: () => void;
  setDeliveryFee: (fee: number) => void;
  setTip: (tip: number) => void;
  toggleCart: () => void;
}

const CartContext = createContext<CartContextType>({
  cart: {
    items: [],
    subtotal: 0,
    tax: 0,
    deliveryFee: 0,
    total: 0,
  },
  isCartOpen: false,
  setIsCartOpen: () => {},
  toggleCart: () => {},
  addItem: () => {},
  updateItem: () => {},
  removeItem: () => {},
  clearCart: () => {},
  setDeliveryFee: () => {},
  setTip: () => {},
  itemCount: 0,
});

export const useCartContext = () => useContext(CartContext);

interface CartProviderProps {
  children: React.ReactNode;
}

export default function CartProvider({ children }: CartProviderProps) {
  const [cart, setCart] = useState<Cart>({
    items: [],
    subtotal: 0,
    tax: 0,
    deliveryFee: 0,
    tip: 0,
    total: 0,
  });
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  // Load cart from localStorage on mount (client-side only)
  useEffect(() => {
    setIsHydrated(true);
    const savedCart = safeJsonParse<Cart>(getLocalStorage('cart', '{}'));
    if (savedCart && savedCart.items) {
      setCart(savedCart);
    }
  }, []);

  // Save cart to localStorage whenever it changes (client-side only)
  useEffect(() => {
    if (isHydrated) {
      setLocalStorage('cart', JSON.stringify(cart));
    }
  }, [cart, isHydrated]);

  // Calculate totals whenever items change
  useEffect(() => {
    const subtotal = cart.items.reduce((sum, item) => sum + item.price * item.quantity, 0);
    const tax = subtotal * 0.24; // 24% VAT
    const total = subtotal + tax + cart.deliveryFee + (cart.tip || 0);

    setCart(prev => ({
      ...prev,
      subtotal,
      tax,
      total,
    }));
  }, [cart.items, cart.deliveryFee, cart.tip]);

  const addItem = useCallback(
    (
      menuItem: MenuItem,
      quantity: number = 1,
      variantId?: string,
      specialInstructions?: string
    ) => {
      setCart(prev => {
        // Find selected variant if any
        const variant =
          variantId && menuItem.variants
            ? menuItem.variants.find(v => v.id === variantId)
            : undefined;

        // Calculate item price including variant if selected
        const itemPrice = variant ? variant.price : menuItem.price;

        // Check if item already exists with same variant and special instructions
        const existingItemIndex = prev.items.findIndex(
          item =>
            item.menuItem.id === menuItem.id &&
            item.variant?.id === variantId &&
            item.specialInstructions === specialInstructions
        );

        let newItems;
        if (existingItemIndex >= 0) {
          // Update quantity of existing item
          newItems = [...prev.items];
          newItems[existingItemIndex] = {
            ...newItems[existingItemIndex],
            quantity: newItems[existingItemIndex].quantity + quantity,
          };
        } else {
          // Add new item
          const newItem: CartItem = {
            id: `${menuItem.id}-${Math.random().toString(36).substr(2, 9)}`,
            menuItem,
            quantity,
            variant: variant,
            specialInstructions,
            price: itemPrice,
          };
          newItems = [...prev.items, newItem];
        }

        return {
          ...prev,
          items: newItems,
        };
      });
    },
    []
  );

  const updateItem = useCallback(
    (itemId: string, quantity: number, specialInstructions?: string) => {
      setCart(prev => {
        const itemIndex = prev.items.findIndex(item => item.id === itemId);
        if (itemIndex === -1) return prev;

        const newItems = [...prev.items];
        if (quantity <= 0) {
          // Remove item if quantity is 0 or negative
          newItems.splice(itemIndex, 1);
        } else {
          // Update item quantity and special instructions
          newItems[itemIndex] = {
            ...newItems[itemIndex],
            quantity,
            specialInstructions:
              specialInstructions !== undefined
                ? specialInstructions
                : newItems[itemIndex].specialInstructions,
          };
        }

        return {
          ...prev,
          items: newItems,
        };
      });
    },
    []
  );

  const removeItem = useCallback((itemId: string) => {
    setCart(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId),
    }));
  }, []);

  const clearCart = useCallback(() => {
    setCart({
      items: [],
      subtotal: 0,
      tax: 0,
      deliveryFee: 0,
      total: 0,
    });
  }, []);

  const setDeliveryFee = useCallback((fee: number) => {
    setCart(prev => ({
      ...prev,
      deliveryFee: fee,
    }));
  }, []);

  const setTip = useCallback((tip: number) => {
    setCart(prev => ({
      ...prev,
      tip,
    }));
  }, []);

  const toggleCart = useCallback(() => {
    setIsCartOpen(prev => !prev);
  }, []);

  return (
    <CartContext.Provider
      value={{
        cart,
        isCartOpen,
        setIsCartOpen,
        toggleCart,
        addItem,
        updateItem,
        removeItem,
        clearCart,
        setDeliveryFee,
        setTip,
        itemCount: cart.items.reduce((sum, item) => sum + item.quantity, 0),
      }}
    >
      {children}
    </CartContext.Provider>
  );
}
