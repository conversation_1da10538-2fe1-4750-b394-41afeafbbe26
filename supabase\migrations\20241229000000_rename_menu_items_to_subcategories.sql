-- Migration: Rename menu_items table to subcategories
-- This migration renames the menu_items table to subcategories and updates all references

-- Step 1: Rename the table
ALTER TABLE menu_items RENAME TO subcategories;

-- Step 2: Update foreign key constraints that reference the old table name
-- First, drop existing foreign key constraints
ALTER TABLE menu_item_ingredients DROP CONSTRAINT IF EXISTS menu_item_ingredients_menu_item_id_fkey;
ALTER TABLE customization_presets DROP CONSTRAINT IF EXISTS customization_presets_menu_item_id_fkey;

-- Recreate foreign key constraints with new table name
ALTER TABLE menu_item_ingredients 
ADD CONSTRAINT menu_item_ingredients_menu_item_id_fkey 
FOREIGN KEY (menu_item_id) REFERENCES subcategories(id) ON DELETE CASCADE;

ALTER TABLE customization_presets 
ADD CONSTRAINT customization_presets_menu_item_id_fkey 
FOREIGN KEY (menu_item_id) REFERENCES subcategories(id) ON DELETE CASCADE;

-- Step 3: Update any other tables that might reference menu_items
-- Check for any pricing strategies table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'menu_pricing_strategies') THEN
        ALTER TABLE menu_pricing_strategies DROP CONSTRAINT IF EXISTS menu_pricing_strategies_menu_item_id_fkey;
        ALTER TABLE menu_pricing_strategies 
        ADD CONSTRAINT menu_pricing_strategies_menu_item_id_fkey 
        FOREIGN KEY (menu_item_id) REFERENCES subcategories(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Step 4: Update indexes (they should be automatically renamed, but let's be explicit)
-- Drop old indexes if they exist with old names
DROP INDEX IF EXISTS idx_menu_items_category;
DROP INDEX IF EXISTS idx_menu_items_available;
DROP INDEX IF EXISTS idx_menu_items_customizable;
DROP INDEX IF EXISTS idx_menu_items_featured;
DROP INDEX IF EXISTS idx_menu_items_name;

-- Create new indexes with updated names
CREATE INDEX IF NOT EXISTS idx_subcategories_category ON subcategories (category_id);
CREATE INDEX IF NOT EXISTS idx_subcategories_available ON subcategories (is_available);
CREATE INDEX IF NOT EXISTS idx_subcategories_customizable ON subcategories (is_customizable);
CREATE INDEX IF NOT EXISTS idx_subcategories_featured ON subcategories (is_featured);
CREATE INDEX IF NOT EXISTS idx_subcategories_name ON subcategories (name);

-- Step 5: Update RLS policies
-- Drop old policies
DROP POLICY IF EXISTS "Public read access for menu items" ON subcategories;
DROP POLICY IF EXISTS "Admin full access to menu items" ON subcategories;
DROP POLICY IF EXISTS "Dev: Full access to menu items" ON subcategories;
DROP POLICY IF EXISTS "Service role full access to menu items" ON subcategories;
DROP POLICY IF EXISTS "Prod: Admin access to menu items" ON subcategories;
DROP POLICY IF EXISTS "Public read menu items" ON subcategories;
DROP POLICY IF EXISTS "Enhanced public read access for menu items" ON subcategories;
DROP POLICY IF EXISTS "Dev: Any authenticated user can manage menu items" ON subcategories;

-- Create new policies with updated names
CREATE POLICY "Public read access for subcategories" ON subcategories 
FOR SELECT USING (is_available = true);

CREATE POLICY "Admin full access to subcategories" ON subcategories 
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.id = auth.uid() 
    AND user_profiles.role IN ('admin', 'super_admin')
  )
);

CREATE POLICY "Dev: Full access to subcategories" ON subcategories 
FOR ALL USING (
  current_setting('app.environment', true) = 'development'
  OR EXISTS (
    SELECT 1 FROM user_profiles 
    WHERE user_profiles.id = auth.uid() 
    AND user_profiles.role IN ('admin', 'super_admin', 'developer')
  )
);

CREATE POLICY "Service role full access to subcategories" ON subcategories 
FOR ALL USING (auth.role() = 'service_role');

-- Step 6: Update triggers
-- Drop old trigger
DROP TRIGGER IF EXISTS menu_items_sync_trigger ON subcategories;

-- Create new trigger with updated name
CREATE TRIGGER subcategories_sync_trigger
AFTER INSERT OR UPDATE OR DELETE ON subcategories
FOR EACH ROW EXECUTE FUNCTION handle_menu_sync();

-- Step 7: Update realtime publication
-- Remove old table from publication
ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS menu_items;
-- Add new table to publication
ALTER PUBLICATION supabase_realtime ADD TABLE subcategories;

-- Step 8: Update any functions that reference the old table name
-- This will be handled in the application layer and other migrations

-- Add a comment to track this change
COMMENT ON TABLE subcategories IS 'Menu items table (renamed from menu_items for consistency)';