// Pickup/Delivery Pricing System Types
// Shared types for pricing calculations across admin-dashboard and pos-system

export interface DeliveryZone {
  id: string;
  name: string;
  deliveryFee: number;
  minimumOrderAmount: number;
  estimatedTimeMin: number;
  estimatedTimeMax: number;
  coordinates: Array<{ lat: number; lng: number }>;
  isActive: boolean;
  priority: number;
  color?: string;
  branchId?: string;
}

export interface PricingBreakdown {
  subtotal: number;
  deliveryFee: number;
  pickupDiscount: number;
  serviceFee: number;
  taxAmount: number;
  totalAmount: number;
  deliveryZone?: DeliveryZone | null;
  estimatedTime?: {
    min: number;
    max: number;
    message: string;
  } | null;
}

export interface PricingConfiguration {
  pickup: {
    enabled: boolean;
    discountPercentage: number;
    estimatedTime: { min: number; max: number };
  };
  delivery: {
    enabled: boolean;
    zones: DeliveryZone[];
    defaultFee: number;
    freeDeliveryThreshold?: number | null;
  };
  general: {
    taxRate: number;
    serviceFeeRate: number;
    currency: string;
  };
}

export interface PricingCalculationRequest {
  orderType: 'takeaway' | 'delivery';
  subtotal: number;
  address?: { lat: number; lng: number } | string;
  branchId?: string;
  customerTier?: 'standard' | 'premium' | 'vip';
}

export interface PricingCalculationResponse {
  success: boolean;
  orderType: 'takeaway' | 'delivery';
  pricing: PricingBreakdown;
  error?: string;
  message?: string;
}

export interface DeliveryValidationRequest {
  address: { lat: number; lng: number } | string;
  branchId?: string;
  orderAmount?: number;
}

export interface DeliveryValidationResponse {
  success: boolean;
  deliveryAvailable: boolean;
  zone?: {
    id: string;
    name: string;
    deliveryFee: number;
    minimumOrderAmount: number;
    estimatedTime: { min: number; max: number };
  };
  validation?: {
    meetsMinimumOrder: boolean;
    orderAmount: number;
    estimatedTotal: number;
    shortfall: number;
  };
  coordinates?: { lat: number; lng: number };
  reason?: string;
  message?: string;
  suggestion?: string;
  alternatives?: {
    pickup: boolean;
    nearestZone?: string | null;
  };
}

export interface OrderPricingHistory {
  id: string;
  orderId: string;
  calculationType: 'initial' | 'update' | 'recalculation';
  orderType: 'takeaway' | 'delivery' | 'dine-in';
  subtotal: number;
  deliveryFee: number;
  pickupDiscount: number;
  serviceFee: number;
  taxAmount: number;
  totalAmount: number;
  deliveryZoneId?: string | null;
  deliveryZoneName?: string | null;
  pricingRulesApplied?: Record<string, any> | null;
  calculationTimestamp: string;
  calculatedBy?: string | null;
}

export interface PricingRule {
  id: string;
  name: string;
  orderType: 'takeaway' | 'delivery' | 'all';
  conditions: {
    timeOfDay?: { start: string; end: string };
    dayOfWeek?: number[];
    minimumOrderAmount?: number;
    customerTier?: string[];
    deliveryZones?: string[];
  };
  pricing: {
    discountPercentage?: number;
    fixedDiscount?: number;
    deliveryFeeOverride?: number;
    freeDeliveryThreshold?: number;
  };
  isActive: boolean;
  priority: number;
  validFrom?: string;
  validUntil?: string;
}

export interface PricingError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Error codes for pricing system
export const PRICING_ERROR_CODES = {
  MINIMUM_ORDER_NOT_MET: 'MINIMUM_ORDER_NOT_MET',
  DELIVERY_UNAVAILABLE: 'DELIVERY_UNAVAILABLE',
  GEOCODING_REQUIRED: 'GEOCODING_REQUIRED',
  INVALID_COORDINATES: 'INVALID_COORDINATES',
  ZONE_NOT_FOUND: 'ZONE_NOT_FOUND',
  PRICING_CONFIG_ERROR: 'PRICING_CONFIG_ERROR',
  CALCULATION_ERROR: 'CALCULATION_ERROR'
} as const;

export type PricingErrorCode = keyof typeof PRICING_ERROR_CODES;

// Settings keys for pricing configuration
export const PRICING_SETTINGS_KEYS = {
  PICKUP_DISCOUNT_PERCENTAGE: 'pickup_discount_percentage',
  PICKUP_ENABLED: 'pickup_enabled',
  PICKUP_TIME_MIN: 'pickup_time_min',
  PICKUP_TIME_MAX: 'pickup_time_max',
  DELIVERY_ENABLED: 'delivery_enabled',
  DELIVERY_DEFAULT_FEE: 'delivery_default_fee',
  DELIVERY_FREE_THRESHOLD: 'delivery_free_threshold',
  TAX_RATE: 'tax_rate',
  SERVICE_FEE_RATE: 'service_fee_rate',
  CURRENCY: 'currency',
  PRICING_SYSTEM_VERSION: 'pricing_system_version'
} as const;

// Order type constants
export const ORDER_TYPES = {
  TAKEAWAY: 'takeaway',
  DELIVERY: 'delivery',
  DINE_IN: 'dine-in'
} as const;

export type OrderType = typeof ORDER_TYPES[keyof typeof ORDER_TYPES];

// Calculation type constants for pricing history
export const CALCULATION_TYPES = {
  INITIAL: 'initial',
  UPDATE: 'update',
  RECALCULATION: 'recalculation'
} as const;

export type CalculationType = typeof CALCULATION_TYPES[keyof typeof CALCULATION_TYPES];

// Helper type for API responses
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  code?: string;
}

// Type guards
export function isPricingError(error: any): error is PricingError {
  return error && typeof error === 'object' && 'code' in error && 'message' in error;
}

export function isValidOrderType(orderType: string): orderType is OrderType {
  return Object.values(ORDER_TYPES).includes(orderType as OrderType);
}

export function isValidCalculationType(calculationType: string): calculationType is CalculationType {
  return Object.values(CALCULATION_TYPES).includes(calculationType as CalculationType);
}

// Utility functions for pricing calculations
export function formatCurrency(amount: number, currency: string = 'EUR'): string {
  return new Intl.NumberFormat('en-EU', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}

export function roundToCents(amount: number): number {
  return Math.round(amount * 100) / 100;
}

export function calculatePercentage(amount: number, percentage: number): number {
  return roundToCents(amount * percentage);
}

// Validation helpers
export function validateCoordinates(lat: number, lng: number): boolean {
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
}

export function validateOrderAmount(amount: number): boolean {
  return amount > 0 && Number.isFinite(amount);
}

export function validatePricingBreakdown(breakdown: PricingBreakdown): boolean {
  const requiredFields = ['subtotal', 'deliveryFee', 'pickupDiscount', 'serviceFee', 'taxAmount', 'totalAmount'];
  return requiredFields.every(field => 
    field in breakdown && 
    typeof breakdown[field as keyof PricingBreakdown] === 'number' &&
    Number.isFinite(breakdown[field as keyof PricingBreakdown])
  );
}