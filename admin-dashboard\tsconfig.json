{"compilerOptions": {"lib": ["dom", "dom.iterable", "es6"], "types": ["jest", "node", "@testing-library/jest-dom"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/hooks/*": ["./src/hooks/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/styles/*": ["./src/styles/*"], "@/config/*": ["./src/config/*"], "@/shared/*": ["../shared/src/*"]}, "target": "es2020", "forceConsistentCasingInFileNames": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "declaration": false, "declarationMap": false, "sourceMap": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/@types/**/*.d.ts", "src/@types/global.d.ts"], "exclude": ["node_modules", ".next", "out", "dist", "build", "cypress", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "src/**/__tests__/**/*", "tests/**/*"]}