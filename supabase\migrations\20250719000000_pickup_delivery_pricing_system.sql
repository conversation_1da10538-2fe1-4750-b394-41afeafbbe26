-- Pickup/Delivery Pricing System Migration
-- Adds pricing configuration and order breakdown support

-- =============================================
-- RESTAURANT SETTINGS ENHANCEMENTS
-- =============================================

-- Add pickup pricing settings to restaurant_settings if they don't exist
INSERT INTO restaurant_settings (setting_key, setting_value, description) VALUES
  ('pickup_discount_percentage', '0.05', 'Discount percentage for pickup orders (e.g., 0.05 = 5%)')
ON CONFLICT (setting_key) DO NOTHING;

INSERT INTO restaurant_settings (setting_key, setting_value, description) VALUES
  ('pickup_enabled', 'true', 'Enable/disable pickup orders')
ON CONFLICT (setting_key) DO NOTHING;

INSERT INTO restaurant_settings (setting_key, setting_value, description) VALUES
  ('pickup_time_min', '10', 'Minimum pickup time in minutes')
ON CONFLICT (setting_key) DO NOTHING;

INSERT INTO restaurant_settings (setting_key, setting_value, description) VALUES
  ('pickup_time_max', '20', 'Maximum pickup time in minutes')
ON CONFLICT (setting_key) DO NOTHING;

INSERT INTO restaurant_settings (setting_key, setting_value, description) VALUES
  ('delivery_enabled', 'true', 'Enable/disable delivery orders')
ON CONFLICT (setting_key) DO NOTHING;

INSERT INTO restaurant_settings (setting_key, setting_value, description) VALUES
  ('delivery_default_fee', '2.50', 'Default delivery fee when no zone matches')
ON CONFLICT (setting_key) DO NOTHING;

INSERT INTO restaurant_settings (setting_key, setting_value, description) VALUES
  ('delivery_free_threshold', '50.00', 'Order amount for free delivery (null = disabled)')
ON CONFLICT (setting_key) DO NOTHING;

INSERT INTO restaurant_settings (setting_key, setting_value, description) VALUES
  ('tax_rate', '0.10', 'Tax rate percentage (e.g., 0.10 = 10%)')
ON CONFLICT (setting_key) DO NOTHING;

INSERT INTO restaurant_settings (setting_key, setting_value, description) VALUES
  ('service_fee_rate', '0.00', 'Service fee rate percentage')
ON CONFLICT (setting_key) DO NOTHING;

INSERT INTO restaurant_settings (setting_key, setting_value, description) VALUES
  ('currency', 'EUR', 'Currency code')
ON CONFLICT (setting_key) DO NOTHING;

-- =============================================
-- ORDERS TABLE ENHANCEMENTS
-- =============================================

-- Add pricing breakdown fields to orders table
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_zone_id UUID REFERENCES delivery_zones(id);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS pickup_discount DECIMAL(10,2) DEFAULT 0.00;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_fee DECIMAL(10,2) DEFAULT 0.00;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS service_fee DECIMAL(10,2) DEFAULT 0.00;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS tax_amount DECIMAL(10,2) DEFAULT 0.00;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS subtotal DECIMAL(10,2) DEFAULT 0.00;

-- Add pricing calculation metadata
ALTER TABLE orders ADD COLUMN IF NOT EXISTS pricing_calculated_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE orders ADD COLUMN IF NOT EXISTS pricing_version VARCHAR(20) DEFAULT '1.0';

-- Create index for delivery zone lookup
CREATE INDEX IF NOT EXISTS idx_orders_delivery_zone ON orders (delivery_zone_id);
CREATE INDEX IF NOT EXISTS idx_orders_pricing_calculated ON orders (pricing_calculated_at);

-- =============================================
-- ORDER HISTORY TABLE ENHANCEMENTS  
-- =============================================

-- Create order_pricing_history table for audit trail
CREATE TABLE IF NOT EXISTS order_pricing_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  calculation_type VARCHAR(20) NOT NULL, -- 'initial', 'update', 'recalculation'
  order_type VARCHAR(20) NOT NULL, -- 'pickup', 'delivery', 'dine-in'
  subtotal DECIMAL(10,2) NOT NULL,
  delivery_fee DECIMAL(10,2) DEFAULT 0.00,
  pickup_discount DECIMAL(10,2) DEFAULT 0.00,
  service_fee DECIMAL(10,2) DEFAULT 0.00,
  tax_amount DECIMAL(10,2) DEFAULT 0.00,
  total_amount DECIMAL(10,2) NOT NULL,
  delivery_zone_id UUID REFERENCES delivery_zones(id),
  delivery_zone_name VARCHAR(100),
  pricing_rules_applied JSONB, -- Store which rules were applied
  calculation_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  calculated_by UUID -- Reference to auth.users
);

-- Create indexes for order pricing history
CREATE INDEX IF NOT EXISTS idx_order_pricing_history_order ON order_pricing_history (order_id);
CREATE INDEX IF NOT EXISTS idx_order_pricing_history_timestamp ON order_pricing_history (calculation_timestamp);
CREATE INDEX IF NOT EXISTS idx_order_pricing_history_type ON order_pricing_history (calculation_type);

-- =============================================
-- PRICING CALCULATION FUNCTIONS
-- =============================================

-- Function to calculate order pricing
CREATE OR REPLACE FUNCTION calculate_order_pricing(
  p_order_type VARCHAR(20),
  p_subtotal DECIMAL(10,2),
  p_delivery_address_lat DECIMAL DEFAULT NULL,
  p_delivery_address_lng DECIMAL DEFAULT NULL,
  p_branch_id UUID DEFAULT NULL
) RETURNS TABLE(
  delivery_fee DECIMAL(10,2),
  pickup_discount DECIMAL(10,2),
  service_fee DECIMAL(10,2),
  tax_amount DECIMAL(10,2),
  total_amount DECIMAL(10,2),
  delivery_zone_id UUID,
  delivery_zone_name VARCHAR(100),
  estimated_time_min INTEGER,
  estimated_time_max INTEGER,
  calculation_details JSONB
) AS $$
DECLARE
  v_delivery_fee DECIMAL(10,2) := 0.00;
  v_pickup_discount DECIMAL(10,2) := 0.00;
  v_service_fee DECIMAL(10,2) := 0.00;
  v_tax_amount DECIMAL(10,2) := 0.00;
  v_total_amount DECIMAL(10,2) := 0.00;
  v_delivery_zone_id UUID := NULL;
  v_delivery_zone_name VARCHAR(100) := NULL;
  v_estimated_time_min INTEGER := 0;
  v_estimated_time_max INTEGER := 0;
  v_calculation_details JSONB := '{}';
  
  v_pickup_discount_rate DECIMAL(10,4) := 0.0500; -- Default 5%
  v_tax_rate DECIMAL(10,4) := 0.1000; -- Default 10%
  v_service_fee_rate DECIMAL(10,4) := 0.0000; -- Default 0%
  v_delivery_default_fee DECIMAL(10,2) := 2.50;
  
  zone_record RECORD;
BEGIN
  -- Get pricing configuration from settings
  SELECT 
    COALESCE((SELECT setting_value::DECIMAL FROM restaurant_settings WHERE setting_key = 'pickup_discount_percentage'), 0.05) as pickup_rate,
    COALESCE((SELECT setting_value::DECIMAL FROM restaurant_settings WHERE setting_key = 'tax_rate'), 0.10) as tax_rate,
    COALESCE((SELECT setting_value::DECIMAL FROM restaurant_settings WHERE setting_key = 'service_fee_rate'), 0.00) as service_rate,
    COALESCE((SELECT setting_value::DECIMAL FROM restaurant_settings WHERE setting_key = 'delivery_default_fee'), 2.50) as default_fee
  INTO v_pickup_discount_rate, v_tax_rate, v_service_fee_rate, v_delivery_default_fee;
  
  -- Calculate service fee
  v_service_fee := p_subtotal * v_service_fee_rate;
  
  IF p_order_type = 'pickup' THEN
    -- Calculate pickup discount
    v_pickup_discount := p_subtotal * v_pickup_discount_rate;
    v_estimated_time_min := 10;
    v_estimated_time_max := 20;
    
    v_calculation_details := jsonb_build_object(
      'order_type', 'pickup',
      'discount_rate', v_pickup_discount_rate,
      'discount_applied', v_pickup_discount
    );
    
  ELSIF p_order_type = 'delivery' THEN
    -- Find delivery zone if coordinates provided
    IF p_delivery_address_lat IS NOT NULL AND p_delivery_address_lng IS NOT NULL THEN
      SELECT * INTO zone_record
      FROM find_delivery_zone_for_address(
        p_delivery_address_lat, 
        p_delivery_address_lng, 
        p_branch_id
      ) LIMIT 1;
      
      IF FOUND THEN
        v_delivery_fee := zone_record.delivery_fee;
        v_delivery_zone_id := zone_record.zone_id;
        v_delivery_zone_name := zone_record.zone_name;
        v_estimated_time_min := zone_record.estimated_delivery_time_min;
        v_estimated_time_max := zone_record.estimated_delivery_time_max;
      ELSE
        -- Use default delivery fee if no zone found
        v_delivery_fee := v_delivery_default_fee;
        v_delivery_zone_name := 'Default Zone';
        v_estimated_time_min := 30;
        v_estimated_time_max := 60;
      END IF;
    ELSE
      -- Use default delivery fee if no coordinates
      v_delivery_fee := v_delivery_default_fee;
      v_delivery_zone_name := 'Default Zone';
      v_estimated_time_min := 30;
      v_estimated_time_max := 60;
    END IF;
    
    v_calculation_details := jsonb_build_object(
      'order_type', 'delivery',
      'zone_found', zone_record IS NOT NULL,
      'zone_name', v_delivery_zone_name,
      'delivery_fee', v_delivery_fee
    );
  END IF;
  
  -- Calculate tax on subtotal + service fee + delivery fee - discount
  v_tax_amount := (p_subtotal + v_service_fee + v_delivery_fee - v_pickup_discount) * v_tax_rate;
  
  -- Calculate total
  v_total_amount := p_subtotal + v_service_fee + v_delivery_fee + v_tax_amount - v_pickup_discount;
  
  -- Round to 2 decimal places
  v_delivery_fee := ROUND(v_delivery_fee, 2);
  v_pickup_discount := ROUND(v_pickup_discount, 2);
  v_service_fee := ROUND(v_service_fee, 2);
  v_tax_amount := ROUND(v_tax_amount, 2);
  v_total_amount := ROUND(v_total_amount, 2);
  
  RETURN QUERY SELECT 
    v_delivery_fee,
    v_pickup_discount,
    v_service_fee,
    v_tax_amount,
    v_total_amount,
    v_delivery_zone_id,
    v_delivery_zone_name,
    v_estimated_time_min,
    v_estimated_time_max,
    v_calculation_details;
END;
$$ LANGUAGE plpgsql STABLE;

-- =============================================
-- TRIGGERS FOR PRICING AUDIT
-- =============================================

-- Function to log pricing calculations
CREATE OR REPLACE FUNCTION log_order_pricing_calculation()
RETURNS TRIGGER AS $$
BEGIN
  -- Only log if pricing fields have changed
  IF (TG_OP = 'INSERT') OR 
     (TG_OP = 'UPDATE' AND (
       OLD.subtotal IS DISTINCT FROM NEW.subtotal OR
       OLD.delivery_fee IS DISTINCT FROM NEW.delivery_fee OR
       OLD.pickup_discount IS DISTINCT FROM NEW.pickup_discount OR
       OLD.tax_amount IS DISTINCT FROM NEW.tax_amount OR
       OLD.total_amount IS DISTINCT FROM NEW.total_amount
     )) THEN
    
    INSERT INTO order_pricing_history (
      order_id,
      calculation_type,
      order_type,
      subtotal,
      delivery_fee,
      pickup_discount,
      service_fee,
      tax_amount,
      total_amount,
      delivery_zone_id,
      delivery_zone_name,
      pricing_rules_applied,
      calculated_by
    ) VALUES (
      NEW.id,
      CASE WHEN TG_OP = 'INSERT' THEN 'initial' ELSE 'update' END,
      NEW.order_type,
      COALESCE(NEW.subtotal, 0),
      COALESCE(NEW.delivery_fee, 0),
      COALESCE(NEW.pickup_discount, 0),
      COALESCE(NEW.service_fee, 0),
      COALESCE(NEW.tax_amount, 0),
      NEW.total_amount,
      NEW.delivery_zone_id,
      (SELECT name FROM delivery_zones WHERE id = NEW.delivery_zone_id),
      jsonb_build_object('trigger_event', TG_OP, 'timestamp', NOW()),
      auth.uid()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for order pricing audit
DROP TRIGGER IF EXISTS trigger_log_order_pricing ON orders;
CREATE TRIGGER trigger_log_order_pricing
  AFTER INSERT OR UPDATE ON orders
  FOR EACH ROW
  EXECUTE FUNCTION log_order_pricing_calculation();

-- =============================================
-- ROW LEVEL SECURITY FOR NEW TABLES
-- =============================================

-- Enable RLS on order_pricing_history
ALTER TABLE order_pricing_history ENABLE ROW LEVEL SECURITY;

-- Policy for viewing order pricing history
CREATE POLICY "Users can view order pricing history" ON order_pricing_history
  FOR SELECT
  USING (auth.role() = 'authenticated');

-- Policy for admin users to manage pricing history
CREATE POLICY "Admin users can manage pricing history" ON order_pricing_history
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' IN ('admin', 'manager')
    )
  );

-- =============================================
-- REALTIME SUBSCRIPTIONS
-- =============================================

-- Add new tables to realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE order_pricing_history;

-- Update existing orders table subscription to include new pricing fields
-- (orders should already be in realtime publication)

-- =============================================
-- SAMPLE PRICING CONFIGURATIONS
-- =============================================

-- Create some sample pricing scenarios for testing
COMMENT ON TABLE order_pricing_history IS 'Audit trail for order pricing calculations and changes';
COMMENT ON FUNCTION calculate_order_pricing IS 'Calculate complete order pricing based on order type and delivery location';
COMMENT ON COLUMN orders.delivery_zone_id IS 'Reference to the delivery zone used for this order';
COMMENT ON COLUMN orders.pickup_discount IS 'Discount amount applied for pickup orders';
COMMENT ON COLUMN orders.delivery_fee IS 'Delivery fee charged for this order';
COMMENT ON COLUMN orders.service_fee IS 'Service fee applied to this order';
COMMENT ON COLUMN orders.tax_amount IS 'Tax amount calculated for this order';
COMMENT ON COLUMN orders.subtotal IS 'Order subtotal before fees, taxes, and discounts';

-- =============================================
-- MIGRATION COMPLETION
-- =============================================

INSERT INTO restaurant_settings (setting_key, setting_value, description) VALUES
  ('pricing_system_version', '1.0', 'Version of the pickup/delivery pricing system')
ON CONFLICT (setting_key) DO UPDATE SET
  setting_value = EXCLUDED.setting_value,
  updated_at = NOW();