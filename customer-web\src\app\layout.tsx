import type { <PERSON>ada<PERSON>, Viewport } from 'next';
import { Inter } from 'next/font/google';
import '@/styles/globals.css';
import { Providers, AuthProvider, CartProvider } from '@/providers';
import { I18nProvider } from '@/components/i18n-provider/i18n-provider';
import { MainNavigation } from '@/components/navigation/main-navigation';
import { CartDrawer } from '@/components/cart/cart-drawer';
import { NotificationProvider } from '@/components/ui/notification-system';
import { FavoritesProvider } from '@/components/favorites/favorites-provider';
import { ThemeProvider } from '@/providers/theme-provider';
import { OrderTypeProvider } from '@/contexts/OrderTypeContext';
import { generateMetadata as generateSEOMetadata, generateStructuredData } from '@/lib/seo';
// import { StagewiseToolbar } from '@stagewise/toolbar-next';
// import { ReactPlugin } from '@stagewise-plugins/react';

const inter = Inter({ subsets: ['latin', 'greek'] });

export const metadata: Metadata = {
  ...generateSEOMetadata(),
  authors: [{ name: 'Creperie Team' }],
  creator: '<PERSON><PERSON><PERSON><PERSON>',
  publisher: '<PERSON><PERSON><PERSON><PERSON>',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'),
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_VERIFICATION_CODE,
    yandex: process.env.YANDEX_VERIFICATION_CODE,
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#121212' },
  ],

  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const restaurantData = generateStructuredData('restaurant', {
    phone: '******-CREPE-1',
    address: {
      street: '123 French Quarter St',
      city: 'New Orleans',
      state: 'LA',
      zip: '70116'
    },
    coordinates: {
      lat: 29.9511,
      lng: -90.0715
    },
    hours: [
      'Mo-Th 08:00-21:00',
      'Fr-Sa 08:00-22:00',
      'Su 09:00-20:00'
    ]
  });

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#fbbf24" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Creperie" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#fbbf24" />
        <meta name="msapplication-tap-highlight" content="no" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(restaurantData),
          }}
        />
      </head>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <Providers>
            <I18nProvider>
              <AuthProvider>
                <CartProvider>
                  <OrderTypeProvider>
                    <NotificationProvider>
                      <FavoritesProvider>
                        {/* <StagewiseToolbar config={{ plugins: [ReactPlugin] }} /> */}
                        <MainNavigation />
                        <main className="pb-20 md:pb-0 md:pt-16">
                          {children}
                        </main>
                        <CartDrawer />
                      </FavoritesProvider>
                    </NotificationProvider>
                  </OrderTypeProvider>
                </CartProvider>
              </AuthProvider>
            </I18nProvider>
          </Providers>
        </ThemeProvider>
      </body>
    </html>
  );
}
