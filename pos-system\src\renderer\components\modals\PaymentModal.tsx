import React, { useState } from 'react';
import { X, CreditCard, Banknote } from 'lucide-react';
import { useTheme } from '../../contexts/theme-context';
import toast from 'react-hot-toast';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderTotal: number;
  isProcessing?: boolean;
  onPaymentComplete: (paymentData: {
    method: 'cash' | 'card';
    amount: number;
    transactionId?: string;
  }) => void;
}

export const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  orderTotal,
  isProcessing = false,
  onPaymentComplete
}) => {
  const { resolvedTheme } = useTheme();
  const [selectedMethod, setSelectedMethod] = useState<'cash' | 'card' | null>(null);
  const [cashReceived, setCashReceived] = useState<string>('');
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  if (!isOpen) return null;

  const handleCashPayment = async () => {
    const received = parseFloat(cashReceived);
    if (isNaN(received) || received < orderTotal) {
      toast.error('Insufficient cash amount');
      return;
    }

    setIsProcessingPayment(true);
    try {
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      onPaymentComplete({
        method: 'cash',
        amount: orderTotal,
        transactionId: `CASH-${Date.now()}`
      });
      
      toast.success(`Payment successful! Change: €${(received - orderTotal).toFixed(2)}`);
      onClose();
    } catch (error) {
      toast.error('Payment processing failed');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const handleCardPayment = async () => {
    setIsProcessingPayment(true);
    try {
      // Simulate card processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      onPaymentComplete({
        method: 'card',
        amount: orderTotal,
        transactionId: `CARD-${Date.now()}`
      });
      
      toast.success('Card payment successful!');
      onClose();
    } catch (error) {
      toast.error('Card payment failed');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const resetModal = () => {
    setSelectedMethod(null);
    setCashReceived('');
    setIsProcessingPayment(false);
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className={`w-full max-w-md rounded-3xl shadow-2xl border transform transition-all duration-300 ${
        resolvedTheme === 'dark'
          ? 'bg-gray-800/90 border-gray-700/50 backdrop-blur-xl'
          : 'bg-white/90 border-gray-200/50 backdrop-blur-xl'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200/20">
          <h2 className={`text-xl font-bold ${
            resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            Payment
          </h2>
          <button
            type="button"
            onClick={handleClose}
            title="Close payment"
            aria-label="Close payment"
            className={`p-2 rounded-xl transition-all duration-200 ${
              resolvedTheme === 'dark'
                ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700/50'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100/50'
            }`}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Order Total */}
          <div className="text-center mb-6">
            <p className={`text-sm ${
              resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Total Amount
            </p>
            <p className={`text-3xl font-bold ${
              resolvedTheme === 'dark' ? 'text-emerald-400' : 'text-emerald-600'
            }`}>
              €{orderTotal.toFixed(2)}
            </p>
          </div>

          {!selectedMethod ? (
            /* Payment Method Selection */
            <div className="space-y-4">
              <button
                onClick={() => setSelectedMethod('cash')}
                className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left group ${
                  resolvedTheme === 'light'
                    ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-200 hover:border-green-400 hover:shadow-lg'
                    : 'bg-gradient-to-r from-green-900/30 to-green-800/30 border-green-400/30 hover:border-green-400/60 hover:shadow-lg hover:shadow-green-400/20'
                }`}
              >
                <div className="flex items-center gap-4">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                    resolvedTheme === 'light'
                      ? 'bg-green-500 text-white'
                      : 'bg-green-600 text-green-100'
                  }`}>
                    <Banknote className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className={`font-semibold ${
                      resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
                    }`}>
                      Cash Payment
                    </h3>
                    <p className={`text-sm ${
                      resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Pay with cash
                    </p>
                  </div>
                </div>
              </button>

              <button
                onClick={() => setSelectedMethod('card')}
                className={`w-full p-4 rounded-xl border-2 transition-all duration-200 text-left group ${
                  resolvedTheme === 'light'
                    ? 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 hover:border-blue-400 hover:shadow-lg'
                    : 'bg-gradient-to-r from-blue-900/30 to-blue-800/30 border-blue-400/30 hover:border-blue-400/60 hover:shadow-lg hover:shadow-blue-400/20'
                }`}
              >
                <div className="flex items-center gap-4">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                    resolvedTheme === 'light'
                      ? 'bg-blue-500 text-white'
                      : 'bg-blue-600 text-blue-100'
                  }`}>
                    <CreditCard className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className={`font-semibold ${
                      resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
                    }`}>
                      Card Payment
                    </h3>
                    <p className={`text-sm ${
                      resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Pay with credit/debit card
                    </p>
                  </div>
                </div>
              </button>
            </div>
          ) : selectedMethod === 'cash' ? (
            /* Cash Payment Form */
            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Cash Received
                </label>
                <input
                  type="number"
                  step="0.01"
                  min={orderTotal}
                  value={cashReceived}
                  onChange={(e) => setCashReceived(e.target.value)}
                  placeholder={`€${orderTotal.toFixed(2)}`}
                  className={`w-full px-4 py-3 rounded-xl border transition-all duration-200 ${
                    resolvedTheme === 'dark'
                      ? 'bg-gray-700/50 border-gray-600 text-white placeholder-gray-400 focus:border-green-400'
                      : 'bg-white/50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-green-500'
                  } focus:outline-none focus:ring-2 focus:ring-green-500/20`}
                />
                {cashReceived && parseFloat(cashReceived) >= orderTotal && (
                  <p className={`text-sm mt-2 ${
                    resolvedTheme === 'dark' ? 'text-green-400' : 'text-green-600'
                  }`}>
                    Change: €{(parseFloat(cashReceived) - orderTotal).toFixed(2)}
                  </p>
                )}
              </div>
              
              <div className="flex gap-3">
                <button
                  onClick={() => setSelectedMethod(null)}
                  className={`flex-1 py-3 rounded-xl font-medium transition-all duration-200 ${
                    resolvedTheme === 'dark'
                      ? 'bg-gray-700/50 text-gray-300 hover:bg-gray-700'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  Back
                </button>
                <button
                  onClick={handleCashPayment}
                  disabled={isProcessingPayment || !cashReceived || parseFloat(cashReceived) < orderTotal}
                  className={`flex-1 py-3 rounded-xl font-medium transition-all duration-200 ${
                    isProcessingPayment || !cashReceived || parseFloat(cashReceived) < orderTotal
                      ? 'bg-gray-400/50 text-gray-500 cursor-not-allowed'
                      : 'bg-green-600 text-white hover:bg-green-700'
                  }`}
                >
                  {isProcessingPayment ? 'Processing...' : 'Complete Payment'}
                </button>
              </div>
            </div>
          ) : (
            /* Card Payment Processing */
            <div className="text-center space-y-4">
              <div className="animate-pulse">
                <CreditCard className={`w-16 h-16 mx-auto mb-4 ${
                  resolvedTheme === 'dark' ? 'text-blue-400' : 'text-blue-600'
                }`} />
              </div>
              <p className={`${
                resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'
              }`}>
                {isProcessingPayment ? 'Processing card payment...' : 'Insert or tap your card'}
              </p>
              
              <div className="flex gap-3">
                <button
                  onClick={() => setSelectedMethod(null)}
                  disabled={isProcessingPayment}
                  className={`flex-1 py-3 rounded-xl font-medium transition-all duration-200 ${
                    isProcessingPayment
                      ? 'bg-gray-400/50 text-gray-500 cursor-not-allowed'
                      : resolvedTheme === 'dark'
                        ? 'bg-gray-700/50 text-gray-300 hover:bg-gray-700'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  Cancel
                </button>
                <button
                  onClick={handleCardPayment}
                  disabled={isProcessingPayment}
                  className={`flex-1 py-3 rounded-xl font-medium transition-all duration-200 ${
                    isProcessingPayment
                      ? 'bg-gray-400/50 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isProcessingPayment ? 'Processing...' : 'Process Payment'}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
