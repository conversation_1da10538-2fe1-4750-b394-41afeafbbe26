// Unified customer type definitions for the POS system
// This file consolidates all customer-related interfaces

export interface CustomerAddress {
  id: string;
  street: string;
  city: string;
  postal_code: string;
  country?: string;
  is_default: boolean;
  delivery_notes?: string;
}

export interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  addresses?: CustomerAddress[];
  created_at?: string;
  updated_at?: string;
  total_orders?: number;
  last_order_date?: string;
}

export interface CustomerInfo {
  name: string;
  phone: string;
  email?: string;
  address?: {
    street: string;
    city: string;
    postalCode: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  notes?: string;
}

export interface CustomerLookupResult {
  found: boolean;
  customer?: Customer;
  isNew?: boolean;
}

export interface CustomerSearchHistory {
  phone: string;
  timestamp: string;
  found: boolean;
}

// Re-export commonly used customer types
export type { Customer as CustomerType };
export type { CustomerInfo as CustomerInfoType };
export type { CustomerAddress as AddressType };