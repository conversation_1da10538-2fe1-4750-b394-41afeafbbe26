'use client'

import React, { useState, useEffect, useCallback } from 'react'
import dynamic from 'next/dynamic'
import { useTheme } from '@/contexts/theme-context'

// Dynamically import the enhanced map component to avoid SSR issues with Google Maps
const DeliveryZoneMapEnhanced = dynamic(
  () =>
    import('@/components/delivery-zones/DeliveryZoneMapEnhanced').then(mod => ({
      default: mod.DeliveryZoneMapEnhanced,
    })),
  {
    ssr: false,
    loading: () => {
      // Note: We can't use useTheme hook here, so we'll use a simple loading state
      return (
        <div className="flex items-center justify-center h-96 bg-white/10 backdrop-blur-md border border-white/20 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white/50 mx-auto mb-2"></div>
            <p className="text-white/70">Loading enhanced map...</p>
          </div>
        </div>
      )
    },
  }
)
import { GlassCard, GlassButton, GlassInput } from '@/components/ui/glass-components'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Plus,
  Edit3,
  Trash2,
  Save,
  X,
  MapPin,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
} from 'lucide-react'
import {
  DeliveryZone,
  PolygonCoordinate,
  CreateDeliveryZoneRequest,
  UpdateDeliveryZoneRequest,
} from '@/types/delivery-zones'

// Mock translations - in a real app, this would come from your i18n system
const translations = {
  en: {
    title: 'Delivery Zone Management',
    subtitle: 'Manage delivery zones and their settings',
    map: {
      title: 'Delivery Zone Map',
      subtitle: 'Draw and edit delivery zones on the map',
      drawPolygon: 'Draw Zone',
      editPolygon: 'Edit Zone',
      deletePolygon: 'Delete Zone',
      saveZone: 'Save Zone',
      cancelEdit: 'Cancel',
      clearMap: 'Clear Map',
      zoomIn: 'Zoom In',
      zoomOut: 'Zoom Out',
      resetView: 'Reset View',
      searchAddress: 'Search address...',
      loading: 'Loading map...',
      error: 'Failed to load Google Maps',
      noZones: 'No delivery zones defined',
      retry: 'Retry',
      instructions: {
        draw: 'Click on the map to draw a polygon. Click the first point again to complete.',
        edit: 'Drag the points to modify the zone boundaries.',
        complete: 'Click Save to confirm changes or Cancel to discard.',
      },
      validation: {
        minimumPoints: 'A delivery zone must have at least 3 points',
        invalidPolygon: 'Invalid polygon shape',
        overlappingZone: 'This zone overlaps with an existing zone',
      },
    },
    form: {
      title: 'Zone Details',
      name: 'Zone Name',
      namePlaceholder: 'Enter zone name',
      deliveryFee: 'Delivery Fee ($)',
      deliveryFeePlaceholder: '0.00',
      minimumOrder: 'Minimum Order ($)',
      minimumOrderPlaceholder: '0.00',
      estimatedTime: 'Estimated Delivery Time (minutes)',
      estimatedTimePlaceholder: '30',
      isActive: 'Active',
      save: 'Save Zone',
      cancel: 'Cancel',
      creating: 'Creating...',
      updating: 'Updating...',
    },
    table: {
      title: 'Existing Zones',
      name: 'Name',
      fee: 'Delivery Fee',
      minOrder: 'Min Order',
      time: 'Est. Time',
      status: 'Status',
      actions: 'Actions',
      active: 'Active',
      inactive: 'Inactive',
      edit: 'Edit',
      delete: 'Delete',
      noZones: 'No delivery zones configured',
    },
    messages: {
      success: {
        created: 'Delivery zone created successfully',
        updated: 'Delivery zone updated successfully',
        deleted: 'Delivery zone deleted successfully',
      },
      error: {
        create: 'Failed to create delivery zone',
        update: 'Failed to update delivery zone',
        delete: 'Failed to delete delivery zone',
        load: 'Failed to load delivery zones',
        validation: 'Please fill in all required fields',
        polygon: 'Please draw a zone on the map first',
      },
    },
  },
}

interface ZoneFormData {
  name: string
  delivery_fee: number
  minimum_order_amount: number
  estimated_delivery_time: number
  is_active: boolean
}

const initialFormData: ZoneFormData = {
  name: '',
  delivery_fee: 0,
  minimum_order_amount: 0,
  estimated_delivery_time: 30,
  is_active: true,
}

export default function DeliveryZonesPage() {
  // Theme context
  const { isDarkTheme } = useTheme()
  
  // State management
  const [zones, setZones] = useState<DeliveryZone[]>([])
  const [_isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [editingZone, setEditingZone] = useState<DeliveryZone | null>(null)
  const [formData, setFormData] = useState<ZoneFormData>(initialFormData)
  const [pendingCoordinates, setPendingCoordinates] = useState<PolygonCoordinate[] | null>(null)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  const t = translations.en // In a real app, this would be dynamic based on locale

  // Load zones on component mount
  useEffect(() => {
    loadZones()
  }, [])

  // Auto-hide messages after 5 seconds
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000)
      return () => clearTimeout(timer)
    }
    return undefined
  }, [message])

  // Load delivery zones from API
  const loadZones = useCallback(async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/delivery-zones')
      if (response.ok) {
        const data = await response.json()
        // Handle the API response format which includes a 'zones' property
        setZones(data.zones || data || [])
      } else {
        throw new Error('Failed to fetch zones')
      }
    } catch (error) {
      console.error('Error loading zones:', error)
      setMessage({ type: 'error', text: t.messages.error.load })
    } finally {
      setIsLoading(false)
    }
  }, [t.messages.error.load])

  // Handle form input changes
  const handleInputChange = useCallback(
    (field: keyof ZoneFormData, value: string | number | boolean) => {
      setFormData(prev => ({ ...prev, [field]: value }))
    },
    []
  )

  // Handle zone creation from map
  const handleZoneCreate = useCallback((coordinates: PolygonCoordinate[]) => {
    setPendingCoordinates(coordinates)
    setShowForm(true)
    setEditingZone(null)
    setFormData(initialFormData)
  }, [])

  // Handle zone update from map
  const handleZoneUpdate = useCallback(
    async (zoneId: string, coordinates: PolygonCoordinate[]) => {
      try {
        setIsSubmitting(true)
        const updateData: UpdateDeliveryZoneRequest = {
          id: zoneId,
          polygon_coordinates: coordinates,
        }

        const response = await fetch(`/api/delivery-zones/${zoneId}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updateData),
        })

        if (response.ok) {
          await loadZones()
          setMessage({ type: 'success', text: t.messages.success.updated })
        } else {
          throw new Error('Update failed')
        }
      } catch (error) {
        console.error('Error updating zone:', error)
        setMessage({ type: 'error', text: t.messages.error.update })
      } finally {
        setIsSubmitting(false)
      }
    },
    [loadZones, t.messages.success.updated, t.messages.error.update]
  )

  // Handle zone deletion
  const handleZoneDelete = useCallback(
    async (zoneId: string) => {
      if (!confirm('Are you sure you want to delete this delivery zone?')) {
        return
      }

      try {
        const response = await fetch(`/api/delivery-zones/${zoneId}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          await loadZones()
          setMessage({ type: 'success', text: t.messages.success.deleted })
        } else {
          throw new Error('Delete failed')
        }
      } catch (error) {
        console.error('Error deleting zone:', error)
        setMessage({ type: 'error', text: t.messages.error.delete })
      }
    },
    [loadZones, t.messages.success.deleted, t.messages.error.delete]
  )

  // Handle form submission
  const handleFormSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()

      // Validation
      if (!formData.name.trim()) {
        setMessage({ type: 'error', text: t.messages.error.validation })
        return
      }

      if (!editingZone && !pendingCoordinates) {
        setMessage({ type: 'error', text: t.messages.error.polygon })
        return
      }

      try {
        setIsSubmitting(true)

        if (editingZone) {
          // Update existing zone
          const updateData: UpdateDeliveryZoneRequest = {
            id: editingZone.id,
            name: formData.name,
            delivery_fee: formData.delivery_fee,
            minimum_order_amount: formData.minimum_order_amount,
            estimated_delivery_time_min: formData.estimated_delivery_time,
            estimated_delivery_time_max: formData.estimated_delivery_time,
            is_active: formData.is_active,
          }

          const response = await fetch(`/api/delivery-zones/${editingZone.id}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(updateData),
          })

          if (!response.ok) throw new Error('Update failed')
          setMessage({ type: 'success', text: t.messages.success.updated })
        } else {
          // Create new zone
          const createData: CreateDeliveryZoneRequest = {
            name: formData.name,
            polygon_coordinates: pendingCoordinates!,
            delivery_fee: formData.delivery_fee,
            minimum_order_amount: formData.minimum_order_amount,
            estimated_delivery_time_min: formData.estimated_delivery_time,
            estimated_delivery_time_max: formData.estimated_delivery_time,
            is_active: formData.is_active,
            delivery_days: [
              { day: 'monday', is_available: true },
              { day: 'tuesday', is_available: true },
              { day: 'wednesday', is_available: true },
              { day: 'thursday', is_available: true },
              { day: 'friday', is_available: true },
              { day: 'saturday', is_available: true },
              { day: 'sunday', is_available: true },
            ],

            delivery_hours_start: '09:00',
            delivery_hours_end: '21:00',
            distance_based_pricing: false,
          }

          const response = await fetch('/api/delivery-zones', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(createData),
          })

          if (!response.ok) throw new Error('Create failed')
          setMessage({ type: 'success', text: t.messages.success.created })
        }

        // Reset form and reload zones
        handleFormCancel()
        await loadZones()
      } catch (error) {
        console.error('Error saving zone:', error)
        setMessage({
          type: 'error',
          text: editingZone ? t.messages.error.update : t.messages.error.create,
        })
      } finally {
        setIsSubmitting(false)
      }
    },
    [formData, editingZone, pendingCoordinates, loadZones, t.messages]
  )

  // Handle form cancellation
  const handleFormCancel = useCallback(() => {
    setShowForm(false)
    setEditingZone(null)
    setFormData(initialFormData)
    setPendingCoordinates(null)
  }, [])

  // Handle edit zone from table
  const handleEditZone = useCallback((zone: DeliveryZone) => {
    setEditingZone(zone)
    setFormData({
      name: zone.name,
      delivery_fee: zone.delivery_fee,
      minimum_order_amount: zone.minimum_order_amount,
      estimated_delivery_time: zone.estimated_delivery_time_min,
      is_active: zone.is_active,
    })
    setShowForm(true)
    setPendingCoordinates(null)
  }, [])

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className={`text-3xl font-bold ${isDarkTheme ? 'text-white' : 'text-gray-900'}`}>{t.title}</h1>
          <p className={`mt-1 ${isDarkTheme ? 'text-white/70' : 'text-gray-600'}`}>{t.subtitle}</p>
        </div>
        <GlassButton
          onClick={() => setShowForm(true)}
          className="flex items-center space-x-2"
          variant="primary"
        >
          <Plus className="h-4 w-4" />
          <span>Add Zone</span>
        </GlassButton>
      </div>

      {/* Messages */}
      {message && (
        <Alert 
          variant={message.type === 'error' ? 'destructive' : 'default'} 
          className={`${isDarkTheme 
            ? 'bg-white/10 backdrop-blur-md border border-white/20 text-white' 
            : 'bg-black/10 backdrop-blur-md border border-black/20 text-gray-900'
          }`}
        >
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* Map Component */}
      <DeliveryZoneMapEnhanced
        zones={zones}
        onZoneCreate={handleZoneCreate}
        onZoneUpdate={handleZoneUpdate}
        isDarkTheme={isDarkTheme}
        t={t.map}
      />

      {/* Zone Form */}
      {showForm && (
        <GlassCard variant="primary">
          <div className="p-6">
            <h3 className={`text-lg font-semibold mb-4 ${isDarkTheme ? 'text-white' : 'text-gray-900'}`}>{t.form.title}</h3>
            <form onSubmit={handleFormSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name" className={isDarkTheme ? 'text-white/80' : 'text-gray-700'}>{t.form.name}</Label>
                  <GlassInput
                    id="name"
                    type="text"
                    placeholder={t.form.namePlaceholder}
                    value={formData.name}
                    onChange={e => handleInputChange('name', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="delivery_fee" className={isDarkTheme ? 'text-white/80' : 'text-gray-700'}>{t.form.deliveryFee}</Label>
                  <GlassInput
                    id="delivery_fee"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder={t.form.deliveryFeePlaceholder}
                    value={formData.delivery_fee}
                    onChange={e =>
                      handleInputChange('delivery_fee', parseFloat(e.target.value) || 0)
                    }
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="minimum_order" className={isDarkTheme ? 'text-white/80' : 'text-gray-700'}>{t.form.minimumOrder}</Label>
                  <GlassInput
                    id="minimum_order"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder={t.form.minimumOrderPlaceholder}
                    value={formData.minimum_order_amount}
                    onChange={e =>
                      handleInputChange('minimum_order_amount', parseFloat(e.target.value) || 0)
                    }
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="estimated_time" className={isDarkTheme ? 'text-white/80' : 'text-gray-700'}>{t.form.estimatedTime}</Label>
                  <GlassInput
                    id="estimated_time"
                    type="number"
                    min="1"
                    placeholder={t.form.estimatedTimePlaceholder}
                    value={formData.estimated_delivery_time}
                    onChange={e =>
                      handleInputChange('estimated_delivery_time', parseInt(e.target.value) || 30)
                    }
                    required
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  id="is_active"
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={e => handleInputChange('is_active', e.target.checked)}
                  className={`rounded ${isDarkTheme 
                    ? 'bg-white/10 border-white/20 text-blue-500 focus:ring-blue-500/50' 
                    : 'bg-black/10 border-black/20 text-blue-600 focus:ring-blue-600/50'
                  }`}
                />
                <Label htmlFor="is_active" className={isDarkTheme ? 'text-white/80' : 'text-gray-700'}>{t.form.isActive}</Label>
              </div>

              <div className="flex space-x-2">
                <GlassButton type="submit" disabled={isSubmitting} variant="success">
                  {isSubmitting ? (
                    editingZone ? (
                      t.form.updating
                    ) : (
                      t.form.creating
                    )
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      {t.form.save}
                    </>
                  )}
                </GlassButton>
                <GlassButton type="button" variant="secondary" onClick={handleFormCancel}>
                  <X className="h-4 w-4 mr-2" />
                  {t.form.cancel}
                </GlassButton>
              </div>
            </form>
          </div>
        </GlassCard>
      )}

      {/* Zones Table */}
      <GlassCard variant="secondary">
        <div className="p-6">
          <h3 className={`text-lg font-semibold mb-4 ${isDarkTheme ? 'text-white' : 'text-gray-900'}`}>{t.table.title}</h3>
          {zones.length === 0 ? (
            <div className="text-center py-8">
              <MapPin className={`h-12 w-12 mx-auto mb-4 ${isDarkTheme ? 'text-white/40' : 'text-gray-400'}`} />
              <p className={isDarkTheme ? 'text-white/60' : 'text-gray-600'}>{t.table.noZones}</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow className={isDarkTheme ? 'border-white/10' : 'border-black/10'}>
                  <TableHead className={isDarkTheme ? 'text-white/80' : 'text-gray-700'}>{t.table.name}</TableHead>
                  <TableHead className={isDarkTheme ? 'text-white/80' : 'text-gray-700'}>{t.table.fee}</TableHead>
                  <TableHead className={isDarkTheme ? 'text-white/80' : 'text-gray-700'}>{t.table.minOrder}</TableHead>
                  <TableHead className={isDarkTheme ? 'text-white/80' : 'text-gray-700'}>{t.table.time}</TableHead>
                  <TableHead className={isDarkTheme ? 'text-white/80' : 'text-gray-700'}>{t.table.status}</TableHead>
                  <TableHead className={isDarkTheme ? 'text-white/80' : 'text-gray-700'}>{t.table.actions}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {zones.map(zone => (
                  <TableRow 
                    key={zone.id} 
                    className={`${isDarkTheme ? 'border-white/10 hover:bg-white/5' : 'border-black/10 hover:bg-black/5'}`}
                  >
                    <TableCell className={`font-medium ${isDarkTheme ? 'text-white' : 'text-gray-900'}`}>{zone.name}</TableCell>
                    <TableCell>
                      <div className={`flex items-center ${isDarkTheme ? 'text-white/80' : 'text-gray-700'}`}>
                        <DollarSign className="h-4 w-4 mr-1" />
                        {zone.delivery_fee.toFixed(2)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className={`flex items-center ${isDarkTheme ? 'text-white/80' : 'text-gray-700'}`}>
                        <DollarSign className="h-4 w-4 mr-1" />
                        {zone.minimum_order_amount.toFixed(2)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className={`flex items-center ${isDarkTheme ? 'text-white/80' : 'text-gray-700'}`}>
                        <Clock className="h-4 w-4 mr-1" />
                        {zone.estimated_delivery_time_min}-{zone.estimated_delivery_time_max} min
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {zone.is_active ? (
                          <>
                            <CheckCircle className="h-4 w-4 mr-1 text-green-400" />
                            <span className="text-green-400">{t.table.active}</span>
                          </>
                        ) : (
                          <>
                            <XCircle className="h-4 w-4 mr-1 text-red-400" />
                            <span className="text-red-400">{t.table.inactive}</span>
                          </>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <GlassButton
                          variant="info"
                          size="small"
                          onClick={() => handleEditZone(zone)}
                        >
                          <Edit3 className="h-4 w-4" />
                        </GlassButton>
                        <GlassButton
                          variant="error"
                          size="small"
                          onClick={() => handleZoneDelete(zone.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </GlassButton>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
      </GlassCard>
    </div>
  )
}
