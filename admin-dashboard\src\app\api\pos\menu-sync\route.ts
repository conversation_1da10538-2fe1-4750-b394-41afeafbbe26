import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    const terminalId = searchParams.get('terminal_id')
    const lastSync = searchParams.get('last_sync')
    const includeInactive = searchParams.get('include_inactive') === 'true'

    if (!terminalId) {
      return NextResponse.json(
        { error: 'terminal_id is required' },
        { status: 400 }
      )
    }

    // Get the last sync timestamp for this terminal
    const syncTimestamp = lastSync || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()

    // Fetch menu data that has been updated since last sync
    const [categoriesResult, subcategoriesResult, ingredientsResult] = await Promise.all([
      // Categories
      supabase
        .from('menu_categories')
        .select('*')
        .gte('updated_at', syncTimestamp)
        .eq('is_active', includeInactive ? undefined : true),
      
      // Subcategories with related data
      supabase
        .from('subcategories')
        .select(`
          *,
          menu_categories (id, name, is_active),
          menu_customizations (*)
        `)
        .gte('updated_at', syncTimestamp)
        .eq('active', includeInactive ? undefined : true),
      
      // Ingredients
      supabase
        .from('ingredients')
        .select('*')
        .gte('updated_at', syncTimestamp)
    ])

    const categories = categoriesResult.data || []
    const subcategories = subcategoriesResult.data || []
    const ingredients = ingredientsResult.data || []

    // Get pending sync operations for this terminal
    const { data: pendingSyncs, error: syncError } = await supabase
      .from('menu_sync_queue')
      .select('*')
      .eq('sync_status', 'pending')
      .contains('metadata', { terminal_id: terminalId })
      .order('priority', { ascending: true })

    if (syncError) {
      console.error('Error fetching pending syncs:', syncError)
    }

    // Update terminal last sync timestamp
    await supabase
      .from('pos_terminals')
      .update({
        last_menu_sync: new Date().toISOString(),
        sync_status: 'synced'
      })
      .eq('terminal_id', terminalId)

    // Mark pending syncs as processing
    if (pendingSyncs && pendingSyncs.length > 0) {
      await supabase
        .from('menu_sync_queue')
        .update({ sync_status: 'processing' })
        .in('id', pendingSyncs.map(sync => sync.id))
    }

    // Prepare menu data for POS system
    const menuData = {
      categories: categories.map(cat => ({
        id: cat.id,
        name: cat.name,
        description: cat.description,
        display_order: cat.display_order,
        is_active: cat.is_active,
        icon: cat.icon,
        color: cat.color,
        updated_at: cat.updated_at
      })),
      
      subcategories: subcategories.map(sub => ({
        id: sub.id,
        name: sub.name,
        description: sub.description,
        category_id: sub.category_id,
        category_name: sub.menu_categories?.name,
        price: sub.price,
        display_order: sub.display_order,
        active: sub.active,
        image_url: sub.image_url,
        customizations: sub.menu_customizations || [],
        updated_at: sub.updated_at
      })),
      
      ingredients: ingredients.map(ing => ({
        id: ing.id,
        name: ing.name,
        category: ing.category,
        stock_quantity: ing.stock_quantity,
        min_stock_level: ing.min_stock_level,
        is_available: ing.is_available,
        allergens: ing.allergens,
        updated_at: ing.updated_at
      }))
    }

    // Calculate sync statistics
    const syncStats = {
      categories_updated: categories.length,
      subcategories_updated: subcategories.length,
      ingredients_updated: ingredients.length,
      pending_operations: pendingSyncs?.length || 0,
      last_sync_timestamp: syncTimestamp,
      current_sync_timestamp: new Date().toISOString()
    }

    return NextResponse.json({
      success: true,
      menu_data: menuData,
      sync_operations: pendingSyncs || [],
      sync_stats: syncStats,
      terminal_id: terminalId,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('POS menu sync error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { terminal_id, sync_operations, sync_results } = await request.json()

    if (!terminal_id) {
      return NextResponse.json(
        { error: 'terminal_id is required' },
        { status: 400 }
      )
    }

    const results = {
      successful_operations: [],
      failed_operations: [],
      conflicts: []
    }

    // Process sync operation results from POS
    if (sync_operations && Array.isArray(sync_operations)) {
      for (const operation of sync_operations) {
        try {
          const { id, status, error_message, applied_at } = operation

          if (status === 'completed') {
            // Mark operation as completed
            await supabase
              .from('menu_sync_queue')
              .update({
                sync_status: 'completed',
                completed_at: applied_at || new Date().toISOString(),
                error_message: null
              })
              .eq('id', id)

            results.successful_operations.push(operation)
          } else if (status === 'failed') {
            // Mark operation as failed
            await supabase
              .from('menu_sync_queue')
              .update({
                sync_status: 'failed',
                error_message: error_message || 'Unknown error',
                sync_attempts: supabase.raw('sync_attempts + 1')
              })
              .eq('id', id)

            results.failed_operations.push(operation)
          } else if (status === 'conflict') {
            // Handle sync conflicts
            await supabase
              .from('menu_sync_queue')
              .update({
                sync_status: 'failed',
                error_message: 'Sync conflict detected',
                metadata: supabase.raw(`metadata || '${JSON.stringify({ conflict: true })}'::jsonb`)
              })
              .eq('id', id)

            results.conflicts.push(operation)
          }
        } catch (error) {
          console.error('Error processing sync operation:', error)
          results.failed_operations.push({
            ...operation,
            error_message: 'Failed to process sync result'
          })
        }
      }
    }

    // Process any additional sync results
    if (sync_results) {
      // Update terminal sync statistics
      await supabase
        .from('pos_terminals')
        .update({
          last_sync_completed: new Date().toISOString(),
          sync_status: results.failed_operations.length > 0 ? 'partial' : 'synced',
          pending_updates: Math.max(0, (sync_results.pending_updates || 0) - results.successful_operations.length)
        })
        .eq('terminal_id', terminal_id)
    }

    return NextResponse.json({
      success: true,
      message: `Processed ${sync_operations?.length || 0} sync operations`,
      results,
      summary: {
        successful: results.successful_operations.length,
        failed: results.failed_operations.length,
        conflicts: results.conflicts.length,
        terminal_id
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('POS menu sync POST error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { terminal_id, menu_updates } = await request.json()

    if (!terminal_id || !menu_updates) {
      return NextResponse.json(
        { error: 'terminal_id and menu_updates are required' },
        { status: 400 }
      )
    }

    const updateResults = {
      categories: [],
      subcategories: [],
      ingredients: [],
      errors: []
    }

    // Process category updates from POS
    if (menu_updates.categories) {
      for (const category of menu_updates.categories) {
        try {
          const { data, error } = await supabase
            .from('menu_categories')
            .upsert({
              ...category,
              updated_at: new Date().toISOString()
            })
            .select()
            .single()

          if (error) {
            updateResults.errors.push(`Category ${category.name}: ${error.message}`)
          } else {
            updateResults.categories.push(data)
          }
        } catch (error) {
          updateResults.errors.push(`Category ${category.name}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }
    }

    // Process subcategory updates from POS
    if (menu_updates.subcategories) {
      for (const subcategory of menu_updates.subcategories) {
        try {
          const { data, error } = await supabase
            .from('subcategories')
            .upsert({
              ...subcategory,
              updated_at: new Date().toISOString()
            })
            .select()
            .single()

          if (error) {
            updateResults.errors.push(`Subcategory ${subcategory.name}: ${error.message}`)
          } else {
            updateResults.subcategories.push(data)
          }
        } catch (error) {
          updateResults.errors.push(`Subcategory ${subcategory.name}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }
    }

    // Process ingredient updates from POS
    if (menu_updates.ingredients) {
      for (const ingredient of menu_updates.ingredients) {
        try {
          const { data, error } = await supabase
            .from('ingredients')
            .upsert({
              ...ingredient,
              updated_at: new Date().toISOString()
            })
            .select()
            .single()

          if (error) {
            updateResults.errors.push(`Ingredient ${ingredient.name}: ${error.message}`)
          } else {
            updateResults.ingredients.push(data)
          }
        } catch (error) {
          updateResults.errors.push(`Ingredient ${ingredient.name}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }
    }

    return NextResponse.json({
      success: updateResults.errors.length === 0,
      message: `Menu updates processed: ${updateResults.categories.length + updateResults.subcategories.length + updateResults.ingredients.length} items updated`,
      results: updateResults,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('POS menu sync PUT error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
