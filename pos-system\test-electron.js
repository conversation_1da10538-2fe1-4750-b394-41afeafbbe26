const { chromium } = require('playwright');

async function testElectronApp() {
  console.log('🚀 Starting Playwright test for POS System...');
  
  try {
    // Connect to the Electron app via debugging port
    const browser = await chromium.connectOverCDP('http://localhost:9222');
    console.log('✅ Connected to Electron app via CDP');
    
    // Get all contexts (should include the Electron renderer)
    const contexts = browser.contexts();
    console.log(`📱 Found ${contexts.length} browser contexts`);
    
    if (contexts.length === 0) {
      console.log('❌ No browser contexts found');
      return;
    }
    
    // Get the first context (main Electron window)
    const context = contexts[0];
    const pages = context.pages();
    console.log(`📄 Found ${pages.length} pages in context`);
    
    if (pages.length === 0) {
      console.log('❌ No pages found in context');
      return;
    }
    
    // Get the main page
    const page = pages[0];
    console.log(`🌐 Page URL: ${page.url()}`);
    console.log(`📋 Page Title: ${await page.title()}`);
    
    // Take a screenshot
    await page.screenshot({ path: 'pos-system-screenshot.png', fullPage: true });
    console.log('📸 Screenshot saved as pos-system-screenshot.png');
    
    // Get page content info
    const bodyText = await page.textContent('body');
    console.log(`📝 Page has ${bodyText ? bodyText.length : 0} characters of text`);
    
    // Check if login form is present
    const loginElements = await page.$$('input[type="password"], input[placeholder*="PIN"], button[type="submit"]');
    console.log(`🔐 Found ${loginElements.length} login-related elements`);
    
    // Try to find specific POS elements
    const posElements = await page.$$('[class*="pos"], [class*="order"], [class*="dashboard"]');
    console.log(`🏪 Found ${posElements.length} POS-related elements`);
    
    // Get all button elements
    const buttons = await page.$$('button');
    console.log(`🔘 Found ${buttons.length} buttons on the page`);
    
    // Get all input elements
    const inputs = await page.$$('input');
    console.log(`📝 Found ${inputs.length} input fields`);
    
    // Check for any error messages
    const errorElements = await page.$$('[class*="error"], [class*="warning"], .text-red-500');
    console.log(`⚠️ Found ${errorElements.length} error/warning elements`);
    
    console.log('✅ Playwright test completed successfully!');
    
    // Keep the connection open for manual testing
    console.log('🔄 Keeping connection open for manual testing...');
    console.log('Press Ctrl+C to exit');
    
    // Wait indefinitely
    await new Promise(() => {});
    
  } catch (error) {
    console.error('❌ Error connecting to Electron app:', error.message);
    console.log('💡 Make sure the Electron app is running with: npm run start:debug');
  }
}

// Run the test
testElectronApp();
