/**
 * Authentication Security Configuration
 * 
 * This file defines comprehensive security policies and configurations
 * for the isolated authentication system in the admin dashboard.
 */

// Security headers configuration
export const AUTH_SECURITY_HEADERS = {
  // Content Security Policy for auth pages (strict)
  CSP: {
    'default-src': "'self'",
    'script-src': "'self' 'unsafe-inline' https://unpkg.com",
    'style-src': "'self' 'unsafe-inline'",
    'font-src': "'self' https://fonts.gstatic.com",
    'img-src': "'self' data:",
    'connect-src': "'self' https://*.supabase.co wss://*.supabase.co",
    'frame-src': "'none'",
    'object-src': "'none'",
    'base-uri': "'self'",
    'form-action': "'self'",
    'frame-ancestors': "'none'",
    'upgrade-insecure-requests': true,
  },
  
  // Additional security headers
  ADDITIONAL: {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'X-Robots-Tag': 'noindex, nofollow, noarchive, nosnippet',
    'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0',
    'Pragma': 'no-cache',
    'Expires': '0',
  }
} as const

// Rate limiting configuration
export const RATE_LIMITS = {
  LOGIN: {
    maxAttempts: 5,
    windowMs: 15 * 60 * 1000, // 15 minutes
    lockoutDuration: 5 * 60 * 1000, // 5 minutes
    message: 'Too many login attempts. Please try again later.',
  },
  
  PASSWORD_RESET: {
    maxAttempts: 3,
    windowMs: 60 * 60 * 1000, // 1 hour
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
    message: 'Too many password reset requests. Please try again later.',
  },
  
  TWO_FACTOR: {
    maxAttempts: 3,
    windowMs: 5 * 60 * 1000, // 5 minutes
    lockoutDuration: 10 * 60 * 1000, // 10 minutes
    message: 'Too many 2FA attempts. Please try again later.',
  },
} as const

// Input validation rules
export const VALIDATION_RULES = {
  EMAIL: {
    pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    maxLength: 255,
    required: true,
    sanitize: true,
  },
  
  PASSWORD: {
    minLength: 12,
    maxLength: 128,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    required: true,
    sanitize: false, // Don't sanitize passwords
  },
  
  TWO_FACTOR_CODE: {
    pattern: /^[0-9]{6}$/,
    maxLength: 6,
    required: true,
    sanitize: true,
  },
} as const

// Session security configuration
export const SESSION_CONFIG = {
  // Cookie settings
  COOKIE: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    path: '/',
    domain: process.env.NODE_ENV === 'production' ? process.env.NEXT_PUBLIC_DOMAIN : undefined,
  },
  
  // Session timeout settings
  TIMEOUT: {
    idle: 30 * 60 * 1000, // 30 minutes idle timeout
    absolute: 8 * 60 * 60 * 1000, // 8 hours absolute timeout
    warning: 5 * 60 * 1000, // Show warning 5 minutes before timeout
  },
  
  // Token settings
  TOKEN: {
    accessTokenExpiry: 15 * 60 * 1000, // 15 minutes
    refreshTokenExpiry: 7 * 24 * 60 * 60 * 1000, // 7 days
    rotateRefreshToken: true,
  },
} as const

// Brute force protection configuration
export const BRUTE_FORCE_PROTECTION = {
  PROGRESSIVE_DELAYS: [
    1000,   // 1 second after 1st failed attempt
    2000,   // 2 seconds after 2nd failed attempt
    5000,   // 5 seconds after 3rd failed attempt
    10000,  // 10 seconds after 4th failed attempt
    30000,  // 30 seconds after 5th failed attempt
  ],
  
  IP_BLOCKING: {
    enabled: true,
    maxAttemptsPerIP: 20,
    blockDuration: 60 * 60 * 1000, // 1 hour
  },
  
  ACCOUNT_LOCKING: {
    enabled: true,
    maxAttempts: 5,
    lockDuration: 5 * 60 * 1000, // 5 minutes
    escalatingLockout: true, // Increase lockout duration with repeated violations
  },
} as const

// Device fingerprinting configuration
export const DEVICE_FINGERPRINTING = {
  enabled: true,
  trackingPoints: [
    'userAgent',
    'screen',
    'timezone',
    'language',
    'platform',
    'cookieEnabled',
    'doNotTrack',
  ],
  suspiciousThreshold: 0.7, // Similarity threshold for device recognition
} as const

// Audit logging configuration
export const AUDIT_CONFIG = {
  EVENTS: {
    LOGIN_SUCCESS: 'auth.login.success',
    LOGIN_FAILURE: 'auth.login.failure',
    LOGOUT: 'auth.logout',
    PASSWORD_CHANGE: 'auth.password.change',
    PASSWORD_RESET_REQUEST: 'auth.password.reset.request',
    PASSWORD_RESET_SUCCESS: 'auth.password.reset.success',
    TWO_FACTOR_SETUP: 'auth.2fa.setup',
    TWO_FACTOR_SUCCESS: 'auth.2fa.success',
    TWO_FACTOR_FAILURE: 'auth.2fa.failure',
    ACCOUNT_LOCKED: 'auth.account.locked',
    SUSPICIOUS_ACTIVITY: 'auth.suspicious.activity',
  },
  
  RETENTION: {
    security: 90, // days
    audit: 365,   // days
    compliance: 2555, // 7 years in days
  },
} as const

// Security monitoring thresholds
export const MONITORING_THRESHOLDS = {
  FAILED_LOGINS_PER_MINUTE: 10,
  FAILED_LOGINS_PER_HOUR: 50,
  SUSPICIOUS_IPS_PER_HOUR: 5,
  PASSWORD_RESET_REQUESTS_PER_HOUR: 20,
  TWO_FACTOR_FAILURES_PER_MINUTE: 5,
} as const

// Compliance settings
export const COMPLIANCE = {
  GDPR: {
    enabled: true,
    dataRetentionDays: 365,
    rightToErasure: true,
    consentRequired: true,
  },
  
  PCI_DSS: {
    enabled: true,
    requireStrongPasswords: true,
    encryptSensitiveData: true,
    regularSecurityScans: true,
  },
  
  SOC2: {
    enabled: true,
    auditLogging: true,
    accessControls: true,
    dataEncryption: true,
  },
} as const

// Error messages (security-conscious)
export const SECURITY_MESSAGES = {
  GENERIC_ERROR: 'Authentication failed. Please check your credentials and try again.',
  ACCOUNT_LOCKED: 'Account temporarily locked due to security policy. Please try again later.',
  RATE_LIMITED: 'Too many requests. Please wait before trying again.',
  INVALID_SESSION: 'Your session has expired. Please log in again.',
  SUSPICIOUS_ACTIVITY: 'Suspicious activity detected. Please contact support if you believe this is an error.',
  TWO_FACTOR_REQUIRED: 'Two-factor authentication is required to proceed.',
  TWO_FACTOR_INVALID: 'Invalid two-factor authentication code. Please try again.',
} as const

// Feature flags for security features
export const SECURITY_FEATURES = {
  WEBAUTHN_ENABLED: process.env.NEXT_PUBLIC_WEBAUTHN_ENABLED === 'true',
  BIOMETRIC_AUTH_ENABLED: process.env.NEXT_PUBLIC_BIOMETRIC_AUTH_ENABLED === 'true',
  RISK_BASED_AUTH_ENABLED: process.env.NEXT_PUBLIC_RISK_BASED_AUTH_ENABLED === 'true',
  DEVICE_FINGERPRINTING_ENABLED: process.env.NEXT_PUBLIC_DEVICE_FINGERPRINTING_ENABLED === 'true',
  CAPTCHA_ENABLED: process.env.NEXT_PUBLIC_CAPTCHA_ENABLED === 'true',
  PASSWORDLESS_ENABLED: process.env.NEXT_PUBLIC_PASSWORDLESS_ENABLED === 'true',
} as const

// Environment-specific configuration
export const getEnvironmentConfig = () => {
  const isDevelopment = process.env.NODE_ENV === 'development'
  const isProduction = process.env.NODE_ENV === 'production'
  
  return {
    isDevelopment,
    isProduction,
    
    // Relaxed settings for development
    ...(isDevelopment && {
      RATE_LIMITS: {
        ...RATE_LIMITS,
        LOGIN: {
          ...RATE_LIMITS.LOGIN,
          maxAttempts: 10, // More lenient for development
          lockoutDuration: 1 * 60 * 1000, // 1 minute
        },
      },
    }),
    
    // Strict settings for production
    ...(isProduction && {
      ADDITIONAL_HEADERS: {
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
        'Expect-CT': 'max-age=86400, enforce',
        'Feature-Policy': 'camera \'none\'; microphone \'none\'; geolocation \'none\'',
      },
    }),
  }
}

// Type definitions
export type SecurityEvent = {
  type: keyof typeof AUDIT_CONFIG.EVENTS
  timestamp: Date
  userId?: string
  email?: string
  ip?: string
  userAgent?: string
  success: boolean
  metadata?: Record<string, any>
}

export type RateLimitInfo = {
  attempts: number
  windowStart: Date
  isLocked: boolean
  lockExpires?: Date
}

export type DeviceFingerprint = {
  id: string
  userAgent: string
  screen: string
  timezone: string
  language: string
  platform: string
  trusted: boolean
  lastSeen: Date
} 