'use client';

import { usePathname } from 'next/navigation';
import { ErrorBoundary } from './ErrorBoundary';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname();

  // Don't apply any additional layout - let ThemeWrapper handle navigation
  const isAuthRoute = pathname?.startsWith('/auth');

  if (isAuthRoute) {
    return (
      <ErrorBoundary>
        {children}
      </ErrorBoundary>
    );
  }

  // Wrap all non-auth routes in error boundary for better error handling
  return (
    <ErrorBoundary>
      <main role="main" className="min-h-screen">
        {children}
      </main>
    </ErrorBoundary>
  );
}