# Creperie Security System

Comprehensive security implementation for the Creperie digital ecosystem, covering API security, authentication, data protection, audit logging, and compliance with Greek data protection laws and PCI DSS requirements.

## 🔒 Security Features

### API Security
- **Rate Limiting**: Configurable rate limits for authentication, API, and payment endpoints
- **Input Validation**: Email, password, phone number, and general input validation
- **SQL Injection Prevention**: Parameterized queries and input sanitization
- **XSS Protection**: Input sanitization and Content Security Policy headers
- **Security Headers**: Comprehensive HTTP security headers (HSTS, CSP, X-Frame-Options, etc.)

### Authentication Security
- **Password Requirements**: Configurable strength requirements with common password detection
- **Session Management**: Secure session creation, validation, and cleanup
- **JWT Token Security**: Token generation, validation, and blacklisting
- **Brute Force Protection**: Account lockout and suspicious activity detection
- **Two-Factor Authentication**: TOTP-based 2FA with backup codes

### Data Protection
- **Encryption at Rest**: AES-256-GCM encryption for sensitive data
- **HTTPS Enforcement**: Automatic HTTPS redirection and security headers
- **PII Data Handling**: Automatic PII detection, encryption, and anonymization
- **GDPR Compliance**: Data subject rights, consent management, and data retention
- **Key Rotation**: Automatic encryption key rotation and management

### Audit Logging
- **User Actions**: Comprehensive logging of all user interactions
- **Data Changes**: Detailed audit trail for data modifications
- **Login Attempts**: Authentication event logging with failure analysis
- **System Events**: Security events, errors, and system state changes
- **Compliance Reporting**: Automated compliance reports for GDPR, PCI DSS, and Greek DPA

### Compliance
- **Greek Data Protection Laws**: Full compliance with Greek DPA requirements
- **PCI DSS**: Payment card industry compliance for secure payment processing
- **Customer Privacy Rights**: Data subject rights implementation (access, rectification, erasure, portability)
- **Secure Data Storage**: Encrypted storage with proper access controls

## 📁 File Structure

```
shared/security/
├── README.md                    # This documentation
├── index.ts                     # Main security module exports
├── security-config.ts           # Security configuration and policies
├── security-middleware.ts       # Express middleware for security
├── auth-security.ts            # Authentication security utilities
├── data-protection.ts          # Data protection and encryption
├── audit-logging.ts            # Audit logging system
├── pci-compliance.ts           # PCI DSS compliance utilities
└── greek-data-protection.ts    # Greek DPA compliance
```

## 🚀 Quick Start

### 1. Initialize Security

```typescript
import { SecurityManager } from '@/shared/security';

// Initialize security for your application
await SecurityManager.initializeSecurity('customer_web');
```

### 2. Apply Security Middleware

```typescript
import express from 'express';
import {
  securityHeaders,
  authRateLimit,
  apiRateLimit,
  preventSQLInjection,
  xssProtection
} from '@/shared/security';

const app = express();

// Apply security headers
app.use(securityHeaders);

// Apply rate limiting
app.use('/auth', authRateLimit);
app.use('/api', apiRateLimit);

// Apply input protection
app.use(preventSQLInjection);
app.use(xssProtection);
```

### 3. Handle Authentication

```typescript
import { PasswordSecurity, SessionSecurity, BruteForceProtection } from '@/shared/security';

// Validate password strength
const validation = await PasswordSecurity.validatePassword('userPassword123!');
if (!validation.isValid) {
  return res.status(400).json({ errors: validation.errors });
}

// Check for brute force attempts
const isBlocked = await BruteForceProtection.isBlocked(ipAddress);
if (isBlocked) {
  return res.status(429).json({ error: 'Too many failed attempts' });
}

// Create secure session
const session = await SessionSecurity.createSession(userId, 'customer_web', {
  ipAddress,
  userAgent: req.headers['user-agent']
});
```

### 4. Protect Sensitive Data

```typescript
import { SecurityManager, PIIDataHandler } from '@/shared/security';

// Handle PII data
const encryptedData = await SecurityManager.handlePIIData(
  userData,
  'store',
  {
    userId,
    sessionId,
    ipAddress,
    userAgent,
    platform: 'customer_web',
    permissions: ['read', 'write']
  }
);

// Encrypt sensitive data
const encrypted = await SecurityManager.encryptSensitiveData(
  paymentData,
  'payment_information'
);
```

### 5. Log Security Events

```typescript
import { AuditLogger, SecurityLogger } from '@/shared/security';

// Log user action
await AuditLogger.logEvent({
  action: 'order_created',
  resource: 'order',
  resourceId: orderId,
  userId,
  metadata: { orderTotal, paymentMethod },
  category: 'user_action',
  severity: 'low'
});

// Log security event
await SecurityLogger.logEvent({
  type: 'authentication',
  severity: 'medium',
  description: 'Failed login attempt',
  context: { userId, ipAddress, userAgent, platform: 'customer_web', permissions: [] },
  timestamp: new Date()
});
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Encryption Keys
ENCRYPTION_KEY=your_32_character_encryption_key
JWT_SECRET=your_jwt_secret_key

# Security Configuration
SECURITY_ENVIRONMENT=production
SECURITY_DEBUG=false

# Compliance
DPO_EMAIL=<EMAIL>
DPO_PHONE=+30-xxx-xxx-xxxx
ORGANIZATION_ADDRESS=Your Organization Address

# PCI DSS
PCI_ENVIRONMENT=production
PCI_TOKENIZATION_ENDPOINT=https://your-tokenization-service.com
```

### Security Configuration

Customize security settings in `security-config.ts`:

```typescript
export const SECURITY_CONFIG = {
  // Rate limiting
  rateLimiting: {
    authentication: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxAttempts: 5,
      blockDuration: 30 * 60 * 1000 // 30 minutes
    }
  },
  
  // Password requirements
  password: {
    minLength: 12,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true
  },
  
  // Session configuration
  session: {
    duration: {
      admin: 8 * 60 * 60 * 1000, // 8 hours
      pos: 12 * 60 * 60 * 1000, // 12 hours
      customer_web: 24 * 60 * 60 * 1000, // 24 hours
      customer_mobile: 30 * 24 * 60 * 60 * 1000 // 30 days
    }
  }
};
```

## 📊 Database Schema

Run the security migration to set up the required database tables:

```sql
-- Run this migration file
supabase/migrations/20241202000000_security_audit_system.sql
```

This creates tables for:
- Security logs
- Audit logs
- Authentication attempts
- Rate limiting
- Encryption keys
- GDPR requests
- Data retention policies
- PCI compliance logs
- Security incidents
- Vulnerability assessments

## 🛡️ Security Best Practices

### For Developers

1. **Always validate input**: Use the provided input validation utilities
2. **Encrypt sensitive data**: Use `SecurityManager.encryptSensitiveData()` for sensitive information
3. **Log security events**: Use `SecurityLogger` for security-related events
4. **Handle PII properly**: Use `PIIDataHandler` for personal data
5. **Implement proper error handling**: Don't expose sensitive information in error messages

### For System Administrators

1. **Regular security audits**: Use `SecurityManager.generateComplianceReport()`
2. **Monitor security logs**: Set up alerts for high-severity security events
3. **Key rotation**: Regularly rotate encryption keys using `EncryptionManager.rotateKeys()`
4. **Update dependencies**: Keep all security dependencies up to date
5. **Backup security configurations**: Ensure security configurations are backed up

## 🔍 Monitoring and Alerting

### Security Events

The system automatically logs and can alert on:
- Failed authentication attempts
- Suspicious activity patterns
- Data breach incidents
- Compliance violations
- System security events

### Compliance Monitoring

```typescript
// Generate compliance reports
const gdprReport = await SecurityManager.generateComplianceReport(
  'gdpr',
  new Date('2024-01-01'),
  new Date('2024-12-31')
);

const pciReport = await SecurityManager.generateComplianceReport(
  'pci_dss',
  new Date('2024-01-01'),
  new Date('2024-12-31')
);
```

## 🇬🇷 Greek Data Protection Compliance

### GDPR Requests

```typescript
import { GreekDPARequestHandler } from '@/shared/security';

// Handle data access request
const requestId = await GreekDPARequestHandler.submitRequest(
  'access',
  dataSubjectId,
  '<EMAIL>',
  'John Doe',
  'I would like to access my personal data'
);

// Process the request
const responseData = await GreekDPARequestHandler.processAccessRequest(requestId);
```

### Consent Management

```typescript
import { GreekConsentManager } from '@/shared/security';

// Record consent
const consentId = await GreekConsentManager.recordConsent(
  userId,
  'marketing',
  'Email marketing communications',
  'consent',
  'explicit',
  ['email', 'preferences'],
  365 // retention period in days
);

// Withdraw consent
await GreekConsentManager.withdrawConsent(userId, consentId, 'User request');
```

## 💳 PCI DSS Compliance

### Card Data Handling

```typescript
import { PCICardDataHandler } from '@/shared/security';

// Tokenize card data
const token = await PCICardDataHandler.tokenizeCardData({
  cardNumber: '****************',
  expiryMonth: '12',
  expiryYear: '2025',
  cvv: '123'
});

// Use token for processing
const cardData = await PCICardDataHandler.detokenizeCardData(token);
```

### Compliance Reporting

```typescript
import { PCIComplianceReporter } from '@/shared/security';

// Generate PCI compliance report
const report = await PCIComplianceReporter.generateComplianceReport(
  new Date('2024-01-01'),
  new Date('2024-12-31')
);
```

## 🚨 Incident Response

### Data Breach Handling

```typescript
import { GreekDataBreachHandler } from '@/shared/security';

// Report a data breach
const breachId = await GreekDataBreachHandler.reportBreach(
  'confidentiality',
  'high',
  ['personal_data', 'financial_data'],
  150, // affected data subjects
  'Unauthorized access to customer database',
  'SQL injection attack'
);

// Notify Greek DPA if required
await GreekDataBreachHandler.notifyGreekDPA(breachId);
```

### Security Incident Management

```typescript
// Handle critical security events
await SecurityManager.handleSecurityEvent({
  type: 'security_violation',
  severity: 'critical',
  description: 'Multiple failed admin login attempts',
  context: {
    ipAddress: '*************',
    userAgent: 'Suspicious Bot',
    platform: 'admin',
    permissions: []
  },
  timestamp: new Date()
});
```

## 📈 Performance Considerations

### Caching
- Rate limiting data is cached in memory/Redis
- Encryption keys are cached securely
- Session data is optimized for quick access

### Database Optimization
- Indexes on frequently queried security tables
- Partitioning for large audit log tables
- Regular cleanup of expired data

### Monitoring
- Performance metrics for security operations
- Alert thresholds for security processing times
- Resource usage monitoring

## 🔄 Maintenance

### Regular Tasks

1. **Key Rotation**: Monthly encryption key rotation
2. **Log Cleanup**: Automated cleanup of old audit logs
3. **Security Updates**: Regular dependency updates
4. **Compliance Reviews**: Quarterly compliance assessments
5. **Penetration Testing**: Annual security testing

### Backup and Recovery

1. **Security Configuration Backup**: Regular backup of security settings
2. **Encryption Key Backup**: Secure backup of encryption keys
3. **Audit Log Backup**: Long-term storage of audit logs
4. **Disaster Recovery**: Security system recovery procedures

## 📞 Support and Contact

### Security Team
- **Data Protection Officer**: <EMAIL>
- **Security Team**: <EMAIL>
- **Emergency Contact**: +30-xxx-xxx-xxxx

### Compliance Authorities
- **Greek Data Protection Authority**: https://www.dpa.gr/
- **PCI Security Standards Council**: https://www.pcisecuritystandards.org/

## 📚 Additional Resources

- [GDPR Compliance Guide](https://gdpr.eu/)
- [Greek Data Protection Authority Guidelines](https://www.dpa.gr/)
- [PCI DSS Requirements](https://www.pcisecuritystandards.org/)
- [OWASP Security Guidelines](https://owasp.org/)
- [Node.js Security Best Practices](https://nodejs.org/en/docs/guides/security/)

---

**Note**: This security system is designed to be comprehensive and compliant with current regulations. Regular updates and security assessments are recommended to maintain the highest level of protection.