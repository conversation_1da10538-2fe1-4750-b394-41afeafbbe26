import React, { useState, useEffect } from 'react';
import { useTheme } from '../../contexts/theme-context';
import { getApiUrl } from '../../../config/environment';

interface CustomerAddress {
  id: string;
  street_address: string;
  city: string;
  postal_code?: string;
  floor_number?: string;
  notes?: string;
  address_type: string;
  is_default: boolean;
  created_at: string;
}

interface EditAddressModalProps {
  isOpen: boolean;
  onClose: () => void;
  address: CustomerAddress;
  customerId: string;
  onAddressUpdated: (updatedAddress: CustomerAddress) => void;
}

const EditAddressModal: React.FC<EditAddressModalProps> = ({
  isOpen,
  onClose,
  address,
  customerId,
  onAddressUpdated
}) => {
  const { resolvedTheme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    address: '',
    postal_code: '',
    floor_number: '',
    notes: '',
    address_type: 'delivery',
    is_default: false
  });

  useEffect(() => {
    if (isOpen && address) {
      setFormData({
        address: `${address.street_address}, ${address.city}`,
        postal_code: address.postal_code || '',
        floor_number: address.floor_number || '',
        notes: address.notes || '',
        address_type: address.address_type,
        is_default: address.is_default
      });
    }
  }, [isOpen, address]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {

      const response = await fetch(getApiUrl(`customers/${customerId}/addresses/${address.id}`), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success && result.address) {
        onAddressUpdated(result.address);
        onClose();
      } else {
        console.error('Failed to update address:', result.error);
        alert(`Failed to update address: ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error updating address:', error);
      alert('Error updating address. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center animate-fade-in">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm animate-fade-in" 
        onClick={onClose} 
      />
      
      {/* Modal */}
      <div className={`relative z-10 w-full max-w-lg mx-4 p-6 rounded-2xl border animate-float-up ${
        resolvedTheme === 'light'
          ? 'bg-white/95 border-blue-200/60 shadow-2xl backdrop-blur-xl'
          : 'bg-gray-900/95 border-blue-400/30 shadow-2xl shadow-black/50 backdrop-blur-xl'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className={`text-xl font-bold ${
            resolvedTheme === 'light' ? 'text-gray-900' : 'text-white'
          }`}>
            Edit Address
          </h2>
          <button
            onClick={onClose}
            className={`p-2 rounded-lg transition-all ${
              resolvedTheme === 'light'
                ? 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                : 'hover:bg-gray-800 text-gray-400 hover:text-gray-200'
            }`}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Address Field */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              resolvedTheme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              Street Address *
            </label>
            <input
              type="text"
              name="address"
              value={formData.address}
              onChange={handleChange}
              required
              className={`w-full px-4 py-3 rounded-lg border transition-all ${
                resolvedTheme === 'light'
                  ? 'bg-white/80 border-gray-200 focus:border-blue-400 focus:bg-white text-gray-900'
                  : 'bg-gray-800/80 border-gray-600 focus:border-blue-400 focus:bg-gray-800 text-white'
              } focus:outline-none focus:ring-2 focus:ring-blue-400/20`}
              placeholder="Enter street address"
            />
          </div>

          {/* Postal Code Field */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              resolvedTheme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              Postal Code
            </label>
            <input
              type="text"
              name="postal_code"
              value={formData.postal_code}
              onChange={handleChange}
              className={`w-full px-4 py-3 rounded-lg border transition-all ${
                resolvedTheme === 'light'
                  ? 'bg-white/80 border-gray-200 focus:border-blue-400 focus:bg-white text-gray-900'
                  : 'bg-gray-800/80 border-gray-600 focus:border-blue-400 focus:bg-gray-800 text-white'
              } focus:outline-none focus:ring-2 focus:ring-blue-400/20`}
              placeholder="Enter postal code"
            />
          </div>

          {/* Floor Number Field */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              resolvedTheme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              Floor Number
            </label>
            <input
              type="text"
              name="floor_number"
              value={formData.floor_number}
              onChange={handleChange}
              className={`w-full px-4 py-3 rounded-lg border transition-all ${
                resolvedTheme === 'light'
                  ? 'bg-white/80 border-gray-200 focus:border-blue-400 focus:bg-white text-gray-900'
                  : 'bg-gray-800/80 border-gray-600 focus:border-blue-400 focus:bg-gray-800 text-white'
              } focus:outline-none focus:ring-2 focus:ring-blue-400/20`}
              placeholder="e.g., 2nd Floor, Apt 3B"
            />
          </div>

          {/* Address Type Field */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              resolvedTheme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              Address Type
            </label>
            <select
              name="address_type"
              value={formData.address_type}
              onChange={handleChange}
              className={`w-full px-4 py-3 rounded-lg border transition-all ${
                resolvedTheme === 'light'
                  ? 'bg-white/80 border-gray-200 focus:border-blue-400 focus:bg-white text-gray-900'
                  : 'bg-gray-800/80 border-gray-600 focus:border-blue-400 focus:bg-gray-800 text-white'
              } focus:outline-none focus:ring-2 focus:ring-blue-400/20`}
            >
              <option value="delivery">Delivery</option>
              <option value="home">Home</option>
              <option value="work">Work</option>
              <option value="other">Other</option>
            </select>
          </div>

          {/* Notes Field */}
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              resolvedTheme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              Delivery Notes
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              rows={3}
              className={`w-full px-4 py-3 rounded-lg border transition-all resize-none ${
                resolvedTheme === 'light'
                  ? 'bg-white/80 border-gray-200 focus:border-blue-400 focus:bg-white text-gray-900'
                  : 'bg-gray-800/80 border-gray-600 focus:border-blue-400 focus:bg-gray-800 text-white'
              } focus:outline-none focus:ring-2 focus:ring-blue-400/20`}
              placeholder="Any special delivery instructions..."
            />
          </div>

          {/* Default Address Checkbox */}
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              name="is_default"
              checked={formData.is_default}
              onChange={handleChange}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
            />
            <label className={`text-sm font-medium ${
              resolvedTheme === 'light' ? 'text-gray-700' : 'text-gray-300'
            }`}>
              Set as default address
            </label>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 mt-8">
            <button
              type="button"
              onClick={onClose}
              disabled={isLoading}
              className={`flex-1 px-6 py-3 rounded-lg font-medium transition-all ${
                resolvedTheme === 'light'
                  ? 'bg-gray-200/80 text-gray-800 hover:bg-gray-300/80 backdrop-blur-sm'
                  : 'bg-gray-700/80 text-gray-200 hover:bg-gray-600/80 backdrop-blur-sm'
              } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className={`flex-1 px-6 py-3 rounded-lg font-medium transition-all ${
                resolvedTheme === 'light'
                  ? 'bg-blue-500/90 text-white hover:bg-blue-600/90 backdrop-blur-sm'
                  : 'bg-blue-600/90 text-blue-100 hover:bg-blue-500/90 backdrop-blur-sm'
              } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''} shadow-lg`}
            >
              {isLoading ? 'Updating...' : 'Update Address'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditAddressModal; 