#!/usr/bin/env node

// Remove All Menu Items - Enhanced MCP Implementation
// Uses proper environment configuration and admin dashboard integration

const { createClient } = require('@supabase/supabase-js');
const path = require('path');

// Load environment variables from admin-dashboard
require('dotenv').config({ path: path.join(__dirname, 'admin-dashboard', '.env.local') });

// Supabase configuration using the correct environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.NEXT_PUBLIC_SUPABASE_SECRET; // This is the service role key

console.log('🔧 Enhanced Menu Items Removal using MCP-style Implementation\n');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.log('Required variables:');
  console.log('- NEXT_PUBLIC_SUPABASE_URL');
  console.log('- NEXT_PUBLIC_SUPABASE_SECRET (service role key)');
  console.log('\nFound:');
  console.log(`- URL: ${supabaseUrl ? '✅ Set' : '❌ Missing'}`);
  console.log(`- Service Key: ${supabaseServiceKey ? '✅ Set' : '❌ Missing'}`);
  process.exit(1);
}

// Create admin client with service role key for full database access
const adminClient = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// MCP-style function implementations
const mcpOperations = {
  async testConnection() {
    console.log('🔍 Testing MCP-style database connection...');
    try {
      const { data, error } = await adminClient
        .from('categories')
        .select('id')
        .limit(1);
      
      if (error) {
        console.error('❌ Connection test failed:', error.message);
        return false;
      }
      
      console.log('✅ MCP-style connection successful');
      return true;
    } catch (error) {
      console.error('❌ Connection test error:', error.message);
      return false;
    }
  },

  async executeSQL(query, description = 'SQL Query') {
    console.log(`🔍 MCP-style ${description}:`);
    console.log(`Query: ${query.substring(0, 100)}${query.length > 100 ? '...' : ''}`);
    
    try {
      // For DELETE operations, use the direct table method
      if (query.toLowerCase().includes('delete from menu_items')) {
        const { data, error } = await adminClient
          .from('menu_items')
          .delete()
          .neq('id', '00000000-0000-0000-0000-000000000000'); // This will match all rows
        
        if (error) {
          console.error(`❌ ${description} failed:`, error.message);
          return { error };
        }
        
        console.log(`✅ ${description} successful`);
        return { data: data || [] };
      }
      
      // For SELECT operations, parse the table name and use direct table access
      if (query.toLowerCase().includes('select')) {
        const tableMatch = query.match(/from\s+(\w+)/i);
        if (tableMatch) {
          const tableName = tableMatch[1];
          
          if (query.includes('COUNT(*)')) {
            const { count, error } = await adminClient
              .from(tableName)
              .select('*', { count: 'exact', head: true });
            
            if (error) {
              console.error(`❌ ${description} failed:`, error.message);
              return { error };
            }
            
            console.log(`✅ ${description} successful`);
            return { data: [{ count: count || 0 }] };
          } else {
            const { data, error } = await adminClient
              .from(tableName)
              .select('*');
            
            if (error) {
              console.error(`❌ ${description} failed:`, error.message);
              return { error };
            }
            
            console.log(`✅ ${description} successful`);
            return { data: data || [] };
          }
        }
      }
      
      // Fallback: try RPC if available
      try {
        const { data, error } = await adminClient.rpc('exec_sql', { sql: query });
        
        if (error) {
          console.error(`❌ ${description} failed:`, error.message);
          return { error };
        }
        
        console.log(`✅ ${description} successful`);
        return { data: data || [] };
      } catch (rpcError) {
        console.error(`❌ ${description} failed (RPC not available):`, rpcError.message);
        return { error: rpcError };
      }
      
    } catch (error) {
      console.error(`❌ ${description} error:`, error.message);
      return { error };
    }
  },

  async listTables() {
    console.log('🔍 MCP-style table listing...');
    try {
      // Get table information from information_schema
      const { data, error } = await adminClient
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public');
      
      if (error) {
        console.error('❌ Table listing failed:', error.message);
        return { error };
      }
      
      console.log('✅ Table listing successful');
      return { data: data || [] };
    } catch (error) {
      console.error('❌ Table listing error:', error.message);
      return { error };
    }
  }
};

async function removeAllMenuItemsEnhanced() {
  console.log('🚀 Starting Enhanced Menu Items Removal\n');

  try {
    // Step 1: Test MCP-style connection
    const connectionOk = await mcpOperations.testConnection();
    if (!connectionOk) {
      console.error('❌ Database connection failed. Exiting.');
      return;
    }

    // Step 2: Check existing menu items using MCP-style operation
    console.log('\n📋 Checking existing menu items...');
    const checkResult = await mcpOperations.executeSQL(
      'SELECT id, name_en, name_el, category_id, price, is_available, created_at FROM menu_items ORDER BY created_at ASC',
      'Menu Items Check'
    );

    if (checkResult.error) {
      console.error('❌ Error checking menu items:', checkResult.error);
      return;
    }

    const menuItems = checkResult.data || [];
    
    if (menuItems.length === 0) {
      console.log('✅ No menu items found in the database. Nothing to delete.');
      return;
    }

    console.log(`\n📊 Found ${menuItems.length} menu items to delete:\n`);
    
    // Display menu items with enhanced formatting
    menuItems.forEach((item, index) => {
      const name = item.name_en || item.name_el || 'Unnamed';
      const price = item.price ? `€${parseFloat(item.price).toFixed(2)}` : 'No price';
      const status = item.is_available ? '✅ Available' : '❌ Unavailable';
      const date = new Date(item.created_at).toLocaleDateString('en-GB');
      
      console.log(`${index + 1}. ${name}`);
      console.log(`   💰 Price: ${price} | ${status}`);
      console.log(`   🆔 ID: ${item.id}`);
      console.log(`   📂 Category: ${item.category_id}`);
      console.log(`   📅 Created: ${date}`);
      console.log('');
    });

    // Step 3: Check dependencies using MCP-style operations
    console.log('🔍 Checking for related data dependencies...');
    
    const dependencyChecks = [
      { name: 'Menu Item Ingredients', table: 'menu_item_ingredients' },
      { name: 'Order Items', table: 'order_items' }
    ];

    for (const check of dependencyChecks) {
      const result = await mcpOperations.executeSQL(
        `SELECT COUNT(*) as count FROM ${check.table}`,
        `${check.name} Count`
      );
      
      if (result.data && result.data.length > 0) {
        const count = result.data[0].count;
        console.log(`   📊 ${check.name}: ${count} records`);
        if (count > 0) {
          console.log(`   ⚠️  Warning: ${count} ${check.name.toLowerCase()} may be affected`);
        }
      }
    }

    console.log('');

    // Step 4: Delete menu items using enhanced MCP-style operation
    console.log('🗑️  Starting deletion process using enhanced MCP approach...');
    
    const deleteResult = await mcpOperations.executeSQL(
      'DELETE FROM menu_items',
      'Menu Items Deletion'
    );
    
    if (deleteResult.error) {
      console.error('❌ Error deleting menu items:', deleteResult.error);
      return;
    }

    console.log(`✅ Successfully deleted all menu items from the database`);

    // Step 5: Verify deletion using MCP-style operation
    console.log('\n🔍 Verifying deletion...');
    const verifyResult = await mcpOperations.executeSQL(
      'SELECT COUNT(*) as count FROM menu_items',
      'Deletion Verification'
    );
    
    if (verifyResult.data && verifyResult.data.length > 0) {
      const remainingCount = verifyResult.data[0].count;
      if (remainingCount === 0) {
        console.log('✅ Verification successful: All menu items have been deleted');
      } else {
        console.log(`⚠️  Warning: ${remainingCount} menu items still remain`);
      }
    }

    // Step 6: Enhanced summary and next steps
    console.log('\n📝 Enhanced Summary:');
    console.log(`   - Original count: ${menuItems.length} menu items`);
    console.log('   - Deletion method: Enhanced MCP-style operations');
    console.log('   - Database client: Admin service role');
    console.log('   - Status: Complete ✅');
    
    console.log('\n💡 What this accomplishes:');
    console.log('   ✓ Removes all foreign key constraints preventing category deletion');
    console.log('   ✓ Enables deletion of "Breakfast" and all other categories');
    console.log('   ✓ Preserves all categories, subcategories, and ingredients');
    console.log('   ✓ Uses enhanced MCP-style operations for reliability');
    console.log('   ✓ Provides comprehensive verification and error handling');
    
    console.log('\n🔄 Next steps:');
    console.log('   1. Refresh your admin dashboard (F5 or Ctrl+R)');
    console.log('   2. Navigate to the Categories tab');
    console.log('   3. Try deleting the "Breakfast" category');
    console.log('   4. The deletion should now work without any foreign key errors');
    console.log('   5. You can now manage all categories freely');

    console.log('\n🎉 Enhanced menu items removal completed successfully!');
    console.log('🔧 Using MCP-style implementation with admin service role access');

  } catch (error) {
    console.error('❌ Unexpected error during enhanced MCP operation:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('   - Check your admin-dashboard/.env.local file');
    console.log('   - Verify NEXT_PUBLIC_SUPABASE_URL is set');
    console.log('   - Verify NEXT_PUBLIC_SUPABASE_SECRET (service role key) is set');
    console.log('   - Check database permissions and table access');
    console.log('   - Ensure Supabase project is accessible');
  }
}

// Run the enhanced function if called directly
if (require.main === module) {
  removeAllMenuItemsEnhanced();
}

module.exports = { 
  removeAllMenuItemsEnhanced, 
  mcpOperations,
  adminClient 
};