'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import {
  HardDrive,
  Printer,
  CreditCard,
  Scan,
  Monitor,
  Scale,
  Wifi,
  Settings,
  TestTube,
  Play,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Save,
  Copy,
  Trash2,
  Plus,
  Edit,
  Eye,
  Download,
  Upload
} from 'lucide-react'

interface HardwareConfig {
  id: string
  terminal_id: string
  hardware_type: 'printer' | 'cash_drawer' | 'barcode_scanner' | 'display' | 'scale' | 'payment_terminal'
  hardware_config: any
  validation_status: 'valid' | 'invalid' | 'pending'
  validation_error: string | null
  sync_status: 'pending' | 'synced' | 'failed'
  sync_error: string | null
  requires_restart: boolean
  is_active: boolean
  created_at: string
  updated_at: string
  last_sync_at: string | null
  sync_attempts: number
}

interface HardwareTemplate {
  id: string
  name: string
  hardware_type: string
  description: string
  config_template: any
  is_default: boolean
}

interface TestResult {
  hardware_type: string
  terminal_id: string
  test_status: 'success' | 'failed' | 'running'
  test_results: any
  error_message?: string
  timestamp: string
}

export default function HardwareConfigurationPanel() {
  const [hardwareConfigs, setHardwareConfigs] = useState<HardwareConfig[]>([])
  const [hardwareTemplates, setHardwareTemplates] = useState<HardwareTemplate[]>([])
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [selectedTerminal, setSelectedTerminal] = useState<string>('all')
  const [selectedHardwareType, setSelectedHardwareType] = useState<string>('all')
  const [showConfigModal, setShowConfigModal] = useState(false)
  const [showTestModal, setShowTestModal] = useState(false)
  const [editingConfig, setEditingConfig] = useState<HardwareConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [testing, setTesting] = useState(false)
  const [syncing, setSyncing] = useState(false)

  // Hardware types with their configurations
  const hardwareTypes = [
    {
      id: 'printer',
      name: 'Receipt Printer',
      icon: Printer,
      description: 'Thermal receipt printers',
      configFields: [
        { key: 'type', label: 'Connection Type', type: 'select', options: ['usb', 'network', 'bluetooth'] },
        { key: 'ip', label: 'IP Address', type: 'text', condition: 'type=network' },
        { key: 'port', label: 'Port', type: 'number', condition: 'type=network' },
        { key: 'paper_size', label: 'Paper Size', type: 'select', options: ['58mm', '80mm', '112mm'] },
        { key: 'cut_type', label: 'Cut Type', type: 'select', options: ['full', 'partial', 'none'] },
        { key: 'encoding', label: 'Character Encoding', type: 'select', options: ['utf8', 'cp1252', 'cp850'] }
      ]
    },
    {
      id: 'cash_drawer',
      name: 'Cash Drawer',
      icon: CreditCard,
      description: 'Electronic cash drawers',
      configFields: [
        { key: 'type', label: 'Connection Type', type: 'select', options: ['rj11', 'usb', 'network'] },
        { key: 'open_code', label: 'Open Code', type: 'text' },
        { key: 'pulse_duration', label: 'Pulse Duration (ms)', type: 'number' },
        { key: 'auto_open', label: 'Auto Open on Sale', type: 'boolean' }
      ]
    },
    {
      id: 'barcode_scanner',
      name: 'Barcode Scanner',
      icon: Scan,
      description: 'Barcode and QR code scanners',
      configFields: [
        { key: 'type', label: 'Connection Type', type: 'select', options: ['usb', 'bluetooth', 'serial'] },
        { key: 'scan_mode', label: 'Scan Mode', type: 'select', options: ['trigger', 'continuous', 'auto'] },
        { key: 'beep_enabled', label: 'Beep on Scan', type: 'boolean' },
        { key: 'led_enabled', label: 'LED Indicator', type: 'boolean' },
        { key: 'prefix', label: 'Scan Prefix', type: 'text' },
        { key: 'suffix', label: 'Scan Suffix', type: 'text' }
      ]
    },
    {
      id: 'display',
      name: 'Customer Display',
      icon: Monitor,
      description: 'Customer-facing displays',
      configFields: [
        { key: 'type', label: 'Display Type', type: 'select', options: ['lcd', 'led', 'oled'] },
        { key: 'connection', label: 'Connection', type: 'select', options: ['usb', 'serial', 'network'] },
        { key: 'brightness', label: 'Brightness (%)', type: 'number', min: 0, max: 100 },
        { key: 'contrast', label: 'Contrast (%)', type: 'number', min: 0, max: 100 },
        { key: 'timeout', label: 'Screen Timeout (s)', type: 'number' },
        { key: 'welcome_message', label: 'Welcome Message', type: 'text' }
      ]
    },
    {
      id: 'scale',
      name: 'Digital Scale',
      icon: Scale,
      description: 'Digital weighing scales',
      configFields: [
        { key: 'type', label: 'Scale Type', type: 'select', options: ['serial', 'usb', 'network'] },
        { key: 'port', label: 'Port/Address', type: 'text' },
        { key: 'baud_rate', label: 'Baud Rate', type: 'select', options: ['9600', '19200', '38400', '57600'] },
        { key: 'unit', label: 'Weight Unit', type: 'select', options: ['kg', 'lb', 'g', 'oz'] },
        { key: 'precision', label: 'Decimal Places', type: 'number', min: 0, max: 4 },
        { key: 'auto_zero', label: 'Auto Zero', type: 'boolean' }
      ]
    },
    {
      id: 'payment_terminal',
      name: 'Payment Terminal',
      icon: CreditCard,
      description: 'Card payment terminals',
      configFields: [
        { key: 'provider', label: 'Payment Provider', type: 'select', options: ['stripe', 'square', 'sumup', 'worldpay'] },
        { key: 'terminal_id', label: 'Terminal ID', type: 'text' },
        { key: 'merchant_id', label: 'Merchant ID', type: 'text' },
        { key: 'connection', label: 'Connection Type', type: 'select', options: ['ethernet', 'wifi', 'bluetooth'] },
        { key: 'timeout', label: 'Transaction Timeout (s)', type: 'number' },
        { key: 'auto_settle', label: 'Auto Settlement', type: 'boolean' }
      ]
    }
  ]

  useEffect(() => {
    loadData()
  }, [selectedTerminal, selectedHardwareType])

  const loadData = async () => {
    try {
      setLoading(true)
      await Promise.all([
        loadHardwareConfigs(),
        loadHardwareTemplates(),
        loadTestResults()
      ])
    } catch (error) {
      console.error('Failed to load data:', error)
      toast.error('Failed to load hardware configuration data')
    } finally {
      setLoading(false)
    }
  }

  const loadHardwareConfigs = async () => {
    try {
      const params = new URLSearchParams()
      if (selectedTerminal !== 'all') params.append('terminal_id', selectedTerminal)
      if (selectedHardwareType !== 'all') params.append('hardware_type', selectedHardwareType)

      const response = await fetch(`/api/pos/hardware-configs?${params}`)
      if (response.ok) {
        const data = await response.json()
        setHardwareConfigs(data.configs || [])
      }
    } catch (error) {
      console.error('Failed to load hardware configs:', error)
    }
  }

  const loadHardwareTemplates = async () => {
    try {
      const response = await fetch('/api/pos/hardware-templates')
      if (response.ok) {
        const data = await response.json()
        setHardwareTemplates(data.templates || [])
      }
    } catch (error) {
      console.error('Failed to load hardware templates:', error)
    }
  }

  const loadTestResults = async () => {
    try {
      const response = await fetch('/api/pos/hardware-test-results')
      if (response.ok) {
        const data = await response.json()
        setTestResults(data.results || [])
      }
    } catch (error) {
      console.error('Failed to load test results:', error)
    }
  }

  const saveHardwareConfig = async (config: Partial<HardwareConfig>) => {
    try {
      const response = await fetch('/api/pos/sync-hardware', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          terminal_id: config.terminal_id,
          hardware_type: config.hardware_type,
          hardware_config: config.hardware_config,
          force_sync: false,
          version_check: true
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          toast.success('Hardware configuration saved successfully')
          await loadHardwareConfigs()
          setShowConfigModal(false)
          setEditingConfig(null)
        } else {
          toast.error(`Failed to save configuration: ${result.errors?.join(', ')}`)
        }
      } else {
        toast.error('Failed to save hardware configuration')
      }
    } catch (error) {
      console.error('Failed to save hardware config:', error)
      toast.error('Failed to save hardware configuration')
    }
  }

  const testHardware = async (terminalId: string, hardwareType: string) => {
    try {
      setTesting(true)
      const response = await fetch('/api/pos/test-hardware', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          terminal_id: terminalId,
          hardware_type: hardwareType
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          toast.success(`Hardware test completed: ${result.status}`)
          await loadTestResults()
        } else {
          toast.error(`Hardware test failed: ${result.error}`)
        }
      } else {
        toast.error('Failed to run hardware test')
      }
    } catch (error) {
      console.error('Failed to test hardware:', error)
      toast.error('Failed to test hardware')
    } finally {
      setTesting(false)
    }
  }

  const syncAllConfigs = async () => {
    try {
      setSyncing(true)
      const response = await fetch('/api/pos/sync-hardware', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'sync_all',
          terminal_id: selectedTerminal === 'all' ? null : selectedTerminal,
          force_sync: true
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          toast.success('All hardware configurations synchronized successfully')
          await loadHardwareConfigs()
        } else {
          toast.error(`Sync failed: ${result.errors?.join(', ')}`)
        }
      } else {
        toast.error('Failed to sync hardware configurations')
      }
    } catch (error) {
      console.error('Failed to sync configs:', error)
      toast.error('Failed to sync hardware configurations')
    } finally {
      setSyncing(false)
    }
  }

  const getHardwareTypeInfo = (type: string) => {
    return hardwareTypes.find(ht => ht.id === type) || hardwareTypes[0]
  }

  const getStatusIcon = (config: HardwareConfig) => {
    if (config.sync_status === 'failed' || config.validation_status === 'invalid') {
      return <XCircle className="w-5 h-5 text-red-500" />
    }
    if (config.sync_status === 'pending' || config.validation_status === 'pending') {
      return <RefreshCw className="w-5 h-5 text-yellow-500 animate-spin" />
    }
    return <CheckCircle className="w-5 h-5 text-green-500" />
  }

  const getTestStatus = (terminalId: string, hardwareType: string) => {
    const result = testResults.find(r => r.terminal_id === terminalId && r.hardware_type === hardwareType)
    return result?.test_status || 'unknown'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Hardware Configuration</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Manage hardware settings and configurations for POS terminals
          </p>
        </div>
        
        <div className="flex gap-2 mt-4 sm:mt-0">
          <button
            onClick={() => setShowTestModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
          >
            <TestTube className="w-4 h-4" />
            Test Hardware
          </button>
          
          <button
            onClick={() => {
              setEditingConfig(null)
              setShowConfigModal(true)
            }}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="w-4 h-4" />
            Add Configuration
          </button>
          
          <button
            onClick={syncAllConfigs}
            disabled={syncing}
            className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
          >
            {syncing ? (
              <div className="w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
            ) : (
              <RefreshCw className="w-4 h-4" />
            )}
            {syncing ? 'Syncing...' : 'Sync All'}
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Terminal
            </label>
            <select
              value={selectedTerminal}
              onChange={(e) => setSelectedTerminal(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Terminals</option>
              <option value="terminal-001">Terminal 001</option>
              <option value="terminal-002">Terminal 002</option>
              <option value="terminal-003">Terminal 003</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Hardware Type
            </label>
            <select
              value={selectedHardwareType}
              onChange={(e) => setSelectedHardwareType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Hardware</option>
              {hardwareTypes.map((type) => (
                <option key={type.id} value={type.id}>{type.name}</option>
              ))}
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={loadData}
              className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              <RefreshCw className="w-4 h-4 mx-auto" />
            </button>
          </div>
        </div>
      </div>

      {/* Hardware Configuration Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {hardwareConfigs.map((config) => {
          const hardwareInfo = getHardwareTypeInfo(config.hardware_type)
          const Icon = hardwareInfo.icon
          const testStatus = getTestStatus(config.terminal_id, config.hardware_type)

          return (
            <div key={config.id} className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <Icon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">{hardwareInfo.name}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{config.terminal_id}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {getStatusIcon(config)}
                  {config.requires_restart && (
                    <AlertTriangle className="w-5 h-5 text-orange-500" title="Restart required" />
                  )}
                </div>
              </div>

              <div className="space-y-3 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Validation:</span>
                  <span className={`font-medium ${
                    config.validation_status === 'valid' ? 'text-green-600' :
                    config.validation_status === 'invalid' ? 'text-red-600' :
                    'text-yellow-600'
                  }`}>
                    {config.validation_status}
                  </span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Sync Status:</span>
                  <span className={`font-medium ${
                    config.sync_status === 'synced' ? 'text-green-600' :
                    config.sync_status === 'failed' ? 'text-red-600' :
                    'text-yellow-600'
                  }`}>
                    {config.sync_status}
                  </span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Test Status:</span>
                  <span className={`font-medium ${
                    testStatus === 'success' ? 'text-green-600' :
                    testStatus === 'failed' ? 'text-red-600' :
                    testStatus === 'running' ? 'text-yellow-600' :
                    'text-gray-600'
                  }`}>
                    {testStatus === 'unknown' ? 'Not tested' : testStatus}
                  </span>
                </div>

                {config.last_sync_at && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Last Sync:</span>
                    <span className="text-gray-900 dark:text-white">
                      {new Date(config.last_sync_at).toLocaleString()}
                    </span>
                  </div>
                )}
              </div>

              {(config.validation_error || config.sync_error) && (
                <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <p className="text-sm text-red-700 dark:text-red-400">
                    {config.validation_error || config.sync_error}
                  </p>
                </div>
              )}

              <div className="flex gap-2">
                <button
                  onClick={() => testHardware(config.terminal_id, config.hardware_type)}
                  disabled={testing}
                  className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  {testing ? (
                    <div className="w-4 h-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  ) : (
                    <Play className="w-4 h-4" />
                  )}
                  Test
                </button>

                <button
                  onClick={() => {
                    setEditingConfig(config)
                    setShowConfigModal(true)
                  }}
                  className="flex items-center justify-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <Edit className="w-4 h-4" />
                </button>

                <button
                  onClick={() => {/* Copy config */}}
                  className="flex items-center justify-center px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  <Copy className="w-4 h-4" />
                </button>

                <button
                  onClick={() => {/* Delete config */}}
                  className="flex items-center justify-center px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          )
        })}
      </div>

      {hardwareConfigs.length === 0 && (
        <div className="text-center py-12">
          <HardDrive className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No hardware configurations found</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Get started by adding your first hardware configuration.
          </p>
          <button
            onClick={() => {
              setEditingConfig(null)
              setShowConfigModal(true)
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="w-4 h-4" />
            Add Hardware Configuration
          </button>
        </div>
      )}

      {/* Hardware Templates */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Hardware Templates</h3>
          <button
            onClick={() => {/* Create template */}}
            className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="w-4 h-4" />
            Create Template
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {hardwareTemplates.map((template) => {
            const hardwareInfo = getHardwareTypeInfo(template.hardware_type)
            const Icon = hardwareInfo.icon

            return (
              <div key={template.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <Icon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">{template.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{hardwareInfo.name}</p>
                  </div>
                  {template.is_default && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      Default
                    </span>
                  )}
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{template.description}</p>

                <div className="flex gap-2">
                  <button
                    onClick={() => {/* Apply template */}}
                    className="flex-1 px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                  >
                    Apply
                  </button>

                  <button
                    onClick={() => {/* View template */}}
                    className="px-3 py-2 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
