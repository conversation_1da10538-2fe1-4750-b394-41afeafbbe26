-- Branches Management System Migration
-- Creates comprehensive branch management with locations, operating hours, and configurations

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- BRANCHES TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS branches (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(200) NOT NULL,
  code VARCHAR(20) UNIQUE NOT NULL, -- unique branch code (e.g., 'BR001')
  display_name VARCHAR(250), -- public-facing name
  description TEXT,
  
  -- Location information
  address_line1 VARCHAR(500) NOT NULL,
  address_line2 VARCHAR(500),
  city VARCHAR(100) NOT NULL,
  state VARCHAR(100) NOT NULL,
  postal_code VARCHAR(20) NOT NULL,
  country VARCHAR(100) NOT NULL DEFAULT 'United States',
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  timezone VARCHAR(50) DEFAULT 'America/New_York',
  
  -- Contact information
  phone VARCHAR(30),
  email VARCHAR(255),
  website VARCHAR(500),
  
  -- Business information
  manager_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  assistant_manager_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  business_license VARCHAR(100),
  tax_id VARCHAR(50),
  
  -- Operational settings
  seating_capacity INTEGER DEFAULT 0,
  parking_spaces INTEGER DEFAULT 0,
  has_drive_through BOOLEAN DEFAULT FALSE,
  has_delivery BOOLEAN DEFAULT TRUE,
  has_pickup BOOLEAN DEFAULT TRUE,
  has_dine_in BOOLEAN DEFAULT TRUE,
  
  -- Service areas
  delivery_radius DECIMAL(5,2) DEFAULT 5.0, -- in miles/km
  delivery_fee DECIMAL(10,2) DEFAULT 0.00,
  minimum_order_amount DECIMAL(10,2) DEFAULT 0.00,
  
  -- Status and metadata
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'temporarily_closed', 'coming_soon', 'permanently_closed')),
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  opening_date DATE,
  closing_date DATE,
  
  -- System metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  
  -- Constraints
  CONSTRAINT valid_coordinates CHECK (
    (latitude IS NULL AND longitude IS NULL) OR 
    (latitude IS NOT NULL AND longitude IS NOT NULL AND 
     latitude BETWEEN -90 AND 90 AND longitude BETWEEN -180 AND 180)
  )
);-- =============================================
-- BRANCH OPERATING HOURS
-- =============================================
CREATE TABLE IF NOT EXISTS branch_operating_hours (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6), -- 0=Sunday, 6=Saturday
  is_closed BOOLEAN NOT NULL DEFAULT FALSE,
  
  -- Regular hours
  open_time TIME,
  close_time TIME,
  
  -- Special periods (lunch break, etc.)
  break_start_time TIME,
  break_end_time TIME,
  
  -- Service-specific hours
  delivery_start_time TIME,
  delivery_end_time TIME,
  pickup_start_time TIME,
  pickup_end_time TIME,
  dine_in_start_time TIME,
  dine_in_end_time TIME,
  
  -- Metadata
  notes TEXT,
  effective_from DATE DEFAULT CURRENT_DATE,
  effective_until DATE,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one active schedule per branch per day
  UNIQUE(branch_id, day_of_week, effective_from)
);

-- =============================================
-- BRANCH SPECIAL HOURS (Holidays, events, etc.)
-- =============================================
CREATE TABLE IF NOT EXISTS branch_special_hours (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
  name VARCHAR(200) NOT NULL, -- 'Christmas Day', 'Labor Day', 'Grand Opening'
  date DATE NOT NULL,
  is_closed BOOLEAN NOT NULL DEFAULT FALSE,
  
  -- Special hours if not closed
  open_time TIME,
  close_time TIME,
  delivery_start_time TIME,
  delivery_end_time TIME,
  pickup_start_time TIME,
  pickup_end_time TIME,
  
  -- Metadata
  description TEXT,
  is_recurring BOOLEAN DEFAULT FALSE, -- for annual holidays
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(branch_id, date)
);-- =============================================
-- INDEXES
-- =============================================

-- Branches indexes
CREATE INDEX IF NOT EXISTS idx_branches_code ON branches (code);
CREATE INDEX IF NOT EXISTS idx_branches_status ON branches (status);
CREATE INDEX IF NOT EXISTS idx_branches_active ON branches (is_active);
CREATE INDEX IF NOT EXISTS idx_branches_manager ON branches (manager_id);
CREATE INDEX IF NOT EXISTS idx_branches_location ON branches (city, state);
CREATE INDEX IF NOT EXISTS idx_branches_coordinates ON branches (latitude, longitude);

-- Operating hours indexes
CREATE INDEX IF NOT EXISTS idx_branch_hours_branch_day ON branch_operating_hours (branch_id, day_of_week);
CREATE INDEX IF NOT EXISTS idx_branch_hours_active ON branch_operating_hours (is_active);
CREATE INDEX IF NOT EXISTS idx_branch_hours_effective ON branch_operating_hours (effective_from, effective_until);

-- Special hours indexes
CREATE INDEX IF NOT EXISTS idx_branch_special_hours_branch ON branch_special_hours (branch_id);
CREATE INDEX IF NOT EXISTS idx_branch_special_hours_date ON branch_special_hours (date);
CREATE INDEX IF NOT EXISTS idx_branch_special_hours_recurring ON branch_special_hours (is_recurring);

-- =============================================
-- FUNCTIONS
-- =============================================

-- Function to generate unique branch code
CREATE OR REPLACE FUNCTION generate_branch_code()
RETURNS TEXT AS $$
DECLARE
  code TEXT;
  counter INTEGER := 1;
BEGIN
  LOOP
    code := 'BR' || LPAD(counter::TEXT, 3, '0');
    EXIT WHEN NOT EXISTS (SELECT 1 FROM branches WHERE code = code);
    counter := counter + 1;
  END LOOP;
  RETURN code;
END;
$$ LANGUAGE plpgsql;-- Function to check if branch is open at specific time
CREATE OR REPLACE FUNCTION is_branch_open(
  branch_uuid UUID,
  check_datetime TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS BOOLEAN AS $$
DECLARE
  branch_timezone TEXT;
  local_time TIME;
  local_date DATE;
  day_of_week INTEGER;
  is_open BOOLEAN := FALSE;
  special_hours RECORD;
  regular_hours RECORD;
BEGIN
  -- Get branch timezone
  SELECT timezone INTO branch_timezone FROM branches WHERE id = branch_uuid;
  
  -- Convert to branch local time
  local_time := (check_datetime AT TIME ZONE branch_timezone)::TIME;
  local_date := (check_datetime AT TIME ZONE branch_timezone)::DATE;
  day_of_week := EXTRACT(DOW FROM (check_datetime AT TIME ZONE branch_timezone));
  
  -- Check for special hours first
  SELECT * INTO special_hours
  FROM branch_special_hours
  WHERE branch_id = branch_uuid AND date = local_date;
  
  IF FOUND THEN
    -- Use special hours
    IF special_hours.is_closed THEN
      RETURN FALSE;
    ELSE
      RETURN (local_time BETWEEN special_hours.open_time AND special_hours.close_time);
    END IF;
  END IF;
  
  -- Check regular hours
  SELECT * INTO regular_hours
  FROM branch_operating_hours
  WHERE branch_id = branch_uuid 
    AND day_of_week = day_of_week
    AND is_active = TRUE
    AND (effective_from IS NULL OR effective_from <= local_date)
    AND (effective_until IS NULL OR effective_until >= local_date);
  
  IF FOUND AND NOT regular_hours.is_closed THEN
    RETURN (local_time BETWEEN regular_hours.open_time AND regular_hours.close_time);
  END IF;
  
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql;-- =============================================
-- TRIGGERS
-- =============================================

-- Auto-generate branch code
CREATE OR REPLACE FUNCTION trigger_generate_branch_code()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.code IS NULL OR NEW.code = '' THEN
    NEW.code := generate_branch_code();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER branch_code_generation_trigger
  BEFORE INSERT ON branches
  FOR EACH ROW EXECUTE FUNCTION trigger_generate_branch_code();

-- Update branch updated_at timestamp
CREATE OR REPLACE FUNCTION trigger_update_branch_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at := NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER branch_update_timestamp_trigger
  BEFORE UPDATE ON branches
  FOR EACH ROW EXECUTE FUNCTION trigger_update_branch_timestamp();

-- =============================================
-- ROW LEVEL SECURITY
-- =============================================

-- Enable RLS on all tables
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE branch_operating_hours ENABLE ROW LEVEL SECURITY;
ALTER TABLE branch_special_hours ENABLE ROW LEVEL SECURITY;-- Branches RLS policies
CREATE POLICY "Authenticated users can view branches" ON branches
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admin and managers can manage branches" ON branches
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles up
      JOIN roles r ON up.role_id = r.id
      WHERE up.user_id = auth.uid() AND r.name IN ('admin', 'manager')
    )
  );

-- Service role can manage all
CREATE POLICY "Service role can manage all branches" ON branches
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Similar policies for other tables
CREATE POLICY "Users can view branch hours" ON branch_operating_hours
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can view special hours" ON branch_special_hours
  FOR SELECT USING (auth.role() = 'authenticated');

-- =============================================
-- INITIAL DATA
-- =============================================

-- Insert default main branch
INSERT INTO branches (
  id, name, code, display_name, description,
  address_line1, city, state, postal_code, country,
  phone, email, 
  seating_capacity, has_delivery, has_pickup, has_dine_in,
  delivery_radius, delivery_fee, minimum_order_amount,
  status, is_active, opening_date
) VALUES (
  '11111111-1111-1111-1111-111111111111',
  'Main Branch',
  'MAIN',
  'Delicious Bites - Main Location',
  'Our flagship restaurant location with full service dining, delivery, and pickup.',
  '123 Main Street',
  'Downtown',
  'CA',
  '90210',
  'United States',
  '+****************',
  '<EMAIL>',
  50,
  true,
  true,
  true,
  5.0,
  2.99,
  15.00,
  'active',
  true,
  '2024-01-01'
) ON CONFLICT (id) DO NOTHING;-- Insert default operating hours for main branch (Monday-Sunday)
INSERT INTO branch_operating_hours (branch_id, day_of_week, is_closed, open_time, close_time, delivery_start_time, delivery_end_time, pickup_start_time, pickup_end_time, dine_in_start_time, dine_in_end_time) VALUES
  ('11111111-1111-1111-1111-111111111111', 0, false, '08:00', '22:00', '09:00', '21:30', '08:00', '22:00', '08:00', '21:00'), -- Sunday
  ('11111111-1111-1111-1111-111111111111', 1, false, '07:00', '23:00', '08:00', '22:30', '07:00', '23:00', '07:00', '22:00'), -- Monday
  ('11111111-1111-1111-1111-111111111111', 2, false, '07:00', '23:00', '08:00', '22:30', '07:00', '23:00', '07:00', '22:00'), -- Tuesday
  ('11111111-1111-1111-1111-111111111111', 3, false, '07:00', '23:00', '08:00', '22:30', '07:00', '23:00', '07:00', '22:00'), -- Wednesday
  ('11111111-1111-1111-1111-111111111111', 4, false, '07:00', '23:00', '08:00', '22:30', '07:00', '23:00', '07:00', '22:00'), -- Thursday
  ('11111111-1111-1111-1111-111111111111', 5, false, '07:00', '24:00', '08:00', '23:30', '07:00', '24:00', '07:00', '23:00'), -- Friday
  ('11111111-1111-1111-1111-111111111111', 6, false, '08:00', '24:00', '09:00', '23:30', '08:00', '24:00', '08:00', '23:00')  -- Saturday
ON CONFLICT (branch_id, day_of_week, effective_from) DO NOTHING;

-- Enable realtime for branch tables
ALTER PUBLICATION supabase_realtime ADD TABLE branches;
ALTER PUBLICATION supabase_realtime ADD TABLE branch_operating_hours;
ALTER PUBLICATION supabase_realtime ADD TABLE branch_special_hours;