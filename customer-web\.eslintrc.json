{"extends": ["next/core-web-vitals", "eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "react/react-in-jsx-scope": "off", "react/prop-types": "off", "react/no-unescaped-entities": "warn", "no-case-declarations": "warn", "react-hooks/exhaustive-deps": "warn", "@next/next/no-img-element": "warn"}}