const { createClient } = require('@supabase/supabase-js');

// Supabase configuration (using the actual values from the app)
const supabaseUrl = 'https://voiwzwyfnkzvcffuxpwl.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0MDU2MzIsImV4cCI6MjA2NDk4MTYzMn0.RvabQ6EGP1gBb0w8J8_VpaqqXgkpEFR7Nbbkmw11wNA';

const supabase = createClient(supabaseUrl, supabaseKey);

async function addSampleData() {
  console.log('Adding sample data to the database...');

  try {
    // Add sample menu categories
    console.log('Adding menu categories...');
    const { data: categories, error: categoriesError } = await supabase
      .from('menu_categories')
      .insert([
        {
          name: 'Appetizers',
          description: 'Start your meal with our delicious appetizers',
          category_type: 'standard',
          is_active: true,
          is_featured: true,
          display_order: 1
        },
        {
          name: 'Main Courses',
          description: 'Hearty and satisfying main dishes',
          category_type: 'standard',
          is_active: true,
          is_featured: true,
          display_order: 2
        },
        {
          name: 'Beverages',
          description: 'Refreshing drinks to complement your meal',
          category_type: 'standard',
          is_active: true,
          is_featured: false,
          display_order: 3
        },
        {
          name: 'Desserts',
          description: 'Sweet treats to end your meal perfectly',
          category_type: 'standard',
          is_active: true,
          is_featured: false,
          display_order: 4
        }
      ])
      .select();

    if (categoriesError) {
      console.error('Error adding categories:', categoriesError);
      return;
    }

    console.log('Categories added successfully:', categories.length);

    // Add sample ingredient categories
    console.log('Adding ingredient categories...');
    const { data: ingredientCategories, error: ingredientCategoriesError } = await supabase
      .from('ingredient_categories')
      .insert([
        {
          name: 'Proteins',
          description: 'Meat, fish, and protein sources',
          color_code: '#EF4444',
          is_active: true,
          display_order: 1
        },
        {
          name: 'Vegetables',
          description: 'Fresh vegetables and greens',
          color_code: '#10B981',
          is_active: true,
          display_order: 2
        },
        {
          name: 'Dairy',
          description: 'Milk, cheese, and dairy products',
          color_code: '#F59E0B',
          is_active: true,
          display_order: 3
        },
        {
          name: 'Spices & Herbs',
          description: 'Seasonings and flavor enhancers',
          color_code: '#8B5CF6',
          is_active: true,
          display_order: 4
        }
      ])
      .select();

    if (ingredientCategoriesError) {
      console.error('Error adding ingredient categories:', ingredientCategoriesError);
      return;
    }

    console.log('Ingredient categories added successfully:', ingredientCategories.length);

    // Add sample ingredients
    console.log('Adding ingredients...');
    const { data: ingredients, error: ingredientsError } = await supabase
      .from('ingredients')
      .insert([
        {
          name: 'Chicken Breast',
          description: 'Fresh chicken breast',
          category_id: ingredientCategories.find(c => c.name === 'Proteins').id,
          price: 3.50,
          cost: 2.25,
          stock_quantity: 50,
          min_stock_level: 10,
          allergens: [],
          is_available: true,
          display_order: 1
        },
        {
          name: 'Tomatoes',
          description: 'Fresh ripe tomatoes',
          category_id: ingredientCategories.find(c => c.name === 'Vegetables').id,
          price: 1.25,
          cost: 0.75,
          stock_quantity: 100,
          min_stock_level: 20,
          allergens: [],
          is_available: true,
          display_order: 2
        },
        {
          name: 'Mozzarella Cheese',
          description: 'Fresh mozzarella cheese',
          category_id: ingredientCategories.find(c => c.name === 'Dairy').id,
          price: 2.50,
          cost: 1.50,
          stock_quantity: 30,
          min_stock_level: 5,
          allergens: ['dairy'],
          is_available: true,
          display_order: 3
        },
        {
          name: 'Basil',
          description: 'Fresh basil leaves',
          category_id: ingredientCategories.find(c => c.name === 'Spices & Herbs').id,
          price: 0.75,
          cost: 0.25,
          stock_quantity: 25,
          min_stock_level: 5,
          allergens: [],
          is_available: true,
          display_order: 4
        }
      ])
      .select();

    if (ingredientsError) {
      console.error('Error adding ingredients:', ingredientsError);
      return;
    }

    console.log('Ingredients added successfully:', ingredients.length);

    // Add sample menu items
    console.log('Adding menu items...');
    const { data: menuItems, error: menuItemsError } = await supabase
      .from('subcategories')
      .insert([
        {
          name: 'Chicken Caesar Salad',
          description: 'Grilled chicken breast with fresh romaine lettuce, parmesan cheese, and caesar dressing',
          category_id: categories.find(c => c.name === 'Main Courses').id,
          base_price: 12.99,
          cost: 4.50,
          preparation_time: 15,
          calories: 450,
          allergens: ['dairy', 'gluten'],
          is_available: true,
          is_featured: true,
          is_customizable: false,
          display_order: 1
        },
        {
          name: 'Margherita Pizza',
          description: 'Classic pizza with fresh tomatoes, mozzarella cheese, and basil',
          category_id: categories.find(c => c.name === 'Main Courses').id,
          base_price: 14.99,
          cost: 5.25,
          preparation_time: 20,
          calories: 650,
          allergens: ['dairy', 'gluten'],
          is_available: true,
          is_featured: true,
          is_customizable: true,
          max_ingredients: 8,
          display_order: 2
        },
        {
          name: 'Bruschetta',
          description: 'Toasted bread topped with fresh tomatoes, basil, and garlic',
          category_id: categories.find(c => c.name === 'Appetizers').id,
          base_price: 8.99,
          cost: 2.75,
          preparation_time: 10,
          calories: 220,
          allergens: ['gluten'],
          is_available: true,
          is_featured: false,
          is_customizable: false,
          display_order: 1
        },
        {
          name: 'Fresh Orange Juice',
          description: 'Freshly squeezed orange juice',
          category_id: categories.find(c => c.name === 'Beverages').id,
          base_price: 4.99,
          cost: 1.25,
          preparation_time: 5,
          calories: 110,
          allergens: [],
          is_available: true,
          is_featured: false,
          is_customizable: false,
          display_order: 1
        },
        {
          name: 'Chocolate Cake',
          description: 'Rich chocolate cake with chocolate frosting',
          category_id: categories.find(c => c.name === 'Desserts').id,
          base_price: 6.99,
          cost: 2.25,
          preparation_time: 5,
          calories: 420,
          allergens: ['dairy', 'gluten', 'eggs'],
          is_available: true,
          is_featured: true,
          is_customizable: false,
          display_order: 1
        }
      ])
      .select();

    if (menuItemsError) {
      console.error('Error adding menu items:', menuItemsError);
      return;
    }

    console.log('Menu items added successfully:', menuItems.length);

    // Add sample menu item ingredients relationships
    console.log('Adding menu item ingredients...');
    const { data: menuItemIngredients, error: menuItemIngredientsError } = await supabase
      .from('menu_item_ingredients')
      .insert([
        {
          menu_item_id: menuItems.find(m => m.name === 'Chicken Caesar Salad').id,
          ingredient_id: ingredients.find(i => i.name === 'Chicken Breast').id,
          quantity: 150.00,
          is_default: true,
          is_optional: false,
          additional_price: 0.00
        },
        {
          menu_item_id: menuItems.find(m => m.name === 'Margherita Pizza').id,
          ingredient_id: ingredients.find(i => i.name === 'Tomatoes').id,
          quantity: 100.00,
          is_default: true,
          is_optional: false,
          additional_price: 0.00
        },
        {
          menu_item_id: menuItems.find(m => m.name === 'Margherita Pizza').id,
          ingredient_id: ingredients.find(i => i.name === 'Mozzarella Cheese').id,
          quantity: 80.00,
          is_default: true,
          is_optional: false,
          additional_price: 0.00
        },
        {
          menu_item_id: menuItems.find(m => m.name === 'Margherita Pizza').id,
          ingredient_id: ingredients.find(i => i.name === 'Basil').id,
          quantity: 5.00,
          is_default: true,
          is_optional: true,
          additional_price: 0.50
        },
        {
          menu_item_id: menuItems.find(m => m.name === 'Bruschetta').id,
          ingredient_id: ingredients.find(i => i.name === 'Tomatoes').id,
          quantity: 75.00,
          is_default: true,
          is_optional: false,
          additional_price: 0.00
        },
        {
          menu_item_id: menuItems.find(m => m.name === 'Bruschetta').id,
          ingredient_id: ingredients.find(i => i.name === 'Basil').id,
          quantity: 3.00,
          is_default: true,
          is_optional: false,
          additional_price: 0.00
        }
      ])
      .select();

    if (menuItemIngredientsError) {
      console.error('Error adding menu item ingredients:', menuItemIngredientsError);
      return;
    }

    console.log('Menu item ingredients added successfully:', menuItemIngredients.length);

    console.log('\n✅ Sample data added successfully!');
    console.log('You can now refresh the admin dashboard to see the sample data.');

  } catch (error) {
    console.error('Error adding sample data:', error);
  }
}

// Run the script
addSampleData();