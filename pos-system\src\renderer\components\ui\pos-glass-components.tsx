'use client'

import React from 'react'
import { cn } from '../../utils/cn'

// Import the glassmorphism CSS
import '../../styles/glassmorphism.css'

// Note: cn utility function is imported above

// POS Glass Card Component - Optimized for touch
interface POSGlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'pending' | 'preparing' | 'ready'
  size?: 'compact' | 'default' | 'large'
  className?: string
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void
  isSelected?: boolean
  isLoading?: boolean
}

export const POSGlassCard = React.forwardRef<HTMLDivElement, POSGlassCardProps>(
  ({ 
    children, 
    variant = 'primary', 
    size = 'default',
    className = '', 
    onClick, 
    isSelected = false,
    isLoading = false,
    ...props 
  }, ref) => {
    const baseClasses = size === 'compact' ? 'pos-glass-card-compact' : 'pos-glass-card'
    const variantClass = variant !== 'primary' ? `pos-glass-${variant}` : 'pos-glass-primary'
    const interactiveClass = onClick ? 'pos-glass-interactive' : ''
    const selectedClass = isSelected ? 'ring-2 ring-blue-400 ring-opacity-50' : ''
    const loadingClass = isLoading ? 'animate-pulse' : ''
    const sizeClasses = {
      compact: 'p-3',
      default: 'p-4',
      large: 'p-6'
    }

    const combinedClasses = cn(
      baseClasses, 
      variantClass, 
      interactiveClass,
      selectedClass,
      loadingClass,
      sizeClasses[size],
      className
    )

    return (
      <div
        ref={ref}
        className={combinedClasses}
        onClick={onClick}
        role={onClick ? 'button' : undefined}
        tabIndex={onClick ? 0 : undefined}
        onKeyDown={
          onClick
            ? e => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault()
                  onClick(e as unknown as React.MouseEvent<HTMLDivElement>)
                }
              }
            : undefined
        }
        {...props}
      >
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-20 rounded-inherit">
            <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
        {children}
      </div>
    )
  }
)

POSGlassCard.displayName = 'POSGlassCard'

// POS Glass Button Component - Touch-optimized
interface POSGlassButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
  size?: 'default' | 'large' | 'xl'
  loading?: boolean
  className?: string
  icon?: React.ReactNode
  fullWidth?: boolean
}

export const POSGlassButton = React.forwardRef<HTMLButtonElement, POSGlassButtonProps>(
  (
    {
      children,
      variant = 'primary',
      size = 'default',
      disabled = false,
      loading = false,
      className = '',
      icon,
      fullWidth = false,
      ...props
    },
    ref
  ) => {
    const baseClasses = 'pos-glass-button'
    const variantClass = variant !== 'primary' ? `pos-glass-${variant}` : ''
    const sizeClasses = {
      default: 'pos-glass-button',
      large: 'pos-glass-button-large',
      xl: 'pos-glass-button-xl'
    }
    const widthClass = fullWidth ? 'w-full' : ''

    const combinedClasses = cn(
      sizeClasses[size], 
      variantClass, 
      widthClass,
      className
    )

    return (
      <button
        ref={ref}
        className={combinedClasses}
        disabled={disabled || loading}
        aria-disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
        )}
        {!loading && icon && <span className="flex-shrink-0">{icon}</span>}
        <span className={cn("flex-1", (icon && !loading) ? "ml-2" : undefined)}>{children}</span>
      </button>
    )
  }
)

POSGlassButton.displayName = 'POSGlassButton'

// POS Glass Input Component - Touch-friendly
interface POSGlassInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string
  label?: string
  error?: string
  icon?: React.ReactNode
}

export const POSGlassInput = React.forwardRef<HTMLInputElement, POSGlassInputProps>(
  ({ className = '', label, error, icon, ...props }, ref) => {
    return (
      <div className="space-y-2">
        {label && (
          <label className="block text-sm font-medium pos-glass-text-primary">
            {label}
          </label>
        )}
        <div className="relative">
          {icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pos-glass-text-muted">
              {icon}
            </div>
          )}
          <input 
            ref={ref} 
                       className={cn(
               'pos-glass-input', 
               icon ? 'pl-10' : undefined,
               error ? 'border-red-500 focus:border-red-500' : undefined,
               className
             )} 
            {...props} 
          />
        </div>
        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}
      </div>
    )
  }
)

POSGlassInput.displayName = 'POSGlassInput'

// POS Glass PIN Input Component - Specialized for PIN entry
interface POSGlassPINInputProps {
  value: string
  onChange: (value: string) => void
  length?: number
  className?: string
  disabled?: boolean
}

export const POSGlassPINInput: React.FC<POSGlassPINInputProps> = ({
  value,
  onChange,
  length = 4,
  className = '',
  disabled = false
}) => {
  const inputRefs = React.useRef<(HTMLInputElement | null)[]>([])

  const handleChange = (index: number, inputValue: string) => {
    if (inputValue.length > 1) return
    
    const newValue = value.split('')
    newValue[index] = inputValue
    
    const finalValue = newValue.join('').slice(0, length)
    onChange(finalValue)
    
    // Move to next input
    if (inputValue && index < length - 1) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !value[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  return (
    <div className={cn("flex gap-3 justify-center", className)}>
      {Array.from({ length }, (_, index) => (
        <input
          key={index}
          ref={el => { inputRefs.current[index] = el }}
          type="tel"
          maxLength={1}
          value={value[index] || ''}
          onChange={e => handleChange(index, e.target.value)}
          onKeyDown={e => handleKeyDown(index, e)}
          className="pos-glass-pin-input text-center"
          disabled={disabled}
          inputMode="numeric"
          pattern="[0-9]*"
        />
      ))}
    </div>
  )
}

// POS Glass Modal Component - Touch-optimized
interface POSGlassModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  children: React.ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closeOnBackdrop?: boolean
}

export const POSGlassModal: React.FC<POSGlassModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  className = '',
  size = 'md',
  closeOnBackdrop = true
}) => {
  // Handle escape key
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-[95vw] max-h-[95vh]'
  }

  return (
    <>
      <div 
        className="pos-glass-modal-backdrop" 
        onClick={closeOnBackdrop ? onClose : undefined} 
        aria-hidden="true" 
      />

      <div
        className={cn('pos-glass-modal', sizeClasses[size], className)}
        role="dialog"
        aria-modal="true"
        aria-labelledby={title ? 'modal-title' : undefined}
      >
        {title && (
          <div className="flex items-center justify-between mb-6">
            <h2 id="modal-title" className="text-2xl font-bold pos-glass-text-primary">
              {title}
            </h2>
            <button
              onClick={onClose}
              className="pos-glass-button p-2 min-h-0 min-w-0"
              aria-label="Close modal"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        )}
        {children}
      </div>
    </>
  )
}

// POS Glass Container - Generic container with glass effect
interface POSGlassContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: 'primary' | 'secondary'
  className?: string
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

export const POSGlassContainer = React.forwardRef<HTMLDivElement, POSGlassContainerProps>(
  ({ children, variant = 'primary', className = '', padding = 'md', ...props }, ref) => {
    const baseClasses = 'pos-glass-container'
    const variantClass = `pos-glass-${variant}`
    const paddingClasses = {
      none: '',
      sm: 'p-3',
      md: 'p-4',
      lg: 'p-6'
    }

    const combinedClasses = cn(
      baseClasses,
      variantClass,
      paddingClasses[padding],
      className
    )

    return (
      <div ref={ref} className={combinedClasses} {...props}>
        {children}
      </div>
    )
  }
)

POSGlassContainer.displayName = 'POSGlassContainer'

// POS Glass Toggle Switch - For settings and options
interface POSGlassToggleProps {
  checked: boolean
  onChange: (checked: boolean) => void
  label?: string
  description?: string
  disabled?: boolean
  className?: string
}

export const POSGlassToggle: React.FC<POSGlassToggleProps> = ({
  checked,
  onChange,
  label,
  description,
  disabled = false,
  className = ''
}) => {
  return (
    <div className={cn("flex items-center justify-between", className)}>
      <div className="flex-1">
        {label && (
          <div className="font-medium pos-glass-text-primary">{label}</div>
        )}
        {description && (
          <div className="text-sm pos-glass-text-secondary">{description}</div>
        )}
      </div>
      <button
        type="button"
        role="switch"
        aria-checked={checked}
        disabled={disabled}
        onClick={() => onChange(!checked)}
        className={cn(
          "relative inline-flex h-8 w-14 items-center rounded-full transition-colors",
          "pos-glass-interactive min-h-0 min-w-0",
          checked ? "pos-glass-success" : "pos-glass-secondary",
          disabled && "opacity-50 cursor-not-allowed"
        )}
      >
        <span
          className={cn(
            "inline-block h-6 w-6 transform rounded-full bg-white shadow-lg transition-transform",
            checked ? "translate-x-7" : "translate-x-1"
          )}
        />
      </button>
    </div>
  )
}

// POS Glass Badge - For status indicators
interface POSGlassBadgeProps {
  children: React.ReactNode
  variant?: 'success' | 'warning' | 'error' | 'info' | 'pending' | 'preparing' | 'ready'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export const POSGlassBadge: React.FC<POSGlassBadgeProps> = ({
  children,
  variant = 'info',
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1.5',
    lg: 'text-base px-4 py-2'
  }

  return (
    <span
      className={cn(
        'pos-glass-container',
        `pos-glass-${variant}`,
        'rounded-full font-medium inline-flex items-center',
        sizeClasses[size],
        className
      )}
    >
      {children}
    </span>
  )
}

// POS Glass Number Input - For quantities and prices
interface POSGlassNumberInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string
  onIncrement?: () => void
  onDecrement?: () => void
  min?: number
  max?: number
  step?: number
  className?: string
}

export const POSGlassNumberInput = React.forwardRef<HTMLInputElement, POSGlassNumberInputProps>(
  ({ 
    label, 
    onIncrement, 
    onDecrement, 
    min = 0, 
    max, 
    step = 1, 
    className = '',
    value,
    onChange,
    ...props 
  }, ref) => {
    const numValue = typeof value === 'string' ? parseInt(value) || 0 : (value as number) || 0

    const handleIncrement = () => {
      if (onIncrement) {
        onIncrement()
      } else if (onChange) {
        const newValue = Math.min(numValue + step, max || Infinity)
        onChange({ target: { value: newValue.toString() } } as React.ChangeEvent<HTMLInputElement>)
      }
    }

    const handleDecrement = () => {
      if (onDecrement) {
        onDecrement()
      } else if (onChange) {
        const newValue = Math.max(numValue - step, min)
        onChange({ target: { value: newValue.toString() } } as React.ChangeEvent<HTMLInputElement>)
      }
    }

    return (
      <div className={cn("space-y-2", className)}>
        {label && (
          <label className="block text-sm font-medium pos-glass-text-primary">
            {label}
          </label>
        )}
        <div className="flex items-center">
          <button
            type="button"
            onClick={handleDecrement}
            disabled={numValue <= min}
            className="pos-glass-button p-3 min-h-0 min-w-0 rounded-r-none border-r-0"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>
          <input
            ref={ref}
            type="number"
            min={min}
            max={max}
            step={step}
            value={value}
            onChange={onChange}
            className="pos-glass-input rounded-none border-x-0 text-center flex-1 min-w-0"
            {...props}
          />
          <button
            type="button"
            onClick={handleIncrement}
            disabled={max !== undefined && numValue >= max}
            className="pos-glass-button p-3 min-h-0 min-w-0 rounded-l-none border-l-0"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>
        </div>
      </div>
    )
  }
)

POSGlassNumberInput.displayName = 'POSGlassNumberInput' 