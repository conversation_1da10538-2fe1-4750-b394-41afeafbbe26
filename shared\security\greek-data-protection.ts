/**
 * Greek Data Protection Compliance Module
 * Implements Greek Data Protection Authority (DPA) requirements
 * and GDPR compliance specific to Greek jurisdiction
 */

import { createClient } from '@supabase/supabase-js';
import { EncryptionManager } from './data-protection';
import { AuditLogger } from './audit-logging';
import { SECURITY_CONFIG } from './security-config';
import crypto from 'crypto';

// Types
interface GreekDPARequest {
  id: string;
  requestType: 'access' | 'rectification' | 'erasure' | 'portability' | 'restriction' | 'objection';
  dataSubjectId: string;
  requestorEmail: string;
  requestorName: string;
  identityVerified: boolean;
  requestDetails: string;
  legalBasis?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'rejected' | 'partially_fulfilled';
  submittedAt: Date;
  responseDeadline: Date;
  completedAt?: Date;
  responseData?: any;
  rejectionReason?: string;
}

interface ConsentRecord {
  id: string;
  dataSubjectId: string;
  consentType: string;
  purpose: string;
  legalBasis: string;
  consentGiven: boolean;
  consentDate: Date;
  withdrawalDate?: Date;
  consentMethod: 'explicit' | 'implicit' | 'opt_in' | 'pre_ticked';
  processingCategories: string[];
  dataRetentionPeriod: number; // in days
  thirdPartySharing: boolean;
  thirdParties?: string[];
}

interface DataProcessingActivity {
  id: string;
  activityName: string;
  controller: string;
  processor?: string;
  dataCategories: string[];
  dataSubjectCategories: string[];
  purposes: string[];
  legalBasis: string;
  recipients: string[];
  internationalTransfers: boolean;
  transferSafeguards?: string;
  retentionPeriod: number;
  securityMeasures: string[];
  dataProtectionImpactAssessment: boolean;
  lastReviewed: Date;
}

interface DataBreach {
  id: string;
  breachType: 'confidentiality' | 'integrity' | 'availability';
  severity: 'low' | 'medium' | 'high' | 'critical';
  affectedDataCategories: string[];
  affectedDataSubjects: number;
  breachDescription: string;
  causeOfBreach: string;
  discoveryDate: Date;
  reportedToAuthority: boolean;
  authorityReportDate?: Date;
  dataSubjectsNotified: boolean;
  notificationDate?: Date;
  mitigationMeasures: string[];
  status: 'discovered' | 'investigating' | 'contained' | 'resolved';
}

interface PrivacyImpactAssessment {
  id: string;
  projectName: string;
  dataController: string;
  assessmentDate: Date;
  dataProcessingDescription: string;
  dataCategories: string[];
  dataSubjectCategories: string[];
  purposes: string[];
  legalBasis: string;
  riskLevel: 'low' | 'medium' | 'high';
  identifiedRisks: string[];
  mitigationMeasures: string[];
  residualRisk: 'low' | 'medium' | 'high';
  consultationRequired: boolean;
  approvalStatus: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
  approvalDate?: Date;
}

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * Greek DPA Request Handler
 * Handles data subject rights requests according to Greek DPA guidelines
 */
export class GreekDPARequestHandler {
  /**
   * Submits a new data protection request
   */
  static async submitRequest(
    requestType: GreekDPARequest['requestType'],
    dataSubjectId: string,
    requestorEmail: string,
    requestorName: string,
    requestDetails: string,
    legalBasis?: string
  ): Promise<string> {
    const requestId = crypto.randomUUID();
    const submittedAt = new Date();
    
    // Calculate response deadline (30 days as per Greek DPA)
    const responseDeadline = new Date(submittedAt);
    responseDeadline.setDate(responseDeadline.getDate() + 30);

    const request: GreekDPARequest = {
      id: requestId,
      requestType,
      dataSubjectId,
      requestorEmail,
      requestorName,
      identityVerified: false,
      requestDetails,
      legalBasis,
      status: 'pending',
      submittedAt,
      responseDeadline
    };

    // Store request
    await supabase
      .from('gdpr_requests')
      .insert({
        id: requestId,
        request_type: requestType,
        data_subject_id: dataSubjectId,
        requestor_email: requestorEmail,
        requestor_name: requestorName,
        identity_verified: false,
        request_details: requestDetails,
        legal_basis: legalBasis,
        status: 'pending',
        submitted_at: submittedAt,
        response_deadline: responseDeadline
      });

    // Log the request
    await AuditLogger.logEvent({
      action: 'gdpr_request_submitted',
      resource: 'data_protection',
      resourceId: requestId,
      userId: dataSubjectId,
      metadata: {
        requestType,
        requestorEmail,
        greekDPACompliance: true
      },
      category: 'data_change',
      severity: 'medium'
    });

    // Send confirmation email
    await this.sendRequestConfirmation(requestorEmail, requestId, responseDeadline);

    return requestId;
  }

  /**
   * Processes data access request (Article 15 GDPR)
   */
  static async processAccessRequest(requestId: string): Promise<any> {
    const request = await this.getRequest(requestId);
    if (!request || request.requestType !== 'access') {
      throw new Error('Invalid access request');
    }

    // Verify identity before processing
    if (!request.identityVerified) {
      throw new Error('Identity verification required');
    }

    // Collect all personal data
    const personalData = await this.collectPersonalData(request.dataSubjectId);
    
    // Prepare response according to Greek DPA format
    const responseData = {
      dataSubject: {
        id: request.dataSubjectId,
        name: request.requestorName,
        email: request.requestorEmail
      },
      dataCategories: personalData.categories,
      processingPurposes: personalData.purposes,
      legalBasis: personalData.legalBasis,
      dataRetentionPeriods: personalData.retentionPeriods,
      recipients: personalData.recipients,
      internationalTransfers: personalData.internationalTransfers,
      dataSource: personalData.sources,
      automatedDecisionMaking: personalData.automatedDecisions,
      dataSubjectRights: this.getDataSubjectRights(),
      contactDetails: {
        dataController: 'Creperie Management System',
        dpoContact: process.env.DPO_EMAIL || '<EMAIL>',
        greekDPAContact: 'https://www.dpa.gr/'
      },
      responseDate: new Date(),
      responseLanguage: 'el' // Greek language as per local requirements
    };

    // Update request status
    await this.updateRequestStatus(requestId, 'completed', responseData);

    return responseData;
  }

  /**
   * Processes data erasure request (Article 17 GDPR - Right to be forgotten)
   */
  static async processErasureRequest(
    requestId: string,
    verifyLegalGrounds: boolean = true
  ): Promise<{ erased: boolean; reason?: string }> {
    const request = await this.getRequest(requestId);
    if (!request || request.requestType !== 'erasure') {
      throw new Error('Invalid erasure request');
    }

    if (verifyLegalGrounds) {
      const canErase = await this.verifyErasureGrounds(request.dataSubjectId);
      if (!canErase.allowed) {
        await this.updateRequestStatus(requestId, 'rejected', null, canErase.reason);
        return { erased: false, reason: canErase.reason };
      }
    }

    // Perform data erasure
    const erasureResult = await this.performDataErasure(request.dataSubjectId);
    
    // Update request status
    await this.updateRequestStatus(requestId, 'completed', {
      erasedData: erasureResult.erasedCategories,
      retainedData: erasureResult.retainedCategories,
      retentionReasons: erasureResult.retentionReasons
    });

    // Log erasure for compliance
    await AuditLogger.logEvent({
      action: 'data_erasure_completed',
      resource: 'personal_data',
      resourceId: request.dataSubjectId,
      userId: request.dataSubjectId,
      metadata: {
        requestId,
        erasedCategories: erasureResult.erasedCategories,
        retainedCategories: erasureResult.retainedCategories,
        greekDPACompliance: true
      },
      category: 'data_change',
      severity: 'high'
    });

    return { erased: true };
  }

  /**
   * Processes data portability request (Article 20 GDPR)
   */
  static async processPortabilityRequest(requestId: string): Promise<any> {
    const request = await this.getRequest(requestId);
    if (!request || request.requestType !== 'portability') {
      throw new Error('Invalid portability request');
    }

    // Get portable data (only data provided by data subject)
    const portableData = await this.getPortableData(request.dataSubjectId);
    
    // Format data in machine-readable format (JSON)
    const exportData = {
      dataSubject: request.dataSubjectId,
      exportDate: new Date(),
      dataFormat: 'JSON',
      data: portableData,
      metadata: {
        exportedBy: 'Creperie Management System',
        greekDPACompliant: true,
        dataPortabilityRights: 'Article 20 GDPR'
      }
    };

    await this.updateRequestStatus(requestId, 'completed', exportData);
    
    return exportData;
  }

  /**
   * Verifies identity for data protection requests
   */
  static async verifyIdentity(
    requestId: string,
    verificationMethod: 'email' | 'document' | 'two_factor',
    verificationData: any
  ): Promise<boolean> {
    const request = await this.getRequest(requestId);
    if (!request) {
      throw new Error('Request not found');
    }

    let verified = false;

    switch (verificationMethod) {
      case 'email':
        verified = await this.verifyEmailIdentity(request, verificationData);
        break;
      case 'document':
        verified = await this.verifyDocumentIdentity(request, verificationData);
        break;
      case 'two_factor':
        verified = await this.verifyTwoFactorIdentity(request, verificationData);
        break;
    }

    if (verified) {
      await supabase
        .from('gdpr_requests')
        .update({ identity_verified: true })
        .eq('id', requestId);

      await AuditLogger.logEvent({
        action: 'identity_verified',
        resource: 'gdpr_request',
        resourceId: requestId,
        metadata: {
          verificationMethod,
          greekDPACompliance: true
        },
        category: 'security_event',
        severity: 'medium'
      });
    }

    return verified;
  }

  private static async getRequest(requestId: string): Promise<GreekDPARequest | null> {
    const { data, error } = await supabase
      .from('gdpr_requests')
      .select('*')
      .eq('id', requestId)
      .single();

    if (error || !data) {
      return null;
    }

    return {
      id: data.id,
      requestType: data.request_type,
      dataSubjectId: data.data_subject_id,
      requestorEmail: data.requestor_email,
      requestorName: data.requestor_name,
      identityVerified: data.identity_verified,
      requestDetails: data.request_details,
      legalBasis: data.legal_basis,
      status: data.status,
      submittedAt: new Date(data.submitted_at),
      responseDeadline: new Date(data.response_deadline),
      completedAt: data.completed_at ? new Date(data.completed_at) : undefined,
      responseData: data.response_data,
      rejectionReason: data.rejection_reason
    };
  }

  private static async collectPersonalData(dataSubjectId: string): Promise<any> {
    // Collect data from all relevant tables
    const [profile, orders, payments, preferences] = await Promise.all([
      supabase.from('user_profiles').select('*').eq('id', dataSubjectId).single(),
      supabase.from('orders').select('*').eq('customer_id', dataSubjectId),
      supabase.from('payments').select('*').eq('customer_id', dataSubjectId),
      supabase.from('customer_preferences').select('*').eq('customer_id', dataSubjectId)
    ]);

    return {
      categories: ['identity', 'contact', 'financial', 'behavioral', 'preferences'],
      purposes: ['service_provision', 'order_processing', 'payment_processing', 'marketing'],
      legalBasis: ['contract', 'legitimate_interest', 'consent'],
      retentionPeriods: {
        profile: '7 years after account closure',
        orders: '10 years for tax purposes',
        payments: '7 years for financial records',
        preferences: 'Until consent withdrawal'
      },
      recipients: ['payment_processors', 'delivery_partners', 'analytics_providers'],
      internationalTransfers: false,
      sources: ['data_subject', 'public_sources', 'third_parties'],
      automatedDecisions: ['fraud_detection', 'recommendation_system'],
      data: {
        profile: profile.data,
        orders: orders.data,
        payments: payments.data?.map(p => ({ ...p, card_number: '****' })), // Mask sensitive data
        preferences: preferences.data
      }
    };
  }

  private static async verifyErasureGrounds(dataSubjectId: string): Promise<{ allowed: boolean; reason?: string }> {
    // Check if there are legal obligations to retain data
    const { data: orders } = await supabase
      .from('orders')
      .select('created_at, status')
      .eq('customer_id', dataSubjectId)
      .gte('created_at', new Date(Date.now() - 10 * 365 * 24 * 60 * 60 * 1000).toISOString()); // 10 years

    if (orders && orders.length > 0) {
      return {
        allowed: false,
        reason: 'Data retention required for tax and accounting purposes (10 years as per Greek law)'
      };
    }

    // Check for ongoing legal proceedings
    const { data: legalHolds } = await supabase
      .from('legal_holds')
      .select('*')
      .eq('data_subject_id', dataSubjectId)
      .eq('status', 'active');

    if (legalHolds && legalHolds.length > 0) {
      return {
        allowed: false,
        reason: 'Data subject to legal hold - cannot be erased'
      };
    }

    return { allowed: true };
  }

  private static async performDataErasure(dataSubjectId: string): Promise<any> {
    const erasedCategories: string[] = [];
    const retainedCategories: string[] = [];
    const retentionReasons: Record<string, string> = {};

    try {
      // Erase profile data (except what's required for legal compliance)
      await supabase
        .from('user_profiles')
        .update({
          email: '<EMAIL>',
          first_name: '[ERASED]',
          last_name: '[ERASED]',
          phone: '[ERASED]',
          date_of_birth: null,
          preferences: null
        })
        .eq('id', dataSubjectId);
      erasedCategories.push('profile_data');

      // Erase preferences
      await supabase
        .from('customer_preferences')
        .delete()
        .eq('customer_id', dataSubjectId);
      erasedCategories.push('preferences');

      // Retain orders for legal compliance but anonymize
      await supabase
        .from('orders')
        .update({
          customer_name: '[ANONYMIZED]',
          delivery_address: '[ANONYMIZED]',
          phone: '[ANONYMIZED]'
        })
        .eq('customer_id', dataSubjectId);
      retainedCategories.push('order_data');
      retentionReasons['order_data'] = 'Required for tax and accounting compliance (10 years)';

      // Retain payment records but anonymize
      await supabase
        .from('payments')
        .update({
          customer_name: '[ANONYMIZED]'
        })
        .eq('customer_id', dataSubjectId);
      retainedCategories.push('payment_data');
      retentionReasons['payment_data'] = 'Required for financial compliance (7 years)';

    } catch (error) {
      console.error('Error during data erasure:', error);
      throw error;
    }

    return {
      erasedCategories,
      retainedCategories,
      retentionReasons
    };
  }

  private static async getPortableData(dataSubjectId: string): Promise<any> {
    // Only include data provided by the data subject
    const [profile, preferences, reviews] = await Promise.all([
      supabase.from('user_profiles').select('email, first_name, last_name, phone, date_of_birth').eq('id', dataSubjectId).single(),
      supabase.from('customer_preferences').select('*').eq('customer_id', dataSubjectId),
      supabase.from('reviews').select('*').eq('customer_id', dataSubjectId)
    ]);

    return {
      personalInfo: profile.data,
      preferences: preferences.data,
      reviews: reviews.data
    };
  }

  private static getDataSubjectRights(): string[] {
    return [
      'Right to access (Article 15 GDPR)',
      'Right to rectification (Article 16 GDPR)',
      'Right to erasure (Article 17 GDPR)',
      'Right to restrict processing (Article 18 GDPR)',
      'Right to data portability (Article 20 GDPR)',
      'Right to object (Article 21 GDPR)',
      'Right to withdraw consent (Article 7 GDPR)',
      'Right to lodge complaint with Greek DPA'
    ];
  }

  private static async updateRequestStatus(
    requestId: string,
    status: GreekDPARequest['status'],
    responseData?: any,
    rejectionReason?: string
  ): Promise<void> {
    const updateData: any = {
      status,
      completed_at: status === 'completed' ? new Date() : null,
      response_data: responseData,
      rejection_reason: rejectionReason
    };

    await supabase
      .from('gdpr_requests')
      .update(updateData)
      .eq('id', requestId);
  }

  private static async sendRequestConfirmation(
    email: string,
    requestId: string,
    deadline: Date
  ): Promise<void> {
    // Implementation would integrate with email service
    console.log(`Sending GDPR request confirmation to ${email} for request ${requestId}`);
  }

  private static async verifyEmailIdentity(request: GreekDPARequest, verificationData: any): Promise<boolean> {
    // Implement email verification logic
    return true;
  }

  private static async verifyDocumentIdentity(request: GreekDPARequest, verificationData: any): Promise<boolean> {
    // Implement document verification logic
    return true;
  }

  private static async verifyTwoFactorIdentity(request: GreekDPARequest, verificationData: any): Promise<boolean> {
    // Implement 2FA verification logic
    return true;
  }
}

/**
 * Greek Consent Management
 * Manages consent according to Greek DPA and GDPR requirements
 */
export class GreekConsentManager {
  /**
   * Records consent with Greek DPA requirements
   */
  static async recordConsent(
    dataSubjectId: string,
    consentType: string,
    purpose: string,
    legalBasis: string,
    consentMethod: ConsentRecord['consentMethod'],
    processingCategories: string[],
    dataRetentionPeriod: number,
    thirdPartySharing: boolean = false,
    thirdParties?: string[]
  ): Promise<string> {
    const consentId = crypto.randomUUID();
    
    const consentRecord: ConsentRecord = {
      id: consentId,
      dataSubjectId,
      consentType,
      purpose,
      legalBasis,
      consentGiven: true,
      consentDate: new Date(),
      consentMethod,
      processingCategories,
      dataRetentionPeriod,
      thirdPartySharing,
      thirdParties
    };

    await supabase
      .from('consent_records')
      .insert({
        id: consentId,
        data_subject_id: dataSubjectId,
        consent_type: consentType,
        purpose,
        legal_basis: legalBasis,
        consent_given: true,
        consent_date: new Date(),
        consent_method: consentMethod,
        processing_categories: processingCategories,
        data_retention_period: dataRetentionPeriod,
        third_party_sharing: thirdPartySharing,
        third_parties: thirdParties
      });

    await AuditLogger.logEvent({
      action: 'consent_recorded',
      resource: 'consent_management',
      resourceId: consentId,
      userId: dataSubjectId,
      metadata: {
        consentType,
        purpose,
        legalBasis,
        consentMethod,
        greekDPACompliance: true
      },
      category: 'data_change',
      severity: 'low'
    });

    return consentId;
  }

  /**
   * Withdraws consent
   */
  static async withdrawConsent(
    dataSubjectId: string,
    consentId: string,
    withdrawalReason?: string
  ): Promise<boolean> {
    const { data: consent } = await supabase
      .from('consent_records')
      .select('*')
      .eq('id', consentId)
      .eq('data_subject_id', dataSubjectId)
      .single();

    if (!consent) {
      return false;
    }

    await supabase
      .from('consent_records')
      .update({
        consent_given: false,
        withdrawal_date: new Date(),
        withdrawal_reason: withdrawalReason
      })
      .eq('id', consentId);

    await AuditLogger.logEvent({
      action: 'consent_withdrawn',
      resource: 'consent_management',
      resourceId: consentId,
      userId: dataSubjectId,
      metadata: {
        withdrawalReason,
        greekDPACompliance: true
      },
      category: 'data_change',
      severity: 'medium'
    });

    return true;
  }

  /**
   * Gets consent status for data subject
   */
  static async getConsentStatus(dataSubjectId: string): Promise<ConsentRecord[]> {
    const { data } = await supabase
      .from('consent_records')
      .select('*')
      .eq('data_subject_id', dataSubjectId)
      .order('consent_date', { ascending: false });

    return data?.map(record => ({
      id: record.id,
      dataSubjectId: record.data_subject_id,
      consentType: record.consent_type,
      purpose: record.purpose,
      legalBasis: record.legal_basis,
      consentGiven: record.consent_given,
      consentDate: new Date(record.consent_date),
      withdrawalDate: record.withdrawal_date ? new Date(record.withdrawal_date) : undefined,
      consentMethod: record.consent_method,
      processingCategories: record.processing_categories,
      dataRetentionPeriod: record.data_retention_period,
      thirdPartySharing: record.third_party_sharing,
      thirdParties: record.third_parties
    })) || [];
  }
}

/**
 * Greek Data Breach Notification
 * Handles data breach notifications according to Greek DPA requirements
 */
export class GreekDataBreachHandler {
  /**
   * Reports a data breach
   */
  static async reportBreach(
    breachType: DataBreach['breachType'],
    severity: DataBreach['severity'],
    affectedDataCategories: string[],
    affectedDataSubjects: number,
    breachDescription: string,
    causeOfBreach: string,
    discoveryDate: Date = new Date()
  ): Promise<string> {
    const breachId = crypto.randomUUID();
    
    const breach: DataBreach = {
      id: breachId,
      breachType,
      severity,
      affectedDataCategories,
      affectedDataSubjects,
      breachDescription,
      causeOfBreach,
      discoveryDate,
      reportedToAuthority: false,
      dataSubjectsNotified: false,
      mitigationMeasures: [],
      status: 'discovered'
    };

    await supabase
      .from('data_breaches')
      .insert({
        id: breachId,
        breach_type: breachType,
        severity,
        affected_data_categories: affectedDataCategories,
        affected_data_subjects: affectedDataSubjects,
        breach_description: breachDescription,
        cause_of_breach: causeOfBreach,
        discovery_date: discoveryDate,
        reported_to_authority: false,
        data_subjects_notified: false,
        mitigation_measures: [],
        status: 'discovered'
      });

    // Check if breach requires notification to Greek DPA (within 72 hours)
    if (this.requiresAuthorityNotification(severity, affectedDataSubjects)) {
      await this.scheduleAuthorityNotification(breachId);
    }

    // Check if data subjects need to be notified
    if (this.requiresDataSubjectNotification(severity, affectedDataCategories)) {
      await this.scheduleDataSubjectNotification(breachId);
    }

    await AuditLogger.logEvent({
      action: 'data_breach_reported',
      resource: 'data_breach',
      resourceId: breachId,
      metadata: {
        breachType,
        severity,
        affectedDataSubjects,
        greekDPACompliance: true
      },
      category: 'security_event',
      severity: 'critical'
    });

    return breachId;
  }

  /**
   * Notifies Greek DPA about the breach
   */
  static async notifyGreekDPA(breachId: string): Promise<boolean> {
    const { data: breach } = await supabase
      .from('data_breaches')
      .select('*')
      .eq('id', breachId)
      .single();

    if (!breach) {
      return false;
    }

    // Prepare notification for Greek DPA
    const notification = {
      breachId,
      organizationName: 'Creperie Management System',
      organizationContact: process.env.DPO_EMAIL,
      breachType: breach.breach_type,
      discoveryDate: breach.discovery_date,
      reportDate: new Date(),
      affectedDataCategories: breach.affected_data_categories,
      affectedDataSubjects: breach.affected_data_subjects,
      breachDescription: breach.breach_description,
      causeOfBreach: breach.cause_of_breach,
      mitigationMeasures: breach.mitigation_measures,
      riskAssessment: this.assessBreachRisk(breach),
      contactDetails: {
        dpo: process.env.DPO_EMAIL,
        phone: process.env.DPO_PHONE,
        address: process.env.ORGANIZATION_ADDRESS
      }
    };

    // In real implementation, this would submit to Greek DPA portal
    console.log('Notifying Greek DPA:', notification);

    await supabase
      .from('data_breaches')
      .update({
        reported_to_authority: true,
        authority_report_date: new Date()
      })
      .eq('id', breachId);

    return true;
  }

  private static requiresAuthorityNotification(severity: string, affectedSubjects: number): boolean {
    // High risk breaches must be reported to Greek DPA
    return severity === 'high' || severity === 'critical' || affectedSubjects > 100;
  }

  private static requiresDataSubjectNotification(severity: string, dataCategories: string[]): boolean {
    // Notify data subjects if high risk to their rights and freedoms
    const sensitiveCategories = ['financial', 'health', 'biometric', 'genetic'];
    const hasSensitiveData = dataCategories.some(cat => sensitiveCategories.includes(cat));
    
    return severity === 'critical' || (severity === 'high' && hasSensitiveData);
  }

  private static async scheduleAuthorityNotification(breachId: string): Promise<void> {
    // Schedule notification within 72 hours
    const notificationTime = new Date(Date.now() + 72 * 60 * 60 * 1000); // 72 hours
    
    // In real implementation, this would schedule a job
    console.log(`Scheduling Greek DPA notification for breach ${breachId} at ${notificationTime}`);
  }

  private static async scheduleDataSubjectNotification(breachId: string): Promise<void> {
    // Schedule notification without undue delay
    console.log(`Scheduling data subject notification for breach ${breachId}`);
  }

  private static assessBreachRisk(breach: any): string {
    if (breach.severity === 'critical') {
      return 'High risk to rights and freedoms of data subjects';
    } else if (breach.severity === 'high') {
      return 'Medium to high risk to rights and freedoms';
    } else {
      return 'Low to medium risk';
    }
  }
}

/**
 * Greek Privacy Impact Assessment
 * Conducts DPIA according to Greek DPA guidelines
 */
export class GreekPrivacyImpactAssessment {
  /**
   * Conducts a Privacy Impact Assessment
   */
  static async conductAssessment(
    projectName: string,
    dataController: string,
    dataProcessingDescription: string,
    dataCategories: string[],
    dataSubjectCategories: string[],
    purposes: string[],
    legalBasis: string
  ): Promise<string> {
    const assessmentId = crypto.randomUUID();
    
    // Assess risk level
    const riskLevel = this.assessRiskLevel(dataCategories, purposes, dataSubjectCategories);
    
    // Identify risks
    const identifiedRisks = this.identifyRisks(dataCategories, purposes);
    
    // Propose mitigation measures
    const mitigationMeasures = this.proposeMitigationMeasures(identifiedRisks);
    
    // Calculate residual risk
    const residualRisk = this.calculateResidualRisk(riskLevel, mitigationMeasures);
    
    // Determine if consultation with Greek DPA is required
    const consultationRequired = residualRisk === 'high';

    const assessment: PrivacyImpactAssessment = {
      id: assessmentId,
      projectName,
      dataController,
      assessmentDate: new Date(),
      dataProcessingDescription,
      dataCategories,
      dataSubjectCategories,
      purposes,
      legalBasis,
      riskLevel,
      identifiedRisks,
      mitigationMeasures,
      residualRisk,
      consultationRequired,
      approvalStatus: consultationRequired ? 'pending' : 'approved'
    };

    await supabase
      .from('privacy_impact_assessments')
      .insert({
        id: assessmentId,
        project_name: projectName,
        data_controller: dataController,
        assessment_date: new Date(),
        data_processing_description: dataProcessingDescription,
        data_categories: dataCategories,
        data_subject_categories: dataSubjectCategories,
        purposes,
        legal_basis: legalBasis,
        risk_level: riskLevel,
        identified_risks: identifiedRisks,
        mitigation_measures: mitigationMeasures,
        residual_risk: residualRisk,
        consultation_required: consultationRequired,
        approval_status: consultationRequired ? 'pending' : 'approved'
      });

    await AuditLogger.logEvent({
      action: 'privacy_impact_assessment_conducted',
      resource: 'privacy_assessment',
      resourceId: assessmentId,
      metadata: {
        projectName,
        riskLevel,
        consultationRequired,
        greekDPACompliance: true
      },
      category: 'system_event',
      severity: 'medium'
    });

    return assessmentId;
  }

  private static assessRiskLevel(
    dataCategories: string[],
    purposes: string[],
    dataSubjectCategories: string[]
  ): 'low' | 'medium' | 'high' {
    let riskScore = 0;

    // High-risk data categories
    const highRiskData = ['biometric', 'genetic', 'health', 'criminal', 'financial'];
    riskScore += dataCategories.filter(cat => highRiskData.includes(cat)).length * 3;

    // High-risk purposes
    const highRiskPurposes = ['profiling', 'automated_decision_making', 'surveillance'];
    riskScore += purposes.filter(purpose => highRiskPurposes.includes(purpose)).length * 2;

    // Vulnerable data subjects
    const vulnerableSubjects = ['children', 'elderly', 'disabled', 'employees'];
    riskScore += dataSubjectCategories.filter(cat => vulnerableSubjects.includes(cat)).length * 2;

    if (riskScore >= 8) return 'high';
    if (riskScore >= 4) return 'medium';
    return 'low';
  }

  private static identifyRisks(dataCategories: string[], purposes: string[]): string[] {
    const risks: string[] = [];

    if (dataCategories.includes('financial')) {
      risks.push('Financial fraud and identity theft');
    }

    if (dataCategories.includes('biometric')) {
      risks.push('Permanent identity compromise');
    }

    if (purposes.includes('automated_decision_making')) {
      risks.push('Unfair or discriminatory automated decisions');
    }

    if (purposes.includes('profiling')) {
      risks.push('Invasive profiling and privacy intrusion');
    }

    return risks;
  }

  private static proposeMitigationMeasures(risks: string[]): string[] {
    const measures: string[] = [];

    if (risks.some(r => r.includes('fraud'))) {
      measures.push('Implement strong encryption and access controls');
      measures.push('Regular security audits and monitoring');
    }

    if (risks.some(r => r.includes('biometric'))) {
      measures.push('Use irreversible biometric templates');
      measures.push('Implement biometric data isolation');
    }

    if (risks.some(r => r.includes('automated'))) {
      measures.push('Implement human oversight for automated decisions');
      measures.push('Provide explanation and appeal mechanisms');
    }

    return measures;
  }

  private static calculateResidualRisk(
    initialRisk: 'low' | 'medium' | 'high',
    mitigationMeasures: string[]
  ): 'low' | 'medium' | 'high' {
    const riskReduction = Math.min(mitigationMeasures.length, 2); // Max 2 levels reduction
    
    const riskLevels = ['low', 'medium', 'high'];
    const currentIndex = riskLevels.indexOf(initialRisk);
    const newIndex = Math.max(0, currentIndex - riskReduction);
    
    return riskLevels[newIndex] as 'low' | 'medium' | 'high';
  }
}

