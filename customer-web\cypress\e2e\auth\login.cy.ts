/// <reference types="cypress" />

describe('Customer Authentication - Login', () => {
  beforeEach(() => {
    // Clear any existing sessions
    cy.clearAllCookies();
    cy.clearAllLocalStorage();
    cy.clearAllSessionStorage();
    
    // Set up API interceptors
    cy.intercept('POST', '/api/auth/login', { fixture: 'auth/customer-user.json' }).as('login');
    cy.intercept('GET', '/api/auth/session', { fixture: 'auth/customer-user.json' }).as('session');
    cy.intercept('POST', '/api/auth/logout', { fixture: 'auth/customer-user.json' }).as('logout');
  });

  describe('Login Page Display', () => {
    it('should display login form with all required elements', () => {
      cy.visit('/login');
      cy.waitForPageLoad();
      
      // Check page title and heading
      cy.title().should('contain', 'Login');
      cy.get('h1').should('contain', 'Welcome Back');
      
      // Check form elements
      cy.get('[data-testid="email-input"]').should('be.visible');
      cy.get('[data-testid="password-input"]').should('be.visible');
      cy.get('[data-testid="login-button"]').should('be.visible').and('contain', 'Sign In');
      
      // Check additional elements
      cy.get('[data-testid="forgot-password-link"]').should('be.visible');
      cy.get('[data-testid="register-link"]').should('be.visible');
      cy.get('[data-testid="password-toggle"]').should('be.visible');
      
      // Check social login options
      cy.get('[data-testid="google-login"]').should('be.visible');
      cy.get('[data-testid="facebook-login"]').should('be.visible');
    });

    it('should have proper form labels and placeholders', () => {
      cy.visit('/login');
      
      cy.get('[data-testid="email-input"]')
        .should('have.attr', 'placeholder', 'Enter your email')
        .and('have.attr', 'type', 'email');
      
      cy.get('[data-testid="password-input"]')
        .should('have.attr', 'placeholder', 'Enter your password')
        .and('have.attr', 'type', 'password');
      
      // Check for proper labels
      cy.get('label[for="email"]').should('contain', 'Email');
      cy.get('label[for="password"]').should('contain', 'Password');
    });
  });

  describe('Form Validation', () => {
    beforeEach(() => {
      cy.visit('/login');
    });

    it('should show validation errors for empty fields', () => {
      cy.get('[data-testid="login-button"]').click();
      
      cy.get('[data-testid="email-error"]')
        .should('be.visible')
        .and('contain', 'Email is required');
      
      cy.get('[data-testid="password-error"]')
        .should('be.visible')
        .and('contain', 'Password is required');
    });

    it('should validate email format', () => {
      cy.get('[data-testid="email-input"]').type('invalid-email');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="login-button"]').click();
      
      cy.get('[data-testid="email-error"]')
        .should('be.visible')
        .and('contain', 'Please enter a valid email address');
    });

    it('should clear validation errors when user starts typing', () => {
      // Trigger validation errors
      cy.get('[data-testid="login-button"]').click();
      cy.get('[data-testid="email-error"]').should('be.visible');
      
      // Start typing and check errors clear
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="email-error"]').should('not.exist');
      
      cy.get('[data-testid="password-input"]').type('password');
      cy.get('[data-testid="password-error"]').should('not.exist');
    });

    it('should disable submit button when form is invalid', () => {
      cy.get('[data-testid="login-button"]').should('not.be.disabled');
      
      cy.get('[data-testid="email-input"]').type('invalid-email');
      cy.get('[data-testid="login-button"]').should('be.disabled');
      
      cy.get('[data-testid="email-input"]').clear().type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('password');
      cy.get('[data-testid="login-button"]').should('not.be.disabled');
    });
  });

  describe('Password Visibility Toggle', () => {
    beforeEach(() => {
      cy.visit('/login');
    });

    it('should toggle password visibility', () => {
      cy.get('[data-testid="password-input"]').type('secretpassword');
      
      // Initially password should be hidden
      cy.get('[data-testid="password-input"]').should('have.attr', 'type', 'password');
      
      // Click toggle to show password
      cy.get('[data-testid="password-toggle"]').click();
      cy.get('[data-testid="password-input"]').should('have.attr', 'type', 'text');
      
      // Click toggle to hide password again
      cy.get('[data-testid="password-toggle"]').click();
      cy.get('[data-testid="password-input"]').should('have.attr', 'type', 'password');
    });

    it('should show appropriate icons for password visibility', () => {
      cy.get('[data-testid="password-toggle"]').should('contain.attr', 'aria-label', 'Show password');
      
      cy.get('[data-testid="password-toggle"]').click();
      cy.get('[data-testid="password-toggle"]').should('contain.attr', 'aria-label', 'Hide password');
    });
  });

  describe('Successful Login', () => {
    it('should login successfully with valid credentials', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/login', {
          statusCode: 200,
          body: userData.authResponses.loginSuccess
        }).as('loginSuccess');
        
        cy.visit('/login');
        
        cy.get('[data-testid="email-input"]').type(userData.validCustomer.email);
        cy.get('[data-testid="password-input"]').type(userData.validCustomer.password);
        cy.get('[data-testid="login-button"]').click();
        
        cy.wait('@loginSuccess');
        
        // Should redirect to home page or dashboard
        cy.url().should('not.include', '/login');
        cy.url().should('match', /\/(dashboard|menu|home)?$/);
        
        // Should show success notification
        cy.checkNotification('Login successful', 'success');
      });
    });

    it('should remember user session after successful login', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/login', {
          statusCode: 200,
          body: userData.authResponses.loginSuccess
        }).as('loginSuccess');
        
        cy.intercept('GET', '/api/auth/session', {
          statusCode: 200,
          body: userData.authResponses.sessionValid
        }).as('sessionCheck');
        
        cy.visit('/login');
        
        cy.get('[data-testid="email-input"]').type(userData.validCustomer.email);
        cy.get('[data-testid="password-input"]').type(userData.validCustomer.password);
        cy.get('[data-testid="login-button"]').click();
        
        cy.wait('@loginSuccess');
        
        // Refresh page and check if still logged in
        cy.reload();
        cy.wait('@sessionCheck');
        
        // Should not redirect to login page
        cy.url().should('not.include', '/login');
      });
    });
  });

  describe('Login Errors', () => {
    beforeEach(() => {
      cy.visit('/login');
    });

    it('should handle invalid credentials error', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/login', {
          statusCode: 401,
          body: userData.authResponses.loginInvalidCredentials
        }).as('loginError');
        
        cy.get('[data-testid="email-input"]').type(userData.invalidCredentials.email);
        cy.get('[data-testid="password-input"]').type(userData.invalidCredentials.password);
        cy.get('[data-testid="login-button"]').click();
        
        cy.wait('@loginError');
        
        cy.checkErrorState('Invalid email or password');
        cy.url().should('include', '/login');
      });
    });

    it('should handle account locked error', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/login', {
          statusCode: 423,
          body: userData.authResponses.loginAccountLocked
        }).as('accountLocked');
        
        cy.get('[data-testid="email-input"]').type(userData.validCustomer.email);
        cy.get('[data-testid="password-input"]').type('wrongpassword');
        cy.get('[data-testid="login-button"]').click();
        
        cy.wait('@accountLocked');
        
        cy.checkErrorState('Account temporarily locked');
        cy.get('[data-testid="login-button"]').should('be.disabled');
      });
    });

    it('should handle rate limiting error', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/login', {
          statusCode: 429,
          body: userData.authResponses.loginRateLimit
        }).as('rateLimited');
        
        cy.get('[data-testid="email-input"]').type(userData.validCustomer.email);
        cy.get('[data-testid="password-input"]').type(userData.validCustomer.password);
        cy.get('[data-testid="login-button"]').click();
        
        cy.wait('@rateLimited');
        
        cy.checkErrorState('Too many login attempts');
        cy.get('[data-testid="login-button"]').should('be.disabled');
      });
    });

    it('should handle server error', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/login', {
          statusCode: 500,
          body: userData.authResponses.serverError
        }).as('serverError');
        
        cy.get('[data-testid="email-input"]').type(userData.validCustomer.email);
        cy.get('[data-testid="password-input"]').type(userData.validCustomer.password);
        cy.get('[data-testid="login-button"]').click();
        
        cy.wait('@serverError');
        
        cy.checkErrorState('Something went wrong. Please try again.');
      });
    });

    it('should handle network error', () => {
      cy.intercept('POST', '/api/auth/login', { forceNetworkError: true }).as('networkError');
      
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="login-button"]').click();
      
      cy.wait('@networkError');
      
      cy.checkErrorState('Network error. Please check your connection.');
    });
  });

  describe('Loading States', () => {
    it('should show loading state during login', () => {
      cy.intercept('POST', '/api/auth/login', (req) => {
        req.reply({
          delay: 2000,
          fixture: 'auth/customer-user.json'
        });
      }).as('slowLogin');
      
      cy.visit('/login');
      
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="login-button"]').click();
      
      // Check loading state
      cy.get('[data-testid="login-button"]')
        .should('be.disabled')
        .and('contain', 'Signing In...');
      
      cy.get('[data-testid="loading-spinner"]').should('be.visible');
      
      cy.wait('@slowLogin');
      
      // Loading state should be cleared
      cy.get('[data-testid="loading-spinner"]').should('not.exist');
    });
  });

  describe('Social Login', () => {
    it('should handle Google login', () => {
      cy.visit('/login');
      
      cy.get('[data-testid="google-login"]').click();
      
      // Should redirect to Google OAuth (we'll mock this in real tests)
      cy.url().should('include', 'google');
    });

    it('should handle Facebook login', () => {
      cy.visit('/login');
      
      cy.get('[data-testid="facebook-login"]').click();
      
      // Should redirect to Facebook OAuth (we'll mock this in real tests)
      cy.url().should('include', 'facebook');
    });
  });

  describe('Navigation and Links', () => {
    beforeEach(() => {
      cy.visit('/login');
    });

    it('should navigate to registration page', () => {
      cy.get('[data-testid="register-link"]').click();
      cy.url().should('include', '/register');
    });

    it('should navigate to forgot password page', () => {
      cy.get('[data-testid="forgot-password-link"]').click();
      cy.url().should('include', '/forgot-password');
    });

    it('should redirect authenticated users away from login page', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('GET', '/api/auth/session', {
          statusCode: 200,
          body: userData.authResponses.sessionValid
        }).as('authenticatedSession');
        
        cy.visit('/login');
        cy.wait('@authenticatedSession');
        
        // Should redirect away from login page
        cy.url().should('not.include', '/login');
      });
    });
  });

  describe('Session Management', () => {
    it('should handle expired session', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('GET', '/api/auth/session', {
          statusCode: 401,
          body: userData.authResponses.sessionExpired
        }).as('expiredSession');
        
        cy.visit('/menu'); // Try to visit protected page
        cy.wait('@expiredSession');
        
        // Should redirect to login
        cy.url().should('include', '/login');
        cy.checkNotification('Session expired. Please login again.', 'info');
      });
    });

    it('should preserve redirect URL after login', () => {
      cy.fixture('auth/customer-user').then((userData) => {
        cy.intercept('POST', '/api/auth/login', {
          statusCode: 200,
          body: userData.authResponses.loginSuccess
        }).as('loginSuccess');
        
        // Try to visit protected page
        cy.visit('/checkout');
        
        // Should redirect to login with return URL
        cy.url().should('include', '/login');
        cy.url().should('include', 'redirect=%2Fcheckout');
        
        // Login
        cy.get('[data-testid="email-input"]').type(userData.validCustomer.email);
        cy.get('[data-testid="password-input"]').type(userData.validCustomer.password);
        cy.get('[data-testid="login-button"]').click();
        
        cy.wait('@loginSuccess');
        
        // Should redirect to original page
        cy.url().should('include', '/checkout');
      });
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      cy.visit('/login');
    });

    it('should be accessible', () => {
      cy.checkAccessibility();
    });

    it('should support keyboard navigation', () => {
      cy.checkKeyboardNavigation();
      
      // Test tab order
      cy.get('body').tab();
      cy.focused().should('have.attr', 'data-testid', 'email-input');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'password-input');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'password-toggle');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'login-button');
    });

    it('should have proper ARIA labels', () => {
      cy.get('[data-testid="email-input"]')
        .should('have.attr', 'aria-label', 'Email address')
        .and('have.attr', 'aria-required', 'true');
      
      cy.get('[data-testid="password-input"]')
        .should('have.attr', 'aria-label', 'Password')
        .and('have.attr', 'aria-required', 'true');
      
      cy.get('[data-testid="login-button"]')
        .should('have.attr', 'aria-label', 'Sign in to your account');
    });

    it('should announce errors to screen readers', () => {
      cy.get('[data-testid="login-button"]').click();
      
      cy.get('[data-testid="email-error"]')
        .should('have.attr', 'role', 'alert')
        .and('have.attr', 'aria-live', 'polite');
    });
  });

  describe('Responsive Design', () => {
    it('should work on different screen sizes', () => {
      cy.testResponsiveDesign();
      
      // Test mobile layout
      cy.viewport(375, 667);
      cy.visit('/login');
      
      cy.get('[data-testid="login-form"]').should('be.visible');
      cy.get('[data-testid="social-login-buttons"]').should('be.visible');
      
      // Test tablet layout
      cy.viewport(768, 1024);
      cy.get('[data-testid="login-form"]').should('be.visible');
      
      // Test desktop layout
      cy.viewport(1280, 720);
      cy.get('[data-testid="login-form"]').should('be.visible');
    });
  });

  describe('Security', () => {
    it('should clear form data on page refresh', () => {
      cy.visit('/login');
      
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('password123');
      
      cy.reload();
      
      cy.get('[data-testid="email-input"]').should('have.value', '');
      cy.get('[data-testid="password-input"]').should('have.value', '');
    });

    it('should not expose sensitive data in DOM', () => {
      cy.visit('/login');
      
      cy.get('[data-testid="password-input"]').type('secretpassword');
      
      // Password should not be visible in DOM when type="password"
      cy.get('[data-testid="password-input"]')
        .should('have.attr', 'type', 'password')
        .and('not.contain', 'secretpassword');
    });

    it('should prevent form submission on Enter key when invalid', () => {
      cy.visit('/login');
      
      cy.get('[data-testid="email-input"]').type('invalid-email{enter}');
      
      // Should not submit form
      cy.get('[data-testid="email-error"]').should('be.visible');
      cy.url().should('include', '/login');
    });
  });

  describe('Performance', () => {
    it('should load login page quickly', () => {
      cy.visit('/login');
      cy.measurePageLoadTime();
    });

    it('should optimize images', () => {
      cy.visit('/login');
      cy.checkImageOptimization();
    });
  });
});