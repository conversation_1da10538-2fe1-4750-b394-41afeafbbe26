{"extends": ["next/core-web-vitals", "eslint:recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "react", "react-hooks"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-non-null-assertion": "warn", "react/react-in-jsx-scope": "off", "react/prop-types": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "prefer-const": "error", "no-var": "error", "no-console": ["warn", {"allow": ["warn", "error"]}], "eqeqeq": "error", "curly": "error", "no-duplicate-imports": "error", "no-unused-expressions": "error", "prefer-template": "error", "object-shorthand": "error", "arrow-body-style": ["error", "as-needed"]}, "env": {"browser": true, "es2021": true, "node": true}, "settings": {"react": {"version": "detect"}}, "ignorePatterns": ["node_modules/", ".next/", "out/", "dist/", "build/", "*.config.js", "*.config.ts"]}