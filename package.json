{"name": "creperie-management-system", "version": "1.0.0", "description": "Comprehensive digital ecosystem for Creperie management including admin dashboard, POS system, customer website, and mobile app", "private": true, "workspaces": ["admin-dashboard", "pos-system", "customer-web", "customer-mobile", "shared", "database"], "scripts": {"dev": "npm run dev --workspaces", "build": "npm run build --workspaces", "lint": "npm run lint --workspaces", "clean": "npm run clean --workspaces", "install-all": "npm install", "admin:dev": "npm run dev --workspace=admin-dashboard", "pos:dev": "npm run dev --workspace=pos-system", "web:dev": "npm run dev --workspace=customer-web", "mobile:dev": "npm run dev --workspace=customer-mobile", "db:migrate": "npm run migrate --workspace=database", "db:seed": "npm run seed --workspace=database"}, "devDependencies": {"eslint": "^8.0.0", "prettier": "^3.0.0"}, "overrides": {"react": "19.1.0", "react-dom": "19.1.0", "@supabase/supabase-js": "2.50.0", "typescript": "5.8.3", "lucide-react": "0.427.0", "@types/react": "19.1.0", "@types/react-dom": "19.1.0", "@radix-ui/react-slider": "1.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/creperie-management-system.git"}, "keywords": ["creperie", "management", "pos", "restaurant", "nextjs", "electron", "react-native", "supabase"], "author": "Creperie Management Team", "license": "MIT", "dependencies": {"@expo/config-plugins": "^10.0.3", "@expo/metro-config": "^0.20.15", "@googlemaps/js-api-loader": "^1.16.8", "@tanstack/react-query-devtools": "^5.80.6", "@types/google.maps": "^3.58.1", "expo": "^53.0.13", "expo-build-properties": "~0.14.6", "metro": "^0.82.4", "react-toastify": "^11.0.5"}}