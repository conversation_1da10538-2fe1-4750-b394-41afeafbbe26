import React from 'react';
import { ShoppingCart } from 'lucide-react';
import { useTheme } from '../../contexts/theme-context';

interface CartItem {
  id: string | number;
  name: string;
  quantity: number;
  price: number;
  totalPrice: number;
  customizations?: Array<{
    name: string;
    price: number;
  }>;
  notes?: string;
}

interface MenuCartProps {
  cartItems: CartItem[];
  onCheckout: () => void;
  onUpdateCart: (items: CartItem[]) => void;
}

export const MenuCart: React.FC<MenuCartProps> = ({
  cartItems,
  onCheckout,
  onUpdateCart
}) => {
  const { resolvedTheme } = useTheme();

  const getTotalPrice = () => {
    return cartItems.reduce((sum, item) => sum + (item.totalPrice || 0), 0);
  };

  return (
    <div className={`w-80 border-l border-gray-200/20 flex flex-col ${
      resolvedTheme === 'dark' ? 'bg-gray-900/30' : 'bg-gray-50/30'
    }`}>
      <div className="p-4 border-b border-gray-200/20">
        <h3 className={`text-lg font-semibold ${
          resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
        }`}>
          Cart ({cartItems.length})
        </h3>
      </div>

      <div className="flex-1 p-4 overflow-y-auto">
        {cartItems.length === 0 ? (
          <div className="text-center py-8">
            <ShoppingCart className={`w-16 h-16 mx-auto mb-4 ${
              resolvedTheme === 'dark' ? 'text-gray-600' : 'text-gray-400'
            }`} />
            <p className={`${
              resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
            }`}>
              Your cart is empty
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {cartItems.map((item) => (
              <div
                key={item.id}
                className={`p-3 rounded-lg border ${
                  resolvedTheme === 'dark'
                    ? 'bg-gray-800/50 border-gray-700/50'
                    : 'bg-white/50 border-gray-200/50'
                }`}
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className={`font-medium ${
                    resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    {item.name}
                  </h4>
                  <span className={`font-semibold ${
                    resolvedTheme === 'dark' ? 'text-emerald-400' : 'text-emerald-600'
                  }`}>
                    €{(item.totalPrice || 0).toFixed(2)}
                  </span>
                </div>
                <div className={`text-sm ${
                  resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  Qty: {item.quantity} × €{(item.price || 0).toFixed(2)}
                </div>
                {item.customizations && item.customizations.length > 0 && (
                  <div className={`text-xs mt-1 ${
                    resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                  }`}>
                    {item.customizations.map(c => c.name || c).join(', ')}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Cart Footer */}
      <div className="p-4 border-t border-gray-200/20">
        <div className="flex justify-between items-center mb-4">
          <span className={`text-lg font-semibold ${
            resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            Total:
          </span>
          <span className={`text-xl font-bold ${
            resolvedTheme === 'dark' ? 'text-emerald-400' : 'text-emerald-600'
          }`}>
            €{getTotalPrice().toFixed(2)}
          </span>
        </div>
        <button
          onClick={onCheckout}
          disabled={cartItems.length === 0}
          className={`w-full py-3 rounded-xl font-semibold transition-all duration-300 ${
            cartItems.length === 0
              ? 'bg-gray-400/50 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600/80 text-white hover:bg-blue-600/90 hover:scale-[1.02]'
          } backdrop-blur-sm`}
        >
          Complete Order
        </button>
      </div>
    </div>
  );
}; 