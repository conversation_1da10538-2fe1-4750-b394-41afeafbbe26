{"categories": [{"id": "cat-1", "name": "Pizza", "description": "Freshly made pizzas with premium ingredients", "image": "/images/categories/pizza.jpg", "displayOrder": 1, "isActive": true}, {"id": "cat-2", "name": "Burgers", "description": "Juicy burgers made with fresh ingredients", "image": "/images/categories/burgers.jpg", "displayOrder": 2, "isActive": true}, {"id": "cat-3", "name": "Salads", "description": "Fresh and healthy salad options", "image": "/images/categories/salads.jpg", "displayOrder": 3, "isActive": true}, {"id": "cat-4", "name": "Beverages", "description": "Refreshing drinks and beverages", "image": "/images/categories/beverages.jpg", "displayOrder": 4, "isActive": true}, {"id": "cat-5", "name": "Desserts", "description": "Sweet treats and desserts", "image": "/images/categories/desserts.jpg", "displayOrder": 5, "isActive": true}], "items": [{"id": "item-1", "name": "Margherita Pizza", "description": "Classic pizza with fresh tomatoes, mozzarella, and basil", "price": 14.99, "categoryId": "cat-1", "image": "/images/menu/margherita-pizza.jpg", "isAvailable": true, "isPopular": true, "preparationTime": 15, "calories": 280, "allergens": ["gluten", "dairy"], "dietaryTags": ["vegetarian"], "ingredients": ["tomato sauce", "mozzarella", "fresh basil", "olive oil"], "customizations": [{"id": "size", "name": "Size", "type": "single", "required": true, "options": [{"id": "small", "name": "Small (10\")", "price": 0}, {"id": "medium", "name": "Medium (12\")", "price": 3}, {"id": "large", "name": "Large (14\")", "price": 6}]}, {"id": "crust", "name": "Crust Type", "type": "single", "required": false, "options": [{"id": "thin", "name": "Thin Crust", "price": 0}, {"id": "thick", "name": "<PERSON><PERSON><PERSON>", "price": 2}, {"id": "stuffed", "name": "Stuffed Crust", "price": 4}]}, {"id": "toppings", "name": "Extra Toppings", "type": "multiple", "required": false, "options": [{"id": "pepperoni", "name": "<PERSON><PERSON>", "price": 2}, {"id": "mushrooms", "name": "Mushrooms", "price": 1.5}, {"id": "olives", "name": "Black Olives", "price": 1.5}, {"id": "peppers", "name": "Bell Peppers", "price": 1.5}]}], "nutritionInfo": {"calories": 280, "protein": 12, "carbs": 35, "fat": 10, "fiber": 2, "sodium": 640}}, {"id": "item-2", "name": "Classic Cheeseburger", "description": "Juicy beef patty with cheese, lettuce, tomato, and our special sauce", "price": 12.99, "categoryId": "cat-2", "image": "/images/menu/cheeseburger.jpg", "isAvailable": true, "isPopular": true, "preparationTime": 12, "calories": 520, "allergens": ["gluten", "dairy"], "dietaryTags": [], "ingredients": ["beef patty", "cheddar cheese", "lettuce", "tomato", "onion", "special sauce", "sesame bun"], "customizations": [{"id": "patty", "name": "Patty Type", "type": "single", "required": true, "options": [{"id": "beef", "name": "<PERSON><PERSON>", "price": 0}, {"id": "chicken", "name": "Chicken Patty", "price": 1}, {"id": "veggie", "name": "<PERSON><PERSON><PERSON>", "price": 1}]}, {"id": "cheese", "name": "Cheese Type", "type": "single", "required": false, "options": [{"id": "cheddar", "name": "Cheddar", "price": 0}, {"id": "swiss", "name": "Swiss", "price": 0.5}, {"id": "blue", "name": "Blue Cheese", "price": 1}]}, {"id": "extras", "name": "Add Extras", "type": "multiple", "required": false, "options": [{"id": "bacon", "name": "<PERSON>", "price": 2}, {"id": "avocado", "name": "Avocado", "price": 1.5}, {"id": "pickles", "name": "Extra Pickles", "price": 0.5}]}], "nutritionInfo": {"calories": 520, "protein": 25, "carbs": 35, "fat": 28, "fiber": 3, "sodium": 980}}, {"id": "item-3", "name": "<PERSON>", "description": "Fresh romaine lettuce with parmesan, croutons, and caesar dressing", "price": 9.99, "categoryId": "cat-3", "image": "/images/menu/caesar-salad.jpg", "isAvailable": true, "isPopular": false, "preparationTime": 8, "calories": 180, "allergens": ["dairy", "gluten"], "dietaryTags": ["vegetarian"], "ingredients": ["romaine lettuce", "parmesan cheese", "croutons", "caesar dressing"], "customizations": [{"id": "protein", "name": "<PERSON><PERSON>", "type": "single", "required": false, "options": [{"id": "chicken", "name": "Grilled Chicken", "price": 4}, {"id": "shrimp", "name": "Grilled Shrimp", "price": 5}, {"id": "salmon", "name": "Grilled Salmon", "price": 6}]}, {"id": "dressing", "name": "Dressing", "type": "single", "required": false, "options": [{"id": "caesar", "name": "<PERSON>", "price": 0}, {"id": "ranch", "name": "Ranch", "price": 0}, {"id": "vinaigrette", "name": "Balsamic Vinaigrette", "price": 0}]}], "nutritionInfo": {"calories": 180, "protein": 8, "carbs": 12, "fat": 12, "fiber": 4, "sodium": 420}}, {"id": "item-4", "name": "Coca Cola", "description": "Classic refreshing cola drink", "price": 2.99, "categoryId": "cat-4", "image": "/images/menu/coca-cola.jpg", "isAvailable": true, "isPopular": true, "preparationTime": 1, "calories": 140, "allergens": [], "dietaryTags": ["vegan"], "ingredients": ["carbonated water", "high fructose corn syrup", "caramel color", "phosphoric acid"], "customizations": [{"id": "size", "name": "Size", "type": "single", "required": true, "options": [{"id": "small", "name": "Small (12oz)", "price": 0}, {"id": "medium", "name": "Medium (16oz)", "price": 0.5}, {"id": "large", "name": "Large (20oz)", "price": 1}]}, {"id": "ice", "name": "Ice", "type": "single", "required": false, "options": [{"id": "regular", "name": "Regular Ice", "price": 0}, {"id": "extra", "name": "Extra Ice", "price": 0}, {"id": "no-ice", "name": "No Ice", "price": 0}]}], "nutritionInfo": {"calories": 140, "protein": 0, "carbs": 39, "fat": 0, "fiber": 0, "sodium": 45}}, {"id": "item-5", "name": "Chocolate Brownie", "description": "Rich chocolate brownie served warm with vanilla ice cream", "price": 6.99, "categoryId": "cat-5", "image": "/images/menu/chocolate-brownie.jpg", "isAvailable": true, "isPopular": true, "preparationTime": 5, "calories": 420, "allergens": ["gluten", "dairy", "eggs"], "dietaryTags": ["vegetarian"], "ingredients": ["chocolate", "butter", "sugar", "eggs", "flour", "vanilla ice cream"], "customizations": [{"id": "temperature", "name": "Serving Temperature", "type": "single", "required": false, "options": [{"id": "warm", "name": "Warm", "price": 0}, {"id": "room-temp", "name": "Room Temperature", "price": 0}]}, {"id": "toppings", "name": "Add Toppings", "type": "multiple", "required": false, "options": [{"id": "ice-cream", "name": "Vanilla Ice Cream", "price": 0}, {"id": "whipped-cream", "name": "Whip<PERSON>", "price": 1}, {"id": "chocolate-sauce", "name": "Chocolate Sauce", "price": 0.5}, {"id": "nuts", "name": "Chopped Nuts", "price": 1}]}], "nutritionInfo": {"calories": 420, "protein": 6, "carbs": 52, "fat": 22, "fiber": 3, "sodium": 180}}, {"id": "item-6", "name": "Vegan Buddha Bowl", "description": "Nutritious bowl with quinoa, roasted vegetables, and tahini dressing", "price": 13.99, "categoryId": "cat-3", "image": "/images/menu/buddha-bowl.jpg", "isAvailable": false, "isPopular": false, "preparationTime": 10, "calories": 350, "allergens": ["sesame"], "dietaryTags": ["vegan", "gluten-free", "healthy"], "ingredients": ["quinoa", "roasted sweet potato", "chickpeas", "kale", "avocado", "tahini dressing"], "customizations": [{"id": "protein", "name": "Extra Protein", "type": "single", "required": false, "options": [{"id": "tofu", "name": "Grilled Tofu", "price": 3}, {"id": "tempeh", "name": "Tempeh", "price": 3.5}, {"id": "chickpeas", "name": "Extra Chickpeas", "price": 2}]}], "nutritionInfo": {"calories": 350, "protein": 15, "carbs": 45, "fat": 14, "fiber": 12, "sodium": 320}}], "pagination": {"currentPage": 1, "totalPages": 1, "totalItems": 6, "itemsPerPage": 20}, "filters": {"categories": [{"id": "cat-1", "name": "Pizza", "count": 1}, {"id": "cat-2", "name": "Burgers", "count": 1}, {"id": "cat-3", "name": "Salads", "count": 2}, {"id": "cat-4", "name": "Beverages", "count": 1}, {"id": "cat-5", "name": "Desserts", "count": 1}], "dietaryTags": [{"id": "vegetarian", "name": "Vegetarian", "count": 3}, {"id": "vegan", "name": "Vegan", "count": 2}, {"id": "gluten-free", "name": "Gluten Free", "count": 1}, {"id": "healthy", "name": "Healthy", "count": 1}], "allergens": [{"id": "gluten", "name": "Gluten", "count": 4}, {"id": "dairy", "name": "Dairy", "count": 4}, {"id": "eggs", "name": "Eggs", "count": 1}, {"id": "sesame", "name": "Sesame", "count": 1}], "priceRanges": [{"id": "under-10", "name": "Under $10", "min": 0, "max": 10, "count": 2}, {"id": "10-15", "name": "$10 - $15", "min": 10, "max": 15, "count": 3}, {"id": "over-15", "name": "Over $15", "min": 15, "max": 999, "count": 1}]}, "searchSuggestions": ["pizza", "burger", "salad", "vegetarian", "vegan", "gluten-free", "chocolate", "chicken", "cheese"], "popularItems": ["item-1", "item-2", "item-4", "item-5"], "newItems": ["item-6"], "featuredItems": [{"id": "item-1", "name": "Margherita Pizza", "description": "Classic pizza with fresh tomatoes, mozzarella, and basil", "price": 14.99, "image": "/images/menu/margherita-pizza.jpg", "badge": "Popular"}, {"id": "item-2", "name": "Classic Cheeseburger", "description": "Juicy beef patty with cheese, lettuce, tomato, and our special sauce", "price": 12.99, "image": "/images/menu/cheeseburger.jpg", "badge": "Bestseller"}]}