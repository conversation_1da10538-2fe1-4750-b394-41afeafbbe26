import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { z } from 'zod'

// Validation schema for stock updates
const updateStockSchema = z.object({
  stock_quantity: z.number().min(0, 'Stock quantity must be non-negative'),
  min_stock_level: z.number().min(0, 'Minimum stock level must be non-negative').optional(),
  cost: z.number().min(0, 'Cost must be non-negative').optional()
})

interface RouteParams {
  params: {
    id: string
  }
}

// PUT /api/ingredients/[id]/stock - Update ingredient stock quantity
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params
    const body = await request.json()
    
    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json(
        { error: 'Invalid ingredient ID format' },
        { status: 400 }
      )
    }
    
    // Validate request body
    const validationResult = updateStockSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }
    
    const { stock_quantity, min_stock_level, cost } = validationResult.data
    
    // Check if ingredient exists
    const { data: existingIngredient, error: fetchError } = await supabase
      .from('ingredients')
      .select('id, name, stock_quantity, min_stock_level, is_available')
      .eq('id', id)
      .single()
    
    if (fetchError || !existingIngredient) {
      return NextResponse.json(
        { error: 'Ingredient not found' },
        { status: 404 }
      )
    }
    
    // Prepare update data
    const updateData: any = {
      stock_quantity,
      updated_at: new Date().toISOString()
    }
    
    if (min_stock_level !== undefined) {
      updateData.min_stock_level = min_stock_level
    }
    
    if (cost !== undefined) {
      updateData.cost = cost
    }
    
    // Determine if ingredient should be automatically disabled
    // If stock falls to 0 or below minimum level, consider disabling
    const shouldAutoDisable = stock_quantity === 0
    if (shouldAutoDisable && existingIngredient.is_available) {
      updateData.is_available = false
    }
    
    // Update ingredient stock
    const { data, error } = await supabase
      .from('ingredients')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single()
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to update ingredient stock' },
        { status: 500 }
      )
    }
    
    // If ingredient was auto-disabled, also disable related subcategories
    if (shouldAutoDisable && existingIngredient.is_available) {
      await autoDisableSubcategories(supabase, id)
    }
    
    // Check if stock is below minimum level for warning
    const isLowStock = stock_quantity > 0 && 
                      min_stock_level !== undefined && 
                      stock_quantity <= min_stock_level
    
    return NextResponse.json({
      data,
      warnings: isLowStock ? ['Stock level is below minimum threshold'] : [],
      auto_disabled: shouldAutoDisable && existingIngredient.is_available
    })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Helper function to auto-disable subcategories when ingredient runs out
async function autoDisableSubcategories(supabase: any, ingredientId: string) {
  try {
    // Find all subcategories that use this ingredient
    const { data: subcategoryIngredients, error: fetchError } = await supabase
      .from('subcategory_ingredients')
      .select(`
        subcategory_id,
        subcategories(
          id,
          name_en,
          is_available
        )
      `)
      .eq('ingredient_id', ingredientId)
    
    if (fetchError || !subcategoryIngredients) {
      return
    }
    
    // Disable subcategories that are currently available
    const subcategoriesToDisable = subcategoryIngredients
      .filter((item: any) => item.subcategories?.is_available)
      .map((item: any) => item.subcategory_id)
    
    if (subcategoriesToDisable.length > 0) {
      const { error: updateError } = await supabase
        .from('subcategories')
        .update({ 
          is_available: false,
          updated_at: new Date().toISOString()
        })
        .in('id', subcategoriesToDisable)
      
      if (updateError) {
      } else {
      }
    }
  } catch (error) {
  }
}

// GET /api/ingredients/[id]/stock - Get current stock information
export async function GET(
  _request: NextRequest,
  { params }: RouteParams
) {
  try {
    const supabase = createServerSupabaseClient()
    const { id } = params
    
    // Validate UUID format
    if (!z.string().uuid().safeParse(id).success) {
      return NextResponse.json(
        { error: 'Invalid ingredient ID format' },
        { status: 400 }
      )
    }
    
    const { data, error } = await supabase
      .from('ingredients')
      .select(`
        id,
        name,
        stock_quantity,
        min_stock_level,
        cost,
        is_available,
        updated_at
      `)
      .eq('id', id)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Ingredient not found' },
          { status: 404 }
        )
      }
      
      return NextResponse.json(
        { error: 'Failed to fetch ingredient stock' },
        { status: 500 }
      )
    }
    
    // Calculate stock status
    const isLowStock = data.stock_quantity > 0 && 
                      data.min_stock_level && 
                      data.stock_quantity <= data.min_stock_level
    
    const isOutOfStock = data.stock_quantity === 0
    
    return NextResponse.json({
      data: {
        ...data,
        stock_status: {
          is_low_stock: isLowStock,
          is_out_of_stock: isOutOfStock,
          status: isOutOfStock ? 'out_of_stock' : isLowStock ? 'low_stock' : 'in_stock'
        }
      }
    })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}