import React, { useState, useEffect } from 'react';

interface SyncNotificationManagerProps {
  onSettingsUpdate?: (settings: any) => void;
  onStaffPermissionUpdate?: (update: any) => void;
  onHardwareConfigUpdate?: (update: any) => void;
}

interface SettingsUpdate {
  id: string;
  category: string;
  description: string;
  data: any;
  timestamp: string;
}

interface RestartNotification {
  reason: string;
  hardware_type: string;
  config?: any;
}

export const SyncNotificationManager: React.FC<SyncNotificationManagerProps> = ({
  onSettingsUpdate,
  onStaffPermissionUpdate,
  onHardwareConfigUpdate
}) => {
  const [pendingUpdates, setPendingUpdates] = useState<SettingsUpdate[]>([]);
  const [showNotificationPanel, setShowNotificationPanel] = useState(false);
  const [restartRequired, setRestartRequired] = useState<RestartNotification | null>(null);

  useEffect(() => {
    // Listen for settings updates from main process
    const handleSettingsUpdate = (update: SettingsUpdate) => {
      setPendingUpdates(prev => [...prev, update]);
      setShowNotificationPanel(true);

      // Show notification
      console.log('Settings update received:', update);
    };

    const handleStaffPermissionUpdate = (update: any) => {
      onStaffPermissionUpdate?.(update);
      console.log('Staff permission updated:', update);
    };

    const handleHardwareConfigUpdate = (update: any) => {
      onHardwareConfigUpdate?.(update);
      console.log('Hardware config updated:', update);
    };

    const handleRestartRequired = (notification: RestartNotification) => {
      setRestartRequired(notification);
      console.log('Restart required:', notification);
    };

    const handleSyncError = (error: any) => {
      console.error('Sync failed:', error);
    };

    const handleSyncComplete = (data: any) => {
      console.log('Settings synchronized successfully:', data);
    };

    // Register IPC listeners (if available)
    if (window.electronAPI) {
      window.electronAPI.onSettingsUpdate?.(handleSettingsUpdate);
      window.electronAPI.onStaffPermissionUpdate?.(handleStaffPermissionUpdate);
      window.electronAPI.onHardwareConfigUpdate?.(handleHardwareConfigUpdate);
      window.electronAPI.onRestartRequired?.(handleRestartRequired);
      window.electronAPI.onSyncError?.(handleSyncError);
      window.electronAPI.onSyncComplete?.(handleSyncComplete);
    }

    return () => {
      // Cleanup listeners
      if (window.electronAPI) {
        window.electronAPI.removeSettingsUpdateListener?.();
        window.electronAPI.removeStaffPermissionUpdateListener?.();
        window.electronAPI.removeHardwareConfigUpdateListener?.();
        window.electronAPI.removeRestartRequiredListener?.();
        window.electronAPI.removeSyncErrorListener?.();
        window.electronAPI.removeSyncCompleteListener?.();
      }
    };
  }, [onSettingsUpdate, onStaffPermissionUpdate, onHardwareConfigUpdate]);

  const handleApplyUpdate = async (update: SettingsUpdate) => {
    try {
      await onSettingsUpdate?.(update);
      setPendingUpdates(prev => prev.filter(u => u.id !== update.id));
      console.log(`${update.category} settings updated successfully`);
    } catch (error) {
      console.error(`Failed to apply ${update.category} settings:`, error);
    }
  };

  const handleRestartNow = () => {
    // Request application restart
    window.electronAPI?.requestRestart?.();
    setRestartRequired(null);
  };

  const handleRestartLater = () => {
    setRestartRequired(null);
    console.log('Restart reminder will appear again in 30 minutes');
    
    // Set reminder for 30 minutes
    setTimeout(() => {
      if (restartRequired) {
        console.log('Restart still required for hardware changes to take effect');
      }
    }, 30 * 60 * 1000);
  };

  return (
    <>
      {/* Notification Panel */}
      {showNotificationPanel && pendingUpdates.length > 0 && (
        <div className="fixed top-4 right-4 bg-white shadow-lg rounded-lg p-4 border-l-4 border-blue-500 z-50 max-w-sm">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-medium text-gray-900">Pending Settings Updates</h3>
            <button 
              onClick={() => setShowNotificationPanel(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>
          
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {pendingUpdates.map((update) => (
              <div key={update.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div>
                  <div className="text-sm font-medium">{update.category}</div>
                  <div className="text-xs text-gray-600">{update.description}</div>
                </div>
                <button 
                  onClick={() => handleApplyUpdate(update)}
                  className="bg-blue-600 text-white px-2 py-1 rounded text-xs hover:bg-blue-700"
                >
                  Apply
                </button>
              </div>
            ))}
          </div>
          
          <div className="mt-3 pt-2 border-t">
            <button 
              onClick={() => {
                pendingUpdates.forEach(handleApplyUpdate);
              }}
              className="w-full bg-blue-600 text-white py-2 rounded text-sm font-medium hover:bg-blue-700"
            >
              Apply All Updates
            </button>
          </div>
        </div>
      )}

      {/* Restart Required Modal */}
      {restartRequired && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mr-4">
                <span className="text-orange-600 text-xl">⚠️</span>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">Restart Required</h3>
                <p className="text-sm text-gray-600">Hardware configuration changes require a restart</p>
              </div>
            </div>
            
            <div className="mb-6">
              <p className="text-gray-700">{restartRequired.reason}</p>
              {restartRequired.hardware_type && (
                <p className="text-sm text-gray-500 mt-2">
                  Hardware: {restartRequired.hardware_type}
                </p>
              )}
            </div>
            
            <div className="flex gap-3">
              <button 
                onClick={handleRestartLater}
                className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded font-medium hover:bg-gray-300"
              >
                Restart Later
              </button>
              <button 
                onClick={handleRestartNow}
                className="flex-1 bg-red-600 text-white py-2 px-4 rounded font-medium hover:bg-red-700"
              >
                Restart Now
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
