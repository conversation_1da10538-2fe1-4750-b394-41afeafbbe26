'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import {
  Bug,
  Code,
  Terminal,
  Database,
  Zap,
  Activity,
  FileText,
  Download,
  Upload,
  Play,
  Pause,
  Square,
  RefreshCw,
  Search,
  Filter,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  EyeOff,
  Trash2,
  Copy,
  Edit,
  Save,
  BarChart3,
  PieChart,
  TrendingUp,
  Monitor,
  Wifi,
  HardDrive
} from 'lucide-react'

interface LogEntry {
  id: string
  timestamp: string
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal'
  source: 'admin' | 'pos' | 'sync' | 'api' | 'database'
  terminal_id?: string
  message: string
  details?: any
  stack_trace?: string
  user_id?: string
  session_id?: string
  request_id?: string
}

interface SyncTest {
  id: string
  name: string
  description: string
  test_type: 'staff_permissions' | 'hardware_config' | 'inventory' | 'menu' | 'full_sync'
  target_terminals: string[]
  status: 'pending' | 'running' | 'passed' | 'failed' | 'cancelled'
  started_at: string
  completed_at?: string
  duration_ms?: number
  results: TestResult[]
  error_message?: string
}

interface TestResult {
  terminal_id: string
  status: 'passed' | 'failed' | 'skipped'
  duration_ms: number
  error_message?: string
  details: any
}

interface PerformanceMetric {
  metric_name: string
  current_value: number
  previous_value: number
  unit: string
  status: 'good' | 'warning' | 'critical'
  threshold_warning: number
  threshold_critical: number
  trend: 'up' | 'down' | 'stable'
}

interface SystemHealth {
  overall_score: number
  components: {
    database: { status: string; response_time: number; connections: number }
    api: { status: string; response_time: number; error_rate: number }
    sync_service: { status: string; queue_size: number; success_rate: number }
    terminals: { online: number; total: number; avg_response_time: number }
  }
  alerts: SystemAlert[]
}

interface SystemAlert {
  id: string
  type: 'performance' | 'error' | 'security' | 'sync'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  created_at: string
  acknowledged: boolean
}

export default function DeveloperTools() {
  const [activeTab, setActiveTab] = useState<'logs' | 'testing' | 'performance' | 'health' | 'tools'>('logs')
  const [logs, setLogs] = useState<LogEntry[]>([])
  const [syncTests, setSyncTests] = useState<SyncTest[]>([])
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetric[]>([])
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    overall_score: 0,
    components: {
      database: { status: 'unknown', response_time: 0, connections: 0 },
      api: { status: 'unknown', response_time: 0, error_rate: 0 },
      sync_service: { status: 'unknown', queue_size: 0, success_rate: 0 },
      terminals: { online: 0, total: 0, avg_response_time: 0 }
    },
    alerts: []
  })
  const [logFilter, setLogFilter] = useState({
    level: 'all',
    source: 'all',
    terminal_id: 'all',
    search: ''
  })
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [loading, setLoading] = useState(true)

  const tabs = [
    { id: 'logs', name: 'System Logs', icon: FileText, description: 'View and analyze system logs' },
    { id: 'testing', name: 'Sync Testing', icon: Bug, description: 'Test synchronization functionality' },
    { id: 'performance', name: 'Performance', icon: BarChart3, description: 'Monitor system performance' },
    { id: 'health', name: 'System Health', icon: Activity, description: 'Overall system health monitoring' },
    { id: 'tools', name: 'Dev Tools', icon: Code, description: 'Developer utilities and tools' }
  ]

  useEffect(() => {
    loadData()
    
    // Set up auto-refresh every 10 seconds
    const interval = setInterval(() => {
      if (autoRefresh) {
        loadData()
      }
    }, 10000)

    return () => clearInterval(interval)
  }, [activeTab, logFilter, autoRefresh])

  const loadData = async () => {
    try {
      setLoading(true)
      
      switch (activeTab) {
        case 'logs':
          await loadLogs()
          break
        case 'testing':
          await loadSyncTests()
          break
        case 'performance':
          await loadPerformanceMetrics()
          break
        case 'health':
          await loadSystemHealth()
          break
      }
    } catch (error) {
      console.error('Failed to load developer tools data:', error)
      toast.error('Failed to load developer tools data')
    } finally {
      setLoading(false)
    }
  }

  const loadLogs = async () => {
    try {
      const params = new URLSearchParams()
      if (logFilter.level !== 'all') params.append('level', logFilter.level)
      if (logFilter.source !== 'all') params.append('source', logFilter.source)
      if (logFilter.terminal_id !== 'all') params.append('terminal_id', logFilter.terminal_id)
      if (logFilter.search) params.append('search', logFilter.search)
      params.append('limit', '100')

      const response = await fetch(`/api/dev/logs?${params}`)
      if (response.ok) {
        const data = await response.json()
        setLogs(data.logs || [])
      }
    } catch (error) {
      console.error('Failed to load logs:', error)
    }
  }

  const loadSyncTests = async () => {
    try {
      const response = await fetch('/api/dev/sync-tests')
      if (response.ok) {
        const data = await response.json()
        setSyncTests(data.tests || [])
      }
    } catch (error) {
      console.error('Failed to load sync tests:', error)
    }
  }

  const loadPerformanceMetrics = async () => {
    try {
      const response = await fetch('/api/dev/performance-metrics')
      if (response.ok) {
        const data = await response.json()
        setPerformanceMetrics(data.metrics || [])
      }
    } catch (error) {
      console.error('Failed to load performance metrics:', error)
    }
  }

  const loadSystemHealth = async () => {
    try {
      const response = await fetch('/api/dev/system-health')
      if (response.ok) {
        const data = await response.json()
        setSystemHealth(data.health || systemHealth)
      }
    } catch (error) {
      console.error('Failed to load system health:', error)
    }
  }

  const runSyncTest = async (testType: string, terminalIds: string[]) => {
    try {
      const response = await fetch('/api/dev/run-sync-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          test_type: testType,
          terminal_ids: terminalIds,
          name: `${testType} Test - ${new Date().toLocaleString()}`
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          toast.success('Sync test started successfully')
          await loadSyncTests()
        } else {
          toast.error(`Failed to start sync test: ${result.error}`)
        }
      } else {
        toast.error('Failed to start sync test')
      }
    } catch (error) {
      console.error('Failed to run sync test:', error)
      toast.error('Failed to run sync test')
    }
  }

  const exportLogs = async () => {
    try {
      const params = new URLSearchParams()
      if (logFilter.level !== 'all') params.append('level', logFilter.level)
      if (logFilter.source !== 'all') params.append('source', logFilter.source)
      if (logFilter.terminal_id !== 'all') params.append('terminal_id', logFilter.terminal_id)
      if (logFilter.search) params.append('search', logFilter.search)

      const response = await fetch(`/api/dev/export-logs?${params}`, {
        method: 'POST'
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `system-logs-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        toast.success('Logs exported successfully')
      } else {
        toast.error('Failed to export logs')
      }
    } catch (error) {
      console.error('Failed to export logs:', error)
      toast.error('Failed to export logs')
    }
  }

  const clearLogs = async () => {
    try {
      const response = await fetch('/api/dev/clear-logs', {
        method: 'POST'
      })

      if (response.ok) {
        toast.success('Logs cleared successfully')
        await loadLogs()
      } else {
        toast.error('Failed to clear logs')
      }
    } catch (error) {
      console.error('Failed to clear logs:', error)
      toast.error('Failed to clear logs')
    }
  }

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error':
      case 'fatal':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'warn':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />
      case 'info':
        return <CheckCircle className="w-4 h-4 text-blue-500" />
      case 'debug':
        return <Bug className="w-4 h-4 text-gray-500" />
      default:
        return <Activity className="w-4 h-4 text-gray-500" />
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error':
      case 'fatal':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300'
      case 'warn':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300'
      case 'info':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300'
      case 'debug':
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300'
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  const getTestStatusIcon = (status: string) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />
      case 'running':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'cancelled':
        return <Square className="w-4 h-4 text-gray-500" />
      default:
        return <Activity className="w-4 h-4 text-gray-500" />
    }
  }

  const getMetricStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'text-green-600'
      case 'warning':
        return 'text-yellow-600'
      case 'critical':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getHealthColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-yellow-600'
    if (score >= 50) return 'text-orange-600'
    return 'text-red-600'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Developer Tools</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Comprehensive debugging and development utilities for POS system
          </p>
        </div>
        
        <div className="flex gap-2 mt-4 sm:mt-0">
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
              autoRefresh 
                ? 'bg-green-600 text-white hover:bg-green-700' 
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            <RefreshCw className={`w-4 h-4 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto Refresh: {autoRefresh ? 'On' : 'Off'}
          </button>
          
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
              showAdvanced 
                ? 'bg-purple-600 text-white hover:bg-purple-700' 
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            <Settings className="w-4 h-4" />
            Advanced Mode
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'logs' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">System Logs</h3>
                <div className="flex gap-2">
                  <button
                    onClick={exportLogs}
                    className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    <Download className="w-4 h-4" />
                    Export
                  </button>
                  <button
                    onClick={clearLogs}
                    className="flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                    Clear
                  </button>
                </div>
              </div>

              {/* Log Filters */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Level
                  </label>
                  <select
                    value={logFilter.level}
                    onChange={(e) => setLogFilter(prev => ({ ...prev, level: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                  >
                    <option value="all">All Levels</option>
                    <option value="debug">Debug</option>
                    <option value="info">Info</option>
                    <option value="warn">Warning</option>
                    <option value="error">Error</option>
                    <option value="fatal">Fatal</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Source
                  </label>
                  <select
                    value={logFilter.source}
                    onChange={(e) => setLogFilter(prev => ({ ...prev, source: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                  >
                    <option value="all">All Sources</option>
                    <option value="admin">Admin Dashboard</option>
                    <option value="pos">POS Terminal</option>
                    <option value="sync">Sync Service</option>
                    <option value="api">API</option>
                    <option value="database">Database</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Terminal
                  </label>
                  <select
                    value={logFilter.terminal_id}
                    onChange={(e) => setLogFilter(prev => ({ ...prev, terminal_id: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                  >
                    <option value="all">All Terminals</option>
                    <option value="terminal-001">Terminal 001</option>
                    <option value="terminal-002">Terminal 002</option>
                    <option value="terminal-003">Terminal 003</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Search
                  </label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search logs..."
                      value={logFilter.search}
                      onChange={(e) => setLogFilter(prev => ({ ...prev, search: e.target.value }))}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-800 dark:text-white"
                    />
                  </div>
                </div>
              </div>

              {/* Log Entries */}
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {logs.map((log) => (
                  <div key={log.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-3">
                        {getLevelIcon(log.level)}
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(log.level)}`}>
                          {log.level.toUpperCase()}
                        </span>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {log.source}
                        </span>
                        {log.terminal_id && (
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {log.terminal_id}
                          </span>
                        )}
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(log.timestamp).toLocaleString()}
                      </span>
                    </div>

                    <p className="text-sm text-gray-900 dark:text-white mb-2">{log.message}</p>

                    {showAdvanced && log.details && (
                      <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs">
                        <pre className="whitespace-pre-wrap">{JSON.stringify(log.details, null, 2)}</pre>
                      </div>
                    )}

                    {showAdvanced && log.stack_trace && (
                      <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded text-xs">
                        <pre className="whitespace-pre-wrap text-red-700 dark:text-red-400">{log.stack_trace}</pre>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {logs.length === 0 && (
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No logs found</h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    No log entries match your current filters.
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'testing' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Sync Testing</h3>
                <button
                  onClick={() => runSyncTest('full_sync', ['all'])}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <Play className="w-4 h-4" />
                  Run Full Sync Test
                </button>
              </div>

              {/* Quick Test Buttons */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button
                  onClick={() => runSyncTest('staff_permissions', ['all'])}
                  className="flex flex-col items-center gap-2 p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <Bug className="w-6 h-6 text-blue-600" />
                  <span className="text-sm font-medium">Staff Permissions</span>
                </button>

                <button
                  onClick={() => runSyncTest('hardware_config', ['all'])}
                  className="flex flex-col items-center gap-2 p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <HardDrive className="w-6 h-6 text-green-600" />
                  <span className="text-sm font-medium">Hardware Config</span>
                </button>

                <button
                  onClick={() => runSyncTest('inventory', ['all'])}
                  className="flex flex-col items-center gap-2 p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <Database className="w-6 h-6 text-purple-600" />
                  <span className="text-sm font-medium">Inventory</span>
                </button>

                <button
                  onClick={() => runSyncTest('menu', ['all'])}
                  className="flex flex-col items-center gap-2 p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <FileText className="w-6 h-6 text-orange-600" />
                  <span className="text-sm font-medium">Menu</span>
                </button>
              </div>

              {/* Test Results */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900 dark:text-white">Recent Test Results</h4>
                {syncTests.map((test) => (
                  <div key={test.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {getTestStatusIcon(test.status)}
                        <div>
                          <h5 className="font-medium text-gray-900 dark:text-white">{test.name}</h5>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{test.description}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {test.duration_ms ? `${test.duration_ms}ms` : 'Running...'}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date(test.started_at).toLocaleString()}
                        </div>
                      </div>
                    </div>

                    {test.results.length > 0 && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        {test.results.map((result) => (
                          <div key={result.terminal_id} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                            <span className="text-sm">{result.terminal_id}</span>
                            <div className="flex items-center gap-2">
                              {getTestStatusIcon(result.status)}
                              <span className="text-xs">{result.duration_ms}ms</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {test.error_message && (
                      <div className="mt-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
                        <p className="text-sm text-red-700 dark:text-red-400">{test.error_message}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'performance' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Performance Metrics</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {performanceMetrics.map((metric) => (
                  <div key={metric.metric_name} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900 dark:text-white">{metric.metric_name}</h4>
                      <span className={`text-sm font-medium ${getMetricStatusColor(metric.status)}`}>
                        {metric.status}
                      </span>
                    </div>

                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-2xl font-bold text-gray-900 dark:text-white">
                        {metric.current_value}
                      </span>
                      <span className="text-sm text-gray-600 dark:text-gray-400">{metric.unit}</span>
                      {metric.trend === 'up' && <TrendingUp className="w-4 h-4 text-green-500" />}
                      {metric.trend === 'down' && <TrendingUp className="w-4 h-4 text-red-500 rotate-180" />}
                    </div>

                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Previous: {metric.previous_value} {metric.unit}
                    </div>

                    <div className="mt-2 text-xs">
                      <span className="text-yellow-600">Warning: {metric.threshold_warning}</span>
                      <span className="mx-2">•</span>
                      <span className="text-red-600">Critical: {metric.threshold_critical}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'health' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">System Health</h3>
                <div className={`text-3xl font-bold ${getHealthColor(systemHealth.overall_score)}`}>
                  {systemHealth.overall_score}%
                </div>
              </div>

              {/* Component Health */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Database</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <span className={systemHealth.components.database.status === 'healthy' ? 'text-green-600' : 'text-red-600'}>
                        {systemHealth.components.database.status}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Response Time:</span>
                      <span>{systemHealth.components.database.response_time}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Connections:</span>
                      <span>{systemHealth.components.database.connections}</span>
                    </div>
                  </div>
                </div>

                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">API</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <span className={systemHealth.components.api.status === 'healthy' ? 'text-green-600' : 'text-red-600'}>
                        {systemHealth.components.api.status}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Response Time:</span>
                      <span>{systemHealth.components.api.response_time}ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Error Rate:</span>
                      <span>{systemHealth.components.api.error_rate}%</span>
                    </div>
                  </div>
                </div>

                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Sync Service</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <span className={systemHealth.components.sync_service.status === 'healthy' ? 'text-green-600' : 'text-red-600'}>
                        {systemHealth.components.sync_service.status}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Queue Size:</span>
                      <span>{systemHealth.components.sync_service.queue_size}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Success Rate:</span>
                      <span>{systemHealth.components.sync_service.success_rate}%</span>
                    </div>
                  </div>
                </div>

                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Terminals</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Online:</span>
                      <span>{systemHealth.components.terminals.online}/{systemHealth.components.terminals.total}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg Response:</span>
                      <span>{systemHealth.components.terminals.avg_response_time}ms</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* System Alerts */}
              {systemHealth.alerts.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">System Alerts</h4>
                  <div className="space-y-2">
                    {systemHealth.alerts.map((alert) => (
                      <div key={alert.id} className={`p-3 rounded-lg border ${
                        alert.severity === 'critical' ? 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800' :
                        alert.severity === 'high' ? 'bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:border-orange-800' :
                        alert.severity === 'medium' ? 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800' :
                        'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800'
                      }`}>
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="font-medium">{alert.title}</h5>
                            <p className="text-sm text-gray-600 dark:text-gray-400">{alert.message}</p>
                          </div>
                          <span className="text-xs text-gray-500">
                            {new Date(alert.created_at).toLocaleString()}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'tools' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Developer Utilities</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Database Tools</h4>
                  <div className="space-y-2">
                    <button className="w-full text-left px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                      Run Database Migrations
                    </button>
                    <button className="w-full text-left px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                      Backup Database
                    </button>
                    <button className="w-full text-left px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                      Optimize Tables
                    </button>
                  </div>
                </div>

                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Cache Management</h4>
                  <div className="space-y-2">
                    <button className="w-full text-left px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                      Clear All Caches
                    </button>
                    <button className="w-full text-left px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                      Clear Sync Cache
                    </button>
                    <button className="w-full text-left px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                      Rebuild Cache
                    </button>
                  </div>
                </div>

                <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">System Maintenance</h4>
                  <div className="space-y-2">
                    <button className="w-full text-left px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                      Restart Services
                    </button>
                    <button className="w-full text-left px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                      Update System
                    </button>
                    <button className="w-full text-left px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 rounded hover:bg-gray-100 dark:hover:bg-gray-600">
                      Generate Report
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
