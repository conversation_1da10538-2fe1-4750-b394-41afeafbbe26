/**
 * Authentication Hooks for Admin Dashboard
 * Provides authentication state and user management
 */

'use client';

import { useAuth as useAuthContext } from '../contexts/auth-context';

/**
 * Hook to access authentication context
 * @returns Authentication context with user data and auth methods
 */
export const useAuth = useAuthContext;

/**
 * Hook to check if user has specific role
 * @param requiredRole - The role to check against
 * @returns boolean indicating if user has the required role
 */
export const useRole = (requiredRole: string) => {
  const { profile } = useAuth();
  return profile?.role_id === requiredRole;
};

/**
 * Hook to check if user has any of the specified roles
 * @param roles - Array of roles to check against
 * @returns boolean indicating if user has any of the specified roles
 */
export const useRoles = (roles: string[]) => {
  const { profile } = useAuth();
  return profile?.role_id ? roles.includes(profile.role_id) : false;
};

/**
 * Hook to get user permissions
 * @returns Array of user permissions
 */
export const usePermissions = (): string[] => {
  // TODO: Implement permissions based on role_id
  return [];
};

/**
 * Hook to check if user has specific permission
 * @param permission - The permission to check
 * @returns boolean indicating if user has the permission
 */
export const usePermission = (permission: string): boolean => {
  const permissions = usePermissions();
  return permissions.includes(permission);
};