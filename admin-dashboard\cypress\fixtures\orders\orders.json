{"data": [{"id": "ORD-001", "customer_id": "cust-1", "customer_name": "<PERSON>", "customer_email": "<EMAIL>", "customer_phone": "******-0123", "items": [{"id": "item-1", "name": "<PERSON><PERSON><PERSON>", "price": 12.99, "quantity": 2, "customizations": ["Extra Nutella", "Whip<PERSON>"]}, {"id": "item-2", "name": "Fresh Strawberry Crepe", "price": 14.99, "quantity": 1, "customizations": ["Extra Strawberries"]}], "subtotal": 40.97, "tax": 3.28, "total": 44.25, "status": "pending", "payment_method": "credit_card", "payment_status": "paid", "order_type": "dine_in", "table_number": 5, "special_instructions": "Please make it extra crispy", "created_at": "2024-01-15T10:30:00.000Z", "updated_at": "2024-01-15T10:30:00.000Z", "estimated_completion": "2024-01-15T10:45:00.000Z"}, {"id": "ORD-002", "customer_id": "cust-2", "customer_name": "<PERSON>", "customer_email": "<EMAIL>", "customer_phone": "******-0456", "items": [{"id": "item-3", "name": "Savory Ham & Cheese Crepe", "price": 16.99, "quantity": 1, "customizations": ["Extra Cheese"]}, {"id": "item-4", "name": "French Coffee", "price": 4.99, "quantity": 2, "customizations": []}], "subtotal": 26.97, "tax": 2.16, "total": 29.13, "status": "completed", "payment_method": "cash", "payment_status": "paid", "order_type": "takeout", "table_number": null, "special_instructions": null, "created_at": "2024-01-15T09:15:00.000Z", "updated_at": "2024-01-15T09:45:00.000Z", "estimated_completion": "2024-01-15T09:30:00.000Z"}, {"id": "ORD-003", "customer_id": "cust-3", "customer_name": "<PERSON>", "customer_email": "<EMAIL>", "customer_phone": "******-0789", "items": [{"id": "item-5", "name": "Chocolate Banana Crepe", "price": 13.99, "quantity": 1, "customizations": ["Extra Chocolate Sauce"]}, {"id": "item-6", "name": "Orange Juice", "price": 3.99, "quantity": 1, "customizations": []}], "subtotal": 17.98, "tax": 1.44, "total": 19.42, "status": "preparing", "payment_method": "debit_card", "payment_status": "paid", "order_type": "dine_in", "table_number": 3, "special_instructions": "Customer has nut allergy", "created_at": "2024-01-15T11:00:00.000Z", "updated_at": "2024-01-15T11:05:00.000Z", "estimated_completion": "2024-01-15T11:20:00.000Z"}, {"id": "ORD-004", "customer_id": "cust-4", "customer_name": "<PERSON>", "customer_email": "<EMAIL>", "customer_phone": "******-0321", "items": [{"id": "item-7", "name": "Lemon Sugar Crepe", "price": 11.99, "quantity": 2, "customizations": ["Extra Lemon"]}], "subtotal": 23.98, "tax": 1.92, "total": 25.9, "status": "cancelled", "payment_method": "credit_card", "payment_status": "refunded", "order_type": "delivery", "table_number": null, "special_instructions": "Delivery to apartment 4B", "created_at": "2024-01-15T08:30:00.000Z", "updated_at": "2024-01-15T08:45:00.000Z", "estimated_completion": null, "cancellation_reason": "Customer requested cancellation"}, {"id": "ORD-005", "customer_id": "cust-5", "customer_name": "<PERSON>", "customer_email": "<EMAIL>", "customer_phone": "******-0654", "items": [{"id": "item-8", "name": "Spinach & Feta Crepe", "price": 15.99, "quantity": 1, "customizations": ["Extra Feta"]}, {"id": "item-9", "name": "Green Tea", "price": 3.49, "quantity": 1, "customizations": []}], "subtotal": 19.48, "tax": 1.56, "total": 21.04, "status": "ready", "payment_method": "mobile_pay", "payment_status": "paid", "order_type": "takeout", "table_number": null, "special_instructions": null, "created_at": "2024-01-15T12:15:00.000Z", "updated_at": "2024-01-15T12:30:00.000Z", "estimated_completion": "2024-01-15T12:35:00.000Z"}], "pagination": {"page": 1, "limit": 10, "total": 5, "totalPages": 1}, "summary": {"total_orders": 5, "pending_orders": 1, "completed_orders": 1, "preparing_orders": 1, "ready_orders": 1, "cancelled_orders": 1, "total_revenue": 139.74, "average_order_value": 27.95}}