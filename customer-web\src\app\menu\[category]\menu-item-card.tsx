'use client';

import Image from 'next/image';
import { useState } from 'react';
import { GlassCard, GlassButton } from '@/components/ui/glass-components';
import { useCartContext } from '@/providers/cart-provider';
import { useNotificationHelpers } from '@/components/ui/notification-system';
import { Plus, Check, Settings } from 'lucide-react';
import { MenuItem } from '@/types/types';
import { IngredientsModal } from '@/components/menu/IngredientsModal';
import { useOrderType } from '@/contexts/OrderTypeContext';

interface MenuItemCardProps {
  item: MenuItem;
  categoryId: string;
  categoryVariant?: string;
}

export function MenuItemCard({ item, categoryId, categoryVariant }: MenuItemCardProps) {
  const [isAdding, setIsAdding] = useState(false);
  const [justAdded, setJustAdded] = useState(false);
  const [showIngredientsModal, setShowIngredientsModal] = useState(false);
  const { addItem, itemCount } = useCartContext();
  const { success } = useNotificationHelpers();
  const { orderType } = useOrderType();

  // Get the correct price based on order type
  const getPrice = () => {
    if (orderType === 'delivery') {
      return item.delivery_price || item.price;
    }
    return item.pickup_price || item.price;
  };

  const currentPrice = getPrice();

  const handleAddToCart = async () => {
    // If item is customizable, show ingredients modal
    if (item.is_customizable) {
      setShowIngredientsModal(true);
      return;
    }

    setIsAdding(true);

    try {
      // Add item to cart using the proper cart provider
      addItem({
        ...item,
        price: currentPrice, // Use the correct price for the order type
        orderType
      });

      // Show success notification
      success(
        'Added to Cart!',
        `${item.name} has been added to your cart`,
        {
          label: 'View Cart',
          onClick: () => window.location.href = '/cart'
        }
      );

      // Show success state
      setJustAdded(true);

      // Reset success state after 2 seconds
      setTimeout(() => {
        setJustAdded(false);
      }, 2000);
    } catch (error) {
      console.error('Error adding item to cart:', error);
    } finally {
      setIsAdding(false);
    }
  };

  const handleCustomizedAddToCart = (customization: {
    selectedIngredients: Array<{
      ingredient: any;
      quantity: number;
    }>;
    totalPrice: number;
  }) => {
    try {
      // Add customized item to cart
      addItem({
        ...item,
        customizations: customization.selectedIngredients,
        price: customization.totalPrice, // This already includes the correct base price from the modal
        orderType
      });

      // Show success notification
      success(
        'Added to Cart!',
        `Customized ${item.name} has been added to your cart`,
        {
          label: 'View Cart',
          onClick: () => window.location.href = '/cart'
        }
      );

      // Close modal and show success state
      setShowIngredientsModal(false);
      setJustAdded(true);

      // Reset success state after 2 seconds
      setTimeout(() => {
        setJustAdded(false);
      }, 2000);
    } catch (error) {
      console.error('Error adding customized item to cart:', error);
    }
  };

  return (
    <GlassCard className="overflow-hidden hover:shadow-lg transition-all duration-300">
      <div className="relative h-48">
        <Image src={item.image_url || '/placeholder-food.jpg'} alt={item.name} fill className="object-cover" />

        {/* Badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1">
          {item.featured && (
            <div className="bg-amber-500 text-white text-xs font-bold px-2 py-1 rounded-full">
              Popular
            </div>
          )}
          {item.isVegetarian && (
            <div className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
              Vegetarian
            </div>
          )}
        </div>

        {/* Quick Add Button */}
        <div className="absolute top-2 right-2">
          <button
            onClick={handleAddToCart}
            disabled={isAdding}
            className="w-8 h-8 bg-white/90 hover:bg-white rounded-full flex items-center justify-center transition-all duration-200 hover:scale-110 disabled:opacity-50"
          >
            {justAdded ? (
              <Check className="w-4 h-4 text-green-600" />
            ) : (
              <Plus className="w-4 h-4 text-gray-800" />
            )}
          </button>
        </div>
      </div>

      <div className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-xl font-semibold">{item.name}</h3>
          <div className="text-right">
            <span className="text-lg font-bold text-primary">${currentPrice.toFixed(2)}</span>
            {orderType === 'delivery' && item.pickup_price && item.delivery_price && item.pickup_price !== item.delivery_price && (
              <div className="text-xs text-muted-foreground">
                Pickup: ${item.pickup_price.toFixed(2)}
              </div>
            )}
          </div>
        </div>

        <p className="text-muted-foreground mb-4 text-sm leading-relaxed">{item.description}</p>

        {/* Action Buttons */}
        <div className="space-y-2">
          {item.is_customizable && (
            <GlassButton
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => setShowIngredientsModal(true)}
            >
              <Settings className="w-4 h-4 mr-2" />
              Customize
            </GlassButton>
          )}

          <GlassButton
            variant={categoryVariant as any}
            size="sm"
            className="w-full"
            onClick={handleAddToCart}
            disabled={isAdding}
          >
            {isAdding ? (
              <span className="flex items-center justify-center">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                Adding...
              </span>
            ) : justAdded ? (
              <span className="flex items-center justify-center">
                <Check className="w-4 h-4 mr-2" />
                Added to Cart!
              </span>
            ) : (
              item.is_customizable ? 'Quick Add' : 'Add to Cart'
            )}
          </GlassButton>
        </div>
      </div>

      {/* Ingredients Modal */}
        {item.is_customizable && (
          <IngredientsModal
            isOpen={showIngredientsModal}
            onClose={() => setShowIngredientsModal(false)}
            menuItem={{
              ...item,
              base_price: currentPrice
            }}
            onAddToCart={handleAddCustomizedItem}
          />
        )}
    </GlassCard>
  );
}
