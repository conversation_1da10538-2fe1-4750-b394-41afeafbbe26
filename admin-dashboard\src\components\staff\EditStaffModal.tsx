'use client'

import React, { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { X, Save, Eye, EyeOff, User, Mail, Phone, Shield, Building, CreditCard, Settings, Lock, RefreshCw, UserPlus, Check } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { useTheme } from '@/contexts/theme-context'
import { GlassModal, GlassCard, GlassButton, GlassInput } from '@/components/ui/glass-components'

interface Role {
  id: string
  name: string
  display_name: string
  color: string
}

interface Branch {
  id: string
  name: string
}

interface Staff {
  id: string
  staff_code?: string
  first_name: string
  last_name: string
  email: string
  phone?: string
  role_id?: string
  role?: {
    id: string
    name: string
    display_name: string
  }
  branch_id?: string
  department?: string
  employment_type?: string
  hourly_rate?: string
  pin?: string
  can_login_pos: boolean
  can_login_admin?: boolean
  emergency_contact_name?: string
  emergency_contact_phone?: string
  notes?: string
  is_active: boolean
  last_login_at?: string
}

interface EditStaffModalProps {
  isOpen: boolean
  onClose: () => void
  onStaffUpdated: () => void
  staff: Staff | null
}

interface StaffFormData {
  first_name: string
  last_name: string
  email: string
  phone: string
  role_id: string
  branch_id: string
  department: string
  employment_type: string
  hourly_rate: string
  pin: string
  can_login_pos: boolean
  can_login_admin: boolean
  emergency_contact_name: string
  emergency_contact_phone: string
  notes: string
  is_active: boolean
}

const EditStaffModal = ({ isOpen, onClose, onStaffUpdated, staff }: EditStaffModalProps) => {
  const { isDarkTheme } = useTheme()
  const [roles, setRoles] = useState<Role[]>([])
  const [branches, setBranches] = useState<Branch[]>([])
  const [loading, setLoading] = useState(false)
  const [showPin, setShowPin] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)
  
  const [formData, setFormData] = useState<StaffFormData>({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    role_id: '',
    branch_id: '',
    department: '',
    employment_type: 'full-time',
    hourly_rate: '',
    pin: '',
    can_login_pos: true,
    can_login_admin: false,
    emergency_contact_name: '',
    emergency_contact_phone: '',
    notes: '',
    is_active: true
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (isOpen && staff) {
      loadRoles()
      loadBranches()
      // Populate form with staff data
      setFormData({
        first_name: staff.first_name || '',
        last_name: staff.last_name || '',
        email: staff.email || '',
        phone: staff.phone || '',
        role_id: staff.role_id || staff.role?.id || '',
        branch_id: staff.branch_id || '',
        department: staff.department || '',
        employment_type: staff.employment_type || 'full-time',
        hourly_rate: staff.hourly_rate || '',
        pin: staff.pin || '',
        can_login_pos: staff.can_login_pos || false,
        can_login_admin: staff.can_login_admin || false,
        emergency_contact_name: staff.emergency_contact_name || '',
        emergency_contact_phone: staff.emergency_contact_phone || '',
        notes: staff.notes || '',
        is_active: staff.is_active !== false
      })
    }
  }, [isOpen, staff])

  const loadRoles = async () => {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('id, name, display_name, color')
        .eq('is_active', true)
        .order('level')

      if (error) {
        if (error.message?.includes('relation') || error.message?.includes('does not exist')) {
          console.log('Roles table not found - staff management system not yet set up')
          setRoles([])
          return
        }
        throw error
      }
      setRoles(data || [])
    } catch (error) {
      console.error('Error loading roles:', error)
      setRoles([])
    }
  }

  const loadBranches = async () => {
    try {
      const { data, error } = await supabase
        .from('branches')
        .select('id, name')
        .eq('is_active', true)
        .order('name')

      if (error) {
        if (error.message?.includes('relation') || error.message?.includes('does not exist')) {
          console.log('Branches table not found - branch management system not yet set up')
          setBranches([])
          return
        }
        throw error
      }
      setBranches(data || [])
    } catch (error) {
      console.error('Error loading branches:', error)
      setBranches([])
    }
  }

  const generateRandomPin = () => {
    const pin = Math.floor(1000 + Math.random() * 9000).toString()
    setFormData(prev => ({ ...prev, pin }))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Step 1 validation
    if (!formData.first_name.trim()) newErrors.first_name = 'First name is required'
    if (!formData.last_name.trim()) newErrors.last_name = 'Last name is required'
    if (!formData.email.trim()) newErrors.email = 'Email is required'
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) newErrors.email = 'Invalid email format'
    
    // Step 2 validation
    if (!formData.role_id) newErrors.role_id = 'Role is required'
    if (!formData.department.trim()) newErrors.department = 'Department is required'
    
    // Step 3 validation
    if (formData.can_login_pos && !formData.pin.trim()) newErrors.pin = 'PIN is required for POS access'
    else if (formData.pin && !/^\d{4,6}$/.test(formData.pin)) newErrors.pin = 'PIN must be 4-6 digits'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm() || !staff) {
      toast.error('Please fix the errors before continuing')
      return
    }

    setLoading(true)
    
    try {
      // Update staff member directly using Supabase
      const { error } = await supabase
        .from('staff')
        .update({
          first_name: formData.first_name,
          last_name: formData.last_name,
          email: formData.email,
          phone: formData.phone,
          role_id: formData.role_id,
          branch_id: formData.branch_id,
          department: formData.department,
          employment_type: formData.employment_type,
          hourly_rate: formData.hourly_rate ? parseFloat(formData.hourly_rate) : null,
          notes: formData.notes,
          can_login_pos: formData.can_login_pos,
          can_login_admin: formData.can_login_admin,
          pin: formData.pin,
          emergency_contact_name: formData.emergency_contact_name,
          emergency_contact_phone: formData.emergency_contact_phone,
          is_active: formData.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', staff.id)

      if (error) {
        throw error
      }

      toast.success('Staff member updated successfully')
      onStaffUpdated()
      onClose()
      
    } catch (error) {
      console.error('Error updating staff:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update staff member')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof StaffFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const nextStep = () => {
    if (currentStep === 1) {
      // Validate step 1
      const step1Errors: Record<string, string> = {}
      if (!formData.first_name.trim()) step1Errors.first_name = 'First name is required'
      if (!formData.last_name.trim()) step1Errors.last_name = 'Last name is required'
      if (!formData.email.trim()) step1Errors.email = 'Email is required'
      else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) step1Errors.email = 'Invalid email format'
      
      if (Object.keys(step1Errors).length > 0) {
        setErrors(step1Errors)
        return
      }
    }
    
    if (currentStep === 2) {
      // Validate step 2
      const step2Errors: Record<string, string> = {}
      if (!formData.role_id) step2Errors.role_id = 'Role is required'
      if (!formData.department.trim()) step2Errors.department = 'Department is required'
      
      if (Object.keys(step2Errors).length > 0) {
        setErrors(step2Errors)
        return
      }
    }
    
    setCurrentStep(prev => Math.min(prev + 1, 3))
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  if (!isOpen || !staff) return null

  return (
    <GlassModal
      isOpen={isOpen}
      onClose={onClose}
      title={`Edit Staff Member - ${staff.first_name} ${staff.last_name}`}
      className="max-w-2xl w-full max-h-[90vh] flex flex-col"
    >
      <form onSubmit={handleSubmit} className="flex flex-col h-full">
        {/* Progress Steps */}
        <div className="flex items-center justify-center space-x-4 mb-8">
          {[1, 2, 3].map((step) => (
            <div key={step} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                step === currentStep
                  ? isDarkTheme 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-blue-600 text-white'
                  : step < currentStep
                    ? isDarkTheme 
                      ? 'bg-green-500 text-white' 
                      : 'bg-green-600 text-white'
                    : isDarkTheme 
                      ? 'bg-white/20 text-white/60' 
                      : 'bg-gray-200 text-gray-500'
              }`}>
                {step < currentStep ? <Check className="w-4 h-4" /> : step}
              </div>
              {step < 3 && (
                <div className={`w-12 h-0.5 mx-2 transition-colors duration-300 ${
                  step < currentStep
                    ? isDarkTheme ? 'bg-green-500' : 'bg-green-600'
                    : isDarkTheme ? 'bg-white/20' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step Labels */}
        <div className="flex justify-between text-sm mb-6">
          <span className={`transition-colors duration-300 ${
            currentStep === 1 
              ? isDarkTheme ? 'text-blue-400' : 'text-blue-600'
              : isDarkTheme ? 'text-white/60' : 'text-gray-500'
          }`}>
            Personal Info
          </span>
          <span className={`transition-colors duration-300 ${
            currentStep === 2 
              ? isDarkTheme ? 'text-blue-400' : 'text-blue-600'
              : isDarkTheme ? 'text-white/60' : 'text-gray-500'
          }`}>
            Role & Department
          </span>
          <span className={`transition-colors duration-300 ${
            currentStep === 3 
              ? isDarkTheme ? 'text-blue-400' : 'text-blue-600'
              : isDarkTheme ? 'text-white/60' : 'text-gray-500'
          }`}>
            Access & Security
          </span>
        </div>

        {/* Scrollable Form Content */}
        <div className="glass-modal-content">
          <div className="space-y-6">
            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkTheme ? 'text-white' : 'text-gray-700'}`}>
                      <User className="w-4 h-4 inline mr-2" />
                      First Name *
                    </label>
                    <GlassInput
                      type="text"
                      value={formData.first_name}
                      onChange={(e) => handleInputChange('first_name', e.target.value)}
                      placeholder="Enter first name"
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkTheme ? 'text-white' : 'text-gray-700'}`}>
                      <User className="w-4 h-4 inline mr-2" />
                      Last Name *
                    </label>
                    <GlassInput
                      type="text"
                      value={formData.last_name}
                      onChange={(e) => handleInputChange('last_name', e.target.value)}
                      placeholder="Enter last name"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkTheme ? 'text-white' : 'text-gray-700'}`}>
                      <Mail className="w-4 h-4 inline mr-2" />
                      Email Address *
                    </label>
                    <GlassInput
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="Enter email address"
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkTheme ? 'text-white' : 'text-gray-700'}`}>
                      <Phone className="w-4 h-4 inline mr-2" />
                      Phone Number
                    </label>
                    <GlassInput
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="Enter phone number"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkTheme ? 'text-white' : 'text-gray-700'}`}>
                      Emergency Contact Name
                    </label>
                    <GlassInput
                      type="text"
                      value={formData.emergency_contact_name}
                      onChange={(e) => handleInputChange('emergency_contact_name', e.target.value)}
                      placeholder="Enter emergency contact name"
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkTheme ? 'text-white' : 'text-gray-700'}`}>
                      Emergency Contact Phone
                    </label>
                    <GlassInput
                      type="tel"
                      value={formData.emergency_contact_phone}
                      onChange={(e) => handleInputChange('emergency_contact_phone', e.target.value)}
                      placeholder="Enter emergency contact phone"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Role & Department */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkTheme ? 'text-white' : 'text-gray-700'}`}>
                      <Shield className="w-4 h-4 inline mr-2" />
                      Role *
                    </label>
                    <select
                      value={formData.role_id}
                      onChange={(e) => handleInputChange('role_id', e.target.value)}
                      className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 ${
                        isDarkTheme
                          ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400'
                          : 'bg-white/80 border-gray-200 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                      } backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500/20 ${
                        errors.role_id ? 'border-red-500' : ''
                      }`}
                    >
                      <option value="">Select a role</option>
                      {roles.map((role) => (
                        <option key={role.id} value={role.id}>
                          {role.display_name}
                        </option>
                      ))}
                    </select>
                    {errors.role_id && (
                      <p className="text-red-500 text-sm mt-1">{errors.role_id}</p>
                    )}
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkTheme ? 'text-white' : 'text-gray-700'}`}>
                      <Building className="w-4 h-4 inline mr-2" />
                      Branch
                    </label>
                    <select
                      value={formData.branch_id}
                      onChange={(e) => handleInputChange('branch_id', e.target.value)}
                      className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 ${
                        isDarkTheme
                          ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400'
                          : 'bg-white/80 border-gray-200 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                      } backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                    >
                      <option value="">Select a branch</option>
                      {branches.map((branch) => (
                        <option key={branch.id} value={branch.id}>
                          {branch.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkTheme ? 'text-white' : 'text-gray-700'}`}>
                      Department *
                    </label>
                    <GlassInput
                      type="text"
                      value={formData.department}
                      onChange={(e) => handleInputChange('department', e.target.value)}
                      placeholder="Enter department"
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkTheme ? 'text-white' : 'text-gray-700'}`}>
                      Employment Type
                    </label>
                    <select
                      value={formData.employment_type}
                      onChange={(e) => handleInputChange('employment_type', e.target.value)}
                      className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 ${
                        isDarkTheme
                          ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400'
                          : 'bg-white/80 border-gray-200 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                      } backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                    >
                      <option value="full-time">Full Time</option>
                      <option value="part-time">Part Time</option>
                      <option value="contract">Contract</option>
                      <option value="temporary">Temporary</option>
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkTheme ? 'text-white' : 'text-gray-700'}`}>
                      <CreditCard className="w-4 h-4 inline mr-2" />
                      Hourly Rate (€)
                    </label>
                    <GlassInput
                      type="number"
                      step="0.01"
                      value={formData.hourly_rate}
                      onChange={(e) => handleInputChange('hourly_rate', e.target.value)}
                      placeholder="Enter hourly rate"
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${isDarkTheme ? 'text-white' : 'text-gray-700'}`}>
                      Status
                    </label>
                    <div className="flex items-center space-x-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="is_active"
                          checked={formData.is_active}
                          onChange={() => handleInputChange('is_active', true)}
                          className="mr-2"
                        />
                        <span className={isDarkTheme ? 'text-white' : 'text-gray-700'}>Active</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="is_active"
                          checked={!formData.is_active}
                          onChange={() => handleInputChange('is_active', false)}
                          className="mr-2"
                        />
                        <span className={isDarkTheme ? 'text-white' : 'text-gray-700'}>Inactive</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkTheme ? 'text-white' : 'text-gray-700'}`}>
                    Notes
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    placeholder="Enter any additional notes"
                    rows={3}
                    className={`w-full px-4 py-3 rounded-lg border transition-all duration-300 ${
                      isDarkTheme
                        ? 'bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400'
                        : 'bg-white/80 border-gray-200 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                    } backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-blue-500/20 resize-none`}
                  />
                </div>
              </div>
            )}

            {/* Step 3: Access & Security */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className={`p-4 rounded-lg border ${
                    isDarkTheme ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'
                  }`}>
                    <h4 className={`font-medium mb-3 ${isDarkTheme ? 'text-white' : 'text-gray-900'}`}>
                      <Settings className="w-4 h-4 inline mr-2" />
                      System Access
                    </h4>
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.can_login_pos}
                          onChange={(e) => handleInputChange('can_login_pos', e.target.checked)}
                          className="mr-3"
                        />
                        <span className={isDarkTheme ? 'text-white' : 'text-gray-700'}>
                          POS System Access
                        </span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.can_login_admin}
                          onChange={(e) => handleInputChange('can_login_admin', e.target.checked)}
                          className="mr-3"
                        />
                        <span className={isDarkTheme ? 'text-white' : 'text-gray-700'}>
                          Admin Dashboard Access
                        </span>
                      </label>
                    </div>
                  </div>

                  <div className={`p-4 rounded-lg border ${
                    isDarkTheme ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'
                  }`}>
                    <h4 className={`font-medium mb-3 ${isDarkTheme ? 'text-white' : 'text-gray-900'}`}>
                      <Lock className="w-4 h-4 inline mr-2" />
                      Security PIN
                    </h4>
                    <div className="space-y-3">
                      <div className="relative">
                        <GlassInput
                          type={showPin ? "text" : "password"}
                          value={formData.pin}
                          onChange={(e) => handleInputChange('pin', e.target.value)}
                          placeholder="Enter 4-6 digit PIN"
                          disabled={!formData.can_login_pos}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPin(!showPin)}
                          className={`absolute right-3 top-1/2 transform -translate-y-1/2 ${
                            isDarkTheme ? 'text-white/60 hover:text-white' : 'text-gray-400 hover:text-gray-600'
                          }`}
                          disabled={!formData.can_login_pos}
                        >
                          {showPin ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                      <GlassButton
                        type="button"
                        variant="secondary"
                        size="small"
                        onClick={generateRandomPin}
                        disabled={!formData.can_login_pos}
                        className="w-full"
                      >
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Generate New PIN
                      </GlassButton>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="glass-modal-footer">
          <div className="flex justify-between">
            <div className="flex space-x-3">
              {currentStep > 1 && (
                <GlassButton
                  type="button"
                  variant="secondary"
                  onClick={prevStep}
                >
                  Previous
                </GlassButton>
              )}
            </div>
            
            <div className="flex space-x-3">
              <GlassButton
                type="button"
                variant="secondary"
                onClick={onClose}
              >
                Cancel
              </GlassButton>
              
              {currentStep < 3 ? (
                <GlassButton
                  type="button"
                  onClick={nextStep}
                >
                  Next
                </GlassButton>
              ) : (
                <GlassButton
                  type="submit"
                  disabled={loading}
                  className="min-w-[120px]"
                >
                  {loading ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Update Staff
                    </>
                  )}
                </GlassButton>
              )}
            </div>
          </div>
        </div>
      </form>
    </GlassModal>
  )
}

export default EditStaffModal