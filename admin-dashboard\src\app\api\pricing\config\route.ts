import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';

interface PricingConfig {
  pickup: {
    enabled: boolean;
    discountPercentage: number;
    estimatedTime: { min: number; max: number };
  };
  delivery: {
    enabled: boolean;
    zones: any[];
    defaultFee: number;
    freeDeliveryThreshold: number | null;
  };
  general: {
    taxRate: number;
    serviceFeeRate: number;
    currency: string;
  };
  [key: string]: any; // Allow dynamic access
}

// GET /api/pricing/config - Get pricing configuration settings
export async function GET(request: NextRequest) {
  const supabase = createServerSupabaseClient();
  
  try {
    const { searchParams } = new URL(request.url);
    const orderType = searchParams.get('orderType');
    const branchId = searchParams.get('branchId');

    if (orderType && !['pickup', 'delivery'].includes(orderType)) {
      return NextResponse.json(
        { error: 'orderType must be "pickup" or "delivery"' },
        { status: 400 }
      );
    }

    // Fetch all pricing-related settings
    const { data: settings, error } = await supabase
      .from('restaurant_settings')
      .select('setting_key, setting_value, description')
      .or(`setting_key.like.%pickup%,setting_key.like.%delivery%,setting_key.like.%tax%,setting_key.like.%fee%`);

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch pricing configuration' },
        { status: 500 }
      );
    }

    // Get delivery zones if needed
    let deliveryZones = [];
    if (!orderType || orderType === 'delivery') {
      const { data: zones } = await supabase
        .from('delivery_zones')
        .select('*')
        .eq('is_active', true)
        .order('priority', { ascending: false });
      
      deliveryZones = zones || [];
    }

    // Organize settings by category
    const config: PricingConfig = {
      pickup: {
        enabled: true,
        discountPercentage: 0.05, // Default 5%
        estimatedTime: { min: 10, max: 20 }
      },
      delivery: {
        enabled: true,
        zones: deliveryZones,
        defaultFee: 2.50,
        freeDeliveryThreshold: null
      },
      general: {
        taxRate: 0.10, // Default 10%
        serviceFeeRate: 0,
        currency: 'EUR'
      }
    };

    // Override with actual settings
    (settings || []).forEach(setting => {
      const value = parseFloat(setting.setting_value) || setting.setting_value;
      
      switch (setting.setting_key) {
        case 'pickup_discount_percentage':
          config.pickup.discountPercentage = value;
          break;
        case 'pickup_enabled':
          config.pickup.enabled = value === 'true' || value === true;
          break;
        case 'pickup_time_min':
          config.pickup.estimatedTime.min = value;
          break;
        case 'pickup_time_max':
          config.pickup.estimatedTime.max = value;
          break;
        case 'delivery_enabled':
          config.delivery.enabled = value === 'true' || value === true;
          break;
        case 'delivery_default_fee':
          config.delivery.defaultFee = value;
          break;
        case 'delivery_free_threshold':
          config.delivery.freeDeliveryThreshold = value;
          break;
        case 'tax_rate':
          config.general.taxRate = value;
          break;
        case 'service_fee_rate':
          config.general.serviceFeeRate = value;
          break;
        case 'currency':
          config.general.currency = value;
          break;
      }
    });

    // Return specific order type config or full config
    const response = orderType ? config[orderType] : config;

    return NextResponse.json({
      success: true,
      orderType,
      configuration: response
    });

  } catch (error) {
    console.error('Error fetching pricing config:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/pricing/config - Update pricing configuration
export async function PUT(request: NextRequest) {
  const supabase = createServerSupabaseClient();
  
  try {
    const body = await request.json();
    const { orderType, configuration, branchId } = body;

    if (!orderType || !configuration) {
      return NextResponse.json(
        { error: 'orderType and configuration are required' },
        { status: 400 }
      );
    }

    if (!['pickup', 'delivery', 'general'].includes(orderType)) {
      return NextResponse.json(
        { error: 'orderType must be "pickup", "delivery", or "general"' },
        { status: 400 }
      );
    }

    // Prepare settings to update/insert
    const settingsToUpsert = [];

    if (orderType === 'pickup') {
      if (configuration.discountPercentage !== undefined) {
        settingsToUpsert.push({
          setting_key: 'pickup_discount_percentage',
          setting_value: configuration.discountPercentage.toString(),
          description: 'Discount percentage for pickup orders'
        });
      }
      if (configuration.enabled !== undefined) {
        settingsToUpsert.push({
          setting_key: 'pickup_enabled',
          setting_value: configuration.enabled.toString(),
          description: 'Enable/disable pickup orders'
        });
      }
      if (configuration.estimatedTime) {
        if (configuration.estimatedTime.min !== undefined) {
          settingsToUpsert.push({
            setting_key: 'pickup_time_min',
            setting_value: configuration.estimatedTime.min.toString(),
            description: 'Minimum pickup time in minutes'
          });
        }
        if (configuration.estimatedTime.max !== undefined) {
          settingsToUpsert.push({
            setting_key: 'pickup_time_max',
            setting_value: configuration.estimatedTime.max.toString(),
            description: 'Maximum pickup time in minutes'
          });
        }
      }
    } else if (orderType === 'delivery') {
      if (configuration.enabled !== undefined) {
        settingsToUpsert.push({
          setting_key: 'delivery_enabled',
          setting_value: configuration.enabled.toString(),
          description: 'Enable/disable delivery orders'
        });
      }
      if (configuration.defaultFee !== undefined) {
        settingsToUpsert.push({
          setting_key: 'delivery_default_fee',
          setting_value: configuration.defaultFee.toString(),
          description: 'Default delivery fee when no zone matches'
        });
      }
      if (configuration.freeDeliveryThreshold !== undefined) {
        settingsToUpsert.push({
          setting_key: 'delivery_free_threshold',
          setting_value: configuration.freeDeliveryThreshold?.toString() || '',
          description: 'Order amount for free delivery'
        });
      }
    } else if (orderType === 'general') {
      if (configuration.taxRate !== undefined) {
        settingsToUpsert.push({
          setting_key: 'tax_rate',
          setting_value: configuration.taxRate.toString(),
          description: 'Tax rate percentage'
        });
      }
      if (configuration.serviceFeeRate !== undefined) {
        settingsToUpsert.push({
          setting_key: 'service_fee_rate',
          setting_value: configuration.serviceFeeRate.toString(),
          description: 'Service fee rate percentage'
        });
      }
      if (configuration.currency !== undefined) {
        settingsToUpsert.push({
          setting_key: 'currency',
          setting_value: configuration.currency,
          description: 'Currency code'
        });
      }
    }

    if (settingsToUpsert.length === 0) {
      return NextResponse.json(
        { error: 'No valid configuration provided' },
        { status: 400 }
      );
    }

    // Upsert settings (insert or update if exists)
    const { data, error } = await supabase
      .from('restaurant_settings')
      .upsert(settingsToUpsert, { 
        onConflict: 'setting_key',
        ignoreDuplicates: false 
      })
      .select();

    if (error) {
      console.error('Error updating pricing configuration:', error);
      return NextResponse.json(
        { error: 'Failed to update pricing configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      orderType,
      updatedSettings: data,
      message: `${orderType} pricing configuration updated successfully`
    });

  } catch (error) {
    console.error('Error updating pricing config:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/pricing/config - Create new pricing rule (for future advanced rules)
export async function POST(request: NextRequest) {
  const supabase = createServerSupabaseClient();
  
  try {
    const body = await request.json();
    const { 
      name,
      orderType,
      conditions,
      pricing,
      isActive = true,
      priority = 1
    } = body;

    // This is for future implementation of advanced pricing rules
    // For now, we'll store it as a JSON configuration in restaurant_settings
    
    const ruleKey = `pricing_rule_${name.toLowerCase().replace(/\s+/g, '_')}`;
    const ruleConfig = {
      name,
      orderType,
      conditions,
      pricing,
      isActive,
      priority,
      createdAt: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('restaurant_settings')
      .insert([{
        setting_key: ruleKey,
        setting_value: JSON.stringify(ruleConfig),
        description: `Advanced pricing rule: ${name}`
      }])
      .select();

    if (error) {
      return NextResponse.json(
        { error: 'Failed to create pricing rule' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      rule: data[0],
      message: 'Pricing rule created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating pricing rule:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}