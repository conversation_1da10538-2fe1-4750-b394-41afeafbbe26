import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    const terminalId = searchParams.get('terminal_id')
    const status = searchParams.get('status') || 'unresolved'
    const limit = parseInt(searchParams.get('limit') || '50')

    // Get sync conflicts from the sync history (failed syncs)
    let query = supabase
      .from('pos_settings_sync_history')
      .select('*')
      .eq('sync_status', 'failed')
      .order('synced_at', { ascending: false })
      .limit(limit)

    if (terminalId) {
      query = query.eq('terminal_id', terminalId)
    }

    const { data: conflicts, error } = await query

    if (error) {
      console.error('Error fetching sync conflicts:', error)
      return NextResponse.json(
        { error: 'Failed to fetch sync conflicts' },
        { status: 500 }
      )
    }

    // Enrich conflicts with additional details
    const enrichedConflicts = await Promise.all(
      (conflicts || []).map(async (conflict) => {
        // Get ingredient details - assume failed syncs are inventory related
        let resourceDetails = null
        if (conflict.config_id) {
          const { data: ingredient } = await supabase
            .from('ingredients')
            .select('id, name, stock_quantity, is_available, category_id')
            .eq('id', conflict.config_id)
            .single()

          resourceDetails = ingredient
        }

        // Get terminal details
        let terminalDetails = null
        if (conflict.terminal_id) {
          const { data: terminal } = await supabase
            .from('pos_terminals')
            .select('terminal_id, name, location, status')
            .eq('terminal_id', conflict.terminal_id)
            .single()
          
          terminalDetails = terminal
        }

        // Determine conflict type and severity based on available data
        const conflictType = 'sync_failure'
        const severity = 'medium'

        return {
          id: conflict.id,
          conflict_type: conflictType,
          severity,
          resource_type: 'inventory_item',
          resource_id: conflict.config_id,
          resource_details: resourceDetails,
          terminal_id: conflict.terminal_id,
          terminal_details: terminalDetails,
          conflict_description: conflict.error_message || 'Sync operation failed',
          local_data: null,
          remote_data: null,
          suggested_resolution: 'retry_sync',
          created_at: conflict.synced_at,
          last_attempt: conflict.synced_at,
          attempt_count: 1,
          status: 'unresolved'
        }
      })
    )

    // Filter by status if specified
    const filteredConflicts = status === 'all' 
      ? enrichedConflicts 
      : enrichedConflicts.filter(conflict => conflict.status === status)

    // Calculate summary statistics
    const totalConflicts = filteredConflicts.length
    const unresolvedConflicts = filteredConflicts.filter(c => c.status === 'unresolved').length
    const resolvedConflicts = filteredConflicts.filter(c => c.status === 'resolved').length
    const highSeverityConflicts = filteredConflicts.filter(c => c.severity === 'high').length

    // Group by conflict type
    const conflictsByType = filteredConflicts.reduce((acc, conflict) => {
      acc[conflict.conflict_type] = (acc[conflict.conflict_type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // Group by terminal
    const conflictsByTerminal = filteredConflicts.reduce((acc, conflict) => {
      const terminalId = conflict.terminal_id || 'unknown'
      acc[terminalId] = (acc[terminalId] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return NextResponse.json({
      conflicts: filteredConflicts,
      summary: {
        total: totalConflicts,
        unresolved: unresolvedConflicts,
        resolved: resolvedConflicts,
        high_severity: highSeverityConflicts,
        resolution_rate: totalConflicts > 0 ? (resolvedConflicts / totalConflicts) * 100 : 0
      },
      analytics: {
        by_type: conflictsByType,
        by_terminal: conflictsByTerminal,
        most_common_type: Object.keys(conflictsByType).reduce((a, b) => 
          conflictsByType[a] > conflictsByType[b] ? a : b, 'none'
        )
      },
      filters: {
        terminal_id: terminalId,
        status,
        limit
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Inventory sync conflicts error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { conflict_id, resolution_action, resolution_data } = await request.json()

    if (!conflict_id || !resolution_action) {
      return NextResponse.json(
        { error: 'conflict_id and resolution_action are required' },
        { status: 400 }
      )
    }

    // Get the conflict details
    const { data: conflict, error: conflictError } = await supabase
      .from('pos_settings_sync_history')
      .select('*')
      .eq('id', conflict_id)
      .single()

    if (conflictError || !conflict) {
      return NextResponse.json(
        { error: 'Conflict not found' },
        { status: 404 }
      )
    }

    let updateResult = { success: false, message: '' }

    switch (resolution_action) {
      case 'accept_local':
        // Accept local data as authoritative
        updateResult = await resolveWithLocalData(supabase, conflict, resolution_data)
        break

      case 'accept_remote':
        // Accept remote data as authoritative
        updateResult = await resolveWithRemoteData(supabase, conflict, resolution_data)
        break

      case 'manual_merge':
        // Apply manually merged data
        updateResult = await resolveWithMergedData(supabase, conflict, resolution_data)
        break

      case 'ignore':
        // Mark conflict as resolved but take no action
        updateResult = await ignoreConflict(supabase, conflict)
        break

      default:
        return NextResponse.json(
          { error: 'Invalid resolution_action. Must be one of: accept_local, accept_remote, manual_merge, ignore' },
          { status: 400 }
        )
    }

    if (updateResult.success) {
      // Update conflict status
      await supabase
        .from('pos_settings_sync_history')
        .update({
          sync_status: 'success',
          error_message: `Resolved: ${resolution_action} at ${new Date().toISOString()}`
        })
        .eq('id', conflict_id)
    }

    return NextResponse.json({
      success: updateResult.success,
      message: updateResult.message,
      conflict_id,
      resolution_action,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Inventory sync conflicts POST error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function resolveWithLocalData(supabase: any, conflict: any, resolutionData: any) {
  // Implementation would depend on the specific conflict type
  // For now, return a mock success
  return { success: true, message: 'Conflict resolved with local data' }
}

async function resolveWithRemoteData(supabase: any, conflict: any, resolutionData: any) {
  // Implementation would depend on the specific conflict type
  // For now, return a mock success
  return { success: true, message: 'Conflict resolved with remote data' }
}

async function resolveWithMergedData(supabase: any, conflict: any, resolutionData: any) {
  // Implementation would depend on the specific conflict type
  // For now, return a mock success
  return { success: true, message: 'Conflict resolved with merged data' }
}

async function ignoreConflict(supabase: any, conflict: any) {
  return { success: true, message: 'Conflict marked as ignored' }
}
