'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { AlertTriangle, CheckCircle, XCircle, RefreshCw, Server, Shield, Settings, Clock, Download, Upload } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { systemAdminService, type AppStatus, type FeatureFlag, type MaintenanceMode } from '@/lib/system-admin';

export default function SystemAdminPage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // App Status State
  const [appStatuses, setAppStatuses] = useState<AppStatus[]>([]);

  // Feature Flags State
  const [featureFlags, setFeatureFlags] = useState<FeatureFlag[]>([]);

  // Maintenance Mode State
  const [maintenanceModes, setMaintenanceModes] = useState<MaintenanceMode[]>([]);

  // Security Settings State
  const [securitySettings, setSecuritySettings] = useState({
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    requireTwoFactor: true,
    passwordMinLength: 8,
    apiRateLimit: 1000,
    enableAuditLog: true
  });

  // Backup Settings State
  const [backupSettings, setBackupSettings] = useState({
    frequency: 'daily',
    retention: 30,
    autoBackup: true,
    lastBackup: '2024-01-15 02:00:00',
    nextBackup: '2024-01-16 02:00:00'
  });

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      await Promise.all([
        loadAppStatuses(),
        loadFeatureFlags(),
        loadMaintenanceModes()
      ]);
      
      // Show a success notice when database is connected
      toast({
        title: "System Admin Ready",
        description: "Connected to Supabase database. All system administration features are now available.",
        variant: "default",
      });
    };
    
    loadData();
  }, []);

  const loadAppStatuses = async () => {
    try {
      const statuses = await systemAdminService.getAppStatuses();
      setAppStatuses(statuses);
    } catch (error) {
      console.error('Error loading app statuses:', error);
    }
  };

  const loadFeatureFlags = async () => {
    try {
      const flags = await systemAdminService.getFeatureFlags();
      setFeatureFlags(flags);
    } catch (error) {
      console.error('Error loading feature flags:', error);
    }
  };

  const loadMaintenanceModes = async () => {
    try {
      const modes = await systemAdminService.getMaintenanceModes();
      setMaintenanceModes(modes);
    } catch (error) {
      console.error('Error loading maintenance modes:', error);
    }
  };

  const refreshAppStatus = async () => {
    setRefreshing(true);
    try {
      await loadAppStatuses();
      
      toast({
        title: "Status Updated",
        description: "App statuses have been refreshed successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to refresh app statuses.",
        variant: "destructive",
      });
    } finally {
      setRefreshing(false);
    }
  };

  const toggleFeatureFlag = async (flagId: string) => {
    try {
      const flag = featureFlags.find(f => f.id === flagId);
      if (!flag) return;

      await systemAdminService.updateFeatureFlag(flagId, { enabled: !flag.enabled });
      await loadFeatureFlags();
      
      toast({
        title: "Feature Flag Updated",
        description: `Feature flag has been ${flag.enabled ? 'disabled' : 'enabled'}.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update feature flag.",
        variant: "destructive",
      });
    }
  };

  const toggleMaintenanceMode = async (platform: string) => {
    try {
      const mode = maintenanceModes.find(m => m.platform === platform);
      if (!mode) return;

      await systemAdminService.updateMaintenanceMode(platform, { enabled: !mode.enabled });
      await loadMaintenanceModes();
      
      toast({
        title: "Maintenance Mode Updated",
        description: `Maintenance mode ${mode.enabled ? 'disabled' : 'enabled'} for ${platform}.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update maintenance mode.",
        variant: "destructive",
      });
    }
  };

  const triggerBackup = async () => {
    setLoading(true);
    try {
      await systemAdminService.triggerBackup('manual');
      
      setBackupSettings(prev => ({
        ...prev,
        lastBackup: new Date().toISOString().replace('T', ' ').slice(0, 19)
      }));
      
      toast({
        title: "Backup Successful",
        description: "System backup has been completed successfully.",
      });
    } catch (error) {
      toast({
        title: "Backup Failed",
        description: "Failed to create system backup.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const pushConfigurationUpdate = async (platform: string) => {
    setLoading(true);
    try {
      await systemAdminService.pushConfigurationUpdate(platform);
      
      toast({
        title: "Configuration Pushed",
        description: `Configuration updates have been pushed to ${platform}.`,
      });
    } catch (error) {
      toast({
        title: "Push Failed",
        description: `Failed to push configuration to ${platform}.`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'offline':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'maintenance':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <XCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      online: 'default',
      offline: 'destructive',
      maintenance: 'secondary'
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const averageHealth = appStatuses.length > 0 
    ? (appStatuses.reduce((sum, app) => sum + app.health_score, 0) / appStatuses.length).toFixed(2)
    : '0';

  const onlineApps = appStatuses.filter(app => app.status === 'online').length;
  const maintenanceApps = appStatuses.filter(app => app.status === 'maintenance').length;
  const enabledFlags = featureFlags.filter(flag => flag.enabled).length;

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Administration</h1>
          <p className="text-muted-foreground">
            Manage app versions, feature flags, maintenance modes, and system configurations
          </p>
        </div>
        <Button 
          onClick={refreshAppStatus} 
          disabled={refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh Status
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="versions">App Versions</TabsTrigger>
          <TabsTrigger value="features">Feature Flags</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="backups">Backups</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Apps</CardTitle>
                <Server className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{appStatuses.length}</div>
                <p className="text-xs text-muted-foreground">
                  {onlineApps} online, {maintenanceApps} maintenance
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">System Health</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{averageHealth}%</div>
                <p className="text-xs text-muted-foreground">
                  Average across all platforms
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Features</CardTitle>
                <Settings className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{enabledFlags}/{featureFlags.length}</div>
                <p className="text-xs text-muted-foreground">
                  Feature flags enabled
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Last Backup</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">13h</div>
                <p className="text-xs text-muted-foreground">
                  ago (scheduled daily)
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>System Health Monitor</CardTitle>
                <CardDescription>Real-time health status of all platforms</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {appStatuses.map((app) => (
                  <div key={app.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(app.status)}
                      <div>
                        <p className="font-medium">{app.name}</p>
                        <p className="text-sm text-muted-foreground">v{app.version}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{app.health_score}%</p>
                      <Progress value={app.health_score} className="w-16 h-2" />
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common system administration tasks</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={triggerBackup} 
                  disabled={loading}
                  className="w-full justify-start"
                  variant="outline"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Trigger Manual Backup
                </Button>
                <Button 
                  onClick={() => pushConfigurationUpdate('all')} 
                  disabled={loading}
                  className="w-full justify-start"
                  variant="outline"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Push Config to All Apps
                </Button>
                <Button 
                  onClick={refreshAppStatus} 
                  disabled={refreshing}
                  className="w-full justify-start"
                  variant="outline"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                  Refresh All Status
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="versions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Application Versions</CardTitle>
              <CardDescription>
                Manage and monitor versions across all platforms
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {appStatuses.map((app) => (
                  <div key={app.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      {getStatusIcon(app.status)}
                      <div>
                        <h3 className="font-medium">{app.name}</h3>
                        <p className="text-sm text-muted-foreground">{app.url}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <p className="font-medium">v{app.version}</p>
                        <p className="text-sm text-muted-foreground">
                          Updated: {app.last_update}
                        </p>
                      </div>
                      {getStatusBadge(app.status)}
                      <Button 
                        size="sm" 
                        onClick={() => pushConfigurationUpdate(app.platform)}
                        disabled={loading}
                      >
                        <Upload className="h-4 w-4 mr-1" />
                        Push Config
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="features" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Feature Flags</CardTitle>
              <CardDescription>
                Enable or disable features across different platforms
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {featureFlags.map((flag) => (
                  <div key={flag.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <h3 className="font-medium">{flag.name}</h3>
                        <Badge variant={flag.enabled ? 'default' : 'secondary'}>
                          {flag.enabled ? 'Enabled' : 'Disabled'}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {flag.description}
                      </p>
                      <div className="flex gap-1 mt-2">
                        {flag.platforms.map((platform) => (
                          <Badge key={platform} variant="outline" className="text-xs">
                            {platform}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <Switch
                      checked={flag.enabled}
                      onCheckedChange={() => toggleFeatureFlag(flag.id)}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Maintenance Mode</CardTitle>
              <CardDescription>
                Control maintenance modes for each platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {maintenanceModes.map((mode) => (
                  <div key={mode.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <h3 className="font-medium capitalize">{mode.platform}</h3>
                        <Badge variant={mode.enabled ? 'destructive' : 'default'}>
                          {mode.enabled ? 'Maintenance' : 'Active'}
                        </Badge>
                      </div>
                      <Switch
                        checked={mode.enabled}
                        onCheckedChange={() => toggleMaintenanceMode(mode.platform)}
                      />
                    </div>
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor={`message-${mode.platform}`}>Maintenance Message</Label>
                        <Textarea
                          id={`message-${mode.platform}`}
                          value={mode.message}
                          onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                            setMaintenanceModes(prev => prev.map(m => 
                              m.platform === mode.platform 
                                ? { ...m, message: e.target.value }
                                : m
                            ));
                          }}
                          placeholder="Enter maintenance message..."
                          rows={2}
                        />
                      </div>
                      {mode.scheduled_start && mode.scheduled_end && (
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <Label>Scheduled Start</Label>
                            <Input 
                              type="datetime-local" 
                              value={mode.scheduled_start}
                              onChange={(e) => {
                                setMaintenanceModes(prev => prev.map(m => 
                                  m.platform === mode.platform 
                                    ? { ...m, scheduled_start: e.target.value }
                                    : m
                                ));
                              }}
                            />
                          </div>
                          <div>
                            <Label>Scheduled End</Label>
                            <Input 
                              type="datetime-local" 
                              value={mode.scheduled_end}
                              onChange={(e) => {
                                setMaintenanceModes(prev => prev.map(m => 
                                  m.platform === mode.platform 
                                    ? { ...m, scheduled_end: e.target.value }
                                    : m
                                ));
                              }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Configure security parameters for all applications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
                  <Input
                    id="session-timeout"
                    type="number"
                    value={securitySettings.sessionTimeout}
                    onChange={(e) => setSecuritySettings(prev => ({
                      ...prev,
                      sessionTimeout: parseInt(e.target.value)
                    }))}
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="max-login">Max Login Attempts</Label>
                  <Input
                    id="max-login"
                    type="number"
                    value={securitySettings.maxLoginAttempts}
                    onChange={(e) => setSecuritySettings(prev => ({
                      ...prev,
                      maxLoginAttempts: parseInt(e.target.value)
                    }))}
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="password-length">Min Password Length</Label>
                  <Input
                    id="password-length"
                    type="number"
                    value={securitySettings.passwordMinLength}
                    onChange={(e) => setSecuritySettings(prev => ({
                      ...prev,
                      passwordMinLength: parseInt(e.target.value)
                    }))}
                  />
                </div>
                <div className="space-y-3">
                  <Label htmlFor="api-rate-limit">API Rate Limit (req/hour)</Label>
                  <Input
                    id="api-rate-limit"
                    type="number"
                    value={securitySettings.apiRateLimit}
                    onChange={(e) => setSecuritySettings(prev => ({
                      ...prev,
                      apiRateLimit: parseInt(e.target.value)
                    }))}
                  />
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="two-factor">Require Two-Factor Authentication</Label>
                    <p className="text-sm text-muted-foreground">
                      Require 2FA for admin accounts
                    </p>
                  </div>
                  <Switch
                    id="two-factor"
                    checked={securitySettings.requireTwoFactor}
                    onCheckedChange={(checked: boolean) => setSecuritySettings(prev => ({
                      ...prev,
                      requireTwoFactor: checked
                    }))}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="audit-log">Enable Audit Logging</Label>
                    <p className="text-sm text-muted-foreground">
                      Log all administrative actions
                    </p>
                  </div>
                  <Switch
                    id="audit-log"
                    checked={securitySettings.enableAuditLog}
                    onCheckedChange={(checked: boolean) => setSecuritySettings(prev => ({
                      ...prev,
                      enableAuditLog: checked
                    }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="backups" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Backup Management</CardTitle>
              <CardDescription>
                Configure automated backups and system recovery
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="backup-frequency">Backup Frequency</Label>
                  <Select 
                    value={backupSettings.frequency}
                    onValueChange={(value: string) => setBackupSettings(prev => ({
                      ...prev,
                      frequency: value
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-3">
                  <Label htmlFor="retention">Retention Period (days)</Label>
                  <Input
                    id="retention"
                    type="number"
                    value={backupSettings.retention}
                    onChange={(e) => setBackupSettings(prev => ({
                      ...prev,
                      retention: parseInt(e.target.value)
                    }))}
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="auto-backup">Automatic Backups</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable scheduled automatic backups
                  </p>
                </div>
                <Switch
                  id="auto-backup"
                  checked={backupSettings.autoBackup}
                  onCheckedChange={(checked: boolean) => setBackupSettings(prev => ({
                    ...prev,
                    autoBackup: checked
                  }))}
                />
              </div>

              <div className="space-y-3">
                <h4 className="font-medium">Backup Status</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm font-medium">Last Backup</p>
                    <p className="text-lg">{backupSettings.lastBackup}</p>
                  </div>
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm font-medium">Next Scheduled</p>
                    <p className="text-lg">{backupSettings.nextBackup}</p>
                  </div>
                </div>
              </div>

              <div className="flex gap-3">
                <Button 
                  onClick={triggerBackup}
                  disabled={loading}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  {loading ? 'Creating Backup...' : 'Create Backup Now'}
                </Button>
                <Button variant="outline">
                  <Upload className="h-4 w-4 mr-2" />
                  Restore from Backup
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}