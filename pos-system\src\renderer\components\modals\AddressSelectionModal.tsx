import React, { useState } from 'react';
import { X } from 'lucide-react';
import { useTheme } from '../../contexts/theme-context';
import { AddressSelectionCard } from '../forms/AddressSelectionCard';

interface Address {
  id: string;
  street: string;
  postal_code: string;
  floor?: string;
  notes?: string;
  delivery_instructions?: string;
}

interface Customer {
  id: string;
  name: string;
  phone_number: string;
  email?: string;
  addresses?: Address[];
}

interface AddressSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer: Customer;
  onAddressSelected: (customer: Customer, address: Address) => void;
  onAddNewAddress?: (customer: Customer) => void;
}

export const AddressSelectionModal: React.FC<AddressSelectionModalProps> = ({
  isOpen,
  onClose,
  customer,
  onAddressSelected,
  onAddNewAddress
}) => {
  const { resolvedTheme } = useTheme();

  const handleAddressSelect = (address: Address) => {
    onAddressSelected(customer, address);
    onClose();
  };

  const handleAddNewAddress = () => {
    onAddNewAddress?.(customer);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className={`w-full max-w-4xl max-h-[90vh] rounded-3xl shadow-2xl border transform transition-all duration-300 overflow-hidden ${
        resolvedTheme === 'dark'
          ? 'bg-gray-800/90 border-gray-700/50 backdrop-blur-xl'
          : 'bg-white/90 border-gray-200/50 backdrop-blur-xl'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200/20">
          <div>
            <h2 className={`text-2xl font-bold ${
              resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              Select Delivery Address
            </h2>
            <p className={`text-sm mt-1 ${
              resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
            }`}>
              Customer: {customer.name} • {customer.phone_number}
            </p>
          </div>
          <button
            onClick={onClose}
            className={`p-2 rounded-xl transition-all duration-200 ${
              resolvedTheme === 'dark'
                ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700/50'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100/50'
            }`}
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Address List */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {customer.addresses?.map((address) => (
              <div key={address.id} onClick={() => handleAddressSelect(address)}>
                <AddressSelectionCard
                  address={address}
                  customer={customer}
                  className="cursor-pointer hover:scale-[1.02] transition-transform duration-200"
                />
              </div>
            ))}
          </div>

          {/* Add New Address Option */}
          {onAddNewAddress && (
            <div className="mt-6 pt-6 border-t border-gray-200/20">
              <button
                onClick={handleAddNewAddress}
                className={`w-full p-6 rounded-2xl border-2 border-dashed transition-all duration-300 hover:scale-[1.02] ${
                  resolvedTheme === 'dark'
                    ? 'border-gray-600/50 bg-gray-700/20 hover:bg-gray-700/40 hover:border-blue-500/50 text-gray-300'
                    : 'border-gray-300/50 bg-gray-100/20 hover:bg-gray-100/40 hover:border-blue-500/50 text-gray-600'
                } backdrop-blur-sm`}
              >
                <div className="flex flex-col items-center space-y-3">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    resolvedTheme === 'dark' ? 'bg-blue-600/20' : 'bg-blue-100/60'
                  }`}>
                    <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1">Add New Address</h3>
                    <p className="text-sm opacity-70">Create a new delivery address for this customer</p>
                  </div>
                </div>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 