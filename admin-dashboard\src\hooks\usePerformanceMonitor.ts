'use client';

import { useEffect, useRef, useState } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  componentMountTime: number;
  memoryUsage?: number;
  isSlowRender: boolean;
}

interface UsePerformanceMonitorOptions {
  componentName?: string;
  slowRenderThreshold?: number;
  enableMemoryMonitoring?: boolean;
  logToConsole?: boolean;
}

export const usePerformanceMonitor = (
  options: UsePerformanceMonitorOptions = {}
): PerformanceMetrics => {
  const {
    componentName = 'Component',
    slowRenderThreshold = 16, // 16ms for 60fps
    enableMemoryMonitoring = false,
    logToConsole = process.env.NODE_ENV === 'development',
  } = options;

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    componentMountTime: 0,
    isSlowRender: false,
  });

  const mountTimeRef = useRef<number>(0);
  const renderStartRef = useRef<number>(0);

  // Track component mount time
  useEffect(() => {
    mountTimeRef.current = performance.now();
    
    return () => {
      const mountTime = performance.now() - mountTimeRef.current;
      
      if (logToConsole) {
        console.log(`${componentName} total mount time: ${mountTime.toFixed(2)}ms`);
      }
    };
  }, [componentName, logToConsole]);

  // Track render performance
  useEffect(() => {
    renderStartRef.current = performance.now();
  });

  useEffect(() => {
    const renderTime = performance.now() - renderStartRef.current;
    const isSlowRender = renderTime > slowRenderThreshold;
    
    let memoryUsage: number | undefined;
    
    if (enableMemoryMonitoring && 'memory' in performance) {
      const memory = (performance as any).memory;
      memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // Convert to MB
    }

    const newMetrics: PerformanceMetrics = {
      renderTime,
      componentMountTime: performance.now() - mountTimeRef.current,
      memoryUsage,
      isSlowRender,
    };

    setMetrics(newMetrics);

    if (logToConsole) {
      if (isSlowRender) {
        console.warn(
          `🐌 Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`
        );
      }
      
      if (memoryUsage && memoryUsage > 50) {
        console.warn(
          `🧠 High memory usage in ${componentName}: ${memoryUsage.toFixed(2)}MB`
        );
      }
    }
  });

  return metrics;
};

// Hook for monitoring specific operations
export const useOperationTimer = (operationName: string) => {
  const startTimer = () => {
    const startTime = performance.now();
    
    return {
      end: () => {
        const duration = performance.now() - startTime;
        
        if (process.env.NODE_ENV === 'development') {
          console.log(`⏱️ ${operationName}: ${duration.toFixed(2)}ms`);
        }
        
        return duration;
      },
    };
  };

  return { startTimer };
};

// Hook for debouncing expensive operations
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Hook for throttling expensive operations
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const throttledCallback = useRef((...args: Parameters<T>) => {
    const now = Date.now();
    
    if (now - lastRun.current >= delay) {
      callback(...args);
      lastRun.current = now;
    } else {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = setTimeout(() => {
        callback(...args);
        lastRun.current = Date.now();
      }, delay - (now - lastRun.current));
    }
  });

  return throttledCallback.current as T;
};

export default usePerformanceMonitor;
