import { useState, useEffect, useCallback } from 'react';
import Constants from 'expo-constants';
import * as Device from 'expo-device';
import AdminDashboardService, { AppConfig } from '../services/adminDashboardService';

export const useAdminDashboard = () => {
  const [appConfig, setAppConfig] = useState<AppConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const adminService = AdminDashboardService.getInstance();

  // Load app configuration
  const loadAppConfig = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const config = await adminService.getAppConfig();
      setAppConfig(config);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load app configuration');
    } finally {
      setIsLoading(false);
    }
  }, [adminService]);

  // Send analytics event
  const sendAnalytics = useCallback(async (eventType: string, eventData: any) => {
    try {
      const deviceInfo = {
        platform: Device.osName || 'unknown',
        os_version: Device.osVersion || 'unknown',
        app_version: Constants.expoConfig?.version || '1.0.0',
        device_model: Device.modelName || 'unknown',
      };

      await adminService.sendAnalytics({
        event_type: eventType,
        event_data: eventData,
        timestamp: new Date().toISOString(),
        app_version: Constants.expoConfig?.version || '1.0.0',
        device_info: deviceInfo,
      });
    } catch (error) {
      console.error('Failed to send analytics:', error);
    }
  }, [adminService]);

  // Send crash report
  const sendCrashReport = useCallback(async (error: Error, additionalInfo?: any) => {
    try {
      const deviceInfo = {
        platform: Device.osName || 'unknown',
        os_version: Device.osVersion || 'unknown',
        app_version: Constants.expoConfig?.version || '1.0.0',
        device_model: Device.modelName || 'unknown',
        ...additionalInfo,
      };

      await adminService.sendCrashReport({
        error_message: error.message,
        stack_trace: error.stack || '',
        app_version: Constants.expoConfig?.version || '1.0.0',
        device_info: deviceInfo,
        timestamp: new Date().toISOString(),
      });
    } catch (err) {
      console.error('Failed to send crash report:', err);
    }
  }, [adminService]);

  // Send user feedback
  const sendUserFeedback = useCallback(async (
    userId: string,
    rating: number,
    comment: string,
    category: 'bug' | 'feature_request' | 'general' | 'complaint' | 'compliment'
  ) => {
    try {
      await adminService.sendUserFeedback({
        user_id: userId,
        rating,
        comment,
        category,
        app_version: Constants.expoConfig?.version || '1.0.0',
        timestamp: new Date().toISOString(),
      });
      return true;
    } catch (error) {
      console.error('Failed to send user feedback:', error);
      return false;
    }
  }, [adminService]);

  // Check for app updates
  const checkForUpdates = useCallback(async () => {
    try {
      const currentVersion = Constants.expoConfig?.version || '1.0.0';
      const updateInfo = await adminService.checkForUpdates(currentVersion);
      return updateInfo;
    } catch (error) {
      console.error('Failed to check for updates:', error);
      return {
        hasUpdate: false,
        latestVersion: Constants.expoConfig?.version || '1.0.0',
        isForced: false,
      };
    }
  }, [adminService]);

  // Check if app is in maintenance mode
  const isMaintenanceMode = useCallback(() => {
    return appConfig?.maintenance_mode || false;
  }, [appConfig]);

  // Check if feature is enabled
  const isFeatureEnabled = useCallback((feature: keyof AppConfig['features']) => {
    return appConfig?.features?.[feature] || false;
  }, [appConfig]);

  // Get theme colors
  const getThemeColors = useCallback(() => {
    return appConfig?.theme || {
      primary_color: '#D97706',
      secondary_color: '#F59E0B',
      accent_color: '#10B981',
      background_color: '#FFFFFF',
      text_color: '#111827',
    };
  }, [appConfig]);

  // Get business hours
  const getBusinessHours = useCallback(() => {
    return appConfig?.business_hours || null;
  }, [appConfig]);

  // Get contact info
  const getContactInfo = useCallback(() => {
    return appConfig?.contact_info || null;
  }, [appConfig]);

  // Get delivery settings
  const getDeliverySettings = useCallback(() => {
    return appConfig?.delivery_settings || null;
  }, [appConfig]);

  // Get payment methods
  const getPaymentMethods = useCallback(() => {
    return appConfig?.payment_methods || {
      cash: true,
      card: true,
      digital_wallet: false,
      bank_transfer: false,
    };
  }, [appConfig]);

  // Check if business is currently open
  const isBusinessOpen = useCallback(() => {
    if (!appConfig?.business_hours) return true;

    const now = new Date();
    const currentDay = now.toLocaleLowerCase() as keyof AppConfig['business_hours'];
    const daySchedule = appConfig.business_hours[currentDay];

    if (!daySchedule?.is_open) return false;

    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
    return currentTime >= daySchedule.open && currentTime <= daySchedule.close;
  }, [appConfig]);

  // Load configuration on mount
  useEffect(() => {
    loadAppConfig();
  }, [loadAppConfig]);

  return {
    appConfig,
    isLoading,
    error,
    loadAppConfig,
    sendAnalytics,
    sendCrashReport,
    sendUserFeedback,
    checkForUpdates,
    isMaintenanceMode,
    isFeatureEnabled,
    getThemeColors,
    getBusinessHours,
    getContactInfo,
    getDeliverySettings,
    getPaymentMethods,
    isBusinessOpen,
  };
};
