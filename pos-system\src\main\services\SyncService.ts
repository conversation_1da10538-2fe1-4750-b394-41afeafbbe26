import Database from 'better-sqlite3';
import { BaseService } from './BaseService';

// Database row interfaces
interface SyncQueueRow {
  id: string;
  table_name: string;
  record_id: string;
  operation: 'insert' | 'update' | 'delete';
  data: string; // JSON string
  created_at: string;
  attempts: number;
  last_attempt?: string;
  error_message?: string;
  count?: number; // For COUNT(*) queries
}

interface SyncStatsRow {
  count: number;
}

interface SyncFilter {
  tableName?: string;
  recordId?: string;
  operation?: string;
  maxAttempts?: number;
}

export interface SyncQueue {
  id: string;
  table_name: string;
  record_id: string;
  operation: 'insert' | 'update' | 'delete';
  data: string; // JSON string
  created_at: string;
  attempts: number;
  last_attempt?: string;
  error_message?: string;
}

export interface SyncResult {
  success: boolean;
  processed: number;
  failed: number;
  errors: string[];
}

export class SyncService extends BaseService {
  private maxRetries = 3;
  private retryDelay = 5000; // 5 seconds

  constructor(database: Database.Database) {
    super(database);
  }

  addToSyncQueue(
    tableName: string,
    recordId: string,
    operation: 'insert' | 'update' | 'delete',
    data: Record<string, unknown>
  ): void {
    this.executeTransaction(() => {
      const queueItem: SyncQueue = {
        id: this.generateId(),
        table_name: tableName,
        record_id: recordId,
        operation,
        data: JSON.stringify(data),
        created_at: this.getCurrentTimestamp(),
        attempts: 0
      };

      const stmt = this.db.prepare(`
        INSERT INTO sync_queue (
          id, table_name, record_id, operation, data, created_at, attempts
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      stmt.run(
        queueItem.id, queueItem.table_name, queueItem.record_id,
        queueItem.operation, queueItem.data, queueItem.created_at,
        queueItem.attempts
      );
    });
  }

  getPendingSyncItems(limit: number = 50): SyncQueue[] {
    const stmt = this.db.prepare(`
      SELECT * FROM sync_queue 
      WHERE attempts < ?
      ORDER BY created_at ASC
      LIMIT ?
    `);

    const rows = stmt.all(this.maxRetries, limit) as SyncQueueRow[];
    
    return rows.map(row => this.mapRowToSyncItem(row));
  }

  markSyncSuccess(syncId: string): void {
    this.executeTransaction(() => {
      const stmt = this.db.prepare('DELETE FROM sync_queue WHERE id = ?');
      stmt.run(syncId);
    });
  }

  markSyncFailed(syncId: string, errorMessage: string): void {
    this.executeTransaction(() => {
      const stmt = this.db.prepare(`
        UPDATE sync_queue SET 
          attempts = attempts + 1,
          last_attempt = ?,
          error_message = ?
        WHERE id = ?
      `);

      stmt.run(this.getCurrentTimestamp(), errorMessage, syncId);
    });
  }

  getFailedSyncItems(): SyncQueue[] {
    const stmt = this.db.prepare(`
      SELECT * FROM sync_queue 
      WHERE attempts >= ?
      ORDER BY created_at DESC
    `);

    const rows = stmt.all(this.maxRetries) as SyncQueueRow[];
    
    return rows.map(row => this.mapRowToSyncItem(row));
  }

  retrySyncItem(syncId: string): void {
    this.executeTransaction(() => {
      const stmt = this.db.prepare(`
        UPDATE sync_queue SET 
          attempts = 0,
          error_message = NULL
        WHERE id = ?
      `);

      stmt.run(syncId);
    });
  }

  retryAllFailedSyncs(): void {
    this.executeTransaction(() => {
      const stmt = this.db.prepare(`
        UPDATE sync_queue SET 
          attempts = 0,
          error_message = NULL
        WHERE attempts >= ?
      `);

      stmt.run(this.maxRetries);
    });
  }

  clearSyncQueue(): void {
    this.executeTransaction(() => {
      const stmt = this.db.prepare('DELETE FROM sync_queue');
      stmt.run();
    });
  }

  getSyncStats(): {
    pending: number;
    failed: number;
    total: number;
  } {
    const pendingStmt = this.db.prepare(`
      SELECT COUNT(*) as count FROM sync_queue WHERE attempts < ?
    `);
    const pending = (pendingStmt.get(this.maxRetries) as SyncStatsRow).count;

    const failedStmt = this.db.prepare(`
      SELECT COUNT(*) as count FROM sync_queue WHERE attempts >= ?
    `);
    const failed = (failedStmt.get(this.maxRetries) as SyncStatsRow).count;

    const totalStmt = this.db.prepare('SELECT COUNT(*) as count FROM sync_queue');
    const total = (totalStmt.get() as SyncStatsRow).count;

    return { pending, failed, total };
  }

  cleanupOldSyncItems(daysOld: number = 7): number {
    return this.executeTransaction(() => {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      const cutoffTimestamp = cutoffDate.toISOString();

      const stmt = this.db.prepare(`
        DELETE FROM sync_queue 
        WHERE created_at < ? AND attempts >= ?
      `);
      
      const result = stmt.run(cutoffTimestamp, this.maxRetries);
      return result.changes;
    });
  }

  getSyncHistory(tableName?: string, limit: number = 100): SyncQueue[] {
    let query = 'SELECT * FROM sync_queue';
    const params: (string | number)[] = [];

    if (tableName) {
      query += ' WHERE table_name = ?';
      params.push(tableName);
    }

    query += ' ORDER BY created_at DESC LIMIT ?';
    params.push(limit);

    const stmt = this.db.prepare(query);
    const rows = stmt.all(...params) as SyncQueueRow[];
    
    return rows.map(row => this.mapRowToSyncItem(row));
  }

  // Utility method to check if a record has pending sync operations
  hasPendingSync(tableName: string, recordId: string): boolean {
    const stmt = this.db.prepare(`
      SELECT COUNT(*) as count FROM sync_queue 
      WHERE table_name = ? AND record_id = ? AND attempts < ?
    `);

    const result = stmt.get(tableName, recordId, this.maxRetries) as SyncStatsRow | undefined;
    return result ? result.count > 0 : false;
  }

  // Get sync items for a specific record
  getRecordSyncItems(tableName: string, recordId: string): SyncQueue[] {
    const stmt = this.db.prepare(`
      SELECT * FROM sync_queue 
      WHERE table_name = ? AND record_id = ?
      ORDER BY created_at DESC
    `);

    const rows = stmt.all(tableName, recordId) as SyncQueueRow[];
    
    return rows.map(row => this.mapRowToSyncItem(row));
  }

  // Remove sync items for a specific record (useful when record is deleted)
  removeSyncItemsForRecord(tableName: string, recordId: string): number {
    return this.executeTransaction(() => {
      const stmt = this.db.prepare(`
        DELETE FROM sync_queue 
        WHERE table_name = ? AND record_id = ?
      `);
      
      const result = stmt.run(tableName, recordId);
      return result.changes;
    });
  }

  private mapRowToSyncItem(row: SyncQueueRow): SyncQueue {
    return {
      id: row.id,
      table_name: row.table_name,
      record_id: row.record_id,
      operation: row.operation,
      data: row.data,
      created_at: row.created_at,
      attempts: row.attempts,
      last_attempt: row.last_attempt,
      error_message: row.error_message
    };
  }
}