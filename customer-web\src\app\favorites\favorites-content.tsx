'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Heart, ShoppingCart, Trash2 } from 'lucide-react';
import { useFavorites } from '@/components/favorites/favorites-provider';
import { useCartContext } from '@/providers/cart-provider';
import { GlassCard, GlassButton } from '@/components/ui/glass-components';
import { Breadcrumb } from '@/components/navigation/breadcrumb';
import { EmptyState } from '@/components/ui/error-states';
import { OptimizedImage } from '@/components/ui/optimized-image';
import { FavoriteButton } from '@/components/favorites/favorite-button';
import { useNotificationHelpers } from '@/components/ui/notification-system';
import { cn } from '@/lib/utils';

export function FavoritesPageContent() {
  const router = useRouter();
  const { favorites, clearFavorites, favoritesCount } = useFavorites();
  const { addItem } = useCartContext();
  const { success } = useNotificationHelpers();

  const handleAddToCart = (item: any) => {
    addItem(item);
    success('Added to cart', `${item.name} has been added to your cart.`);
  };

  const handleClearAll = () => {
    if (window.confirm('Are you sure you want to remove all favorites?')) {
      clearFavorites();
    }
  };

  if (favoritesCount === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 p-4">
        <div className="max-w-6xl mx-auto">
          <Breadcrumb />
          
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">My Favorites</h1>
            <p className="text-gray-600">Save your favorite menu items for easy ordering</p>
          </div>

          <EmptyState
            title="No favorites yet"
            message="Start adding items to your favorites by clicking the heart icon on menu items."
            actionLabel="Browse Menu"
            onAction={() => router.push('/menu')}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 p-4">
      <div className="max-w-6xl mx-auto">
        <Breadcrumb />
        
        <div className="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">My Favorites</h1>
            <p className="text-gray-600">
              {favoritesCount} {favoritesCount === 1 ? 'item' : 'items'} in your favorites
            </p>
          </div>
          
          {favoritesCount > 0 && (
            <GlassButton
              variant="secondary"
              onClick={handleClearAll}
              className="flex items-center gap-2 text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
              Clear All
            </GlassButton>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {favorites.map((item) => (
            <GlassCard key={item.id} className="group relative overflow-hidden">
              <div className="relative">
                <OptimizedImage
                  src={item.image_url}
                  alt={item.name}
                  width={400}
                  height={250}
                  className="w-full h-48 object-cover"
                />
                
                <div className="absolute top-2 right-2">
                  <FavoriteButton item={item} size="md" variant="icon" />
                </div>
              </div>
              
              <div className="p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-primary transition-colors">
                  {item.name}
                </h3>
                
                {item.description && (
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                    {item.description}
                  </p>
                )}
                
                <div className="flex items-center justify-between">
                  <span className="text-xl font-bold text-primary">
                    ${item.price.toFixed(2)}
                  </span>
                  
                  <GlassButton
                    variant="primary"
                    size="sm"
                    onClick={() => handleAddToCart(item)}
                    className="flex items-center gap-2"
                  >
                    <ShoppingCart className="h-4 w-4" />
                    Add to Cart
                  </GlassButton>
                </div>
                
                <div className="mt-3 text-xs text-gray-500">
                  Added {item.addedAt.toLocaleDateString()}
                </div>
              </div>
            </GlassCard>
          ))}
        </div>
      </div>
    </div>
  );
}