import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  const supabase = createServerSupabaseClient()
  
  try {
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30d'
    
    // Calculate date range
    const now = new Date()
    const startDate = new Date()
    
    switch (timeRange) {
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        startDate.setDate(now.getDate() - 30)
    }

    // Get basic zone counts
    const [totalZonesResult, activeZonesResult] = await Promise.all([
      supabase
        .from('delivery_zones')
        .select('id', { count: 'exact' }),
      
      supabase
        .from('delivery_zones')
        .select('id', { count: 'exact' })
        .eq('is_active', true)
    ])

    // Get delivery analytics
    const { data: deliveryData } = await supabase
      .from('orders')
      .select('id, total_amount, delivery_time, delivery_zone_id, created_at')
      .eq('order_type', 'delivery')
      .gte('created_at', startDate.toISOString())
      .not('delivery_zone_id', 'is', null)

    const totalDeliveries = deliveryData?.length || 0
    const totalRevenue = deliveryData?.reduce((sum, order) => sum + (order.total_amount || 0), 0) || 0
    
    // Calculate average delivery time
    const deliveryTimes = deliveryData?.filter(o => o.delivery_time).map(o => o.delivery_time) || []
    const avgDeliveryTime = deliveryTimes.length > 0 
      ? deliveryTimes.reduce((a, b) => a + b, 0) / deliveryTimes.length 
      : 0

    // Get zone performance data
    const { data: zones } = await supabase
      .from('delivery_zones')
      .select('id, name')

    const zonePerformance = []
    
    if (zones && deliveryData) {
      for (const zone of zones) {
        const zoneOrders = deliveryData.filter(order => order.delivery_zone_id === zone.id)
        const zoneRevenue = zoneOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0)
        const zoneTimes = zoneOrders.filter(o => o.delivery_time).map(o => o.delivery_time)
        const avgZoneTime = zoneTimes.length > 0 
          ? zoneTimes.reduce((a, b) => a + b, 0) / zoneTimes.length 
          : 0

        if (zoneOrders.length > 0) {
          zonePerformance.push({
            zone_id: zone.id,
            zone_name: zone.name,
            total_orders: zoneOrders.length,
            total_revenue: zoneRevenue,
            avg_delivery_time: avgZoneTime,
            customer_satisfaction: Math.random() * 2 + 3, // Mock data for now
            peak_hours: [], // Could be calculated from order times
            popular_items: [] // Could be calculated from order items
          })
        }
      }
    }

    // Generate time analytics (orders by hour)
    const timeAnalytics = []
    if (deliveryData) {
      const hourlyData: { [key: number]: { orders: number; times: number[] } } = {}
      
      deliveryData.forEach(order => {
        const hour = new Date(order.created_at).getHours()
        if (!hourlyData[hour]) {
          hourlyData[hour] = { orders: 0, times: [] }
        }
        hourlyData[hour].orders++
        if (order.delivery_time) {
          hourlyData[hour].times.push(order.delivery_time)
        }
      })

      for (let hour = 0; hour < 24; hour++) {
        const data = hourlyData[hour] || { orders: 0, times: [] }
        const avgTime = data.times.length > 0 
          ? data.times.reduce((a, b) => a + b, 0) / data.times.length 
          : 0

        timeAnalytics.push({
          hour: hour.toString().padStart(2, '0'),
          orders: data.orders,
          avg_time: avgTime
        })
      }
    }

    // Generate mock heatmap data (in real implementation, this would be based on actual coordinates)
    const deliveryHeatMap = []
    if (zones) {
      for (const zone of zones.slice(0, 10)) { // Limit to 10 zones for performance
        // Mock coordinates around a central point (you'd use real zone coordinates)
        const baseLat = 37.7749 + (Math.random() - 0.5) * 0.1
        const baseLng = -122.4194 + (Math.random() - 0.5) * 0.1
        
        for (let i = 0; i < 20; i++) {
          deliveryHeatMap.push({
            lat: baseLat + (Math.random() - 0.5) * 0.02,
            lng: baseLng + (Math.random() - 0.5) * 0.02,
            intensity: Math.random() * 10
          })
        }
      }
    }

    // Compile analytics response
    const analytics = {
      total_zones: totalZonesResult.count || 0,
      active_zones: activeZonesResult.count || 0,
      total_deliveries: totalDeliveries,
      avg_delivery_time: avgDeliveryTime,
      total_revenue: totalRevenue,
      zone_performance: zonePerformance,
      delivery_heat_map: deliveryHeatMap,
      time_analytics: timeAnalytics,
      time_range: timeRange
    }

    return NextResponse.json(analytics)

  } catch (error) {
    console.error('Error fetching map analytics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch map analytics' },
      { status: 500 }
    )
  }
}