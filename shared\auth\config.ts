// Supabase Configuration for Creperie Authentication System

import { createClient } from '@supabase/supabase-js';
import type { Database } from './database.types';

// Supabase Configuration
export const SUPABASE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://voiwzwyfnkzvcffuxpwl.supabase.co',
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0MDU2MzIsImV4cCI6MjA2NDk4MTYzMn0.RvabQ6EGP1gBb0w8J8_VpaqqXgkpEFR7Nbbkmw11wNA',
} as const;

// Create Supabase client
export const supabase = createClient<Database>(
  SUPABASE_CONFIG.url,
  SUPABASE_CONFIG.anonKey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
  }
);

// Create admin client (for server-side operations)
export const createAdminClient = (serviceRoleKey: string) => {
  return createClient<Database>(
    SUPABASE_CONFIG.url,
    serviceRoleKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  );
};

// Authentication Configuration
export const AUTH_CONFIG = {
  // Session duration (24 hours)
  sessionDuration: 24 * 60 * 60 * 1000,
  
  // OTP configuration
  otp: {
    length: 6,
    expiryMinutes: 5,
    maxAttempts: 3,
  },
  
  // PIN configuration
  pin: {
    length: 4,
    maxAttempts: 3,
    lockoutMinutes: 15,
  },
  
  // 2FA configuration
  twoFA: {
    backupCodesCount: 10,
    issuerName: 'Creperie System',
  },
  
  // Rate limiting
  rateLimiting: {
    maxLoginAttempts: 5,
    lockoutMinutes: 15,
    maxOTPRequests: 3,
    otpCooldownMinutes: 1,
  },
  
  // Platform-specific settings
  platforms: {
    admin: {
      requiresEmail: true,
      requires2FA: false, // Temporarily disabled for initial setup
      sessionTimeout: 8 * 60 * 60 * 1000, // 8 hours
    },
    pos: {
      requiresPIN: true,
      sessionTimeout: 12 * 60 * 60 * 1000, // 12 hours
    },
    customer_web: {
      requiresPhone: true,
      sessionTimeout: 30 * 24 * 60 * 60 * 1000, // 30 days
    },
    customer_mobile: {
      requiresPhone: true,
      sessionTimeout: 90 * 24 * 60 * 60 * 1000, // 90 days
    },
  },
} as const;

// Error Messages
export const AUTH_ERRORS = {
  INVALID_CREDENTIALS: 'Invalid email or password',
  INVALID_PIN: 'Invalid PIN',
  INVALID_OTP: 'Invalid or expired OTP',
  INVALID_2FA: 'Invalid 2FA code',
  USER_NOT_FOUND: 'User not found',
  USER_INACTIVE: 'User account is inactive',
  ACCOUNT_LOCKED: 'Account temporarily locked due to too many failed attempts',
  SESSION_EXPIRED: 'Session has expired',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions',
  PHONE_NOT_VERIFIED: 'Phone number not verified',
  EMAIL_NOT_VERIFIED: 'Email address not verified',
  TWO_FA_REQUIRED: '2FA verification required',
  RATE_LIMIT_EXCEEDED: 'Too many requests, please try again later',
  PLATFORM_NOT_ALLOWED: 'Platform access not allowed for this user',
  BRANCH_ACCESS_DENIED: 'Access denied for this branch',
} as const;

// Success Messages
export const AUTH_SUCCESS = {
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  OTP_SENT: 'OTP sent successfully',
  OTP_VERIFIED: 'Phone number verified successfully',
  TWO_FA_ENABLED: '2FA enabled successfully',
  TWO_FA_DISABLED: '2FA disabled successfully',
  PASSWORD_UPDATED: 'Password updated successfully',
  PIN_UPDATED: 'PIN updated successfully',
  PROFILE_UPDATED: 'Profile updated successfully',
} as const;