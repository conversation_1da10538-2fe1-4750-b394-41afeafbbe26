import { supabase } from '../../shared/supabase';
import { getApiUrl } from '../../config/environment';
import { 
  PricingBreakdown, 
  PricingConfiguration, 
  PricingCalculationRequest, 
  PricingCalculationResponse,
  DeliveryValidationRequest,
  DeliveryValidationResponse,
  DeliveryZone,
  formatCurrency,
  roundToCents,
  calculatePercentage,
  validateCoordinates,
  ORDER_TYPES,
  OrderType
} from '../../../../shared/types/pricing';

// Nutritional information interface
interface NutritionalInfo {
  calories?: number;
  protein?: number;
  carbohydrates?: number;
  fat?: number;
  fiber?: number;
  sugar?: number;
  sodium?: number;
  [key: string]: number | undefined;
}

// Cache data type
type CacheData = MenuCategory[] | MenuItem[] | Ingredient[] | IngredientCategory[] | DeliveryZone[] | PricingConfiguration | AdminDashboardSettings | MenuConfiguration;

// Settings types
interface AdminDashboardSettings {
  branding?: Record<string, unknown>;
  features?: Record<string, unknown>;
  display?: Record<string, unknown>;
  [key: string]: unknown;
}

interface MenuConfiguration {
  categories?: string[];
  display_options?: Record<string, unknown>;
  sorting?: Record<string, unknown>;
  [key: string]: unknown;
}

// Enhanced interfaces matching database schema
export interface MenuCategory {
  id: string;
  name?: string; // Computed field
  name_en: string;
  name_el: string;
  description?: string | null; // Computed field
  description_en?: string | null;
  description_el?: string | null;
  image_url?: string | null;
  sort_order?: number; // Computed field
  display_order?: number | null;
  is_active?: boolean | null;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface IngredientCategory {
  id: string;
  name: string;
  description?: string;
  color_code: string;
  display_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Ingredient {
  id: string;
  category_id: string;
  name: string;
  description?: string;
  price: number;
  pickup_price: number;
  delivery_price: number;
  cost: number;
  image_url?: string;
  stock_quantity: number;
  min_stock_level: number;
  is_available: boolean;
  allergens?: string[];
  nutritional_info?: NutritionalInfo;
  display_order: number;
  created_at: string;
  updated_at: string;
  ingredient_category?: IngredientCategory;
}

export interface MenuItem {
  id: string;
  category_id: string;
  name?: string; // Computed from name_en or name_el
  name_en?: string;
  name_el?: string;
  description?: string | null; // Computed field
  description_en?: string | null;
  description_el?: string | null;
  price: number;
  pickup_price?: number; // Price for takeaway orders
  delivery_price?: number; // Price for delivery orders
  base_price?: number; // Alternative property name used in some components
  image_url?: string | null;
  preparation_time?: number | null;
  preparationTime?: number; // Alternative property name for compatibility
  calories?: number | null;
  allergens?: string[] | null;
  ingredients?: string[] | null;
  is_available?: boolean | null;
  is_featured?: boolean; // Whether this item is featured
  is_customizable?: boolean; // Whether this item can be customized
  max_ingredients?: number; // Maximum number of ingredients allowed
  sort_order?: number; // Computed field
  display_order?: number | null;
  created_at?: string | null;
  updated_at?: string | null;
  
  // Computed properties for compatibility
  category?: string; // Alternative to category_id
  customizations?: MenuItemCustomization[]; // For menu items with customizable options
}

export interface MenuItemCustomizationOption {
  id: string;
  name: string;
  price: number;
}

export interface MenuItemCustomization {
  id: string;
  name: string;
  required: boolean;
  maxSelections?: number;
  options: MenuItemCustomizationOption[];
}

export interface MenuItemIngredient {
  id: string;
  subcategory_id: string;
  ingredient_id: string;
  quantity: number;
  is_default: boolean;
  is_optional: boolean;
  additional_price: number;
}

export interface CustomizationPreset {
  id: string;
  subcategory_id: string;
  name: string;
  description?: string;
  preset_ingredients: Array<{ ingredient_id: string; quantity: number }>;
  total_additional_price: number;
  image_url?: string;
  is_popular: boolean;
  display_order: number;
}

class MenuService {
  private static instance: MenuService;
  private cache: Map<string, CacheData> = new Map();
  private lastFetch: Map<string, number> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  static getInstance(): MenuService {
    if (!MenuService.instance) {
      MenuService.instance = new MenuService();
    }
    return MenuService.instance;
  }

  private isCacheValid(key: string): boolean {
    const lastFetch = this.lastFetch.get(key);
    return lastFetch ? Date.now() - lastFetch < this.CACHE_TTL : false;
  }

  private setCache(key: string, data: CacheData): void {
    this.cache.set(key, data);
    this.lastFetch.set(key, Date.now());
  }

  async getMenuCategories(): Promise<MenuCategory[]> {
    const cacheKey = 'menu_categories';
    
    if (this.isCacheValid(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      // Type guard to ensure we have an array of menu categories
      if (Array.isArray(cached)) {
        return cached as MenuCategory[];
      }
    }

    try {
      const { data, error } = await supabase
        .from('menu_categories')
        .select('*')
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error fetching menu categories:', error);
        return [];
      }

      // Transform database data to match our interface
      const transformedData = (data || []).map(item => ({
        ...item,
        name: item.name_en || '',
        description: item.description_en || item.description_el || null,
        sort_order: item.display_order || 0,
        is_active: item.is_active || false
      }));
      
      this.setCache(cacheKey, transformedData);
      return transformedData;
    } catch (error) {
      console.error('Error fetching menu categories:', error);
      return [];
    }
  }

  async getIngredientCategories(): Promise<IngredientCategory[]> {
    const cacheKey = 'ingredient_categories';
    
    if (this.isCacheValid(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Array.isArray(cached)) {
        return cached as IngredientCategory[];
      }
    }

    try {
      const { data, error } = await supabase
        .from('ingredient_categories')
        .select('*')
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error fetching ingredient categories:', error);
        return [];
      }

      const transformedData = (data || []).map(category => ({
        id: category.id,
        name: category.name,
        description: category.description || '',
        color_code: category.color_code || '#6B7280',
        display_order: category.display_order || 0,
        is_active: category.is_active || true,
        created_at: category.created_at || '',
        updated_at: category.updated_at || ''
      }));

      this.setCache(cacheKey, transformedData);
      return transformedData;
    } catch (error) {
      console.error('Error in getIngredientCategories:', error);
      return [];
    }
  }

  async getIngredients(): Promise<Ingredient[]> {
    const cacheKey = 'ingredients';
    
    if (this.isCacheValid(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      // Type guard to ensure we have an array of ingredients
      if (Array.isArray(cached)) {
        return cached as Ingredient[];
      }
    }

    try {
      const { data, error } = await supabase
        .from('ingredients')
        .select(`
          id,
          name_en,
          name_el,
          description,
          price,
          pickup_price,
          delivery_price,
          unit,
          stock_quantity,
          min_stock_level,
          cost_per_unit,
          allergen_info,
          is_active,
          is_available,
          image_url,
          display_order,
          category_id,
          created_at,
          updated_at
        `)
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error fetching ingredients:', error);
        return [];
      }

      // Transform to match our interface with backward compatibility
      const transformedData = (data || []).map(ingredient => ({
        id: ingredient.id,
        category_id: ingredient.category_id || '',
        name: ingredient.name_en || ingredient.name_el || 'Unknown',
        description: ingredient.description || '',
        price: ingredient.price || ingredient.cost_per_unit || 0,
        pickup_price: ingredient.pickup_price || ingredient.price || ingredient.cost_per_unit || 0,
        delivery_price: ingredient.delivery_price || ingredient.price || ingredient.cost_per_unit || 0,
        cost: ingredient.cost_per_unit || 0,
        image_url: ingredient.image_url || '',
        stock_quantity: ingredient.stock_quantity || 0,
        min_stock_level: ingredient.min_stock_level || 0,
        is_available: ingredient.is_available && ingredient.is_active && (ingredient.stock_quantity || 0) > 0,
        allergens: ingredient.allergen_info || [],
        nutritional_info: undefined,
        display_order: ingredient.display_order || 0,
        created_at: ingredient.created_at || '',
        updated_at: ingredient.updated_at || ''
      }));

      this.setCache(cacheKey, transformedData);
      return transformedData;
    } catch (error) {
      console.error('Error fetching ingredients:', error);
      return [];
    }
  }

  async getIngredientsByCategory(categoryId: string): Promise<Ingredient[]> {
    const cacheKey = `ingredients_category_${categoryId}`;
    
    if (this.isCacheValid(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Array.isArray(cached)) {
        return cached as Ingredient[];
      }
    }

    try {
      const { data, error } = await supabase
        .from('ingredients')
        .select(`
          id,
          name_en,
          name_el,
          description,
          price,
          pickup_price,
          delivery_price,
          unit,
          stock_quantity,
          min_stock_level,
          cost_per_unit,
          allergen_info,
          is_active,
          is_available,
          image_url,
          display_order,
          category_id,
          created_at,
          updated_at
        `)
        .eq('category_id', categoryId)
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error fetching ingredients by category:', error);
        return [];
      }

      // Transform to match our interface with backward compatibility
      const transformedData = (data || []).map(ingredient => ({
        id: ingredient.id,
        category_id: ingredient.category_id || '',
        name: ingredient.name_en || ingredient.name_el || 'Unknown',
        description: ingredient.description || '',
        price: ingredient.price || ingredient.cost_per_unit || 0,
        pickup_price: ingredient.pickup_price || ingredient.price || ingredient.cost_per_unit || 0,
        delivery_price: ingredient.delivery_price || ingredient.price || ingredient.cost_per_unit || 0,
        cost: ingredient.cost_per_unit || 0,
        image_url: ingredient.image_url || '',
        stock_quantity: ingredient.stock_quantity || 0,
        min_stock_level: ingredient.min_stock_level || 0,
        is_available: ingredient.is_available && ingredient.is_active && (ingredient.stock_quantity || 0) >= 0, // Allow 0 stock for demo
        allergens: ingredient.allergen_info || [],
        nutritional_info: undefined,
        display_order: ingredient.display_order || 0,
        created_at: ingredient.created_at || '',
        updated_at: ingredient.updated_at || ''
      }));

      this.setCache(cacheKey, transformedData);
      return transformedData;
    } catch (error) {
      console.error('Error fetching ingredients by category:', error);
      return [];
    }
  }

  async getMenuItems(): Promise<MenuItem[]> {
    const cacheKey = 'subcategories';
    
    if (this.isCacheValid(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      // Type guard to ensure we have an array of menu items
      if (Array.isArray(cached)) {
        return cached as MenuItem[];
      }
    }

    try {
      const { data, error } = await supabase
        .from('subcategories')
        .select('*')
        .order('display_order', { ascending: true });

      if (error) {
        console.error('Error fetching menu items:', error);
        return [];
      }

      this.setCache(cacheKey, data || []);
      return data || [];
    } catch (error) {
      console.error('Error fetching menu items:', error);
      return [];
    }
  }

  async getMenuItemsByCategory(categoryId: string): Promise<MenuItem[]> {
    const cacheKey = `subcategories_${categoryId}`;
    
    if (this.isCacheValid(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      // Type guard to ensure we have an array of menu items
      if (Array.isArray(cached)) {
        return cached as MenuItem[];
      }
    }

    try {
      const { data, error } = await supabase
        .from('subcategories')
        .select(`
          *,
          category:menu_categories(*)
        `)
        .eq('category_id', categoryId)
        .eq('is_available', true)
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('Error fetching menu items by category:', error);
        return [];
      }

      this.setCache(cacheKey, data || []);
      return data || [];
    } catch (error) {
      console.error('Error fetching menu items by category:', error);
      return [];
    }
  }

  async getCustomizableItems(): Promise<MenuItem[]> {
    const cacheKey = 'customizable_items';
    
    if (this.isCacheValid(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      // Type guard to ensure we have an array of menu items
      if (Array.isArray(cached)) {
        return cached as MenuItem[];
      }
    }

    try {
      const { data, error } = await supabase
        .from('subcategories')
        .select(`
          *,
          category:menu_categories(*)
        `)
        .eq('is_customizable', true)
        .eq('is_available', true)
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('Error fetching customizable items:', error);
        return [];
      }

      this.setCache(cacheKey, data || []);
      return data || [];
    } catch (error) {
      console.error('Error fetching customizable items:', error);
      return [];
    }
  }

  async getMenuItemIngredients(menuItemId: string): Promise<MenuItemIngredient[]> {
    try {
      const { data, error } = await supabase
        .from('subcategory_ingredients')
        .select(`
          id,
          subcategory_id,
          ingredient_id,
          quantity_required,
          ingredients(
            id,
            name_en,
            name_el,
            cost_per_unit
          )
        `)
        .eq('subcategory_id', menuItemId);

      if (error) {
        console.error('Error fetching menu item ingredients:', error);
        return [];
      }

      return (data || []).map(item => ({
        id: item.id,
        subcategory_id: item.subcategory_id,
        ingredient_id: item.ingredient_id,
        quantity: item.quantity_required || 1,
        is_default: false, // Default to false since column doesn't exist
        is_optional: true, // Default to optional for now
        additional_price: 0 // Default to no additional price
      }));
    } catch (error) {
      console.error('Error fetching menu item ingredients:', error);
      return [];
    }
  }

  async getCustomizationPresets(menuItemId: string): Promise<CustomizationPreset[]> {
    // Note: Simplified version - customization not implemented in current database
    console.warn('getCustomizationPresets: Feature not available in current database schema');
    return [];
  }

  // Helper method to get available ingredients for a customizable item
  async getAvailableIngredientsForItem(menuItemId: string): Promise<Ingredient[]> {
    try {
      // First, check if the menu item is actually customizable
      const { data: menuItemData, error: menuItemError } = await supabase
        .from('menu_items')
        .select('is_customizable')
        .eq('id', menuItemId)
        .single();

      if (menuItemError || !menuItemData?.is_customizable) {
        console.log(`Menu item ${menuItemId} is not customizable, returning empty ingredients list`);
        return [];
      }

      // Get ingredients specifically associated with this subcategory
      const { data, error } = await supabase
        .from('subcategory_ingredients')
        .select(`
          ingredient_id,
          quantity_required,
          ingredients(
            id,
            name_en,
            name_el,
            description,
            price,
            pickup_price,
            delivery_price,
            cost_per_unit,
            image_url,
            stock_quantity,
            min_stock_level,
            allergen_info,
            is_active,
            is_available,
            display_order,
            category_id,
            created_at,
            updated_at
          )
        `)
        .eq('subcategory_id', menuItemId);

      if (error) {
        console.error('Error fetching ingredients for subcategory:', error);
        return [];
      }

      // Transform the data to match our Ingredient interface
      return (data || [])
        .filter((item: any) => item.ingredients && item.ingredients.is_active && item.ingredients.is_available)
        .map((item: any) => {
          const ingredient = item.ingredients;
          return {
            id: ingredient.id,
            category_id: ingredient.category_id || '',
            name: ingredient.name_en || ingredient.name_el || 'Unknown',
            description: ingredient.description || '',
            price: ingredient.price || ingredient.cost_per_unit || 0,
            pickup_price: ingredient.pickup_price || ingredient.price || ingredient.cost_per_unit || 0,
            delivery_price: ingredient.delivery_price || ingredient.price || ingredient.cost_per_unit || 0,
            cost: ingredient.cost_per_unit || 0,
            image_url: ingredient.image_url || '',
            stock_quantity: ingredient.stock_quantity || 0,
            min_stock_level: ingredient.min_stock_level || 0,
            is_available: ingredient.is_available && ingredient.is_active && (ingredient.stock_quantity || 0) > 0,
            allergens: ingredient.allergen_info || [],
            nutritional_info: undefined,
            display_order: ingredient.display_order || 0,
            created_at: ingredient.created_at || '',
            updated_at: ingredient.updated_at || ''
          } as Ingredient;
        });
    } catch (error) {
      console.error('Error fetching available ingredients for item:', error);
      return [];
    }
  }

  // Method to calculate total price with customizations
  calculateItemPrice(basePrice: number, selectedIngredients: Array<{ ingredient: Ingredient; quantity: number }>): number {
    const ingredientTotal = selectedIngredients.reduce((total, item) => {
      return total + (item.ingredient.price * item.quantity);
    }, 0);
    
    return basePrice + ingredientTotal;
  }

  // Enhanced method to calculate item price with order type awareness
  calculateItemPriceWithOrderType(
    itemOrBasePrice: MenuItem | number, 
    orderTypeOrIngredients?: OrderType | Array<{ ingredient: Ingredient; quantity: number }>, 
    orderType?: OrderType,
    pricingConfig?: PricingConfiguration
  ): number {
    // Handle MenuItem overload
    if (typeof itemOrBasePrice === 'object' && itemOrBasePrice !== null) {
      const item = itemOrBasePrice as MenuItem;
      const orderTypeParam = orderTypeOrIngredients as OrderType;
      
      if (!item) return 0;
      
      // Use the appropriate price based on order type
      let basePrice = 0;
      if (orderTypeParam === ORDER_TYPES.TAKEAWAY) {
        basePrice = item.pickup_price || item.price || 0;
      } else if (orderTypeParam === ORDER_TYPES.DELIVERY) {
        basePrice = item.delivery_price || item.price || 0;
      } else {
        // For dine-in, use pickup price as default
        basePrice = item.pickup_price || item.price || 0;
      }
      
      return basePrice;
    }
    
    // Handle original overload with base price and ingredients
    const basePrice = itemOrBasePrice as number;
    const selectedIngredients = orderTypeOrIngredients as Array<{ ingredient: Ingredient; quantity: number }>;
    
    let itemPrice = this.calculateItemPrice(basePrice, selectedIngredients || []);
    
    // Apply order type modifiers if available
    if (pricingConfig && orderType) {
      if (orderType === ORDER_TYPES.TAKEAWAY && pricingConfig.pickup.enabled) {
        const discount = calculatePercentage(itemPrice, pricingConfig.pickup.discountPercentage);
        itemPrice = roundToCents(itemPrice - discount);
      }
    }
    
    return itemPrice;
  }

  // Get pricing configuration from admin dashboard
  async getPricingConfiguration(orderType?: OrderType): Promise<PricingConfiguration> {
    const cacheKey = `pricing_config_${orderType || 'all'}`;
    
    if (this.isCacheValid(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      // Type guard to ensure we have the right type
      if (cached && typeof cached === 'object' && !Array.isArray(cached) && 'pickup' in cached) {
        return cached as PricingConfiguration;
      }
    }

    try {
      const url = orderType 
        ? getApiUrl(`/pricing/config?orderType=${orderType}`)
        : getApiUrl('/pricing/config');
        
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const config = data.configuration as PricingConfiguration;

      this.setCache(cacheKey, config);
      return config;
    } catch (error) {
      console.error('❌ Failed to fetch pricing configuration:', error);
      
      // Return default configuration
      return {
        pickup: {
          enabled: true,
          discountPercentage: 0.05,
          estimatedTime: { min: 10, max: 20 }
        },
        delivery: {
          enabled: true,
          zones: [],
          defaultFee: 2.50
        },
        general: {
          taxRate: 0.10,
          serviceFeeRate: 0.00,
          currency: 'EUR'
        }
      };
    }
  }

  // Calculate complete order pricing
  async calculateOrderPricing(request: PricingCalculationRequest): Promise<PricingCalculationResponse> {
    try {
      const response = await fetch(getApiUrl('/pricing/calculate'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('❌ Failed to calculate order pricing:', error);
      
      // Fallback to local calculation
      return this.calculateOrderPricingLocally(request);
    }
  }

  // Local fallback pricing calculation
  private async calculateOrderPricingLocally(request: PricingCalculationRequest): Promise<PricingCalculationResponse> {
    try {
      const config = await this.getPricingConfiguration(request.orderType);
      
      const pricing: PricingBreakdown = {
        subtotal: request.subtotal,
        deliveryFee: 0,
        pickupDiscount: 0,
        serviceFee: 0,
        taxAmount: 0,
        totalAmount: 0
      };

      // Calculate service fee
      pricing.serviceFee = calculatePercentage(pricing.subtotal, config.general.serviceFeeRate);

      if (request.orderType === ORDER_TYPES.TAKEAWAY) {
        pricing.pickupDiscount = calculatePercentage(pricing.subtotal, config.pickup.discountPercentage);
        pricing.estimatedTime = {
          min: config.pickup.estimatedTime.min,
          max: config.pickup.estimatedTime.max,
          message: 'Order will be ready for pickup'
        };
      } else if (request.orderType === ORDER_TYPES.DELIVERY) {
        pricing.deliveryFee = config.delivery.defaultFee;
        pricing.estimatedTime = {
          min: 30,
          max: 60,
          message: 'Estimated delivery time'
        };
      }

      // Calculate tax
      pricing.taxAmount = calculatePercentage(
        pricing.subtotal + pricing.serviceFee + pricing.deliveryFee - pricing.pickupDiscount,
        config.general.taxRate
      );

      // Calculate total
      pricing.totalAmount = roundToCents(
        pricing.subtotal + pricing.serviceFee + pricing.deliveryFee + pricing.taxAmount - pricing.pickupDiscount
      );

      return {
        success: true,
        orderType: request.orderType,
        pricing
      };
    } catch (error) {
      console.error('❌ Local pricing calculation failed:', error);
      return {
        success: false,
        orderType: request.orderType,
        pricing: {
          subtotal: request.subtotal,
          deliveryFee: 0,
          pickupDiscount: 0,
          serviceFee: 0,
          taxAmount: 0,
          totalAmount: request.subtotal
        },
        error: 'Pricing calculation failed'
      };
    }
  }

  // Validate delivery address and get zone info
  async validateDeliveryAddress(request: DeliveryValidationRequest): Promise<DeliveryValidationResponse> {
    try {
      const response = await fetch(getApiUrl('/delivery-zones/validate'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('❌ Failed to validate delivery address:', error);
      
      // Return fallback response
      return {
        success: false,
        deliveryAvailable: false,
        reason: 'VALIDATION_ERROR',
        message: 'Unable to validate delivery address. Please try again or contact support.',
        suggestion: 'Please verify your address or try pickup instead'
      };
    }
  }

  // Get delivery zones for caching
  async getDeliveryZones(): Promise<DeliveryZone[]> {
    const cacheKey = 'delivery_zones';
    
    if (this.isCacheValid(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      // Type guard to ensure we have an array of delivery zones
      if (cached && Array.isArray(cached) && 
          (cached.length === 0 || 
           (cached[0] && typeof cached[0] === 'object' && 
            'deliveryFee' in cached[0] && 
            'minimumOrderAmount' in cached[0] && 
            'estimatedTimeMin' in cached[0] && 
            'estimatedTimeMax' in cached[0]))) {
        return cached as DeliveryZone[];
      }
    }

    try {
      const response = await fetch(getApiUrl('/delivery-zones'), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const zones = data.zones || [] as DeliveryZone[];

      this.setCache(cacheKey, zones);
      return zones;
    } catch (error) {
      console.error('❌ Failed to fetch delivery zones:', error);
      return [];
    }
  }

  // Calculate estimated delivery time based on order type
  calculateEstimatedTime(orderType: OrderType, deliveryZone?: DeliveryZone): { min: number; max: number; message: string } {
    if (orderType === ORDER_TYPES.TAKEAWAY) {
      return {
        min: 10,
        max: 20,
        message: 'Order will be ready for pickup'
      };
    } else if (orderType === ORDER_TYPES.DELIVERY) {
      if (deliveryZone) {
        return {
          min: deliveryZone.estimatedTimeMin,
          max: deliveryZone.estimatedTimeMax,
          message: `Delivery to ${deliveryZone.name}`
        };
      } else {
        return {
          min: 30,
          max: 60,
          message: 'Estimated delivery time'
        };
      }
    }
    
    return {
      min: 15,
      max: 30,
      message: 'Estimated preparation time'
    };
  }

  // Format pricing breakdown for display
  formatPricingBreakdown(pricing: PricingBreakdown, currency: string = 'EUR'): Record<string, string> {
    return {
      subtotal: formatCurrency(pricing.subtotal, currency),
      deliveryFee: formatCurrency(pricing.deliveryFee, currency),
      pickupDiscount: formatCurrency(pricing.pickupDiscount, currency),
      serviceFee: formatCurrency(pricing.serviceFee, currency),
      taxAmount: formatCurrency(pricing.taxAmount, currency),
      totalAmount: formatCurrency(pricing.totalAmount, currency)
    };
  }

  // Method to check ingredient availability and update stock
  async checkIngredientAvailability(ingredientId: string): Promise<boolean> {
    try {
      // TODO: Ingredients table not yet created in database schema
      // const { data, error } = await supabase
      //   .from('ingredients')
      //   .select('is_available, stock_quantity, min_stock_level')
      //   .eq('id', ingredientId)
      //   .single();

      // if (error || !data) {
      //   return false;
      // }

      // return data.is_available && data.stock_quantity > data.min_stock_level;
      
      // Temporary: return true for all ingredients until ingredients table is created
      return true;
    } catch (error) {
      console.error('Error checking ingredient availability:', error);
      return false;
    }
  }

  // Admin Dashboard Integration Methods
  async getAdminDashboardSettings(): Promise<AdminDashboardSettings> {
    const cacheKey = 'admin_settings';
    
    if (this.isCacheValid(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      // Type guard to ensure we have the right type
      if (cached && typeof cached === 'object' && !Array.isArray(cached)) {
        return cached as AdminDashboardSettings;
      }
    }

    try {
      const response = await fetch(getApiUrl('/settings/pos'), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch admin settings');
      }

      const settings = await response.json();
      this.setCache(cacheKey, settings);
      return settings;
    } catch (error) {
      console.error('Error fetching admin dashboard settings:', error);
      return {
        tax_rate: 0.24, // Default Greek VAT
        service_fee: 0,
        delivery_fee: 2.50,
        currency: 'EUR',
        timezone: 'Europe/Athens'
      };
    }
  }

  async getMenuConfiguration(): Promise<MenuConfiguration> {
    const cacheKey = 'menu_configuration';
    
    if (this.isCacheValid(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      // Type guard to ensure we have the right type
      if (cached && typeof cached === 'object' && !Array.isArray(cached)) {
        return cached as MenuConfiguration;
      }
    }

    try {
      const response = await fetch(getApiUrl('/settings/menu'), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch menu configuration');
      }

      const config = await response.json();
      this.setCache(cacheKey, config);
      return config;
    } catch (error) {
      console.error('Error fetching menu configuration:', error);
      return {
        enable_customization: true,
        max_customizations: 10,
        preparation_time_buffer: 5,
        auto_categorize: true
      };
    }
  }

  // Clear cache (useful for real-time updates)
  clearCache(): void {
    this.cache.clear();
    this.lastFetch.clear();
  }

  // Clear specific cache entry
  clearCacheEntry(key: string): void {
    this.cache.delete(key);
    this.lastFetch.delete(key);
  }

  // Subscription management
  private activeSubscriptions = new Map<string, any>();
  private subscriptionCallbacks = new Map<string, Set<() => void>>();

  // Subscribe to menu updates with proper subscription management
  // TEMPORARILY DISABLED to fix multiple subscription error
  subscribeToMenuUpdates(callback: () => void): () => void {
    console.log('MenuService: subscribeToMenuUpdates temporarily disabled');
    
    // Return a no-op unsubscribe function
    return () => {
      console.log('MenuService: unsubscribe called (no-op)');
    };
    
    /* ORIGINAL CODE - TEMPORARILY DISABLED
    const subscriptionKey = 'menu-updates';
    
    // Add callback to the set
    if (!this.subscriptionCallbacks.has(subscriptionKey)) {
      this.subscriptionCallbacks.set(subscriptionKey, new Set());
    }
    this.subscriptionCallbacks.get(subscriptionKey)!.add(callback);

    // Create subscription only if it doesn't exist
    if (!this.activeSubscriptions.has(subscriptionKey)) {
      const channel = supabase.channel(subscriptionKey);
      
      const subscription = channel
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'subcategories' }, 
          () => {
            this.clearCacheEntry('subcategories');
            this.clearCacheEntry('customizable_items');
            // Call all registered callbacks
            const callbacks = this.subscriptionCallbacks.get(subscriptionKey);
            if (callbacks) {
              callbacks.forEach(cb => cb());
            }
          }
        )
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'ingredients' }, 
          () => {
            this.clearCacheEntry('ingredients');
            // Call all registered callbacks
            const callbacks = this.subscriptionCallbacks.get(subscriptionKey);
            if (callbacks) {
              callbacks.forEach(cb => cb());
            }
          }
        )
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'menu_categories' }, 
          () => {
            this.clearCacheEntry('menu_categories');
            // Call all registered callbacks
            const callbacks = this.subscriptionCallbacks.get(subscriptionKey);
            if (callbacks) {
              callbacks.forEach(cb => cb());
            }
          }
        )
        .subscribe();

      this.activeSubscriptions.set(subscriptionKey, subscription);
    }

    // Return unsubscribe function
    return () => {
      const callbacks = this.subscriptionCallbacks.get(subscriptionKey);
      if (callbacks) {
        callbacks.delete(callback);
        
        // If no more callbacks, remove the subscription
        if (callbacks.size === 0) {
          const subscription = this.activeSubscriptions.get(subscriptionKey);
          if (subscription) {
            supabase.removeChannel(subscription);
            this.activeSubscriptions.delete(subscriptionKey);
            this.subscriptionCallbacks.delete(subscriptionKey);
          }
        }
      }
    };
    */
  }
}

export { MenuService };
export const menuService = MenuService.getInstance();