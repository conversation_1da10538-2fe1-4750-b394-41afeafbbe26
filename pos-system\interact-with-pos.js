const { chromium } = require('playwright');

async function interactWithPOS() {
  console.log('🚀 Starting interactive POS System test...');
  
  try {
    // Connect to the Electron app
    const browser = await chromium.connectOverCDP('http://localhost:9222');
    const context = browser.contexts()[0];
    const page = context.pages()[0];
    
    console.log(`📱 Connected to: ${await page.title()}`);
    console.log(`🌐 URL: ${page.url()}`);
    
    // Take initial screenshot
    await page.screenshot({ path: 'pos-initial.png', fullPage: true });
    console.log('📸 Initial screenshot saved');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Get page content
    const bodyHTML = await page.innerHTML('body');
    console.log(`📄 Page loaded with ${bodyHTML.length} characters of HTML`);
    
    // Look for login elements
    console.log('\n🔍 Looking for login interface...');
    
    // Check for PIN input or password field
    const pinInputs = await page.$$('input[type="password"], input[placeholder*="PIN"], input[placeholder*="pin"]');
    console.log(`🔐 Found ${pinInputs.length} PIN/password inputs`);
    
    if (pinInputs.length > 0) {
      console.log('🔑 Attempting to login...');
      
      // Try common PINs mentioned in the app
      const testPins = ['1234', '0000', 'admin'];
      
      for (const pin of testPins) {
        console.log(`🔢 Trying PIN: ${pin}`);
        
        // Clear and enter PIN
        await pinInputs[0].fill('');
        await pinInputs[0].fill(pin);
        
        // Look for login button
        const loginButtons = await page.$$('button[type="submit"], button:has-text("Login"), button:has-text("Enter")');
        
        if (loginButtons.length > 0) {
          await loginButtons[0].click();
          console.log('👆 Clicked login button');
          
          // Wait a moment for response
          await page.waitForTimeout(2000);
          
          // Check if we're logged in (look for dashboard elements)
          const dashboardElements = await page.$$('[class*="dashboard"], [class*="order"], [class*="pos"]');
          
          if (dashboardElements.length > 0) {
            console.log(`✅ Login successful with PIN: ${pin}`);
            break;
          } else {
            console.log(`❌ Login failed with PIN: ${pin}`);
          }
        }
      }
    }
    
    // Take screenshot after login attempt
    await page.screenshot({ path: 'pos-after-login.png', fullPage: true });
    console.log('📸 Post-login screenshot saved');
    
    // Look for main navigation elements
    console.log('\n🧭 Exploring navigation...');
    
    const navButtons = await page.$$('button, [role="button"], a');
    console.log(`🔘 Found ${navButtons.length} clickable elements`);
    
    // Look for specific POS features
    const orderElements = await page.$$('[class*="order"], [data-testid*="order"]');
    const menuElements = await page.$$('[class*="menu"], [data-testid*="menu"]');
    const customerElements = await page.$$('[class*="customer"], [data-testid*="customer"]');
    
    console.log(`📋 Order elements: ${orderElements.length}`);
    console.log(`🍽️ Menu elements: ${menuElements.length}`);
    console.log(`👥 Customer elements: ${customerElements.length}`);
    
    // Try to interact with main features
    if (orderElements.length > 0) {
      console.log('\n📋 Testing order functionality...');
      await orderElements[0].click();
      await page.waitForTimeout(1000);
      await page.screenshot({ path: 'pos-orders.png', fullPage: true });
      console.log('📸 Orders view screenshot saved');
    }
    
    // Look for floating action button or new order button
    const newOrderButtons = await page.$$('button:has-text("New Order"), [class*="floating"], [class*="fab"]');
    if (newOrderButtons.length > 0) {
      console.log('\n➕ Testing new order creation...');
      await newOrderButtons[0].click();
      await page.waitForTimeout(1000);
      await page.screenshot({ path: 'pos-new-order.png', fullPage: true });
      console.log('📸 New order screenshot saved');
    }
    
    // Test theme switching if available
    const themeButtons = await page.$$('[class*="theme"], button:has-text("Dark"), button:has-text("Light")');
    if (themeButtons.length > 0) {
      console.log('\n🎨 Testing theme switching...');
      await themeButtons[0].click();
      await page.waitForTimeout(500);
      await page.screenshot({ path: 'pos-theme-switched.png', fullPage: true });
      console.log('📸 Theme switched screenshot saved');
    }
    
    // Get final page state
    console.log('\n📊 Final page analysis:');
    const finalButtons = await page.$$('button');
    const finalInputs = await page.$$('input');
    const finalText = await page.textContent('body');
    
    console.log(`🔘 Total buttons: ${finalButtons.length}`);
    console.log(`📝 Total inputs: ${finalInputs.length}`);
    console.log(`📄 Total text length: ${finalText ? finalText.length : 0}`);
    
    // Take final screenshot
    await page.screenshot({ path: 'pos-final.png', fullPage: true });
    console.log('📸 Final screenshot saved');
    
    console.log('\n✅ POS System testing completed!');
    console.log('📁 Screenshots saved:');
    console.log('   - pos-initial.png');
    console.log('   - pos-after-login.png');
    console.log('   - pos-orders.png (if available)');
    console.log('   - pos-new-order.png (if available)');
    console.log('   - pos-theme-switched.png (if available)');
    console.log('   - pos-final.png');
    
  } catch (error) {
    console.error('❌ Error during POS testing:', error.message);
  }
}

// Run the interactive test
interactWithPOS();
