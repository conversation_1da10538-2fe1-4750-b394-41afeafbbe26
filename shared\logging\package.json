{"name": "@creperie/logging", "version": "1.0.0", "description": "Centralized logging infrastructure for Creperie ecosystem", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest"}, "dependencies": {"winston": "^3.11.0", "winston-elasticsearch": "^0.17.4", "winston-daily-rotate-file": "^4.7.1", "@opentelemetry/api": "^1.7.0", "@opentelemetry/sdk-node": "^0.45.0", "@opentelemetry/auto-instrumentations-node": "^0.40.0", "pino": "^8.16.1", "pino-pretty": "^10.2.3"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.2", "jest": "^29.7.0"}}