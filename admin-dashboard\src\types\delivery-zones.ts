// Delivery Zone Types
export interface DeliveryZone {
  id: string;
  name: string;
  description?: string;
  polygon_coordinates: PolygonCoordinate[];
  geometry?: any; // PostGIS geometry field
  delivery_fee: number;
  minimum_order_amount: number;
  maximum_delivery_distance?: number;
  estimated_delivery_time_min: number;
  estimated_delivery_time_max: number;
  is_active: boolean;
  delivery_days: DeliveryDay[];
  delivery_hours_start: string; // HH:MM format
  delivery_hours_end: string; // HH:MM format
  distance_based_pricing: boolean;
  price_per_km?: number;
  created_at: string;
  updated_at: string;
}

export interface PolygonCoordinate {
  lat: number;
  lng: number;
}

export interface DeliveryDay {
  day: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';
  is_available: boolean;
  special_hours_start?: string;
  special_hours_end?: string;
}

// API Request/Response Types
export interface CreateDeliveryZoneRequest {
  name: string;
  description?: string;
  polygon_coordinates: PolygonCoordinate[];
  delivery_fee: number;
  minimum_order_amount: number;
  maximum_delivery_distance?: number;
  estimated_delivery_time_min: number;
  estimated_delivery_time_max: number;
  is_active: boolean;
  delivery_days: DeliveryDay[];
  delivery_hours_start: string;
  delivery_hours_end: string;
  distance_based_pricing: boolean;
  price_per_km?: number;
}

export interface UpdateDeliveryZoneRequest extends Partial<CreateDeliveryZoneRequest> {
  id: string;
}

export interface DeliveryZoneResponse {
  success: boolean;
  data?: DeliveryZone;
  error?: string;
}

export interface DeliveryZonesListResponse {
  success: boolean;
  data?: DeliveryZone[];
  error?: string;
}

// Address Validation Types
export interface AddressValidationRequest {
  address: string;
  latitude?: number;
  longitude?: number;
}

export interface AddressValidationResponse {
  success: boolean;
  data?: {
    is_in_delivery_zone: boolean;
    delivery_zone?: DeliveryZone;
    coordinates: {
      lat: number;
      lng: number;
    };
    formatted_address: string;
    delivery_fee: number;
    estimated_delivery_time: {
      min: number;
      max: number;
    };
    distance_from_center?: number;
  };
  error?: string;
}

// Google Maps Integration Types
export interface MapPolygon {
  id: string;
  coordinates: PolygonCoordinate[];
  options?: google.maps.PolygonOptions;
}

export interface MapMarker {
  id: string;
  position: PolygonCoordinate;
  title?: string;
  options?: google.maps.MarkerOptions;
}

export interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

// Form Types
export interface DeliveryZoneFormData {
  name: string;
  description: string;
  delivery_fee: number;
  minimum_order_amount: number;
  maximum_delivery_distance: number;
  estimated_delivery_time_min: number;
  estimated_delivery_time_max: number;
  is_active: boolean;
  delivery_hours_start: string;
  delivery_hours_end: string;
  distance_based_pricing: boolean;
  price_per_km: number;
  delivery_days: {
    monday: boolean;
    tuesday: boolean;
    wednesday: boolean;
    thursday: boolean;
    friday: boolean;
    saturday: boolean;
    sunday: boolean;
  };
  polygon_coordinates: PolygonCoordinate[];
}

// Settings Types
export interface DeliverySettings {
  default_delivery_fee: number;
  default_minimum_order: number;
  default_delivery_time_min: number;
  default_delivery_time_max: number;
  enable_distance_pricing: boolean;
  default_price_per_km: number;
  business_center_lat: number;
  business_center_lng: number;
  max_delivery_radius: number;
}

// Error Types
export interface DeliveryZoneError {
  code: string;
  message: string;
  field?: string;
}

// Utility Types
export type DeliveryZoneStatus = 'active' | 'inactive' | 'draft';
export type DeliveryTimeSlot = {
  start: string;
  end: string;
  is_available: boolean;
};

export type SortField = 'name' | 'created_at' | 'delivery_fee' | 'is_active';
export type SortOrder = 'asc' | 'desc';

export interface DeliveryZoneFilters {
  status?: DeliveryZoneStatus;
  search?: string;
  sort_field?: SortField;
  sort_order?: SortOrder;
}