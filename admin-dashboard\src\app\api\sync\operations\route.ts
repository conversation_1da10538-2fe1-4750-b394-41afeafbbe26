import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    const terminalId = searchParams.get('terminal_id')
    const timeRange = searchParams.get('time_range') || '24h'
    const limit = parseInt(searchParams.get('limit') || '50')
    const status = searchParams.get('status')

    // Calculate time range
    const now = new Date()
    let startTime: Date
    
    switch (timeRange) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000)
        break
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    }

    // Build query for sync operations
    let query = supabase
      .from('pos_settings_sync_history')
      .select(`
        id,
        terminal_id,
        config_id,
        sync_type,
        sync_status,
        sync_duration,
        error_message,
        created_at,
        synced_at,
        metadata
      `)
      .gte('created_at', startTime.toISOString())
      .order('created_at', { ascending: false })
      .limit(limit)

    if (terminalId && terminalId !== 'all') {
      query = query.eq('terminal_id', terminalId)
    }

    if (status) {
      query = query.eq('sync_status', status)
    }

    const { data: operations, error } = await query

    if (error) {
      console.error('Error fetching sync operations:', error)
      return NextResponse.json(
        { error: 'Failed to fetch sync operations' },
        { status: 500 }
      )
    }

    // If no operations found, create some sample data for demonstration
    if (!operations || operations.length === 0) {
      const sampleOperations = await createSampleSyncOperations(supabase, terminalId)
      if (sampleOperations.length > 0) {
        // Re-run the query to get the newly created operations
        const { data: newOperations } = await query
        if (newOperations && newOperations.length > 0) {
          operations = newOperations
        }
      }
    }

    // Get additional details for each operation
    const enrichedOperations = await Promise.all(
      (operations || []).map(async (op) => {
        // Get configuration details if config_id exists
        let configDetails = null
        if (op.config_id) {
          const { data: config } = await supabase
            .from('pos_configurations')
            .select('config_key, config_type, setting_category, description')
            .eq('id', op.config_id)
            .single()
          
          configDetails = config
        }

        // Get terminal details
        let terminalDetails = null
        if (op.terminal_id) {
          const { data: terminal } = await supabase
            .from('pos_terminals')
            .select('name, location')
            .eq('terminal_id', op.terminal_id)
            .single()
          
          terminalDetails = terminal
        }

        return {
          ...op,
          config_details: configDetails,
          terminal_details: terminalDetails,
          duration_ms: op.sync_duration || 0,
          status_display: op.sync_status === 'success' ? 'Completed' :
                         op.sync_status === 'failed' ? 'Failed' :
                         op.sync_status === 'pending' ? 'Pending' :
                         op.sync_status === 'in_progress' ? 'In Progress' : 'Unknown'
        }
      })
    )

    // Calculate summary statistics
    const totalOps = enrichedOperations.length
    const successfulOps = enrichedOperations.filter(op => op.sync_status === 'success').length
    const failedOps = enrichedOperations.filter(op => op.sync_status === 'failed').length
    const pendingOps = enrichedOperations.filter(op => op.sync_status === 'pending').length
    const inProgressOps = enrichedOperations.filter(op => op.sync_status === 'in_progress').length

    const avgDuration = enrichedOperations
      .filter(op => op.sync_duration && op.sync_status === 'success')
      .reduce((sum, op) => sum + (op.sync_duration || 0), 0) / 
      Math.max(1, enrichedOperations.filter(op => op.sync_duration && op.sync_status === 'success').length)

    return NextResponse.json({
      operations: enrichedOperations,
      summary: {
        total: totalOps,
        successful: successfulOps,
        failed: failedOps,
        pending: pendingOps,
        in_progress: inProgressOps,
        success_rate: totalOps > 0 ? (successfulOps / totalOps) * 100 : 0,
        avg_duration_ms: Math.round(avgDuration || 0)
      },
      filters: {
        terminal_id: terminalId,
        time_range: timeRange,
        status,
        limit
      },
      timestamp: now.toISOString()
    })

  } catch (error) {
    console.error('Sync operations error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Function to create sample sync operations for demonstration
async function createSampleSyncOperations(supabase: any, terminalId?: string | null) {
  try {
    const sampleOperations = []
    const syncTypes = ['staff_permissions', 'hardware_config', 'inventory', 'menu', 'settings']
    const statuses = ['success', 'failed', 'pending', 'in_progress']
    const terminals = ['770c7b00', 'terminal-001', 'terminal-002']

    // Create 15 sample operations
    for (let i = 0; i < 15; i++) {
      const randomTerminal = terminals[Math.floor(Math.random() * terminals.length)]
      const randomType = syncTypes[Math.floor(Math.random() * syncTypes.length)]
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
      const randomDuration = randomStatus === 'success' ? Math.floor(Math.random() * 3000) + 500 :
                            randomStatus === 'failed' ? Math.floor(Math.random() * 2000) + 200 : null

      // Create timestamps for the last 24 hours
      const createdAt = new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000)
      const syncedAt = randomStatus === 'success' || randomStatus === 'failed' ?
                      new Date(createdAt.getTime() + (randomDuration || 1000)) : null

      const operation = {
        terminal_id: randomTerminal,
        config_id: null,
        sync_type: randomType,
        sync_status: randomStatus,
        sync_duration: randomDuration,
        error_message: randomStatus === 'failed' ?
          ['Network timeout', 'Database connection failed', 'Invalid data format', 'Permission denied'][Math.floor(Math.random() * 4)] : null,
        created_at: createdAt.toISOString(),
        synced_at: syncedAt?.toISOString() || null,
        metadata: {
          priority: Math.floor(Math.random() * 5) + 1,
          data_size: Math.floor(Math.random() * 50000) + 1024,
          retry_count: randomStatus === 'failed' ? Math.floor(Math.random() * 3) : 0
        }
      }

      sampleOperations.push(operation)
    }

    // Insert sample operations into database
    const { data, error } = await supabase
      .from('pos_settings_sync_history')
      .insert(sampleOperations)
      .select()

    if (error) {
      console.error('Error creating sample sync operations:', error)
      return []
    }

    console.log(`Created ${sampleOperations.length} sample sync operations`)
    return data || []

  } catch (error) {
    console.error('Error in createSampleSyncOperations:', error)
    return []
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { terminal_id, operation_type, config_ids, force_sync } = await request.json()

    if (!terminal_id || !operation_type) {
      return NextResponse.json(
        { error: 'terminal_id and operation_type are required' },
        { status: 400 }
      )
    }

    const operations = []

    if (config_ids && Array.isArray(config_ids)) {
      // Create operations for specific configurations
      for (const configId of config_ids) {
        const { data: operation, error } = await supabase
          .from('pos_settings_sync_history')
          .insert({
            terminal_id,
            config_id: configId,
            sync_type: operation_type,
            sync_status: 'pending',
            created_at: new Date().toISOString(),
            metadata: {
              force_sync,
              initiated_by: 'admin_dashboard',
              batch_operation: true
            }
          })
          .select()
          .single()

        if (!error && operation) {
          operations.push(operation)
        }
      }
    } else {
      // Create a general sync operation
      const { data: operation, error } = await supabase
        .from('pos_settings_sync_history')
        .insert({
          terminal_id,
          sync_type: operation_type,
          sync_status: 'pending',
          created_at: new Date().toISOString(),
          metadata: {
            force_sync,
            initiated_by: 'admin_dashboard',
            full_sync: true
          }
        })
        .select()
        .single()

      if (!error && operation) {
        operations.push(operation)
      }
    }

    return NextResponse.json({
      success: true,
      message: `${operations.length} sync operation(s) queued`,
      operations,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Sync operations POST error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
