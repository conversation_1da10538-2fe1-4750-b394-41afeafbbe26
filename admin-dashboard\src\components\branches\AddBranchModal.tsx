'use client'

import React, { useState } from 'react'
import { X, MapPin, Phone, Mail, Building2, Clock, Settings, Users } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import toast from 'react-hot-toast'

interface AddBranchModalProps {
  onClose: () => void
  onBranchAdded: () => void
}

interface BranchFormData {
  name: string
  display_name: string
  description: string
  address_line1: string
  address_line2: string
  city: string
  state: string
  postal_code: string
  country: string
  phone: string
  email: string
  website: string
  seating_capacity: number
  parking_spaces: number
  has_drive_through: boolean
  has_delivery: boolean
  has_pickup: boolean
  has_dine_in: boolean
  delivery_radius: number
  delivery_fee: number
  minimum_order_amount: number
  timezone: string
  status: 'active' | 'inactive' | 'temporarily_closed' | 'coming_soon'
}

const initialFormData: BranchFormData = {
  name: '',
  display_name: '',
  description: '',
  address_line1: '',
  address_line2: '',
  city: '',
  state: '',
  postal_code: '',
  country: 'Greece',
  phone: '',
  email: '',
  website: '',
  seating_capacity: 0,
  parking_spaces: 0,
  has_drive_through: false,
  has_delivery: true,
  has_pickup: true,
  has_dine_in: true,
  delivery_radius: 5.0,
  delivery_fee: 2.99,
  minimum_order_amount: 15.00,
  timezone: 'Europe/Athens',
  status: 'active'
}

const timezones = [
  'Europe/Athens',
  'Europe/London',
  'Europe/Paris',
  'Europe/Berlin',
  'Europe/Rome',
  'Europe/Madrid',
  'Europe/Amsterdam',
  'Europe/Brussels',
  'Europe/Vienna',
  'Europe/Prague',
  'Europe/Warsaw',
  'Europe/Budapest',
  'Europe/Bucharest',
  'Europe/Sofia',
  'Europe/Zagreb',
  'Europe/Belgrade',
  'Europe/Skopje',
  'Europe/Sarajevo',
  'Europe/Podgorica',
  'Europe/Tirana',
  'Europe/Istanbul',
  'Europe/Helsinki',
  'Europe/Stockholm',
  'Europe/Oslo',
  'Europe/Copenhagen',
  'Europe/Dublin',
  'Europe/Lisbon',
  'Europe/Zurich'
]

export default function AddBranchModal({ onClose, onBranchAdded }: AddBranchModalProps) {
  const [formData, setFormData] = useState<BranchFormData>(initialFormData)
  const [currentStep, setCurrentStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const totalSteps = 4

  const handleInputChange = (field: keyof BranchFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateStep = (step: number) => {
    const newErrors: Record<string, string> = {}

    switch (step) {
      case 1: // Basic Information
        if (!formData.name.trim()) newErrors.name = 'Branch name is required'
        if (!formData.display_name.trim()) newErrors.display_name = 'Display name is required'
        break
      
      case 2: // Location Information
        if (!formData.address_line1.trim()) newErrors.address_line1 = 'Address is required'
        if (!formData.city.trim()) newErrors.city = 'City is required'
        if (!formData.state.trim()) newErrors.state = 'State is required'
        if (!formData.postal_code.trim()) newErrors.postal_code = 'Postal code is required'
        break
      
      case 3: // Contact & Services
        if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
          newErrors.email = 'Invalid email format'
        }
        if (formData.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
          newErrors.phone = 'Invalid phone format'
        }
        break
      
      case 4: // Operational Settings
        if (formData.delivery_radius <= 0) newErrors.delivery_radius = 'Delivery radius must be greater than 0'
        if (formData.delivery_fee < 0) newErrors.delivery_fee = 'Delivery fee cannot be negative'
        if (formData.minimum_order_amount < 0) newErrors.minimum_order_amount = 'Minimum order amount cannot be negative'
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps))
    }
  }

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return

    try {
      setLoading(true)

      // Check authentication status first
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      
      // Development mode: Create a temporary admin session if not authenticated
      if (!session && process.env.NODE_ENV === 'development') {
        console.log('Development mode: Creating temporary admin session')
        
        // Try to sign in with a development admin account
        const { data: devAuth, error: devError } = await supabase.auth.signInWithPassword({
          email: '<EMAIL>',
          password: 'dev123456'
        })
        
        if (devError) {
          console.log('No dev account found, proceeding with anonymous insert')
          // If no dev account, we'll try to insert anyway and let the user know about the permission issue
        }
      } else if (sessionError || !session) {
        toast.error('You must be logged in to add branches. Please log in and try again.')
        setLoading(false)
        return
      }

      // Prepare the data for insertion
      const branchData = {
        ...formData,
        is_active: formData.status === 'active',
        // Let the database auto-generate the code via trigger
        code: null
      }

      console.log('Submitting branch data:', branchData)
      console.log('Current session:', session?.user?.email || 'No session')

      const { data, error } = await supabase
        .from('branches')
        .insert([branchData])
        .select()

      if (error) {
        console.error('Supabase error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        })
        throw error
      }

      console.log('Branch created successfully:', data)
      toast.success('Branch added successfully!')
      onBranchAdded()
    } catch (error: any) {
      console.error('Error adding branch:', error)
      
      // Provide more specific error messages
      let errorMessage = 'Failed to add branch'
      
      if (error?.message) {
        if (error.message.includes('permission') || error.message.includes('policy')) {
          errorMessage = 'Permission denied. You need admin or manager privileges to add branches. For development, please create an admin account or temporarily disable RLS.'
        } else if (error.message.includes('unique')) {
          errorMessage = 'A branch with this information already exists.'
        } else if (error.message.includes('null value')) {
          errorMessage = 'Please fill in all required fields.'
        } else if (error.message.includes('JWT')) {
          errorMessage = 'Authentication error. Please log in again.'
        } else {
          errorMessage = `Error: ${error.message}`
        }
      }
      
      toast.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="flex items-center gap-3 mb-6">
              <Building2 className="h-6 w-6 text-blue-400" />
              <h3 className="text-lg font-semibold glass-text-primary">Basic Information</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Branch Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className={`glass-input ${
                    errors.name ? 'border-red-400/50' : ''
                  }`}
                  placeholder="e.g., Downtown Branch"
                />
                {errors.name && <p className="mt-1 text-sm text-red-400">{errors.name}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Display Name *
                </label>
                <input
                  type="text"
                  value={formData.display_name}
                  onChange={(e) => handleInputChange('display_name', e.target.value)}
                  className={`glass-input ${
                    errors.display_name ? 'border-red-400/50' : ''
                  }`}
                  placeholder="e.g., Delicious Bites - Downtown"
                />
                {errors.display_name && <p className="mt-1 text-sm text-red-400">{errors.display_name}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium glass-text-primary mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
                className="glass-input"
                placeholder="Brief description of this branch location..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium glass-text-primary mb-2">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
                className="glass-select"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="coming_soon">Coming Soon</option>
                <option value="temporarily_closed">Temporarily Closed</option>
              </select>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="flex items-center gap-3 mb-6">
              <MapPin className="h-6 w-6 text-blue-400" />
              <h3 className="text-lg font-semibold glass-text-primary">Location Information</h3>
            </div>

            <div>
              <label className="block text-sm font-medium glass-text-primary mb-2">
                Address Line 1 *
              </label>
              <input
                type="text"
                value={formData.address_line1}
                onChange={(e) => handleInputChange('address_line1', e.target.value)}
                className={`glass-input ${
                  errors.address_line1 ? 'border-red-400/50' : ''
                }`}
                placeholder="123 Main Street"
              />
              {errors.address_line1 && <p className="mt-1 text-sm text-red-400">{errors.address_line1}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium glass-text-primary mb-2">
                Address Line 2
              </label>
              <input
                type="text"
                value={formData.address_line2}
                onChange={(e) => handleInputChange('address_line2', e.target.value)}
                className="glass-input"
                placeholder="Suite 100 (optional)"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  City *
                </label>
                <input
                  type="text"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  className={`glass-input ${
                    errors.city ? 'border-red-400/50' : ''
                  }`}
                  placeholder="New York"
                />
                {errors.city && <p className="mt-1 text-sm text-red-400">{errors.city}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  State *
                </label>
                <input
                  type="text"
                  value={formData.state}
                  onChange={(e) => handleInputChange('state', e.target.value)}
                  className={`glass-input ${
                    errors.state ? 'border-red-400/50' : ''
                  }`}
                  placeholder="NY"
                />
                {errors.state && <p className="mt-1 text-sm text-red-400">{errors.state}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Postal Code *
                </label>
                <input
                  type="text"
                  value={formData.postal_code}
                  onChange={(e) => handleInputChange('postal_code', e.target.value)}
                  className={`glass-input ${
                    errors.postal_code ? 'border-red-400/50' : ''
                  }`}
                  placeholder="10001"
                />
                {errors.postal_code && <p className="mt-1 text-sm text-red-400">{errors.postal_code}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Country
                </label>
                <select
                  value={formData.country}
                  onChange={(e) => handleInputChange('country', e.target.value)}
                  className="glass-select"
                >
                  <option value="Greece">Greece</option>
                  <option value="Germany">Germany</option>
                  <option value="France">France</option>
                  <option value="Italy">Italy</option>
                  <option value="Spain">Spain</option>
                  <option value="United Kingdom">United Kingdom</option>
                  <option value="Netherlands">Netherlands</option>
                  <option value="Belgium">Belgium</option>
                  <option value="Austria">Austria</option>
                  <option value="Switzerland">Switzerland</option>
                  <option value="Portugal">Portugal</option>
                  <option value="Poland">Poland</option>
                  <option value="Czech Republic">Czech Republic</option>
                  <option value="Hungary">Hungary</option>
                  <option value="Romania">Romania</option>
                  <option value="Bulgaria">Bulgaria</option>
                  <option value="Croatia">Croatia</option>
                  <option value="Serbia">Serbia</option>
                  <option value="North Macedonia">North Macedonia</option>
                  <option value="Albania">Albania</option>
                  <option value="Montenegro">Montenegro</option>
                  <option value="Bosnia and Herzegovina">Bosnia and Herzegovina</option>
                  <option value="Turkey">Turkey</option>
                  <option value="Cyprus">Cyprus</option>
                  <option value="Malta">Malta</option>
                  <option value="Ireland">Ireland</option>
                  <option value="Denmark">Denmark</option>
                  <option value="Sweden">Sweden</option>
                  <option value="Norway">Norway</option>
                  <option value="Finland">Finland</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Timezone
                </label>
                <select
                  value={formData.timezone}
                  onChange={(e) => handleInputChange('timezone', e.target.value)}
                  className="glass-select"
                >
                  {timezones.map(tz => (
                    <option key={tz} value={tz}>{tz}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="flex items-center gap-3 mb-6">
              <Phone className="h-6 w-6 text-blue-400" />
              <h3 className="text-lg font-semibold glass-text-primary">Contact & Services</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className={`glass-input ${
                    errors.phone ? 'border-red-400/50' : ''
                  }`}
                  placeholder="+****************"
                />
                {errors.phone && <p className="mt-1 text-sm text-red-400">{errors.phone}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={`glass-input ${
                    errors.email ? 'border-red-400/50' : ''
                  }`}
                  placeholder="<EMAIL>"
                />
                {errors.email && <p className="mt-1 text-sm text-red-400">{errors.email}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium glass-text-primary mb-2">
                Website
              </label>
              <input
                type="url"
                value={formData.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                className="glass-input"
                placeholder="https://restaurant.com/downtown"
              />
            </div>

            <div>
              <label className="block text-sm font-medium glass-text-primary mb-4">
                Available Services
              </label>
              <div className="space-y-3">
                <label className="flex items-center glass-interactive p-2 rounded-lg transition-colors">
                  <input
                    type="checkbox"
                    checked={formData.has_delivery}
                    onChange={(e) => handleInputChange('has_delivery', e.target.checked)}
                    className="glass-checkbox"
                  />
                  <span className="ml-2 text-sm glass-text-primary">🚚 Delivery Service</span>
                </label>
                <label className="flex items-center glass-interactive p-2 rounded-lg transition-colors">
                  <input
                    type="checkbox"
                    checked={formData.has_pickup}
                    onChange={(e) => handleInputChange('has_pickup', e.target.checked)}
                    className="glass-checkbox"
                  />
                  <span className="ml-2 text-sm glass-text-primary">🛍️ Pickup Orders</span>
                </label>
                <label className="flex items-center glass-interactive p-2 rounded-lg transition-colors">
                  <input
                    type="checkbox"
                    checked={formData.has_dine_in}
                    onChange={(e) => handleInputChange('has_dine_in', e.target.checked)}
                    className="glass-checkbox"
                  />
                  <span className="ml-2 text-sm glass-text-primary">🍽️ Dine-in Service</span>
                </label>
                <label className="flex items-center glass-interactive p-2 rounded-lg transition-colors">
                  <input
                    type="checkbox"
                    checked={formData.has_drive_through}
                    onChange={(e) => handleInputChange('has_drive_through', e.target.checked)}
                    className="glass-checkbox"
                  />
                  <span className="ml-2 text-sm glass-text-primary">🚗 Drive-Through</span>
                </label>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="flex items-center gap-3 mb-6">
              <Settings className="h-6 w-6 text-blue-400" />
              <h3 className="text-lg font-semibold glass-text-primary">Operational Settings</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Seating Capacity
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.seating_capacity}
                  onChange={(e) => handleInputChange('seating_capacity', parseInt(e.target.value) || 0)}
                  className="glass-input"
                  placeholder="50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Parking Spaces
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.parking_spaces}
                  onChange={(e) => handleInputChange('parking_spaces', parseInt(e.target.value) || 0)}
                  className="glass-input"
                  placeholder="20"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Delivery Radius (miles)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.1"
                  value={formData.delivery_radius}
                  onChange={(e) => handleInputChange('delivery_radius', parseFloat(e.target.value) || 0)}
                  className={`glass-input ${
                    errors.delivery_radius ? 'border-red-400/50' : ''
                  }`}
                  placeholder="5.0"
                />
                {errors.delivery_radius && <p className="mt-1 text-sm text-red-400">{errors.delivery_radius}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Delivery Fee ($)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.delivery_fee}
                  onChange={(e) => handleInputChange('delivery_fee', parseFloat(e.target.value) || 0)}
                  className={`glass-input ${
                    errors.delivery_fee ? 'border-red-400/50' : ''
                  }`}
                  placeholder="2.99"
                />
                {errors.delivery_fee && <p className="mt-1 text-sm text-red-400">{errors.delivery_fee}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Minimum Order ($)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.minimum_order_amount}
                  onChange={(e) => handleInputChange('minimum_order_amount', parseFloat(e.target.value) || 0)}
                  className={`glass-input ${
                    errors.minimum_order_amount ? 'border-red-400/50' : ''
                  }`}
                  placeholder="15.00"
                />
                {errors.minimum_order_amount && <p className="mt-1 text-sm text-red-400">{errors.minimum_order_amount}</p>}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  }

  return (
    <div className="glass-modal-backdrop">
      <div className="glass-container glass-primary w-full max-w-4xl max-h-[90vh] overflow-hidden fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-white/20">
          <div>
            <h2 className="text-2xl font-bold glass-text-primary">Add New Branch</h2>
            <p className="glass-text-secondary mt-1">Step {currentStep} of {totalSteps}</p>
          </div>
          <button
            onClick={onClose}
            className="glass-interactive p-2 rounded-lg transition-colors"
          >
            <X className="h-6 w-6 glass-text-secondary" />
          </button>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-4 glass-secondary border-b border-white/20">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium glass-text-primary">Progress</span>
            <span className="text-sm glass-text-secondary">{Math.round((currentStep / totalSteps) * 100)}%</span>
          </div>
          <div className="w-full bg-white/20 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full transition-all duration-300 shadow-lg"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh] scrollbar-glassmorphism">
          {renderStepContent()}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t border-white/20 glass-secondary">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="glass-interactive px-4 py-2 glass-text-secondary hover:glass-text-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Previous
          </button>

          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="glass-interactive px-4 py-2 glass-text-secondary hover:glass-text-primary transition-colors"
            >
              Cancel
            </button>
            
            {currentStep < totalSteps ? (
              <button
                onClick={handleNext}
                className="glass-button glass-primary glass-text-primary px-6 py-2 transition-colors"
              >
                Next
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                disabled={loading}
                className="glass-button glass-success glass-text-primary px-6 py-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Creating...
                  </>
                ) : (
                  'Create Branch'
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}