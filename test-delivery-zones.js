const { createClient } = require('@supabase/supabase-js');

// Simple test to check what's actually in the delivery_zones table
async function testDeliveryZones() {
  const supabaseUrl = 'https://voiwzwyfnkzvcffuxpwl.supabase.co';
  const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0MDU2MzIsImV4cCI6MjA2NDk4MTYzMn0.RvabQ6EGP1gBb0w8J8_VpaqqXgkpEFR7Nbbkmw11wNA';

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    console.log('Testing basic table access...');
    
    // Try to select just the id field first
    const { data: idData, error: idError } = await supabase
      .from('delivery_zones')
      .select('id')
      .limit(1);

    if (idError) {
      console.error('Error selecting id:', idError);
      return;
    }

    console.log('✅ Can select id field:', idData);

    // Try to select name field
    const { data: nameData, error: nameError } = await supabase
      .from('delivery_zones')
      .select('name')
      .limit(1);

    if (nameError) {
      console.error('Error selecting name:', nameError);
      return;
    }

    console.log('✅ Can select name field:', nameData);

    // Try to select coordinates field
    const { data: coordData, error: coordError } = await supabase
      .from('delivery_zones')
      .select('coordinates')
      .limit(1);

    if (coordError) {
      console.error('Error selecting coordinates:', coordError);
      return;
    }

    console.log('✅ Can select coordinates field:', coordData);

    // Try to insert a very minimal record
    const { data: insertData, error: insertError } = await supabase
      .from('delivery_zones')
      .insert([{
        name: 'Test Zone',
        coordinates: [{"lat": 37.7749, "lng": -122.4194}],
        delivery_fee: 5.00
      }])
      .select();

    if (insertError) {
      console.error('Error inserting minimal record:', insertError);
      return;
    }

    console.log('✅ Successfully inserted minimal record:', insertData);

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testDeliveryZones();