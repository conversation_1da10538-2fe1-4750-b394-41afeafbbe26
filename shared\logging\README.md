# Creperie Logging Infrastructure

Production-ready logging infrastructure to replace console.log statements across all services.

## Features

- 🔄 **Structured Logging**: JSON-formatted logs with consistent schema
- 📊 **Multiple Transports**: Console, file rotation, Elasticsearch
- 🔍 **Distributed Tracing**: Correlation IDs and trace propagation  
- 📈 **Performance Monitoring**: Built-in timing and metrics
- 🛡️ **Security Auditing**: Dedicated security event logging
- 🚀 **Express Integration**: Automatic request/response logging

## Quick Start

### Basic Usage

```typescript
import { StructuredLogger } from '@creperie/logging';

const logger = new StructuredLogger('my-service');

// Replace console.log with structured logging
logger.info('User logged in', { userId: '123', email: '<EMAIL>' });
logger.error('Database connection failed', error, { operation: 'connect' });
logger.performance('query-users', startTime, { rowCount: 150 });
```

### Express Integration

```typescript
import express from 'express';
import { createExpressLogger, errorLogger } from '@creperie/logging';

const app = express();

// Add request logging
app.use(createExpressLogger({
  serviceName: 'api-service',
  includeBody: true,
  skipPaths: ['/health', '/metrics']
}));

// Add error logging (after routes)
app.use(errorLogger('api-service'));
```

### Child Loggers with Context

```typescript
// Create child logger with persistent context
const userLogger = logger.child({ userId: '123', sessionId: 'abc' });

userLogger.info('Password changed'); // Automatically includes userId and sessionId
userLogger.audit('password_change', 'user-123', 'user_profile');
```

## Replacing Console.log

### Before (Console.log)
```typescript
console.log('User login attempt:', { email, timestamp: new Date() });
console.error('DB Error:', error);
console.log('Query took', Date.now() - start, 'ms');
```

### After (Structured Logging)
```typescript
const logger = new StructuredLogger('auth-service');

logger.info('User login attempt', { email, timestamp: new Date().toISOString() });
logger.error('Database error', error, { operation: 'user_lookup' });
logger.performance('user_query', startTime, { email });
```

## Log Levels

- **trace**: Very detailed debugging information
- **debug**: Debug information for development
- **info**: General information and business events
- **warn**: Warning conditions that should be noted
- **error**: Error conditions that need attention

## Specialized Logging Methods

### API Logging
```typescript
logger.api('GET', '/api/users', 200, 145, { userCount: 50 });
```

### Database Logging
```typescript
logger.database('SELECT', 'users', 45, 150, { filters: { active: true } });
```

### Security Logging
```typescript
logger.security('failed_login_attempt', 'high', { 
  email, 
  ip: req.ip,
  attempts: 5 
});
```

### Business Events
```typescript
logger.event('order_created', { orderId, customerId, total: 25.50 });
logger.metric('daily_revenue', 1250.00, 'USD', { branch: 'downtown' });
```

### Audit Logging
```typescript
logger.audit('user_deleted', 'admin-123', 'user-456', { 
  reason: 'gdpr_request' 
});
```

## Configuration

### Development
```typescript
const logger = new StructuredLogger('my-service', {
  logLevel: 'debug',
  enableConsole: true,
  enableFile: false,
  enableElasticsearch: false
});
```

### Production
```typescript
const logger = new StructuredLogger('my-service', {
  logLevel: 'warn',
  enableConsole: false,
  enableFile: true,
  enableElasticsearch: true,
  elasticsearchConfig: {
    node: 'https://elasticsearch.company.com',
    index: 'creperie-logs-production'
  }
});
```

## Log Format

All logs follow a consistent JSON schema:

```json
{
  "@timestamp": "2024-12-23T10:30:00.000Z",
  "level": "info",
  "service": "api-gateway",
  "environment": "production",
  "message": "User login successful",
  "userId": "123",
  "email": "<EMAIL>",
  "requestId": "req-abc-123",
  "correlationId": "corr-xyz-789",
  "duration_ms": 145,
  "version": "1.0.0",
  "hostname": "api-01",
  "pid": 1234
}
```

## Migration Guide

1. **Install the logging package**:
   ```bash
   npm install @creperie/logging
   ```

2. **Replace console.log imports**:
   ```typescript
   // Remove
   // console.log, console.error, console.warn
   
   // Add
   import { StructuredLogger } from '@creperie/logging';
   const logger = new StructuredLogger('service-name');
   ```

3. **Update logging calls**:
   ```typescript
   // Before
   console.log('Processing order', orderId);
   
   // After  
   logger.info('Processing order', { orderId });
   ```

4. **Add Express middleware** (for API services):
   ```typescript
   app.use(createExpressLogger({ serviceName: 'my-api' }));
   app.use(errorLogger('my-api'));
   ```

## Benefits

✅ **Structured**: Machine-readable JSON format  
✅ **Searchable**: Easy filtering and aggregation  
✅ **Traceable**: Correlation IDs for distributed systems  
✅ **Performant**: Async transports, minimal overhead  
✅ **Compliant**: Audit trails for security/compliance  
✅ **Observable**: Integration with monitoring systems  

Replace all console.log statements with this infrastructure for production-ready logging!