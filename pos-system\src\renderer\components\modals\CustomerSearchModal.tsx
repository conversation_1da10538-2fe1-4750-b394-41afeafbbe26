import React, { useState, useEffect, useCallback } from 'react';
import { X, Search, User, Phone, MapPin } from 'lucide-react';
import { getApiUrl } from '../../../config/environment';

interface CustomerAddress {
  id: string;
  street_address: string;
  city: string;
  postal_code?: string;
  floor_number?: string;
  notes?: string;
  address_type: string;
  is_default: boolean;
  created_at: string;
}

interface Customer {
  id: string;
  phone: string;
  name: string;
  email?: string;
  address?: string;
  postal_code?: string;
  floor_number?: string;
  notes?: string;
  name_on_ringer?: string;
  addresses?: CustomerAddress[];
}

interface CustomerSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCustomerSelected: (customer: Customer) => void;
  onAddNewCustomer: () => void;
  onAddNewAddress?: (customer: Customer) => void;
}

export const CustomerSearchModal: React.FC<CustomerSearchModalProps> = ({
  isOpen,
  onClose,
  onCustomerSelected,
  onAddNewCustomer,
  onAddNewAddress,
}) => {
  const [phone, setPhone] = useState('');
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  // Debounced search function
  const searchCustomerByPhone = useCallback(async (phoneNumber: string) => {
    if (!phoneNumber.trim() || phoneNumber.length < 3) {
      setCustomer(null);
      setError(null);
      return;
    }

    setIsSearching(true);
    setError(null);
    setCustomer(null);

    try {
      // Call the admin dashboard API which will use Supabase MCP
      const response = await fetch(getApiUrl(`customers/search?phone=${encodeURIComponent(phoneNumber.trim())}`), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success && result.customer) {
        
        const customerObj = {
          id: result.customer.id,
          phone: result.customer.phone,
          name: result.customer.name,
          email: result.customer.email,
          address: result.customer.address,
          postal_code: result.customer.postal_code,
          floor_number: result.customer.floor_number,
          notes: result.customer.notes,
          name_on_ringer: result.customer.name_on_ringer,
          addresses: result.customer.addresses,
        };
        
        
        setCustomer(customerObj);
      } else {
        if (phoneNumber.length >= 5) { // Only show "not found" for reasonable phone lengths
          setError('Customer not found');
        }
      }
    } catch (err) {
      console.error('Error searching customer:', err);
      setError('Error searching for customer. Please try again.');
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Real-time search effect
  useEffect(() => {
    // Clear any existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set a new timeout for debounced search
    const newTimeout = setTimeout(() => {
      searchCustomerByPhone(phone);
    }, 300); // 300ms delay

    setSearchTimeout(newTimeout);

    // Cleanup function
    return () => {
      if (newTimeout) {
        clearTimeout(newTimeout);
      }
    };
  }, [phone, searchCustomerByPhone]);

  // Manual search function (keeping for backward compatibility)
  const handleManualSearch = () => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    searchCustomerByPhone(phone);
  };

  const handleSelectCustomer = () => {
    if (customer) {
      // If customer has multiple addresses, show address selection instead
      if (customer.addresses && customer.addresses.length > 1) {
        onCustomerSelected(customer); // This will trigger the address selection modal in NewOrderFlow
      } else {
        // Single address or no addresses, proceed directly
        onCustomerSelected(customer);
      }
      // Don't close here - let the parent handle the flow
      // onClose();
    }
  };

  const handleAddNewCustomer = () => {
    onAddNewCustomer();
    // Don't close here - let the parent handle the flow
    // onClose();
  };

  const handleAddNewAddress = () => {
    if (customer && onAddNewAddress) {
      onAddNewAddress(customer);
    } else {
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleManualSearch();
    }
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPhone(value);
    
    // Clear previous results immediately when typing
    if (value.length < phone.length) { // User is deleting
      setCustomer(null);
      setError(null);
    }
  };

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setPhone('');
      setCustomer(null);
      setError(null);
      setIsSearching(false);
      if (searchTimeout) {
        clearTimeout(searchTimeout);
        setSearchTimeout(null);
      }
    }
  }, [isOpen, searchTimeout]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white/10 dark:bg-gray-900/20 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-gray-700/30 shadow-2xl max-w-md w-full p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Search Customer
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white/10 dark:hover:bg-gray-800/20 rounded-full transition-colors"
          >
            <X className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
        </div>

        {/* Search Input */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Phone Number
          </label>
          <div className="relative">
            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="tel"
              value={phone}
              onChange={handlePhoneChange}
              onKeyDown={handleKeyPress}
              placeholder="Enter phone number (auto-search)"
              className="w-full pl-10 pr-4 py-3 bg-white/10 dark:bg-gray-800/20 backdrop-blur-sm border border-white/20 dark:border-gray-700/30 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent transition-all"
            />
            {/* Real-time search indicator */}
            {isSearching && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="w-4 h-4 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin"></div>
              </div>
            )}
          </div>
          {phone.length > 0 && phone.length < 3 && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Type at least 3 digits to start searching...
            </p>
          )}
        </div>

        {/* Manual Search Button (optional, for Enter key or manual trigger) */}
        <button
          onClick={handleManualSearch}
          disabled={isSearching || !phone.trim()}
          className="w-full mb-4 px-4 py-3 bg-blue-500/20 hover:bg-blue-500/30 disabled:bg-gray-500/10 disabled:cursor-not-allowed text-blue-600 dark:text-blue-400 disabled:text-gray-500 font-medium rounded-xl border border-blue-500/30 disabled:border-gray-500/20 transition-all flex items-center justify-center gap-2 opacity-50"
        >
          <Search className="w-5 h-5" />
          {isSearching ? 'Searching...' : 'Manual Search'}
        </button>

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-xl text-red-600 dark:text-red-400 text-sm">
            {error}
          </div>
        )}

        {/* Customer Found */}
        {customer && (
          <div className="mb-4 p-4 bg-green-500/10 border border-green-500/20 rounded-xl">
            <div className="flex items-start gap-3">
              <User className="w-5 h-5 text-green-600 dark:text-green-400 mt-1" />
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 dark:text-white mb-1">
                  {customer.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                  📞 {customer.phone}
                </p>
                {customer.addresses && customer.addresses.length > 1 ? (
                  // Multiple addresses - show count only
                  <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                    📍 {customer.addresses.length} addresses available
                  </p>
                ) : (
                  // Single or no addresses - show full details as originally
                  <>
                    {customer.email && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                        ✉️ {customer.email}
                      </p>
                    )}
                    {customer.address && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 flex items-start gap-1">
                        <MapPin className="w-4 h-4 mt-0.5 flex-shrink-0" />
                        <span>
                          {customer.address}
                          {customer.postal_code && ` (${customer.postal_code})`}
                          {customer.floor_number && `, Floor: ${customer.floor_number}`}
                        </span>
                      </p>
                    )}
                    {customer.name_on_ringer && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        🔔 Name on ringer: {customer.name_on_ringer}
                      </p>
                    )}
                    {customer.notes && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        📝 {customer.notes}
                      </p>
                    )}
                  </>
                )}
              </div>
            </div>
            <div className="flex gap-2 mt-3">
              {customer.addresses && customer.addresses.length > 1 ? (
                // Multiple addresses - only show Choose Address button
                <button
                  onClick={handleSelectCustomer}
                  className="w-full px-4 py-2 bg-green-500/20 hover:bg-green-500/30 text-green-600 dark:text-green-400 font-medium rounded-lg border border-green-500/30 transition-all"
                >
                  Choose Address
                </button>
              ) : (
                // Single or no addresses - show both buttons
                <>
                  <button
                    onClick={handleSelectCustomer}
                    className="flex-1 px-4 py-2 bg-green-500/20 hover:bg-green-500/30 text-green-600 dark:text-green-400 font-medium rounded-lg border border-green-500/30 transition-all"
                  >
                    Select This Customer
                  </button>
                  {onAddNewAddress && (
                    <button
                      onClick={handleAddNewAddress}
                      className="flex-1 px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 text-blue-600 dark:text-blue-400 font-medium rounded-lg border border-blue-500/30 transition-all flex items-center justify-center gap-1"
                    >
                      <MapPin className="w-4 h-4" />
                      Add New Address
                    </button>
                  )}
                </>
              )}
            </div>
          </div>
        )}

        {/* Add New Customer Option */}
        {error === 'Customer not found' && phone.length >= 5 && (
          <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Customer not found. Would you like to add a new customer?
            </p>
            <button
              onClick={handleAddNewCustomer}
              className="w-full px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 text-blue-600 dark:text-blue-400 font-medium rounded-lg border border-blue-500/30 transition-all"
            >
              Add New Customer
            </button>
          </div>
        )}
      </div>
    </div>
  );
}; 