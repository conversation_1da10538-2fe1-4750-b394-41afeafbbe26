'use client'

import React from 'react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { GlassCard } from '@/components/ui/glass-components'
import { 
  Search, 
  Filter, 
  Download, 
  UserPlus,
  RefreshCw,
  SortAsc,
  SortDesc
} from 'lucide-react'

interface CustomerFiltersProps {
  searchTerm: string
  onSearchChange: (value: string) => void
  filterStatus: string
  onFilterStatusChange: (value: string) => void
  sortBy: string
  onSortByChange: (value: string) => void
  sortOrder: 'asc' | 'desc'
  onSortOrderChange: (order: 'asc' | 'desc') => void
  onAddCustomer: () => void
  onRefresh: () => void
  onExport: () => void
  totalCustomers: number
  loading: boolean
}

export function CustomerFilters({
  searchTerm,
  onSearchChange,
  filterStatus,
  onFilterStatusChange,
  sortBy,
  onSortByChange,
  sortOrder,
  onSortOrder<PERSON>hange,
  onAddCustomer,
  onRefresh,
  onExport,
  totalCustomers,
  loading
}: CustomerFiltersProps) {
  return (
    <GlassCard className="p-6 mb-6">
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div className="flex-1 max-w-2xl">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-4 w-4" />
            <Input
              placeholder="Search customers by name, phone, or email..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10 bg-white/5 border-white/10 text-white placeholder:text-white/40"
            />
          </div>
        </div>

        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-white/60" />
            <select
              value={filterStatus}
              onChange={(e) => onFilterStatusChange(e.target.value)}
              className="bg-white/5 border border-white/10 rounded-md px-3 py-2 text-white text-sm"
            >
              <option value="all">All Customers</option>
              <option value="vip">VIP Only</option>
              <option value="recent">Recent</option>
              <option value="frequent">Frequent</option>
            </select>
          </div>

          <div className="flex items-center gap-2">
            <select
              value={sortBy}
              onChange={(e) => onSortByChange(e.target.value)}
              className="bg-white/5 border border-white/10 rounded-md px-3 py-2 text-white text-sm"
            >
              <option value="created_at">Date Added</option>
              <option value="name">Name</option>
              <option value="total_orders">Total Orders</option>
              <option value="loyalty_points">Loyalty Points</option>
            </select>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => onSortOrderChange(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="text-white/60 hover:text-white hover:bg-white/10"
            >
              {sortOrder === 'asc' ? (
                <SortAsc className="h-4 w-4" />
              ) : (
                <SortDesc className="h-4 w-4" />
              )}
            </Button>
          </div>

          <div className="h-6 w-px bg-white/20" />

          <Button
            variant="ghost"
            size="sm"
            onClick={onRefresh}
            disabled={loading}
            className="text-white/60 hover:text-white hover:bg-white/10"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={onExport}
            className="text-white/60 hover:text-white hover:bg-white/10"
          >
            <Download className="h-4 w-4" />
          </Button>

          <Button
            onClick={onAddCustomer}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Add Customer
          </Button>
        </div>
      </div>

      <div className="mt-4 flex items-center justify-between text-sm text-white/60">
        <span>
          {totalCustomers > 0 ? (
            <>Showing {totalCustomers} customer{totalCustomers !== 1 ? 's' : ''}</>
          ) : (
            'No customers found'
          )}
        </span>
        
        {searchTerm && (
          <span>
            Filtered by: "{searchTerm}"
          </span>
        )}
      </div>
    </GlassCard>
  )
}