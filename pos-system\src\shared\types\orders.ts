// Consolidated Order Types for POS System
// Shared between main and renderer processes

export type OrderStatus = 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled';
export type OrderType = 'dine-in' | 'takeaway' | 'delivery';
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
export type PaymentMethod = 'cash' | 'card' | 'digital';
export type SyncStatus = 'synced' | 'pending' | 'failed';

// Order item options interface
export interface OrderItemOption {
  id: string;
  name: string;
  value: string | number | boolean;
  price?: number;
  category?: string;
}

// Order item interface
export interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
  options?: OrderItemOption[];
  customizations?: any[];
}

// Order pricing breakdown
export interface OrderPricing {
  subtotal: number;
  deliveryFee: number;
  pickupDiscount: number;
  serviceFee: number;
  taxAmount: number;
  totalAmount: number;
  deliveryZoneId?: string | null;
  deliveryZoneName?: string | null;
  pricingCalculatedAt?: string | null;
  pricingVersion?: string | null;
  estimatedTime?: {
    min: number;
    max: number;
    message: string;
  } | null;
}

// Main Order interface - consolidated from all sources
export interface Order {
  id: string;
  order_number?: string;
  orderNumber: string; // Required for renderer compatibility
  status: OrderStatus;
  items: OrderItem[];
  total_amount: number;
  totalAmount: number; // Required for renderer compatibility
  
  // Customer information
  customer_name?: string;
  customerName?: string; // For backward compatibility
  customer_phone?: string;
  customerPhone?: string; // For backward compatibility
  customer_email?: string | null; // Allow null for compatibility
  customer_id?: string | null; // Allow null for compatibility
  
  // Order details
  order_type?: OrderType;
  orderType: OrderType; // Required for renderer compatibility
  table_number?: string | null; // Allow null for compatibility
  tableNumber?: string; // For backward compatibility
  delivery_address?: string | null; // Allow null for compatibility
  address?: string; // For backward compatibility
  special_instructions?: string;
  notes?: string; // For backward compatibility
  
  // Timestamps
  created_at: string;
  createdAt: string; // Required for renderer compatibility
  updated_at: string;
  updatedAt: string; // Required for renderer compatibility
  
  // Timing
  estimated_time?: number; // in minutes
  estimatedTime?: number; // For backward compatibility
  
  // Payment information
  payment_status?: PaymentStatus;
  paymentStatus?: PaymentStatus; // For backward compatibility
  payment_method?: PaymentMethod;
  paymentMethod?: PaymentMethod; // For backward compatibility
  payment_transaction_id?: string;
  paymentTransactionId?: string; // For backward compatibility
  
  // Sync information
  supabase_id?: string;
  sync_status?: SyncStatus;
  
  // Enhanced pricing breakdown
  pricing?: OrderPricing;
  subtotal?: number;
  deliveryFee?: number;
  pickupDiscount?: number;
  serviceFee?: number;
  taxAmount?: number;
  deliveryZoneId?: string | null;
  pricingCalculatedAt?: string | null;
  pricingVersion?: string | null;
}

// Database row interface for SQLite
export interface OrderRow {
  id: string;
  order_number?: string;
  customer_name?: string;
  customer_phone?: string;
  customer_email?: string;
  items: string; // JSON string
  total_amount: number;
  status: OrderStatus;
  order_type?: OrderType;
  table_number?: string;
  delivery_address?: string;
  special_instructions?: string;
  created_at: string;
  updated_at: string;
  estimated_time?: number;
  supabase_id?: string;
  sync_status: SyncStatus;
  payment_status?: PaymentStatus;
  payment_method?: string;
  payment_transaction_id?: string;
}

// Order filters for queries
export interface OrderFilters {
  status?: OrderStatus;
  date?: string;
  fromDate?: string;
  toDate?: string;
  limit?: number;
  offset?: number;
  customer_id?: string;
  order_type?: OrderType;
}

// Order creation parameters
export interface OrderCreateParams {
  customer_name?: string;
  customer_phone?: string;
  customer_email?: string;
  customer_id?: string;
  items: OrderItem[];
  total_amount: number;
  order_type?: OrderType;
  table_number?: string;
  delivery_address?: string;
  special_instructions?: string;
  estimated_time?: number;
  payment_method?: PaymentMethod;
  payment_transaction_id?: string;
}

// Order update parameters
export interface OrderUpdateParams {
  status?: OrderStatus;
  customer_name?: string;
  customer_phone?: string;
  customer_email?: string;
  items?: OrderItem[];
  total_amount?: number;
  order_type?: OrderType;
  table_number?: string;
  delivery_address?: string;
  special_instructions?: string;
  estimated_time?: number;
  payment_status?: PaymentStatus;
  payment_method?: PaymentMethod;
  payment_transaction_id?: string;
}
