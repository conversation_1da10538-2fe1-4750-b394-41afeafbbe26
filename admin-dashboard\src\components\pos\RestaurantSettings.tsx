'use client'

import { Receipt, FileText, Package, Archive } from 'lucide-react'
import { GlassCard } from '@/components/ui/glass-components'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useTheme } from '@/contexts/theme-context'

interface ReceiptSettings {
  header_text: string
  footer_text: string
  show_logo: boolean
  show_qr_code: boolean
  show_social_media: boolean
  receipt_width: string
  font_size: string
  print_customer_copy: boolean
  print_merchant_copy: boolean
  email_receipts: boolean
  sms_receipts: boolean
}

interface PrinterSettings {
  receipt_printer_ip: string
  receipt_printer_port: number
  kitchen_printer_ip: string
  kitchen_printer_port: number
  paper_size: string
  print_logo: boolean
  print_order_number: boolean
  print_date_time: boolean
  copy_count: number
}

interface InventorySettings {
  auto_sync: boolean
  sync_interval: number
  low_stock_alerts: boolean
  low_stock_threshold: number
  auto_disable_out_of_stock: boolean
  track_ingredient_usage: boolean
  batch_sync: boolean
  sync_on_order: boolean
  inventory_adjustments: boolean
  waste_tracking: boolean
}

interface RestaurantSettingsProps {
  receiptSettings: ReceiptSettings
  printerSettings: PrinterSettings
  inventorySettings: InventorySettings
  onReceiptSettingsChange: (settings: ReceiptSettings) => void
  onPrinterSettingsChange: (settings: PrinterSettings) => void
  onInventorySettingsChange: (settings: InventorySettings) => void
}

export function RestaurantSettings({
  receiptSettings,
  printerSettings,
  inventorySettings,
  onReceiptSettingsChange,
  onPrinterSettingsChange,
  onInventorySettingsChange
}: RestaurantSettingsProps) {
  const { isDarkTheme } = useTheme()
  return (
    <div className="space-y-6">
      {/* Receipt Settings */}
      <GlassCard className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Receipt className="h-5 w-5 text-purple-400" />
          <h3 className={`text-xl font-semibold transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>Receipt Settings</h3>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Header Text</label>
              <Textarea
                value={receiptSettings.header_text}
                onChange={(e) => onReceiptSettingsChange({ ...receiptSettings, header_text: e.target.value })}
                className={`border transition-all duration-1000 ${
                  isDarkTheme
                    ? 'bg-white/5 border-white/10 text-white'
                    : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="Thank you for your visit!"
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Footer Text</label>
              <Textarea
                value={receiptSettings.footer_text}
                onChange={(e) => onReceiptSettingsChange({ ...receiptSettings, footer_text: e.target.value })}
                className={`border transition-all duration-1000 ${
                  isDarkTheme
                    ? 'bg-white/5 border-white/10 text-white'
                    : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="Visit us again soon!"
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Receipt Width</label>
              <Select
                value={receiptSettings.receipt_width}
                onValueChange={(value: string) => onReceiptSettingsChange({ ...receiptSettings, receipt_width: value })}
              >
                <SelectTrigger className={`border transition-all duration-1000 ${
                  isDarkTheme
                    ? 'bg-white/5 border-white/10 text-white'
                    : 'bg-black/5 border-black/10 text-black'
                }`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="58mm">58mm</SelectItem>
                  <SelectItem value="80mm">80mm</SelectItem>
                  <SelectItem value="112mm">112mm</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Font Size</label>
              <Select
                value={receiptSettings.font_size}
                onValueChange={(value: string) => onReceiptSettingsChange({ ...receiptSettings, font_size: value })}
              >
                <SelectTrigger className={`border transition-all duration-1000 ${
                  isDarkTheme
                    ? 'bg-white/5 border-white/10 text-white'
                    : 'bg-black/5 border-black/10 text-black'
                }`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="small">Small</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="large">Large</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Show Logo</span>
              <Switch
                checked={receiptSettings.show_logo}
                onCheckedChange={(checked: boolean) => onReceiptSettingsChange({ ...receiptSettings, show_logo: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Show QR Code</span>
              <Switch
                checked={receiptSettings.show_qr_code}
                onCheckedChange={(checked: boolean) => onReceiptSettingsChange({ ...receiptSettings, show_qr_code: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Show Social Media</span>
              <Switch
                checked={receiptSettings.show_social_media}
                onCheckedChange={(checked: boolean) => onReceiptSettingsChange({ ...receiptSettings, show_social_media: checked })}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Print Customer Copy</span>
              <Switch
                checked={receiptSettings.print_customer_copy}
                onCheckedChange={(checked: boolean) => onReceiptSettingsChange({ ...receiptSettings, print_customer_copy: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Print Merchant Copy</span>
              <Switch
                checked={receiptSettings.print_merchant_copy}
                onCheckedChange={(checked: boolean) => onReceiptSettingsChange({ ...receiptSettings, print_merchant_copy: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Email Receipts</span>
              <Switch
                checked={receiptSettings.email_receipts}
                onCheckedChange={(checked: boolean) => onReceiptSettingsChange({ ...receiptSettings, email_receipts: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>SMS Receipts</span>
              <Switch
                checked={receiptSettings.sms_receipts}
                onCheckedChange={(checked: boolean) => onReceiptSettingsChange({ ...receiptSettings, sms_receipts: checked })}
              />
            </div>
          </div>
        </div>
      </GlassCard>

      {/* Printer Settings */}
      <GlassCard className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <FileText className="h-5 w-5 text-blue-400" />
          <h3 className={`text-xl font-semibold transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>Printer Settings</h3>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Receipt Printer IP</label>
              <Input
                value={printerSettings.receipt_printer_ip}
                onChange={(e) => onPrinterSettingsChange({ ...printerSettings, receipt_printer_ip: e.target.value })}
                className={`border transition-all duration-1000 ${
                  isDarkTheme
                    ? 'bg-white/5 border-white/10 text-white'
                    : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="*************"
              />
            </div>

            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Receipt Printer Port</label>
              <Input
                type="number"
                value={printerSettings.receipt_printer_port}
                onChange={(e) => onPrinterSettingsChange({ ...printerSettings, receipt_printer_port: parseInt(e.target.value) || 9100 })}
                className={`border transition-all duration-1000 ${
                  isDarkTheme
                    ? 'bg-white/5 border-white/10 text-white'
                    : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="9100"
              />
            </div>

            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Kitchen Printer IP</label>
              <Input
                value={printerSettings.kitchen_printer_ip}
                onChange={(e) => onPrinterSettingsChange({ ...printerSettings, kitchen_printer_ip: e.target.value })}
                className={`border transition-all duration-1000 ${
                  isDarkTheme
                    ? 'bg-white/5 border-white/10 text-white'
                    : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="*************"
              />
            </div>

            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Kitchen Printer Port</label>
              <Input
                type="number"
                value={printerSettings.kitchen_printer_port}
                onChange={(e) => onPrinterSettingsChange({ ...printerSettings, kitchen_printer_port: parseInt(e.target.value) || 9100 })}
                className={`border transition-all duration-1000 ${
                  isDarkTheme
                    ? 'bg-white/5 border-white/10 text-white'
                    : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="9100"
              />
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Paper Size</label>
              <Select
                value={printerSettings.paper_size}
                onValueChange={(value: string) => onPrinterSettingsChange({ ...printerSettings, paper_size: value })}
              >
                <SelectTrigger className={`border transition-all duration-1000 ${
                  isDarkTheme
                    ? 'bg-white/5 border-white/10 text-white'
                    : 'bg-black/5 border-black/10 text-black'
                }`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="58mm">58mm</SelectItem>
                  <SelectItem value="80mm">80mm</SelectItem>
                  <SelectItem value="112mm">112mm</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Copy Count</label>
              <Input
                type="number"
                min="1"
                max="5"
                value={printerSettings.copy_count}
                onChange={(e) => onPrinterSettingsChange({ ...printerSettings, copy_count: parseInt(e.target.value) || 1 })}
                className={`border transition-all duration-1000 ${
                  isDarkTheme
                    ? 'bg-white/5 border-white/10 text-white'
                    : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="1"
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Print Logo</span>
              <Switch
                checked={printerSettings.print_logo}
                onCheckedChange={(checked: boolean) => onPrinterSettingsChange({ ...printerSettings, print_logo: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Print Order Number</span>
              <Switch
                checked={printerSettings.print_order_number}
                onCheckedChange={(checked: boolean) => onPrinterSettingsChange({ ...printerSettings, print_order_number: checked })}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Print Date/Time</span>
              <Switch
                checked={printerSettings.print_date_time}
                onCheckedChange={(checked: boolean) => onPrinterSettingsChange({ ...printerSettings, print_date_time: checked })}
              />
            </div>
          </div>
        </div>
      </GlassCard>

      {/* Inventory Settings */}
      <GlassCard className="p-6">
        <div className="flex items-center gap-2 mb-6">
          <Package className="h-5 w-5 text-green-400" />
          <h3 className={`text-xl font-semibold transition-colors duration-1000 ${
            isDarkTheme ? 'text-white' : 'text-black'
          }`}>Inventory Settings</h3>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Sync Interval (minutes)</label>
              <Input
                type="number"
                value={inventorySettings.sync_interval}
                onChange={(e) => onInventorySettingsChange({ ...inventorySettings, sync_interval: parseInt(e.target.value) || 30 })}
                className={`border transition-all duration-1000 ${
                  isDarkTheme
                    ? 'bg-white/5 border-white/10 text-white'
                    : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="30"
              />
            </div>

            <div className="space-y-2">
              <label className={`text-sm transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Low Stock Threshold</label>
              <Input
                type="number"
                value={inventorySettings.low_stock_threshold}
                onChange={(e) => onInventorySettingsChange({ ...inventorySettings, low_stock_threshold: parseInt(e.target.value) || 10 })}
                className={`border transition-all duration-1000 ${
                  isDarkTheme
                    ? 'bg-white/5 border-white/10 text-white'
                    : 'bg-black/5 border-black/10 text-black'
                }`}
                placeholder="10"
              />
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Auto Sync</span>
              <Switch
                checked={inventorySettings.auto_sync}
                onCheckedChange={(checked: boolean) => onInventorySettingsChange({ ...inventorySettings, auto_sync: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Low Stock Alerts</span>
              <Switch
                checked={inventorySettings.low_stock_alerts}
                onCheckedChange={(checked: boolean) => onInventorySettingsChange({ ...inventorySettings, low_stock_alerts: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Auto Disable Out of Stock</span>
              <Switch
                checked={inventorySettings.auto_disable_out_of_stock}
                onCheckedChange={(checked: boolean) => onInventorySettingsChange({ ...inventorySettings, auto_disable_out_of_stock: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Track Ingredient Usage</span>
              <Switch
                checked={inventorySettings.track_ingredient_usage}
                onCheckedChange={(checked: boolean) => onInventorySettingsChange({ ...inventorySettings, track_ingredient_usage: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Batch Sync</span>
              <Switch
                checked={inventorySettings.batch_sync}
                onCheckedChange={(checked: boolean) => onInventorySettingsChange({ ...inventorySettings, batch_sync: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Sync on Order</span>
              <Switch
                checked={inventorySettings.sync_on_order}
                onCheckedChange={(checked: boolean) => onInventorySettingsChange({ ...inventorySettings, sync_on_order: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Inventory Adjustments</span>
              <Switch
                checked={inventorySettings.inventory_adjustments}
                onCheckedChange={(checked: boolean) => onInventorySettingsChange({ ...inventorySettings, inventory_adjustments: checked })}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className={`transition-colors duration-1000 ${
                isDarkTheme ? 'text-white/80' : 'text-black/80'
              }`}>Waste Tracking</span>
              <Switch
                checked={inventorySettings.waste_tracking}
                onCheckedChange={(checked: boolean) => onInventorySettingsChange({ ...inventorySettings, waste_tracking: checked })}
              />
            </div>
          </div>
        </div>
      </GlassCard>
    </div>
  )
}