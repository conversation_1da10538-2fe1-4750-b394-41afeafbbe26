import React from 'react';
import { useTheme } from '../../contexts/theme-context';

interface OrderTypeSelectorProps {
  orderType: "dine-in" | "takeaway" | "delivery";
  setOrderType: React.Dispatch<React.SetStateAction<"dine-in" | "takeaway" | "delivery">>;
}

export const OrderTypeSelector: React.FC<OrderTypeSelectorProps> = ({
  orderType,
  setOrderType,
}) => {
  const { resolvedTheme } = useTheme();

  return (
    <div>
      <h3 className={`text-lg font-semibold mb-3 ${
        resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
      }`}>Order Type</h3>
      <div className="grid grid-cols-3 gap-4">
        {(["dine-in", "takeaway", "delivery"] as const).map((type) => (
          <button
            key={type}
            onClick={() => setOrderType(type)}
            className={`p-4 border rounded-lg text-center transition-all duration-300 backdrop-blur-sm ${
              orderType === type
                ? "border-blue-500 bg-blue-50 text-blue-700"
                : resolvedTheme === 'dark'
                  ? "border-gray-600/50 bg-gray-700/30 text-gray-300 hover:border-gray-500/70 hover:bg-gray-700/50"
                  : "border-gray-200 bg-white/50 text-gray-700 hover:border-gray-300 hover:bg-white/70"
            } hover:scale-[1.02] transform focus:outline-none focus:ring-2 focus:ring-blue-500/30`}
          >
            <div className="text-2xl mb-2">
              {type === "dine-in" && "🍽️"}
              {type === "takeaway" && "🥡"}
              {type === "delivery" && "🚚"}
            </div>
            <div className="font-medium capitalize">
              {type === "dine-in"
                ? "Dine In"
                : type === "takeaway"
                  ? "Takeaway"
                  : "Delivery"}
            </div>
          </button>
        ))}
      </div>
    </div>
  );
}; 