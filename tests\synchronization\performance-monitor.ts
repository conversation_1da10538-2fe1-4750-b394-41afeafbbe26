/**
 * Performance Monitor for Synchronization Operations
 * Monitors and analyzes sync performance across platforms
 */

import { EventEmitter } from 'events';

export interface PerformanceMetric {
  id: string;
  name: string;
  description: string;
  timestamp: Date;
  duration: number;
  operation: string;
  platform: string;
  table: string;
  recordCount: number;
  success: boolean;
  errorMessage?: string;
}

export interface PerformanceBenchmark {
  operation: string;
  platform: string;
  table: string;
  averageDuration: number;
  maxDuration: number;
  minDuration: number;
  successRate: number;
  totalOperations: number;
  threshold: number;
  status: 'good' | 'warning' | 'critical';
}

export interface PerformanceReport {
  generatedAt: Date;
  timeRange: {
    start: Date;
    end: Date;
  };
  totalOperations: number;
  averageResponseTime: number;
  successRate: number;
  benchmarks: PerformanceBenchmark[];
  slowestOperations: PerformanceMetric[];
  recommendations: string[];
  trends: {
    responseTime: 'improving' | 'stable' | 'degrading';
    successRate: 'improving' | 'stable' | 'degrading';
    throughput: 'improving' | 'stable' | 'degrading';
  };
}

export class PerformanceMonitor extends EventEmitter {
  private metrics: PerformanceMetric[];
  private benchmarks: Map<string, PerformanceBenchmark>;
  private isMonitoring: boolean;
  private thresholds: Map<string, number>;

  constructor() {
    super();
    this.metrics = [];
    this.benchmarks = new Map();
    this.isMonitoring = false;
    this.thresholds = new Map();
    
    this.initializeDefaultThresholds();
  }

  /**
   * Initialize default performance thresholds
   */
  private initializeDefaultThresholds(): void {
    // Sync operation thresholds (in milliseconds)
    this.thresholds.set('pos-sync', 2000);
    this.thresholds.set('web-sync', 3000);
    this.thresholds.set('mobile-sync', 3000);
    this.thresholds.set('admin-update', 1000);
    this.thresholds.set('menu-sync', 2500);
    this.thresholds.set('settings-sync', 1500);
    this.thresholds.set('staff-sync', 1000);
  }

  /**
   * Start performance monitoring
   */
  startMonitoring(): void {
    this.isMonitoring = true;
    this.emit('monitoring-started');
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    this.isMonitoring = false;
    this.emit('monitoring-stopped');
  }

  /**
   * Record a performance metric
   */
  recordMetric(
    operation: string,
    platform: string,
    table: string,
    duration: number,
    recordCount: number = 1,
    success: boolean = true,
    errorMessage?: string
  ): PerformanceMetric {
    const metric: PerformanceMetric = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: `${operation}-${platform}-${table}`,
      description: `${operation} operation on ${table} for ${platform}`,
      timestamp: new Date(),
      duration,
      operation,
      platform,
      table,
      recordCount,
      success,
      errorMessage
    };

    if (this.isMonitoring) {
      this.metrics.push(metric);
      this.updateBenchmarks(metric);
      this.checkThresholds(metric);
      
      // Keep only last 10000 metrics
      if (this.metrics.length > 10000) {
        this.metrics = this.metrics.slice(-10000);
      }
    }

    return metric;
  }

  /**
   * Start timing an operation
   */
  startTiming(operationId: string): (
    operation: string,
    platform: string,
    table: string,
    recordCount?: number,
    success?: boolean,
    errorMessage?: string
  ) => PerformanceMetric {
    const startTime = Date.now();
    
    return (
      operation: string,
      platform: string,
      table: string,
      recordCount: number = 1,
      success: boolean = true,
      errorMessage?: string
    ): PerformanceMetric => {
      const duration = Date.now() - startTime;
      return this.recordMetric(operation, platform, table, duration, recordCount, success, errorMessage);
    };
  }

  /**
   * Get performance metrics with optional filtering
   */
  getMetrics(filter?: {
    operation?: string;
    platform?: string;
    table?: string;
    timeRange?: { start: Date; end: Date };
    success?: boolean;
  }): PerformanceMetric[] {
    let filteredMetrics = this.metrics;

    if (filter) {
      if (filter.operation) {
        filteredMetrics = filteredMetrics.filter(m => m.operation === filter.operation);
      }
      if (filter.platform) {
        filteredMetrics = filteredMetrics.filter(m => m.platform === filter.platform);
      }
      if (filter.table) {
        filteredMetrics = filteredMetrics.filter(m => m.table === filter.table);
      }
      if (filter.timeRange) {
        filteredMetrics = filteredMetrics.filter(m => 
          m.timestamp >= filter.timeRange!.start && m.timestamp <= filter.timeRange!.end
        );
      }
      if (filter.success !== undefined) {
        filteredMetrics = filteredMetrics.filter(m => m.success === filter.success);
      }
    }

    return filteredMetrics;
  }

  /**
   * Generate comprehensive performance report
   */
  generateReport(timeRange?: { start: Date; end: Date }): PerformanceReport {
    const metrics = this.getMetrics({ timeRange });
    
    if (metrics.length === 0) {
      return this.getEmptyReport(timeRange);
    }

    const totalOperations = metrics.length;
    const successfulOperations = metrics.filter(m => m.success).length;
    const averageResponseTime = metrics.reduce((sum, m) => sum + m.duration, 0) / totalOperations;
    const successRate = (successfulOperations / totalOperations) * 100;

    const benchmarks = this.generateBenchmarks(metrics);
    const slowestOperations = metrics
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10);

    const recommendations = this.generateRecommendations(metrics, benchmarks);
    const trends = this.analyzeTrends(metrics);

    return {
      generatedAt: new Date(),
      timeRange: timeRange || {
        start: new Date(Math.min(...metrics.map(m => m.timestamp.getTime()))),
        end: new Date(Math.max(...metrics.map(m => m.timestamp.getTime())))
      },
      totalOperations,
      averageResponseTime,
      successRate,
      benchmarks,
      slowestOperations,
      recommendations,
      trends
    };
  }

  /**
   * Set custom threshold for an operation
   */
  setThreshold(operation: string, thresholdMs: number): void {
    this.thresholds.set(operation, thresholdMs);
  }

  /**
   * Get current benchmarks
   */
  getBenchmarks(): PerformanceBenchmark[] {
    return Array.from(this.benchmarks.values());
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics = [];
    this.benchmarks.clear();
    this.emit('metrics-cleared');
  }

  /**
   * Get real-time statistics
   */
  getRealTimeStats(): {
    recentOperations: number;
    averageResponseTime: number;
    successRate: number;
    slowOperations: number;
  } {
    const last5Minutes = new Date(Date.now() - 5 * 60 * 1000);
    const recentMetrics = this.metrics.filter(m => m.timestamp >= last5Minutes);

    if (recentMetrics.length === 0) {
      return {
        recentOperations: 0,
        averageResponseTime: 0,
        successRate: 100,
        slowOperations: 0
      };
    }

    const successful = recentMetrics.filter(m => m.success).length;
    const averageResponseTime = recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length;
    const slowOperations = recentMetrics.filter(m => this.isSlowOperation(m)).length;

    return {
      recentOperations: recentMetrics.length,
      averageResponseTime,
      successRate: (successful / recentMetrics.length) * 100,
      slowOperations
    };
  }

  /**
   * Update benchmarks based on new metric
   */
  private updateBenchmarks(metric: PerformanceMetric): void {
    const key = `${metric.operation}-${metric.platform}-${metric.table}`;
    const existing = this.benchmarks.get(key);

    if (existing) {
      // Update existing benchmark
      const newTotal = existing.totalOperations + 1;
      const newAverage = ((existing.averageDuration * existing.totalOperations) + metric.duration) / newTotal;
      const newSuccess = existing.successRate * existing.totalOperations + (metric.success ? 1 : 0);

      existing.averageDuration = newAverage;
      existing.maxDuration = Math.max(existing.maxDuration, metric.duration);
      existing.minDuration = Math.min(existing.minDuration, metric.duration);
      existing.successRate = (newSuccess / newTotal) * 100;
      existing.totalOperations = newTotal;
      existing.status = this.getBenchmarkStatus(existing);
    } else {
      // Create new benchmark
      const threshold = this.thresholds.get(`${metric.operation}-${metric.platform}`) || 
                       this.thresholds.get(metric.operation) || 
                       3000;

      const benchmark: PerformanceBenchmark = {
        operation: metric.operation,
        platform: metric.platform,
        table: metric.table,
        averageDuration: metric.duration,
        maxDuration: metric.duration,
        minDuration: metric.duration,
        successRate: metric.success ? 100 : 0,
        totalOperations: 1,
        threshold,
        status: this.getBenchmarkStatus({
          averageDuration: metric.duration,
          successRate: metric.success ? 100 : 0,
          threshold
        } as PerformanceBenchmark)
      };

      this.benchmarks.set(key, benchmark);
    }
  }

  /**
   * Check if metric exceeds thresholds
   */
  private checkThresholds(metric: PerformanceMetric): void {
    const threshold = this.thresholds.get(`${metric.operation}-${metric.platform}`) || 
                     this.thresholds.get(metric.operation) || 
                     3000;

    if (metric.duration > threshold) {
      this.emit('threshold-exceeded', {
        metric,
        threshold,
        exceedBy: metric.duration - threshold
      });
    }

    if (!metric.success) {
      this.emit('operation-failed', metric);
    }
  }

  /**
   * Determine benchmark status
   */
  private getBenchmarkStatus(benchmark: PerformanceBenchmark): 'good' | 'warning' | 'critical' {
    if (benchmark.successRate < 90) {
      return 'critical';
    } else if (benchmark.averageDuration > benchmark.threshold) {
      return benchmark.averageDuration > benchmark.threshold * 1.5 ? 'critical' : 'warning';
    } else if (benchmark.averageDuration > benchmark.threshold * 0.8) {
      return 'warning';
    } else {
      return 'good';
    }
  }

  /**
   * Check if operation is considered slow
   */
  private isSlowOperation(metric: PerformanceMetric): boolean {
    const threshold = this.thresholds.get(`${metric.operation}-${metric.platform}`) || 
                     this.thresholds.get(metric.operation) || 
                     3000;
    return metric.duration > threshold;
  }

  /**
   * Generate benchmarks from metrics
   */
  private generateBenchmarks(metrics: PerformanceMetric[]): PerformanceBenchmark[] {
    const benchmarkMap = new Map<string, {
      durations: number[];
      successes: number;
      total: number;
      operation: string;
      platform: string;
      table: string;
    }>();

    // Group metrics
    metrics.forEach(metric => {
      const key = `${metric.operation}-${metric.platform}-${metric.table}`;
      const existing = benchmarkMap.get(key);

      if (existing) {
        existing.durations.push(metric.duration);
        existing.successes += metric.success ? 1 : 0;
        existing.total += 1;
      } else {
        benchmarkMap.set(key, {
          durations: [metric.duration],
          successes: metric.success ? 1 : 0,
          total: 1,
          operation: metric.operation,
          platform: metric.platform,
          table: metric.table
        });
      }
    });

    // Convert to benchmarks
    return Array.from(benchmarkMap.values()).map(data => {
      const averageDuration = data.durations.reduce((a, b) => a + b, 0) / data.durations.length;
      const maxDuration = Math.max(...data.durations);
      const minDuration = Math.min(...data.durations);
      const successRate = (data.successes / data.total) * 100;
      const threshold = this.thresholds.get(`${data.operation}-${data.platform}`) || 
                       this.thresholds.get(data.operation) || 
                       3000;

      const benchmark: PerformanceBenchmark = {
        operation: data.operation,
        platform: data.platform,
        table: data.table,
        averageDuration,
        maxDuration,
        minDuration,
        successRate,
        totalOperations: data.total,
        threshold,
        status: 'good'
      };

      benchmark.status = this.getBenchmarkStatus(benchmark);
      return benchmark;
    });
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(metrics: PerformanceMetric[], benchmarks: PerformanceBenchmark[]): string[] {
    const recommendations: string[] = [];

    // Check success rate
    const overallSuccessRate = (metrics.filter(m => m.success).length / metrics.length) * 100;
    if (overallSuccessRate < 95) {
      recommendations.push(`Overall success rate is ${overallSuccessRate.toFixed(1)}% - investigate failing operations`);
    }

    // Check slow operations
    const slowOperations = metrics.filter(m => this.isSlowOperation(m)).length;
    if (slowOperations > metrics.length * 0.1) {
      recommendations.push(`${((slowOperations / metrics.length) * 100).toFixed(1)}% of operations are slow - consider optimization`);
    }

    // Check critical benchmarks
    const criticalBenchmarks = benchmarks.filter(b => b.status === 'critical');
    if (criticalBenchmarks.length > 0) {
      recommendations.push(`${criticalBenchmarks.length} operations have critical performance issues`);
    }

    // Platform-specific recommendations
    const platformPerformance = this.analyzePlatformPerformance(metrics);
    Object.entries(platformPerformance).forEach(([platform, stats]) => {
      if (stats.averageResponseTime > 3000) {
        recommendations.push(`${platform} platform has high response times (${stats.averageResponseTime.toFixed(0)}ms avg)`);
      }
    });

    return recommendations;
  }

  /**
   * Analyze performance trends
   */
  private analyzeTrends(metrics: PerformanceMetric[]): {
    responseTime: 'improving' | 'stable' | 'degrading';
    successRate: 'improving' | 'stable' | 'degrading';
    throughput: 'improving' | 'stable' | 'degrading';
  } {
    // This would implement trend analysis
    // For now, return stable trends
    return {
      responseTime: 'stable',
      successRate: 'stable',
      throughput: 'stable'
    };
  }

  /**
   * Analyze platform-specific performance
   */
  private analyzePlatformPerformance(metrics: PerformanceMetric[]): Record<string, {
    averageResponseTime: number;
    successRate: number;
    operationCount: number;
  }> {
    const platformStats: Record<string, {
      totalDuration: number;
      successes: number;
      total: number;
    }> = {};

    metrics.forEach(metric => {
      if (!platformStats[metric.platform]) {
        platformStats[metric.platform] = {
          totalDuration: 0,
          successes: 0,
          total: 0
        };
      }

      platformStats[metric.platform].totalDuration += metric.duration;
      platformStats[metric.platform].successes += metric.success ? 1 : 0;
      platformStats[metric.platform].total += 1;
    });

    const result: Record<string, {
      averageResponseTime: number;
      successRate: number;
      operationCount: number;
    }> = {};

    Object.entries(platformStats).forEach(([platform, stats]) => {
      result[platform] = {
        averageResponseTime: stats.totalDuration / stats.total,
        successRate: (stats.successes / stats.total) * 100,
        operationCount: stats.total
      };
    });

    return result;
  }

  /**
   * Get empty report structure
   */
  private getEmptyReport(timeRange?: { start: Date; end: Date }): PerformanceReport {
    const now = new Date();
    return {
      generatedAt: now,
      timeRange: timeRange || { start: now, end: now },
      totalOperations: 0,
      averageResponseTime: 0,
      successRate: 100,
      benchmarks: [],
      slowestOperations: [],
      recommendations: ['No performance data available'],
      trends: {
        responseTime: 'stable',
        successRate: 'stable',
        throughput: 'stable'
      }
    };
  }
}