-- Temporary RLS Bypass for Development
-- This migration adds more permissive RLS policies for development

-- Add a temporary policy that allows any authenticated user to manage branches
-- This should be removed in production
CREATE POLICY "Dev: Authenticated users can manage branches" ON branches
  FOR ALL USING (
    auth.role() = 'authenticated' AND 
    current_setting('app.environment', true) = 'development'
  );

-- Add similar policies for branch-related tables
CREATE POLICY "Dev: Authenticated users can manage branch hours" ON branch_operating_hours
  FOR ALL USING (
    auth.role() = 'authenticated' AND 
    current_setting('app.environment', true) = 'development'
  );

CREATE POLICY "Dev: Authenticated users can manage special hours" ON branch_special_hours
  FOR ALL USING (
    auth.role() = 'authenticated' AND 
    current_setting('app.environment', true) = 'development'
  );

-- Set the environment setting for development
-- This can be overridden in production
SELECT set_config('app.environment', 'development', false);

-- Add a function to easily toggle development mode
CREATE OR REPLACE FUNCTION toggle_dev_mode(enable boolean)
RETURNS void AS $$
BEGIN
  IF enable THEN
    PERFORM set_config('app.environment', 'development', false);
  ELSE
    PERFORM set_config('app.environment', 'production', false);
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION toggle_dev_mode(boolean) TO authenticated;

-- Add a comment explaining the purpose
COMMENT ON POLICY "Dev: Authenticated users can manage branches" ON branches IS 
'Temporary development policy - allows any authenticated user to manage branches when app.environment is set to development. Remove in production.';

COMMENT ON FUNCTION toggle_dev_mode(boolean) IS 
'Development helper function to toggle between development and production modes. Use SELECT toggle_dev_mode(true) to enable dev mode.'; 