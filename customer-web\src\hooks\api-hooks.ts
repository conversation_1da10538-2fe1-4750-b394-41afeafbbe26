/**
 * API-related React hooks for the customer-web project
 */

import { useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/supabase/database.types';
import { MenuItem, Category, DeliveryZone, Order, ApiResponse } from '@/types/types';
import { getLocalStorage, setLocalStorage, safeJsonParse } from '@/lib/utils';

/**
 * Hook to fetch menu items
 */
export function useMenuItems() {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();

  const fetchMenuItems = useCallback(
    async (categoryId?: string) => {
      try {
        setLoading(true);
        setError(null);

        let query = supabase
          .from('menu_items')
          .select(`
            *,
            categories!inner(
              id,
              name_en,
              name_el
            )
          `)
          .eq('is_available', true);

        if (categoryId) {
          query = query.eq('category_id', categoryId);
        }

        const { data, error: fetchError } = await query.order('display_order', { ascending: true });

        if (fetchError) {
          throw new Error(fetchError.message);
        }

        // Transform the data to match MenuItem interface
        const transformedData = data?.map(item => ({
          id: item.id,
          name: item.name_en || item.name || '',
          description: item.description_en || item.description || '',
          price: item.price || 0,
          pickup_price: item.pickup_price || item.price || 0,
          delivery_price: item.delivery_price || item.price || 0,
          base_price: item.base_price || item.price || 0,
          image_url: item.image_url,
          category_id: item.category_id,
          category: item.categories ? {
            id: item.categories.id,
            name: item.categories.name_en || '',
            name_en: item.categories.name_en || '',
            name_el: item.categories.name_el || ''
          } : undefined,
          allergens: item.allergens ? (Array.isArray(item.allergens) ? item.allergens : [item.allergens]) : [],
          available: item.is_available || false,
          slug: item.name_en?.toLowerCase().replace(/\s+/g, '-') || item.id,
          is_customizable: item.is_customizable || false,
          max_ingredients: item.max_ingredients || 0,
          created_at: item.created_at,
          updated_at: item.updated_at,
          preparation_time: item.preparation_time
        })) || [];

        setMenuItems(transformedData as MenuItem[]);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch menu items');
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  useEffect(() => {
    fetchMenuItems();
  }, [fetchMenuItems]);

  const getMenuItemById = useCallback(
    (id: string) => {
      return menuItems.find(item => item.id === id) || null;
    },
    [menuItems]
  );

  const getMenuItemsByCategory = useCallback(
    (categoryId: string) => {
      return menuItems.filter(item => item.category_id === categoryId);
    },
    [menuItems]
  );

  const searchMenuItems = useCallback(
    (query: string) => {
      const searchTerms = query.toLowerCase().split(' ');
      return menuItems.filter(item => {
        const itemText = `${item.name} ${item.description}`.toLowerCase();
        return searchTerms.every(term => itemText.includes(term));
      });
    },
    [menuItems]
  );

  return {
    menuItems,
    loading,
    error,
    fetchMenuItems,
    getMenuItemById,
    getMenuItemsByCategory,
    searchMenuItems,
  };
}

/**
 * Hook to fetch categories
 */
export function useCategories() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();

  const fetchCategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('menu_categories')
        .select('*')
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      // Transform the data to match Category interface
      const transformedData = data?.map(category => ({
        id: category.id,
        name: category.name_en || category.name || '',
        name_en: category.name_en || '',
        name_el: category.name_el || '',
        description: category.description_en || category.description || '',
        image_url: category.image_url,
        slug: category.name_en?.toLowerCase().replace(/\s+/g, '-') || category.id,
        created_at: category.created_at,
        updated_at: category.updated_at,
        order: category.display_order,
        is_active: category.is_active
      })) || [];

      setCategories(transformedData as Category[]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch categories');
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const getCategoryById = useCallback(
    (id: string) => {
      return categories.find(category => category.id === id) || null;
    },
    [categories]
  );

  return {
    categories,
    loading,
    error,
    fetchCategories,
    getCategoryById,
  };
}

/**
 * Hook to fetch subcategories
 */
export function useSubcategories() {
  const [subcategories, setSubcategories] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();

  const fetchSubcategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('subcategories')
        .select(`
          *,
          menu_categories!inner(
            id,
            name_en,
            name_el
          )
        `)
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      // Transform the data to match MenuItem interface
      const transformedData = data?.map(item => ({
        id: item.id,
        name: item.name || '',
        description: item.description || '',
        price: item.price || 0,
        pickup_price: item.pickup_price || item.price || 0,
        delivery_price: item.delivery_price || item.price || 0,
        base_price: item.base_price || item.price || 0,
        image_url: item.image_url,
        category_id: item.category_id,
        category: item.menu_categories ? {
          id: item.menu_categories.id,
          name: item.menu_categories.name_en || '',
          name_en: item.menu_categories.name_en || '',
          name_el: item.menu_categories.name_el || ''
        } : undefined,
        allergens: item.allergens ? (Array.isArray(item.allergens) ? item.allergens : [item.allergens]) : [],
        available: item.is_available || false,
        slug: item.name?.toLowerCase().replace(/\s+/g, '-') || item.id,
        is_customizable: item.is_customizable || false,
        max_ingredients: item.max_ingredients || 0,
        created_at: item.created_at,
        updated_at: item.updated_at,
        preparation_time: item.preparation_time
      })) || [];

      setSubcategories(transformedData as MenuItem[]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch subcategories');
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  const fetchMostFrequentedSubcategories = useCallback(async (limit: number = 6) => {
    try {
      setLoading(true);
      setError(null);

      // Get most frequented subcategories based on order count
      const { data, error: fetchError } = await supabase
        .rpc('get_most_frequented_subcategories', { limit_count: limit });

      if (fetchError) {
        // Fallback to regular subcategories if RPC doesn't exist
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('subcategories')
          .select(`
            *,
            menu_categories!inner(
              id,
              name_en,
              name_el
            )
          `)
          .eq('is_active', true)
          .eq('is_featured', true)
          .order('display_order', { ascending: true })
          .limit(limit);

        if (fallbackError) {
          throw new Error(fallbackError.message);
        }

        // Transform the fallback data
        const transformedFallbackData = fallbackData?.map(item => ({
          id: item.id,
          name: item.name || '',
          description: item.description || '',
          price: item.price || 0,
          pickup_price: item.pickup_price || item.price || 0,
          delivery_price: item.delivery_price || item.price || 0,
          base_price: item.base_price || item.price || 0,
          image_url: item.image_url,
          category_id: item.category_id,
          category: item.menu_categories ? {
            id: item.menu_categories.id,
            name: item.menu_categories.name_en || '',
            name_en: item.menu_categories.name_en || '',
            name_el: item.menu_categories.name_el || ''
          } : undefined,
          allergens: item.allergens ? (Array.isArray(item.allergens) ? item.allergens : [item.allergens]) : [],
          available: item.is_available || false,
          slug: item.name?.toLowerCase().replace(/\s+/g, '-') || item.id,
          is_customizable: item.is_customizable || false,
          max_ingredients: item.max_ingredients || 0,
          created_at: item.created_at,
          updated_at: item.updated_at,
          preparation_time: item.preparation_time
        })) || [];

        setSubcategories(transformedFallbackData as MenuItem[]);
        return;
      }

      // Transform the RPC data if it exists
      const transformedData = data?.map((item: any) => ({
        id: item.id,
        name: item.name || '',
        description: item.description || '',
        price: item.price || 0,
        pickup_price: item.pickup_price || item.price || 0,
        delivery_price: item.delivery_price || item.price || 0,
        base_price: item.base_price || item.price || 0,
        image_url: item.image_url,
        category_id: item.category_id,
        category: {
          id: item.category_id,
          name: item.category_name || '',
          name_en: item.category_name || '',
          name_el: item.category_name || ''
        },
        allergens: item.allergens ? (Array.isArray(item.allergens) ? item.allergens : [item.allergens]) : [],
        available: item.is_available || false,
        slug: item.name?.toLowerCase().replace(/\s+/g, '-') || item.id,
        is_customizable: item.is_customizable || false,
        max_ingredients: item.max_ingredients || 0,
        created_at: item.created_at,
        updated_at: item.updated_at,
        preparation_time: item.preparation_time,
        order_count: item.order_count || 0
      })) || [];

      setSubcategories(transformedData as MenuItem[]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch most frequented subcategories');
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  useEffect(() => {
    fetchSubcategories();
  }, [fetchSubcategories]);

  const getSubcategoryById = useCallback(
    (id: string) => {
      return subcategories.find(subcategory => subcategory.id === id) || null;
    },
    [subcategories]
  );

  const getSubcategoriesByCategory = useCallback(
    (categoryId: string) => {
      return subcategories.filter(subcategory => subcategory.category_id === categoryId);
    },
    [subcategories]
  );

  return {
    subcategories,
    loading,
    error,
    fetchSubcategories,
    fetchMostFrequentedSubcategories,
    getSubcategoryById,
    getSubcategoriesByCategory,
  };
}

/**
 * Hook to fetch delivery zones
 */
export function useDeliveryZones() {
  const [deliveryZones, setDeliveryZones] = useState<DeliveryZone[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();

  const fetchDeliveryZones = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase.from('delivery_zones').select('*');

      if (fetchError) {
        throw new Error(fetchError.message);
      }

      setDeliveryZones(data as unknown as DeliveryZone[]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch delivery zones');
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  useEffect(() => {
    fetchDeliveryZones();
  }, [fetchDeliveryZones]);

  const getDeliveryFee = useCallback(
    (postalCode: string) => {
      const zone = deliveryZones.find(zone => {
        return zone.postalCodes.some(code => code === postalCode);
      });

      return zone ? zone.deliveryFee : null;
    },
    [deliveryZones]
  );

  const isDeliveryAvailable = useCallback(
    (postalCode: string) => {
      return deliveryZones.some(zone => {
        return zone.postalCodes.some(code => code === postalCode);
      });
    },
    [deliveryZones]
  );

  return {
    deliveryZones,
    loading,
    error,
    fetchDeliveryZones,
    getDeliveryFee,
    isDeliveryAvailable,
  };
}

/**
 * Hook to create orders
 */
export function useCreateOrder() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [orderResult, setOrderResult] = useState<ApiResponse<Order> | null>(null);
  const supabase = createClientComponentClient<Database>();

  const createOrder = useCallback(
    async (orderData: Omit<Order, 'id' | 'created_at' | 'updated_at'>) => {
      try {
        setLoading(true);
        setError(null);
        setOrderResult(null);

        // Get current user
        const {
          data: { session },
        } = await supabase.auth.getSession();

        // Add user_id if authenticated
        const orderWithUserId = session?.user
          ? {
              ...orderData,
              user_id: session.user.id,
            }
          : orderData;

        // Insert order
        const { data: orderData_, error: orderError } = await supabase
          .from('orders')
          .insert([orderWithUserId])
          .select()
          .single();

        if (orderError) {
          throw new Error(orderError.message);
        }

        // Insert order items
        if (orderData_.id && orderData.items && orderData.items.length > 0) {
          const orderItems = orderData.items.map(item => ({
            order_id: orderData_.id,
            subcategory_id: item.subcategory_id,
            quantity: item.quantity,
            price: item.price,
            specialInstructions: item.specialInstructions,
            // Remove the variant_name property since it's not available in the input
          }));

          const { error: itemsError } = await supabase.from('order_items').insert(orderItems);

          if (itemsError) {
            throw new Error(`Order created but failed to add items: ${itemsError.message}`);
          }
        }

        setOrderResult({
          status: 'success',
          data: orderData_ as unknown as Order,
        });

        return {
          success: true,
          data: orderData_ as unknown as Order,
        };
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to create order';
        setError(errorMessage);
        setOrderResult({
          status: 'error',
          error: {
            message: errorMessage,
          },
        });
        return {
          success: false,
          error: {
            message: errorMessage,
          },
        };
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  return {
    createOrder,
    loading,
    error,
    orderResult,
  };
}

/**
 * Hook to fetch a single order
 */
export function useOrder(orderId?: string) {
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();

  const fetchOrder = useCallback(
    async (id: string) => {
      if (!id) return;

      try {
        setLoading(true);
        setError(null);

        const { data, error: fetchError } = await supabase
          .from('orders')
          .select(
            `
          *,
          order_items(*,
            subcategories(*)
          )
        `
          )
          .eq('id', id)
          .single();

        if (fetchError) {
          throw new Error(fetchError.message);
        }

        setOrder(data as unknown as Order);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch order');
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  useEffect(() => {
    if (orderId) {
      fetchOrder(orderId);
    }
  }, [orderId, fetchOrder]);

  return {
    order,
    loading,
    error,
    fetchOrder,
  };
}

/**
 * Hook to subscribe to order status updates
 */
export function useOrderStatusSubscription(orderId?: string) {
  const [status, setStatus] = useState<Order['status'] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    if (!orderId) return;

    // First fetch the current status
    const fetchInitialStatus = async () => {
      try {
        setLoading(true);
        const { data, error: fetchError } = await supabase
          .from('orders')
          .select('status')
          .eq('id', orderId)
          .single();

        if (fetchError) {
          throw new Error(fetchError.message);
        }

        setStatus(data.status as Order['status']);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch order status');
      } finally {
        setLoading(false);
      }
    };

    fetchInitialStatus();

    // Then subscribe to changes
    const subscription = supabase
      .channel(`order-${orderId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'orders',
          filter: `id=eq.${orderId}`,
        },
        payload => {
          if (payload.new && 'status' in payload.new) {
            setStatus(payload.new.status as Order['status']);
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [orderId, supabase]);

  return {
    status,
    loading,
    error,
  };
}

/**
 * Hook to fetch store information
 */
export function useStoreInfo() {
  const [storeInfo, setStoreInfo] = useState({
    name: process.env.NEXT_PUBLIC_STORE_NAME || 'Creperie',
    phone: process.env.NEXT_PUBLIC_STORE_PHONE || '+30 ************',
    email: process.env.NEXT_PUBLIC_STORE_EMAIL || '<EMAIL>',
    address: process.env.NEXT_PUBLIC_STORE_ADDRESS || '123 Main St, Athens, Greece',
    businessHours: JSON.parse(process.env.NEXT_PUBLIC_BUSINESS_HOURS || '{}'),
    minimumOrder: Number(process.env.NEXT_PUBLIC_MINIMUM_ORDER || '10'),
    deliveryFee: Number(process.env.NEXT_PUBLIC_DELIVERY_FEE || '2.5'),
    coordinates: {
      lat: 37.9838, // Default Athens coordinates
      lng: 23.7275,
    },
  });

  return { storeInfo };
}

/**
 * Hook to check if store is open
 */
export function useStoreStatus() {
  const { storeInfo } = useStoreInfo();
  const [isOpen, setIsOpen] = useState(false);
  const [nextOpenTime, setNextOpenTime] = useState<string | null>(null);
  const [closingIn, setClosingIn] = useState<number | null>(null);

  useEffect(() => {
    const checkIfStoreIsOpen = () => {
      const now = new Date();
      const day = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
      const hours = now.getHours();
      const minutes = now.getMinutes();
      const currentMinutes = hours * 60 + minutes;

      // Get business hours for today
      const todayHours = storeInfo.businessHours[day];

      if (!todayHours || !todayHours.open) {
        setIsOpen(false);

        // Find next open day
        const daysOfWeek = [
          'sunday',
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
        ];
        const todayIndex = daysOfWeek.indexOf(day);

        let nextOpenDay = null;
        let daysUntilOpen = 0;

        for (let i = 1; i <= 7; i++) {
          const nextIndex = (todayIndex + i) % 7;
          const nextDay = daysOfWeek[nextIndex];
          if (storeInfo.businessHours[nextDay]?.open) {
            nextOpenDay = nextDay;
            daysUntilOpen = i;
            break;
          }
        }

        if (nextOpenDay) {
          const nextDayHours = storeInfo.businessHours[nextOpenDay];
          const openTime = nextDayHours.openTime;
          const [openHour, openMinute] = openTime.split(':').map(Number);

          const nextOpen = new Date();
          nextOpen.setDate(now.getDate() + daysUntilOpen);
          nextOpen.setHours(openHour, openMinute, 0, 0);

          setNextOpenTime(nextOpen.toLocaleString());
        } else {
          setNextOpenTime('Unknown');
        }

        setClosingIn(null);
        return;
      }

      // Parse opening and closing times
      const { openTime, closeTime } = todayHours;
      const [openHour, openMinute] = openTime.split(':').map(Number);
      const [closeHour, closeMinute] = closeTime.split(':').map(Number);

      const openMinutes = openHour * 60 + openMinute;
      const closeMinutes = closeHour * 60 + closeMinute;

      // Check if current time is within business hours
      const isCurrentlyOpen = currentMinutes >= openMinutes && currentMinutes < closeMinutes;
      setIsOpen(isCurrentlyOpen);

      if (isCurrentlyOpen) {
        // Calculate minutes until closing
        const minutesUntilClose = closeMinutes - currentMinutes;
        setClosingIn(minutesUntilClose);
        setNextOpenTime(null);
      } else if (currentMinutes < openMinutes) {
        // Store will open later today
        const nextOpen = new Date();
        nextOpen.setHours(openHour, openMinute, 0, 0);
        setNextOpenTime(nextOpen.toLocaleString());
        setClosingIn(null);
      } else {
        // Store is closed for today, find next open time
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const tomorrowDay = tomorrow.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
        const tomorrowHours = storeInfo.businessHours[tomorrowDay];

        if (tomorrowHours && tomorrowHours.open) {
          const [nextOpenHour, nextOpenMinute] = tomorrowHours.openTime.split(':').map(Number);
          const nextOpen = new Date(tomorrow);
          nextOpen.setHours(nextOpenHour, nextOpenMinute, 0, 0);
          setNextOpenTime(nextOpen.toLocaleString());
        } else {
          // Find next open day
          const daysOfWeek = [
            'sunday',
            'monday',
            'tuesday',
            'wednesday',
            'thursday',
            'friday',
            'saturday',
          ];
          const todayIndex = daysOfWeek.indexOf(day);

          let nextOpenDay = null;
          let daysUntilOpen = 0;

          for (let i = 1; i <= 7; i++) {
            const nextIndex = (todayIndex + i) % 7;
            const nextDay = daysOfWeek[nextIndex];
            if (storeInfo.businessHours[nextDay]?.open) {
              nextOpenDay = nextDay;
              daysUntilOpen = i;
              break;
            }
          }

          if (nextOpenDay) {
            const nextDayHours = storeInfo.businessHours[nextOpenDay];
            const openTime = nextDayHours.openTime;
            const [openHour, openMinute] = openTime.split(':').map(Number);

            const nextOpen = new Date();
            nextOpen.setDate(now.getDate() + daysUntilOpen);
            nextOpen.setHours(openHour, openMinute, 0, 0);

            setNextOpenTime(nextOpen.toLocaleString());
          } else {
            setNextOpenTime('Unknown');
          }
        }

        setClosingIn(null);
      }
    };

    // Check immediately and then every minute
    checkIfStoreIsOpen();
    const interval = setInterval(checkIfStoreIsOpen, 60000);

    return () => clearInterval(interval);
  }, [storeInfo.businessHours]);

  return {
    isOpen,
    nextOpenTime,
    closingIn,
  };
}
