'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  GoogleMapsService, 
  MapConfigService,
  GeocodingService,
  GeometryService 
} from '@/lib/google-maps-service'

interface UseGoogleMapsReturn {
  isLoaded: boolean
  isLoading: boolean
  loadError: string | null
  google: any | null
  retry: () => void
}

/**
 * Enhanced hook for loading Google Maps API
 */
export const useGoogleMaps = (): UseGoogleMapsReturn => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [loadError, setLoadError] = useState<string | null>(null)
  const [google, setGoogle] = useState<any | null>(null)
  const retryCount = useRef(0)
  const maxRetries = 3

  const loadMaps = useCallback(async () => {
    if (isLoading || isLoaded) return

    try {
      setIsLoading(true)
      setLoadError(null)
      
      const googleInstance = await GoogleMapsService.loadGoogleMaps()
      
      setGoogle(googleInstance)
      setIsLoaded(true)
      retryCount.current = 0
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load Google Maps'
      setLoadError(errorMessage)
      console.error('Google Maps loading error:', error)
    } finally {
      setIsLoading(false)
    }
  }, [isLoading, isLoaded])

  const retry = useCallback(() => {
    if (retryCount.current < maxRetries) {
      retryCount.current++
      setIsLoaded(false)
      setLoadError(null)
      loadMaps()
    }
  }, [loadMaps])

  useEffect(() => {
    loadMaps()
  }, [loadMaps])

  return {
    isLoaded,
    isLoading,
    loadError,
    google,
    retry,
  }
}

interface UseMapOptions {
  center?: { lat: number; lng: number }
  zoom?: number
  options?: google.maps.MapOptions
}

interface UseMapReturn {
  mapRef: React.RefObject<HTMLDivElement | null>
  map: google.maps.Map | null
  isMapReady: boolean
  mapError: string | null
  initializeMap: (customOptions?: google.maps.MapOptions) => void
  panTo: (location: { lat: number; lng: number }) => void
  setZoom: (zoom: number) => void
  fitBounds: (bounds: google.maps.LatLngBounds) => void
  getCenter: () => { lat: number; lng: number } | null
  getZoom: () => number | null
}

/**
 * Enhanced hook for managing a Google Map instance
 */
export const useMap = (options: UseMapOptions = {}): UseMapReturn => {
  const { isLoaded, google } = useGoogleMaps()
  const [map, setMap] = useState<google.maps.Map | null>(null)
  const [isMapReady, setIsMapReady] = useState(false)
  const [mapError, setMapError] = useState<string | null>(null)
  const mapRef = useRef<HTMLDivElement>(null)

  const initializeMap = useCallback((customOptions?: google.maps.MapOptions) => {
    if (!isLoaded || !google || !mapRef.current || map) return

    try {
      setMapError(null)
      
      const mapOptions = {
        ...MapConfigService.getDeliveryZoneMapOptions(options.center),
        zoom: options.zoom || 12,
        ...options.options,
        ...customOptions,
      }

      const mapInstance = new google.maps.Map(mapRef.current, mapOptions)
      
      // Wait for map to be fully loaded
      google.maps.event.addListenerOnce(mapInstance, 'idle', () => {
        setIsMapReady(true)
      })

      setMap(mapInstance)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize map'
      setMapError(errorMessage)
      console.error('Map initialization error:', error)
    }
  }, [isLoaded, google, map, options])

  const panTo = useCallback((location: { lat: number; lng: number }) => {
    if (map) {
      map.panTo(location)
    }
  }, [map])

  const setZoom = useCallback((zoom: number) => {
    if (map) {
      map.setZoom(zoom)
    }
  }, [map])

  const fitBounds = useCallback((bounds: google.maps.LatLngBounds) => {
    if (map) {
      map.fitBounds(bounds)
    }
  }, [map])

  const getCenter = useCallback((): { lat: number; lng: number } | null => {
    if (!map) return null
    const center = map.getCenter()
    return center ? { lat: center.lat(), lng: center.lng() } : null
  }, [map])

  const getZoom = useCallback((): number | null => {
    return map ? map.getZoom() || null : null
  }, [map])

  useEffect(() => {
    if (isLoaded && !map) {
      initializeMap()
    }
  }, [isLoaded, map, initializeMap])

  return {
    mapRef,
    map,
    isMapReady,
    mapError,
    initializeMap,
    panTo,
    setZoom,
    fitBounds,
    getCenter,
    getZoom,
  }
}

interface UseDrawingManagerOptions {
  onPolygonComplete?: (polygon: google.maps.Polygon) => void
  onPolygonEdit?: (polygon: google.maps.Polygon) => void
  polygonOptions?: google.maps.PolygonOptions
}

interface UseDrawingManagerReturn {
  drawingManager: google.maps.drawing.DrawingManager | null
  isDrawingMode: boolean
  startDrawing: () => void
  stopDrawing: () => void
  clearDrawing: () => void
  setDrawingMode: (mode: google.maps.drawing.OverlayType | null) => void
}

/**
 * Enhanced hook for managing Google Maps Drawing Manager
 */
export const useDrawingManager = (
  map: google.maps.Map | null,
  options: UseDrawingManagerOptions = {}
): UseDrawingManagerReturn => {
  const { google } = useGoogleMaps()
  const [drawingManager, setDrawingManager] = useState<google.maps.drawing.DrawingManager | null>(null)
  const [isDrawingMode, setIsDrawingMode] = useState(false)

  useEffect(() => {
    if (!google || !map || drawingManager) return

    let currentManager: google.maps.drawing.DrawingManager | null = null

    try {
      const manager = new google.maps.drawing.DrawingManager({
        ...MapConfigService.getDrawingManagerOptions(),
        polygonOptions: {
          ...MapConfigService.getDrawingManagerOptions().polygonOptions,
          ...options.polygonOptions,
        },
      })

      manager.setMap(map)
      currentManager = manager

      // Handle polygon completion
      if (options.onPolygonComplete) {
        google.maps.event.addListener(manager, 'polygoncomplete', options.onPolygonComplete)
      }

      setDrawingManager(manager)
    } catch (error) {
      console.error('Drawing manager initialization error:', error)
    }

    return () => {
      if (currentManager && google) {
        google.maps.event.clearInstanceListeners(currentManager)
        currentManager.setMap(null)
      }
    }
  }, [google, map, drawingManager, options])

  const startDrawing = useCallback(() => {
    if (drawingManager) {
      drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON)
      setIsDrawingMode(true)
    }
  }, [drawingManager, google])

  const stopDrawing = useCallback(() => {
    if (drawingManager) {
      drawingManager.setDrawingMode(null)
      setIsDrawingMode(false)
    }
  }, [drawingManager])

  const clearDrawing = useCallback(() => {
    if (drawingManager && google) {
      drawingManager.setMap(null)
      setDrawingManager(null)
      setIsDrawingMode(false)
    }
  }, [drawingManager, google])

  const setDrawingMode = useCallback((mode: google.maps.drawing.OverlayType | null) => {
    if (drawingManager) {
      drawingManager.setDrawingMode(mode)
      setIsDrawingMode(mode !== null)
    }
  }, [drawingManager])

  return {
    drawingManager,
    isDrawingMode,
    startDrawing,
    stopDrawing,
    clearDrawing,
    setDrawingMode,
  }
}

interface UseGeocodingReturn {
  geocodeAddress: (address: string) => Promise<google.maps.GeocoderResult[]>
  reverseGeocode: (lat: number, lng: number) => Promise<google.maps.GeocoderResult[]>
  isGeocoding: boolean
  geocodingError: string | null
}

/**
 * Hook for geocoding operations
 */
export const useGeocoding = (): UseGeocodingReturn => {
  const [isGeocoding, setIsGeocoding] = useState(false)
  const [geocodingError, setGeocodingError] = useState<string | null>(null)

  const geocodeAddress = useCallback(async (address: string): Promise<google.maps.GeocoderResult[]> => {
    setIsGeocoding(true)
    setGeocodingError(null)

    try {
      const { results } = await GeocodingService.geocodeAddress(address)
      return results
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Geocoding failed'
      setGeocodingError(errorMessage)
      throw error
    } finally {
      setIsGeocoding(false)
    }
  }, [])

  const reverseGeocode = useCallback(async (lat: number, lng: number): Promise<google.maps.GeocoderResult[]> => {
    setIsGeocoding(true)
    setGeocodingError(null)

    try {
      const { results } = await GeocodingService.reverseGeocode(lat, lng)
      return results
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Reverse geocoding failed'
      setGeocodingError(errorMessage)
      throw error
    } finally {
      setIsGeocoding(false)
    }
  }, [])

  return {
    geocodeAddress,
    reverseGeocode,
    isGeocoding,
    geocodingError,
  }
}

interface UseGeometryReturn {
  isPointInPolygon: (point: { lat: number; lng: number }, polygon: { lat: number; lng: number }[]) => Promise<boolean>
  calculateDistance: (point1: { lat: number; lng: number }, point2: { lat: number; lng: number }) => Promise<number>
  calculatePolygonArea: (polygon: { lat: number; lng: number }[]) => Promise<number>
  getPolygonCenter: (polygon: { lat: number; lng: number }[]) => { lat: number; lng: number }
}

/**
 * Hook for geometry operations
 */
export const useGeometry = (): UseGeometryReturn => {
  return {
    isPointInPolygon: GeometryService.isPointInPolygon,
    calculateDistance: GeometryService.calculateDistance,
    calculatePolygonArea: GeometryService.calculatePolygonArea,
    getPolygonCenter: GeometryService.getPolygonCenter,
  }
} 