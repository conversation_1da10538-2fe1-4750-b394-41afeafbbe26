-- Enhanced Menu System with Customizable Ingredients
-- Creates the complete menu workflow with categories, subcategories, and ingredients

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- ENHANCED MENU CATEGORIES TABLE
-- =============================================
-- Drop existing menu_categories if it exists and recreate with enhanced structure
DROP TABLE IF EXISTS menu_categories CASCADE;

CREATE TABLE menu_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  parent_id UUID REFERENCES menu_categories(id) ON DELETE CASCADE,
  category_type VARCHAR(50) NOT NULL DEFAULT 'standard' CHECK (category_type IN ('standard', 'customizable')),
  display_order INTEGER NOT NULL DEFAULT 0,
  image_url TEXT,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  is_featured BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for menu_categories
CREATE INDEX idx_menu_categories_parent ON menu_categories (parent_id);
CREATE INDEX idx_menu_categories_type ON menu_categories (category_type);
CREATE INDEX idx_menu_categories_active ON menu_categories (is_active);
CREATE INDEX idx_menu_categories_order ON menu_categories (display_order);

-- =============================================
-- INGREDIENTS SYSTEM
-- =============================================
-- Ingredient categories (Sweet, Salted)
CREATE TABLE ingredient_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  color_code VARCHAR(7) DEFAULT '#6B7280',
  display_order INTEGER NOT NULL DEFAULT 0,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual ingredients
CREATE TABLE ingredients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  category_id UUID NOT NULL REFERENCES ingredient_categories(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  image_url TEXT,
  stock_quantity INTEGER DEFAULT 0,
  min_stock_level INTEGER DEFAULT 5,
  is_available BOOLEAN NOT NULL DEFAULT TRUE,
  allergens TEXT[], -- array of allergen strings
  nutritional_info JSONB DEFAULT '{}',
  display_order INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for ingredients
CREATE INDEX idx_ingredients_category ON ingredients (category_id);
CREATE INDEX idx_ingredients_available ON ingredients (is_available);
CREATE INDEX idx_ingredients_stock ON ingredients (stock_quantity);
CREATE INDEX idx_ingredients_name ON ingredients (name);

-- =============================================
-- ENHANCED SUBCATEGORIES TABLE (MENU ITEMS)
-- =============================================
-- Drop existing subcategories if it exists and recreate with enhanced structure
DROP TABLE IF EXISTS subcategories CASCADE;

CREATE TABLE subcategories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  category_id UUID NOT NULL REFERENCES menu_categories(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  image_url TEXT,
  preparation_time INTEGER DEFAULT 10, -- minutes
  calories INTEGER DEFAULT 0,
  allergens TEXT[], -- array of allergen strings
  nutritional_info JSONB DEFAULT '{}',
  is_available BOOLEAN NOT NULL DEFAULT TRUE,
  is_featured BOOLEAN NOT NULL DEFAULT FALSE,
  is_customizable BOOLEAN NOT NULL DEFAULT FALSE,
  max_ingredients INTEGER DEFAULT 10, -- max ingredients for customizable items
  display_order INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for subcategories
CREATE INDEX idx_subcategories_category ON subcategories (category_id);
CREATE INDEX idx_subcategories_available ON subcategories (is_available);
CREATE INDEX idx_subcategories_customizable ON subcategories (is_customizable);
CREATE INDEX idx_subcategories_featured ON subcategories (is_featured);
CREATE INDEX idx_subcategories_name ON subcategories (name);

-- =============================================
-- MENU ITEM INGREDIENTS JUNCTION TABLE
-- =============================================
-- Links menu items with ingredients (for both fixed and customizable items)
CREATE TABLE menu_item_ingredients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  menu_item_id UUID NOT NULL REFERENCES subcategories(id) ON DELETE CASCADE,
  ingredient_id UUID NOT NULL REFERENCES ingredients(id) ON DELETE CASCADE,
  quantity DECIMAL(8,2) NOT NULL DEFAULT 1.00,
  is_default BOOLEAN NOT NULL DEFAULT FALSE, -- true for base ingredients
  is_optional BOOLEAN NOT NULL DEFAULT TRUE, -- false for required ingredients
  additional_price DECIMAL(10,2) DEFAULT 0.00, -- extra price for this ingredient
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(menu_item_id, ingredient_id)
);

-- Create indexes for menu_item_ingredients
CREATE INDEX idx_menu_item_ingredients_item ON menu_item_ingredients (menu_item_id);
CREATE INDEX idx_menu_item_ingredients_ingredient ON menu_item_ingredients (ingredient_id);
CREATE INDEX idx_menu_item_ingredients_default ON menu_item_ingredients (is_default);

-- =============================================
-- MENU CUSTOMIZATION PRESETS
-- =============================================
-- Predefined ingredient combinations for quick selection
CREATE TABLE customization_presets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  menu_item_id UUID NOT NULL REFERENCES subcategories(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  preset_ingredients JSONB NOT NULL, -- array of {ingredient_id, quantity}
  total_additional_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  image_url TEXT,
  is_popular BOOLEAN NOT NULL DEFAULT FALSE,
  display_order INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for customization_presets
CREATE INDEX idx_customization_presets_item ON customization_presets (menu_item_id);
CREATE INDEX idx_customization_presets_popular ON customization_presets (is_popular);

-- =============================================
-- REAL-TIME MENU SYNCHRONIZATION TABLE
-- =============================================
-- Enhanced synchronization for menu items with ingredients
CREATE TABLE menu_sync_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  sync_type VARCHAR(50) NOT NULL CHECK (sync_type IN ('menu_item', 'ingredient', 'category', 'availability')),
  resource_id UUID NOT NULL,
  operation VARCHAR(20) NOT NULL CHECK (operation IN ('insert', 'update', 'delete')),
  data_changes JSONB NOT NULL,
  target_platforms TEXT[] DEFAULT ARRAY['admin', 'pos', 'web', 'mobile'],
  sync_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (sync_status IN ('pending', 'processing', 'completed', 'failed')),
  sync_attempts INTEGER NOT NULL DEFAULT 0,
  last_sync_attempt TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  priority INTEGER NOT NULL DEFAULT 5, -- 1=highest, 10=lowest
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for menu_sync_queue
CREATE INDEX idx_menu_sync_queue_status ON menu_sync_queue (sync_status);
CREATE INDEX idx_menu_sync_queue_type ON menu_sync_queue (sync_type);
CREATE INDEX idx_menu_sync_queue_priority ON menu_sync_queue (priority, created_at);
CREATE INDEX idx_menu_sync_queue_resource ON menu_sync_queue (resource_id);

-- =============================================
-- INSERT DEFAULT DATA
-- =============================================

-- Insert ingredient categories
INSERT INTO ingredient_categories (name, description, color_code, display_order) VALUES
('Sweet', 'Sweet ingredients for dessert items', '#F59E0B', 1),
('Salted', 'Savory and salty ingredients', '#6B7280', 2)
ON CONFLICT (name) DO NOTHING;

-- Insert main menu categories
INSERT INTO menu_categories (name, description, category_type, display_order, is_featured) VALUES
('Crepes', 'Traditional and modern crepes', 'standard', 1, true),
('Waffles', 'Belgian and classic waffles', 'standard', 2, true),
('Toasts', 'Artisanal toasts and breads', 'standard', 3, false),
('Beverages', 'Hot and cold beverages', 'standard', 4, false)
ON CONFLICT DO NOTHING;

-- Get category IDs for subcategories
DO $$
DECLARE
    crepes_id UUID;
    waffles_id UUID;
    toasts_id UUID;
    sweet_cat_id UUID;
    salted_cat_id UUID;
BEGIN
    -- Get category IDs
    SELECT id INTO crepes_id FROM menu_categories WHERE name = 'Crepes';
    SELECT id INTO waffles_id FROM menu_categories WHERE name = 'Waffles';
    SELECT id INTO toasts_id FROM menu_categories WHERE name = 'Toasts';
    SELECT id INTO sweet_cat_id FROM ingredient_categories WHERE name = 'Sweet';
    SELECT id INTO salted_cat_id FROM ingredient_categories WHERE name = 'Salted';

    -- Insert customizable subcategories
    INSERT INTO menu_categories (name, description, parent_id, category_type, display_order) VALUES
    ('My Crepe', 'Build your own custom crepe', crepes_id, 'customizable', 1),
    ('My Waffle', 'Build your own custom waffle', waffles_id, 'customizable', 1),
    ('My Toast', 'Build your own custom toast', toasts_id, 'customizable', 1)
    ON CONFLICT DO NOTHING;

    -- Insert sweet ingredients
    INSERT INTO ingredients (category_id, name, description, price, image_url, display_order) VALUES
    (sweet_cat_id, 'Nutella', 'Creamy hazelnut chocolate spread', 2.50, '/images/ingredients/nutella.jpg', 1),
    (sweet_cat_id, 'Fresh Strawberries', 'Sweet, fresh strawberry slices', 3.00, '/images/ingredients/strawberries.jpg', 2),
    (sweet_cat_id, 'Banana Slices', 'Fresh banana slices', 2.00, '/images/ingredients/banana.jpg', 3),
    (sweet_cat_id, 'Whipped Cream', 'Light and fluffy whipped cream', 1.50, '/images/ingredients/whipped-cream.jpg', 4),
    (sweet_cat_id, 'Maple Syrup', 'Pure Canadian maple syrup', 2.00, '/images/ingredients/maple-syrup.jpg', 5),
    (sweet_cat_id, 'Chocolate Chips', 'Premium dark chocolate chips', 2.50, '/images/ingredients/chocolate-chips.jpg', 6),
    (sweet_cat_id, 'Powdered Sugar', 'Fine powdered sugar dusting', 0.50, '/images/ingredients/powdered-sugar.jpg', 7),
    (sweet_cat_id, 'Honey', 'Pure wildflower honey', 1.75, '/images/ingredients/honey.jpg', 8),
    (sweet_cat_id, 'Cinnamon', 'Ground cinnamon spice', 0.75, '/images/ingredients/cinnamon.jpg', 9),
    (sweet_cat_id, 'Vanilla Ice Cream', 'Premium vanilla ice cream scoop', 3.50, '/images/ingredients/vanilla-ice-cream.jpg', 10)
    ON CONFLICT DO NOTHING;

    -- Insert salted ingredients
    INSERT INTO ingredients (category_id, name, description, price, image_url, display_order) VALUES
    (salted_cat_id, 'Ham', 'Thinly sliced premium ham', 3.50, '/images/ingredients/ham.jpg', 1),
    (salted_cat_id, 'Cheese', 'Melted Swiss cheese', 3.00, '/images/ingredients/cheese.jpg', 2),
    (salted_cat_id, 'Mushrooms', 'Sautéed button mushrooms', 2.50, '/images/ingredients/mushrooms.jpg', 3),
    (salted_cat_id, 'Spinach', 'Fresh baby spinach leaves', 2.00, '/images/ingredients/spinach.jpg', 4),
    (salted_cat_id, 'Tomatoes', 'Fresh cherry tomato halves', 2.25, '/images/ingredients/tomatoes.jpg', 5),
    (salted_cat_id, 'Onions', 'Caramelized onions', 1.75, '/images/ingredients/onions.jpg', 6),
    (salted_cat_id, 'Bell Peppers', 'Grilled bell pepper strips', 2.00, '/images/ingredients/bell-peppers.jpg', 7),
    (salted_cat_id, 'Bacon', 'Crispy bacon strips', 4.00, '/images/ingredients/bacon.jpg', 8),
    (salted_cat_id, 'Avocado', 'Fresh avocado slices', 3.25, '/images/ingredients/avocado.jpg', 9),
    (salted_cat_id, 'Smoked Salmon', 'Premium smoked salmon', 6.00, '/images/ingredients/smoked-salmon.jpg', 10)
    ON CONFLICT DO NOTHING;

END $$;

-- =============================================
-- CREATE FUNCTIONS FOR REAL-TIME SYNC
-- =============================================

-- Function to queue menu synchronization
CREATE OR REPLACE FUNCTION queue_menu_sync(
  sync_type_param VARCHAR(50),
  resource_id_param UUID,
  operation_param VARCHAR(20),
  data_changes_param JSONB DEFAULT '{}',
  priority_param INTEGER DEFAULT 5
)
RETURNS UUID AS $$
DECLARE
  queue_id UUID;
BEGIN
  INSERT INTO menu_sync_queue (
    sync_type,
    resource_id,
    operation,
    data_changes,
    priority
  ) VALUES (
    sync_type_param,
    resource_id_param,
    operation_param,
    data_changes_param,
    priority_param
  ) RETURNING id INTO queue_id;
  
  RETURN queue_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update ingredient stock and auto-disable items
CREATE OR REPLACE FUNCTION update_ingredient_stock_and_sync(
  ingredient_id_param UUID,
  new_quantity INTEGER
)
RETURNS VOID AS $$
DECLARE
  old_available BOOLEAN;
  new_available BOOLEAN;
BEGIN
  -- Get current availability
  SELECT is_available INTO old_available 
  FROM ingredients 
  WHERE id = ingredient_id_param;
  
  -- Update stock
  UPDATE ingredients 
  SET 
    stock_quantity = new_quantity,
    is_available = (new_quantity > 0),
    updated_at = NOW()
  WHERE id = ingredient_id_param;
  
  -- Get new availability
  SELECT is_available INTO new_available 
  FROM ingredients 
  WHERE id = ingredient_id_param;
  
  -- If availability changed, queue sync
  IF old_available != new_available THEN
    PERFORM queue_menu_sync(
      'ingredient',
      ingredient_id_param,
      'update',
      jsonb_build_object('availability_changed', true, 'is_available', new_available),
      1 -- high priority
    );
    
    -- Auto-update menu item availability if ingredient became unavailable
    IF NOT new_available THEN
      UPDATE subcategories 
      SET 
        is_available = FALSE,
        updated_at = NOW()
      WHERE id IN (
        SELECT DISTINCT menu_item_id 
        FROM menu_item_ingredients 
        WHERE ingredient_id = ingredient_id_param 
        AND is_optional = FALSE
      );
      
      -- Queue sync for affected menu items
      PERFORM queue_menu_sync(
        'availability',
        mi.id,
        'update',
        jsonb_build_object('auto_disabled', true, 'reason', 'ingredient_unavailable'),
        1
      )
      FROM subcategories mi
      JOIN menu_item_ingredients mii ON mi.id = mii.menu_item_id
      WHERE mii.ingredient_id = ingredient_id_param 
      AND mii.is_optional = FALSE;
    END IF;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- CREATE TRIGGERS FOR AUTO-SYNC
-- =============================================

-- Trigger function for menu items
CREATE OR REPLACE FUNCTION trigger_menu_item_sync() RETURNS TRIGGER AS $$
BEGIN
  PERFORM queue_menu_sync(
    'menu_item',
    COALESCE(NEW.id, OLD.id),
    CASE 
      WHEN TG_OP = 'INSERT' THEN 'insert'
      WHEN TG_OP = 'UPDATE' THEN 'update'
      WHEN TG_OP = 'DELETE' THEN 'delete'
    END,
    CASE 
      WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD)
      ELSE to_jsonb(NEW)
    END,
    CASE 
      WHEN TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.is_available != NEW.is_available) THEN 1
      ELSE 3
    END
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger function for ingredients
CREATE OR REPLACE FUNCTION trigger_ingredient_sync() RETURNS TRIGGER AS $$
BEGIN
  PERFORM queue_menu_sync(
    'ingredient',
    COALESCE(NEW.id, OLD.id),
    CASE 
      WHEN TG_OP = 'INSERT' THEN 'insert'
      WHEN TG_OP = 'UPDATE' THEN 'update'
      WHEN TG_OP = 'DELETE' THEN 'delete'
    END,
    CASE 
      WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD)
      ELSE to_jsonb(NEW)
    END,
    CASE 
      WHEN TG_OP = 'UPDATE' AND OLD.is_available != NEW.is_available THEN 1
      ELSE 3
    END
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS subcategories_sync_trigger ON subcategories;
CREATE TRIGGER subcategories_sync_trigger
  AFTER INSERT OR UPDATE OR DELETE ON subcategories
  FOR EACH ROW EXECUTE FUNCTION trigger_menu_item_sync();

DROP TRIGGER IF EXISTS ingredients_sync_trigger ON ingredients;
CREATE TRIGGER ingredients_sync_trigger
  AFTER INSERT OR UPDATE OR DELETE ON ingredients
  FOR EACH ROW EXECUTE FUNCTION trigger_ingredient_sync();

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

ALTER TABLE menu_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE ingredient_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE ingredients ENABLE ROW LEVEL SECURITY;
ALTER TABLE subcategories ENABLE ROW LEVEL SECURITY;
ALTER TABLE menu_item_ingredients ENABLE ROW LEVEL SECURITY;
ALTER TABLE customization_presets ENABLE ROW LEVEL SECURITY;
ALTER TABLE menu_sync_queue ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for public read access
CREATE POLICY "Public read access for menu categories" ON menu_categories FOR SELECT USING (is_active = true);
CREATE POLICY "Public read access for ingredient categories" ON ingredient_categories FOR SELECT USING (is_active = true);
CREATE POLICY "Public read access for ingredients" ON ingredients FOR SELECT USING (is_available = true);
CREATE POLICY "Public read access for menu items" ON subcategories FOR SELECT USING (is_available = true);
CREATE POLICY "Public read access for menu item ingredients" ON menu_item_ingredients FOR SELECT USING (true);
CREATE POLICY "Public read access for customization presets" ON customization_presets FOR SELECT USING (true);

-- Admin/Manager access policies
CREATE POLICY "Admin full access to menu categories" ON menu_categories
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
        AND user_profiles.role_id IN (
          SELECT id FROM roles WHERE name IN ('admin', 'manager')
        )
    )
  );

CREATE POLICY "Admin full access to ingredients" ON ingredients
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
        AND user_profiles.role_id IN (
          SELECT id FROM roles WHERE name IN ('admin', 'manager')
        )
    )
  );

CREATE POLICY "Admin full access to menu items" ON subcategories
  FOR ALL USING (
    auth.jwt() ->> 'role' = 'service_role' OR
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
        AND user_profiles.role_id IN (
          SELECT id FROM roles WHERE name IN ('admin', 'manager')
        )
    )
  );

-- Service role policies for sync operations
CREATE POLICY "Service role full access to sync queue" ON menu_sync_queue
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- =============================================
-- ENABLE REALTIME
-- =============================================

ALTER PUBLICATION supabase_realtime ADD TABLE menu_categories;
ALTER PUBLICATION supabase_realtime ADD TABLE ingredient_categories;
ALTER PUBLICATION supabase_realtime ADD TABLE ingredients;
ALTER PUBLICATION supabase_realtime ADD TABLE subcategories;
ALTER PUBLICATION supabase_realtime ADD TABLE menu_item_ingredients;
ALTER PUBLICATION supabase_realtime ADD TABLE customization_presets;
ALTER PUBLICATION supabase_realtime ADD TABLE menu_sync_queue;