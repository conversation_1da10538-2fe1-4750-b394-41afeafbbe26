import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { 
      terminal_id, 
      sync_type, 
      resource_ids, 
      force_sync = false,
      target_platforms = ['pos', 'web', 'mobile']
    } = await request.json()

    if (!terminal_id) {
      return NextResponse.json(
        { error: 'terminal_id is required' },
        { status: 400 }
      )
    }

    const syncOperations = []
    const errors = []

    // Determine what to sync based on sync_type
    switch (sync_type) {
      case 'categories':
        await syncCategories(supabase, terminal_id, resource_ids, force_sync, syncOperations, errors, target_platforms)
        break
      
      case 'subcategories':
        await syncSubcategories(supabase, terminal_id, resource_ids, force_sync, syncOperations, errors, target_platforms)
        break
      
      case 'ingredients':
        await syncIngredients(supabase, terminal_id, resource_ids, force_sync, syncOperations, errors, target_platforms)
        break
      
      case 'availability':
        await syncAvailability(supabase, terminal_id, resource_ids, force_sync, syncOperations, errors, target_platforms)
        break
      
      case 'full_menu':
        await syncFullMenu(supabase, terminal_id, force_sync, syncOperations, errors, target_platforms)
        break
      
      default:
        return NextResponse.json(
          { error: 'Invalid sync_type. Must be one of: categories, subcategories, ingredients, availability, full_menu' },
          { status: 400 }
        )
    }

    // Update terminal sync status
    if (syncOperations.length > 0) {
      await supabase
        .from('pos_terminals')
        .update({
          pending_updates: syncOperations.length,
          sync_status: 'syncing',
          last_sync_request: new Date().toISOString()
        })
        .eq('terminal_id', terminal_id)
    }

    return NextResponse.json({
      success: syncOperations.length > 0,
      message: `Menu sync ${syncOperations.length > 0 ? 'initiated' : 'failed'} - ${syncOperations.length} operations queued`,
      sync_operations: syncOperations,
      errors: errors.length > 0 ? errors : undefined,
      summary: {
        total_operations: syncOperations.length,
        failed_operations: errors.length,
        terminal_id,
        sync_type,
        force_sync,
        target_platforms
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Menu sync error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function syncCategories(supabase: any, terminalId: string, resourceIds: string[] | undefined, forceSync: boolean, syncOperations: any[], errors: string[], targetPlatforms: string[]) {
  try {
    let query = supabase.from('menu_categories').select('*')
    
    if (resourceIds && resourceIds.length > 0) {
      query = query.in('id', resourceIds)
    }
    
    const { data: categories, error } = await query
    
    if (error) {
      errors.push(`Failed to fetch categories: ${error.message}`)
      return
    }

    for (const category of categories || []) {
      const { data: syncOp, error: syncError } = await supabase
        .from('menu_sync_queue')
        .insert({
          sync_type: 'category',
          resource_id: category.id,
          operation: 'update',
          data_changes: category,
          target_platforms: targetPlatforms,
          priority: 5,
          metadata: {
            terminal_id: terminalId,
            force_sync: forceSync,
            category_name: category.name
          }
        })
        .select()
        .single()

      if (syncError) {
        errors.push(`Failed to queue category sync for ${category.name}: ${syncError.message}`)
      } else {
        syncOperations.push(syncOp)
      }
    }
  } catch (error) {
    errors.push(`Category sync error: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

async function syncSubcategories(supabase: any, terminalId: string, resourceIds: string[] | undefined, forceSync: boolean, syncOperations: any[], errors: string[], targetPlatforms: string[]) {
  try {
    let query = supabase
      .from('subcategories')
      .select(`
        *,
        menu_categories (id, name),
        menu_customizations (*)
      `)
    
    if (resourceIds && resourceIds.length > 0) {
      query = query.in('id', resourceIds)
    }
    
    const { data: subcategories, error } = await query
    
    if (error) {
      errors.push(`Failed to fetch subcategories: ${error.message}`)
      return
    }

    for (const subcategory of subcategories || []) {
      const { data: syncOp, error: syncError } = await supabase
        .from('menu_sync_queue')
        .insert({
          sync_type: 'subcategory',
          resource_id: subcategory.id,
          operation: 'update',
          data_changes: subcategory,
          target_platforms: targetPlatforms,
          priority: 4,
          metadata: {
            terminal_id: terminalId,
            force_sync: forceSync,
            subcategory_name: subcategory.name,
            category_id: subcategory.category_id
          }
        })
        .select()
        .single()

      if (syncError) {
        errors.push(`Failed to queue subcategory sync for ${subcategory.name}: ${syncError.message}`)
      } else {
        syncOperations.push(syncOp)
      }
    }
  } catch (error) {
    errors.push(`Subcategory sync error: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

async function syncIngredients(supabase: any, terminalId: string, resourceIds: string[] | undefined, forceSync: boolean, syncOperations: any[], errors: string[], targetPlatforms: string[]) {
  try {
    let query = supabase.from('ingredients').select('*')
    
    if (resourceIds && resourceIds.length > 0) {
      query = query.in('id', resourceIds)
    }
    
    const { data: ingredients, error } = await query
    
    if (error) {
      errors.push(`Failed to fetch ingredients: ${error.message}`)
      return
    }

    for (const ingredient of ingredients || []) {
      const { data: syncOp, error: syncError } = await supabase
        .from('menu_sync_queue')
        .insert({
          sync_type: 'ingredient',
          resource_id: ingredient.id,
          operation: 'update',
          data_changes: ingredient,
          target_platforms: targetPlatforms,
          priority: 3,
          metadata: {
            terminal_id: terminalId,
            force_sync: forceSync,
            ingredient_name: ingredient.name,
            stock_quantity: ingredient.stock_quantity,
            is_available: ingredient.is_available
          }
        })
        .select()
        .single()

      if (syncError) {
        errors.push(`Failed to queue ingredient sync for ${ingredient.name}: ${syncError.message}`)
      } else {
        syncOperations.push(syncOp)
      }
    }
  } catch (error) {
    errors.push(`Ingredient sync error: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

async function syncAvailability(supabase: any, terminalId: string, resourceIds: string[] | undefined, forceSync: boolean, syncOperations: any[], errors: string[], targetPlatforms: string[]) {
  try {
    // Sync ingredient availability
    const { data: ingredients, error: ingredientError } = await supabase
      .from('ingredients')
      .select('id, name, is_available, stock_quantity')

    if (ingredientError) {
      errors.push(`Failed to fetch ingredient availability: ${ingredientError.message}`)
    } else {
      for (const ingredient of ingredients || []) {
        const { data: syncOp, error: syncError } = await supabase
          .from('menu_sync_queue')
          .insert({
            sync_type: 'availability',
            resource_id: ingredient.id,
            operation: 'update',
            data_changes: {
              id: ingredient.id,
              is_available: ingredient.is_available,
              stock_quantity: ingredient.stock_quantity
            },
            target_platforms: targetPlatforms,
            priority: 1, // High priority for availability
            metadata: {
              terminal_id: terminalId,
              force_sync: forceSync,
              resource_type: 'ingredient',
              availability_change: true
            }
          })
          .select()
          .single()

        if (!syncError) {
          syncOperations.push(syncOp)
        }
      }
    }

    // Sync subcategory availability
    const { data: subcategories, error: subcategoryError } = await supabase
      .from('subcategories')
      .select('id, name, active')

    if (subcategoryError) {
      errors.push(`Failed to fetch subcategory availability: ${subcategoryError.message}`)
    } else {
      for (const subcategory of subcategories || []) {
        const { data: syncOp, error: syncError } = await supabase
          .from('menu_sync_queue')
          .insert({
            sync_type: 'availability',
            resource_id: subcategory.id,
            operation: 'update',
            data_changes: {
              id: subcategory.id,
              active: subcategory.active
            },
            target_platforms: targetPlatforms,
            priority: 2,
            metadata: {
              terminal_id: terminalId,
              force_sync: forceSync,
              resource_type: 'subcategory',
              availability_change: true
            }
          })
          .select()
          .single()

        if (!syncError) {
          syncOperations.push(syncOp)
        }
      }
    }
  } catch (error) {
    errors.push(`Availability sync error: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

async function syncFullMenu(supabase: any, terminalId: string, forceSync: boolean, syncOperations: any[], errors: string[], targetPlatforms: string[]) {
  // Sync all menu components in order of dependency
  await syncCategories(supabase, terminalId, undefined, forceSync, syncOperations, errors, targetPlatforms)
  await syncSubcategories(supabase, terminalId, undefined, forceSync, syncOperations, errors, targetPlatforms)
  await syncIngredients(supabase, terminalId, undefined, forceSync, syncOperations, errors, targetPlatforms)
  await syncAvailability(supabase, terminalId, undefined, forceSync, syncOperations, errors, targetPlatforms)
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    const terminalId = searchParams.get('terminal_id')
    const syncType = searchParams.get('sync_type')
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '50')

    // Get recent menu sync operations
    let query = supabase
      .from('menu_sync_queue')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit)

    if (terminalId) {
      query = query.contains('metadata', { terminal_id: terminalId })
    }

    if (syncType) {
      query = query.eq('sync_type', syncType)
    }

    if (status) {
      query = query.eq('sync_status', status)
    }

    const { data: operations, error } = await query

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch menu sync operations' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      operations: operations || [],
      summary: {
        total: operations?.length || 0,
        pending: operations?.filter(op => op.sync_status === 'pending').length || 0,
        processing: operations?.filter(op => op.sync_status === 'processing').length || 0,
        completed: operations?.filter(op => op.sync_status === 'completed').length || 0,
        failed: operations?.filter(op => op.sync_status === 'failed').length || 0
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Menu sync GET error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
