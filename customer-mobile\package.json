{"name": "customer-mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@supabase/supabase-js": "^2.39.0", "date-fns": "^2.30.0", "dotenv": "^17.0.0", "expo": "~53.0.13", "expo-blur": "~14.0.1", "expo-constants": "~17.0.3", "expo-device": "~7.0.1", "expo-font": "~13.0.1", "expo-haptics": "~14.0.0", "expo-image": "~2.0.0", "expo-linear-gradient": "~14.0.1", "expo-linking": "~7.0.3", "expo-local-authentication": "~14.0.1", "expo-location": "~18.0.5", "expo-notifications": "~0.30.1", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.0.1", "react": "19.0.0", "react-hook-form": "^7.48.2", "react-native": "0.79.4", "react-native-gesture-handler": "~2.20.2", "react-native-maps": "1.18.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.1.0", "react-native-toast-message": "^2.1.6", "react-native-vector-icons": "^10.0.3", "react-native-web": "^0.20.0", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/metro-config": "^0.20.0", "@types/react": "~19.0.10", "metro": "^0.82.0", "typescript": "~5.8.3"}, "private": true}