'use client'

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { useMap, useDrawingManager, useGoogleMaps } from '@/hooks/useGoogleMaps'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, MapPin, Save, X, Search, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react'
import { DeliveryZone, PolygonCoordinate } from '@/types/delivery-zones'
import { geocodeAddress } from '@/lib/google-maps'

interface DeliveryZoneMapProps {
  /** Existing delivery zones to display on the map */
  zones?: DeliveryZone[]
  /** Callback when a new zone is created */
  onZoneCreate?: (coordinates: PolygonCoordinate[]) => void
  /** Callback when a zone is updated */
  onZoneUpdate?: (zoneId: string, coordinates: PolygonCoordinate[]) => void
  /** Whether the map is in read-only mode */
  readOnly?: boolean
  /** Initial center coordinates for the map */
  center?: { lat: number; lng: number }
  /** Initial zoom level */
  zoom?: number
  /** Theme preference */
  isDarkTheme?: boolean
  /** Translations object */
  t: {
    title: string
    subtitle: string
    drawPolygon: string
    editPolygon: string
    deletePolygon: string
    saveZone: string
    cancelEdit: string
    clearMap: string
    zoomIn: string
    zoomOut: string
    resetView: string
    searchAddress: string
    loading: string
    error: string
    noZones: string
    instructions: {
      draw: string
      edit: string
      complete: string
    }
    validation: {
      minimumPoints: string
      invalidPolygon: string
      overlappingZone: string
    }
  }
}

type DrawingMode = 'none' | 'drawing' | 'editing'

export const DeliveryZoneMapEnhanced = ({
  zones = [],
  onZoneCreate,
  onZoneUpdate,
  readOnly = false,
  center = { lat: 37.7749, lng: -122.4194 },
  zoom = 12,
  isDarkTheme = true,
  t,
}: DeliveryZoneMapProps) => {
  const { isLoaded, loadError, google } = useGoogleMaps()
  const { mapRef, map } = useMap({ center, zoom })
  const { drawingManager, initializeDrawingManager } = useDrawingManager()

  // State management
  const [drawingMode, setDrawingMode] = useState<DrawingMode>('none')
  const [currentPolygon, setCurrentPolygon] = useState<google.maps.Polygon | null>(null)
  const [editingZoneId, setEditingZoneId] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [mapPolygons, setMapPolygons] = useState<Map<string, google.maps.Polygon>>(new Map())
  const [validationError, setValidationError] = useState<string | null>(null)

  // Refs
  const searchInputRef = useRef<HTMLInputElement>(null)
  const initialCenter = useRef(center)
  const initialZoom = useRef(zoom)

  // Initialize drawing manager when map is ready
  useEffect(() => {
    if (map && google && !readOnly) {
      initializeDrawingManager(map, {
        drawingMode: null,
        drawingControl: false, // We'll use custom controls
        polygonOptions: {
          fillColor: '#2563eb',
          fillOpacity: 0.3,
          strokeWeight: 2,
          strokeColor: '#1d4ed8',
          clickable: true,
          editable: false,
          zIndex: 1,
        },
      })
    }
  }, [map, google, readOnly, initializeDrawingManager])

  // Handle polygon completion
  useEffect(() => {
    if (!drawingManager || !google) return

    const handlePolygonComplete = (polygon: google.maps.Polygon) => {
      const path = polygon.getPath()
      const coordinates: PolygonCoordinate[] = []

      for (let i = 0; i < path.getLength(); i++) {
        const point = path.getAt(i)
        coordinates.push({ lat: point.lat(), lng: point.lng() })
      }

      // Validate polygon
      if (coordinates.length < 3) {
        setValidationError(t.validation.minimumPoints)
        polygon.setMap(null)
        return
      }

      setCurrentPolygon(polygon)
      setDrawingMode('editing')
      setValidationError(null)

      // Disable drawing mode
      drawingManager.setDrawingMode(null)
    }

    google.maps.event.addListener(drawingManager, 'polygoncomplete', handlePolygonComplete)

    return () => {
      google.maps.event.clearListeners(drawingManager, 'polygoncomplete')
    }
  }, [drawingManager, google, t.validation.minimumPoints])

  // Load existing zones on map
  useEffect(() => {
    if (!map || !google || !zones.length) return

    // Clear existing polygons
    mapPolygons.forEach(polygon => polygon.setMap(null))
    const newPolygons = new Map<string, google.maps.Polygon>()

    zones.forEach(zone => {
      if (zone.polygon_coordinates && zone.polygon_coordinates.length >= 3) {
        const polygon = new google.maps.Polygon({
          paths: zone.polygon_coordinates,
          fillColor: zone.is_active ? '#10b981' : '#6b7280',
          fillOpacity: 0.3,
          strokeWeight: 2,
          strokeColor: zone.is_active ? '#059669' : '#4b5563',
          clickable: !readOnly,
          editable: false,
          zIndex: 1,
        })

        polygon.setMap(map)
        newPolygons.set(zone.id, polygon)

        // Add click listener for editing (if not read-only)
        if (!readOnly) {
          google.maps.event.addListener(polygon, 'click', () => {
            handleEditZone(zone.id, polygon)
          })
        }

        // Add info window on hover
        const infoWindow = new google.maps.InfoWindow({
          content: `
            <div class="p-2">
              <h3 class="font-semibold">${zone.name}</h3>
              <p class="text-sm text-gray-600">Fee: $${zone.delivery_fee}</p>
              <p class="text-sm text-gray-600">Min Order: $${zone.minimum_order_amount}</p>
              <p class="text-sm text-gray-600">Status: ${zone.is_active ? 'Active' : 'Inactive'}</p>
            </div>
          `,
        })

        google.maps.event.addListener(polygon, 'mouseover', (event: google.maps.PolyMouseEvent) => {
          infoWindow.setPosition(event.latLng)
          infoWindow.open(map)
        })

        google.maps.event.addListener(polygon, 'mouseout', () => {
          infoWindow.close()
        })
      }
    })

    setMapPolygons(newPolygons)
  }, [map, google, zones, readOnly])

  // Handle drawing mode activation
  const handleStartDrawing = useCallback(() => {
    if (!drawingManager || !google) return

    setDrawingMode('drawing')
    setValidationError(null)
    drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON)
  }, [drawingManager, google])

  // Handle zone editing
  const handleEditZone = useCallback(
    (zoneId: string, polygon: google.maps.Polygon) => {
      if (readOnly) return

      setEditingZoneId(zoneId)
      setCurrentPolygon(polygon)
      setDrawingMode('editing')
      polygon.setEditable(true)
    },
    [readOnly]
  )

  // Handle save zone
  const handleSaveZone = useCallback(() => {
    if (!currentPolygon) return

    const path = currentPolygon.getPath()
    const coordinates: PolygonCoordinate[] = []

    for (let i = 0; i < path.getLength(); i++) {
      const point = path.getAt(i)
      coordinates.push({ lat: point.lat(), lng: point.lng() })
    }

    if (editingZoneId) {
      // Update existing zone
      onZoneUpdate?.(editingZoneId, coordinates)
    } else {
      // Create new zone
      onZoneCreate?.(coordinates)
    }

    handleCancelEdit()
  }, [currentPolygon, editingZoneId, onZoneCreate, onZoneUpdate])

  // Handle cancel edit
  const handleCancelEdit = useCallback(() => {
    if (currentPolygon) {
      if (editingZoneId) {
        // Reset to original position for existing zones
        currentPolygon.setEditable(false)
      } else {
        // Remove new polygon
        currentPolygon.setMap(null)
      }
    }

    setCurrentPolygon(null)
    setEditingZoneId(null)
    setDrawingMode('none')
    setValidationError(null)

    if (drawingManager) {
      drawingManager.setDrawingMode(null)
    }
  }, [currentPolygon, editingZoneId, drawingManager])

  // Handle address search
  const handleAddressSearch = useCallback(async () => {
    if (!searchQuery.trim() || !map || !google) return

    setIsSearching(true)
    try {
      const results = await geocodeAddress(searchQuery)
      if (results.length > 0) {
        const location = results[0].geometry.location
        map.setCenter(location)
        map.setZoom(15)

        // Add a temporary marker
        const marker = new google.maps.Marker({
          position: location,
          map: map,
          title: results[0].formatted_address,
        })

        // Remove marker after 5 seconds
        setTimeout(() => marker.setMap(null), 5000)
      }
    } catch (error) {
      console.error('Geocoding error:', error)
    } finally {
      setIsSearching(false)
    }
  }, [searchQuery, map, google])

  // Handle map controls
  const handleZoomIn = useCallback(() => {
    if (map) {
      map.setZoom((map.getZoom() || 12) + 1)
    }
  }, [map])

  const handleZoomOut = useCallback(() => {
    if (map) {
      map.setZoom((map.getZoom() || 12) - 1)
    }
  }, [map])

  const handleResetView = useCallback(() => {
    if (map) {
      map.setCenter(initialCenter.current)
      map.setZoom(initialZoom.current)
    }
  }, [map])

  // Handle search input key press
  const handleSearchKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        handleAddressSearch()
      }
    },
    [handleAddressSearch]
  )

  if (loadError) {
    return (
      <Card data-oid="-yasee.">
        <CardContent className="p-6" data-oid="u0g:0-s">
          <Alert variant="destructive" data-oid="nh-.4.v">
            <AlertDescription data-oid="eqpbixg">{t.error}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  if (!isLoaded) {
    return (
      <Card data-oid="ig0mqhp">
        <CardContent className="p-6" data-oid="eg59t:8">
          <div className="flex items-center justify-center space-x-2" data-oid="ncf:p4l">
            <Loader2 className="h-4 w-4 animate-spin" data-oid="w9dup4n" />
            <span data-oid="7zrn8ms">{t.loading}</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full" data-oid="ljdzsll">
      <CardHeader data-oid="e1yfjcp">
        <CardTitle data-oid="6oujk:7">{t.title}</CardTitle>
        <CardDescription data-oid="n:e1a6a">{t.subtitle}</CardDescription>
      </CardHeader>
      <CardContent className="p-6" data-oid="-ba94jv">
        {/* Controls */}
        <div className="mb-4 space-y-4" data-oid="owmkvy1">
          {/* Search Bar */}
          <div className="flex space-x-2" data-oid="_jz8x7e">
            <div className="flex-1 relative" data-oid="z_s8q-.">
              <Input
                ref={searchInputRef}
                type="text"
                placeholder={t.searchAddress}
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                onKeyPress={handleSearchKeyPress}
                className="pr-10"
                data-oid="8cyrp3z"
              />

              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                onClick={handleAddressSearch}
                disabled={isSearching || !searchQuery.trim()}
                data-oid="lsz.irt"
              >
                {isSearching ? (
                  <Loader2 className="h-4 w-4 animate-spin" data-oid=":icmdwc" />
                ) : (
                  <Search className="h-4 w-4" data-oid="ywbxjsa" />
                )}
              </Button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2" data-oid=".cdv37f">
            {!readOnly && (
              <>
                {drawingMode === 'none' && (
                  <Button
                    onClick={handleStartDrawing}
                    className="flex items-center space-x-2"
                    data-oid="grbl4v:"
                  >
                    <MapPin className="h-4 w-4" data-oid="jp4oumk" />
                    <span data-oid="k0c-ojn">{t.drawPolygon}</span>
                  </Button>
                )}

                {drawingMode !== 'none' && (
                  <>
                    <Button
                      onClick={handleSaveZone}
                      className="flex items-center space-x-2"
                      data-oid="wg8-o8b"
                    >
                      <Save className="h-4 w-4" data-oid="ebuvtu8" />
                      <span data-oid="6ptwy:_">{t.saveZone}</span>
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleCancelEdit}
                      className="flex items-center space-x-2"
                      data-oid="fslpghs"
                    >
                      <X className="h-4 w-4" data-oid="l82.p14" />
                      <span data-oid="z:8lt5u">{t.cancelEdit}</span>
                    </Button>
                  </>
                )}
              </>
            )}

            {/* Map Controls */}
            <div className="flex space-x-1 ml-auto" data-oid="jc.zf75">
              <Button
                variant="outline"
                size="sm"
                onClick={handleZoomIn}
                title={t.zoomIn}
                data-oid="pd4u:7q"
              >
                <ZoomIn className="h-4 w-4" data-oid="e.ed3_9" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleZoomOut}
                title={t.zoomOut}
                data-oid="-9m6vfm"
              >
                <ZoomOut className="h-4 w-4" data-oid="ghdrvc." />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetView}
                title={t.resetView}
                data-oid="3rmhvg:"
              >
                <RotateCcw className="h-4 w-4" data-oid=".t9rsb_" />
              </Button>
            </div>
          </div>

          {/* Instructions */}
          {drawingMode !== 'none' && (
            <Alert data-oid="x8zr4ez">
              <AlertDescription data-oid="wsulee.">
                {drawingMode === 'drawing' ? t.instructions.draw : t.instructions.edit}
              </AlertDescription>
            </Alert>
          )}

          {/* Validation Error */}
          {validationError && (
            <Alert variant="destructive" data-oid="rc3k51o">
              <AlertDescription data-oid="8ouk.c7">{validationError}</AlertDescription>
            </Alert>
          )}
        </div>

        {/* Map Container */}
        <div className="relative" data-oid=".00jsxd">
          <div
            ref={mapRef}
            className="w-full h-[600px] rounded-lg border border-gray-200"
            style={{ minHeight: '400px' }}
            data-oid="ao-_130"
          />

          {/* No Zones Message */}
          {zones.length === 0 && (
            <div
              className="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-75 rounded-lg"
              data-oid="vqn9d52"
            >
              <div className="text-center" data-oid="t4t12di">
                <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-2" data-oid="5:5f:.n" />
                <p className="text-gray-600" data-oid="4q5aip2">
                  {t.noZones}
                </p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
