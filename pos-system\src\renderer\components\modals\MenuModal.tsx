import React, { useState } from 'react';
import { X } from 'lucide-react';
import { useTheme } from '../../contexts/theme-context';
import { MenuCategoryTabs } from '../menu/MenuCategoryTabs';
import { MenuItemGrid } from '../menu/MenuItemGrid';
import { MenuCart } from '../menu/MenuCart';
import { MenuItemModal } from '../menu/MenuItemModal';
import { PaymentModal } from './PaymentModal';
import toast from 'react-hot-toast';

interface MenuModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCustomer?: any;
  selectedAddress?: any;
  orderType?: 'pickup' | 'delivery';
  isProcessingOrder?: boolean;
  onOrderComplete?: (orderData: {
    items: any[];
    total: number;
    customer?: any;
    address?: any;
    orderType?: string;
    notes?: string;
    paymentData?: any;
  }) => void;
}

export const MenuModal: React.FC<MenuModalProps> = ({
  isOpen,
  onClose,
  selectedCustomer,
  selectedAddress,
  orderType = 'delivery',
  isProcessingOrder = false,
  onOrderComplete
}) => {
  const { resolvedTheme } = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>("");
  const [selectedMenuItem, setSelectedMenuItem] = useState<any>(null);
  const [cartItems, setCartItems] = useState<any[]>([]);
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  const handleAddToCart = (item: any, quantity: number, customizations: any[], notes: string) => {
    // Ensure item has required properties
    const basePrice = item.price || 0;
    const itemQuantity = quantity || 1;
    
    // Calculate customization price
    const customizationPrice = customizations.reduce((sum, c) => sum + (c.price || 0), 0);
    const itemTotal = (basePrice + customizationPrice) * itemQuantity;
    
    const cartItem = {
      id: Date.now() + Math.random(), // Unique ID for cart item
      name: item.name || 'Unknown Item',
      price: basePrice,
      quantity: itemQuantity,
      customizations,
      notes,
      totalPrice: itemTotal,
      // Include other item properties
      ...item,
    };
    setCartItems(prev => [...prev, cartItem]);
  };

  const handleCheckout = async () => {
    if (cartItems.length === 0) {
      return;
    }

    // Show payment modal instead of immediately completing order
    setShowPaymentModal(true);
  };

  const handlePaymentComplete = async (paymentData: any) => {
    const total = cartItems.reduce((sum, item) => sum + (item.totalPrice || 0), 0);

    try {
      if (onOrderComplete) {
        await onOrderComplete({
          items: cartItems,
          total,
          customer: selectedCustomer,
          address: selectedAddress,
          orderType,
          notes: '', // Could be enhanced to collect order notes
          paymentData
        });
      }

      // Show success feedback
      setTimeout(() => {
        toast.success('Order completed successfully!');
      }, 100);

      // Reset state
      setCartItems([]);
      setSelectedCategory("all");
      setSelectedSubcategory("");
      setShowPaymentModal(false);
      onClose();
    } catch (error) {
      console.error('Error completing order:', error);
      toast.error('Failed to complete order. Please try again.');
      setShowPaymentModal(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className={`w-full max-w-6xl h-[90vh] rounded-3xl shadow-2xl border transform transition-all duration-300 overflow-hidden ${
        resolvedTheme === 'dark'
          ? 'bg-gray-800/90 border-gray-700/50 backdrop-blur-xl'
          : 'bg-white/90 border-gray-200/50 backdrop-blur-xl'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200/20">
          <div>
            <h2 className={`text-2xl font-bold ${
              resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              Menu - {orderType === 'delivery' ? 'Delivery' : 'Pickup'} Order
            </h2>
            {selectedCustomer && (
              <p className={`text-sm mt-1 ${
                resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-600'
              }`}>
                Customer: {selectedCustomer.name} • {selectedCustomer.phone_number}
              </p>
            )}
            {selectedAddress && orderType === 'delivery' && (
              <p className={`text-xs mt-1 ${
                resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
              }`}>
                Delivery to: {selectedAddress.street}, {selectedAddress.postal_code}
              </p>
            )}
          </div>
          <button
            type="button"
            onClick={onClose}
            title="Close menu"
            aria-label="Close menu"
            className={`p-2 rounded-xl transition-all duration-200 ${
              resolvedTheme === 'dark'
                ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700/50'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100/50'
            }`}
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* Left Panel - Menu */}
          <div className="flex-1 flex flex-col">
            <MenuCategoryTabs
              selectedCategory={selectedCategory}
              onCategoryChange={setSelectedCategory}
              selectedSubcategory={selectedSubcategory}
              onSubcategoryChange={setSelectedSubcategory}
              hideAllItemsButton={true}
            />
            <MenuItemGrid
              selectedCategory={selectedCategory}
              selectedSubcategory={selectedSubcategory}
              orderType={orderType}
              onItemSelect={setSelectedMenuItem}
            />
          </div>

          {/* Right Panel - Cart */}
          <MenuCart
            cartItems={cartItems}
            onCheckout={handleCheckout}
            onUpdateCart={setCartItems}
          />
        </div>

        {/* Menu Item Customization Modal */}
        {selectedMenuItem && (
          <MenuItemModal
            isOpen={!!selectedMenuItem}
            menuItem={selectedMenuItem}
            orderType={orderType}
            onClose={() => setSelectedMenuItem(null)}
            onAddToCart={handleAddToCart}
            isCustomizable={selectedMenuItem.is_customizable || false}
          />
        )}

        {/* Payment Modal */}
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          orderTotal={cartItems.reduce((sum, item) => sum + (item.totalPrice || 0), 0)}
          onPaymentComplete={handlePaymentComplete}
          isProcessing={isProcessingOrder}
        />
        
        {/* Processing Overlay */}
        {isProcessingOrder && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[60]">
            <div className={`rounded-2xl p-8 ${
              resolvedTheme === 'dark'
                ? 'bg-gray-800/90 border-gray-700/50'
                : 'bg-white/90 border-gray-200/50'
            } border backdrop-blur-xl`}>
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                <p className={`text-lg font-medium ${
                  resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  Processing Order...
                </p>
                <p className={`text-sm ${
                  resolvedTheme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Please wait while we create your order
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};