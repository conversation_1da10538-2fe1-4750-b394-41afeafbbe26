/**
 * Real-time Order Tracking Component
 * Displays live order status, preparation progress, and delivery tracking
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useRealtimeOrder } from '../../hooks/use-realtime-order';
import { useAuth } from '../../hooks/auth-hooks';
import type { OrderStatus } from '../../types/types';

interface OrderTrackerProps {
  orderId: string;
  className?: string;
}

interface StatusStepProps {
  status: OrderStatus;
  currentStatus: OrderStatus;
  timestamp?: string;
  isActive: boolean;
  isCompleted: boolean;
}

const StatusStep: React.FC<StatusStepProps> = ({
  status,
  currentStatus,
  timestamp,
  isActive,
  isCompleted,
}) => {
  const getStatusInfo = (status: OrderStatus) => {
    switch (status) {
      case 'pending':
        return { label: 'Order Received', icon: '📝', color: 'text-blue-600' };
      case 'confirmed':
        return { label: 'Confirmed', icon: '✅', color: 'text-green-600' };
      case 'preparing':
        return { label: 'Preparing', icon: '👨‍🍳', color: 'text-orange-600' };
      case 'ready':
        return { label: 'Ready', icon: '🥞', color: 'text-purple-600' };
      case 'out_for_delivery':
        return { label: 'Out for Delivery', icon: '🚗', color: 'text-blue-600' };
      case 'delivered':
        return { label: 'Delivered', icon: '🎉', color: 'text-green-600' };
      default:
        return { label: status, icon: '⏳', color: 'text-gray-600' };
    }
  };

  const statusInfo = getStatusInfo(status);

  return (
    <div
      className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-300 ${
        isActive
          ? 'bg-blue-50 border-2 border-blue-200'
          : isCompleted
            ? 'bg-green-50 border border-green-200'
            : 'bg-gray-50 border border-gray-200'
      }`}
    >
      <div className={`text-2xl ${isActive ? 'animate-pulse' : ''}`}>{statusInfo.icon}</div>
      <div className="flex-1">
        <div className={`font-medium ${statusInfo.color}`}>{statusInfo.label}</div>
        {timestamp && (
          <div className="text-sm text-gray-500">{new Date(timestamp).toLocaleTimeString()}</div>
        )}
      </div>
      {isActive && <div className="w-3 h-3 bg-blue-500 rounded-full animate-ping"></div>}
      {isCompleted && <div className="w-3 h-3 bg-green-500 rounded-full"></div>}
    </div>
  );
};

interface ProgressBarProps {
  progress: number;
  label: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ progress, label }) => {
  return (
    <div className="w-full">
      <div className="flex justify-between text-sm text-gray-600 mb-1">
        <span>{label}</span>
        <span>{progress}%</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-500 ease-out"
          style={{ width: `${progress}%` }}
        ></div>
      </div>
    </div>
  );
};

interface DeliveryMapProps {
  driverLocation: { lat: number; lng: number } | null;
  customerAddress: string;
}

const DeliveryMap: React.FC<DeliveryMapProps> = ({ driverLocation, customerAddress }) => {
  // This would integrate with a mapping service like Google Maps or Mapbox
  // For now, we'll show a placeholder
  return (
    <div className="bg-gray-100 rounded-lg p-6 text-center">
      <div className="text-4xl mb-2">🗺️</div>
      <div className="text-gray-600 mb-2">Live Delivery Tracking</div>
      {driverLocation ? (
        <div className="space-y-2">
          <div className="text-sm text-green-600 font-medium">🚗 Driver is on the way!</div>
          <div className="text-xs text-gray-500">
            Location: {driverLocation.lat.toFixed(4)}, {driverLocation.lng.toFixed(4)}
          </div>
        </div>
      ) : (
        <div className="text-sm text-gray-500">
          Delivery tracking will appear here once your order is out for delivery
        </div>
      )}
    </div>
  );
};

export const RealTimeOrderTracker: React.FC<OrderTrackerProps> = ({ orderId, className = '' }) => {
  const { user } = useAuth();
  const {
    order,
    status,
    estimatedDeliveryTime,
    preparationProgress,
    driverLocation,
    statusHistory,
    loading,
    error,
    lastUpdated,
    getTimeRemaining,
    getStatusMessage,
    refreshOrder,
  } = useRealtimeOrder(orderId, user?.id);

  const [autoRefresh, setAutoRefresh] = useState(true);

  // Auto-refresh every 30 seconds if enabled
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      refreshOrder();
    }, 30000);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshOrder]);

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="text-center text-red-600">
          <div className="text-4xl mb-2">⚠️</div>
          <div className="font-medium mb-2">Unable to load order tracking</div>
          <div className="text-sm text-gray-600 mb-4">{error}</div>
          <button
            onClick={refreshOrder}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="text-center text-gray-600">
          <div className="text-4xl mb-2">📦</div>
          <div className="font-medium">Order not found</div>
        </div>
      </div>
    );
  }

  const statusSteps: OrderStatus[] =
    order.deliveryMethod === 'delivery'
      ? ['pending', 'confirmed', 'preparing', 'out_for_delivery', 'delivered']
      : ['pending', 'confirmed', 'preparing', 'ready'];

  const currentStatusIndex = statusSteps.indexOf(status!);
  const timeRemaining = getTimeRemaining();
  const statusMessage = getStatusMessage();

  return (
    <div className={`bg-white rounded-lg shadow-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold mb-1">Order #{orderId}</h2>
            <p className="text-blue-100">{statusMessage}</p>
          </div>
          <div className="text-right">
            {timeRemaining && <div className="text-lg font-semibold">{timeRemaining}</div>}
            <div className="text-sm text-blue-100">
              {estimatedDeliveryTime ? 'Estimated time' : 'Processing'}
            </div>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Status Timeline */}
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            📋 Order Progress
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`ml-auto text-sm px-3 py-1 rounded-full transition-colors ${
                autoRefresh ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'
              }`}
            >
              {autoRefresh ? '🔄 Live' : '⏸️ Paused'}
            </button>
          </h3>
          <div className="space-y-3">
            {statusSteps.map((stepStatus, index) => {
              const historyItem = statusHistory.find(h => h.status === stepStatus);
              return (
                <StatusStep
                  key={stepStatus}
                  status={stepStatus}
                  currentStatus={status!}
                  timestamp={historyItem?.timestamp}
                  isActive={status === stepStatus}
                  isCompleted={index < currentStatusIndex}
                />
              );
            })}
          </div>
        </div>

        {/* Preparation Progress */}
        {status === 'preparing' && preparationProgress > 0 && (
          <div>
            <h3 className="text-lg font-semibold mb-4">👨‍🍳 Kitchen Progress</h3>
            <ProgressBar
              progress={preparationProgress}
              label="Your delicious crepes are being prepared"
            />
          </div>
        )}

        {/* Delivery Tracking */}
        {(status === 'out_for_delivery' || driverLocation) &&
          order.deliveryMethod === 'delivery' && (
            <div>
              <h3 className="text-lg font-semibold mb-4">🚗 Delivery Tracking</h3>
              <DeliveryMap
                driverLocation={driverLocation}
                customerAddress={order.deliveryAddress?.addressLine1 || 'Your address'}
              />
            </div>
          )}

        {/* Order Summary */}
        <div>
          <h3 className="text-lg font-semibold mb-4">📦 Order Details</h3>
          <div className="bg-gray-50 rounded-lg p-4 space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Order Method:</span>
              <span className="font-medium capitalize">{order.deliveryMethod}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Total Amount:</span>
              <span className="font-medium">€{order.total.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Payment Status:</span>
              <span
                className={`font-medium capitalize ${
                  order.paymentStatus === 'paid' ? 'text-green-600' : 'text-orange-600'
                }`}
              >
                {order.paymentStatus}
              </span>
            </div>
            {order.deliveryAddress && (
              <div className="flex justify-between">
                <span className="text-gray-600">Delivery Address:</span>
                <span className="font-medium text-right">{order.deliveryAddress.addressLine1}</span>
              </div>
            )}
          </div>
        </div>

        {/* Last Updated */}
        {lastUpdated && (
          <div className="text-center text-sm text-gray-500">
            Last updated: {new Date(lastUpdated).toLocaleString()}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={refreshOrder}
            className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            🔄 Refresh
          </button>
          {order.deliveryMethod === 'delivery' && (
            <button className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              📞 Contact Driver
            </button>
          )}
          <button className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
            ❓ Need Help?
          </button>
        </div>
      </div>
    </div>
  );
};

export default RealTimeOrderTracker;
