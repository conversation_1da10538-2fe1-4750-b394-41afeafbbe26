/**
 * Supabase Edge Function: Send Email Notification
 * Handles sending email notifications for order updates and other events
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

interface EmailRequest {
  to: string;
  subject: string;
  html: string;
  text?: string;
  orderId?: string;
  userId?: string;
  type: string;
}

interface OrderEmailData {
  orderNumber: string;
  customerName: string;
  status: string;
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  total: number;
  estimatedDeliveryTime?: string;
  deliveryAddress?: string;
  trackingUrl?: string;
}

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Email templates
const getOrderStatusTemplate = (data: OrderEmailData) => {
  const statusMessages = {
    confirmed: 'Your order has been confirmed and is being prepared.',
    preparing: 'Our chefs are now preparing your delicious crepes!',
    ready: 'Your order is ready for pickup!',
    out_for_delivery: 'Your order is on its way to you!',
    delivered: 'Your order has been delivered. Enjoy your meal!',
    cancelled: 'Your order has been cancelled.'
  };

  const statusMessage = statusMessages[data.status as keyof typeof statusMessages] || 
    `Your order status has been updated to: ${data.status}`;

  return {
    subject: `Order Update - ${data.orderNumber}`,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Order Update</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .status-badge { display: inline-block; padding: 8px 16px; background: #4CAF50; color: white; border-radius: 20px; font-weight: bold; text-transform: uppercase; }
          .order-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
          .item { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid #eee; }
          .total { font-weight: bold; font-size: 18px; color: #667eea; }
          .button { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🥞 The Small Creperie</h1>
          <h2>Order Update</h2>
        </div>
        <div class="content">
          <p>Hello ${data.customerName},</p>
          
          <div style="text-align: center; margin: 20px 0;">
            <span class="status-badge">${data.status.replace('_', ' ')}</span>
          </div>
          
          <p><strong>${statusMessage}</strong></p>
          
          <div class="order-details">
            <h3>Order #${data.orderNumber}</h3>
            
            <div style="margin: 20px 0;">
              <h4>Items:</h4>
              ${data.items.map(item => `
                <div class="item">
                  <span>${item.quantity}x ${item.name}</span>
                  <span>€${(item.price * item.quantity).toFixed(2)}</span>
                </div>
              `).join('')}
              <div class="item total">
                <span>Total</span>
                <span>€${data.total.toFixed(2)}</span>
              </div>
            </div>
            
            ${data.estimatedDeliveryTime ? `
              <p><strong>Estimated Delivery:</strong> ${data.estimatedDeliveryTime}</p>
            ` : ''}
            
            ${data.deliveryAddress ? `
              <p><strong>Delivery Address:</strong><br>${data.deliveryAddress}</p>
            ` : ''}
          </div>
          
          ${data.trackingUrl ? `
            <div style="text-align: center;">
              <a href="${data.trackingUrl}" class="button">Track Your Order</a>
            </div>
          ` : ''}
          
          <p>Thank you for choosing The Small Creperie! If you have any questions, please don't hesitate to contact us.</p>
        </div>
        
        <div class="footer">
          <p>The Small Creperie<br>
          📧 <EMAIL> | 📞 +31 20 123 4567<br>
          <a href="#">Unsubscribe</a> | <a href="#">Update Preferences</a></p>
        </div>
      </body>
      </html>
    `,
    text: `
      The Small Creperie - Order Update
      
      Hello ${data.customerName},
      
      ${statusMessage}
      
      Order #${data.orderNumber}
      
      Items:
      ${data.items.map(item => `${item.quantity}x ${item.name} - €${(item.price * item.quantity).toFixed(2)}`).join('\n')}
      
      Total: €${data.total.toFixed(2)}
      
      ${data.estimatedDeliveryTime ? `Estimated Delivery: ${data.estimatedDeliveryTime}\n` : ''}
      ${data.deliveryAddress ? `Delivery Address: ${data.deliveryAddress}\n` : ''}
      ${data.trackingUrl ? `Track your order: ${data.trackingUrl}\n` : ''}
      
      Thank you for choosing The Small Creperie!
      
      ---
      The Small Creperie
      <EMAIL> | +31 20 123 4567
    `
  };
};

const sendEmail = async (emailData: EmailRequest) => {
  const resendApiKey = Deno.env.get('RESEND_API_KEY');
  
  if (!resendApiKey) {
    throw new Error('RESEND_API_KEY environment variable is not set');
  }

  const response = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${resendApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      from: 'The Small Creperie <<EMAIL>>',
      to: [emailData.to],
      subject: emailData.subject,
      html: emailData.html,
      text: emailData.text,
    }),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Failed to send email: ${error}`);
  }

  return await response.json();
};

const logNotification = async (supabase: any, data: {
  userId: string;
  type: string;
  channel: string;
  status: string;
  orderId?: string;
  errorMessage?: string;
}) => {
  try {
    await supabase
      .from('notification_logs')
      .insert({
        user_id: data.userId,
        type: data.type,
        channel: data.channel,
        status: data.status,
        order_id: data.orderId,
        error_message: data.errorMessage,
        sent_at: new Date().toISOString(),
      });
  } catch (error) {
    console.error('Failed to log notification:', error);
  }
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { to, subject, html, text, orderId, userId, type, orderData } = await req.json();

    if (!to || !type) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: to, type' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    let emailContent = { subject, html, text };

    // Generate email content based on type and order data
    if (type === 'order_status_update' && orderData) {
      emailContent = getOrderStatusTemplate(orderData);
    }

    // Send the email
    const result = await sendEmail({
      to,
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text,
      orderId,
      userId,
      type,
    });

    // Log successful notification
    if (userId) {
      await logNotification(supabaseClient, {
        userId,
        type,
        channel: 'email',
        status: 'sent',
        orderId,
      });
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        messageId: result.id,
        message: 'Email sent successfully' 
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error sending email:', error);

    // Log failed notification
    try {
      const { userId, type, orderId } = await req.json();
      if (userId) {
        const supabaseClient = createClient(
          Deno.env.get('SUPABASE_URL') ?? '',
          Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
        );
        
        await logNotification(supabaseClient, {
          userId,
          type,
          channel: 'email',
          status: 'failed',
          orderId,
          errorMessage: error.message,
        });
      }
    } catch (logError) {
      console.error('Failed to log error:', logError);
    }

    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});