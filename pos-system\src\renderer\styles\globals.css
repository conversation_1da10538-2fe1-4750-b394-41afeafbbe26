@tailwind base;
@tailwind components;
@tailwind utilities;

/* NEUTRAL BACKGROUND - NO FORCED COLORS */
html, body, #root {
  background: #f8fafc;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* Base styles for touch interface */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  html {
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
  }
  
  body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f8fafc;
    overflow-x: hidden;
  }
  
  /* Disable text selection for touch interface */
  body, button, input {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* Allow text selection for input fields */
  input[type="text"], input[type="number"], textarea {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
  
  /* Remove tap highlight */
  * {
    -webkit-tap-highlight-color: transparent;
  }
}

/* Component styles */
@layer components {
  /* Touch-friendly button base */
  .btn-touch {
    @apply min-h-touch min-w-touch px-4 py-3 rounded-lg font-medium transition-all duration-150 ease-in-out;
    @apply active:scale-95 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  /* Primary button */
  .btn-primary {
    @apply btn-touch bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  /* Secondary button */
  .btn-secondary {
    @apply btn-touch bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  /* Success button */
  .btn-success {
    @apply btn-touch bg-pos-success text-white hover:bg-green-700 focus:ring-green-500;
  }
  
  /* Warning button */
  .btn-warning {
    @apply btn-touch bg-pos-warning text-white hover:bg-yellow-600 focus:ring-yellow-500;
  }
  
  /* Error button */
  .btn-error {
    @apply btn-touch bg-pos-error text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  /* Touch-friendly input */
  .input-touch {
    @apply min-h-touch px-4 py-3 border border-gray-300 rounded-lg text-touch-base;
    @apply focus:outline-none focus:ring-2 focus:ring-pos-primary focus:border-transparent;
    @apply transition-all duration-150 ease-in-out;
  }
  
  /* Card component */
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6;
  }
  
  /* Order status badges */
  .status-pending {
    @apply bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium;
  }
  
  .status-preparing {
    @apply bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium;
  }
  
  .status-ready {
    @apply bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium;
  }
  
  .status-completed {
    @apply bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium;
  }
  
  /* Grid layouts for touch */
  .grid-touch {
    @apply grid gap-4;
  }
  
  .grid-touch-sm {
    @apply grid gap-2;
  }
  
  /* Scrollable areas with touch-friendly scrollbars */
  .scroll-touch {
    @apply overflow-auto;
    scrollbar-width: thick;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }
  
  .scroll-touch::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }
  
  .scroll-touch::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 6px;
  }
  
  .scroll-touch::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 6px;
  }
  
  .scroll-touch::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
}

/* Utility classes */
@layer utilities {
  /* Touch feedback animation */
  .touch-feedback {
    @apply transition-transform duration-100 ease-in-out active:scale-95;
  }
  
  /* Prevent text selection */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* Enable text selection */
  .select-text {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
  
  /* Hide scrollbars */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  /* Full height minus header */
  .h-screen-header {
    height: calc(100vh - 4rem);
  }
  
  /* Touch-safe z-index values */
  .z-modal {
    z-index: 1000;
  }
  
  .z-dropdown {
    z-index: 100;
  }
  
  .z-header {
    z-index: 50;
  }
}