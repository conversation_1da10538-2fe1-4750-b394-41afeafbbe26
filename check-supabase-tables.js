const { createClient } = require('@supabase/supabase-js');

// <PERSON>ript to check all tables in the Supabase database
async function checkSupabaseTables() {
  const supabaseUrl = 'https://voiwzwyfnkzvcffuxpwl.supabase.co';
  const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZvaXd6d3lmbmt6dmNmZnV4cHdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0MDU2MzIsImV4cCI6MjA2NDk4MTYzMn0.RvabQ6EGP1gBb0w8J8_VpaqqXgkpEFR7Nbbkmw11wNA';

  const supabase = createClient(supabaseUrl, supabaseKey);

  try {
    console.log('🔍 Checking Supabase database tables...\n');
    
    // List of tables to check based on the database types
    const tablesToCheck = [
      'app_configurations',
      'customers', 
      'delivery_zones',
      'subcategories',
      'menu_categories',
      'branches',
      'orders',
      'order_items',
      'customer_addresses',
      'ingredients',
      'pos_terminals',
      'pos_heartbeats',
      'pos_configurations',
      'web_configurations',
      'user_analytics'
    ];

    for (const tableName of tablesToCheck) {
      try {
        console.log(`📋 Checking table: ${tableName}`);
        
        // Try to get table info by selecting count
        const { data, error, count } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true });

        if (error) {
          console.log(`   ❌ Error: ${error.message}`);
        } else {
          console.log(`   ✅ Exists - Row count: ${count || 0}`);
          
          // If table exists, try to get column info by selecting first row
          const { data: sampleData, error: sampleError } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);
            
          if (!sampleError && sampleData && sampleData.length > 0) {
            const columns = Object.keys(sampleData[0]);
            console.log(`   📊 Columns: ${columns.join(', ')}`);
          } else if (!sampleError) {
            console.log(`   📊 Table is empty, cannot determine columns`);
          }
        }
      } catch (tableError) {
        console.log(`   ❌ Exception: ${tableError.message}`);
      }
      console.log(''); // Empty line for readability
    }

    // Special check for delivery_zones table structure
    console.log('🎯 Special check for delivery_zones table:');
    try {
      const { data: dzData, error: dzError } = await supabase
        .from('delivery_zones')
        .select('*')
        .limit(1);
        
      if (dzError) {
        console.log(`❌ delivery_zones error: ${dzError.message}`);
      } else {
        console.log(`✅ delivery_zones accessible`);
        if (dzData && dzData.length > 0) {
          console.log(`📊 Sample data:`, JSON.stringify(dzData[0], null, 2));
        } else {
          console.log(`📊 Table exists but is empty`);
        }
      }
    } catch (error) {
      console.log(`❌ delivery_zones exception: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

checkSupabaseTables();