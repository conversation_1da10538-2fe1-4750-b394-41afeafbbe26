// Authentication Utilities - Main Export File
// This file exports all authentication-related utilities for use across platforms

// Core authentication functions
export {
  loginWithEmail,
  requestPhoneOTP,
  loginWithPhoneOTP,
  loginWithPIN,
  logout,
  getCurrentUser,
  getUserProfile,
  checkUserPermissions,
  validateUserSession,
} from './auth-utils';

// Session management
export {
  createSession,
  validateSession,
  refreshSession,
  revokeSession,
  revokeAllUserSessions,
  getUserSessions,
  cleanupExpiredSessions,
  logAuthAttempt,
  getRecentFailedAttempts,
  isRateLimited,
  getSessionByToken,
} from './session-utils';

// OTP (One-Time Password) utilities
export {
  generateOTP,
  verifyOTP,
  canRequestOTP,
  getActiveOTP,
  cleanupExpiredOTPs,
  invalidatePhoneOTPs,
  getOTPHistory,
  formatPhoneNumber,
  maskPhoneNumber,
  generateOTPCode,
  isOTPExpired,
} from './otp-utils';

// PIN authentication utilities
export {
  hashPIN,
  verifyPIN,
  isValidPIN,
  verifyStaffPIN,
  updateStaffPIN,
  generateSecurePIN,
  getPINAttemptHistory,
  reset<PERSON>NLockout,
  maskPIN,
  getStaffByPIN,
} from './pin-utils';

// Two-Factor Authentication (2FA) utilities
export {
  generateTwoFASecret,
  verifyTwoFASetup,
  enableTwoFA,
  disableTwoFA,
  getRemainingBackupCodes,
  regenerateBackupCodes,
  isTwoFAEnabled,
  generateTOTPCode,
  isValidTwoFACode,
  getTwoFAStatus,
} from './two-fa-utils';

// Role-Based Access Control (RBAC) utilities
export {
  getUserRolePermissions,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  hasRole,
  getAllRoles,
  getAllPermissions,
  assignUserRole,
  removeUserRole,
  getRoleByName,
  getRolePermissions,
  isAdmin,
  isManager,
  isStaff,
  isCustomer,
  isAdminOrManager,
  hasStaffAccess,
  getUserHighestRole,
  isValidPermission,
  isValidRole,
  getRoleLevel,
  hasHigherOrEqualRole,
  getDefaultRolePermissions,
} from './role-utils';

// Configuration and Supabase client
export {
  supabase,
  createAdminClient,
  AUTH_CONFIG,
} from './config';

// Type definitions
export type {
  User,
  UserProfile,
  Role,
  AuthSession,
  AuthAttempt,
  PhoneVerification,
  TwoFABackupCode,
  Branch,
  LoginRequest,
  LoginResponse,
  OTPRequest,
  OTPResponse,
  TwoFASetupResponse,
  TwoFAVerifyRequest,
  Permission,
  UserRole,
  Platform,
  AuthMethod,
} from './types';

// Database types
export type {
  Database,
  Tables,
  TablesInsert,
  TablesUpdate,
  Enums,
} from './database.types';

// Utility constants
export const AUTH_CONSTANTS = {
  // Session durations (in seconds)
  SESSION_DURATION: {
    ADMIN: 8 * 60 * 60, // 8 hours
    STAFF: 12 * 60 * 60, // 12 hours
    CUSTOMER: 30 * 24 * 60 * 60, // 30 days
  },
  
  // OTP settings
  OTP: {
    LENGTH: 6,
    EXPIRY_MINUTES: 10,
    MAX_ATTEMPTS: 3,
    RATE_LIMIT_MINUTES: 1,
  },
  
  // PIN settings
  PIN: {
    LENGTH: 4,
    MAX_ATTEMPTS: 5,
    LOCKOUT_DURATION_MINUTES: 30,
  },
  
  // 2FA settings
  TWO_FA: {
    BACKUP_CODES_COUNT: 10,
    ISSUER_NAME: 'Small Business POS',
  },
  
  // Rate limiting
  RATE_LIMIT: {
    LOGIN_ATTEMPTS: 5,
    LOGIN_WINDOW_MINUTES: 15,
    OTP_REQUESTS: 3,
    OTP_WINDOW_MINUTES: 5,
  },
  
  // Platforms
  PLATFORMS: {
    ADMIN_DASHBOARD: 'admin-dashboard',
    CUSTOMER_WEB: 'customer-web',
    CUSTOMER_MOBILE: 'customer-mobile',
    POS_SYSTEM: 'pos-system',
  } as const,
  
  // Auth methods
  AUTH_METHODS: {
    EMAIL_PASSWORD: 'email_password',
    PHONE_OTP: 'phone_otp',
    PIN: 'pin',
    TWO_FA: 'two_fa',
  } as const,
  
  // User roles
  USER_ROLES: {
    ADMIN: 'admin',
    MANAGER: 'manager',
    STAFF: 'staff',
    CUSTOMER: 'customer',
  } as const,
  
  // Permissions
  PERMISSIONS: {
    // User management
    USERS_READ: 'users.read',
    USERS_WRITE: 'users.write',
    USERS_DELETE: 'users.delete',
    
    // Product management
    PRODUCTS_READ: 'products.read',
    PRODUCTS_WRITE: 'products.write',
    PRODUCTS_DELETE: 'products.delete',
    
    // Order management
    ORDERS_READ: 'orders.read',
    ORDERS_WRITE: 'orders.write',
    ORDERS_DELETE: 'orders.delete',
    
    // Inventory management
    INVENTORY_READ: 'inventory.read',
    INVENTORY_WRITE: 'inventory.write',
    
    // Reports
    REPORTS_READ: 'reports.read',
    REPORTS_WRITE: 'reports.write',
    
    // Settings
    SETTINGS_READ: 'settings.read',
    SETTINGS_WRITE: 'settings.write',
    
    // System access
    POS_ACCESS: 'pos.access',
    ADMIN_ACCESS: 'admin.access',
  } as const,
};

// Error messages
export const AUTH_ERRORS = {
  INVALID_CREDENTIALS: 'Invalid email or password',
  INVALID_OTP: 'Invalid or expired OTP code',
  INVALID_PIN: 'Invalid PIN',
  INVALID_2FA: 'Invalid 2FA code',
  USER_NOT_FOUND: 'User not found',
  USER_DISABLED: 'User account is disabled',
  SESSION_EXPIRED: 'Session has expired',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions',
  RATE_LIMITED: 'Too many attempts. Please try again later',
  ACCOUNT_LOCKED: 'Account is temporarily locked',
  PHONE_NOT_VERIFIED: 'Phone number not verified',
  TWO_FA_REQUIRED: '2FA verification required',
  TWO_FA_NOT_ENABLED: '2FA is not enabled',
  BACKUP_CODE_INVALID: 'Invalid or already used backup code',
  PIN_LOCKED: 'PIN is locked due to too many failed attempts',
  BRANCH_NOT_FOUND: 'Branch not found',
  STAFF_NOT_FOUND: 'Staff member not found',
  NETWORK_ERROR: 'Network error. Please check your connection',
  SERVER_ERROR: 'Server error. Please try again later',
} as const;

// Success messages
export const AUTH_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  OTP_SENT: 'OTP sent successfully',
  OTP_VERIFIED: 'OTP verified successfully',
  PIN_VERIFIED: 'PIN verified successfully',
  TWO_FA_ENABLED: '2FA enabled successfully',
  TWO_FA_DISABLED: '2FA disabled successfully',
  TWO_FA_VERIFIED: '2FA verified successfully',
  BACKUP_CODES_GENERATED: 'Backup codes generated successfully',
  SESSION_REFRESHED: 'Session refreshed successfully',
  PASSWORD_UPDATED: 'Password updated successfully',
  PIN_UPDATED: 'PIN updated successfully',
  PROFILE_UPDATED: 'Profile updated successfully',
} as const;

// Import hasPermission for use in authHelpers
import { hasPermission } from './role-utils';

// Helper functions for common auth checks
export const authHelpers = {
  /**
   * Check if user can access admin dashboard
   */
  canAccessAdmin: async (userId: string): Promise<boolean> => {
    return await hasPermission(userId, 'admin.access');
  },
  
  /**
   * Check if user can access POS system
   */
  canAccessPOS: async (userId: string): Promise<boolean> => {
    return await hasPermission(userId, 'pos.access');
  },
  
  /**
   * Check if user can manage other users
   */
  canManageUsers: async (userId: string): Promise<boolean> => {
    return await hasPermission(userId, 'users.write');
  },
  
  /**
   * Check if user can manage products
   */
  canManageProducts: async (userId: string): Promise<boolean> => {
    return await hasPermission(userId, 'products.write');
  },
  
  /**
   * Check if user can view reports
   */
  canViewReports: async (userId: string): Promise<boolean> => {
    return await hasPermission(userId, 'reports.read');
  },
  
  /**
   * Get platform-specific auth method
   */
  getPlatformAuthMethod: (platform: import('./types').Platform): import('./types').AuthMethod[] => {
    const platformMethods: Record<import('./types').Platform, import('./types').AuthMethod[]> = {
      'admin-dashboard': ['email_password', 'two_fa'],
      'customer-web': ['phone_otp'],
      'customer-mobile': ['phone_otp'],
      'pos-system': ['pin'],
    };
    
    return platformMethods[platform] || [];
  },
  
  /**
   * Get session duration for user role
   */
  getSessionDuration: (role: import('./types').UserRole): number => {
    const durations: Record<import('./types').UserRole, number> = {
      admin: AUTH_CONSTANTS.SESSION_DURATION.ADMIN,
      manager: AUTH_CONSTANTS.SESSION_DURATION.STAFF,
      staff: AUTH_CONSTANTS.SESSION_DURATION.STAFF,
      customer: AUTH_CONSTANTS.SESSION_DURATION.CUSTOMER,
    };
    
    return durations[role] || AUTH_CONSTANTS.SESSION_DURATION.CUSTOMER;
  },
};