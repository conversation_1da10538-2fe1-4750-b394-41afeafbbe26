import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';

interface DeliveryValidationResponse {
  success: boolean;
  deliveryAvailable: boolean;
  zone: {
    id: any;
    name: any;
    deliveryFee: number;
    minimumOrderAmount: number;
    estimatedTime: { min: any; max: any };
  };
  validation: {
    meetsMinimumOrder: boolean;
    orderAmount: any;
    estimatedTotal: any;
    shortfall: number;
  };
  coordinates: { lat: any; lng: any };
  message?: string;
}

// POST /api/delivery-zones/validate - Validate delivery address and return zone info
export async function POST(request: NextRequest) {
  const supabase = createServerSupabaseClient();
  
  try {
    const body = await request.json();
    const { 
      address, // { lat: number, lng: number } or string address
      branchId,
      orderAmount = 0 // for minimum order validation
    } = body;

    if (!address) {
      return NextResponse.json(
        { error: 'address is required' },
        { status: 400 }
      );
    }

    let lat: number, lng: number;

    // Handle coordinate object or address string
    if (typeof address === 'object' && address.lat && address.lng) {
      lat = address.lat;
      lng = address.lng;
    } else if (typeof address === 'string') {
      // For now, return error asking for coordinates
      // In the future, integrate with Google Geocoding API
      return NextResponse.json({
        error: 'GEOCODING_REQUIRED',
        message: 'Please provide coordinates {lat, lng} for the delivery address',
        suggestGeocoding: true
      }, { status: 400 });
    } else {
      return NextResponse.json(
        { error: 'Invalid address format. Expected {lat, lng} coordinates' },
        { status: 400 }
      );
    }

    // Validate coordinates
    if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      return NextResponse.json(
        { error: 'Invalid coordinates. Latitude must be between -90 and 90, longitude between -180 and 180' },
        { status: 400 }
      );
    }

    // Find delivery zone using existing database function
    const { data: zoneData, error: zoneError } = await supabase
      .rpc('find_delivery_zone_for_address', {
        address_lat: lat,
        address_lng: lng,
        target_branch_id: branchId || null
      });

    if (zoneError) {
      console.error('Error finding delivery zone:', zoneError);
      return NextResponse.json(
        { error: 'Failed to validate delivery address' },
        { status: 500 }
      );
    }

    if (!zoneData || zoneData.length === 0) {
      // Address not in any delivery zone
      return NextResponse.json({
        success: false,
        deliveryAvailable: false,
        reason: 'ADDRESS_NOT_IN_ZONE',
        message: 'Delivery is not available to this address',
        suggestion: 'Please try pickup instead or check if the address is correct',
        alternatives: {
          pickup: true,
          nearestZone: null // Could implement nearest zone finding
        }
      });
    }

    const zone = zoneData[0];
    const deliveryFee = parseFloat(zone.delivery_fee);
    const minimumOrderAmount = parseFloat(zone.minimum_order_amount);

    // Check minimum order amount
    const meetsMinimum = orderAmount >= minimumOrderAmount;

    // Calculate estimated delivery cost
    const estimatedTotal = orderAmount + deliveryFee;

    const response: DeliveryValidationResponse = {
      success: true,
      deliveryAvailable: true,
      zone: {
        id: zone.zone_id,
        name: zone.zone_name,
        deliveryFee,
        minimumOrderAmount,
        estimatedTime: {
          min: zone.estimated_delivery_time_min,
          max: zone.estimated_delivery_time_max
        }
      },
      validation: {
        meetsMinimumOrder: meetsMinimum,
        orderAmount,
        estimatedTotal,
        shortfall: meetsMinimum ? 0 : minimumOrderAmount - orderAmount
      },
      coordinates: { lat, lng }
    };

    if (!meetsMinimum) {
      response.message = `Minimum order amount for ${zone.zone_name} is €${minimumOrderAmount.toFixed(2)}. Add €${(minimumOrderAmount - orderAmount).toFixed(2)} more to your order.`;
    } else {
      response.message = `Delivery available to ${zone.zone_name}. Delivery fee: €${deliveryFee.toFixed(2)}`;
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Delivery validation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/delivery-zones/validate - Get validation configuration
export async function GET(request: NextRequest) {
  const supabase = createServerSupabaseClient();
  
  try {
    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');

    // Get all active delivery zones for preview
    const { data: zones, error } = await supabase
      .from('delivery_zones')
      .select(`
        id,
        name,
        delivery_fee,
        minimum_order_amount,
        estimated_delivery_time_min,
        estimated_delivery_time_max,
        coordinates,
        is_active
      `)
      .eq('is_active', true)
      .eq('branch_id', branchId || null)
      .order('priority', { ascending: false });

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch delivery zones' },
        { status: 500 }
      );
    }

    // Calculate coverage statistics
    const stats = {
      totalZones: zones?.length || 0,
      averageDeliveryFee: 0,
      averageMinimumOrder: 0,
      estimatedTimeRange: { min: 0, max: 0 }
    };

    if (zones && zones.length > 0) {
      stats.averageDeliveryFee = zones.reduce((sum, zone) => sum + parseFloat(zone.delivery_fee), 0) / zones.length;
      stats.averageMinimumOrder = zones.reduce((sum, zone) => sum + parseFloat(zone.minimum_order_amount), 0) / zones.length;
      stats.estimatedTimeRange.min = Math.min(...zones.map(zone => zone.estimated_delivery_time_min));
      stats.estimatedTimeRange.max = Math.max(...zones.map(zone => zone.estimated_delivery_time_max));
    }

    return NextResponse.json({
      success: true,
      zones: zones || [],
      statistics: stats,
      configuration: {
        geocodingRequired: true,
        coordinateFormat: 'decimal_degrees',
        supportedRegions: ['global'], // Could be configured per branch
        validationEnabled: true
      }
    });

  } catch (error) {
    console.error('Error fetching delivery zone validation config:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}