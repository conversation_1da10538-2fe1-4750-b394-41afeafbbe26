-- Admin Control System Migration
-- Creates comprehensive tables for centralized admin control of all apps

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- RESTAURANT SETTINGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS restaurant_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  setting_key VARCHAR(100) NOT NULL UNIQUE,
  setting_value JSONB NOT NULL,
  setting_type VARCHAR(50) NOT NULL CHECK (setting_type IN ('string', 'number', 'boolean', 'json', 'array')),
  category VARCHAR(50) NOT NULL, -- 'general', 'payment', 'notification', 'display', 'operation'
  description TEXT,
  is_public BOOLEAN NOT NULL DEFAULT FALSE, -- Whether setting is visible to customer apps
  requires_restart BOOLEAN NOT NULL DEFAULT FALSE,
  validation_rules JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for restaurant_settings
CREATE INDEX IF NOT EXISTS idx_restaurant_settings_key ON restaurant_settings (setting_key);
CREATE INDEX IF NOT EXISTS idx_restaurant_settings_category ON restaurant_settings (category);
CREATE INDEX IF NOT EXISTS idx_restaurant_settings_public ON restaurant_settings (is_public);

-- =============================================
-- POS CONFIGURATIONS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS pos_configurations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
  terminal_id VARCHAR(100) NOT NULL,
  config_key VARCHAR(100) NOT NULL,
  config_value JSONB NOT NULL,
  config_type VARCHAR(50) NOT NULL CHECK (config_type IN ('terminal', 'printer', 'payment', 'display', 'hardware')),
  description TEXT,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  sync_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (sync_status IN ('pending', 'synced', 'failed')),
  last_sync_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create unique constraint and indexes for pos_configurations
ALTER TABLE pos_configurations ADD CONSTRAINT pos_config_unique UNIQUE(branch_id, terminal_id, config_key);
CREATE INDEX IF NOT EXISTS idx_pos_config_branch ON pos_configurations (branch_id);
CREATE INDEX IF NOT EXISTS idx_pos_config_terminal ON pos_configurations (terminal_id);
CREATE INDEX IF NOT EXISTS idx_pos_config_type ON pos_configurations (config_type);
CREATE INDEX IF NOT EXISTS idx_pos_config_sync_status ON pos_configurations (sync_status);

-- =============================================
-- APP CONFIGURATIONS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS app_configurations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  app_name VARCHAR(50) NOT NULL CHECK (app_name IN ('customer-mobile', 'customer-web', 'pos-system', 'admin-dashboard')),
  config_key VARCHAR(100) NOT NULL,
  config_value JSONB NOT NULL,
  environment VARCHAR(20) NOT NULL DEFAULT 'production' CHECK (environment IN ('development', 'staging', 'production')),
  is_sensitive BOOLEAN NOT NULL DEFAULT FALSE,
  applies_to_branch UUID REFERENCES branches(id) ON DELETE CASCADE, -- NULL means global config
  version VARCHAR(20) DEFAULT '1.0.0',
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create unique constraint and indexes for app_configurations
ALTER TABLE app_configurations ADD CONSTRAINT app_config_unique UNIQUE(app_name, config_key, applies_to_branch, environment);
CREATE INDEX IF NOT EXISTS idx_app_config_app ON app_configurations (app_name);
CREATE INDEX IF NOT EXISTS idx_app_config_key ON app_configurations (config_key);
CREATE INDEX IF NOT EXISTS idx_app_config_branch ON app_configurations (applies_to_branch);
CREATE INDEX IF NOT EXISTS idx_app_config_environment ON app_configurations (environment);

-- =============================================
-- PAYMENT SETTINGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS payment_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
  provider VARCHAR(50) NOT NULL, -- 'stripe', 'square', 'paypal', 'cash', 'card'
  provider_config JSONB NOT NULL DEFAULT '{}',
  is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
  processing_fee_percentage DECIMAL(5,2) DEFAULT 0.00,
  processing_fee_fixed DECIMAL(10,2) DEFAULT 0.00,
  daily_limit DECIMAL(15,2),
  transaction_limit DECIMAL(15,2),
  requires_signature_above DECIMAL(10,2),
  auto_settle BOOLEAN NOT NULL DEFAULT TRUE,
  settlement_time TIME DEFAULT '23:00:00',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create unique constraint and indexes for payment_settings
ALTER TABLE payment_settings ADD CONSTRAINT payment_settings_unique UNIQUE(branch_id, provider);
CREATE INDEX IF NOT EXISTS idx_payment_settings_branch ON payment_settings (branch_id);
CREATE INDEX IF NOT EXISTS idx_payment_settings_provider ON payment_settings (provider);
CREATE INDEX IF NOT EXISTS idx_payment_settings_enabled ON payment_settings (is_enabled);

-- =============================================
-- NOTIFICATION PREFERENCES TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS notification_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  notification_type VARCHAR(50) NOT NULL, -- 'order', 'payment', 'inventory', 'staff', 'system'
  channel VARCHAR(30) NOT NULL CHECK (channel IN ('email', 'sms', 'push', 'in-app', 'webhook')),
  is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
  conditions JSONB DEFAULT '{}', -- conditions for when to send
  template_id VARCHAR(100),
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE, -- NULL means all branches
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create unique constraint and indexes for notification_preferences
ALTER TABLE notification_preferences ADD CONSTRAINT notification_prefs_unique UNIQUE(user_id, notification_type, channel, branch_id);
CREATE INDEX IF NOT EXISTS idx_notification_prefs_user ON notification_preferences (user_id);
CREATE INDEX IF NOT EXISTS idx_notification_prefs_type ON notification_preferences (notification_type);
CREATE INDEX IF NOT EXISTS idx_notification_prefs_channel ON notification_preferences (channel);
CREATE INDEX IF NOT EXISTS idx_notification_prefs_branch ON notification_preferences (branch_id);

-- =============================================
-- SYSTEM CONFIGURATIONS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS system_configurations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  config_group VARCHAR(50) NOT NULL, -- 'security', 'performance', 'features', 'integrations'
  config_key VARCHAR(100) NOT NULL,
  config_value JSONB NOT NULL,
  data_type VARCHAR(20) NOT NULL CHECK (data_type IN ('string', 'number', 'boolean', 'object', 'array')),
  is_readonly BOOLEAN NOT NULL DEFAULT FALSE,
  requires_restart BOOLEAN NOT NULL DEFAULT FALSE,
  validation_schema JSONB DEFAULT '{}',
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create unique constraint and indexes for system_configurations
ALTER TABLE system_configurations ADD CONSTRAINT system_config_unique UNIQUE(config_group, config_key);
CREATE INDEX IF NOT EXISTS idx_system_config_group ON system_configurations (config_group);
CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_configurations (config_key);
CREATE INDEX IF NOT EXISTS idx_system_config_readonly ON system_configurations (is_readonly);-- =============================================
-- MENU SYNCHRONIZATION TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS menu_synchronization (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
  app_name VARCHAR(50) NOT NULL CHECK (app_name IN ('customer-mobile', 'customer-web', 'pos-system')),
  sync_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (sync_status IN ('pending', 'syncing', 'synced', 'failed')),
  last_sync_at TIMESTAMP WITH TIME ZONE,
  sync_error TEXT,
  version_hash VARCHAR(64), -- hash of product data for change detection
  price_override DECIMAL(10,2), -- branch-specific price
  availability_override BOOLEAN, -- branch-specific availability
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create unique constraint and indexes for menu_synchronization
ALTER TABLE menu_synchronization ADD CONSTRAINT menu_sync_unique UNIQUE(product_id, branch_id, app_name);
CREATE INDEX IF NOT EXISTS idx_menu_sync_product ON menu_synchronization (product_id);
CREATE INDEX IF NOT EXISTS idx_menu_sync_branch ON menu_synchronization (branch_id);
CREATE INDEX IF NOT EXISTS idx_menu_sync_app ON menu_synchronization (app_name);
CREATE INDEX IF NOT EXISTS idx_menu_sync_status ON menu_synchronization (sync_status);
CREATE INDEX IF NOT EXISTS idx_menu_sync_last_sync ON menu_synchronization (last_sync_at);

-- =============================================
-- BRANCH CONFIGURATIONS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS branch_configurations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
  config_category VARCHAR(50) NOT NULL, -- 'hours', 'delivery', 'pos', 'staff', 'features'
  config_data JSONB NOT NULL DEFAULT '{}',
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  effective_from TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  effective_until TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create unique constraint and indexes for branch_configurations
ALTER TABLE branch_configurations ADD CONSTRAINT branch_config_unique UNIQUE(branch_id, config_category);
CREATE INDEX IF NOT EXISTS idx_branch_config_branch ON branch_configurations (branch_id);
CREATE INDEX IF NOT EXISTS idx_branch_config_category ON branch_configurations (config_category);
CREATE INDEX IF NOT EXISTS idx_branch_config_active ON branch_configurations (is_active);
CREATE INDEX IF NOT EXISTS idx_branch_config_effective ON branch_configurations (effective_from, effective_until);

-- =============================================
-- FEATURE FLAGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS feature_flags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  flag_name VARCHAR(100) NOT NULL UNIQUE,
  flag_key VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  is_enabled BOOLEAN NOT NULL DEFAULT FALSE,
  rollout_percentage INTEGER NOT NULL DEFAULT 0 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
  target_apps VARCHAR(50)[] DEFAULT ARRAY['customer-mobile', 'customer-web', 'pos-system', 'admin-dashboard'],
  target_branches UUID[] DEFAULT ARRAY[]::UUID[],
  conditions JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for feature_flags
CREATE INDEX IF NOT EXISTS idx_feature_flags_name ON feature_flags (flag_name);
CREATE INDEX IF NOT EXISTS idx_feature_flags_key ON feature_flags (flag_key);
CREATE INDEX IF NOT EXISTS idx_feature_flags_enabled ON feature_flags (is_enabled);

-- =============================================
-- APP SYNC LOGS TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS app_sync_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  app_name VARCHAR(50) NOT NULL,
  sync_type VARCHAR(50) NOT NULL, -- 'settings', 'menu', 'config', 'feature_flags'
  entity_type VARCHAR(50) NOT NULL, -- 'product', 'setting', 'config', 'flag'
  entity_id UUID,
  branch_id UUID REFERENCES branches(id) ON DELETE SET NULL,
  sync_status VARCHAR(20) NOT NULL CHECK (sync_status IN ('started', 'completed', 'failed')),
  sync_direction VARCHAR(10) NOT NULL CHECK (sync_direction IN ('push', 'pull')),
  changes JSONB DEFAULT '{}',
  error_message TEXT,
  sync_duration_ms INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for app_sync_logs
CREATE INDEX IF NOT EXISTS idx_app_sync_logs_app ON app_sync_logs (app_name);
CREATE INDEX IF NOT EXISTS idx_app_sync_logs_type ON app_sync_logs (sync_type);
CREATE INDEX IF NOT EXISTS idx_app_sync_logs_status ON app_sync_logs (sync_status);
CREATE INDEX IF NOT EXISTS idx_app_sync_logs_created_at ON app_sync_logs (created_at);
CREATE INDEX IF NOT EXISTS idx_app_sync_logs_branch ON app_sync_logs (branch_id);

-- =============================================
-- Insert Default Settings
-- =============================================

-- Default restaurant settings
INSERT INTO restaurant_settings (setting_key, setting_value, setting_type, category, description, is_public) VALUES
('restaurant_name', '"Delicious Bites"', 'string', 'general', 'Restaurant name displayed across all apps', true),
('restaurant_phone', '"+****************"', 'string', 'general', 'Main restaurant phone number', true),
('restaurant_email', '"<EMAIL>"', 'string', 'general', 'Main restaurant email address', true),
('currency', '"USD"', 'string', 'general', 'Default currency for all transactions', true),
('tax_rate', '8.25', 'number', 'payment', 'Default tax rate percentage', true),
('delivery_fee', '2.99', 'number', 'operation', 'Default delivery fee', true),
('minimum_order', '15.00', 'number', 'operation', 'Minimum order amount for delivery', true),
('enable_loyalty', 'true', 'boolean', 'general', 'Enable loyalty points system', true),
('loyalty_points_rate', '1', 'number', 'general', 'Points earned per dollar spent', true),
('enable_online_ordering', 'true', 'boolean', 'operation', 'Allow online ordering', true),
('enable_delivery', 'true', 'boolean', 'operation', 'Enable delivery service', true),
('enable_pickup', 'true', 'boolean', 'operation', 'Enable pickup orders', true)
ON CONFLICT (setting_key) DO NOTHING;

-- Default system configurations
INSERT INTO system_configurations (config_group, config_key, config_value, data_type, description) VALUES
('security', 'session_timeout', '3600', 'number', 'Session timeout in seconds'),
('security', 'max_login_attempts', '5', 'number', 'Maximum login attempts before lockout'),
('security', 'lockout_duration', '900', 'number', 'Account lockout duration in seconds'),
('performance', 'cache_duration', '300', 'number', 'Default cache duration in seconds'),
('performance', 'max_concurrent_orders', '100', 'number', 'Maximum concurrent orders'),
('features', 'enable_analytics', 'true', 'boolean', 'Enable analytics tracking'),
('features', 'enable_push_notifications', 'true', 'boolean', 'Enable push notifications'),
('integrations', 'enable_realtime_sync', 'true', 'boolean', 'Enable real-time synchronization')
ON CONFLICT (config_group, config_key) DO NOTHING;

-- Default feature flags
INSERT INTO feature_flags (flag_name, flag_key, description, is_enabled) VALUES
('Enhanced POS Interface', 'enhanced_pos_interface', 'New POS interface with improved UX', false),
('Mobile Push Notifications', 'mobile_push_notifications', 'Push notifications for mobile app', true),
('Advanced Analytics', 'advanced_analytics', 'Advanced analytics dashboard', false),
('Multi-language Support', 'multi_language_support', 'Support for multiple languages', false),
('Voice Ordering', 'voice_ordering', 'Voice-based ordering system', false)
ON CONFLICT (flag_key) DO NOTHING;-- =============================================
-- Create Functions for Settings Management
-- =============================================

-- Function to get setting value with type casting
CREATE OR REPLACE FUNCTION get_restaurant_setting(setting_name TEXT, default_value JSONB DEFAULT NULL)
RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  SELECT setting_value INTO result
  FROM restaurant_settings
  WHERE setting_key = setting_name AND is_public = true;
  
  RETURN COALESCE(result, default_value);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update setting with validation
CREATE OR REPLACE FUNCTION update_restaurant_setting(
  setting_name TEXT,
  new_value JSONB,
  user_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  setting_exists BOOLEAN;
BEGIN
  SELECT EXISTS(SELECT 1 FROM restaurant_settings WHERE setting_key = setting_name) INTO setting_exists;
  
  IF setting_exists THEN
    UPDATE restaurant_settings 
    SET setting_value = new_value, 
        updated_at = NOW(),
        updated_by = user_id
    WHERE setting_key = setting_name;
  ELSE
    INSERT INTO restaurant_settings (setting_key, setting_value, setting_type, category, updated_by)
    VALUES (setting_name, new_value, 'json', 'general', user_id);
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to sync menu item to all apps
CREATE OR REPLACE FUNCTION sync_menu_item_to_apps(
  item_id UUID,
  branch_id UUID DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
  app_name TEXT;
  branch_rec RECORD;
BEGIN
  -- If no branch specified, sync to all branches
  IF branch_id IS NULL THEN
    FOR branch_rec IN SELECT id FROM branches WHERE is_active = true LOOP
      -- Sync to each app
      FOR app_name IN SELECT unnest(ARRAY['customer-mobile', 'customer-web', 'pos-system']) LOOP
        INSERT INTO menu_synchronization (product_id, branch_id, app_name, sync_status)
        VALUES (item_id, branch_rec.id, app_name, 'pending')
        ON CONFLICT (product_id, branch_id, app_name) 
        DO UPDATE SET sync_status = 'pending', updated_at = NOW();
      END LOOP;
    END LOOP;
  ELSE
    -- Sync to specific branch
    FOR app_name IN SELECT unnest(ARRAY['customer-mobile', 'customer-web', 'pos-system']) LOOP
      INSERT INTO menu_synchronization (product_id, branch_id, app_name, sync_status)
      VALUES (item_id, branch_id, app_name, 'pending')
      ON CONFLICT (product_id, branch_id, app_name) 
      DO UPDATE SET sync_status = 'pending', updated_at = NOW();
    END LOOP;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- Create Triggers for Automatic Sync
-- =============================================

-- Trigger to sync menu items when products are updated
CREATE OR REPLACE FUNCTION trigger_menu_sync() RETURNS TRIGGER AS $$
BEGIN
  -- Queue sync for all apps when product is updated
  PERFORM sync_menu_item_to_apps(NEW.id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on products table
DROP TRIGGER IF EXISTS products_sync_trigger ON products;
CREATE TRIGGER products_sync_trigger
  AFTER INSERT OR UPDATE ON products
  FOR EACH ROW EXECUTE FUNCTION trigger_menu_sync();

-- Trigger to log setting changes
CREATE OR REPLACE FUNCTION log_setting_change() RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO audit_logs (action, resource, resource_id, user_id, old_values, new_values)
  VALUES (
    CASE WHEN TG_OP = 'INSERT' THEN 'create' ELSE 'update' END,
    'restaurant_settings',
    NEW.id,
    NEW.updated_by,
    CASE WHEN TG_OP = 'UPDATE' THEN to_jsonb(OLD) ELSE '{}'::jsonb END,
    to_jsonb(NEW)
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on restaurant_settings table
DROP TRIGGER IF EXISTS restaurant_settings_audit_trigger ON restaurant_settings;
CREATE TRIGGER restaurant_settings_audit_trigger
  AFTER INSERT OR UPDATE ON restaurant_settings
  FOR EACH ROW EXECUTE FUNCTION log_setting_change();

-- =============================================
-- Enable Row Level Security
-- =============================================

-- Enable RLS on all tables
ALTER TABLE restaurant_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE pos_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE app_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE menu_synchronization ENABLE ROW LEVEL SECURITY;
ALTER TABLE branch_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE app_sync_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (admin users can manage all, apps can read their relevant settings)
CREATE POLICY "Admin users can manage restaurant settings" ON restaurant_settings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
        AND user_profiles.role_id IN (
          SELECT id FROM roles WHERE name IN ('admin', 'manager')
        )
    )
  );

CREATE POLICY "Apps can read public restaurant settings" ON restaurant_settings
  FOR SELECT USING (is_public = true);

-- Similar policies for other tables...
CREATE POLICY "Admin users can manage POS configurations" ON pos_configurations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
        AND user_profiles.role_id IN (
          SELECT id FROM roles WHERE name IN ('admin', 'manager')
        )
    )
  );

CREATE POLICY "Service role can manage all configurations" ON restaurant_settings
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all POS configurations" ON pos_configurations
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can manage all app configurations" ON app_configurations
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Enable realtime for critical tables
ALTER PUBLICATION supabase_realtime ADD TABLE restaurant_settings;
ALTER PUBLICATION supabase_realtime ADD TABLE pos_configurations;
ALTER PUBLICATION supabase_realtime ADD TABLE app_configurations;
ALTER PUBLICATION supabase_realtime ADD TABLE menu_synchronization;
ALTER PUBLICATION supabase_realtime ADD TABLE feature_flags;

-- Create additional indexes for better performance
CREATE INDEX IF NOT EXISTS idx_restaurant_settings_updated_at ON restaurant_settings (updated_at);
CREATE INDEX IF NOT EXISTS idx_pos_configurations_updated_at ON pos_configurations (updated_at);
CREATE INDEX IF NOT EXISTS idx_app_configurations_updated_at ON app_configurations (updated_at);
CREATE INDEX IF NOT EXISTS idx_menu_sync_updated_at ON menu_synchronization (updated_at);