/// <reference types="cypress" />

describe('Customer Menu - Browse Menu', () => {
  beforeEach(() => {
    // Clear any existing data
    cy.clearAllCookies();
    cy.clearAllLocalStorage();
    cy.clearAllSessionStorage();
    
    // Set up API interceptors
    cy.intercept('GET', '/api/menu', { fixture: 'menu/menu-items.json' }).as('getMenu');
    cy.intercept('GET', '/api/menu/categories', { fixture: 'menu/menu-items.json' }).as('getCategories');
    cy.intercept('POST', '/api/cart/add', { statusCode: 200 }).as('addToCart');
    cy.intercept('GET', '/api/cart', { fixture: 'orders/orders.json' }).as('getCart');
    
    // Login as customer
    cy.loginAsCustomer();
  });

  describe('Menu Page Display', () => {
    it('should display menu page with all required elements', () => {
      cy.visitMenuPage();
      cy.waitForPageLoad();
      
      // Check page title and heading
      cy.title().should('contain', 'Menu');
      cy.get('h1').should('contain', 'Our Menu');
      
      // Check main sections
      cy.get('[data-testid="menu-categories"]').should('be.visible');
      cy.get('[data-testid="menu-items-grid"]').should('be.visible');
      cy.get('[data-testid="search-bar"]').should('be.visible');
      cy.get('[data-testid="filter-options"]').should('be.visible');
      cy.get('[data-testid="sort-options"]').should('be.visible');
      
      // Check cart summary
      cy.get('[data-testid="cart-summary"]').should('be.visible');
      cy.get('[data-testid="cart-item-count"]').should('be.visible');
    });

    it('should load menu items from API', () => {
      cy.visitMenuPage();
      
      cy.wait('@getMenu');
      cy.wait('@getCategories');
      
      // Check that menu items are displayed
      cy.get('[data-testid="menu-item-card"]').should('have.length.greaterThan', 0);
      
      // Check first menu item has required elements
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="item-image"]').should('be.visible');
        cy.get('[data-testid="item-name"]').should('be.visible');
        cy.get('[data-testid="item-description"]').should('be.visible');
        cy.get('[data-testid="item-price"]').should('be.visible');
        cy.get('[data-testid="add-to-cart-button"]').should('be.visible');
      });
    });

    it('should display categories navigation', () => {
      cy.visitMenuPage();
      
      cy.get('[data-testid="menu-categories"]').within(() => {
        cy.get('[data-testid="category-all"]').should('be.visible').and('contain', 'All');
        cy.get('[data-testid="category-appetizers"]').should('be.visible').and('contain', 'Appetizers');
        cy.get('[data-testid="category-mains"]').should('be.visible').and('contain', 'Main Courses');
        cy.get('[data-testid="category-desserts"]').should('be.visible').and('contain', 'Desserts');
        cy.get('[data-testid="category-beverages"]').should('be.visible').and('contain', 'Beverages');
      });
    });
  });

  describe('Menu Item Display', () => {
    beforeEach(() => {
      cy.visitMenuPage();
    });

    it('should display menu item details correctly', () => {
      cy.fixture('menu/menu-items').then((menuData) => {
        const firstItem = menuData.menuItems[0];
        
        cy.get('[data-testid="menu-item-card"]').first().within(() => {
          cy.get('[data-testid="item-name"]').should('contain', firstItem.name);
          cy.get('[data-testid="item-description"]').should('contain', firstItem.description);
          cy.get('[data-testid="item-price"]').should('contain', `$${firstItem.price}`);
          
          // Check image
          cy.get('[data-testid="item-image"]')
            .should('have.attr', 'src')
            .and('include', firstItem.image);
          
          cy.get('[data-testid="item-image"]')
            .should('have.attr', 'alt', firstItem.name);
        });
      });
    });

    it('should display dietary tags and allergens', () => {
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        // Check for dietary tags
        cy.get('[data-testid="dietary-tags"]').should('be.visible');
        cy.get('[data-testid="tag-vegetarian"]').should('be.visible');
        
        // Check for allergen information
        cy.get('[data-testid="allergen-info"]').should('be.visible');
        cy.get('[data-testid="allergen-nuts"]').should('be.visible');
      });
    });

    it('should show unavailable items correctly', () => {
      cy.fixture('menu/menu-items').then((menuData) => {
        // Find an unavailable item
        const unavailableItem = menuData.menuItems.find(item => !item.available);
        
        if (unavailableItem) {
          cy.get(`[data-testid="menu-item-${unavailableItem.id}"]`).within(() => {
            cy.get('[data-testid="unavailable-overlay"]').should('be.visible');
            cy.get('[data-testid="add-to-cart-button"]').should('be.disabled');
            cy.get('[data-testid="unavailable-text"]').should('contain', 'Currently Unavailable');
          });
        }
      });
    });

    it('should display customization options indicator', () => {
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="customizable-indicator"]').should('be.visible');
        cy.get('[data-testid="customization-count"]').should('contain', 'customizations available');
      });
    });
  });

  describe('Search Functionality', () => {
    beforeEach(() => {
      cy.visitMenuPage();
    });

    it('should search menu items by name', () => {
      cy.searchMenu('pizza');
      
      cy.get('[data-testid="menu-item-card"]').should('have.length.greaterThan', 0);
      cy.get('[data-testid="menu-item-card"]').each(($card) => {
        cy.wrap($card).find('[data-testid="item-name"]')
          .invoke('text')
          .should('match', /pizza/i);
      });
    });

    it('should search menu items by description', () => {
      cy.searchMenu('cheese');
      
      cy.get('[data-testid="menu-item-card"]').should('have.length.greaterThan', 0);
      cy.get('[data-testid="search-results-count"]')
        .should('be.visible')
        .and('contain', 'results found');
    });

    it('should show no results message for invalid search', () => {
      cy.searchMenu('nonexistentitem');
      
      cy.get('[data-testid="no-results-message"]')
        .should('be.visible')
        .and('contain', 'No items found');
      
      cy.get('[data-testid="search-suggestions"]').should('be.visible');
    });

    it('should clear search results', () => {
      cy.searchMenu('pizza');
      cy.get('[data-testid="menu-item-card"]').should('have.length.lessThan', 10);
      
      cy.get('[data-testid="clear-search"]').click();
      
      cy.get('[data-testid="search-bar"]').should('have.value', '');
      cy.get('[data-testid="menu-item-card"]').should('have.length.greaterThan', 10);
    });

    it('should show search suggestions', () => {
      cy.get('[data-testid="search-bar"]').type('piz');
      
      cy.get('[data-testid="search-suggestions"]').should('be.visible');
      cy.get('[data-testid="suggestion-item"]').should('have.length.greaterThan', 0);
      
      cy.get('[data-testid="suggestion-item"]').first().click();
      
      cy.get('[data-testid="search-bar"]').should('have.value', 'pizza');
    });
  });

  describe('Category Filtering', () => {
    beforeEach(() => {
      cy.visitMenuPage();
    });

    it('should filter by category', () => {
      cy.filterMenuByCategory('appetizers');
      
      cy.get('[data-testid="menu-item-card"]').should('have.length.greaterThan', 0);
      cy.get('[data-testid="category-appetizers"]').should('have.class', 'active');
      
      // Verify all items are appetizers
      cy.get('[data-testid="menu-item-card"]').each(($card) => {
        cy.wrap($card).should('have.attr', 'data-category', 'appetizers');
      });
    });

    it('should show all items when "All" category is selected', () => {
      cy.filterMenuByCategory('appetizers');
      cy.get('[data-testid="menu-item-card"]').should('have.length.lessThan', 20);
      
      cy.filterMenuByCategory('all');
      
      cy.get('[data-testid="menu-item-card"]').should('have.length.greaterThan', 10);
      cy.get('[data-testid="category-all"]').should('have.class', 'active');
    });

    it('should update item count for each category', () => {
      cy.get('[data-testid="category-appetizers"]').within(() => {
        cy.get('[data-testid="category-count"]')
          .should('be.visible')
          .and('contain', '(');
      });
    });
  });

  describe('Dietary Filtering', () => {
    beforeEach(() => {
      cy.visitMenuPage();
    });

    it('should filter by vegetarian options', () => {
      cy.filterMenuByDietary('vegetarian');
      
      cy.get('[data-testid="menu-item-card"]').should('have.length.greaterThan', 0);
      cy.get('[data-testid="filter-vegetarian"]').should('have.class', 'active');
      
      // Verify all items are vegetarian
      cy.get('[data-testid="menu-item-card"]').each(($card) => {
        cy.wrap($card).find('[data-testid="tag-vegetarian"]').should('be.visible');
      });
    });

    it('should filter by vegan options', () => {
      cy.filterMenuByDietary('vegan');
      
      cy.get('[data-testid="menu-item-card"]').should('have.length.greaterThan', 0);
      cy.get('[data-testid="menu-item-card"]').each(($card) => {
        cy.wrap($card).find('[data-testid="tag-vegan"]').should('be.visible');
      });
    });

    it('should filter by gluten-free options', () => {
      cy.filterMenuByDietary('gluten-free');
      
      cy.get('[data-testid="menu-item-card"]').should('have.length.greaterThan', 0);
      cy.get('[data-testid="menu-item-card"]').each(($card) => {
        cy.wrap($card).find('[data-testid="tag-gluten-free"]').should('be.visible');
      });
    });

    it('should combine multiple dietary filters', () => {
      cy.filterMenuByDietary('vegetarian');
      cy.filterMenuByDietary('gluten-free');
      
      cy.get('[data-testid="menu-item-card"]').each(($card) => {
        cy.wrap($card).find('[data-testid="tag-vegetarian"]').should('be.visible');
        cy.wrap($card).find('[data-testid="tag-gluten-free"]').should('be.visible');
      });
    });

    it('should clear dietary filters', () => {
      cy.filterMenuByDietary('vegetarian');
      cy.get('[data-testid="menu-item-card"]').should('have.length.lessThan', 20);
      
      cy.clearAllFilters();
      
      cy.get('[data-testid="menu-item-card"]').should('have.length.greaterThan', 10);
      cy.get('[data-testid="filter-vegetarian"]').should('not.have.class', 'active');
    });
  });

  describe('Sorting Options', () => {
    beforeEach(() => {
      cy.visitMenuPage();
    });

    it('should sort by price (low to high)', () => {
      cy.sortMenuBy('price-asc');
      
      cy.get('[data-testid="menu-item-card"]').then(($cards) => {
        const prices = [];
        $cards.each((index, card) => {
          const priceText = Cypress.$(card).find('[data-testid="item-price"]').text();
          const price = parseFloat(priceText.replace('$', ''));
          prices.push(price);
        });
        
        // Verify prices are in ascending order
        for (let i = 1; i < prices.length; i++) {
          expect(prices[i]).to.be.at.least(prices[i - 1]);
        }
      });
    });

    it('should sort by price (high to low)', () => {
      cy.sortMenuBy('price-desc');
      
      cy.get('[data-testid="menu-item-card"]').then(($cards) => {
        const prices = [];
        $cards.each((index, card) => {
          const priceText = Cypress.$(card).find('[data-testid="item-price"]').text();
          const price = parseFloat(priceText.replace('$', ''));
          prices.push(price);
        });
        
        // Verify prices are in descending order
        for (let i = 1; i < prices.length; i++) {
          expect(prices[i]).to.be.at.most(prices[i - 1]);
        }
      });
    });

    it('should sort by popularity', () => {
      cy.sortMenuBy('popularity');
      
      cy.get('[data-testid="sort-dropdown"]').should('contain', 'Most Popular');
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="popular-badge"]').should('be.visible');
      });
    });

    it('should sort alphabetically', () => {
      cy.sortMenuBy('name-asc');
      
      cy.get('[data-testid="menu-item-card"]').then(($cards) => {
        const names = [];
        $cards.each((index, card) => {
          const name = Cypress.$(card).find('[data-testid="item-name"]').text();
          names.push(name);
        });
        
        // Verify names are in alphabetical order
        const sortedNames = [...names].sort();
        expect(names).to.deep.equal(sortedNames);
      });
    });
  });

  describe('Add to Cart Functionality', () => {
    beforeEach(() => {
      cy.visitMenuPage();
    });

    it('should add simple item to cart', () => {
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="add-to-cart-button"]').click();
      });
      
      cy.wait('@addToCart');
      
      cy.checkNotification('Item added to cart', 'success');
      cy.get('[data-testid="cart-item-count"]').should('contain', '1');
    });

    it('should open customization modal for customizable items', () => {
      cy.fixture('menu/menu-items').then((menuData) => {
        const customizableItem = menuData.menuItems.find(item => item.customizations && item.customizations.length > 0);
        
        if (customizableItem) {
          cy.get(`[data-testid="menu-item-${customizableItem.id}"]`).within(() => {
            cy.get('[data-testid="add-to-cart-button"]').click();
          });
          
          cy.get('[data-testid="customization-modal"]').should('be.visible');
          cy.get('[data-testid="modal-title"]').should('contain', customizableItem.name);
          cy.get('[data-testid="customization-options"]').should('be.visible');
        }
      });
    });

    it('should handle quantity selection', () => {
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="quantity-selector"]').should('be.visible');
        cy.get('[data-testid="quantity-increase"]').click().click();
        cy.get('[data-testid="quantity-value"]').should('contain', '3');
        
        cy.get('[data-testid="add-to-cart-button"]').click();
      });
      
      cy.wait('@addToCart');
      
      cy.get('@addToCart').should((interception) => {
        expect(interception.request.body).to.have.property('quantity', 3);
      });
    });

    it('should show loading state when adding to cart', () => {
      cy.intercept('POST', '/api/cart/add', (req) => {
        req.reply((res) => {
          res.delay(1000);
          res.send({ statusCode: 200 });
        });
      }).as('slowAddToCart');
      
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="add-to-cart-button"]').click();
        
        cy.get('[data-testid="add-to-cart-button"]')
          .should('be.disabled')
          .and('contain', 'Adding...');
        
        cy.get('[data-testid="loading-spinner"]').should('be.visible');
      });
      
      cy.wait('@slowAddToCart');
    });

    it('should handle add to cart errors', () => {
      cy.intercept('POST', '/api/cart/add', {
        statusCode: 400,
        body: { error: 'Item out of stock' }
      }).as('addToCartError');
      
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="add-to-cart-button"]').click();
      });
      
      cy.wait('@addToCartError');
      
      cy.checkNotification('Item out of stock', 'error');
    });
  });

  describe('Item Details Modal', () => {
    beforeEach(() => {
      cy.visitMenuPage();
    });

    it('should open item details modal', () => {
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="item-image"]').click();
      });
      
      cy.get('[data-testid="item-details-modal"]').should('be.visible');
      cy.get('[data-testid="modal-item-name"]').should('be.visible');
      cy.get('[data-testid="modal-item-description"]').should('be.visible');
      cy.get('[data-testid="modal-item-price"]').should('be.visible');
      cy.get('[data-testid="modal-item-image"]').should('be.visible');
    });

    it('should show detailed nutritional information', () => {
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="view-details"]').click();
      });
      
      cy.get('[data-testid="item-details-modal"]').within(() => {
        cy.get('[data-testid="nutrition-info"]').should('be.visible');
        cy.get('[data-testid="calories"]').should('be.visible');
        cy.get('[data-testid="allergen-details"]').should('be.visible');
        cy.get('[data-testid="ingredients-list"]').should('be.visible');
      });
    });

    it('should allow adding to cart from modal', () => {
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="view-details"]').click();
      });
      
      cy.get('[data-testid="item-details-modal"]').within(() => {
        cy.get('[data-testid="modal-add-to-cart"]').click();
      });
      
      cy.wait('@addToCart');
      cy.checkNotification('Item added to cart', 'success');
    });

    it('should close modal with close button', () => {
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="view-details"]').click();
      });
      
      cy.get('[data-testid="item-details-modal"]').should('be.visible');
      
      cy.get('[data-testid="modal-close"]').click();
      
      cy.get('[data-testid="item-details-modal"]').should('not.exist');
    });

    it('should close modal with escape key', () => {
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="view-details"]').click();
      });
      
      cy.get('[data-testid="item-details-modal"]').should('be.visible');
      
      cy.get('body').type('{esc}');
      
      cy.get('[data-testid="item-details-modal"]').should('not.exist');
    });
  });

  describe('Cart Summary', () => {
    beforeEach(() => {
      cy.visitMenuPage();
    });

    it('should update cart summary when items are added', () => {
      cy.get('[data-testid="cart-item-count"]').should('contain', '0');
      cy.get('[data-testid="cart-total"]').should('contain', '$0.00');
      
      cy.addItemToCart(1, 2); // Add 2 of first item
      
      cy.get('[data-testid="cart-item-count"]').should('contain', '2');
      cy.get('[data-testid="cart-total"]').should('not.contain', '$0.00');
    });

    it('should navigate to cart page', () => {
      cy.addItemToCart(1, 1);
      
      cy.get('[data-testid="view-cart-button"]').click();
      
      cy.url().should('include', '/cart');
    });

    it('should show mini cart preview on hover', () => {
      cy.addItemToCart(1, 1);
      
      cy.get('[data-testid="cart-summary"]').trigger('mouseover');
      
      cy.get('[data-testid="mini-cart"]').should('be.visible');
      cy.get('[data-testid="mini-cart-items"]').should('be.visible');
      cy.get('[data-testid="mini-cart-total"]').should('be.visible');
    });
  });

  describe('Loading States', () => {
    it('should show loading state while fetching menu', () => {
      cy.intercept('GET', '/api/menu', (req) => {
        req.reply((res) => {
          res.delay(2000);
          res.send({ fixture: 'menu/menu-items.json' });
        });
      }).as('slowMenu');
      
      cy.visitMenuPage();
      
      cy.get('[data-testid="menu-loading"]').should('be.visible');
      cy.get('[data-testid="loading-skeleton"]').should('be.visible');
      
      cy.wait('@slowMenu');
      
      cy.get('[data-testid="menu-loading"]').should('not.exist');
      cy.get('[data-testid="menu-items-grid"]').should('be.visible');
    });

    it('should show loading state for search', () => {
      cy.visitMenuPage();
      
      cy.intercept('GET', '/api/menu?search=*', (req) => {
        req.reply((res) => {
          res.delay(1000);
          res.send({ fixture: 'menu/menu-items.json' });
        });
      }).as('searchMenu');
      
      cy.get('[data-testid="search-bar"]').type('pizza');
      
      cy.get('[data-testid="search-loading"]').should('be.visible');
      
      cy.wait('@searchMenu');
      
      cy.get('[data-testid="search-loading"]').should('not.exist');
    });
  });

  describe('Error Handling', () => {
    it('should handle menu loading error', () => {
      cy.intercept('GET', '/api/menu', {
        statusCode: 500,
        body: { error: 'Internal server error' }
      }).as('menuError');
      
      cy.visitMenuPage();
      
      cy.wait('@menuError');
      
      cy.get('[data-testid="error-message"]')
        .should('be.visible')
        .and('contain', 'Unable to load menu');
      
      cy.get('[data-testid="retry-button"]').should('be.visible');
    });

    it('should retry loading menu on error', () => {
      cy.intercept('GET', '/api/menu', {
        statusCode: 500,
        body: { error: 'Internal server error' }
      }).as('menuError');
      
      cy.visitMenuPage();
      cy.wait('@menuError');
      
      // Set up successful retry
      cy.intercept('GET', '/api/menu', { fixture: 'menu/menu-items.json' }).as('menuRetry');
      
      cy.get('[data-testid="retry-button"]').click();
      
      cy.wait('@menuRetry');
      
      cy.get('[data-testid="menu-items-grid"]').should('be.visible');
      cy.get('[data-testid="error-message"]').should('not.exist');
    });

    it('should handle network errors gracefully', () => {
      cy.intercept('GET', '/api/menu', { forceNetworkError: true }).as('networkError');
      
      cy.visitMenuPage();
      
      cy.wait('@networkError');
      
      cy.get('[data-testid="error-message"]')
        .should('be.visible')
        .and('contain', 'Network error');
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      cy.visitMenuPage();
    });

    it('should be accessible', () => {
      cy.checkAccessibility();
    });

    it('should support keyboard navigation', () => {
      cy.checkKeyboardNavigation();
      
      // Test tab order through menu items
      cy.get('[data-testid="menu-item-card"]').first().focus();
      cy.focused().should('have.attr', 'data-testid', 'menu-item-card');
      
      cy.focused().find('[data-testid="add-to-cart-button"]').should('be.visible');
    });

    it('should have proper ARIA labels', () => {
      cy.get('[data-testid="search-bar"]')
        .should('have.attr', 'aria-label', 'Search menu items');
      
      cy.get('[data-testid="menu-items-grid"]')
        .should('have.attr', 'role', 'grid');
      
      cy.get('[data-testid="menu-item-card"]').first()
        .should('have.attr', 'role', 'gridcell');
    });

    it('should announce filter changes to screen readers', () => {
      cy.filterMenuByCategory('appetizers');
      
      cy.get('[data-testid="filter-announcement"]')
        .should('have.attr', 'aria-live', 'polite')
        .and('contain', 'Showing appetizers');
    });
  });

  describe('Responsive Design', () => {
    it('should work on different screen sizes', () => {
      cy.testResponsiveDesign();
      
      // Test mobile layout
      cy.viewport(375, 667);
      cy.visitMenuPage();
      
      cy.get('[data-testid="menu-items-grid"]')
        .should('have.css', 'grid-template-columns')
        .and('match', /repeat\(1,/);
      
      // Test tablet layout
      cy.viewport(768, 1024);
      cy.get('[data-testid="menu-items-grid"]')
        .should('have.css', 'grid-template-columns')
        .and('match', /repeat\(2,/);
      
      // Test desktop layout
      cy.viewport(1280, 720);
      cy.get('[data-testid="menu-items-grid"]')
        .should('have.css', 'grid-template-columns')
        .and('match', /repeat\(3,/);
    });

    it('should show mobile-optimized filters', () => {
      cy.viewport(375, 667);
      cy.visitMenuPage();
      
      cy.get('[data-testid="mobile-filter-button"]').should('be.visible');
      cy.get('[data-testid="mobile-filter-button"]').click();
      
      cy.get('[data-testid="mobile-filter-drawer"]').should('be.visible');
    });
  });

  describe('Performance', () => {
    it('should load menu page quickly', () => {
      cy.visit('/menu');
      cy.measurePageLoadTime();
    });

    it('should optimize images', () => {
      cy.visitMenuPage();
      cy.checkImageOptimization();
    });

    it('should implement virtual scrolling for large menus', () => {
      cy.visitMenuPage();
      
      // Check that not all items are rendered at once
      cy.get('[data-testid="menu-item-card"]').should('have.length.lessThan', 50);
      
      // Scroll down and check that more items are loaded
      cy.scrollTo('bottom');
      cy.get('[data-testid="menu-item-card"]').should('have.length.greaterThan', 20);
    });
  });

  describe('Favorites', () => {
    beforeEach(() => {
      cy.visitMenuPage();
    });

    it('should add item to favorites', () => {
      cy.intercept('POST', '/api/favorites/add', { statusCode: 200 }).as('addFavorite');
      
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="favorite-button"]').click();
      });
      
      cy.wait('@addFavorite');
      
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="favorite-button"]').should('have.class', 'favorited');
      });
      
      cy.checkNotification('Added to favorites', 'success');
    });

    it('should remove item from favorites', () => {
      cy.intercept('DELETE', '/api/favorites/remove', { statusCode: 200 }).as('removeFavorite');
      
      // First add to favorites
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="favorite-button"]').click();
      });
      
      // Then remove from favorites
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="favorite-button"]').click();
      });
      
      cy.wait('@removeFavorite');
      
      cy.get('[data-testid="menu-item-card"]').first().within(() => {
        cy.get('[data-testid="favorite-button"]').should('not.have.class', 'favorited');
      });
    });

    it('should filter by favorites', () => {
      cy.get('[data-testid="show-favorites-only"]').click();
      
      cy.get('[data-testid="menu-item-card"]').each(($card) => {
        cy.wrap($card).find('[data-testid="favorite-button"]')
          .should('have.class', 'favorited');
      });
    });
  });
});