'use client'

import React, { useState, useEffect } from 'react'
import { Alert<PERSON>riangle, User, Shield, Key } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import toast from 'react-hot-toast'
import AdminSetup from './AdminSetup'

interface BranchAuthGuardProps {
  children: React.ReactNode
  onAuthStateChange?: (isAuthenticated: boolean, hasPermissions: boolean) => void
}

interface AuthState {
  isAuthenticated: boolean
  hasPermissions: boolean
  userEmail?: string
  userRole?: string
  loading: boolean
  error?: string
}

export default function BranchAuthGuard({ children, onAuthStateChange }: BranchAuthGuardProps) {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    hasPermissions: false,
    loading: true
  })

  const [showDevLogin, setShowDevLogin] = useState(false)
  const [devCredentials, setDevCredentials] = useState({
    email: '<EMAIL>',
    password: 'dev123456'
  })

  useEffect(() => {
    checkAuthState()
  }, [])

  useEffect(() => {
    onAuthStateChange?.(authState.isAuthenticated, authState.hasPermissions)
  }, [authState.isAuthenticated, authState.hasPermissions, onAuthStateChange])

  const checkAuthState = async () => {
    try {
      setAuthState(prev => ({ ...prev, loading: true }))

      // Check current session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      
      if (sessionError) {
        throw sessionError
      }

      if (!session) {
        setAuthState({
          isAuthenticated: false,
          hasPermissions: false,
          loading: false,
          error: 'Not authenticated'
        })
        return
      }

      // Check user permissions in staff table
      const { data: staffData, error: staffError } = await supabase
        .from('staff')
        .select(`
          id,
          email,
          role_id,
          is_active,
          can_login_admin,
          roles (
            name,
            display_name,
            level
          )
        `)
        .eq('email', session.user.email)
        .eq('is_active', true)
        .single()

      let hasPermissions = false
      let userRole = 'No role assigned'

      if (staffData && staffData.can_login_admin && staffData.roles) {
        const role = Array.isArray(staffData.roles) ? staffData.roles[0] : staffData.roles
        hasPermissions = role && ['admin', 'manager'].includes(role.name)
        userRole = role ? (role.display_name || role.name) : 'No role assigned'
      } else if (staffError && staffError.code === 'PGRST116') {
        // No staff record found, check user_profiles as fallback
        const { data: profileData, error: profileError } = await supabase
          .from('user_profiles')
          .select('role, is_active')
          .eq('email', session.user.email)
          .eq('is_active', true)
          .single()

        if (!profileError && profileData?.role === 'admin') {
          hasPermissions = true
          userRole = 'Administrator'
        }
      }

      setAuthState({
        isAuthenticated: true,
        hasPermissions,
        userEmail: session.user.email,
        userRole,
        loading: false,
        error: staffError && staffError.code !== 'PGRST116' ? 'Failed to load user permissions' : undefined
      })

    } catch (error: any) {
      console.error('Auth check error:', error)
      setAuthState({
        isAuthenticated: false,
        hasPermissions: false,
        loading: false,
        error: error.message
      })
    }
  }

  const handleDevLogin = async () => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: devCredentials.email,
        password: devCredentials.password
      })

      if (error) {
        toast.error(`Login failed: ${error.message}`)
        return
      }

      toast.success('Logged in successfully!')
      setShowDevLogin(false)
      checkAuthState()
    } catch (error: any) {
      toast.error(`Login error: ${error.message}`)
    }
  }

  const handleLogout = async () => {
    try {
      await supabase.auth.signOut()
      toast.success('Logged out successfully')
      checkAuthState()
    } catch (error: any) {
      toast.error(`Logout error: ${error.message}`)
    }
  }

  if (authState.loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-white/70">Checking authentication...</span>
      </div>
    )
  }

  if (!authState.isAuthenticated) {
    return (
      <div className="glass-container glass-primary p-8 rounded-xl max-w-md mx-auto">
        <div className="text-center">
          <User className="h-12 w-12 text-blue-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold glass-text-primary mb-2">Authentication Required</h3>
          <p className="glass-text-secondary mb-6">
            You need to be logged in to manage branches.
          </p>
          
          {process.env.NODE_ENV === 'development' && (
            <div className="space-y-4">
              <button
                onClick={() => setShowDevLogin(!showDevLogin)}
                className="glass-button glass-interactive w-full"
              >
                <Key className="h-4 w-4 mr-2" />
                Development Login
              </button>
              
              {showDevLogin && (
                <div className="space-y-3 p-4 bg-white/5 rounded-lg">
                  <input
                    type="email"
                    placeholder="Email"
                    value={devCredentials.email}
                    onChange={(e) => setDevCredentials(prev => ({ ...prev, email: e.target.value }))}
                    className="glass-input w-full"
                  />
                  <input
                    type="password"
                    placeholder="Password"
                    value={devCredentials.password}
                    onChange={(e) => setDevCredentials(prev => ({ ...prev, password: e.target.value }))}
                    className="glass-input w-full"
                  />
                  <button
                    onClick={handleDevLogin}
                    className="glass-button glass-interactive w-full"
                  >
                    Login
                  </button>
                </div>
              )}
            </div>
          )}
          
          <div className="mt-6 p-4 bg-blue-500/10 rounded-lg">
            <p className="text-sm glass-text-secondary">
              For production, please use the main login page or contact your administrator.
            </p>
          </div>
        </div>
      </div>
    )
  }

  if (!authState.hasPermissions) {
    return (
      <div className="space-y-6">
        {/* Show AdminSetup component for automatic privilege granting */}
        <AdminSetup />
        
        {/* Alternative manual options */}
        <div className="glass-container glass-primary p-8 rounded-xl max-w-md mx-auto">
          <div className="text-center">
            <Shield className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold glass-text-primary mb-2">Alternative Options</h3>
            
            <div className="space-y-2 text-sm glass-text-secondary mb-4">
              <p><strong>Current user:</strong> {authState.userEmail}</p>
              <p><strong>Current role:</strong> {authState.userRole || 'No role assigned'}</p>
            </div>
            
            {/* Development bypass button */}
            {process.env.NODE_ENV === 'development' && (
              <button
                onClick={() => {
                  setAuthState(prev => ({ ...prev, hasPermissions: true, userRole: 'Development Admin' }))
                  toast.success('Development access granted!')
                }}
                className="glass-button glass-interactive mt-4 bg-green-500/20 hover:bg-green-500/30 w-full"
              >
                <Key className="h-4 w-4 mr-2" />
                Grant Development Access
              </button>
            )}
            
            <button
              onClick={handleLogout}
              className="glass-button glass-interactive mt-6 w-full"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div>
      {/* Auth status indicator */}
      <div className="mb-4 p-3 bg-green-500/10 rounded-lg flex items-center justify-between">
        <div className="flex items-center">
          <Shield className="h-4 w-4 text-green-400 mr-2" />
          <span className="text-sm glass-text-primary">
            Authenticated as {authState.userEmail} ({authState.userRole})
          </span>
        </div>
        <button
          onClick={handleLogout}
          className="text-sm glass-text-secondary hover:glass-text-primary transition-colors"
        >
          Logout
        </button>
      </div>
      
      {children}
    </div>
  )
} 