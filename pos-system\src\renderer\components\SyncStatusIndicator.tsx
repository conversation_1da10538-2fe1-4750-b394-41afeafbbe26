import React, { useState, useEffect } from 'react';

interface SyncStatus {
  isOnline: boolean;
  lastSync: string | null;
  pendingItems: number;
  syncInProgress: boolean;
  error: string | null;
  terminalHealth: number;
  settingsVersion: number;
  menuVersion: number;
}

interface SyncStatusIndicatorProps {
  className?: string;
  showDetails?: boolean;
}

export const SyncStatusIndicator: React.FC<SyncStatusIndicatorProps> = ({
  className = '',
  showDetails = false
}) => {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: false,
    lastSync: null,
    pendingItems: 0,
    syncInProgress: false,
    error: null,
    terminalHealth: 0,
    settingsVersion: 0,
    menuVersion: 0
  });
  const [showDetailPanel, setShowDetailPanel] = useState(false);

  useEffect(() => {
    // Get initial sync status
    const loadSyncStatus = async () => {
      try {
        if (window.electronAPI?.getSyncStatus) {
          const status = await window.electronAPI.getSyncStatus();
          setSyncStatus(status);
        }
      } catch (error) {
        console.error('Failed to load sync status:', error);
      }
    };

    loadSyncStatus();

    // Listen for sync status updates
    const handleSyncStatusUpdate = (status: SyncStatus) => {
      setSyncStatus(status);
    };

    const handleNetworkStatus = ({ isOnline }: { isOnline: boolean }) => {
      setSyncStatus(prev => ({ ...prev, isOnline }));
    };

    if (window.electronAPI) {
      window.electronAPI.onSyncStatus?.(handleSyncStatusUpdate);
      window.electronAPI.onNetworkStatus?.(handleNetworkStatus);
    }

    // Refresh status every 30 seconds
    const interval = setInterval(loadSyncStatus, 30000);

    return () => {
      clearInterval(interval);
      if (window.electronAPI) {
        window.electronAPI.removeSyncStatusListener?.();
        window.electronAPI.removeNetworkStatusListener?.();
      }
    };
  }, []);

  const getStatusColor = () => {
    if (!syncStatus.isOnline) return 'bg-red-500';
    if (syncStatus.syncInProgress) return 'bg-yellow-500';
    if (syncStatus.error) return 'bg-orange-500';
    if (syncStatus.pendingItems > 0) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getStatusText = () => {
    if (!syncStatus.isOnline) return 'Offline';
    if (syncStatus.syncInProgress) return 'Syncing...';
    if (syncStatus.error) return 'Sync Error';
    if (syncStatus.pendingItems > 0) return `${syncStatus.pendingItems} Pending`;
    return 'Synced';
  };

  const getHealthColor = () => {
    if (syncStatus.terminalHealth >= 0.8) return 'text-green-600';
    if (syncStatus.terminalHealth >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatLastSync = () => {
    if (!syncStatus.lastSync) return 'Never';
    const date = new Date(syncStatus.lastSync);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleDateString();
  };

  const handleForceSync = async () => {
    try {
      if (window.electronAPI?.forceSync) {
        await window.electronAPI.forceSync();
      }
    } catch (error) {
      console.error('Failed to force sync:', error);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Status Indicator */}
      <div 
        className="flex items-center gap-2 cursor-pointer"
        onClick={() => showDetails && setShowDetailPanel(!showDetailPanel)}
      >
        <div className={`w-3 h-3 rounded-full ${getStatusColor()} ${syncStatus.syncInProgress ? 'animate-pulse' : ''}`} />
        <span className="text-sm font-medium text-gray-700">
          {getStatusText()}
        </span>
        {showDetails && (
          <svg 
            className={`w-4 h-4 text-gray-400 transition-transform ${showDetailPanel ? 'rotate-180' : ''}`}
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </div>

      {/* Detail Panel */}
      {showDetailPanel && showDetails && (
        <div className="absolute top-full left-0 mt-2 bg-white shadow-lg rounded-lg border p-4 min-w-80 z-50">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-gray-900">Sync Status</h3>
              <button 
                onClick={() => setShowDetailPanel(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Connection:</span>
                <span className={`ml-2 font-medium ${syncStatus.isOnline ? 'text-green-600' : 'text-red-600'}`}>
                  {syncStatus.isOnline ? 'Online' : 'Offline'}
                </span>
              </div>
              
              <div>
                <span className="text-gray-500">Last Sync:</span>
                <span className="ml-2 font-medium text-gray-700">
                  {formatLastSync()}
                </span>
              </div>
              
              <div>
                <span className="text-gray-500">Pending:</span>
                <span className="ml-2 font-medium text-gray-700">
                  {syncStatus.pendingItems}
                </span>
              </div>
              
              <div>
                <span className="text-gray-500">Health:</span>
                <span className={`ml-2 font-medium ${getHealthColor()}`}>
                  {(syncStatus.terminalHealth * 100).toFixed(0)}%
                </span>
              </div>
              
              <div>
                <span className="text-gray-500">Settings:</span>
                <span className="ml-2 font-medium text-gray-700">
                  v{syncStatus.settingsVersion}
                </span>
              </div>
              
              <div>
                <span className="text-gray-500">Menu:</span>
                <span className="ml-2 font-medium text-gray-700">
                  v{syncStatus.menuVersion}
                </span>
              </div>
            </div>
            
            {syncStatus.error && (
              <div className="p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                <strong>Error:</strong> {syncStatus.error}
              </div>
            )}
            
            <div className="flex gap-2 pt-2 border-t">
              <button 
                onClick={handleForceSync}
                disabled={syncStatus.syncInProgress || !syncStatus.isOnline}
                className="flex-1 bg-blue-600 text-white py-2 px-3 rounded text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {syncStatus.syncInProgress ? 'Syncing...' : 'Force Sync'}
              </button>
              
              <button 
                onClick={() => window.electronAPI?.openSyncLogs?.()}
                className="bg-gray-200 text-gray-700 py-2 px-3 rounded text-sm font-medium hover:bg-gray-300"
              >
                View Logs
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
