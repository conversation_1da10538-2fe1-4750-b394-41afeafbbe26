-- Delivery Zones Table Migration
-- Creates the delivery_zones table and related functionality

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- =============================================
-- DELIVERY ZONES TABLE
-- =============================================
CREATE TABLE IF NOT EXISTS delivery_zones (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  coordinates JSONB NOT NULL, -- Array of lat/lng points defining the polygon
  delivery_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  minimum_order_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  estimated_delivery_time_min INTEGER NOT NULL DEFAULT 30, -- minutes
  estimated_delivery_time_max INTEGER NOT NULL DEFAULT 60, -- minutes
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  priority INTEGER NOT NULL DEFAULT 1, -- Higher priority zones are checked first
  color VARCHAR(7) DEFAULT '#3B82F6', -- Hex color for map display
  branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID, -- Reference to auth.users, no foreign key constraint
  updated_by UUID  -- Reference to auth.users, no foreign key constraint
);

-- Create indexes for delivery_zones
CREATE INDEX IF NOT EXISTS idx_delivery_zones_branch ON delivery_zones (branch_id);
CREATE INDEX IF NOT EXISTS idx_delivery_zones_active ON delivery_zones (is_active);
CREATE INDEX IF NOT EXISTS idx_delivery_zones_priority ON delivery_zones (priority DESC);
CREATE INDEX IF NOT EXISTS idx_delivery_zones_created_at ON delivery_zones (created_at);

-- Create unique constraint for zone names per branch
ALTER TABLE delivery_zones ADD CONSTRAINT delivery_zones_name_branch_unique UNIQUE(name, branch_id);

-- =============================================
-- ROW LEVEL SECURITY
-- =============================================
ALTER TABLE delivery_zones ENABLE ROW LEVEL SECURITY;

-- Policy for authenticated users to view delivery zones
CREATE POLICY "Users can view delivery zones" ON delivery_zones
  FOR SELECT
  USING (auth.role() = 'authenticated');

-- Policy for admin users to manage delivery zones
CREATE POLICY "Admin users can manage delivery zones" ON delivery_zones
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.raw_user_meta_data->>'role' IN ('admin', 'manager')
    )
  );

-- =============================================
-- FUNCTIONS
-- =============================================

-- Function to check if a point is within a delivery zone
CREATE OR REPLACE FUNCTION is_point_in_delivery_zone(
  zone_coordinates JSONB,
  check_lat DECIMAL,
  check_lng DECIMAL
) RETURNS BOOLEAN AS $$
DECLARE
  point_count INTEGER;
  i INTEGER;
  j INTEGER;
  inside BOOLEAN := FALSE;
  xi DECIMAL;
  yi DECIMAL;
  xj DECIMAL;
  yj DECIMAL;
BEGIN
  -- Get the number of points in the polygon
  point_count := jsonb_array_length(zone_coordinates);
  
  -- Need at least 3 points for a polygon
  IF point_count < 3 THEN
    RETURN FALSE;
  END IF;
  
  -- Ray casting algorithm for point-in-polygon test
  j := point_count - 1;
  
  FOR i IN 0..(point_count - 1) LOOP
    xi := (zone_coordinates->i->>'lat')::DECIMAL;
    yi := (zone_coordinates->i->>'lng')::DECIMAL;
    xj := (zone_coordinates->j->>'lat')::DECIMAL;
    yj := (zone_coordinates->j->>'lng')::DECIMAL;
    
    IF ((yi > check_lng) != (yj > check_lng)) AND 
       (check_lat < (xj - xi) * (check_lng - yi) / (yj - yi) + xi) THEN
      inside := NOT inside;
    END IF;
    
    j := i;
  END LOOP;
  
  RETURN inside;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to find delivery zone for a given address
CREATE OR REPLACE FUNCTION find_delivery_zone_for_address(
  address_lat DECIMAL,
  address_lng DECIMAL,
  target_branch_id UUID DEFAULT NULL
) RETURNS TABLE(
  zone_id UUID,
  zone_name VARCHAR(100),
  delivery_fee DECIMAL(10,2),
  minimum_order_amount DECIMAL(10,2),
  estimated_delivery_time_min INTEGER,
  estimated_delivery_time_max INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    dz.id,
    dz.name,
    dz.delivery_fee,
    dz.minimum_order_amount,
    dz.estimated_delivery_time_min,
    dz.estimated_delivery_time_max
  FROM delivery_zones dz
  WHERE dz.is_active = TRUE
    AND (target_branch_id IS NULL OR dz.branch_id = target_branch_id)
    AND is_point_in_delivery_zone(dz.coordinates, address_lat, address_lng)
  ORDER BY dz.priority DESC, dz.created_at ASC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql STABLE;

-- =============================================
-- TRIGGERS
-- =============================================

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_delivery_zones_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_delivery_zones_updated_at
  BEFORE UPDATE ON delivery_zones
  FOR EACH ROW
  EXECUTE FUNCTION update_delivery_zones_updated_at();

-- =============================================
-- REALTIME SUBSCRIPTIONS
-- =============================================
ALTER PUBLICATION supabase_realtime ADD TABLE delivery_zones;

-- =============================================
-- SAMPLE DATA (Optional - for development)
-- =============================================
-- Uncomment the following to insert sample delivery zones

/*
-- Sample delivery zone for testing
INSERT INTO delivery_zones (
  name,
  description,
  coordinates,
  delivery_fee,
  minimum_order_amount,
  estimated_delivery_time_min,
  estimated_delivery_time_max,
  color,
  branch_id
) VALUES (
  'Downtown Area',
  'Central downtown delivery zone with fast delivery',
  '[
    {"lat": 40.7589, "lng": -73.9851},
    {"lat": 40.7614, "lng": -73.9776},
    {"lat": 40.7505, "lng": -73.9934},
    {"lat": 40.7489, "lng": -73.9857}
  ]'::jsonb,
  2.99,
  15.00,
  20,
  35,
  '#3B82F6',
  (SELECT id FROM branches LIMIT 1)
);
*/