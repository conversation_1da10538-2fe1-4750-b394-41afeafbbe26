'use client'

import React, { useState } from 'react'
import { X, Save } from 'lucide-react'
import { supabase } from '@/lib/supabase'
import toast from 'react-hot-toast'

interface Branch {
  id: string
  name: string
  code: string
  display_name: string
  description: string
  address_line1: string
  address_line2?: string
  city: string
  state: string
  postal_code: string
  country: string
  phone?: string
  email?: string
  website?: string
  status: 'active' | 'inactive' | 'temporarily_closed' | 'coming_soon' | 'permanently_closed'
  has_delivery: boolean
  has_pickup: boolean
  has_dine_in: boolean
  has_drive_through: boolean
  seating_capacity: number
  parking_spaces: number
  delivery_radius: number
  delivery_fee: number
  minimum_order_amount: number
  timezone: string
}

interface EditBranchModalProps {
  branch: Branch
  onClose: () => void
  onBranchUpdated: () => void
}

const timezones = [
  'America/New_York',
  'America/Chicago',
  'America/Denver',
  'America/Los_Angeles',
  'America/Phoenix',
  'America/Anchorage',
  'Pacific/Honolulu'
]

export default function EditBranchModal({ branch, onClose, onBranchUpdated }: EditBranchModalProps) {
  const [formData, setFormData] = useState<Branch>(branch)
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleInputChange = (field: keyof Branch, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) newErrors.name = 'Branch name is required'
    if (!formData.display_name.trim()) newErrors.display_name = 'Display name is required'
    if (!formData.address_line1.trim()) newErrors.address_line1 = 'Address is required'
    if (!formData.city.trim()) newErrors.city = 'City is required'
    if (!formData.state.trim()) newErrors.state = 'State is required'
    if (!formData.postal_code.trim()) newErrors.postal_code = 'Postal code is required'
    
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format'
    }
    
    if (formData.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
      newErrors.phone = 'Invalid phone format'
    }

    if (formData.delivery_radius <= 0) newErrors.delivery_radius = 'Delivery radius must be greater than 0'
    if (formData.delivery_fee < 0) newErrors.delivery_fee = 'Delivery fee cannot be negative'
    if (formData.minimum_order_amount < 0) newErrors.minimum_order_amount = 'Minimum order amount cannot be negative'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    try {
      setLoading(true)

      const { error } = await supabase
        .from('branches')
        .update({
          ...formData,
          is_active: formData.status === 'active',
          updated_at: new Date().toISOString()
        })
        .eq('id', branch.id)

      if (error) throw error

      toast.success('Branch updated successfully!')
      onBranchUpdated()
    } catch (error) {
      console.error('Error updating branch:', error)
      toast.error('Failed to update branch')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="glass-modal-backdrop">
      <div className="glass-container glass-primary w-full max-w-4xl max-h-[90vh] overflow-hidden fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-white/20">
          <div>
            <h2 className="text-2xl font-bold glass-text-primary">Edit Branch</h2>
            <p className="glass-text-secondary mt-1">Update branch information and settings</p>
          </div>
          <button
            onClick={onClose}
            className="glass-interactive p-2 rounded-lg transition-colors"
          >
            <X className="h-6 w-6 glass-text-secondary" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 overflow-y-auto max-h-[75vh] scrollbar-glassmorphism">
          <div className="space-y-8">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-semibold glass-text-primary mb-4">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium glass-text-primary mb-2">
                    Branch Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={`glass-input glass-text-primary placeholder:glass-text-muted ${
                      errors.name ? 'glass-error' : ''
                    }`}
                    placeholder="e.g., Downtown Branch"
                  />
                  {errors.name && <p className="mt-1 text-sm text-red-400">{errors.name}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium glass-text-primary mb-2">
                    Display Name *
                  </label>
                  <input
                    type="text"
                    value={formData.display_name}
                    onChange={(e) => handleInputChange('display_name', e.target.value)}
                    className={`glass-input glass-text-primary placeholder:glass-text-muted ${
                      errors.display_name ? 'glass-error' : ''
                    }`}
                    placeholder="e.g., Delicious Bites - Downtown"
                  />
                  {errors.display_name && <p className="mt-1 text-sm text-red-400">{errors.display_name}</p>}
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={3}
                  className="glass-input glass-text-primary placeholder:glass-text-muted"
                  placeholder="Brief description of this branch location..."
                />
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="glass-input glass-text-primary"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="coming_soon">Coming Soon</option>
                  <option value="temporarily_closed">Temporarily Closed</option>
                  <option value="permanently_closed">Permanently Closed</option>
                </select>
              </div>
            </div>

            {/* Location Information */}
            <div>
              <h3 className="text-lg font-semibold glass-text-primary mb-4">Location Information</h3>
              <div className="space-y-4">
                                  <div>
                    <label className="block text-sm font-medium glass-text-primary mb-2">
                      Address Line 1 *
                    </label>
                    <input
                      type="text"
                      value={formData.address_line1}
                      onChange={(e) => handleInputChange('address_line1', e.target.value)}
                      className={`glass-input glass-text-primary placeholder:glass-text-muted ${
                        errors.address_line1 ? 'glass-error' : ''
                      }`}
                      placeholder="123 Main Street"
                    />
                    {errors.address_line1 && <p className="mt-1 text-sm text-red-400">{errors.address_line1}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium glass-text-primary mb-2">
                      Address Line 2
                    </label>
                    <input
                      type="text"
                      value={formData.address_line2 || ''}
                      onChange={(e) => handleInputChange('address_line2', e.target.value)}
                      className="glass-input glass-text-primary placeholder:glass-text-muted"
                      placeholder="Suite 100 (optional)"
                    />
                  </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                      <div>
                      <label className="block text-sm font-medium glass-text-primary mb-2">
                        City *
                      </label>
                      <input
                        type="text"
                        value={formData.city}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        className={`glass-input glass-text-primary placeholder:glass-text-muted ${
                          errors.city ? 'glass-error' : ''
                        }`}
                        placeholder="New York"
                      />
                      {errors.city && <p className="mt-1 text-sm text-red-400">{errors.city}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium glass-text-primary mb-2">
                        State *
                      </label>
                      <input
                        type="text"
                        value={formData.state}
                        onChange={(e) => handleInputChange('state', e.target.value)}
                        className={`glass-input glass-text-primary placeholder:glass-text-muted ${
                          errors.state ? 'glass-error' : ''
                        }`}
                        placeholder="NY"
                      />
                      {errors.state && <p className="mt-1 text-sm text-red-400">{errors.state}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium glass-text-primary mb-2">
                        Postal Code *
                      </label>
                      <input
                        type="text"
                        value={formData.postal_code}
                        onChange={(e) => handleInputChange('postal_code', e.target.value)}
                        className={`glass-input glass-text-primary placeholder:glass-text-muted ${
                          errors.postal_code ? 'glass-error' : ''
                        }`}
                        placeholder="10001"
                      />
                      {errors.postal_code && <p className="mt-1 text-sm text-red-400">{errors.postal_code}</p>}
                    </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                      <div>
                      <label className="block text-sm font-medium glass-text-primary mb-2">
                        Country
                      </label>
                      <select
                        value={formData.country}
                        onChange={(e) => handleInputChange('country', e.target.value)}
                        className="glass-input glass-text-primary"
                      >
                        <option value="United States">United States</option>
                        <option value="Canada">Canada</option>
                        <option value="Mexico">Mexico</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium glass-text-primary mb-2">
                        Timezone
                      </label>
                      <select
                        value={formData.timezone}
                        onChange={(e) => handleInputChange('timezone', e.target.value)}
                        className="glass-input glass-text-primary"
                      >
                        {timezones.map(tz => (
                          <option key={tz} value={tz}>{tz}</option>
                        ))}
                      </select>
                    </div>
                </div>
                              </div>
              </div>

            {/* Contact Information */}
            <div>
              <h3 className="text-lg font-semibold glass-text-primary mb-4">Contact Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium glass-text-primary mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={formData.phone || ''}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className={`glass-input glass-text-primary placeholder:glass-text-muted ${
                      errors.phone ? 'glass-error' : ''
                    }`}
                    placeholder="+****************"
                  />
                  {errors.phone && <p className="mt-1 text-sm text-red-400">{errors.phone}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium glass-text-primary mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={formData.email || ''}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={`glass-input glass-text-primary placeholder:glass-text-muted ${
                      errors.email ? 'glass-error' : ''
                    }`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && <p className="mt-1 text-sm text-red-400">{errors.email}</p>}
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium glass-text-primary mb-2">
                  Website
                </label>
                <input
                  type="url"
                  value={formData.website || ''}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  className="glass-input glass-text-primary placeholder:glass-text-muted"
                  placeholder="https://restaurant.com/downtown"
                />
              </div>
            </div>

            {/* Services */}
            <div>
              <h3 className="text-lg font-semibold glass-text-primary mb-4">Available Services</h3>
              <div className="space-y-3">
                <label className="flex items-center glass-interactive p-2 rounded-lg cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.has_delivery}
                    onChange={(e) => handleInputChange('has_delivery', e.target.checked)}
                    className="rounded border-white/30 text-blue-400 focus:ring-blue-400 bg-white/10"
                  />
                  <span className="ml-2 text-sm glass-text-primary">🚚 Delivery Service</span>
                </label>
                <label className="flex items-center glass-interactive p-2 rounded-lg cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.has_pickup}
                    onChange={(e) => handleInputChange('has_pickup', e.target.checked)}
                    className="rounded border-white/30 text-blue-400 focus:ring-blue-400 bg-white/10"
                  />
                  <span className="ml-2 text-sm glass-text-primary">🛍️ Pickup Orders</span>
                </label>
                <label className="flex items-center glass-interactive p-2 rounded-lg cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.has_dine_in}
                    onChange={(e) => handleInputChange('has_dine_in', e.target.checked)}
                    className="rounded border-white/30 text-blue-400 focus:ring-blue-400 bg-white/10"
                  />
                  <span className="ml-2 text-sm glass-text-primary">🍽️ Dine-in Service</span>
                </label>
                <label className="flex items-center glass-interactive p-2 rounded-lg cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.has_drive_through}
                    onChange={(e) => handleInputChange('has_drive_through', e.target.checked)}
                    className="rounded border-white/30 text-blue-400 focus:ring-blue-400 bg-white/10"
                  />
                  <span className="ml-2 text-sm glass-text-primary">🚗 Drive-Through</span>
                </label>
              </div>
            </div>

            {/* Operational Settings */}
            <div>
              <h3 className="text-lg font-semibold glass-text-primary mb-4">Operational Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium glass-text-primary mb-2">
                    Seating Capacity
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={formData.seating_capacity}
                    onChange={(e) => handleInputChange('seating_capacity', parseInt(e.target.value) || 0)}
                    className="glass-input glass-text-primary placeholder:glass-text-muted"
                    placeholder="50"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium glass-text-primary mb-2">
                    Parking Spaces
                  </label>
                  <input
                    type="number"
                    min="0"
                    value={formData.parking_spaces}
                    onChange={(e) => handleInputChange('parking_spaces', parseInt(e.target.value) || 0)}
                    className="glass-input glass-text-primary placeholder:glass-text-muted"
                    placeholder="20"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div>
                  <label className="block text-sm font-medium glass-text-primary mb-2">
                    Delivery Radius (miles)
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.1"
                    value={formData.delivery_radius}
                    onChange={(e) => handleInputChange('delivery_radius', parseFloat(e.target.value) || 0)}
                    className={`glass-input glass-text-primary placeholder:glass-text-muted ${
                      errors.delivery_radius ? 'glass-error' : ''
                    }`}
                    placeholder="5.0"
                  />
                  {errors.delivery_radius && <p className="mt-1 text-sm text-red-400">{errors.delivery_radius}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium glass-text-primary mb-2">
                    Delivery Fee ($)
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.delivery_fee}
                    onChange={(e) => handleInputChange('delivery_fee', parseFloat(e.target.value) || 0)}
                    className={`glass-input glass-text-primary placeholder:glass-text-muted ${
                      errors.delivery_fee ? 'glass-error' : ''
                    }`}
                    placeholder="2.99"
                  />
                  {errors.delivery_fee && <p className="mt-1 text-sm text-red-400">{errors.delivery_fee}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium glass-text-primary mb-2">
                    Minimum Order ($)
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.minimum_order_amount}
                    onChange={(e) => handleInputChange('minimum_order_amount', parseFloat(e.target.value) || 0)}
                    className={`glass-input glass-text-primary placeholder:glass-text-muted ${
                      errors.minimum_order_amount ? 'glass-error' : ''
                    }`}
                    placeholder="15.00"
                  />
                  {errors.minimum_order_amount && <p className="mt-1 text-sm text-red-400">{errors.minimum_order_amount}</p>}
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 mt-8 pt-6 border-t border-white/20">
            <button
              type="button"
              onClick={onClose}
              className="glass-interactive px-4 py-2 glass-text-secondary hover:glass-text-primary transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="glass-button glass-primary glass-text-primary px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Updating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  Update Branch
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}