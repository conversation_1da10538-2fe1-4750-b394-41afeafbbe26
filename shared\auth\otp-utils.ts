// OTP (One-Time Password) Utilities for Phone Authentication

import { supabase, AUTH_CONFIG } from './config';
import type { PhoneVerification } from './types';

/**
 * Generate and send OTP for phone verification
 */
export async function generateOTP(phone: string): Promise<{
  success: boolean;
  message: string;
  expires_in?: number;
}> {
  try {
    // Use the database function to generate OTP
    const { data: result, error } = await (supabase as any)
      .rpc('generate_phone_otp', { phone_number: phone });

    if (error) {
      console.error('Generate OTP error:', error);
      return {
        success: false,
        message: error.message || 'Failed to generate OTP',
      };
    }

    return {
      success: result.success,
      message: result.message,
      expires_in: result.expires_in,
    };
  } catch (error) {
    console.error('Generate OTP error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to generate OTP',
    };
  }
}

/**
 * Verify OTP for phone authentication
 */
export async function verifyOTP(phone: string, otp: string): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    // Use the database function to verify OTP
    const { data: result, error } = await (supabase as any)
      .rpc('verify_phone_otp', { 
        phone_number: phone, 
        otp_code: otp 
      });

    if (error) {
      console.error('Verify OTP error:', error);
      return {
        success: false,
        message: error.message || 'Failed to verify OTP',
      };
    }

    return {
      success: result.success,
      message: result.message,
    };
  } catch (error) {
    console.error('Verify OTP error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to verify OTP',
    };
  }
}

/**
 * Check if phone number can request new OTP (rate limiting)
 */
export async function canRequestOTP(phone: string): Promise<{
  canRequest: boolean;
  remainingTime?: number;
  message?: string;
}> {
  try {
    // Check recent OTP requests
    const cooldownMinutes = AUTH_CONFIG.rateLimiting.otpCooldownMinutes;
    const maxRequests = AUTH_CONFIG.rateLimiting.maxOTPRequests;
    
    const since = new Date();
    since.setMinutes(since.getMinutes() - cooldownMinutes);

    const { data: recentRequests, error } = await (supabase as any)
      .from('phone_verifications')
      .select('created_at')
      .eq('phone', phone)
      .gte('created_at', since.toISOString())
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Check OTP rate limit error:', error);
      return {
        canRequest: true, // Allow on error to not block users
      };
    }

    if (recentRequests && recentRequests.length >= maxRequests) {
      const lastRequest = new Date(recentRequests[0].created_at);
      const nextAllowed = new Date(lastRequest.getTime() + (cooldownMinutes * 60 * 1000));
      const now = new Date();
      
      if (now < nextAllowed) {
        const remainingTime = Math.ceil((nextAllowed.getTime() - now.getTime()) / 1000 / 60);
        return {
          canRequest: false,
          remainingTime,
          message: `Too many OTP requests. Please wait ${remainingTime} minute(s) before requesting again.`,
        };
      }
    }

    return { canRequest: true };
  } catch (error) {
    console.error('Check OTP rate limit error:', error);
    return { canRequest: true }; // Allow on error
  }
}

/**
 * Get active OTP for phone number
 */
export async function getActiveOTP(phone: string): Promise<PhoneVerification | null> {
  try {
    const { data: verification, error } = await (supabase as any)
      .from('phone_verifications')
      .select('*')
      .eq('phone', phone)
      .eq('verified', false)
      .gt('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error || !verification) {
      return null;
    }

    return verification as PhoneVerification;
  } catch (error) {
    console.error('Get active OTP error:', error);
    return null;
  }
}

/**
 * Clean up expired OTP records
 */
export async function cleanupExpiredOTPs(): Promise<{
  success: boolean;
  deletedCount?: number;
}> {
  try {
    const { data, error } = await (supabase as any)
      .from('phone_verifications')
      .delete()
      .lt('expires_at', new Date().toISOString())
      .select('id');

    if (error) {
      console.error('OTP cleanup error:', error);
      return { success: false };
    }

    return {
      success: true,
      deletedCount: data?.length || 0,
    };
  } catch (error) {
    console.error('OTP cleanup error:', error);
    return { success: false };
  }
}

/**
 * Invalidate all OTPs for a phone number
 */
export async function invalidatePhoneOTPs(phone: string): Promise<{ success: boolean }> {
  try {
    const { error } = await (supabase as any)
      .from('phone_verifications')
      .update({ verified: true }) // Mark as used to invalidate
      .eq('phone', phone)
      .eq('verified', false);

    if (error) {
      console.error('Invalidate OTPs error:', error);
      return { success: false };
    }

    return { success: true };
  } catch (error) {
    console.error('Invalidate OTPs error:', error);
    return { success: false };
  }
}

/**
 * Get OTP verification history for a phone number
 */
export async function getOTPHistory(phone: string, limit: number = 10): Promise<PhoneVerification[]> {
  try {
    const { data: history, error } = await (supabase as any)
      .from('phone_verifications')
      .select('*')
      .eq('phone', phone)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Get OTP history error:', error);
      return [];
    }

    return history as PhoneVerification[];
  } catch (error) {
    console.error('Get OTP history error:', error);
    return [];
  }
}

/**
 * Format phone number for consistency
 */
export function formatPhoneNumber(phone: string): string {
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Add country code if not present (assuming US/Canada +1)
  if (digits.length === 10) {
    return `+1${digits}`;
  }
  
  // Add + if not present
  if (!digits.startsWith('+')) {
    return `+${digits}`;
  }
  
  return digits;
}

/**
 * Validate phone number format
 */
export function isValidPhoneNumber(phone: string): boolean {
  const formatted = formatPhoneNumber(phone);
  
  // Basic validation for international format
  const phoneRegex = /^\+[1-9]\d{1,14}$/;
  return phoneRegex.test(formatted);
}

/**
 * Generate random OTP code
 */
export function generateOTPCode(length: number = AUTH_CONFIG.otp.length): string {
  const digits = '0123456789';
  let otp = '';
  
  for (let i = 0; i < length; i++) {
    otp += digits.charAt(Math.floor(Math.random() * digits.length));
  }
  
  return otp;
}

/**
 * Calculate OTP expiration time
 */
export function getOTPExpirationTime(): Date {
  const expiration = new Date();
  expiration.setMinutes(expiration.getMinutes() + AUTH_CONFIG.otp.expiryMinutes);
  return expiration;
}

/**
 * Check if OTP is expired
 */
export function isOTPExpired(expiresAt: string): boolean {
  const now = new Date();
  const expiration = new Date(expiresAt);
  return now > expiration;
}

/**
 * Mask phone number for display (e.g., +1234***5678)
 */
export function maskPhoneNumber(phone: string): string {
  const formatted = formatPhoneNumber(phone);
  
  if (formatted.length < 8) {
    return formatted;
  }
  
  const start = formatted.substring(0, 4);
  const end = formatted.substring(formatted.length - 4);
  const middle = '*'.repeat(formatted.length - 8);
  
  return `${start}${middle}${end}`;
}