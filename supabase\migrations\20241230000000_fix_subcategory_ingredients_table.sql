-- Create a view that maps menu_item_ingredients to subcategory_ingredients
-- This provides backward compatibility for code expecting subcategory_ingredients table

CREATE OR REPLACE VIEW subcategory_ingredients AS
SELECT 
    ROW_NUMBER() OVER (ORDER BY menu_item_id, ingredient_id) as id,
    menu_item_id as subcategory_id,
    ingredient_id,
    quantity_required,
    created_at
FROM menu_item_ingredients;

-- Create INSTEAD OF triggers to handle INSERT, UPDATE, DELETE operations on the view

-- INSERT trigger
CREATE OR REPLACE FUNCTION subcategory_ingredients_insert()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO menu_item_ingredients (menu_item_id, ingredient_id, quantity_required)
    VALUES (NEW.subcategory_id, NEW.ingredient_id, NEW.quantity_required);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER subcategory_ingredients_insert_trigger
    INSTEAD OF INSERT ON subcategory_ingredients
    FOR EACH ROW EXECUTE FUNCTION subcategory_ingredients_insert();

-- <PERSON><PERSON><PERSON> trigger
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION subcategory_ingredients_update()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE menu_item_ingredients 
    SET 
        menu_item_id = NEW.subcategory_id,
        ingredient_id = NEW.ingredient_id,
        quantity_required = NEW.quantity_required
    WHERE menu_item_id = OLD.subcategory_id 
      AND ingredient_id = OLD.ingredient_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER subcategory_ingredients_update_trigger
    INSTEAD OF UPDATE ON subcategory_ingredients
    FOR EACH ROW EXECUTE FUNCTION subcategory_ingredients_update();

-- DELETE trigger
CREATE OR REPLACE FUNCTION subcategory_ingredients_delete()
RETURNS TRIGGER AS $$
BEGIN
    DELETE FROM menu_item_ingredients 
    WHERE menu_item_id = OLD.subcategory_id 
      AND ingredient_id = OLD.ingredient_id;
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER subcategory_ingredients_delete_trigger
    INSTEAD OF DELETE ON subcategory_ingredients
    FOR EACH ROW EXECUTE FUNCTION subcategory_ingredients_delete();