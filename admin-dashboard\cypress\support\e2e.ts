// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';
import '@cypress/code-coverage/support';

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Global configuration
Cypress.on('uncaught:exception', (err, runnable) => {
  // Returning false here prevents <PERSON><PERSON> from failing the test
  // on uncaught exceptions that we expect (like network errors)
  if (err.message.includes('Network Error') || 
      err.message.includes('fetch') ||
      err.message.includes('ResizeObserver loop limit exceeded')) {
    return false;
  }
  return true;
});

// Custom assertions
Chai.use((chai, utils) => {
  chai.Assertion.addMethod('beVisible', function () {
    const obj = this._obj;
    this.assert(
      obj.should('be.visible'),
      'expected #{this} to be visible',
      'expected #{this} not to be visible'
    );
  });
});

// Global hooks
beforeEach(() => {
  // Clear local storage and cookies before each test
  cy.clearLocalStorage();
  cy.clearCookies();
  
  // Set up viewport
  cy.viewport(1280, 720);
  
  // Intercept common API calls
  cy.intercept('GET', '/api/auth/session', { fixture: 'auth/session.json' }).as('getSession');
  cy.intercept('POST', '/api/auth/signin', { fixture: 'auth/signin.json' }).as('signIn');
  cy.intercept('GET', '/api/orders*', { fixture: 'orders/orders.json' }).as('getOrders');
  cy.intercept('GET', '/api/menu*', { fixture: 'menu/menu-items.json' }).as('getMenuItems');
  cy.intercept('GET', '/api/analytics*', { fixture: 'analytics/dashboard.json' }).as('getAnalytics');
});

after(() => {
  // Clean up after all tests
  cy.task('clearDatabase');
});

// Add custom types
declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to login as admin user
       * @example cy.loginAsAdmin()
       */
      loginAsAdmin(): Chainable<void>;
      
      /**
       * Custom command to seed test data
       * @example cy.seedTestData()
       */
      seedTestData(): Chainable<void>;
      
      /**
       * Custom command to wait for page to load
       * @example cy.waitForPageLoad()
       */
      waitForPageLoad(): Chainable<void>;
      
      /**
       * Custom command to check accessibility
       * @example cy.checkA11y()
       */
      checkA11y(): Chainable<void>;
      
      /**
       * Custom command to test responsive design
       * @example cy.testResponsive()
       */
      testResponsive(): Chainable<void>;
    }
  }
}