'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, Menu, Heart, User } from 'lucide-react';
import { CartIndicator } from '@/components/cart/cart-indicator';
import { cn } from '@/lib/utils';
import { GlassNavbar, GlassButton } from '@/components/ui/glass-components';
import { useCartContext } from '@/providers/cart-provider';
import { ThemeToggle } from '@/components/ui/theme-toggle';

interface NavigationItem {
  href: string;
  label: string;
  icon: React.ReactNode;
  badge?: number;
}

export function MainNavigation() {
  const pathname = usePathname();

  const navigationItems: NavigationItem[] = [
    {
      href: '/',
      label: 'Home',
      icon: <Home className="w-5 h-5" />,
    },
    {
      href: '/menu',
      label: 'Menu',
      icon: <Menu className="w-5 h-5" />,
    },
    {
      href: '/cart',
      label: 'Cart',
      icon: <CartIndicator className="w-5 h-5" showAnimation={false} />,
    },
    {
      href: '/profile/favorites',
      label: 'Favorites',
      icon: <Heart className="w-5 h-5" />,
    },
    {
      href: '/profile',
      label: 'Profile',
      icon: <User className="w-5 h-5" />,
    },
  ];

  return (
    <GlassNavbar className="fixed bottom-0 left-0 right-0 z-50 md:top-0 md:bottom-auto border-t md:border-b md:border-t-0">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo - Hidden on mobile, shown on desktop */}
          <div className="hidden md:flex items-center">
            <Link href="/" className="text-2xl font-bold text-primary">
              Creperie
            </Link>
          </div>

          {/* Navigation Items */}
          <nav className="flex items-center justify-around w-full md:w-auto md:space-x-8">
            {navigationItems.map((item) => {
              const isActive = pathname === item.href || 
                (item.href !== '/' && pathname && pathname.startsWith(item.href));
              
              return (
                item.href === '/cart' ? (
                  <div
                    key={item.href}
                    className={cn(
                      'flex flex-col md:flex-row items-center space-y-1 md:space-y-0 md:space-x-2 px-3 py-2 rounded-lg transition-colors',
                      isActive
                        ? 'text-primary bg-primary/10'
                        : 'text-muted-foreground hover:text-primary hover:bg-primary/5'
                    )}
                  >
                    {item.icon}
                    <span className="text-xs md:text-sm font-medium">
                      {item.label}
                    </span>
                  </div>
                ) : (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      'flex flex-col md:flex-row items-center space-y-1 md:space-y-0 md:space-x-2 px-3 py-2 rounded-lg transition-colors',
                      isActive
                        ? 'text-primary bg-primary/10'
                        : 'text-muted-foreground hover:text-primary hover:bg-primary/5'
                    )}
                  >
                    <div className="relative">
                      {item.icon}
                    </div>
                    <span className="text-xs md:text-sm font-medium">
                      {item.label}
                    </span>
                  </Link>
                )
              );
            })}
          </nav>

          {/* Theme Toggle and Order Now Button - Desktop only */}
          <div className="hidden md:flex items-center space-x-4">
            <ThemeToggle />
            <Link href="/order">
              <GlassButton variant="primary" size="sm">
                Order Now
              </GlassButton>
            </Link>
          </div>
        </div>
      </div>
    </GlassNavbar>
  );
}

export default MainNavigation;