import React, { useState } from 'react';
import { X, MapPin, Home, User, Phone, Mail } from 'lucide-react';
import { getApiUrl } from '../../../config/environment';

interface Customer {
  id: string;
  phone: string;
  name: string;
  email?: string;
  address?: string;
  postal_code?: string;
  floor_number?: string;
  notes?: string;
  name_on_ringer?: string;
}

interface AddNewAddressModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer: Customer;
  onAddressAdded: (customer: Customer, newAddress: string, postalCode?: string, floorNumber?: string, notes?: string) => void;
}

export const AddNewAddressModal: React.FC<AddNewAddressModalProps> = ({
  isOpen,
  onClose,
  customer,
  onAddressAdded,
}) => {
  const [formData, setFormData] = useState({
    address: '',
    postalCode: '',
    floorNumber: '',
    notes: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<any[]>([]);

  const searchAddresses = async (input: string) => {
    if (input.length < 3) {
      setSuggestions([]);
      return;
    }

    setIsLoading(true);
    try {
      // Call the admin dashboard API which will use Google Maps MCP
      const response = await fetch(getApiUrl('google-maps/search-places'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: input + ', Greece', // Restrict to Greece
          location: { latitude: 37.9755, longitude: 23.7348 }, // Athens center
          radius: 50000 // 50km radius
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result && result.places && Array.isArray(result.places)) {
        setSuggestions(result.places.slice(0, 5)); // Limit to 5 suggestions
      } else {
        setSuggestions([]);
      }
    } catch (error) {
      console.error('Error searching addresses:', error);
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestionClick = async (suggestion: any) => {
    try {
      // Get place details to extract postal code
      const response = await fetch(getApiUrl('google-maps/place-details'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          place_id: suggestion.place_id
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const detailsResult = await response.json();
      
      let fullAddress = suggestion.formatted_address;
      let postalCode = '';

      if (detailsResult && detailsResult.result) {
        const addressComponents = detailsResult.result.address_components || [];
        
        // Extract postal code
        const postalCodeComponent = addressComponents.find((component: any) => 
          component.types.includes('postal_code')
        );
        
        if (postalCodeComponent) {
          postalCode = postalCodeComponent.long_name;
        }
      }

      setFormData(prev => ({
        ...prev,
        address: fullAddress,
        postalCode: postalCode
      }));
      setSuggestions([]);
    } catch (error) {
      console.error('Error getting place details:', error);
      // Fallback: just use the suggestion address
      setFormData(prev => ({
        ...prev,
        address: suggestion.formatted_address
      }));
      setSuggestions([]);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    if (field === 'address') {
      searchAddresses(value);
    }
  };

  const handleSubmit = async () => {
    if (!formData.address.trim()) {
      alert('Please enter an address');
      return;
    }


    setIsLoading(true);
    try {
      // First, save the new address to the database
      const addAddressResponse = await fetch(getApiUrl(`customers/${customer.id}/addresses`), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          address: formData.address.trim(),
          postal_code: formData.postalCode.trim() || undefined,
          floor_number: formData.floorNumber.trim() || undefined,
          notes: formData.notes.trim() || undefined,
          address_type: 'delivery',
          is_default: false // This is a secondary address, not the default
        }),
      });

      if (!addAddressResponse.ok) {
        throw new Error('Failed to save address to database');
      }

      const addressResult = await addAddressResponse.json();
      
      if (!addressResult.success) {
        throw new Error(addressResult.error || 'Failed to save address');
      }


      // Call the callback with the updated customer information
      onAddressAdded(
        customer,
        formData.address.trim(),
        formData.postalCode.trim() || undefined,
        formData.floorNumber.trim() || undefined,
        formData.notes.trim() || undefined
      );
      
      // Reset form
      setFormData({
        address: '',
        postalCode: '',
        floorNumber: '',
        notes: '',
      });
      
      onClose();
    } catch (error) {
      console.error('Error adding new address:', error);
      alert(`Failed to add new address: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSubmit();
    }
  };

  if (!isOpen) return null;


  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-[9999] p-4">
      <div className="bg-white/10 dark:bg-gray-900/20 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-gray-700/30 shadow-2xl max-w-lg w-full p-6 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Add New Address
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Adding address for {customer.name}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white/10 dark:hover:bg-gray-800/20 rounded-full transition-colors"
          >
            <X className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          </button>
        </div>

        {/* Customer Info */}
        <div className="mb-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
          <div className="flex items-center gap-3">
            <User className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <div>
              <p className="font-medium text-gray-900 dark:text-white">{customer.name}</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">📞 {customer.phone}</p>
              {customer.email && (
                <p className="text-sm text-gray-600 dark:text-gray-400">✉️ {customer.email}</p>
              )}
            </div>
          </div>
        </div>

        {/* Address Form */}
        <div className="space-y-4">
          {/* Address Input with Autocomplete */}
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Address *
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Start typing address..."
                className="w-full pl-10 pr-4 py-3 bg-white/10 dark:bg-gray-800/20 backdrop-blur-sm border border-white/20 dark:border-gray-700/30 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent transition-all"
                required
              />
            </div>
            
            {/* Address Suggestions */}
            {suggestions.length > 0 && (
              <div className="absolute z-10 w-full mt-1 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-white/20 dark:border-gray-700/30 rounded-xl shadow-lg max-h-48 overflow-y-auto">
                {suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full px-4 py-3 text-left hover:bg-blue-500/10 transition-colors border-b border-gray-200/20 dark:border-gray-700/20 last:border-b-0"
                  >
                    <div className="flex items-start gap-2">
                      <MapPin className="w-4 h-4 text-gray-400 mt-1 flex-shrink-0" />
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {suggestion.name}
                        </p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {suggestion.formatted_address}
                        </p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Postal Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Postal Code
            </label>
            <input
              type="text"
              value={formData.postalCode}
              onChange={(e) => handleInputChange('postalCode', e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="e.g., 10678"
              className="w-full px-4 py-3 bg-white/10 dark:bg-gray-800/20 backdrop-blur-sm border border-white/20 dark:border-gray-700/30 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent transition-all"
            />
          </div>

          {/* Floor Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Floor Number
            </label>
            <input
              type="text"
              value={formData.floorNumber}
              onChange={(e) => handleInputChange('floorNumber', e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="e.g., 2nd Floor, Apt 5"
              className="w-full px-4 py-3 bg-white/10 dark:bg-gray-800/20 backdrop-blur-sm border border-white/20 dark:border-gray-700/30 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent transition-all"
            />
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Delivery Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Special delivery instructions..."
              rows={3}
              className="w-full px-4 py-3 bg-white/10 dark:bg-gray-800/20 backdrop-blur-sm border border-white/20 dark:border-gray-700/30 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent transition-all resize-none"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 mt-6">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-3 bg-gray-500/10 hover:bg-gray-500/20 text-gray-600 dark:text-gray-400 font-medium rounded-xl border border-gray-500/20 transition-all"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isLoading || !formData.address.trim()}
            className="flex-1 px-4 py-3 bg-blue-500/20 hover:bg-blue-500/30 disabled:bg-gray-500/10 disabled:cursor-not-allowed text-blue-600 dark:text-blue-400 disabled:text-gray-500 font-medium rounded-xl border border-blue-500/30 disabled:border-gray-500/20 transition-all flex items-center justify-center gap-2"
          >
            <Home className="w-5 h-5" />
            {isLoading ? 'Adding...' : 'Add Address'}
          </button>
        </div>

        {/* Help Text */}
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-3 text-center">
          Press Ctrl+Enter to submit quickly
        </p>
      </div>
    </div>
  );
}; 