'use client'

import React, { useState } from 'react'
import {
  GlassCard,
  GlassButton,
  GlassInput,
  GlassModal,
  GlassContainer,
} from '@/components/ui/glass-components'

export default function GlassmorphismDemo() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [inputValue, setInputValue] = useState('')
  const [loading, setLoading] = useState(false)

  const handleLoadingDemo = () => {
    setLoading(true)
    setTimeout(() => setLoading(false), 2000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <GlassCard className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">Glassmorphism Design System</h1>
          <p className="text-white/80 text-lg">
            A complete CSS framework for implementing glassmorphism effects
          </p>
        </GlassCard>

        {/* Glass Cards Demo */}
        <section>
          <h2 className="text-2xl font-bold text-white mb-6">Glass Cards</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <GlassCard variant="primary">
              <h3 className="text-xl font-semibold text-white mb-2">Primary Card</h3>
              <p className="text-white/80">
                This is a primary glass card with the default glassmorphism effect.
              </p>
            </GlassCard>

            <GlassCard variant="secondary">
              <h3 className="text-xl font-semibold text-white mb-2">Secondary Card</h3>
              <p className="text-white/80">This is a secondary glass card with reduced opacity.</p>
            </GlassCard>

            <GlassCard
              variant="primary"
              onClick={() => alert('Interactive card clicked!')}
              className="cursor-pointer"
            >
              <h3 className="text-xl font-semibold text-white mb-2">Interactive Card</h3>
              <p className="text-white/80">Click me! This card has hover and click effects.</p>
            </GlassCard>
          </div>
        </section>

        {/* Status Cards */}
        <section>
          <h2 className="text-2xl font-bold text-white mb-6">Status Variants</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <GlassCard variant="success">
              <h4 className="font-semibold text-green-100 mb-2">Success</h4>
              <p className="text-green-200/80 text-sm">Operation completed successfully</p>
            </GlassCard>

            <GlassCard variant="warning">
              <h4 className="font-semibold text-yellow-100 mb-2">Warning</h4>
              <p className="text-yellow-200/80 text-sm">Please review this action</p>
            </GlassCard>

            <GlassCard variant="error">
              <h4 className="font-semibold text-red-100 mb-2">Error</h4>
              <p className="text-red-200/80 text-sm">Something went wrong</p>
            </GlassCard>

            <GlassCard variant="info">
              <h4 className="font-semibold text-blue-100 mb-2">Info</h4>
              <p className="text-blue-200/80 text-sm">Additional information</p>
            </GlassCard>
          </div>
        </section>

        {/* Buttons Demo */}
        <section>
          <h2 className="text-2xl font-bold text-white mb-6">Glass Buttons</h2>
          <GlassCard>
            <div className="space-y-6">
              {/* Button Sizes */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Button Sizes</h3>
                <div className="flex flex-wrap gap-4">
                  <GlassButton size="small" className="text-white">
                    Small Button
                  </GlassButton>
                  <GlassButton size="medium" className="text-white">
                    Medium Button
                  </GlassButton>
                  <GlassButton size="large" className="text-white">
                    Large Button
                  </GlassButton>
                </div>
              </div>

              {/* Button Variants */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Button Variants</h3>
                <div className="flex flex-wrap gap-4">
                  <GlassButton variant="primary" className="text-white">
                    Primary
                  </GlassButton>
                  <GlassButton variant="success" className="text-green-100">
                    Success
                  </GlassButton>
                  <GlassButton variant="warning" className="text-yellow-100">
                    Warning
                  </GlassButton>
                  <GlassButton variant="error" className="text-red-100">
                    Error
                  </GlassButton>
                </div>
              </div>

              {/* Button States */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">Button States</h3>
                <div className="flex flex-wrap gap-4">
                  <GlassButton loading={loading} onClick={handleLoadingDemo} className="text-white">
                    {loading ? 'Loading...' : 'Click for Loading'}
                  </GlassButton>
                  <GlassButton disabled className="text-white/50">
                    Disabled Button
                  </GlassButton>
                </div>
              </div>
            </div>
          </GlassCard>
        </section>

        {/* Form Elements */}
        <section>
          <h2 className="text-2xl font-bold text-white mb-6">Form Elements</h2>
          <GlassCard>
            <div className="space-y-4">
              <div>
                <label className="block text-white font-medium mb-2">Glass Input Field</label>
                <GlassInput
                  type="text"
                  placeholder="Enter some text..."
                  value={inputValue}
                  onChange={e => setInputValue(e.target.value)}
                  className="text-white placeholder:text-white/60"
                />
              </div>

              <div>
                <label className="block text-white font-medium mb-2">Email Input</label>
                <GlassInput
                  type="email"
                  placeholder="<EMAIL>"
                  className="text-white placeholder:text-white/60"
                />
              </div>

              <div className="flex gap-4">
                <GlassButton onClick={() => setIsModalOpen(true)} className="text-white">
                  Open Modal
                </GlassButton>
                <GlassButton
                  variant="secondary"
                  onClick={() => setInputValue('')}
                  className="text-white"
                >
                  Clear Form
                </GlassButton>
              </div>
            </div>
          </GlassCard>
        </section>

        {/* Containers Demo */}
        <section>
          <h2 className="text-2xl font-bold text-white mb-6">Glass Containers</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <GlassContainer variant="primary" className="p-6">
              <h3 className="text-lg font-semibold text-white mb-3">Primary Container</h3>
              <p className="text-white/80 mb-4">
                This is a primary glass container that can hold any content.
              </p>
              <div className="space-y-2">
                <div className="h-2 bg-white/20 rounded"></div>
                <div className="h-2 bg-white/15 rounded w-3/4"></div>
                <div className="h-2 bg-white/10 rounded w-1/2"></div>
              </div>
            </GlassContainer>

            <GlassContainer variant="secondary" className="p-6">
              <h3 className="text-lg font-semibold text-white mb-3">Secondary Container</h3>
              <p className="text-white/80 mb-4">
                This is a secondary glass container with different styling.
              </p>
              <div className="grid grid-cols-3 gap-2">
                <div className="h-12 bg-white/10 rounded"></div>
                <div className="h-12 bg-white/15 rounded"></div>
                <div className="h-12 bg-white/20 rounded"></div>
              </div>
            </GlassContainer>
          </div>
        </section>
      </div>

      {/* Modal Demo */}
      <GlassModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Glass Modal Example"
        className="w-full max-w-md"
      >
        <div className="space-y-4">
          <p className="text-white/80">
            This is a beautiful glassmorphism modal with backdrop blur effect. It supports keyboard
            navigation and accessibility features.
          </p>

          <div className="space-y-3">
            <GlassInput
              type="text"
              placeholder="Modal input field"
              className="text-white placeholder:text-white/60"
            />

            <GlassInput
              type="password"
              placeholder="Password field"
              className="text-white placeholder:text-white/60"
            />
          </div>

          <div className="flex gap-3 justify-end">
            <GlassButton
              variant="secondary"
              onClick={() => setIsModalOpen(false)}
              className="text-white"
            >
              Cancel
            </GlassButton>
            <GlassButton
              variant="success"
              onClick={() => {
                alert('Modal action confirmed!')
                setIsModalOpen(false)
              }}
              className="text-green-100"
            >
              Confirm
            </GlassButton>
          </div>
        </div>
      </GlassModal>
    </div>
  )
}
