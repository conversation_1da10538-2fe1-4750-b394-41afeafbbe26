// Session Management Utilities for Creperie Authentication System

import { supabase, AUTH_CONFIG } from './config';
import type { AuthSession, AuthAttempt, Platform } from './types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Create a new authentication session
 */
export async function createSession({
  user_id,
  platform,
  ip_address,
  user_agent,
}: {
  user_id: string;
  platform: Platform;
  ip_address?: string;
  user_agent?: string;
}): Promise<AuthSession> {
  const sessionToken = uuidv4();
  const expiresAt = new Date();
  
  // Set expiration based on platform
  switch (platform) {
    case 'admin-dashboard':
      expiresAt.setTime(expiresAt.getTime() + AUTH_CONFIG.platforms.admin.sessionTimeout);
      break;
    case 'pos-system':
      expiresAt.setTime(expiresAt.getTime() + AUTH_CONFIG.platforms.pos.sessionTimeout);
      break;
    case 'customer-web':
      expiresAt.setTime(expiresAt.getTime() + AUTH_CONFIG.platforms.customer_web.sessionTimeout);
      break;
    case 'customer-mobile':
      expiresAt.setTime(expiresAt.getTime() + AUTH_CONFIG.platforms.customer_mobile.sessionTimeout);
      break;
    default:
      expiresAt.setTime(expiresAt.getTime() + AUTH_CONFIG.sessionDuration);
  }

  const { data: session, error } = await supabase
    .from('auth_sessions')
    .insert({
      user_id,
      session_token: sessionToken,
      platform,
      ip_address,
      user_agent,
      expires_at: expiresAt.toISOString(),
    })
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create session: ${error.message}`);
  }

  return session as any;
}

/**
 * Validate an existing session
 */
export async function validateSession(sessionToken: string): Promise<{
  valid: boolean;
  session?: AuthSession;
  expired?: boolean;
}> {
  try {
    const { data: session, error } = await supabase
      .from('auth_sessions')
      .select('*')
      .eq('session_token', sessionToken)
      .single();

    if (error || !session) {
      return { valid: false };
    }

    // Check if session is expired
    const now = new Date();
    const expiresAt = new Date(session.expires_at);
    
    if (now > expiresAt) {
      // Clean up expired session
      await supabase
        .from('auth_sessions')
        .delete()
        .eq('session_token', sessionToken);
      
      return { valid: false, expired: true };
    }

    return { valid: true, session: session as any };
  } catch (error) {
    console.error('Session validation error:', error);
    return { valid: false };
  }
}

/**
 * Refresh a session (extend expiration)
 */
export async function refreshSession(sessionToken: string): Promise<{
  success: boolean;
  session?: AuthSession;
}> {
  try {
    const validation = await validateSession(sessionToken);
    
    if (!validation.valid || !validation.session) {
      return { success: false };
    }

    const newExpiresAt = new Date();
    
    // Extend based on platform
    switch (validation.session.platform) {
      case 'admin-dashboard':
        newExpiresAt.setTime(newExpiresAt.getTime() + AUTH_CONFIG.platforms.admin.sessionTimeout);
        break;
      case 'pos-system':
        newExpiresAt.setTime(newExpiresAt.getTime() + AUTH_CONFIG.platforms.pos.sessionTimeout);
        break;
      case 'customer-web':
        newExpiresAt.setTime(newExpiresAt.getTime() + AUTH_CONFIG.platforms.customer_web.sessionTimeout);
        break;
      case 'customer-mobile':
        newExpiresAt.setTime(newExpiresAt.getTime() + AUTH_CONFIG.platforms.customer_mobile.sessionTimeout);
        break;
      default:
        newExpiresAt.setTime(newExpiresAt.getTime() + AUTH_CONFIG.sessionDuration);
    }

    const { data: updatedSession, error } = await supabase
      .from('auth_sessions')
      .update({ expires_at: newExpiresAt.toISOString() })
      .eq('session_token', sessionToken)
      .select()
      .single();

    if (error) {
      return { success: false };
    }

    return { success: true, session: updatedSession as any };
  } catch (error) {
    console.error('Session refresh error:', error);
    return { success: false };
  }
}

/**
 * Revoke a session (logout)
 */
export async function revokeSession(sessionToken: string): Promise<{ success: boolean }> {
  try {
    const { error } = await supabase
      .from('auth_sessions')
      .delete()
      .eq('session_token', sessionToken);

    if (error) {
      console.error('Session revocation error:', error);
      return { success: false };
    }

    return { success: true };
  } catch (error) {
    console.error('Session revocation error:', error);
    return { success: false };
  }
}

/**
 * Revoke all sessions for a user
 */
export async function revokeAllUserSessions(userId: string): Promise<{ success: boolean }> {
  try {
    const { error } = await supabase
      .from('auth_sessions')
      .delete()
      .eq('user_id', userId);

    if (error) {
      console.error('All sessions revocation error:', error);
      return { success: false };
    }

    return { success: true };
  } catch (error) {
    console.error('All sessions revocation error:', error);
    return { success: false };
  }
}

/**
 * Get active sessions for a user
 */
export async function getUserSessions(userId: string): Promise<AuthSession[]> {
  try {
    const { data: sessions, error } = await supabase
      .from('auth_sessions')
      .select('*')
      .eq('user_id', userId)
      .gt('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Get user sessions error:', error);
      return [];
    }

    return sessions as any;
  } catch (error) {
    console.error('Get user sessions error:', error);
    return [];
  }
}

/**
 * Clean up expired sessions
 */
export async function cleanupExpiredSessions(): Promise<{ success: boolean; deletedCount?: number }> {
  try {
    const { data, error } = await supabase
      .from('auth_sessions')
      .delete()
      .lt('expires_at', new Date().toISOString())
      .select('id');

    if (error) {
      console.error('Session cleanup error:', error);
      return { success: false };
    }

    return { success: true, deletedCount: data?.length || 0 };
  } catch (error) {
    console.error('Session cleanup error:', error);
    return { success: false };
  }
}

/**
 * Log authentication attempt
 */
export async function logAuthAttempt({
  user_id,
  email,
  phone,
  attempt_type,
  success,
  ip_address,
  user_agent,
  error_message,
}: {
  user_id?: string;
  email?: string;
  phone?: string;
  attempt_type: 'email_password' | 'phone_otp' | 'pin' | 'two_fa';
  success: boolean;
  ip_address?: string;
  user_agent?: string;
  error_message?: string;
}): Promise<void> {
  try {
    await supabase
      .from('auth_attempts')
      .insert({
        user_id,
        email,
        phone,
        attempt_type,
        success,
        ip_address,
        user_agent,
        error_message,
      });
  } catch (error) {
    console.error('Log auth attempt error:', error);
    // Don't throw error as this is logging
  }
}

/**
 * Get recent failed authentication attempts
 */
export async function getRecentFailedAttempts({
  email,
  phone,
  ip_address,
  minutes = 15,
}: {
  email?: string;
  phone?: string;
  ip_address?: string;
  minutes?: number;
}): Promise<AuthAttempt[]> {
  try {
    const since = new Date();
    since.setMinutes(since.getMinutes() - minutes);

    let query = supabase
      .from('auth_attempts')
      .select('*')
      .eq('success', false)
      .gte('created_at', since.toISOString())
      .order('created_at', { ascending: false });

    if (email) {
      query = query.eq('email', email);
    } else if (phone) {
      query = query.eq('phone', phone);
    } else if (ip_address) {
      query = query.eq('ip_address', ip_address);
    }

    const { data: attempts, error } = await query;

    if (error) {
      console.error('Get failed attempts error:', error);
      return [];
    }

    return attempts as AuthAttempt[];
  } catch (error) {
    console.error('Get failed attempts error:', error);
    return [];
  }
}

/**
 * Check if user/IP is rate limited
 */
export async function isRateLimited({
  email,
  phone,
  ip_address,
}: {
  email?: string;
  phone?: string;
  ip_address?: string;
}): Promise<{ limited: boolean; remainingTime?: number }> {
  try {
    const failedAttempts = await getRecentFailedAttempts({
      email,
      phone,
      ip_address,
      minutes: AUTH_CONFIG.rateLimiting.lockoutMinutes,
    });

    if (failedAttempts.length >= AUTH_CONFIG.rateLimiting.maxLoginAttempts) {
      const lastAttempt = failedAttempts[0];
      const lockoutEnd = new Date(lastAttempt.created_at);
      lockoutEnd.setMinutes(lockoutEnd.getMinutes() + AUTH_CONFIG.rateLimiting.lockoutMinutes);
      
      const now = new Date();
      if (now < lockoutEnd) {
        const remainingTime = Math.ceil((lockoutEnd.getTime() - now.getTime()) / 1000 / 60);
        return { limited: true, remainingTime };
      }
    }

    return { limited: false };
  } catch (error) {
    console.error('Rate limit check error:', error);
    return { limited: false };
  }
}

/**
 * Get session by token
 */
export async function getSessionByToken(sessionToken: string): Promise<AuthSession | null> {
  try {
    const { data: session, error } = await supabase
      .from('auth_sessions')
      .select('*')
      .eq('session_token', sessionToken)
      .single();

    if (error || !session) {
      return null;
    }

    return session as any;
  } catch (error) {
    console.error('Get session by token error:', error);
    return null;
  }
}