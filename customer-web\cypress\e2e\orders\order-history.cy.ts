/// <reference types="cypress" />

describe('Order History', () => {
  beforeEach(() => {
    // Clear any existing data
    cy.clearAllCookies();
    cy.clearAllLocalStorage();
    cy.clearAllSessionStorage();
    
    // Set up API interceptors
    cy.intercept('GET', '/api/orders', { fixture: 'orders/orders.json' }).as('getOrders');
    cy.intercept('GET', '/api/orders/search*', { fixture: 'orders/orders.json' }).as('searchOrders');
    cy.intercept('GET', '/api/orders/filter*', { fixture: 'orders/orders.json' }).as('filterOrders');
    cy.intercept('POST', '/api/orders/*/reorder', { statusCode: 200 }).as('reorderItems');
    cy.intercept('POST', '/api/orders/*/cancel', { statusCode: 200 }).as('cancelOrder');
    cy.intercept('GET', '/api/orders/*/receipt', { statusCode: 200 }).as('downloadReceipt');
    
    // Login as customer
    cy.loginAsCustomer();
  });

  describe('Order History Page Access', () => {
    it('should redirect to login if not authenticated', () => {
      cy.clearAllCookies();
      cy.visit('/orders');
      
      cy.url().should('include', '/login');
      cy.get('[data-testid="redirect-message"]')
        .should('contain', 'Please login to view your orders');
    });

    it('should display order history page successfully', () => {
      cy.visit('/orders');
      cy.wait('@getOrders');
      
      // Check page title and heading
      cy.title().should('contain', 'Order History');
      cy.get('h1').should('contain', 'Your Orders');
      
      // Check main sections
      cy.get('[data-testid="order-filters"]').should('be.visible');
      cy.get('[data-testid="order-search"]').should('be.visible');
      cy.get('[data-testid="order-list"]').should('be.visible');
    });

    it('should show empty state when no orders exist', () => {
      cy.intercept('GET', '/api/orders', {
        body: { orders: [], total: 0, page: 1, totalPages: 0 }
      }).as('getEmptyOrders');
      
      cy.visit('/orders');
      cy.wait('@getEmptyOrders');
      
      cy.get('[data-testid="empty-orders"]')
        .should('be.visible')
        .and('contain', 'No orders found');
      
      cy.get('[data-testid="start-ordering-button"]')
        .should('be.visible')
        .and('contain', 'Start Ordering');
    });
  });

  describe('Order List Display', () => {
    beforeEach(() => {
      cy.visit('/orders');
      cy.wait('@getOrders');
    });

    it('should display order cards with correct information', () => {
      cy.fixture('orders/orders').then((orderData) => {
        const orders = orderData.orderHistory;
        
        cy.get('[data-testid="order-card"]').should('have.length', orders.length);
        
        orders.forEach((order, index) => {
          cy.get('[data-testid="order-card"]').eq(index).within(() => {
            cy.get('[data-testid="order-number"]').should('contain', order.id);
            cy.get('[data-testid="order-date"]').should('be.visible');
            cy.get('[data-testid="order-status"]').should('contain', order.status);
            cy.get('[data-testid="order-total"]').should('contain', `$${order.total}`);
            cy.get('[data-testid="order-items-count"]').should('contain', `${order.items.length} items`);
          });
        });
      });
    });

    it('should display order status with correct styling', () => {
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="order-status"]')
          .should('be.visible')
          .and('have.class', 'status-delivered');
      });
      
      cy.get('[data-testid="order-card"]').eq(1).within(() => {
        cy.get('[data-testid="order-status"]')
          .should('be.visible')
          .and('have.class', 'status-cancelled');
      });
    });

    it('should show order items preview', () => {
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="order-items-preview"]').should('be.visible');
        
        cy.get('[data-testid="item-preview"]').should('have.length.at.least', 1);
        
        cy.get('[data-testid="item-preview"]').first().within(() => {
          cy.get('[data-testid="item-image"]').should('be.visible');
          cy.get('[data-testid="item-name"]').should('be.visible');
          cy.get('[data-testid="item-quantity"]').should('be.visible');
        });
      });
    });

    it('should show more items indicator when order has many items', () => {
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="more-items-indicator"]')
          .should('be.visible')
          .and('contain', '+');
      });
    });

    it('should display delivery information', () => {
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="delivery-method"]')
          .should('be.visible')
          .and('contain', 'Delivery');
        
        cy.get('[data-testid="delivery-address"]')
          .should('be.visible');
      });
    });
  });

  describe('Order Search', () => {
    beforeEach(() => {
      cy.visit('/orders');
      cy.wait('@getOrders');
    });

    it('should search orders by order number', () => {
      cy.get('[data-testid="order-search-input"]')
        .type('ORD-12345');
      
      cy.get('[data-testid="search-button"]').click();
      
      cy.wait('@searchOrders');
      
      cy.get('[data-testid="search-results"]').should('be.visible');
      cy.get('[data-testid="search-query"]').should('contain', 'ORD-12345');
    });

    it('should search orders by item name', () => {
      cy.get('[data-testid="order-search-input"]')
        .type('Margherita Pizza');
      
      cy.get('[data-testid="search-button"]').click();
      
      cy.wait('@searchOrders');
      
      cy.get('[data-testid="order-card"]').should('be.visible');
    });

    it('should show search suggestions', () => {
      cy.get('[data-testid="order-search-input"]')
        .type('Pizza');
      
      cy.get('[data-testid="search-suggestions"]').should('be.visible');
      
      cy.get('[data-testid="suggestion-item"]').should('have.length.at.least', 1);
      
      cy.get('[data-testid="suggestion-item"]').first().click();
      
      cy.wait('@searchOrders');
    });

    it('should clear search results', () => {
      cy.get('[data-testid="order-search-input"]').type('ORD-12345');
      cy.get('[data-testid="search-button"]').click();
      cy.wait('@searchOrders');
      
      cy.get('[data-testid="clear-search-button"]').click();
      
      cy.get('[data-testid="order-search-input"]').should('have.value', '');
      cy.wait('@getOrders');
    });

    it('should handle no search results', () => {
      cy.intercept('GET', '/api/orders/search*', {
        body: { orders: [], total: 0, page: 1, totalPages: 0 }
      }).as('getNoResults');
      
      cy.get('[data-testid="order-search-input"]').type('NONEXISTENT');
      cy.get('[data-testid="search-button"]').click();
      
      cy.wait('@getNoResults');
      
      cy.get('[data-testid="no-search-results"]')
        .should('be.visible')
        .and('contain', 'No orders found');
    });

    it('should search on Enter key press', () => {
      cy.get('[data-testid="order-search-input"]')
        .type('ORD-12345{enter}');
      
      cy.wait('@searchOrders');
    });
  });

  describe('Order Filtering', () => {
    beforeEach(() => {
      cy.visit('/orders');
      cy.wait('@getOrders');
    });

    it('should filter orders by status', () => {
      cy.get('[data-testid="status-filter"]').select('delivered');
      
      cy.wait('@filterOrders');
      
      cy.get('[data-testid="order-card"]').each(($card) => {
        cy.wrap($card).within(() => {
          cy.get('[data-testid="order-status"]')
            .should('contain', 'Delivered');
        });
      });
    });

    it('should filter orders by date range', () => {
      cy.get('[data-testid="date-filter-from"]')
        .type('2024-01-01');
      
      cy.get('[data-testid="date-filter-to"]')
        .type('2024-01-31');
      
      cy.get('[data-testid="apply-date-filter"]').click();
      
      cy.wait('@filterOrders');
      
      cy.get('[data-testid="filter-applied"]')
        .should('be.visible')
        .and('contain', 'Date range applied');
    });

    it('should filter orders by total amount', () => {
      cy.get('[data-testid="amount-filter-min"]').type('20');
      cy.get('[data-testid="amount-filter-max"]').type('50');
      
      cy.get('[data-testid="apply-amount-filter"]').click();
      
      cy.wait('@filterOrders');
      
      cy.get('[data-testid="order-card"]').each(($card) => {
        cy.wrap($card).within(() => {
          cy.get('[data-testid="order-total"]').invoke('text').then((text) => {
            const amount = parseFloat(text.replace('$', ''));
            expect(amount).to.be.at.least(20);
            expect(amount).to.be.at.most(50);
          });
        });
      });
    });

    it('should use quick filter buttons', () => {
      cy.get('[data-testid="quick-filter-last-month"]').click();
      
      cy.wait('@filterOrders');
      
      cy.get('[data-testid="active-filter"]')
        .should('be.visible')
        .and('contain', 'Last month');
    });

    it('should combine multiple filters', () => {
      cy.get('[data-testid="status-filter"]').select('delivered');
      cy.get('[data-testid="quick-filter-last-week"]').click();
      
      cy.wait('@filterOrders');
      
      cy.get('[data-testid="active-filters"]').within(() => {
        cy.get('[data-testid="filter-tag"]').should('have.length', 2);
      });
    });

    it('should clear all filters', () => {
      cy.get('[data-testid="status-filter"]').select('delivered');
      cy.get('[data-testid="quick-filter-last-week"]').click();
      cy.wait('@filterOrders');
      
      cy.get('[data-testid="clear-all-filters"]').click();
      
      cy.get('[data-testid="active-filters"]').should('not.exist');
      cy.wait('@getOrders');
    });

    it('should remove individual filters', () => {
      cy.get('[data-testid="status-filter"]').select('delivered');
      cy.get('[data-testid="quick-filter-last-week"]').click();
      cy.wait('@filterOrders');
      
      cy.get('[data-testid="filter-tag"]').first().within(() => {
        cy.get('[data-testid="remove-filter"]').click();
      });
      
      cy.get('[data-testid="filter-tag"]').should('have.length', 1);
    });
  });

  describe('Order Sorting', () => {
    beforeEach(() => {
      cy.visit('/orders');
      cy.wait('@getOrders');
    });

    it('should sort orders by date (newest first)', () => {
      cy.get('[data-testid="sort-select"]').select('date-desc');
      
      cy.wait('@filterOrders');
      
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="order-date"]').invoke('text').then((firstDate) => {
          cy.get('[data-testid="order-card"]').eq(1).within(() => {
            cy.get('[data-testid="order-date"]').invoke('text').then((secondDate) => {
              expect(new Date(firstDate)).to.be.at.least(new Date(secondDate));
            });
          });
        });
      });
    });

    it('should sort orders by date (oldest first)', () => {
      cy.get('[data-testid="sort-select"]').select('date-asc');
      
      cy.wait('@filterOrders');
      
      cy.get('[data-testid="sort-indicator"]')
        .should('be.visible')
        .and('contain', 'Oldest first');
    });

    it('should sort orders by total amount', () => {
      cy.get('[data-testid="sort-select"]').select('amount-desc');
      
      cy.wait('@filterOrders');
      
      cy.get('[data-testid="sort-indicator"]')
        .should('be.visible')
        .and('contain', 'Highest amount');
    });

    it('should sort orders by status', () => {
      cy.get('[data-testid="sort-select"]').select('status');
      
      cy.wait('@filterOrders');
      
      cy.get('[data-testid="sort-indicator"]')
        .should('be.visible')
        .and('contain', 'Status');
    });
  });

  describe('Order Actions', () => {
    beforeEach(() => {
      cy.visit('/orders');
      cy.wait('@getOrders');
    });

    it('should view order details', () => {
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="view-details-button"]').click();
      });
      
      cy.url().should('include', '/order-confirmation/');
    });

    it('should reorder items from completed order', () => {
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="reorder-button"]')
          .should('be.visible')
          .and('not.be.disabled');
        
        cy.get('[data-testid="reorder-button"]').click();
      });
      
      cy.wait('@reorderItems');
      
      cy.checkNotification('Items added to cart', 'success');
      
      cy.get('[data-testid="go-to-cart-button"]')
        .should('be.visible')
        .and('contain', 'Go to Cart');
    });

    it('should handle reorder errors', () => {
      cy.intercept('POST', '/api/orders/*/reorder', {
        statusCode: 400,
        body: { error: 'Some items are no longer available' }
      }).as('reorderError');
      
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="reorder-button"]').click();
      });
      
      cy.wait('@reorderError');
      
      cy.checkNotification('Some items are no longer available', 'warning');
    });

    it('should cancel pending order', () => {
      // Mock a pending order
      cy.fixture('orders/orders').then((orderData) => {
        const pendingOrder = {
          ...orderData.orderHistory[0],
          status: 'confirmed',
          canCancel: true
        };
        
        cy.intercept('GET', '/api/orders', {
          body: {
            orders: [pendingOrder, ...orderData.orderHistory.slice(1)],
            total: orderData.orderHistory.length,
            page: 1,
            totalPages: 1
          }
        }).as('getOrdersWithPending');
      });
      
      cy.reload();
      cy.wait('@getOrdersWithPending');
      
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="cancel-order-button"]')
          .should('be.visible')
          .and('not.be.disabled');
        
        cy.get('[data-testid="cancel-order-button"]').click();
      });
      
      cy.get('[data-testid="cancel-confirmation-modal"]').should('be.visible');
      
      cy.get('[data-testid="cancel-reason-select"]').select('Changed my mind');
      cy.get('[data-testid="confirm-cancel-button"]').click();
      
      cy.wait('@cancelOrder');
      
      cy.checkNotification('Order cancelled successfully', 'success');
    });

    it('should download receipt', () => {
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="download-receipt-button"]')
          .should('be.visible');
        
        cy.get('[data-testid="download-receipt-button"]').click();
      });
      
      cy.wait('@downloadReceipt');
      
      cy.checkNotification('Receipt downloaded', 'success');
    });

    it('should share order details', () => {
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="share-order-button"]').click();
      });
      
      cy.get('[data-testid="share-modal"]').should('be.visible');
      
      cy.get('[data-testid="copy-link-button"]').click();
      
      cy.checkNotification('Link copied to clipboard', 'success');
    });

    it('should leave feedback for delivered order', () => {
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="leave-feedback-button"]')
          .should('be.visible');
        
        cy.get('[data-testid="leave-feedback-button"]').click();
      });
      
      cy.get('[data-testid="feedback-modal"]').should('be.visible');
      
      cy.get('[data-testid="rating-stars"]').within(() => {
        cy.get('[data-testid="star-5"]').click();
      });
      
      cy.get('[data-testid="feedback-comment"]')
        .type('Great food and service!');
      
      cy.get('[data-testid="submit-feedback-button"]').click();
      
      cy.checkNotification('Thank you for your feedback!', 'success');
    });
  });

  describe('Order Details Modal', () => {
    beforeEach(() => {
      cy.visit('/orders');
      cy.wait('@getOrders');
    });

    it('should open order details in modal', () => {
      cy.get('[data-testid="order-card"]').first().click();
      
      cy.get('[data-testid="order-details-modal"]').should('be.visible');
      
      cy.get('[data-testid="modal-order-number"]')
        .should('be.visible')
        .and('contain', 'ORD-');
    });

    it('should display complete order information in modal', () => {
      cy.get('[data-testid="order-card"]').first().click();
      
      cy.get('[data-testid="order-details-modal"]').within(() => {
        cy.get('[data-testid="modal-order-items"]').should('be.visible');
        cy.get('[data-testid="modal-delivery-info"]').should('be.visible');
        cy.get('[data-testid="modal-payment-info"]').should('be.visible');
        cy.get('[data-testid="modal-order-summary"]').should('be.visible');
      });
    });

    it('should close modal with close button', () => {
      cy.get('[data-testid="order-card"]').first().click();
      
      cy.get('[data-testid="close-modal-button"]').click();
      
      cy.get('[data-testid="order-details-modal"]').should('not.exist');
    });

    it('should close modal with escape key', () => {
      cy.get('[data-testid="order-card"]').first().click();
      
      cy.get('body').type('{esc}');
      
      cy.get('[data-testid="order-details-modal"]').should('not.exist');
    });

    it('should close modal by clicking backdrop', () => {
      cy.get('[data-testid="order-card"]').first().click();
      
      cy.get('[data-testid="modal-backdrop"]').click({ force: true });
      
      cy.get('[data-testid="order-details-modal"]').should('not.exist');
    });
  });

  describe('Pagination', () => {
    beforeEach(() => {
      // Mock paginated response
      cy.fixture('orders/orders').then((orderData) => {
        const paginatedResponse = {
          orders: orderData.orderHistory,
          total: 25,
          page: 1,
          totalPages: 3,
          hasNext: true,
          hasPrev: false
        };
        
        cy.intercept('GET', '/api/orders*', {
          body: paginatedResponse
        }).as('getPaginatedOrders');
      });
      
      cy.visit('/orders');
      cy.wait('@getPaginatedOrders');
    });

    it('should display pagination controls', () => {
      cy.get('[data-testid="pagination"]').should('be.visible');
      
      cy.get('[data-testid="page-info"]')
        .should('be.visible')
        .and('contain', 'Page 1 of 3');
      
      cy.get('[data-testid="total-orders"]')
        .should('be.visible')
        .and('contain', '25 orders');
    });

    it('should navigate to next page', () => {
      cy.get('[data-testid="next-page-button"]')
        .should('be.visible')
        .and('not.be.disabled');
      
      cy.get('[data-testid="next-page-button"]').click();
      
      cy.wait('@getPaginatedOrders');
      
      cy.url().should('include', 'page=2');
    });

    it('should navigate to previous page', () => {
      // Go to page 2 first
      cy.get('[data-testid="next-page-button"]').click();
      cy.wait('@getPaginatedOrders');
      
      cy.get('[data-testid="prev-page-button"]')
        .should('be.visible')
        .and('not.be.disabled');
      
      cy.get('[data-testid="prev-page-button"]').click();
      
      cy.wait('@getPaginatedOrders');
      
      cy.url().should('include', 'page=1');
    });

    it('should navigate to specific page', () => {
      cy.get('[data-testid="page-number-3"]').click();
      
      cy.wait('@getPaginatedOrders');
      
      cy.url().should('include', 'page=3');
      
      cy.get('[data-testid="page-info"]')
        .should('contain', 'Page 3 of 3');
    });

    it('should disable navigation buttons appropriately', () => {
      // On first page
      cy.get('[data-testid="prev-page-button"]')
        .should('be.disabled');
      
      cy.get('[data-testid="first-page-button"]')
        .should('be.disabled');
      
      // Go to last page
      cy.get('[data-testid="page-number-3"]').click();
      cy.wait('@getPaginatedOrders');
      
      cy.get('[data-testid="next-page-button"]')
        .should('be.disabled');
      
      cy.get('[data-testid="last-page-button"]')
        .should('be.disabled');
    });

    it('should change page size', () => {
      cy.get('[data-testid="page-size-select"]').select('20');
      
      cy.wait('@getPaginatedOrders');
      
      cy.url().should('include', 'limit=20');
    });
  });

  describe('Loading States', () => {
    it('should show loading state while fetching orders', () => {
      cy.intercept('GET', '/api/orders', (req) => {
        req.reply((res) => {
          res.delay(2000);
          res.send({ fixture: 'orders/orders.json' });
        });
      }).as('slowOrders');
      
      cy.visit('/orders');
      
      cy.get('[data-testid="orders-loading"]').should('be.visible');
      cy.get('[data-testid="loading-spinner"]').should('be.visible');
      
      cy.wait('@slowOrders');
      
      cy.get('[data-testid="orders-loading"]').should('not.exist');
    });

    it('should show loading state for search', () => {
      cy.visit('/orders');
      cy.wait('@getOrders');
      
      cy.intercept('GET', '/api/orders/search*', (req) => {
        req.reply((res) => {
          res.delay(1000);
          res.send({ fixture: 'orders/orders.json' });
        });
      }).as('slowSearch');
      
      cy.get('[data-testid="order-search-input"]').type('ORD-12345');
      cy.get('[data-testid="search-button"]').click();
      
      cy.get('[data-testid="search-loading"]').should('be.visible');
      
      cy.wait('@slowSearch');
      
      cy.get('[data-testid="search-loading"]').should('not.exist');
    });

    it('should show loading state for reorder action', () => {
      cy.visit('/orders');
      cy.wait('@getOrders');
      
      cy.intercept('POST', '/api/orders/*/reorder', (req) => {
        req.reply((res) => {
          res.delay(1500);
          res.send({ statusCode: 200 });
        });
      }).as('slowReorder');
      
      cy.get('[data-testid="order-card"]').first().within(() => {
        cy.get('[data-testid="reorder-button"]').click();
        
        cy.get('[data-testid="reorder-button"]')
          .should('be.disabled')
          .and('contain', 'Adding...');
      });
      
      cy.wait('@slowReorder');
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      cy.visit('/orders');
      cy.wait('@getOrders');
    });

    it('should handle orders loading error', () => {
      cy.intercept('GET', '/api/orders', {
        statusCode: 500,
        body: { error: 'Internal server error' }
      }).as('ordersError');
      
      cy.reload();
      cy.wait('@ordersError');
      
      cy.get('[data-testid="orders-error"]')
        .should('be.visible')
        .and('contain', 'Unable to load orders');
      
      cy.get('[data-testid="retry-orders-button"]').should('be.visible');
    });

    it('should retry failed requests', () => {
      cy.intercept('GET', '/api/orders', {
        statusCode: 500
      }).as('ordersError');
      
      cy.reload();
      cy.wait('@ordersError');
      
      // Set up successful retry
      cy.intercept('GET', '/api/orders', {
        fixture: 'orders/orders.json'
      }).as('ordersRetry');
      
      cy.get('[data-testid="retry-orders-button"]').click();
      
      cy.wait('@ordersRetry');
      
      cy.get('[data-testid="orders-error"]').should('not.exist');
      cy.get('[data-testid="order-list"]').should('be.visible');
    });

    it('should handle network errors gracefully', () => {
      cy.intercept('GET', '/api/orders/search*', {
        forceNetworkError: true
      }).as('networkError');
      
      cy.get('[data-testid="order-search-input"]').type('ORD-12345');
      cy.get('[data-testid="search-button"]').click();
      
      cy.wait('@networkError');
      
      cy.get('[data-testid="search-error"]')
        .should('be.visible')
        .and('contain', 'Network error');
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      cy.visit('/orders');
      cy.wait('@getOrders');
    });

    it('should be accessible', () => {
      cy.checkAccessibility();
    });

    it('should support keyboard navigation', () => {
      cy.checkKeyboardNavigation();
      
      // Test tab order through order cards
      cy.get('[data-testid="order-card"]').first().focus();
      cy.focused().should('have.attr', 'data-testid', 'order-card');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'view-details-button');
    });

    it('should have proper ARIA labels', () => {
      cy.get('[data-testid="order-search-input"]')
        .should('have.attr', 'aria-label')
        .and('contain', 'Search orders');
      
      cy.get('[data-testid="order-list"]')
        .should('have.attr', 'role', 'list');
      
      cy.get('[data-testid="order-card"]')
        .should('have.attr', 'role', 'listitem');
    });

    it('should announce filter changes to screen readers', () => {
      cy.get('[data-testid="filter-announcement"]')
        .should('have.attr', 'aria-live', 'polite');
      
      cy.get('[data-testid="status-filter"]').select('delivered');
      cy.wait('@filterOrders');
      
      cy.get('[data-testid="filter-announcement"]')
        .should('contain', 'Filtered by status: delivered');
    });
  });

  describe('Responsive Design', () => {
    it('should work on different screen sizes', () => {
      cy.testResponsiveDesign();
      
      // Test mobile layout
      cy.viewport(375, 667);
      cy.visit('/orders');
      cy.wait('@getOrders');
      
      cy.get('[data-testid="order-card"]')
        .should('have.css', 'flex-direction', 'column');
      
      cy.get('[data-testid="mobile-filters-toggle"]')
        .should('be.visible');
      
      // Test tablet layout
      cy.viewport(768, 1024);
      cy.get('[data-testid="order-list"]')
        .should('have.css', 'grid-template-columns')
        .and('match', /1fr/);
      
      // Test desktop layout
      cy.viewport(1280, 720);
      cy.get('[data-testid="order-list"]')
        .should('have.css', 'grid-template-columns')
        .and('match', /repeat/);
    });

    it('should show mobile-optimized filters', () => {
      cy.viewport(375, 667);
      cy.visit('/orders');
      cy.wait('@getOrders');
      
      cy.get('[data-testid="mobile-filters-toggle"]').click();
      
      cy.get('[data-testid="mobile-filters-drawer"]')
        .should('be.visible');
      
      cy.get('[data-testid="close-filters-button"]').click();
      
      cy.get('[data-testid="mobile-filters-drawer"]')
        .should('not.be.visible');
    });
  });

  describe('Performance', () => {
    it('should load order history page quickly', () => {
      cy.visit('/orders');
      cy.measurePageLoadTime();
    });

    it('should optimize images', () => {
      cy.visit('/orders');
      cy.wait('@getOrders');
      cy.checkImageOptimization();
    });

    it('should implement virtual scrolling for large lists', () => {
      // Mock large order list
      cy.fixture('orders/orders').then((orderData) => {
        const largeOrderList = {
          orders: Array(100).fill(orderData.orderHistory[0]),
          total: 100,
          page: 1,
          totalPages: 5
        };
        
        cy.intercept('GET', '/api/orders', {
          body: largeOrderList
        }).as('getLargeOrderList');
      });
      
      cy.visit('/orders');
      cy.wait('@getLargeOrderList');
      
      // Check that not all items are rendered at once
      cy.get('[data-testid="order-card"]')
        .should('have.length.lessThan', 100);
      
      // Check virtual scrolling container
      cy.get('[data-testid="virtual-scroll-container"]')
        .should('be.visible');
    });

    it('should debounce search input', () => {
      cy.visit('/orders');
      cy.wait('@getOrders');
      
      // Rapidly type in search field
      cy.get('[data-testid="order-search-input"]')
        .type('O')
        .type('R')
        .type('D')
        .type('-')
        .type('1');
      
      // Should only make one search call after debounce
      cy.wait('@searchOrders');
      cy.get('@searchOrders.all').should('have.length', 1);
    });
  });

  describe('Security', () => {
    beforeEach(() => {
      cy.visit('/orders');
      cy.wait('@getOrders');
    });

    it('should not expose sensitive payment information', () => {
      cy.get('body').should('not.contain', '****************');
      cy.get('body').should('not.contain', 'CVV');
      
      // Should only show masked card numbers
      cy.get('[data-testid="payment-details"]')
        .should('contain', '**** 1111');
    });

    it('should validate user ownership of orders', () => {
      // This is handled by the API interceptor setup
      // In real implementation, the API should only return user's orders
      cy.get('[data-testid="order-list"]').should('be.visible');
    });

    it('should use secure communication', () => {
      cy.get('[data-testid="order-search-input"]').type('ORD-12345');
      cy.get('[data-testid="search-button"]').click();
      
      cy.wait('@searchOrders').then((interception) => {
        expect(interception.request.url).to.include('https://');
      });
    });
  });
});