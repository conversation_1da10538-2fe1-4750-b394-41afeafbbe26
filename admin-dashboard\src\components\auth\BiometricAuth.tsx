'use client'

import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { 
  Fingerprint, 
  Shield, 
  Smartphone, 
  Key, 
  AlertTriangle,
  CheckCircle,
  Loader2,
  Eye,
  Scan
} from 'lucide-react'

import { webAuthnService, type BiometricCapabilities } from '@/services/webauthn-service'
import { riskBasedAuthService, type RiskAssessment, type DeviceFingerprint } from '@/services/risk-based-auth-service'
import { GlassButton as Button } from '@/components/ui/glass-components'
import { GlassCard as Card, GlassCardContent as CardContent } from '@/components/ui/glass-components'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface BiometricAuthProps {
  onSuccess: (result: { userId: string; credentialId: string; method: string }) => void
  onError: (error: string) => void
  userEmail?: string
  allowFallback?: boolean
}

interface AuthenticationState {
  isLoading: boolean
  isScanning: boolean
  capabilities: BiometricCapabilities | null
  deviceFingerprint: DeviceFingerprint | null
  riskAssessment: RiskAssessment | null
  availableMethods: string[]
  selectedMethod: string | null
  error: string | null
  success: boolean
}

export default function BiometricAuth({ 
  onSuccess, 
  onError, 
  userEmail,
  allowFallback = true 
}: BiometricAuthProps) {
  const { t } = useTranslation()
  const [state, setState] = useState<AuthenticationState>({
    isLoading: true,
    isScanning: false,
    capabilities: null,
    deviceFingerprint: null,
    riskAssessment: null,
    availableMethods: [],
    selectedMethod: null,
    error: null,
    success: false,
  })

  // Initialize biometric capabilities and risk assessment
  useEffect(() => {
    initializeBiometricAuth()
  }, [])

  const initializeBiometricAuth = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }))

      // Get biometric capabilities
      const capabilities = await webAuthnService.getBiometricCapabilities()

      // Generate device fingerprint
      const deviceFingerprint = await riskBasedAuthService.generateDeviceFingerprint()

      // Assess risk
      const riskAssessment = await riskBasedAuthService.assessLoginRisk(
        deviceFingerprint,
        true, // Assume new device for now
        true, // Assume new location for now
        0     // No failed attempts initially
      )

      // Determine available authentication methods
      const availableMethods = getAvailableAuthMethods(capabilities, riskAssessment)

      setState(prev => ({
        ...prev,
        isLoading: false,
        capabilities,
        deviceFingerprint,
        riskAssessment,
        availableMethods,
        selectedMethod: availableMethods[0] || null,
      }))

    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to initialize biometric authentication',
      }))
    }
  }

  const getAvailableAuthMethods = (
    capabilities: BiometricCapabilities,
    riskAssessment: RiskAssessment
  ): string[] => {
    const methods: string[] = []

    // Add WebAuthn methods based on capabilities
    if (capabilities.webAuthnSupported) {
      if (capabilities.availableAuthenticators.includes('touch-id')) {
        methods.push('touch-id')
      }
      if (capabilities.availableAuthenticators.includes('face-id')) {
        methods.push('face-id')
      }
      if (capabilities.availableAuthenticators.includes('windows-hello')) {
        methods.push('windows-hello')
      }
      if (capabilities.availableAuthenticators.includes('android-biometric')) {
        methods.push('android-biometric')
      }
      if (capabilities.webAuthnSupported && !methods.length) {
        methods.push('webauthn') // Generic WebAuthn
      }
    }

    // Filter methods based on risk assessment
    const allowedMethods = riskAssessment.allowedMethods
    return methods.filter(method => 
      allowedMethods.includes('webauthn') || 
      allowedMethods.includes('biometric') ||
      allowedMethods.includes(method)
    )
  }

  const handleBiometricAuth = async (method: string) => {
    if (!state.capabilities?.webAuthnSupported) {
      onError('Biometric authentication is not supported on this device')
      return
    }

    setState(prev => ({ ...prev, isScanning: true, error: null }))

    try {
      // Perform WebAuthn authentication
      const result = await webAuthnService.authenticateWithWebAuthn({
        userVerification: 'required',
        timeout: 60000,
      })

      if (result.success && result.userId && result.credentialId) {
        setState(prev => ({ ...prev, success: true, isScanning: false }))
        onSuccess({
          userId: result.userId,
          credentialId: result.credentialId,
          method,
        })
      } else {
        throw new Error(result.error || 'Authentication failed')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Biometric authentication failed'
      setState(prev => ({ ...prev, error: errorMessage, isScanning: false }))
      onError(errorMessage)
    }
  }

  const handleConditionalAuth = async () => {
    if (!state.capabilities?.conditionalMediationSupported) {
      onError('Conditional authentication is not supported')
      return
    }

    setState(prev => ({ ...prev, isScanning: true, error: null }))

    try {
      const result = await webAuthnService.authenticateWithConditionalUI()

      if (result.success && result.userId && result.credentialId) {
        setState(prev => ({ ...prev, success: true, isScanning: false }))
        onSuccess({
          userId: result.userId,
          credentialId: result.credentialId,
          method: 'conditional-ui',
        })
      } else {
        throw new Error(result.error || 'Authentication failed')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Conditional authentication failed'
      setState(prev => ({ ...prev, error: errorMessage, isScanning: false }))
      onError(errorMessage)
    }
  }

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'touch-id':
        return <Fingerprint className="h-6 w-6" />
      case 'face-id':
        return <Eye className="h-6 w-6" />
      case 'windows-hello':
        return <Shield className="h-6 w-6" />
      case 'android-biometric':
        return <Scan className="h-6 w-6" />
      case 'webauthn':
        return <Key className="h-6 w-6" />
      default:
        return <Fingerprint className="h-6 w-6" />
    }
  }

  const getMethodName = (method: string) => {
    switch (method) {
      case 'touch-id':
        return 'Touch ID'
      case 'face-id':
        return 'Face ID'
      case 'windows-hello':
        return 'Windows Hello'
      case 'android-biometric':
        return 'Android Biometric'
      case 'webauthn':
        return 'Security Key'
      default:
        return 'Biometric'
    }
  }

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low':
        return 'bg-green-500/10 text-green-400 border-green-500/20'
      case 'medium':
        return 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20'
      case 'high':
        return 'bg-orange-500/10 text-orange-400 border-orange-500/20'
      case 'critical':
        return 'bg-red-500/10 text-red-400 border-red-500/20'
      default:
        return 'bg-gray-500/10 text-gray-400 border-gray-500/20'
    }
  }

  if (state.isLoading) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="p-6">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
            <p className="text-sm text-gray-400">Initializing biometric authentication...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (state.riskAssessment?.riskLevel === 'critical') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="p-6">
          <Alert className="border-red-500/20 bg-red-500/10">
            <AlertTriangle className="h-4 w-4 text-red-400" />
            <AlertDescription className="text-red-400">
              Authentication blocked due to high security risk. Please contact support.
            </AlertDescription>
          </Alert>
          
          {state.riskAssessment.factors.length > 0 && (
            <div className="mt-4">
              <p className="text-sm text-gray-400 mb-2">Risk factors detected:</p>
              <ul className="text-xs text-gray-500 space-y-1">
                {state.riskAssessment.factors.map((factor: string, index: number) => (
                  <li key={index}>• {factor}</li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 rounded-full bg-blue-500/10 border border-blue-500/20">
                <Shield className="h-8 w-8 text-blue-400" />
              </div>
            </div>
            <h2 className="text-xl font-semibold text-white mb-2">
              Secure Authentication
            </h2>
            <p className="text-sm text-gray-400">
              Use your biometric authentication to sign in securely
            </p>
          </div>

          {/* Risk Assessment Display */}
          {state.riskAssessment && (
            <div className="flex items-center justify-between p-3 rounded-lg bg-gray-800/50 border border-gray-700/50">
              <span className="text-sm text-gray-400">Security Level:</span>
              <Badge className={getRiskLevelColor(state.riskAssessment.riskLevel)}>
                {state.riskAssessment.riskLevel.toUpperCase()}
              </Badge>
            </div>
          )}

          {/* Available Methods */}
          {state.availableMethods.length > 0 ? (
            <div className="space-y-3">
              <p className="text-sm text-gray-400 font-medium">Available Methods:</p>
              
              {state.availableMethods.map((method) => (
                <Button
                  key={method}
                  onClick={() => handleBiometricAuth(method)}
                  disabled={state.isScanning}
                  className="w-full flex items-center justify-center space-x-3 p-4 h-auto"
                  variant="secondary"
                >
                  {state.isScanning && state.selectedMethod === method ? (
                    <Loader2 className="h-5 w-5 animate-spin" />
                  ) : (
                    getMethodIcon(method)
                  )}
                  <span>{getMethodName(method)}</span>
                </Button>
              ))}

              {/* Conditional UI Authentication */}
              {state.capabilities?.conditionalMediationSupported && (
                <Button
                  onClick={handleConditionalAuth}
                  disabled={state.isScanning}
                  className="w-full flex items-center justify-center space-x-3 p-4 h-auto"
                  variant="secondary"
                >
                  {state.isScanning ? (
                    <Loader2 className="h-5 w-5 animate-spin" />
                  ) : (
                    <Smartphone className="h-5 w-5" />
                  )}
                  <span>Use Saved Passkey</span>
                </Button>
              )}
            </div>
          ) : (
            <Alert className="border-yellow-500/20 bg-yellow-500/10">
              <AlertTriangle className="h-4 w-4 text-yellow-400" />
              <AlertDescription className="text-yellow-400">
                No biometric authentication methods are available on this device.
              </AlertDescription>
            </Alert>
          )}

          {/* Error Display */}
          {state.error && (
            <Alert className="border-red-500/20 bg-red-500/10">
              <AlertTriangle className="h-4 w-4 text-red-400" />
              <AlertDescription className="text-red-400">
                {state.error}
              </AlertDescription>
            </Alert>
          )}

          {/* Success Display */}
          {state.success && (
            <Alert className="border-green-500/20 bg-green-500/10">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <AlertDescription className="text-green-400">
                Authentication successful! Redirecting...
              </AlertDescription>
            </Alert>
          )}

          {/* Fallback Option */}
          {allowFallback && (
            <div className="pt-4 border-t border-gray-700/50">
              <p className="text-xs text-gray-500 text-center">
                Having trouble? You can use traditional login methods as a fallback.
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 