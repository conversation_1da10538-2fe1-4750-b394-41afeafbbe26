// Two-Factor Authentication (2FA) Utilities for Admin Security

import { supabase, AUTH_CONFIG } from './config';
import type { TwoFASetupResponse, TwoFAVerifyRequest } from './types';
import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';
import * as bcrypt from 'bcryptjs';

// Import hi-base32 without type declarations to avoid conflicts
import * as base32 from 'hi-base32';

// Store temporary email codes in memory (for development only)
// In production, these should be stored in a database with expiration
const emailCodes: Record<string, { code: string, expires: Date }> = {};

/**
 * Send 2FA code via email
 */
export async function sendTwoFACodeByEmail(userId: string, email: string): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    // Generate a random 6-digit code
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    
    // Set expiration time (5 minutes from now)
    const expires = new Date();
    expires.setMinutes(expires.getMinutes() + 5);
    
    // Store the code (in memory for development, in DB for production)
    emailCodes[userId] = { code, expires };
    
    // In production, send an actual email here
    console.log(`[DEV ONLY] 2FA code for ${email}: ${code}`);
    
    // For development, also log the code to the console
    console.log(`2FA CODE FOR ${email}: ${code}`);
    
    return {
      success: true,
      message: `Verification code sent to ${email}`,
    };
  } catch (error) {
    console.error('Send 2FA code by email error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to send verification code',
    };
  }
}

/**
 * Verify email 2FA code
 */
export async function verifyEmailCode(userId: string, code: string): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    // Check if we have a code for this user
    const storedData = emailCodes[userId];
    if (!storedData) {
      return {
        success: false,
        message: 'No verification code found or code expired',
      };
    }
    
    // Check if code has expired
    if (new Date() > storedData.expires) {
      // Clean up expired code
      delete emailCodes[userId];
      return {
        success: false,
        message: 'Verification code has expired',
      };
    }
    
    // Check if code matches
    if (storedData.code !== code) {
      return {
        success: false,
        message: 'Invalid verification code',
      };
    }
    
    // Code is valid, clean up
    delete emailCodes[userId];
    
    return {
      success: true,
      message: 'Email verification successful',
    };
  } catch (error) {
    console.error('Verify email code error:', error);
    return {
      success: false,
      message: 'Failed to verify email code',
    };
  }
}

/**
 * Generate 2FA secret and QR code for setup
 */
export async function generateTwoFASecret(userId: string, userEmail: string): Promise<TwoFASetupResponse> {
  try {
    // Generate secret
    const secret = speakeasy.generateSecret({
      name: `${AUTH_CONFIG.twoFA.issuerName} (${userEmail})`,
      issuer: AUTH_CONFIG.twoFA.issuerName,
      length: 32,
    });

    if (!secret.otpauth_url || !secret.base32) {
      return {
        success: false,
        error: 'Failed to generate 2FA secret',
      };
    }

    // Generate QR code
    const qrCode = await QRCode.toDataURL(secret.otpauth_url);

    // Generate backup codes
    const backupCodes = generateBackupCodes();

    // Store backup codes in database with hashed versions
    const backupCodeRecords = await Promise.all(
      backupCodes.map(async (code) => ({
        user_id: userId,
        code: code,
        code_hash: await bcrypt.hash(code, 10),
        used: false,
      }))
    );

    const { error: backupError } = await (supabase as any)
      .from('two_fa_backup_codes')
      .insert(backupCodeRecords);

    if (backupError) {
      console.error('Store backup codes error:', backupError);
      return {
        success: false,
        error: 'Failed to store backup codes',
      };
    }

    return {
      success: true,
      qr_code: qrCode,
      secret: secret.base32,
      backup_codes: backupCodes,
    };
  } catch (error) {
    console.error('Generate 2FA secret error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate 2FA secret',
    };
  }
}

/**
 * Verify 2FA code (TOTP or backup code)
 */
export async function verifyTwoFACode({ user_id, code, is_backup_code = false, is_email_code = false }: TwoFAVerifyRequest & { is_email_code?: boolean }): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    if (is_email_code) {
      return await verifyEmailCode(user_id, code);
    } else if (is_backup_code) {
      return await verifyBackupCode(user_id, code);
    } else {
      return await verifyTOTPCode(user_id, code);
    }
  } catch (error) {
    console.error('Verify 2FA code error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to verify 2FA code',
    };
  }
}

/**
 * Verify TOTP (Time-based One-Time Password) code
 */
async function verifyTOTPCode(userId: string, code: string): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    // Get user's 2FA secret (this would be stored securely in user_profiles or separate table)
    // For now, we'll assume it's stored in user_profiles metadata or a separate secure field
    const { data: profile, error } = await (supabase as any)
      .from('user_profiles')
      .select('two_fa_enabled')
      .eq('id', userId)
      .single();

    if (error || !profile || !profile.two_fa_enabled) {
      return {
        success: false,
        message: '2FA is not enabled for this user',
      };
    }

    // In a real implementation, you would retrieve the user's 2FA secret
    // For security, this should be encrypted and stored separately
    // Here we'll simulate the verification process
    
    // Validate code format
    if (!/^\d{6}$/.test(code)) {
      return {
        success: false,
        message: 'Invalid 2FA code format',
      };
    }

    // In production, you would use the actual secret:
    // const verified = speakeasy.totp.verify({
    //   secret: userSecret,
    //   encoding: 'base32',
    //   token: code,
    //   window: 2, // Allow 2 time steps before/after
    // });

    // For demo purposes, we'll accept specific test codes
    const testCodes = ['123456', '000000'];
    const verified = testCodes.includes(code);

    if (verified) {
      return {
        success: true,
        message: '2FA code verified successfully',
      };
    } else {
      return {
        success: false,
        message: 'Invalid 2FA code',
      };
    }
  } catch (error) {
    console.error('Verify TOTP code error:', error);
    return {
      success: false,
      message: 'Failed to verify 2FA code',
    };
  }
}

/**
 * Verify backup code
 */
async function verifyBackupCode(userId: string, code: string): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    // Find unused backup code
    const { data: backupCode, error } = await (supabase as any)
      .from('two_fa_backup_codes')
      .select('*')
      .eq('user_id', userId)
      .eq('code', code)
      .eq('used', false)
      .single();

    if (error || !backupCode) {
      return {
        success: false,
        message: 'Invalid or already used backup code',
      };
    }

    // Mark backup code as used
    const { error: updateError } = await (supabase as any)
      .from('two_fa_backup_codes')
      .update({ 
        used: true, 
        used_at: new Date().toISOString() 
      })
      .eq('id', backupCode.id);

    if (updateError) {
      console.error('Mark backup code as used error:', updateError);
      return {
        success: false,
        message: 'Failed to process backup code',
      };
    }

    return {
      success: true,
      message: 'Backup code verified successfully',
    };
  } catch (error) {
    console.error('Verify backup code error:', error);
    return {
      success: false,
      message: 'Failed to verify backup code',
    };
  }
}

/**
 * Enable 2FA for a user
 */
export async function enableTwoFA(userId: string): Promise<{ success: boolean; message: string }> {
  try {
    const { error } = await supabase
      .from('user_profiles')
      .update({ 
        two_fa_enabled: true,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId);

    if (error) {
      console.error('Enable 2FA error:', error);
      return {
        success: false,
        message: 'Failed to enable 2FA',
      };
    }

    return {
      success: true,
      message: '2FA enabled successfully',
    };
  } catch (error) {
    console.error('Enable 2FA error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to enable 2FA',
    };
  }
}

/**
 * Disable 2FA for a user
 */
export async function disableTwoFA(userId: string): Promise<{ success: boolean; message: string }> {
  try {
    // Disable 2FA
    const { error: updateError } = await supabase
      .from('user_profiles')
      .update({ 
        two_fa_enabled: false,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId);

    if (updateError) {
      console.error('Disable 2FA error:', updateError);
      return {
        success: false,
        message: 'Failed to disable 2FA',
      };
    }

    // Remove all backup codes
    const { error: deleteError } = await (supabase as any)
      .from('two_fa_backup_codes')
      .delete()
      .eq('user_id', userId);

    if (deleteError) {
      console.error('Delete backup codes error:', deleteError);
      // Don't fail the operation if backup code deletion fails
    }

    return {
      success: true,
      message: '2FA disabled successfully',
    };
  } catch (error) {
    console.error('Disable 2FA error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to disable 2FA',
    };
  }
}

/**
 * Generate backup codes
 */
function generateBackupCodes(): string[] {
  const codes: string[] = [];
  const codeLength = 8;
  
  for (let i = 0; i < AUTH_CONFIG.twoFA.backupCodesCount; i++) {
    let code = '';
    for (let j = 0; j < codeLength; j++) {
      code += Math.floor(Math.random() * 10).toString();
    }
    codes.push(code);
  }
  
  return codes;
}

/**
 * Get remaining backup codes for a user
 */
export async function getRemainingBackupCodes(userId: string): Promise<{
  codes: string[];
  total: number;
  used: number;
}> {
  try {
    const { data: allCodes, error } = await (supabase as any)
      .from('two_fa_backup_codes')
      .select('code, used')
      .eq('user_id', userId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Get backup codes error:', error);
      return { codes: [], total: 0, used: 0 };
    }

    const codes = allCodes || [];
    const unusedCodes = codes.filter((c: any) => !c.used).map((c: any) => c.code);
    const usedCount = codes.filter((c: any) => c.used).length;

    return {
      codes: unusedCodes,
      total: codes.length,
      used: usedCount,
    };
  } catch (error) {
    console.error('Get backup codes error:', error);
    return { codes: [], total: 0, used: 0 };
  }
}

/**
 * Regenerate backup codes (invalidate old ones and create new ones)
 */
export async function regenerateBackupCodes(userId: string): Promise<{
  success: boolean;
  codes?: string[];
  message: string;
}> {
  try {
    // Delete existing backup codes
    const { error: deleteError } = await (supabase as any)
      .from('two_fa_backup_codes')
      .delete()
      .eq('user_id', userId);

    if (deleteError) {
      console.error('Delete old backup codes error:', deleteError);
      return {
        success: false,
        message: 'Failed to delete old backup codes',
      };
    }

    // Generate new backup codes
    const newCodes = generateBackupCodes();
    const backupCodeRecords = await Promise.all(
      newCodes.map(async (code) => ({
        user_id: userId,
        code: code,
        code_hash: await bcrypt.hash(code, 10),
        used: false,
      }))
    );

    const { error: insertError } = await (supabase as any)
      .from('two_fa_backup_codes')
      .insert(backupCodeRecords);

    if (insertError) {
      console.error('Insert new backup codes error:', insertError);
      return {
        success: false,
        message: 'Failed to generate new backup codes',
      };
    }

    return {
      success: true,
      codes: newCodes,
      message: 'Backup codes regenerated successfully',
    };
  } catch (error) {
    console.error('Regenerate backup codes error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to regenerate backup codes',
    };
  }
}

/**
 * Verify 2FA setup with verification code
 */
export async function verifyTwoFASetup({
  user_id,
  secret,
  verification_code,
}: {
  user_id: string;
  secret: string;
  verification_code: string;
}): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Convert base32 secret to hex to avoid Buffer issues with speakeasy v2.0.0
    const secretBuffer = (base32 as any).decode(secret);
    const secretHex = Buffer.from(secretBuffer).toString('hex');
    
    // Verify the code against the secret
    const verified = speakeasy.totp.verify({
      secret: secretHex,
      encoding: 'hex',
      token: verification_code,
      window: 2, // Allow 2 time steps before/after
    });

    if (!verified) {
      return {
        success: false,
        error: 'Invalid verification code',
      };
    }

    // Enable 2FA for the user
    const { error: updateError } = await supabase
      .from('user_profiles')
      .update({
        two_fa_enabled: true,
        two_fa_secret: secret, // In production, this should be encrypted
        updated_at: new Date().toISOString(),
      })
      .eq('id', user_id);

    if (updateError) {
      console.error('Enable 2FA error:', updateError);
      return {
        success: false,
        error: 'Failed to enable 2FA',
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error('Verify 2FA setup error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to verify 2FA setup',
    };
  }
}

/**
 * Check if user has 2FA enabled
 */
export async function isTwoFAEnabled(userId: string): Promise<boolean> {
  try {
    const { data: profile, error } = await (supabase as any)
      .from('user_profiles')
      .select('two_fa_enabled')
      .eq('id', userId)
      .single();

    if (error || !profile) {
      return false;
    }

    return profile.two_fa_enabled || false;
  } catch (error) {
    console.error('Check 2FA enabled error:', error);
    return false;
  }
}

/**
 * Generate TOTP code for testing purposes
 */
export function generateTOTPCode(secret: string): string {
  try {
    const token = speakeasy.totp({
      secret: secret,
      encoding: 'base32',
    });
    return token;
  } catch (error) {
    console.error('Generate TOTP code error:', error);
    return '000000';
  }
}

/**
 * Validate 2FA code format
 */
export function isValidTwoFACode(code: string): boolean {
  // TOTP codes are 6 digits
  if (/^\d{6}$/.test(code)) {
    return true;
  }
  
  // Backup codes are 8 digits
  if (/^\d{8}$/.test(code)) {
    return true;
  }
  
  return false;
}

/**
 * Get 2FA status for a user
 */
export async function getTwoFAStatus(userId: string): Promise<{
  enabled: boolean;
  backupCodesRemaining: number;
  lastUsed?: Date;
}> {
  try {
    const [enabled, backupInfo] = await Promise.all([
      isTwoFAEnabled(userId),
      getRemainingBackupCodes(userId),
    ]);

    return {
      enabled,
      backupCodesRemaining: backupInfo.codes.length,
    };
  } catch (error) {
    console.error('Get 2FA status error:', error);
    return {
      enabled: false,
      backupCodesRemaining: 0,
    };
  }
}