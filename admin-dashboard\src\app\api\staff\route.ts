import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    // Extract query parameters
    const search = searchParams.get('search')
    const status = searchParams.get('status') // 'active', 'inactive', 'all'
    const role = searchParams.get('role')
    const sort_by = searchParams.get('sort_by') || 'created_at'
    const sort_order = searchParams.get('sort_order') || 'desc'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    
    // Calculate pagination
    const offset = (page - 1) * limit
    
    let query = supabase
      .from('staff')
      .select(`
        *,
        role:roles(
          id,
          name,
          display_name
        )
      `, { count: 'exact' })
    
    // Apply filters
    if (search) {
      query = query.or(`first_name.ilike.%${search}%,last_name.ilike.%${search}%,email.ilike.%${search}%,staff_code.ilike.%${search}%`)
    }
    
    if (status && status !== 'all') {
      const isActive = status === 'active'
      query = query.eq('is_active', isActive)
    }
    
    if (role) {
      query = query.eq('role_id', role)
    }
    
    // Apply sorting
    query = query.order(sort_by, { ascending: sort_order === 'asc' })
    
    // Apply pagination
    query = query.range(offset, offset + limit - 1)
    
    const { data: staff, error, count } = await query
    
    if (error) {
      console.error('Error fetching staff:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch staff members' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: staff || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })
    
  } catch (error) {
    console.error('Error in staff GET route:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}