import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase';
import { UpdateDeliveryZoneRequest } from '@/types/delivery-zones';

interface RouteParams {
  params: {
    id: string;
  };
}

// GET /api/delivery-zones/[id] - Fetch a specific delivery zone
export async function GET(
  _request: NextRequest,
  { params }: RouteParams
) {
  const supabase = createServerSupabaseClient();
  
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { error: 'Zone ID is required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('delivery_zones')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Delivery zone not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(
        { error: 'Failed to fetch delivery zone' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH /api/delivery-zones/[id] - Update a specific delivery zone
export async function PATCH(
  request: NextRequest,
  { params }: RouteParams
) {
  const supabase = createServerSupabaseClient();
  
  try {
    const { id } = params;
    const body: UpdateDeliveryZoneRequest = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Zone ID is required' },
        { status: 400 }
      );
    }

    // Validate polygon coordinates if provided
    if (body.polygon_coordinates) {
      if (body.polygon_coordinates.length < 3) {
        return NextResponse.json(
          { error: 'Polygon must have at least 3 coordinates' },
          { status: 400 }
        );
      }

      const isValidCoordinates = body.polygon_coordinates.every(
        coord => 
          typeof coord.lat === 'number' && 
          typeof coord.lng === 'number' &&
          coord.lat >= -90 && coord.lat <= 90 &&
          coord.lng >= -180 && coord.lng <= 180
      );

      if (!isValidCoordinates) {
        return NextResponse.json(
          { error: 'Invalid polygon coordinates' },
          { status: 400 }
        );
      }
    }

    // Validate numeric fields
    if (body.delivery_fee !== undefined && (body.delivery_fee < 0 || isNaN(body.delivery_fee))) {
      return NextResponse.json(
        { error: 'Delivery fee must be a non-negative number' },
        { status: 400 }
      );
    }

    if (body.minimum_order_amount !== undefined && (body.minimum_order_amount < 0 || isNaN(body.minimum_order_amount))) {
      return NextResponse.json(
        { error: 'Minimum order amount must be a non-negative number' },
        { status: 400 }
      );
    }

    if (body.estimated_delivery_time_min !== undefined && (body.estimated_delivery_time_min < 1 || isNaN(body.estimated_delivery_time_min))) {
      return NextResponse.json(
        { error: 'Estimated delivery time min must be a positive number' },
        { status: 400 }
      );
    }

    if (body.estimated_delivery_time_max !== undefined && (body.estimated_delivery_time_max < 1 || isNaN(body.estimated_delivery_time_max))) {
      return NextResponse.json(
        { error: 'Estimated delivery time max must be a positive number' },
        { status: 400 }
      );
    }

    // Check if zone exists
    const { data: _existingZone, error: fetchError } = await supabase
      .from('delivery_zones')
      .select('id')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Delivery zone not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(
        { error: 'Failed to update delivery zone' },
        { status: 500 }
      );
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    if (body.name !== undefined) updateData.name = body.name;
    if (body.polygon_coordinates !== undefined) updateData.polygon_coordinates = body.polygon_coordinates;
    if (body.delivery_fee !== undefined) updateData.delivery_fee = body.delivery_fee;
    if (body.minimum_order_amount !== undefined) updateData.minimum_order_amount = body.minimum_order_amount;
    if (body.estimated_delivery_time_min !== undefined) updateData.estimated_delivery_time_min = body.estimated_delivery_time_min;
    if (body.estimated_delivery_time_max !== undefined) updateData.estimated_delivery_time_max = body.estimated_delivery_time_max;
    if (body.is_active !== undefined) updateData.is_active = body.is_active;

    // Update the delivery zone
    const { data, error } = await supabase
      .from('delivery_zones')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      return NextResponse.json(
        { error: 'Failed to update delivery zone' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/delivery-zones/[id] - Delete a specific delivery zone
export async function DELETE(
  _request: NextRequest,
  { params }: RouteParams
) {
  const supabase = createServerSupabaseClient();
  
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { error: 'Zone ID is required' },
        { status: 400 }
      );
    }

    // Check if zone exists
    const { data: _existingZone, error: fetchError } = await supabase
      .from('delivery_zones')
      .select('id, name')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Delivery zone not found' },
          { status: 404 }
        );
      }
      return NextResponse.json(
        { error: 'Failed to delete delivery zone' },
        { status: 500 }
      );
    }

    // Delete the delivery zone
    const { error } = await supabase
      .from('delivery_zones')
      .delete()
      .eq('id', id);

    if (error) {
      return NextResponse.json(
        { error: 'Failed to delete delivery zone' },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: `Delivery zone "${_existingZone.name}" deleted successfully` },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}