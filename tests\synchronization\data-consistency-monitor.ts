/**
 * Data Consistency Monitor
 * Real-time monitoring and validation of data consistency across all platforms
 */

import { createClient } from '@supabase/supabase-js';
import { EventEmitter } from 'events';

export interface ConsistencyRule {
  id: string;
  name: string;
  description: string;
  table: string;
  fields: string[];
  platforms: string[];
  criticalLevel: 'high' | 'medium' | 'low';
  enabled: boolean;
}

export interface ConsistencyViolation {
  ruleId: string;
  ruleName: string;
  table: string;
  field: string;
  recordId: string;
  expectedValue: any;
  actualValues: Record<string, any>;
  detectedAt: Date;
  severity: 'critical' | 'warning' | 'info';
  platforms: string[];
}

export interface ConsistencyReport {
  generatedAt: Date;
  totalRules: number;
  activeRules: number;
  violations: ConsistencyViolation[];
  platformStatus: Record<string, 'online' | 'offline' | 'degraded'>;
  recommendations: string[];
}

export class DataConsistencyMonitor extends EventEmitter {
  private supabase: any;
  private rules: Map<string, ConsistencyRule>;
  private violations: ConsistencyViolation[];
  private isMonitoring: boolean;
  private monitoringInterval: NodeJS.Timeout | null;
  private platforms: Map<string, any>;

  constructor(supabaseUrl: string, supabaseKey: string) {
    super();
    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.rules = new Map();
    this.violations = [];
    this.isMonitoring = false;
    this.monitoringInterval = null;
    this.platforms = new Map();

    this.initializeDefaultRules();
  }

  /**
   * Initialize default consistency rules
   */
  private initializeDefaultRules(): void {
    const defaultRules: ConsistencyRule[] = [
      {
        id: 'menu-price-consistency',
        name: 'Menu Price Consistency',
        description: 'Ensure menu item prices are consistent across all platforms',
        table: 'subcategories',
        fields: ['price', 'is_available'],
        platforms: ['admin', 'pos', 'web', 'mobile'],
        criticalLevel: 'high',
        enabled: true
      },
      {
        id: 'tax-rate-consistency',
        name: 'Tax Rate Consistency',
        description: 'Ensure tax rates are synchronized across POS and admin',
        table: 'pos_configurations',
        fields: ['sales_tax_rate', 'service_tax_rate'],
        platforms: ['admin', 'pos'],
        criticalLevel: 'high',
        enabled: true
      },
      {
        id: 'payment-settings-consistency',
        name: 'Payment Settings Consistency',
        description: 'Ensure payment configurations are consistent',
        table: 'payment_settings',
        fields: ['accept_cash', 'accept_card', 'accept_mobile', 'minimum_card_amount'],
        platforms: ['admin', 'pos', 'web', 'mobile'],
        criticalLevel: 'high',
        enabled: true
      },
      {
        id: 'operating-hours-consistency',
        name: 'Operating Hours Consistency',
        description: 'Ensure operating hours are consistent on customer-facing platforms',
        table: 'restaurant_settings',
        fields: ['monday_open', 'monday_close', 'tuesday_open', 'tuesday_close', 'wednesday_open', 'wednesday_close'],
        platforms: ['admin', 'web', 'mobile'],
        criticalLevel: 'medium',
        enabled: true
      },
      {
        id: 'staff-permissions-consistency',
        name: 'Staff Permissions Consistency',
        description: 'Ensure staff permissions are synchronized',
        table: 'staff_members',
        fields: ['can_process_refunds', 'can_modify_prices', 'can_access_reports'],
        platforms: ['admin', 'pos'],
        criticalLevel: 'high',
        enabled: true
      },
      {
        id: 'feature-flags-consistency',
        name: 'Feature Flags Consistency',
        description: 'Ensure feature flags are properly applied across platforms',
        table: 'feature_flags',
        fields: ['enabled', 'platforms'],
        platforms: ['admin', 'pos', 'web', 'mobile'],
        criticalLevel: 'medium',
        enabled: true
      }
    ];

    defaultRules.forEach(rule => this.rules.set(rule.id, rule));
  }

  /**
   * Start real-time monitoring
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    
    // Set up real-time listeners for all monitored tables
    const tables = Array.from(new Set(Array.from(this.rules.values()).map(rule => rule.table)));
    
    for (const table of tables) {
      await this.setupTableListener(table);
    }

    // Start periodic consistency checks
    this.monitoringInterval = setInterval(() => {
      this.performConsistencyCheck();
    }, 30000); // Check every 30 seconds

    this.emit('monitoring-started');
  }

  /**
   * Stop monitoring
   */
  async stopMonitoring(): Promise<void> {
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    // Clean up listeners would go here
    this.emit('monitoring-stopped');
  }

  /**
   * Add a custom consistency rule
   */
  addRule(rule: ConsistencyRule): void {
    this.rules.set(rule.id, rule);
    
    if (this.isMonitoring) {
      // Set up listener for this table if not already monitored
      this.setupTableListener(rule.table);
    }
    
    this.emit('rule-added', rule);
  }

  /**
   * Remove a consistency rule
   */
  removeRule(ruleId: string): void {
    const rule = this.rules.get(ruleId);
    if (rule) {
      this.rules.delete(ruleId);
      this.emit('rule-removed', rule);
    }
  }

  /**
   * Get current violations
   */
  getViolations(filter?: {
    severity?: 'critical' | 'warning' | 'info';
    platform?: string;
    table?: string;
  }): ConsistencyViolation[] {
    let filteredViolations = this.violations;

    if (filter) {
      if (filter.severity) {
        filteredViolations = filteredViolations.filter(v => v.severity === filter.severity);
      }
      if (filter.platform) {
        filteredViolations = filteredViolations.filter(v => v.platforms.includes(filter.platform!));
      }
      if (filter.table) {
        filteredViolations = filteredViolations.filter(v => v.table === filter.table);
      }
    }

    return filteredViolations;
  }

  /**
   * Generate comprehensive consistency report
   */
  async generateReport(): Promise<ConsistencyReport> {
    const platformStatus = await this.checkPlatformStatus();
    const recommendations = this.generateRecommendations();

    return {
      generatedAt: new Date(),
      totalRules: this.rules.size,
      activeRules: Array.from(this.rules.values()).filter(r => r.enabled).length,
      violations: this.violations,
      platformStatus,
      recommendations
    };
  }

  /**
   * Perform manual consistency check
   */
  async performConsistencyCheck(): Promise<void> {
    for (const rule of Array.from(this.rules.values())) {
      if (!rule.enabled) continue;

      try {
        await this.checkRuleConsistency(rule);
      } catch (error) {
        this.emit('check-error', { ruleId: rule.id, error: error.message });
      }
    }
  }

  /**
   * Set up real-time listener for a table
   */
  private async setupTableListener(table: string): Promise<void> {
    const channel = this.supabase
      .channel(`consistency-${table}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: table
      }, (payload) => {
        this.handleTableChange(table, payload);
      })
      .subscribe();
  }

  /**
   * Handle real-time table changes
   */
  private async handleTableChange(table: string, payload: any): Promise<void> {
    // Find rules that apply to this table
    const applicableRules = Array.from(this.rules.values()).filter(
      rule => rule.table === table && rule.enabled
    );

    // Check each applicable rule
    for (const rule of applicableRules) {
      try {
        await this.checkRuleConsistency(rule, payload.new?.id);
      } catch (error) {
        this.emit('check-error', { ruleId: rule.id, error: error.message });
      }
    }
  }

  /**
   * Check consistency for a specific rule
   */
  private async checkRuleConsistency(rule: ConsistencyRule, recordId?: string): Promise<void> {
    let query = this.supabase.from(rule.table).select('*');
    
    if (recordId) {
      query = query.eq('id', recordId);
    } else {
      query = query.limit(100); // Limit for periodic checks
    }

    const { data: records, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch data for rule ${rule.id}: ${error.message}`);
    }

    if (!records || records.length === 0) {
      return;
    }

    for (const record of records) {
      await this.validateRecordConsistency(rule, record);
    }
  }

  /**
   * Validate consistency for a specific record
   */
  private async validateRecordConsistency(rule: ConsistencyRule, record: any): Promise<void> {
    for (const field of rule.fields) {
      const expectedValue = record[field];
      const actualValues: Record<string, any> = {};

      // Get values from each platform
      for (const platform of rule.platforms) {
        try {
          const platformValue = await this.getPlatformValue(platform, rule.table, field, record.id);
          actualValues[platform] = platformValue;
        } catch (error) {
          actualValues[platform] = { error: error.message };
        }
      }

      // Check for inconsistencies
      const inconsistentPlatforms = rule.platforms.filter(platform => {
        const platformValue = actualValues[platform];
        return platformValue !== expectedValue && !platformValue?.error;
      });

      if (inconsistentPlatforms.length > 0) {
        const violation: ConsistencyViolation = {
          ruleId: rule.id,
          ruleName: rule.name,
          table: rule.table,
          field,
          recordId: record.id,
          expectedValue,
          actualValues,
          detectedAt: new Date(),
          severity: this.getSeverity(rule.criticalLevel, inconsistentPlatforms.length),
          platforms: inconsistentPlatforms
        };

        this.addViolation(violation);
      }
    }
  }

  /**
   * Get value from a specific platform
   */
  private async getPlatformValue(platform: string, table: string, field: string, recordId: string): Promise<any> {
    // This would need platform-specific implementation
    // For now, we'll simulate by returning the database value
    
    if (platform === 'admin') {
      // Admin dashboard uses the main database
      const { data } = await this.supabase
        .from(table)
        .select(field)
        .eq('id', recordId)
        .single();
      return data?.[field];
    }

    // For other platforms, we would need to implement API calls
    // or check local caches/databases
    return null;
  }

  /**
   * Add a violation to the list
   */
  private addViolation(violation: ConsistencyViolation): void {
    // Remove existing violation for the same rule/record/field if exists
    this.violations = this.violations.filter(v => 
      !(v.ruleId === violation.ruleId && 
        v.recordId === violation.recordId && 
        v.field === violation.field)
    );

    this.violations.push(violation);
    this.emit('violation-detected', violation);

    // Keep only the last 1000 violations
    if (this.violations.length > 1000) {
      this.violations = this.violations.slice(-1000);
    }
  }

  /**
   * Get severity based on critical level and number of affected platforms
   */
  private getSeverity(criticalLevel: string, affectedPlatforms: number): 'critical' | 'warning' | 'info' {
    if (criticalLevel === 'high' && affectedPlatforms > 1) {
      return 'critical';
    } else if (criticalLevel === 'high' || affectedPlatforms > 2) {
      return 'warning';
    } else {
      return 'info';
    }
  }

  /**
   * Check platform status
   */
  private async checkPlatformStatus(): Promise<Record<string, 'online' | 'offline' | 'degraded'>> {
    const status: Record<string, 'online' | 'offline' | 'degraded'> = {};

    // This would implement actual platform health checks
    // For now, return mock status
    status.admin = 'online';
    status.pos = 'online';
    status.web = 'online';
    status.mobile = 'online';

    return status;
  }

  /**
   * Generate recommendations based on current violations
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    
    const criticalViolations = this.violations.filter(v => v.severity === 'critical');
    const warningViolations = this.violations.filter(v => v.severity === 'warning');

    if (criticalViolations.length > 0) {
      recommendations.push(`${criticalViolations.length} critical data inconsistencies detected - immediate attention required`);
    }

    if (warningViolations.length > 5) {
      recommendations.push('Multiple warning-level inconsistencies - consider reviewing sync mechanisms');
    }

    const frequentTables = this.getFrequentlyViolatedTables();
    if (frequentTables.length > 0) {
      recommendations.push(`Tables with frequent violations: ${frequentTables.join(', ')} - investigate sync issues`);
    }

    if (this.violations.length === 0) {
      recommendations.push('All systems are consistent - monitoring is working effectively');
    }

    return recommendations;
  }

  /**
   * Get tables with frequent violations
   */
  private getFrequentlyViolatedTables(): string[] {
    const tableCounts: Record<string, number> = {};
    
    this.violations.forEach(violation => {
      tableCounts[violation.table] = (tableCounts[violation.table] || 0) + 1;
    });

    return Object.entries(tableCounts)
      .filter(([_, count]) => count > 3)
      .map(([table, _]) => table);
  }

  /**
   * Clear all violations
   */
  clearViolations(): void {
    this.violations = [];
    this.emit('violations-cleared');
  }

  /**
   * Resolve a specific violation
   */
  resolveViolation(violationIndex: number): void {
    if (violationIndex >= 0 && violationIndex < this.violations.length) {
      const violation = this.violations.splice(violationIndex, 1)[0];
      this.emit('violation-resolved', violation);
    }
  }
}