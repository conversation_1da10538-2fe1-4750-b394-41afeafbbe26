/**
 * Order Service - Centralized order business logic
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { z } from 'zod';
import { Logger } from '../utils/Logger';
import { EventEmitter } from 'events';

// Validation schemas
const OrderItemSchema = z.object({
  id: z.string().uuid().optional(),
  subcategory_id: z.string().uuid(),
  quantity: z.number().min(1),
  unit_price: z.number().min(0),
  total_price: z.number().min(0),
  notes: z.string().optional(),
  customizations: z.record(z.any()).optional()
});

const OrderSchema = z.object({
  id: z.string().uuid().optional(),
  order_number: z.string().optional(),
  customer_id: z.string().uuid().optional(),
  customer_name: z.string().optional(),
  customer_phone: z.string().optional(),
  order_type: z.enum(['dine-in', 'takeaway', 'delivery']),
  status: z.enum(['pending', 'confirmed', 'preparing', 'ready', 'completed', 'cancelled']).default('pending'),
  payment_status: z.enum(['pending', 'processing', 'completed', 'failed', 'refunded']).default('pending'),
  payment_method: z.enum(['cash', 'card', 'digital']).optional(),
  items: z.array(OrderItemSchema),
  subtotal: z.number().min(0),
  tax_amount: z.number().min(0).default(0),
  delivery_fee: z.number().min(0).default(0),
  discount_amount: z.number().min(0).default(0),
  total_amount: z.number().min(0),
  delivery_address: z.string().optional(),
  table_number: z.string().optional(),
  notes: z.string().optional(),
  estimated_time: z.number().optional(),
  branch_id: z.string().uuid().optional()
});

export type Order = z.infer<typeof OrderSchema>;
export type OrderItem = z.infer<typeof OrderItemSchema>;

export interface OrderSearchParams {
  status?: Order['status'];
  order_type?: Order['order_type'];
  customer_id?: string;
  branch_id?: string;
  date_from?: string;
  date_to?: string;
  limit?: number;
  offset?: number;
}

export interface OrderCreateParams extends Omit<Order, 'id' | 'order_number'> {}
export interface OrderUpdateParams extends Partial<Order> {
  id: string;
}

export interface OrderStats {
  total_orders: number;
  pending_orders: number;
  completed_orders: number;
  cancelled_orders: number;
  total_revenue: number;
  average_order_value: number;
}

export class OrderService extends EventEmitter {
  private supabase: SupabaseClient;
  private logger: Logger;

  constructor(supabaseUrl: string, supabaseKey: string, logger: Logger) {
    super();
    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.logger = logger;
  }

  /**
   * Search orders with filters
   */
  async searchOrders(params: OrderSearchParams): Promise<Order[]> {
    try {
      this.logger.info('Searching orders', { params });

      let query = this.supabase
        .from('orders')
        .select(`
          *,
          order_items (
            id,
            subcategory_id,
            quantity,
            unit_price,
            total_price,
            notes,
            customizations,
            subcategories (
              name,
              category
            )
          )
        `);

      if (params.status) {
        query = query.eq('status', params.status);
      }

      if (params.order_type) {
        query = query.eq('order_type', params.order_type);
      }

      if (params.customer_id) {
        query = query.eq('customer_id', params.customer_id);
      }

      if (params.branch_id) {
        query = query.eq('branch_id', params.branch_id);
      }

      if (params.date_from) {
        query = query.gte('created_at', params.date_from);
      }

      if (params.date_to) {
        query = query.lte('created_at', params.date_to);
      }

      query = query.order('created_at', { ascending: false });

      if (params.limit) {
        query = query.limit(params.limit);
      }

      if (params.offset) {
        query = query.range(params.offset, params.offset + (params.limit || 10) - 1);
      }

      const { data, error } = await query;

      if (error) {
        this.logger.error('Order search failed', { error, params });
        throw new Error(`Order search failed: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      this.logger.error('Order search error', { error, params });
      throw error;
    }
  }

  /**
   * Get order by ID
   */
  async getOrderById(id: string): Promise<Order | null> {
    try {
      this.logger.info('Getting order by ID', { id });

      const { data, error } = await this.supabase
        .from('orders')
        .select(`
          *,
          order_items (
            id,
            subcategory_id,
            quantity,
            unit_price,
            total_price,
            notes,
            customizations,
            subcategories (
              name,
              category,
              description
            )
          )
        `)
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') { // Not found
          return null;
        }
        this.logger.error('Get order failed', { error, id });
        throw new Error(`Get order failed: ${error.message}`);
      }

      return data;
    } catch (error) {
      this.logger.error('Get order error', { error, id });
      throw error;
    }
  }

  /**
   * Create new order
   */
  async createOrder(orderData: OrderCreateParams): Promise<Order> {
    try {
      // Validate input
      const validatedData = OrderSchema.parse(orderData);
      this.logger.info('Creating order', { orderType: validatedData.order_type });

      // Generate order number
      const orderNumber = await this.generateOrderNumber();

      // Start transaction
      const { data: order, error: orderError } = await this.supabase
        .from('orders')
        .insert([{
          ...validatedData,
          order_number: orderNumber,
          items: undefined // Remove items from order insert
        }])
        .select()
        .single();

      if (orderError) {
        this.logger.error('Order creation failed', { error: orderError, orderData });
        throw new Error(`Order creation failed: ${orderError.message}`);
      }

      // Insert order items
      if (validatedData.items.length > 0) {
        const orderItems = validatedData.items.map(item => ({
          ...item,
          order_id: order.id
        }));

        const { error: itemsError } = await this.supabase
          .from('order_items')
          .insert(orderItems);

        if (itemsError) {
          this.logger.error('Order items creation failed', { error: itemsError, orderId: order.id });
          // Rollback order creation
          await this.supabase.from('orders').delete().eq('id', order.id);
          throw new Error(`Order items creation failed: ${itemsError.message}`);
        }
      }

      // Get complete order with items
      const completeOrder = await this.getOrderById(order.id);
      if (!completeOrder) {
        throw new Error('Failed to retrieve created order');
      }

      this.logger.info('Order created successfully', { id: order.id, orderNumber });
      
      // Emit order created event
      this.emit('orderCreated', completeOrder);

      return completeOrder;
    } catch (error) {
      this.logger.error('Create order error', { error, orderData });
      throw error;
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(id: string, status: Order['status'], notes?: string): Promise<Order> {
    try {
      this.logger.info('Updating order status', { id, status });

      const { data, error } = await this.supabase
        .from('orders')
        .update({
          status,
          updated_at: new Date().toISOString(),
          ...(notes && { notes })
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        this.logger.error('Order status update failed', { error, id, status });
        throw new Error(`Order status update failed: ${error.message}`);
      }

      this.logger.info('Order status updated successfully', { id, status });
      
      // Emit status change event
      this.emit('orderStatusChanged', { orderId: id, oldStatus: undefined, newStatus: status });

      return data;
    } catch (error) {
      this.logger.error('Update order status error', { error, id, status });
      throw error;
    }
  }

  /**
   * Update payment status
   */
  async updatePaymentStatus(
    id: string, 
    paymentStatus: Order['payment_status'],
    paymentMethod?: Order['payment_method'],
    transactionId?: string
  ): Promise<Order> {
    try {
      this.logger.info('Updating payment status', { id, paymentStatus });

      const updateData: any = {
        payment_status: paymentStatus,
        updated_at: new Date().toISOString()
      };

      if (paymentMethod) {
        updateData.payment_method = paymentMethod;
      }

      if (transactionId) {
        updateData.payment_transaction_id = transactionId;
      }

      const { data, error } = await this.supabase
        .from('orders')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        this.logger.error('Payment status update failed', { error, id, paymentStatus });
        throw new Error(`Payment status update failed: ${error.message}`);
      }

      this.logger.info('Payment status updated successfully', { id, paymentStatus });
      
      // Emit payment status change event
      this.emit('paymentStatusChanged', { orderId: id, paymentStatus, paymentMethod });

      return data;
    } catch (error) {
      this.logger.error('Update payment status error', { error, id, paymentStatus });
      throw error;
    }
  }

  /**
   * Cancel order
   */
  async cancelOrder(id: string, reason?: string): Promise<Order> {
    try {
      this.logger.info('Cancelling order', { id, reason });

      const order = await this.getOrderById(id);
      if (!order) {
        throw new Error('Order not found');
      }

      if (['completed', 'cancelled'].includes(order.status)) {
        throw new Error(`Cannot cancel order with status: ${order.status}`);
      }

      const { data, error } = await this.supabase
        .from('orders')
        .update({
          status: 'cancelled',
          notes: reason ? `${order.notes || ''}\nCancellation reason: ${reason}` : order.notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        this.logger.error('Order cancellation failed', { error, id });
        throw new Error(`Order cancellation failed: ${error.message}`);
      }

      this.logger.info('Order cancelled successfully', { id });
      
      // Emit order cancelled event
      this.emit('orderCancelled', { orderId: id, reason });

      return data;
    } catch (error) {
      this.logger.error('Cancel order error', { error, id });
      throw error;
    }
  }

  /**
   * Get order statistics
   */
  async getOrderStats(params?: { 
    branch_id?: string; 
    date_from?: string; 
    date_to?: string 
  }): Promise<OrderStats> {
    try {
      this.logger.info('Getting order statistics', { params });

      let query = this.supabase
        .from('orders')
        .select('status, total_amount');

      if (params?.branch_id) {
        query = query.eq('branch_id', params.branch_id);
      }

      if (params?.date_from) {
        query = query.gte('created_at', params.date_from);
      }

      if (params?.date_to) {
        query = query.lte('created_at', params.date_to);
      }

      const { data, error } = await query;

      if (error) {
        this.logger.error('Order stats query failed', { error, params });
        throw new Error(`Order stats query failed: ${error.message}`);
      }

      const stats: OrderStats = {
        total_orders: data.length,
        pending_orders: data.filter(o => o.status === 'pending').length,
        completed_orders: data.filter(o => o.status === 'completed').length,
        cancelled_orders: data.filter(o => o.status === 'cancelled').length,
        total_revenue: data.filter(o => o.status === 'completed').reduce((sum, o) => sum + o.total_amount, 0),
        average_order_value: 0
      };

      if (stats.completed_orders > 0) {
        stats.average_order_value = stats.total_revenue / stats.completed_orders;
      }

      return stats;
    } catch (error) {
      this.logger.error('Get order stats error', { error, params });
      throw error;
    }
  }

  /**
   * Generate unique order number
   */
  private async generateOrderNumber(): Promise<string> {
    const now = new Date();
    const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
    
    // Get today's order count
    const { count } = await this.supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}T00:00:00Z`)
      .lt('created_at', `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}T23:59:59Z`);

    const orderNumber = `ORD-${dateStr}-${String((count || 0) + 1).padStart(3, '0')}`;
    return orderNumber;
  }
}