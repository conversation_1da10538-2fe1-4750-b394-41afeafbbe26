import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { z } from 'zod'

// Validation schemas
const createIngredientSchema = z.object({
  name_en: z.string().min(1, 'English name is required'),
  name_el: z.string().min(1, 'Greek name is required'),
  unit: z.string().default('pieces'),
  stock_quantity: z.number().min(0, 'Stock quantity must be non-negative').default(0),
  minimum_stock_level: z.number().min(0, 'Minimum stock level must be non-negative').default(5),
  cost_per_unit: z.number().min(0, 'Cost per unit must be non-negative').default(0),
  is_active: z.boolean().default(true),
  allergens: z.array(z.string()).optional(),
  // Backward compatibility fields (will be mapped to correct fields)
  name: z.string().optional(),
  min_stock_level: z.number().optional(),
  cost: z.number().optional(),
  is_available: z.boolean().optional()
})

// GET /api/ingredients - Fetch all ingredients with optional filtering
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    // Extract query parameters
    const is_active = searchParams.get('is_active')
    const low_stock_only = searchParams.get('low_stock_only') === 'true'
    const out_of_stock_only = searchParams.get('out_of_stock_only') === 'true'
    const search = searchParams.get('search')
    const sort_by = searchParams.get('sort_by') || 'name'
    const sort_order = searchParams.get('sort_order') || 'asc'
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    
    let query = supabase
      .from('ingredients')
      .select(`
        id,
        name_en,
        name_el,
        stock_quantity,
        minimum_stock_level,
        cost_per_unit,
        is_active,
        unit,
        allergens,
        created_at,
        updated_at,
        ingredient_categories(
          id,
          name,
          color_code
        ),
        menu_item_ingredients(
          subcategory_id,
          subcategories(
            id,
            name_en,
            is_available
          )
        )
      `, { count: 'exact' })
    
    // Apply filters
    if (is_active !== null) {
      query = query.eq('is_active', is_active === 'true')
    }
    
    if (out_of_stock_only) {
      query = query.eq('stock_quantity', 0)
    } else if (low_stock_only) {
      // Low stock: stock > 0 but <= min_stock_level
      query = query.gt('stock_quantity', 0)
      // Note: We'll filter by min_stock_level in post-processing since Supabase doesn't support column comparisons easily
    }
    
    if (search) {
      query = query.or(`name_en.ilike.%${search}%,name_el.ilike.%${search}%`)
    }
    
    // Apply sorting - map common field names to actual database fields
    const sortField = sort_by === 'name' ? 'name_en' : sort_by === 'min_stock_level' ? 'minimum_stock_level' : sort_by === 'cost' ? 'cost_per_unit' : sort_by === 'is_available' ? 'is_active' : sort_by
    query = query.order(sortField, { ascending: sort_order === 'asc' })
    
    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)
    
    const { data, error, count } = await query
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch ingredients' },
        { status: 500 }
      )
    }
    
    // Post-process data to add computed fields and maintain backward compatibility
    let processedData = data?.map(ingredient => {
      const isLowStock = ingredient.stock_quantity > 0 && 
                        ingredient.minimum_stock_level > 0 && 
                        ingredient.stock_quantity <= ingredient.minimum_stock_level
      
      const isOutOfStock = ingredient.stock_quantity === 0
      
      return {
        ...ingredient,
        // Backward compatibility fields
        name: ingredient.name_en || ingredient.name_el || 'Unknown',
        min_stock_level: ingredient.minimum_stock_level,
        cost: ingredient.cost_per_unit,
        is_available: ingredient.is_active,
        // Stock status analysis
        stock_status: {
          is_low_stock: isLowStock,
          is_out_of_stock: isOutOfStock,
          status: isOutOfStock ? 'out_of_stock' : isLowStock ? 'low_stock' : 'in_stock'
        },
        subcategories_using: ingredient.menu_item_ingredients?.length || 0
      }
    }) || []
    
    // Apply low stock filter if requested
    if (low_stock_only && !out_of_stock_only) {
      processedData = processedData.filter(ingredient => 
        ingredient.stock_status.is_low_stock
      )
    }
    
    return NextResponse.json({
      data: processedData,
      pagination: {
        page,
        limit,
        total: count || 0,
        pages: Math.ceil((count || 0) / limit)
      }
    })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/ingredients - Create a new ingredient
export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const body = await request.json()
    
    // Validate request body
    const validationResult = createIngredientSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }
    
    let ingredientData = validationResult.data
    
    // Map backward compatibility fields
    if (ingredientData.name && !ingredientData.name_en) {
      ingredientData.name_en = ingredientData.name
      ingredientData.name_el = ingredientData.name
    }
    if (ingredientData.min_stock_level && !ingredientData.minimum_stock_level) {
      ingredientData.minimum_stock_level = ingredientData.min_stock_level
    }
    if (ingredientData.cost && !ingredientData.cost_per_unit) {
      ingredientData.cost_per_unit = ingredientData.cost
    }
    if (ingredientData.is_available !== undefined && ingredientData.is_active === undefined) {
      ingredientData.is_active = ingredientData.is_available
    }
    
    // Check if ingredient with same name already exists
    const { data: existingIngredient } = await supabase
      .from('ingredients')
      .select('id, name_en, name_el')
      .or(`name_en.ilike.${ingredientData.name_en},name_el.ilike.${ingredientData.name_el}`)
      .single()
    
    if (existingIngredient) {
      return NextResponse.json(
        { error: 'An ingredient with this name already exists' },
        { status: 400 }
      )
    }
    
    // Remove backward compatibility fields before inserting
    const { name, min_stock_level, cost, is_available, ...dbData } = ingredientData
    
    // Create ingredient
    const { data, error } = await supabase
      .from('ingredients')
      .insert([dbData])
      .select('*')
      .single()
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to create ingredient' },
        { status: 500 }
      )
    }
    
    // Add stock status and backward compatibility fields to response
    const responseData = {
      ...data,
      // Backward compatibility fields
      name: data.name_en || data.name_el || 'Unknown',
      min_stock_level: data.minimum_stock_level,
      cost: data.cost_per_unit,
      is_available: data.is_active,
      // Stock status
      stock_status: {
        is_low_stock: data.stock_quantity > 0 && data.stock_quantity <= data.minimum_stock_level,
        is_out_of_stock: data.stock_quantity === 0,
        status: data.stock_quantity === 0 ? 'out_of_stock' : 
               (data.stock_quantity <= data.minimum_stock_level ? 'low_stock' : 'in_stock')
      }
    }
    
    return NextResponse.json({ data: responseData }, { status: 201 })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}