import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { POSGlassModal, POSGlassButton, POSGlassInput } from '../ui/pos-glass-components';

interface CustomerInfo {
  name: string;
  phone: string;
  address: string;
}

interface CustomerInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (customerInfo: CustomerInfo) => void;
  initialData: CustomerInfo;
  orderType: 'dine-in' | 'takeaway' | 'delivery';
}

export const CustomerInfoModal: React.FC<CustomerInfoModalProps> = ({
  isOpen,
  onClose,
  onSave,
  initialData,
  orderType
}) => {
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>(initialData);

  useEffect(() => {
    if (isOpen) {
      setCustomerInfo(initialData);
    }
  }, [isOpen, initialData]);

  const handleSave = () => {
    // Validate required fields
    if (!customerInfo.name.trim()) {
      toast.error('Customer name is required');
      return;
    }

    if (!customerInfo.phone.trim()) {
      toast.error('Phone number is required');
      return;
    }

    // Validate phone number format (basic validation)
    const phoneRegex = /^[\d\s\-\+\(\)]+$/;
    if (!phoneRegex.test(customerInfo.phone)) {
      toast.error('Please enter a valid phone number');
      return;
    }

    // For delivery orders, address is required
    if (orderType === 'delivery' && !customerInfo.address.trim()) {
      toast.error('Delivery address is required');
      return;
    }

    onSave(customerInfo);
    onClose();
  };

  const handleInputChange = (field: keyof CustomerInfo, value: string) => {
    setCustomerInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const getAddressLabel = () => {
    switch (orderType) {
      case 'delivery':
        return 'Delivery Address *';
      case 'dine-in':
        return 'Table Number (Optional)';
      case 'takeaway':
        return 'Notes (Optional)';
      default:
        return 'Address';
    }
  };

  const getAddressPlaceholder = () => {
    switch (orderType) {
      case 'delivery':
        return 'Enter full delivery address including floor, building number, etc.';
      case 'dine-in':
        return 'Table number or location';
      case 'takeaway':
        return 'Any special notes';
      default:
        return 'Enter address';
    }
  };

  return (
    <POSGlassModal
      isOpen={isOpen}
      onClose={onClose}
      title="Customer Information"
      size="md"
    >
      <div className="space-y-4">
        {/* Customer Name */}
        <div>
          <label className="block text-sm font-medium text-white/90 mb-2">
            Customer Name *
          </label>
          <POSGlassInput
            type="text"
            value={customerInfo.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="Enter customer name"
            autoFocus
          />
        </div>

        {/* Phone Number */}
        <div>
          <label className="block text-sm font-medium text-white/90 mb-2">
            Phone Number *
          </label>
          <POSGlassInput
            type="tel"
            value={customerInfo.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            placeholder="Enter phone number"
          />
        </div>

        {/* Address/Notes Field */}
        <div>
          <label className="block text-sm font-medium text-white/90 mb-2">
            {getAddressLabel()}
          </label>
          {orderType === 'delivery' ? (
            <textarea
              value={customerInfo.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              className="w-full p-3 bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 text-white placeholder-white/50 backdrop-blur-sm"
              rows={3}
              placeholder={getAddressPlaceholder()}
            />
          ) : (
            <POSGlassInput
              type="text"
              value={customerInfo.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              placeholder={getAddressPlaceholder()}
            />
          )}
        </div>

        {/* Order Type Display */}
        <div className="p-3 bg-white/10 rounded-lg border border-white/20">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-white/90">Order Type:</span>
            <span className="text-sm font-semibold text-blue-300 capitalize">
              {orderType.replace('-', ' ')}
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3 mt-6">
          <POSGlassButton
            variant="secondary"
            onClick={onClose}
            className="flex-1"
          >
            Cancel
          </POSGlassButton>
          <POSGlassButton
            variant="primary"
            onClick={handleSave}
            className="flex-1"
          >
            Save
          </POSGlassButton>
        </div>
      </div>
    </POSGlassModal>
  );
};
