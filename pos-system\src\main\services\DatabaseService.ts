import Database from 'better-sqlite3';
import * as path from 'path';
import { app } from 'electron';

// Import domain services
import { OrderService } from './OrderService';
import { StaffService } from './StaffService';
import { SyncService } from './SyncService';
import { SettingsService } from './SettingsService';
import { PaymentService } from './PaymentService';

export class DatabaseService {
  private db: Database.Database | null = null;
  private dbPath: string;

  // Domain services
  public orders: OrderService;
  public staff: StaffService;
  public sync: SyncService;
  public settings: SettingsService;
  public payments: PaymentService;

  constructor() {
    // Store database in user data directory
    const userDataPath = app.getPath('userData');
    this.dbPath = path.join(userDataPath, 'pos-database.db');

    // Initialize services after database connection
    this.orders = null as unknown as OrderService;
    this.staff = null as unknown as StaffService;
    this.sync = null as unknown as SyncService;
    this.settings = null as unknown as SettingsService;
    this.payments = null as unknown as PaymentService;
  }

  async initialize(): Promise<void> {
    try {
      
      this.db = new Database(this.dbPath);
      
      // Enable WAL mode for better concurrency
      this.db.pragma('journal_mode = WAL');
      this.db.pragma('synchronous = NORMAL');
      this.db.pragma('cache_size = 1000');
      this.db.pragma('temp_store = memory');
      
      await this.createTables();
      
      // Initialize domain services
      this.orders = new OrderService(this.db);
      this.staff = new StaffService(this.db);
      this.sync = new SyncService(this.db);
      this.settings = new SettingsService(this.db);
      this.payments = new PaymentService(this.db);
      
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Orders table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS orders (
        id TEXT PRIMARY KEY,
        order_number TEXT UNIQUE NOT NULL,
        status TEXT NOT NULL,
        items TEXT NOT NULL,
        total_amount REAL NOT NULL,
        customer_name TEXT,
        customer_phone TEXT,
        customer_email TEXT,
        order_type TEXT NOT NULL,
        table_number TEXT,
        delivery_address TEXT,
        special_instructions TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        estimated_time INTEGER,
        supabase_id TEXT,
        sync_status TEXT DEFAULT 'pending',
        payment_status TEXT DEFAULT 'pending',
        payment_method TEXT,
        payment_transaction_id TEXT
      )
    `);

    // Payment transactions table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS payment_transactions (
        id TEXT PRIMARY KEY,
        order_id TEXT NOT NULL,
        amount REAL NOT NULL,
        payment_method TEXT NOT NULL,
        status TEXT NOT NULL,
        gateway_transaction_id TEXT,
        gateway_response TEXT,
        processed_at TEXT NOT NULL,
        refunded_amount REAL DEFAULT 0,
        metadata TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (order_id) REFERENCES orders (id)
      )
    `);

    // Payment receipts table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS payment_receipts (
        id TEXT PRIMARY KEY,
        transaction_id TEXT NOT NULL,
        receipt_number TEXT UNIQUE NOT NULL,
        order_details TEXT NOT NULL,
        subtotal REAL NOT NULL,
        tax REAL NOT NULL,
        delivery_fee REAL DEFAULT 0,
        total_amount REAL NOT NULL,
        payment_method TEXT NOT NULL,
        cash_received REAL,
        change_given REAL,
        printed BOOLEAN DEFAULT FALSE,
        emailed BOOLEAN DEFAULT FALSE,
        email_address TEXT,
        created_at TEXT NOT NULL,
        FOREIGN KEY (transaction_id) REFERENCES payment_transactions (id)
      )
    `);

    // Payment refunds table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS payment_refunds (
        id TEXT PRIMARY KEY,
        transaction_id TEXT NOT NULL,
        amount REAL NOT NULL,
        reason TEXT,
        status TEXT NOT NULL,
        gateway_refund_id TEXT,
        processed_at TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (transaction_id) REFERENCES payment_transactions (id)
      )
    `);

    // Staff sessions table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS staff_sessions (
        id TEXT PRIMARY KEY,
        staff_id TEXT NOT NULL,
        pin_hash TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('admin', 'staff')),
        login_time TEXT NOT NULL,
        logout_time TEXT,
        is_active BOOLEAN NOT NULL DEFAULT 1
      )
    `);

    // Sync queue table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS sync_queue (
        id TEXT PRIMARY KEY,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        operation TEXT NOT NULL CHECK (operation IN ('insert', 'update', 'delete')),
        data TEXT NOT NULL,
        created_at TEXT NOT NULL,
        attempts INTEGER NOT NULL DEFAULT 0,
        last_attempt TEXT,
        error_message TEXT
      )
    `);

    // Local settings cache table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS local_settings (
        id TEXT PRIMARY KEY,
        setting_category TEXT NOT NULL,
        setting_key TEXT NOT NULL,
        setting_value TEXT NOT NULL,
        last_sync TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        UNIQUE(setting_category, setting_key)
      )
    `);

    // POS configurations local cache
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS pos_local_config (
        id TEXT PRIMARY KEY,
        terminal_id TEXT NOT NULL,
        config_key TEXT NOT NULL,
        config_value TEXT NOT NULL,
        last_sync TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        UNIQUE(terminal_id, config_key)
      )
    `);

    // Create indexes for performance
    this.createIndexes();
  }

  private createIndexes(): void {
    if (!this.db) return;

    try {
      // Orders indexes
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_orders_sync_status ON orders(sync_status)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_orders_supabase_id ON orders(supabase_id)');

      // Payment transactions indexes
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_payment_transactions_order_id ON payment_transactions(order_id)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_payment_transactions_status ON payment_transactions(status)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_payment_transactions_created_at ON payment_transactions(created_at)');

      // Staff sessions indexes
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_staff_sessions_staff_id ON staff_sessions(staff_id)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_staff_sessions_active ON staff_sessions(is_active)');

      // Sync queue indexes
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_sync_queue_attempts ON sync_queue(attempts)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_sync_queue_created_at ON sync_queue(created_at)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_sync_queue_table_record ON sync_queue(table_name, record_id)');

      // Settings indexes
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_local_settings_category_key ON local_settings(setting_category, setting_key)');
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_pos_config_terminal_key ON pos_local_config(terminal_id, config_key)');
    } catch (error) {
      console.warn('Some indexes could not be created:', error);
    }
  }

  async close(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }

  // Database maintenance operations
  async vacuum(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    this.db.exec('VACUUM');
  }

  async analyze(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    this.db.exec('ANALYZE');
  }

  async getStats(): Promise<{
    orders: number;
    transactions: number;
    sessions: number;
    sync_items: number;
    settings: number;
    database_size: string;
  }> {
    if (!this.db) throw new Error('Database not initialized');

    interface CountResult { count: number; }
    const ordersCount = (this.db.prepare('SELECT COUNT(*) as count FROM orders').get() as CountResult).count;
    const transactionsCount = (this.db.prepare('SELECT COUNT(*) as count FROM payment_transactions').get() as CountResult).count;
    const sessionsCount = (this.db.prepare('SELECT COUNT(*) as count FROM staff_sessions').get() as CountResult).count;
    const syncItemsCount = (this.db.prepare('SELECT COUNT(*) as count FROM sync_queue').get() as CountResult).count;
    const settingsCount = (this.db.prepare('SELECT COUNT(*) as count FROM local_settings').get() as CountResult).count;

    // Get database file size
    const fs = require('fs');
    let databaseSize = '0 MB';
    try {
      const stats = fs.statSync(this.dbPath);
      const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
      databaseSize = `${sizeInMB} MB`;
    } catch (error) {
      console.warn('Could not get database file size:', error);
    }

    return {
      orders: ordersCount,
      transactions: transactionsCount,
      sessions: sessionsCount,
      sync_items: syncItemsCount,
      settings: settingsCount,
      database_size: databaseSize
    };
  }

  // Backup and restore operations
  async backup(backupPath: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    const fs = require('fs');
    const sourceStream = fs.createReadStream(this.dbPath);
    const destStream = fs.createWriteStream(backupPath);
    
    return new Promise((resolve, reject) => {
      sourceStream.pipe(destStream);
      destStream.on('finish', resolve);
      destStream.on('error', reject);
    });
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.db) return false;
      
      // Try a simple query
      this.db.prepare('SELECT 1').get();
      return true;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }
}