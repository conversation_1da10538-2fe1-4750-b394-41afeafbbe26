import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { z } from 'zod'

// Validation schemas
const createCategorySchema = z.object({
  name_en: z.string().min(1, 'English name is required'),
  name_el: z.string().min(1, 'Greek name is required'),
  description_en: z.string().optional(),
  description_el: z.string().optional(),
  display_order: z.number().int().min(0).default(0),
  is_active: z.boolean().default(true)
})

const reorderCategoriesSchema = z.object({
  categories: z.array(z.object({
    id: z.string().uuid(),
    display_order: z.number().int().min(0)
  }))
})

// GET /api/menu_categories - Fetch all menu categories
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    
    // Extract query parameters
    const is_active = searchParams.get('is_active')
    const include_counts = searchParams.get('include_counts') === 'true'
    const sort_by = searchParams.get('sort_by') || 'display_order'
    const sort_order = searchParams.get('sort_order') || 'asc'
    
    let query = supabase
      .from('menu_categories')
      .select(`
        *
        ${include_counts ? `,
        subcategories(
          count
        )` : ''}
      `)
    
    // Apply filters
    if (is_active !== null) {
      query = query.eq('is_active', is_active === 'true')
    }
    
    // Apply sorting
    query = query.order(sort_by, { ascending: sort_order === 'asc' })
    
    const { data, error } = await query
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch menu categories' },
        { status: 500 }
      )
    }
    
    // Transform data to include subcategory counts if requested
    const transformedData = include_counts ? data?.map((category: any) => ({
      ...category,
      subcategories_count: category.subcategories?.length || 0,
      subcategories: undefined // Remove the nested array
    })) : data
    
    return NextResponse.json({ data: transformedData })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/menu_categories - Create a new menu category
export async function POST(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const body = await request.json()
    
    // Validate request body
    const validationResult = createCategorySchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }
    
    const categoryData = validationResult.data
    
    // If no display_order provided, set it to the next available position
    if (categoryData.display_order === 0) {
      const { data: maxOrderData } = await supabase
        .from('menu_categories')
        .select('display_order')
        .order('display_order', { ascending: false })
        .limit(1)
        .single()
      
      categoryData.display_order = (maxOrderData?.display_order || 0) + 1
    }
    
    // Create menu category
    const { data, error } = await supabase
      .from('menu_categories')
      .insert([categoryData])
      .select('*')
      .single()
    
    if (error) {
      return NextResponse.json(
        { error: 'Failed to create menu category' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({ data }, { status: 201 })
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/menu_categories/reorder - Reorder categories (for drag-and-drop)
export async function PUT(request: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const body = await request.json()
    
    // Check if this is a reorder operation
    if (body.action === 'reorder') {
      const validationResult = reorderCategoriesSchema.safeParse(body)
      if (!validationResult.success) {
        return NextResponse.json(
          { 
            error: 'Validation failed',
            details: validationResult.error.errors
          },
          { status: 400 }
        )
      }
      
      const { categories } = validationResult.data
      
      // Update display orders in a transaction-like manner
      const updatePromises = categories.map(category => 
        supabase
          .from('menu_categories')
          .update({ display_order: category.display_order })
          .eq('id', category.id)
      )
      
      const results = await Promise.all(updatePromises)
      
      // Check if any updates failed
      const failedUpdates = results.filter(result => result.error)
      if (failedUpdates.length > 0) {
        return NextResponse.json(
          { error: 'Failed to reorder some categories' },
          { status: 500 }
        )
      }
      
      // Fetch updated categories
      const { data, error } = await supabase
        .from('menu_categories')
        .select('*')
        .order('display_order', { ascending: true })
      
      if (error) {
        return NextResponse.json(
          { error: 'Failed to fetch updated categories' },
          { status: 500 }
        )
      }
      
      return NextResponse.json({ 
        data,
        message: 'Categories reordered successfully'
      })
    }
    
    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
    
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}